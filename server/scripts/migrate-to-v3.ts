/**
 * v2.0 到 v3.0 数据迁移脚本
 * 
 * 将现有的96个中文语素转换为概念-语言分离的v3.0架构
 * 确保向后兼容性和数据完整性
 * 
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */

import * as fs from 'fs'
import * as path from 'path'
import { fileURLToPath } from 'url'
import { v4 as uuidv4 } from 'uuid'

// ES模块中的__dirname替代方案
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 导入类型定义
import type { Morpheme } from '../types/core.js'
import {
  LanguageCode,
  ConceptCategory,
  RegisterLevel,
  type UniversalConcept,
  type LanguageSpecificMorpheme,
  type UniversalSemanticVector,
  type CulturalContext,
  type PhoneticFeatures,
  type MorphologicalInfo,
  type SyntacticProperties,
  type LanguageQualityScores,
  type MigrationMapping
} from '../types/multilingual.js'

// ============================================================================
// 配置和常量
// ============================================================================

const DATA_DIR = path.join(__dirname, '../data')
const V2_DATA_FILE = path.join(DATA_DIR, 'morphemes_current.json')
const V3_CONCEPTS_FILE = path.join(DATA_DIR, 'v3_universal_concepts.json')
const V3_ZH_MORPHEMES_FILE = path.join(DATA_DIR, 'v3_zh_morphemes.json')
const MIGRATION_MAPPING_FILE = path.join(DATA_DIR, 'v2_to_v3_mapping.json')

/**
 * 语义向量扩展配置
 * 将v2.0的20维向量扩展为v3.0的512维向量
 */
const SEMANTIC_VECTOR_CONFIG = {
  v2_dimensions: 20,
  v3_dimensions: 512,
  expansion_method: 'interpolation_with_noise' as const
}

// ============================================================================
// 数据迁移类
// ============================================================================

export class DataMigrationV3 {
  private v2Morphemes: Morpheme[] = []
  private v3Concepts: UniversalConcept[] = []
  private v3ZhMorphemes: LanguageSpecificMorpheme[] = []
  private migrationMappings: MigrationMapping[] = []

  /**
   * 执行完整的数据迁移流程
   */
  async migrate(): Promise<void> {
    console.log('🚀 开始 v2.0 → v3.0 数据迁移...')
    const startTime = Date.now()

    try {
      // 1. 加载v2.0数据
      await this.loadV2Data()
      
      // 2. 生成通用概念
      await this.generateUniversalConcepts()
      
      // 3. 生成中文语言特定语素
      await this.generateChineseMorphemes()
      
      // 4. 建立迁移映射
      await this.createMigrationMappings()
      
      // 5. 验证迁移结果
      await this.validateMigration()
      
      // 6. 保存迁移结果
      await this.saveMigrationResults()
      
      const duration = Date.now() - startTime
      console.log(`✅ 数据迁移完成! 耗时: ${duration}ms`)
      console.log(`📊 迁移统计: ${this.v2Morphemes.length}个v2.0语素 → ${this.v3Concepts.length}个v3.0概念`)
      
    } catch (error) {
      console.error('❌ 数据迁移失败:', error)
      throw error
    }
  }

  /**
   * 加载v2.0数据
   */
  private async loadV2Data(): Promise<void> {
    console.log('📁 加载v2.0数据...')
    
    if (!fs.existsSync(V2_DATA_FILE)) {
      throw new Error(`v2.0数据文件不存在: ${V2_DATA_FILE}`)
    }
    
    const rawData = fs.readFileSync(V2_DATA_FILE, 'utf8')
    this.v2Morphemes = JSON.parse(rawData)
    
    console.log(`✅ 加载了 ${this.v2Morphemes.length} 个v2.0语素`)
  }

  /**
   * 生成通用概念
   */
  private async generateUniversalConcepts(): Promise<void> {
    console.log('🧠 生成通用概念...')
    
    for (const v2Morpheme of this.v2Morphemes) {
      const concept = await this.morphemeToUniversalConcept(v2Morpheme)
      this.v3Concepts.push(concept)
    }
    
    console.log(`✅ 生成了 ${this.v3Concepts.length} 个通用概念`)
  }

  /**
   * 将v2.0语素转换为通用概念
   */
  private async morphemeToUniversalConcept(v2Morpheme: Morpheme): Promise<UniversalConcept> {
    const conceptId = `concept_${uuidv4().replace(/-/g, '').substring(0, 12)}`
    
    // 扩展语义向量从20维到512维
    const expandedVector = this.expandSemanticVector(v2Morpheme.semantic_vector)
    
    // 计算抽象程度 (基于语素类别和语义特征)
    const abstractionLevel = this.calculateAbstractionLevel(v2Morpheme)
    
    // 计算文化中性度
    const culturalNeutrality = this.calculateCulturalNeutrality(v2Morpheme)
    
    // 计算跨语言稳定性
    const crossLingualStability = this.calculateCrossLingualStability(v2Morpheme)

    const concept: UniversalConcept = {
      concept_id: conceptId,
      semantic_vector: {
        vector: expandedVector,
        model_version: 'v3.0-migration',
        confidence: 0.85, // 迁移数据的置信度
        updated_at: new Date().toISOString()
      },
      concept_category: v2Morpheme.category as ConceptCategory,
      abstraction_level: abstractionLevel,
      cultural_neutrality: culturalNeutrality,
      cross_lingual_stability: crossLingualStability,
      
      // 认知属性 (基于v2.0质量指标推算)
      cognitive_load: 1 - v2Morpheme.quality_metrics.naturalness,
      memorability_score: v2Morpheme.quality_metrics.acceptability,
      emotional_valence: this.calculateEmotionalValence(v2Morpheme),
      
      // 关系映射 (初始为空，后续可扩展)
      related_concepts: [],
      hierarchical_children: [],
      
      // 元数据
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      version: '3.0.0'
    }

    return concept
  }

  /**
   * 扩展语义向量维度
   */
  private expandSemanticVector(v2Vector: number[]): number[] {
    const { v2_dimensions, v3_dimensions } = SEMANTIC_VECTOR_CONFIG
    
    if (v2Vector.length !== v2_dimensions) {
      console.warn(`警告: v2向量维度不匹配，期望${v2_dimensions}，实际${v2Vector.length}`)
    }
    
    const expandedVector = new Array(v3_dimensions).fill(0)
    
    // 使用插值和噪声扩展方法
    const expansionRatio = v3_dimensions / v2_dimensions
    
    for (let i = 0; i < v3_dimensions; i++) {
      const sourceIndex = Math.floor(i / expansionRatio)
      const baseValue = v2Vector[sourceIndex] || 0
      
      // 添加小量随机噪声以增加向量多样性
      const noise = (Math.random() - 0.5) * 0.1
      expandedVector[i] = Math.max(0, Math.min(1, baseValue + noise))
    }
    
    return expandedVector
  }

  /**
   * 计算抽象程度
   */
  private calculateAbstractionLevel(morpheme: Morpheme): number {
    const categoryAbstraction = {
      'emotions': 0.8,      // 情感类较抽象
      'concepts': 0.9,      // 概念类最抽象
      'characteristics': 0.7, // 特征类中等抽象
      'professions': 0.3,   // 职业类较具体
      'objects': 0.2,       // 物体类较具体
      'actions': 0.4        // 动作类中等具体
    }
    
    const baseAbstraction = categoryAbstraction[morpheme.category] || 0.5
    
    // 基于语义标签调整
    const abstractTags = ['概念', '理念', '思想', '精神', '哲学']
    const concreteTags = ['物体', '工具', '设备', '材料']
    
    let adjustment = 0
    for (const tag of morpheme.tags) {
      if (abstractTags.some(t => tag.includes(t))) adjustment += 0.1
      if (concreteTags.some(t => tag.includes(t))) adjustment -= 0.1
    }
    
    return Math.max(0, Math.min(1, baseAbstraction + adjustment))
  }

  /**
   * 计算文化中性度
   */
  private calculateCulturalNeutrality(morpheme: Morpheme): number {
    const contextNeutrality = {
      'neutral': 0.9,
      'modern': 0.6,
      'ancient': 0.4
    }
    
    const baseNeutrality = contextNeutrality[morpheme.cultural_context] || 0.5
    
    // 基于语义标签调整
    const culturalTags = ['传统', '古典', '现代', '时尚', '民族', '地域']
    let culturalSpecificity = 0
    
    for (const tag of morpheme.tags) {
      if (culturalTags.some(t => tag.includes(t))) {
        culturalSpecificity += 0.1
      }
    }
    
    return Math.max(0, Math.min(1, baseNeutrality - culturalSpecificity))
  }

  /**
   * 计算跨语言稳定性
   */
  private calculateCrossLingualStability(morpheme: Morpheme): number {
    // 基于概念的普遍性和文化中性度
    const categoryStability = {
      'emotions': 0.8,      // 情感概念跨语言较稳定
      'professions': 0.7,   // 职业概念较稳定
      'characteristics': 0.8, // 特征概念较稳定
      'objects': 0.6,       // 物体概念中等稳定
      'actions': 0.7,       // 动作概念较稳定
      'concepts': 0.9       // 抽象概念最稳定
    }
    
    const baseStability = categoryStability[morpheme.category] || 0.6
    
    // 基于质量指标调整
    const qualityBonus = morpheme.quality_score * 0.2
    
    return Math.max(0, Math.min(1, baseStability + qualityBonus))
  }

  /**
   * 计算情感价值
   */
  private calculateEmotionalValence(morpheme: Morpheme): number {
    // 基于语义标签判断情感倾向
    const positiveTags = ['正面', '积极', '美好', '温暖', '治愈', '优雅', '可爱']
    const negativeTags = ['负面', '消极', '冷漠', '严厉', '沉重']
    
    let valence = 0
    
    for (const tag of morpheme.tags) {
      if (positiveTags.some(t => tag.includes(t))) valence += 0.3
      if (negativeTags.some(t => tag.includes(t))) valence -= 0.3
    }
    
    // 基于类别调整
    if (morpheme.category === 'emotions') {
      if (morpheme.subcategory.includes('positive')) valence += 0.4
      if (morpheme.subcategory.includes('negative')) valence -= 0.4
    }
    
    return Math.max(-1, Math.min(1, valence))
  }

  /**
   * 生成中文语言特定语素
   */
  private async generateChineseMorphemes(): Promise<void> {
    console.log('🇨🇳 生成中文语言特定语素...')

    for (let i = 0; i < this.v2Morphemes.length; i++) {
      const v2Morpheme = this.v2Morphemes[i]
      const concept = this.v3Concepts[i]

      const zhMorpheme = await this.morphemeToLanguageSpecific(v2Morpheme, concept)
      this.v3ZhMorphemes.push(zhMorpheme)
    }

    console.log(`✅ 生成了 ${this.v3ZhMorphemes.length} 个中文语素`)
  }

  /**
   * 将v2.0语素转换为语言特定语素
   */
  private async morphemeToLanguageSpecific(
    v2Morpheme: Morpheme,
    concept: UniversalConcept
  ): Promise<LanguageSpecificMorpheme> {
    const morphemeId = `zh_${uuidv4().replace(/-/g, '').substring(0, 12)}`

    // 构建语音特征
    const phoneticFeatures: PhoneticFeatures = {
      ipa_transcription: this.generateIPA(v2Morpheme),
      syllable_count: v2Morpheme.language_properties.syllable_count,
      tone_pattern: this.extractTonePattern(v2Morpheme),
      phonetic_harmony: v2Morpheme.quality_metrics.aesthetic_appeal
    }

    // 构建词法信息
    const morphologicalInfo: MorphologicalInfo = {
      pos_tag: this.mapToUniversalPOS(v2Morpheme.language_properties.morphological_type),
      morphological_type: v2Morpheme.language_properties.morphological_type,
      prefixes: [],
      suffixes: []
    }

    // 构建句法属性
    const syntacticProperties: SyntacticProperties = {
      syntactic_function: [this.deriveSyntacticFunction(v2Morpheme)],
      collocation_constraints: [],
      grammatical_features: {}
    }

    // 构建多维度文化语境
    const culturalContext: CulturalContext = {
      traditionality: v2Morpheme.cultural_context === 'ancient' ? 0.8 : 0.3,
      modernity: v2Morpheme.cultural_context === 'modern' ? 0.8 : 0.3,
      formality: this.calculateFormality(v2Morpheme),
      regionality: 0.5, // 默认中等地域性
      religious_sensitivity: 0.1, // 默认低宗教敏感性
      age_appropriateness: ['all'], // 默认全年龄适宜
      cultural_tags: this.extractCulturalTags(v2Morpheme)
    }

    // 构建语言特定质量评分
    const languageQualityScores: LanguageQualityScores = {
      naturalness: v2Morpheme.quality_metrics.naturalness,
      fluency: v2Morpheme.quality_metrics.acceptability,
      authenticity: v2Morpheme.quality_metrics.frequency,
      aesthetic_appeal: v2Morpheme.quality_metrics.aesthetic_appeal,
      pronunciation_ease: this.calculatePronunciationEase(v2Morpheme),
      memorability: v2Morpheme.quality_score,
      uniqueness: 1 - v2Morpheme.usage_frequency,
      practicality: v2Morpheme.usage_frequency
    }

    const zhMorpheme: LanguageSpecificMorpheme = {
      morpheme_id: morphemeId,
      concept_id: concept.concept_id,
      language: LanguageCode.ZH_CN,

      // 语言形式
      text: v2Morpheme.text,
      alternative_forms: [],

      // 语言学属性
      phonetic_features: phoneticFeatures,
      morphological_info: morphologicalInfo,
      syntactic_properties: syntacticProperties,

      // 文化适配
      cultural_context: culturalContext,
      regional_variants: [],
      register_level: this.determineRegisterLevel(v2Morpheme),

      // 质量指标
      language_quality_scores: languageQualityScores,
      cultural_appropriateness: 0.9, // 中文语素文化适宜性高
      native_speaker_rating: v2Morpheme.quality_score,

      // 使用统计
      usage_frequency: v2Morpheme.usage_frequency,
      popularity_trend: 0, // 初始趋势为中性

      // 元数据
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      version: '3.0.0',
      source: `migrated_from_v2_${v2Morpheme.source}`,
      validation_status: 'validated'
    }

    return zhMorpheme
  }

  /**
   * 生成IPA音标 (简化版)
   */
  private generateIPA(morpheme: Morpheme): string {
    // 这里应该集成真正的中文转IPA工具
    // 暂时使用拼音作为占位符
    return morpheme.language_properties.pronunciation || morpheme.text
  }

  /**
   * 提取声调模式
   */
  private extractTonePattern(morpheme: Morpheme): string[] {
    const features = morpheme.language_properties.phonetic_features
    const tones = features.filter(f =>
      ['阴平', '阳平', '上声', '去声', '轻声'].includes(f)
    )
    return tones.length > 0 ? tones : ['unknown']
  }

  /**
   * 映射到通用词性标注
   */
  private mapToUniversalPOS(chineseType: string): string {
    const posMapping = {
      '名词': 'NOUN',
      '动词': 'VERB',
      '形容词': 'ADJ',
      '副词': 'ADV',
      '介词': 'ADP',
      '连词': 'CCONJ',
      '感叹词': 'INTJ'
    }
    return posMapping[chineseType] || 'X'
  }

  /**
   * 推导句法功能
   */
  private deriveSyntacticFunction(morpheme: Morpheme): string {
    const categoryFunction = {
      'emotions': 'modifier',
      'characteristics': 'modifier',
      'professions': 'head_noun',
      'objects': 'head_noun',
      'actions': 'predicate',
      'concepts': 'head_noun'
    }
    return categoryFunction[morpheme.category] || 'modifier'
  }

  /**
   * 计算正式程度
   */
  private calculateFormality(morpheme: Morpheme): number {
    if (morpheme.cultural_context === 'ancient') return 0.8
    if (morpheme.cultural_context === 'modern') return 0.4
    return 0.6 // neutral
  }

  /**
   * 提取文化标签
   */
  private extractCulturalTags(morpheme: Morpheme): string[] {
    const culturalKeywords = ['传统', '现代', '古典', '时尚', '文艺', '商务', '学术']
    return morpheme.tags.filter(tag =>
      culturalKeywords.some(keyword => tag.includes(keyword))
    )
  }

  /**
   * 计算发音友好度
   */
  private calculatePronunciationEase(morpheme: Morpheme): number {
    // 基于音节数和字符数计算
    const syllableCount = morpheme.language_properties.syllable_count
    const charCount = morpheme.language_properties.character_count

    // 2-3音节最容易发音
    let ease = 1.0
    if (syllableCount === 1) ease = 0.8
    if (syllableCount > 3) ease = 0.7 - (syllableCount - 3) * 0.1

    // 字符数影响
    if (charCount > 4) ease -= 0.1

    return Math.max(0.3, Math.min(1.0, ease))
  }

  /**
   * 确定语域层次
   */
  private determineRegisterLevel(morpheme: Morpheme): RegisterLevel {
    if (morpheme.cultural_context === 'ancient') return RegisterLevel.FORMAL
    if (morpheme.cultural_context === 'modern') {
      // 基于标签判断
      const informalTags = ['可爱', '萌', '治愈']
      if (morpheme.tags.some(tag => informalTags.includes(tag))) {
        return RegisterLevel.INFORMAL
      }
    }
    return RegisterLevel.NEUTRAL
  }

  /**
   * 创建迁移映射
   */
  private async createMigrationMappings(): Promise<void> {
    console.log('🔗 创建迁移映射...')

    for (let i = 0; i < this.v2Morphemes.length; i++) {
      const v2Morpheme = this.v2Morphemes[i]
      const concept = this.v3Concepts[i]
      const zhMorpheme = this.v3ZhMorphemes[i]

      const mapping: MigrationMapping = {
        v2_morpheme_id: v2Morpheme.id,
        v3_concept_id: concept.concept_id,
        v3_morpheme_id: zhMorpheme.morpheme_id,
        migration_confidence: 0.95, // 高置信度，因为是直接迁移
        requires_manual_review: false,
        migration_notes: [
          `从v2.0语素 "${v2Morpheme.text}" 迁移`,
          `质量评分: ${v2Morpheme.quality_score}`,
          `类别: ${v2Morpheme.category}`
        ]
      }

      this.migrationMappings.push(mapping)
    }

    console.log(`✅ 创建了 ${this.migrationMappings.length} 个迁移映射`)
  }

  /**
   * 验证迁移结果
   */
  private async validateMigration(): Promise<void> {
    console.log('🔍 验证迁移结果...')

    // 验证数据完整性
    if (this.v2Morphemes.length !== this.v3Concepts.length) {
      throw new Error('概念数量与原始语素数量不匹配')
    }

    if (this.v3Concepts.length !== this.v3ZhMorphemes.length) {
      throw new Error('中文语素数量与概念数量不匹配')
    }

    // 验证ID唯一性
    const conceptIds = new Set(this.v3Concepts.map(c => c.concept_id))
    const morphemeIds = new Set(this.v3ZhMorphemes.map(m => m.morpheme_id))

    if (conceptIds.size !== this.v3Concepts.length) {
      throw new Error('概念ID存在重复')
    }

    if (morphemeIds.size !== this.v3ZhMorphemes.length) {
      throw new Error('语素ID存在重复')
    }

    // 验证概念-语素关联
    for (let i = 0; i < this.v3Concepts.length; i++) {
      const concept = this.v3Concepts[i]
      const morpheme = this.v3ZhMorphemes[i]

      if (morpheme.concept_id !== concept.concept_id) {
        throw new Error(`概念-语素关联错误: ${concept.concept_id} != ${morpheme.concept_id}`)
      }
    }

    console.log('✅ 迁移结果验证通过')
  }

  /**
   * 保存迁移结果
   */
  private async saveMigrationResults(): Promise<void> {
    console.log('💾 保存迁移结果...')

    // 确保目录存在
    if (!fs.existsSync(DATA_DIR)) {
      fs.mkdirSync(DATA_DIR, { recursive: true })
    }

    // 保存通用概念
    fs.writeFileSync(
      V3_CONCEPTS_FILE,
      JSON.stringify(this.v3Concepts, null, 2),
      'utf8'
    )

    // 保存中文语素
    fs.writeFileSync(
      V3_ZH_MORPHEMES_FILE,
      JSON.stringify(this.v3ZhMorphemes, null, 2),
      'utf8'
    )

    // 保存迁移映射
    fs.writeFileSync(
      MIGRATION_MAPPING_FILE,
      JSON.stringify(this.migrationMappings, null, 2),
      'utf8'
    )

    console.log('✅ 迁移结果已保存')
    console.log(`📁 通用概念: ${V3_CONCEPTS_FILE}`)
    console.log(`📁 中文语素: ${V3_ZH_MORPHEMES_FILE}`)
    console.log(`📁 迁移映射: ${MIGRATION_MAPPING_FILE}`)
  }
}

// ============================================================================
// 执行脚本
// ============================================================================

/**
 * 主执行函数
 */
async function main() {
  try {
    const migrator = new DataMigrationV3()
    await migrator.migrate()

    console.log('\n🎉 v2.0 → v3.0 数据迁移成功完成!')
    console.log('📋 下一步: 运行验证脚本确保数据质量')

  } catch (error) {
    console.error('\n❌ 迁移失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本 (ES模块版本)
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
