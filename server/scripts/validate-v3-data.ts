/**
 * v3.0 数据验证脚本
 * 
 * 验证迁移后的v3.0数据质量和完整性
 * 确保概念-语言分离架构的正确性
 * 
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */

import * as fs from 'fs'
import * as path from 'path'
import { fileURLToPath } from 'url'

// ES模块中的__dirname替代方案
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 导入类型定义
import type { Morpheme } from '../types/core.js'
import type { 
  UniversalConcept, 
  LanguageSpecificMorpheme, 
  MigrationMapping
} from '../types/multilingual.js'

// ============================================================================
// 配置和常量
// ============================================================================

const DATA_DIR = path.join(__dirname, '../data')
const V2_DATA_FILE = path.join(DATA_DIR, 'morphemes_current.json')
const V3_CONCEPTS_FILE = path.join(DATA_DIR, 'v3_universal_concepts.json')
const V3_ZH_MORPHEMES_FILE = path.join(DATA_DIR, 'v3_zh_morphemes.json')
const MIGRATION_MAPPING_FILE = path.join(DATA_DIR, 'v2_to_v3_mapping.json')

// ============================================================================
// 数据验证类
// ============================================================================

export class DataValidatorV3 {
  private v2Morphemes: Morpheme[] = []
  private v3Concepts: UniversalConcept[] = []
  private v3ZhMorphemes: LanguageSpecificMorpheme[] = []
  private migrationMappings: MigrationMapping[] = []
  
  private validationResults: {
    totalTests: number
    passedTests: number
    failedTests: number
    errors: string[]
    warnings: string[]
    summary: Record<string, any>
  } = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    errors: [],
    warnings: [],
    summary: {}
  }

  /**
   * 执行完整的数据验证流程
   */
  async validate(): Promise<void> {
    console.log('🔍 开始 v3.0 数据验证...')
    const startTime = Date.now()

    try {
      // 1. 加载所有数据
      await this.loadAllData()
      
      // 2. 基础数据完整性验证
      await this.validateDataIntegrity()
      
      // 3. 概念-语素关联验证
      await this.validateConceptMorphemeMapping()
      
      // 4. 数据质量验证
      await this.validateDataQuality()
      
      // 5. 迁移映射验证
      await this.validateMigrationMapping()
      
      // 6. 语义向量验证
      await this.validateSemanticVectors()
      
      // 7. 文化适配验证
      await this.validateCulturalAdaptation()
      
      // 8. 生成验证报告
      await this.generateValidationReport()
      
      const duration = Date.now() - startTime
      console.log(`✅ 数据验证完成! 耗时: ${duration}ms`)
      console.log(`📊 验证结果: ${this.validationResults.passedTests}/${this.validationResults.totalTests} 通过`)
      
      if (this.validationResults.failedTests > 0) {
        console.log(`⚠️  发现 ${this.validationResults.failedTests} 个问题`)
        this.validationResults.errors.forEach(error => console.error(`❌ ${error}`))
      }
      
      if (this.validationResults.warnings.length > 0) {
        console.log(`⚠️  发现 ${this.validationResults.warnings.length} 个警告`)
        this.validationResults.warnings.forEach(warning => console.warn(`⚠️  ${warning}`))
      }
      
    } catch (error) {
      console.error('❌ 数据验证失败:', error)
      throw error
    }
  }

  /**
   * 加载所有数据文件
   */
  private async loadAllData(): Promise<void> {
    console.log('📁 加载数据文件...')
    
    // 加载v2.0数据
    if (fs.existsSync(V2_DATA_FILE)) {
      const v2Data = fs.readFileSync(V2_DATA_FILE, 'utf8')
      this.v2Morphemes = JSON.parse(v2Data)
    }
    
    // 加载v3.0概念数据
    if (fs.existsSync(V3_CONCEPTS_FILE)) {
      const conceptsData = fs.readFileSync(V3_CONCEPTS_FILE, 'utf8')
      this.v3Concepts = JSON.parse(conceptsData)
    }
    
    // 加载v3.0中文语素数据
    if (fs.existsSync(V3_ZH_MORPHEMES_FILE)) {
      const morphemesData = fs.readFileSync(V3_ZH_MORPHEMES_FILE, 'utf8')
      this.v3ZhMorphemes = JSON.parse(morphemesData)
    }
    
    // 加载迁移映射数据
    if (fs.existsSync(MIGRATION_MAPPING_FILE)) {
      const mappingData = fs.readFileSync(MIGRATION_MAPPING_FILE, 'utf8')
      this.migrationMappings = JSON.parse(mappingData)
    }
    
    console.log(`✅ 数据加载完成:`)
    console.log(`   - v2.0语素: ${this.v2Morphemes.length}`)
    console.log(`   - v3.0概念: ${this.v3Concepts.length}`)
    console.log(`   - v3.0中文语素: ${this.v3ZhMorphemes.length}`)
    console.log(`   - 迁移映射: ${this.migrationMappings.length}`)
  }

  /**
   * 验证数据完整性
   */
  private async validateDataIntegrity(): Promise<void> {
    console.log('🔍 验证数据完整性...')
    
    // 测试1: 数据数量一致性
    this.runTest('数据数量一致性', () => {
      const v2Count = this.v2Morphemes.length
      const v3ConceptCount = this.v3Concepts.length
      const v3MorphemeCount = this.v3ZhMorphemes.length
      const mappingCount = this.migrationMappings.length
      
      if (v2Count !== v3ConceptCount) {
        throw new Error(`v2.0语素数量(${v2Count})与v3.0概念数量(${v3ConceptCount})不匹配`)
      }
      
      if (v3ConceptCount !== v3MorphemeCount) {
        throw new Error(`v3.0概念数量(${v3ConceptCount})与中文语素数量(${v3MorphemeCount})不匹配`)
      }
      
      if (v2Count !== mappingCount) {
        throw new Error(`v2.0语素数量(${v2Count})与迁移映射数量(${mappingCount})不匹配`)
      }
    })
    
    // 测试2: ID唯一性
    this.runTest('概念ID唯一性', () => {
      const conceptIds = this.v3Concepts.map(c => c.concept_id)
      const uniqueIds = new Set(conceptIds)
      if (conceptIds.length !== uniqueIds.size) {
        throw new Error('概念ID存在重复')
      }
    })
    
    this.runTest('语素ID唯一性', () => {
      const morphemeIds = this.v3ZhMorphemes.map(m => m.morpheme_id)
      const uniqueIds = new Set(morphemeIds)
      if (morphemeIds.length !== uniqueIds.size) {
        throw new Error('语素ID存在重复')
      }
    })
    
    // 测试3: 必需字段完整性
    this.runTest('概念必需字段完整性', () => {
      for (const concept of this.v3Concepts) {
        if (!concept.concept_id || !concept.semantic_vector || !concept.concept_category) {
          throw new Error(`概念 ${concept.concept_id} 缺少必需字段`)
        }
      }
    })
    
    this.runTest('语素必需字段完整性', () => {
      for (const morpheme of this.v3ZhMorphemes) {
        if (!morpheme.morpheme_id || !morpheme.concept_id || !morpheme.text) {
          throw new Error(`语素 ${morpheme.morpheme_id} 缺少必需字段`)
        }
      }
    })
  }

  /**
   * 验证概念-语素映射关系
   */
  private async validateConceptMorphemeMapping(): Promise<void> {
    console.log('🔗 验证概念-语素映射关系...')
    
    // 创建概念ID映射
    const conceptIdMap = new Map(this.v3Concepts.map(c => [c.concept_id, c]))
    
    this.runTest('概念-语素关联完整性', () => {
      for (const morpheme of this.v3ZhMorphemes) {
        if (!conceptIdMap.has(morpheme.concept_id)) {
          throw new Error(`语素 ${morpheme.morpheme_id} 引用了不存在的概念 ${morpheme.concept_id}`)
        }
      }
    })
    
    this.runTest('概念-语素一对一映射', () => {
      const conceptUsage = new Map<string, number>()
      
      for (const morpheme of this.v3ZhMorphemes) {
        const count = conceptUsage.get(morpheme.concept_id) || 0
        conceptUsage.set(morpheme.concept_id, count + 1)
      }
      
      for (const [conceptId, count] of conceptUsage) {
        if (count !== 1) {
          throw new Error(`概念 ${conceptId} 被 ${count} 个语素引用，应该是1对1关系`)
        }
      }
    })
  }

  /**
   * 验证数据质量
   */
  private async validateDataQuality(): Promise<void> {
    console.log('📊 验证数据质量...')
    
    // 语义向量维度验证
    this.runTest('语义向量维度正确性', () => {
      for (const concept of this.v3Concepts) {
        const vector = concept.semantic_vector.vector
        if (!Array.isArray(vector) || vector.length !== 512) {
          throw new Error(`概念 ${concept.concept_id} 的语义向量维度不正确: ${vector?.length}`)
        }
        
        // 检查向量值范围
        for (const value of vector) {
          if (typeof value !== 'number' || value < 0 || value > 1) {
            throw new Error(`概念 ${concept.concept_id} 的语义向量包含无效值: ${value}`)
          }
        }
      }
    })
    
    // 质量评分范围验证
    this.runTest('质量评分范围正确性', () => {
      for (const morpheme of this.v3ZhMorphemes) {
        const scores = morpheme.language_quality_scores
        const scoreFields = ['naturalness', 'fluency', 'authenticity', 'aesthetic_appeal', 
                            'pronunciation_ease', 'memorability', 'uniqueness', 'practicality']
        
        for (const field of scoreFields) {
          const score = scores[field as keyof typeof scores]
          if (typeof score !== 'number' || score < 0 || score > 1) {
            throw new Error(`语素 ${morpheme.morpheme_id} 的 ${field} 评分超出范围: ${score}`)
          }
        }
      }
    })
    
    // 文本内容验证
    this.runTest('语素文本内容有效性', () => {
      for (const morpheme of this.v3ZhMorphemes) {
        if (!morpheme.text || morpheme.text.trim().length === 0) {
          throw new Error(`语素 ${morpheme.morpheme_id} 的文本内容为空`)
        }
        
        // 检查中文字符
        const chineseRegex = /[\u4e00-\u9fff]/
        if (!chineseRegex.test(morpheme.text)) {
          this.validationResults.warnings.push(`语素 ${morpheme.morpheme_id} 的文本 "${morpheme.text}" 可能不包含中文字符`)
        }
      }
    })
  }

  /**
   * 验证迁移映射
   */
  private async validateMigrationMapping(): Promise<void> {
    console.log('🔄 验证迁移映射...')
    
    // 创建ID映射
    const v2IdMap = new Map(this.v2Morphemes.map(m => [m.id, m]))
    const v3ConceptMap = new Map(this.v3Concepts.map(c => [c.concept_id, c]))
    const v3MorphemeMap = new Map(this.v3ZhMorphemes.map(m => [m.morpheme_id, m]))
    
    this.runTest('迁移映射完整性', () => {
      for (const mapping of this.migrationMappings) {
        // 验证v2.0语素存在
        if (!v2IdMap.has(mapping.v2_morpheme_id)) {
          throw new Error(`迁移映射引用了不存在的v2.0语素: ${mapping.v2_morpheme_id}`)
        }
        
        // 验证v3.0概念存在
        if (!v3ConceptMap.has(mapping.v3_concept_id)) {
          throw new Error(`迁移映射引用了不存在的v3.0概念: ${mapping.v3_concept_id}`)
        }
        
        // 验证v3.0语素存在
        if (!v3MorphemeMap.has(mapping.v3_morpheme_id)) {
          throw new Error(`迁移映射引用了不存在的v3.0语素: ${mapping.v3_morpheme_id}`)
        }
      }
    })
    
    this.runTest('迁移映射一致性', () => {
      for (const mapping of this.migrationMappings) {
        const v3Morpheme = v3MorphemeMap.get(mapping.v3_morpheme_id)!
        
        // 验证概念-语素关联一致性
        if (v3Morpheme.concept_id !== mapping.v3_concept_id) {
          throw new Error(`迁移映射中概念ID不一致: ${mapping.v3_concept_id} vs ${v3Morpheme.concept_id}`)
        }
      }
    })
  }

  /**
   * 验证语义向量
   */
  private async validateSemanticVectors(): Promise<void> {
    console.log('🧠 验证语义向量...')
    
    this.runTest('语义向量数值有效性', () => {
      for (const concept of this.v3Concepts) {
        const vector = concept.semantic_vector.vector
        
        // 检查是否有NaN或无穷大
        for (let i = 0; i < vector.length; i++) {
          if (isNaN(vector[i]) || !isFinite(vector[i])) {
            throw new Error(`概念 ${concept.concept_id} 的语义向量包含无效数值: ${vector[i]} at index ${i}`)
          }
        }
        
        // 检查向量模长是否合理
        const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))
        if (magnitude < 0.1 || magnitude > 100) {
          this.validationResults.warnings.push(`概念 ${concept.concept_id} 的语义向量模长异常: ${magnitude}`)
        }
      }
    })
  }

  /**
   * 验证文化适配
   */
  private async validateCulturalAdaptation(): Promise<void> {
    console.log('🌍 验证文化适配...')
    
    this.runTest('文化语境数值范围', () => {
      for (const morpheme of this.v3ZhMorphemes) {
        const context = morpheme.cultural_context
        const fields = ['traditionality', 'modernity', 'formality', 'regionality', 'religious_sensitivity']
        
        for (const field of fields) {
          const value = context[field as keyof typeof context] as number
          if (typeof value !== 'number' || value < 0 || value > 1) {
            throw new Error(`语素 ${morpheme.morpheme_id} 的文化语境 ${field} 超出范围: ${value}`)
          }
        }
      }
    })
  }

  /**
   * 运行单个测试
   */
  private runTest(testName: string, testFn: () => void): void {
    this.validationResults.totalTests++
    
    try {
      testFn()
      this.validationResults.passedTests++
      console.log(`  ✅ ${testName}`)
    } catch (error) {
      this.validationResults.failedTests++
      const errorMsg = `${testName}: ${error instanceof Error ? error.message : String(error)}`
      this.validationResults.errors.push(errorMsg)
      console.log(`  ❌ ${testName}`)
    }
  }

  /**
   * 生成验证报告
   */
  private async generateValidationReport(): Promise<void> {
    console.log('📋 生成验证报告...')
    
    // 计算统计信息
    this.validationResults.summary = {
      v2_morphemes_count: this.v2Morphemes.length,
      v3_concepts_count: this.v3Concepts.length,
      v3_morphemes_count: this.v3ZhMorphemes.length,
      migration_mappings_count: this.migrationMappings.length,
      
      // 质量统计
      average_concept_abstraction: this.calculateAverageAbstraction(),
      average_morpheme_quality: this.calculateAverageMorphemeQuality(),
      category_distribution: this.calculateCategoryDistribution(),
      
      // 验证结果
      validation_success_rate: this.validationResults.passedTests / this.validationResults.totalTests,
      total_errors: this.validationResults.failedTests,
      total_warnings: this.validationResults.warnings.length
    }
    
    // 保存验证报告
    const reportPath = path.join(DATA_DIR, 'v3_validation_report.json')
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      validation_results: this.validationResults
    }, null, 2))
    
    console.log(`📁 验证报告已保存: ${reportPath}`)
  }

  /**
   * 计算平均抽象程度
   */
  private calculateAverageAbstraction(): number {
    const total = this.v3Concepts.reduce((sum, concept) => sum + concept.abstraction_level, 0)
    return total / this.v3Concepts.length
  }

  /**
   * 计算平均语素质量
   */
  private calculateAverageMorphemeQuality(): number {
    const total = this.v3ZhMorphemes.reduce((sum, morpheme) => sum + morpheme.native_speaker_rating, 0)
    return total / this.v3ZhMorphemes.length
  }

  /**
   * 计算类别分布
   */
  private calculateCategoryDistribution(): Record<string, number> {
    const distribution: Record<string, number> = {}
    
    for (const concept of this.v3Concepts) {
      const category = concept.concept_category
      distribution[category] = (distribution[category] || 0) + 1
    }
    
    return distribution
  }
}

// ============================================================================
// 执行脚本
// ============================================================================

/**
 * 主执行函数
 */
async function main() {
  try {
    const validator = new DataValidatorV3()
    await validator.validate()
    
    console.log('\n🎉 v3.0 数据验证成功完成!')
    
  } catch (error) {
    console.error('\n❌ 验证失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
