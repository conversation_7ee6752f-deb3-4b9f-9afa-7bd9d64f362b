/**
 * 语素库扩展脚本
 * 
 * 基于现有17个高质量语素，扩展到100个语素的目标
 * 使用智能生成和质量控制机制
 * 
 * @fileoverview 语素库扩展和质量管理脚本
 * @version 1.0.0
 * @since 2025-06-23
 */

import { readFile, writeFile } from 'fs/promises'
import { join } from 'path'
import type { Morpheme, MorphemeCategory, CulturalContext } from '../types/core'

// ============================================================================
// 配置常量
// ============================================================================

const DATA_DIR = join(process.cwd(), 'data')
const SOURCE_FILE = join(DATA_DIR, 'reorganized/morphemes_v2_unified.json')
const OUTPUT_FILE = join(DATA_DIR, 'morphemes_expanded.json')

// 目标分布 (总计100个语素)
const TARGET_DISTRIBUTION = {
  emotions: 25,      // 25% - 情感类语素
  professions: 25,   // 25% - 职业类语素  
  characteristics: 20, // 20% - 特征类语素
  objects: 10,       // 10% - 物体类语素
  actions: 10,       // 10% - 动作类语素
  concepts: 10       // 10% - 概念类语素
} as const

// ============================================================================
// 语素扩展数据库
// ============================================================================

const EXPANSION_DATABASE = {
  emotions: [
    // 正面情感
    { text: '温柔', subcategory: 'positive_emotions', cultural_context: 'neutral', quality_score: 0.89 },
    { text: '活泼', subcategory: 'positive_emotions', cultural_context: 'modern', quality_score: 0.85 },
    { text: '开朗', subcategory: 'positive_emotions', cultural_context: 'neutral', quality_score: 0.87 },
    { text: '阳光', subcategory: 'positive_emotions', cultural_context: 'modern', quality_score: 0.88 },
    { text: '甜美', subcategory: 'positive_emotions', cultural_context: 'modern', quality_score: 0.86 },
    { text: '纯真', subcategory: 'positive_emotions', cultural_context: 'neutral', quality_score: 0.84 },
    { text: '灿烂', subcategory: 'positive_emotions', cultural_context: 'neutral', quality_score: 0.85 },
    { text: '明媚', subcategory: 'positive_emotions', cultural_context: 'ancient', quality_score: 0.87 },
    
    // 艺术情感
    { text: '浪漫', subcategory: 'artistic_emotions', cultural_context: 'modern', quality_score: 0.90 },
    { text: '梦幻', subcategory: 'artistic_emotions', cultural_context: 'modern', quality_score: 0.88 },
    { text: '空灵', subcategory: 'artistic_emotions', cultural_context: 'ancient', quality_score: 0.91 },
    { text: '飘逸', subcategory: 'artistic_emotions', cultural_context: 'ancient', quality_score: 0.89 },
    { text: '淡雅', subcategory: 'artistic_emotions', cultural_context: 'ancient', quality_score: 0.90 },
    { text: '清雅', subcategory: 'artistic_emotions', cultural_context: 'ancient', quality_score: 0.88 },
    { text: '静谧', subcategory: 'artistic_emotions', cultural_context: 'neutral', quality_score: 0.86 },
    { text: '悠然', subcategory: 'artistic_emotions', cultural_context: 'ancient', quality_score: 0.87 },
    
    // 现代情感
    { text: '酷炫', subcategory: 'modern_emotions', cultural_context: 'modern', quality_score: 0.83 },
    { text: '潮流', subcategory: 'modern_emotions', cultural_context: 'modern', quality_score: 0.84 },
    { text: '时尚', subcategory: 'modern_emotions', cultural_context: 'modern', quality_score: 0.85 }
  ],
  
  professions: [
    // 创意职业
    { text: '画家', subcategory: 'creative_professions', cultural_context: 'neutral', quality_score: 0.89 },
    { text: '音乐家', subcategory: 'creative_professions', cultural_context: 'neutral', quality_score: 0.90 },
    { text: '作家', subcategory: 'creative_professions', cultural_context: 'neutral', quality_score: 0.91 },
    { text: '摄影师', subcategory: 'creative_professions', cultural_context: 'modern', quality_score: 0.87 },
    { text: '建筑师', subcategory: 'creative_professions', cultural_context: 'modern', quality_score: 0.88 },
    { text: '导演', subcategory: 'creative_professions', cultural_context: 'modern', quality_score: 0.86 },
    
    // 技术职业
    { text: '程序员', subcategory: 'technical_professions', cultural_context: 'modern', quality_score: 0.85 },
    { text: '科学家', subcategory: 'technical_professions', cultural_context: 'neutral', quality_score: 0.92 },
    { text: '研究员', subcategory: 'technical_professions', cultural_context: 'neutral', quality_score: 0.88 },
    { text: '分析师', subcategory: 'technical_professions', cultural_context: 'modern', quality_score: 0.84 },
    
    // 传统职业
    { text: '学者', subcategory: 'traditional_professions', cultural_context: 'ancient', quality_score: 0.93 },
    { text: '文人', subcategory: 'traditional_professions', cultural_context: 'ancient', quality_score: 0.91 },
    { text: '书生', subcategory: 'traditional_professions', cultural_context: 'ancient', quality_score: 0.89 },
    { text: '匠人', subcategory: 'traditional_professions', cultural_context: 'ancient', quality_score: 0.87 },
    
    // 现代职业
    { text: '策划师', subcategory: 'modern_professions', cultural_context: 'modern', quality_score: 0.83 },
    { text: '咨询师', subcategory: 'modern_professions', cultural_context: 'modern', quality_score: 0.84 },
    { text: '产品经理', subcategory: 'modern_professions', cultural_context: 'modern', quality_score: 0.82 },
    { text: '运营师', subcategory: 'modern_professions', cultural_context: 'modern', quality_score: 0.81 },
    { text: '数据师', subcategory: 'modern_professions', cultural_context: 'modern', quality_score: 0.83 }
  ],
  
  characteristics: [
    // 能力特征
    { text: '智慧', subcategory: 'ability_traits', cultural_context: 'neutral', quality_score: 0.92 },
    { text: '机智', subcategory: 'ability_traits', cultural_context: 'neutral', quality_score: 0.87 },
    { text: '敏锐', subcategory: 'ability_traits', cultural_context: 'neutral', quality_score: 0.86 },
    { text: '睿智', subcategory: 'ability_traits', cultural_context: 'ancient', quality_score: 0.90 },
    { text: '博学', subcategory: 'ability_traits', cultural_context: 'ancient', quality_score: 0.89 },
    
    // 品质特征
    { text: '坚韧', subcategory: 'quality_traits', cultural_context: 'neutral', quality_score: 0.88 },
    { text: '勇敢', subcategory: 'quality_traits', cultural_context: 'neutral', quality_score: 0.87 },
    { text: '诚实', subcategory: 'quality_traits', cultural_context: 'neutral', quality_score: 0.89 },
    { text: '善良', subcategory: 'quality_traits', cultural_context: 'neutral', quality_score: 0.90 },
    { text: '正直', subcategory: 'quality_traits', cultural_context: 'neutral', quality_score: 0.91 },
    
    // 性格特征
    { text: '独立', subcategory: 'personality_traits', cultural_context: 'modern', quality_score: 0.85 },
    { text: '自信', subcategory: 'personality_traits', cultural_context: 'modern', quality_score: 0.86 },
    { text: '沉稳', subcategory: 'personality_traits', cultural_context: 'neutral', quality_score: 0.84 },
    { text: '果断', subcategory: 'personality_traits', cultural_context: 'neutral', quality_score: 0.83 },
    { text: '细致', subcategory: 'personality_traits', cultural_context: 'neutral', quality_score: 0.82 }
  ],
  
  objects: [
    { text: '月亮', subcategory: 'celestial_objects', cultural_context: 'ancient', quality_score: 0.93 },
    { text: '彩虹', subcategory: 'natural_objects', cultural_context: 'neutral', quality_score: 0.89 },
    { text: '花朵', subcategory: 'natural_objects', cultural_context: 'neutral', quality_score: 0.87 },
    { text: '海洋', subcategory: 'natural_objects', cultural_context: 'neutral', quality_score: 0.90 },
    { text: '森林', subcategory: 'natural_objects', cultural_context: 'neutral', quality_score: 0.88 },
    { text: '山峰', subcategory: 'natural_objects', cultural_context: 'ancient', quality_score: 0.86 },
    { text: '流水', subcategory: 'natural_objects', cultural_context: 'ancient', quality_score: 0.85 },
    { text: '晨光', subcategory: 'natural_objects', cultural_context: 'neutral', quality_score: 0.84 },
    { text: '微风', subcategory: 'natural_objects', cultural_context: 'neutral', quality_score: 0.83 }
  ],
  
  actions: [
    { text: '探索', subcategory: 'creative_actions', cultural_context: 'neutral', quality_score: 0.88 },
    { text: '发现', subcategory: 'creative_actions', cultural_context: 'neutral', quality_score: 0.87 },
    { text: '构建', subcategory: 'creative_actions', cultural_context: 'modern', quality_score: 0.85 },
    { text: '设计', subcategory: 'creative_actions', cultural_context: 'modern', quality_score: 0.86 },
    { text: '思考', subcategory: 'mental_actions', cultural_context: 'neutral', quality_score: 0.84 },
    { text: '领悟', subcategory: 'mental_actions', cultural_context: 'ancient', quality_score: 0.89 },
    { text: '追求', subcategory: 'aspirational_actions', cultural_context: 'neutral', quality_score: 0.83 },
    { text: '奋斗', subcategory: 'aspirational_actions', cultural_context: 'modern', quality_score: 0.82 },
    { text: '飞翔', subcategory: 'aspirational_actions', cultural_context: 'neutral', quality_score: 0.85 }
  ],
  
  concepts: [
    { text: '希望', subcategory: 'aspirational_concepts', cultural_context: 'neutral', quality_score: 0.92 },
    { text: '未来', subcategory: 'aspirational_concepts', cultural_context: 'modern', quality_score: 0.89 },
    { text: '自由', subcategory: 'aspirational_concepts', cultural_context: 'modern', quality_score: 0.90 },
    { text: '和谐', subcategory: 'philosophical_concepts', cultural_context: 'ancient', quality_score: 0.88 },
    { text: '平衡', subcategory: 'philosophical_concepts', cultural_context: 'neutral', quality_score: 0.86 },
    { text: '智慧', subcategory: 'philosophical_concepts', cultural_context: 'ancient', quality_score: 0.91 },
    { text: '真理', subcategory: 'philosophical_concepts', cultural_context: 'ancient', quality_score: 0.93 },
    { text: '创新', subcategory: 'modern_concepts', cultural_context: 'modern', quality_score: 0.87 },
    { text: '进步', subcategory: 'modern_concepts', cultural_context: 'modern', quality_score: 0.85 }
  ]
} as const

// ============================================================================
// 语素扩展类
// ============================================================================

class MorphemeExpander {
  private existingMorphemes: Morpheme[] = []
  private expandedMorphemes: Morpheme[] = []
  private categoryCounters = new Map<string, number>()

  /**
   * 执行语素库扩展
   */
  async expand(): Promise<void> {
    console.log('🚀 开始语素库扩展...')

    // 1. 加载现有语素
    await this.loadExistingMorphemes()

    // 2. 分析当前分布
    this.analyzeCurrentDistribution()

    // 3. 执行扩展
    await this.performExpansion()

    // 4. 质量验证
    await this.validateQuality()

    // 5. 输出结果
    await this.outputResults()

    console.log('✅ 语素库扩展完成!')
  }

  /**
   * 加载现有语素
   */
  private async loadExistingMorphemes(): Promise<void> {
    console.log('📁 加载现有语素...')
    
    const content = await readFile(SOURCE_FILE, 'utf-8')
    this.existingMorphemes = JSON.parse(content)
    
    // 初始化计数器
    for (const morpheme of this.existingMorphemes) {
      const category = morpheme.category
      this.categoryCounters.set(category, (this.categoryCounters.get(category) || 0) + 1)
    }
    
    console.log(`📊 现有语素: ${this.existingMorphemes.length}个`)
    for (const [category, count] of this.categoryCounters) {
      console.log(`   ${category}: ${count}个`)
    }
  }

  /**
   * 分析当前分布
   */
  private analyzeCurrentDistribution(): void {
    console.log('📊 分析当前分布...')
    
    for (const [category, target] of Object.entries(TARGET_DISTRIBUTION)) {
      const current = this.categoryCounters.get(category) || 0
      const needed = target - current
      
      console.log(`   ${category}: ${current}/${target} (需要${needed}个)`)
    }
  }

  /**
   * 执行扩展
   */
  private async performExpansion(): Promise<void> {
    console.log('🔧 执行语素扩展...')
    
    this.expandedMorphemes = [...this.existingMorphemes]
    
    for (const [category, target] of Object.entries(TARGET_DISTRIBUTION)) {
      const current = this.categoryCounters.get(category) || 0
      const needed = target - current
      
      if (needed > 0) {
        console.log(`📈 扩展${category}: 添加${needed}个语素`)
        await this.expandCategory(category as MorphemeCategory, needed)
      }
    }
  }

  /**
   * 扩展特定类别
   */
  private async expandCategory(category: MorphemeCategory, count: number): Promise<void> {
    const expansionData = EXPANSION_DATABASE[category] || []
    
    // 检查是否有足够的扩展数据
    if (expansionData.length < count) {
      console.warn(`⚠️ ${category}类别扩展数据不足: 需要${count}个，可用${expansionData.length}个`)
    }
    
    // 按质量评分排序，优先选择高质量语素
    const sortedData = [...expansionData].sort((a, b) => b.quality_score - a.quality_score)
    
    for (let i = 0; i < Math.min(count, sortedData.length); i++) {
      const data = sortedData[i]
      
      // 检查是否已存在
      const exists = this.expandedMorphemes.some(m => m.text === data.text)
      if (exists) {
        console.log(`   ⚠️ 跳过重复语素: ${data.text}`)
        continue
      }
      
      // 生成新语素
      const newMorpheme = this.createMorpheme(category, data)
      this.expandedMorphemes.push(newMorpheme)
      
      console.log(`   ✅ 添加: ${newMorpheme.id} (${newMorpheme.text})`)
    }
  }

  /**
   * 创建新语素
   */
  private createMorpheme(category: MorphemeCategory, data: any): Morpheme {
    const counter = this.categoryCounters.get(category) || 0
    this.categoryCounters.set(category, counter + 1)
    
    const id = `${category}_${(counter + 1).toString().padStart(3, '0')}`
    
    return {
      id,
      text: data.text,
      category,
      subcategory: data.subcategory,
      cultural_context: data.cultural_context as CulturalContext,
      usage_frequency: this.estimateUsageFrequency(data.quality_score),
      quality_score: data.quality_score,
      semantic_vector: this.generateSemanticVector(),
      tags: this.generateTags(data.text, category, data.subcategory),
      language_properties: {
        syllable_count: Math.ceil(data.text.length / 2),
        character_count: data.text.length,
        phonetic_features: [],
        morphological_type: this.getMorphologicalType(category),
        pronunciation: undefined
      },
      quality_metrics: {
        naturalness: data.quality_score * 0.95,
        frequency: this.estimateUsageFrequency(data.quality_score),
        acceptability: data.quality_score * 0.92,
        aesthetic_appeal: data.quality_score * 0.88
      },
      created_at: Date.now(),
      source: 'morpheme_expansion_v1',
      version: '3.0.0'
    }
  }

  /**
   * 估算使用频率
   */
  private estimateUsageFrequency(qualityScore: number): number {
    // 基于质量评分估算使用频率，添加一些随机性
    const base = qualityScore * 0.8
    const variance = (Math.random() - 0.5) * 0.2
    return Math.max(0.1, Math.min(1.0, base + variance))
  }

  /**
   * 生成语义向量
   */
  private generateSemanticVector(): number[] {
    // 生成20维语义向量 (简化版本，实际应该基于语义模型)
    return Array.from({ length: 20 }, () => Math.random())
  }

  /**
   * 生成标签
   */
  private generateTags(text: string, category: string, subcategory: string): string[] {
    const tags = [text]
    
    // 基于类别添加标签
    const categoryTags: Record<string, string[]> = {
      emotions: ['情感', '感受'],
      professions: ['职业', '专业'],
      characteristics: ['特征', '品质'],
      objects: ['物体', '自然'],
      actions: ['行动', '动作'],
      concepts: ['概念', '理念']
    }
    
    if (categoryTags[category]) {
      tags.push(...categoryTags[category])
    }
    
    return tags
  }

  /**
   * 获取词法类型
   */
  private getMorphologicalType(category: MorphemeCategory): string {
    const typeMap: Record<string, string> = {
      emotions: '形容词',
      professions: '名词',
      characteristics: '形容词',
      objects: '名词',
      actions: '动词',
      concepts: '名词'
    }
    
    return typeMap[category] || '未知'
  }

  /**
   * 质量验证
   */
  private async validateQuality(): Promise<void> {
    console.log('🔍 执行质量验证...')
    
    const issues: string[] = []
    
    // 检查总数
    if (this.expandedMorphemes.length !== 100) {
      issues.push(`总数不符合目标: ${this.expandedMorphemes.length}/100`)
    }
    
    // 检查分布
    for (const [category, target] of Object.entries(TARGET_DISTRIBUTION)) {
      const actual = this.expandedMorphemes.filter(m => m.category === category).length
      if (actual !== target) {
        issues.push(`${category}分布不符合目标: ${actual}/${target}`)
      }
    }
    
    // 检查重复
    const textSet = new Set()
    for (const morpheme of this.expandedMorphemes) {
      if (textSet.has(morpheme.text)) {
        issues.push(`发现重复语素: ${morpheme.text}`)
      }
      textSet.add(morpheme.text)
    }
    
    if (issues.length > 0) {
      console.warn(`⚠️ 发现${issues.length}个质量问题:`)
      issues.forEach(issue => console.warn(`   - ${issue}`))
    } else {
      console.log('✅ 质量验证通过')
    }
  }

  /**
   * 输出结果
   */
  private async outputResults(): Promise<void> {
    console.log('📤 输出扩展结果...')
    
    await writeFile(OUTPUT_FILE, JSON.stringify(this.expandedMorphemes, null, 2), 'utf-8')
    
    console.log(`📄 输出文件: ${OUTPUT_FILE}`)
    console.log(`📊 总计语素: ${this.expandedMorphemes.length}个`)
    
    // 输出分布统计
    const distribution: Record<string, number> = {}
    for (const morpheme of this.expandedMorphemes) {
      distribution[morpheme.category] = (distribution[morpheme.category] || 0) + 1
    }
    
    console.log('📊 最终分布:')
    for (const [category, count] of Object.entries(distribution)) {
      const percentage = ((count / this.expandedMorphemes.length) * 100).toFixed(1)
      console.log(`   ${category}: ${count}个 (${percentage}%)`)
    }
  }
}

// ============================================================================
// 主函数
// ============================================================================

async function main() {
  try {
    const expander = new MorphemeExpander()
    await expander.expand()
  } catch (error) {
    console.error('❌ 语素库扩展失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { MorphemeExpander }
