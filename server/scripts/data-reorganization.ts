/**
 * 数据重组脚本
 * 
 * 负责数据去重、标准化和质量提升，为语素库扩展做准备
 * 
 * @fileoverview 数据重组和优化脚本
 * @version 1.0.0
 * @since 2025-06-23
 */

import { readFile, writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import type { Morpheme } from '../types/core'

// ============================================================================
// 配置常量
// ============================================================================

const DATA_DIR = join(process.cwd(), 'data')
const OUTPUT_DIR = join(DATA_DIR, 'reorganized')
const BACKUP_DIR = join(DATA_DIR, 'backup')

// ============================================================================
// 接口定义
// ============================================================================

interface ReorganizationResult {
  original_count: number
  deduplicated_count: number
  removed_duplicates: number
  standardized_ids: number
  upgraded_to_v2: number
  quality_issues: string[]
  output_files: string[]
}

interface DuplicateInfo {
  text: string
  ids: string[]
  sources: string[]
  keep_id: string
  reason: string
}

// ============================================================================
// 数据重组类
// ============================================================================

class DataReorganizer {
  private morphemes: Morpheme[] = []
  private duplicates: DuplicateInfo[] = []
  private qualityIssues: string[] = []

  /**
   * 执行完整的数据重组流程
   */
  async reorganize(): Promise<ReorganizationResult> {
    console.log('🚀 开始数据重组流程...')

    // 1. 备份原始数据
    await this.backupOriginalData()

    // 2. 加载所有数据
    await this.loadAllData()
    const originalCount = this.morphemes.length

    // 3. 数据去重
    const deduplicatedCount = await this.deduplicateData()

    // 4. ID标准化
    const standardizedIds = await this.standardizeIds()

    // 5. 版本升级
    const upgradedToV2 = await this.upgradeToV2()

    // 6. 质量验证
    await this.validateQuality()

    // 7. 按类别分组输出
    const outputFiles = await this.outputByCategory()

    // 8. 生成报告
    const result: ReorganizationResult = {
      original_count: originalCount,
      deduplicated_count: deduplicatedCount,
      removed_duplicates: originalCount - deduplicatedCount,
      standardized_ids: standardizedIds,
      upgraded_to_v2: upgradedToV2,
      quality_issues: this.qualityIssues,
      output_files: outputFiles
    }

    await this.generateReport(result)
    console.log('✅ 数据重组完成!')

    return result
  }

  /**
   * 备份原始数据
   */
  private async backupOriginalData(): Promise<void> {
    console.log('📋 备份原始数据...')
    
    try {
      await mkdir(BACKUP_DIR, { recursive: true })
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      
      // 备份现有文件
      const filesToBackup = ['morphemes.json', 'morphemes_base.json']
      
      for (const filename of filesToBackup) {
        try {
          const sourcePath = join(DATA_DIR, filename)
          const backupPath = join(BACKUP_DIR, `${timestamp}_${filename}`)
          
          const content = await readFile(sourcePath, 'utf-8')
          await writeFile(backupPath, content, 'utf-8')
          
          console.log(`   ✅ 备份: ${filename} → ${timestamp}_${filename}`)
        } catch (error) {
          console.log(`   ⚠️ 跳过: ${filename} (文件不存在)`)
        }
      }
    } catch (error) {
      console.error('❌ 备份失败:', error)
      throw error
    }
  }

  /**
   * 加载所有数据
   */
  private async loadAllData(): Promise<void> {
    console.log('📁 加载所有数据...')
    
    const files = ['morphemes.json', 'morphemes_base.json']
    
    for (const filename of files) {
      try {
        const filePath = join(DATA_DIR, filename)
        const content = await readFile(filePath, 'utf-8')
        const parsed = JSON.parse(content)
        
        let morphemes: any[]
        if (Array.isArray(parsed)) {
          morphemes = parsed
        } else if (parsed.morphemes && Array.isArray(parsed.morphemes)) {
          morphemes = parsed.morphemes
        } else {
          console.warn(`⚠️ 跳过无效格式文件: ${filename}`)
          continue
        }
        
        // 添加来源标识
        morphemes.forEach(m => {
          m._source_file = filename
        })
        
        this.morphemes.push(...morphemes)
        console.log(`   📄 加载: ${filename} (${morphemes.length}个语素)`)
        
      } catch (error) {
        console.warn(`⚠️ 跳过文件: ${filename} (${error})`)
      }
    }
    
    console.log(`📊 总计加载: ${this.morphemes.length}个语素`)
  }

  /**
   * 数据去重
   */
  private async deduplicateData(): Promise<number> {
    console.log('🔍 执行数据去重...')
    
    const textMap = new Map<string, Morpheme[]>()
    
    // 按文本分组
    for (const morpheme of this.morphemes) {
      const text = morpheme.text
      if (!textMap.has(text)) {
        textMap.set(text, [])
      }
      textMap.get(text)!.push(morpheme)
    }
    
    // 识别重复项
    const uniqueMorphemes: Morpheme[] = []
    
    for (const [text, morphemes] of textMap) {
      if (morphemes.length === 1) {
        uniqueMorphemes.push(morphemes[0])
      } else {
        // 处理重复项
        const duplicate = this.resolveDuplicate(text, morphemes)
        this.duplicates.push(duplicate)
        
        // 保留最佳版本
        const bestMorpheme = morphemes.find(m => m.id === duplicate.keep_id)!
        uniqueMorphemes.push(bestMorpheme)
        
        console.log(`   🔄 去重: "${text}" (保留${duplicate.keep_id}, 移除${duplicate.ids.filter(id => id !== duplicate.keep_id).join(', ')})`)
      }
    }
    
    this.morphemes = uniqueMorphemes
    console.log(`✅ 去重完成: ${uniqueMorphemes.length}个唯一语素`)
    
    return uniqueMorphemes.length
  }

  /**
   * 解决重复项冲突
   */
  private resolveDuplicate(text: string, morphemes: Morpheme[]): DuplicateInfo {
    // 优先级规则：
    // 1. v2.0格式优于v1.0
    // 2. 有完整language_properties和quality_metrics的优先
    // 3. 来源为morphemes_base.json的优先
    // 4. 质量评分更高的优先
    
    let bestMorpheme = morphemes[0]
    let reason = '默认选择第一个'
    
    for (const morpheme of morphemes) {
      // 检查v2.0格式
      if (morpheme.version === '2.0.0' && bestMorpheme.version !== '2.0.0') {
        bestMorpheme = morpheme
        reason = 'v2.0格式优先'
        continue
      }
      
      // 检查完整性
      if (morpheme.language_properties && morpheme.quality_metrics && 
          (!bestMorpheme.language_properties || !bestMorpheme.quality_metrics)) {
        bestMorpheme = morpheme
        reason = '数据完整性更好'
        continue
      }
      
      // 检查来源文件
      if (morpheme._source_file === 'morphemes_base.json' && 
          bestMorpheme._source_file !== 'morphemes_base.json') {
        bestMorpheme = morpheme
        reason = 'base文件优先'
        continue
      }
      
      // 检查质量评分
      if (morpheme.quality_score > bestMorpheme.quality_score) {
        bestMorpheme = morpheme
        reason = '质量评分更高'
      }
    }
    
    return {
      text,
      ids: morphemes.map(m => m.id),
      sources: morphemes.map(m => m._source_file || 'unknown'),
      keep_id: bestMorpheme.id,
      reason
    }
  }

  /**
   * ID标准化
   */
  private async standardizeIds(): Promise<number> {
    console.log('🏷️ 执行ID标准化...')
    
    let standardizedCount = 0
    const categoryCounters = new Map<string, number>()
    
    for (const morpheme of this.morphemes) {
      const category = morpheme.category
      
      // 初始化计数器
      if (!categoryCounters.has(category)) {
        categoryCounters.set(category, 0)
      }
      
      // 生成标准ID
      const counter = categoryCounters.get(category)! + 1
      categoryCounters.set(category, counter)
      
      const standardId = `${category}_${counter.toString().padStart(3, '0')}`
      
      if (morpheme.id !== standardId) {
        console.log(`   🏷️ ID标准化: ${morpheme.id} → ${standardId}`)
        morpheme.id = standardId
        standardizedCount++
      }
    }
    
    console.log(`✅ ID标准化完成: ${standardizedCount}个ID已更新`)
    return standardizedCount
  }

  /**
   * 版本升级到v2.0
   */
  private async upgradeToV2(): Promise<number> {
    console.log('⬆️ 升级到v2.0格式...')
    
    let upgradedCount = 0
    
    for (const morpheme of this.morphemes) {
      if (morpheme.version !== '2.0.0' || !morpheme.language_properties || !morpheme.quality_metrics) {
        // 升级到v2.0格式
        this.upgradeToV2Format(morpheme)
        upgradedCount++
        console.log(`   ⬆️ 升级: ${morpheme.id}`)
      }
    }
    
    console.log(`✅ 版本升级完成: ${upgradedCount}个语素已升级`)
    return upgradedCount
  }

  /**
   * 升级单个语素到v2.0格式
   */
  private upgradeToV2Format(morpheme: any): void {
    // 添加language_properties
    if (!morpheme.language_properties) {
      morpheme.language_properties = {
        syllable_count: Math.ceil((morpheme.text?.length || 1) / 2),
        character_count: morpheme.text?.length || 1,
        phonetic_features: [],
        morphological_type: this.inferMorphologicalType(morpheme.category),
        pronunciation: undefined
      }
    }
    
    // 添加quality_metrics
    if (!morpheme.quality_metrics) {
      morpheme.quality_metrics = {
        naturalness: (morpheme.quality_score || 0.5) * 0.9,
        frequency: morpheme.usage_frequency || 0.5,
        acceptability: (morpheme.quality_score || 0.5) * 0.95,
        aesthetic_appeal: (morpheme.quality_score || 0.5) * 0.85
      }
    }
    
    // 设置版本
    morpheme.version = '2.0.0'
    
    // 移除临时字段
    delete morpheme._source_file
  }

  /**
   * 推断词法类型
   */
  private inferMorphologicalType(category: string): string {
    const typeMap: Record<string, string> = {
      'emotions': '形容词',
      'professions': '名词',
      'characteristics': '形容词',
      'objects': '名词',
      'actions': '动词',
      'concepts': '名词'
    }
    
    return typeMap[category] || '未知'
  }

  /**
   * 质量验证
   */
  private async validateQuality(): Promise<void> {
    console.log('🔍 执行质量验证...')
    
    for (const morpheme of this.morphemes) {
      // 验证必需字段
      const requiredFields = ['id', 'text', 'category', 'quality_score']
      for (const field of requiredFields) {
        if (!(morpheme as any)[field]) {
          this.qualityIssues.push(`${morpheme.id}: 缺少字段 ${field}`)
        }
      }
      
      // 验证质量评分范围
      if (morpheme.quality_score < 0 || morpheme.quality_score > 1) {
        this.qualityIssues.push(`${morpheme.id}: 质量评分超出范围 [0-1]`)
      }
      
      // 验证语义向量
      if (!Array.isArray(morpheme.semantic_vector) || morpheme.semantic_vector.length !== 20) {
        this.qualityIssues.push(`${morpheme.id}: 语义向量维度错误`)
      }
    }
    
    if (this.qualityIssues.length > 0) {
      console.warn(`⚠️ 发现${this.qualityIssues.length}个质量问题`)
    } else {
      console.log('✅ 质量验证通过')
    }
  }

  /**
   * 按类别输出文件
   */
  private async outputByCategory(): Promise<string[]> {
    console.log('📤 按类别输出文件...')
    
    await mkdir(OUTPUT_DIR, { recursive: true })
    
    // 按类别分组
    const categoryGroups = new Map<string, Morpheme[]>()
    
    for (const morpheme of this.morphemes) {
      const category = morpheme.category
      if (!categoryGroups.has(category)) {
        categoryGroups.set(category, [])
      }
      categoryGroups.get(category)!.push(morpheme)
    }
    
    const outputFiles: string[] = []
    
    // 输出分类文件
    for (const [category, morphemes] of categoryGroups) {
      const filename = `${category}.json`
      const filePath = join(OUTPUT_DIR, filename)
      
      await writeFile(filePath, JSON.stringify(morphemes, null, 2), 'utf-8')
      outputFiles.push(filename)
      
      console.log(`   📄 输出: ${filename} (${morphemes.length}个语素)`)
    }
    
    // 输出合并文件
    const allFilename = 'morphemes_v2_unified.json'
    const allFilePath = join(OUTPUT_DIR, allFilename)
    await writeFile(allFilePath, JSON.stringify(this.morphemes, null, 2), 'utf-8')
    outputFiles.push(allFilename)
    
    console.log(`   📄 输出: ${allFilename} (${this.morphemes.length}个语素)`)
    
    return outputFiles
  }

  /**
   * 生成重组报告
   */
  private async generateReport(result: ReorganizationResult): Promise<void> {
    const report = {
      timestamp: new Date().toISOString(),
      summary: result,
      duplicates: this.duplicates,
      quality_issues: this.qualityIssues,
      final_morphemes: this.morphemes.map(m => ({
        id: m.id,
        text: m.text,
        category: m.category,
        quality_score: m.quality_score,
        version: m.version
      }))
    }
    
    const reportPath = join(OUTPUT_DIR, 'reorganization_report.json')
    await writeFile(reportPath, JSON.stringify(report, null, 2), 'utf-8')
    
    console.log(`📊 重组报告已生成: ${reportPath}`)
  }
}

// ============================================================================
// 主函数
// ============================================================================

async function main() {
  try {
    const reorganizer = new DataReorganizer()
    const result = await reorganizer.reorganize()
    
    console.log('\n📊 重组结果摘要:')
    console.log(`   原始语素数量: ${result.original_count}`)
    console.log(`   去重后数量: ${result.deduplicated_count}`)
    console.log(`   移除重复: ${result.removed_duplicates}`)
    console.log(`   标准化ID: ${result.standardized_ids}`)
    console.log(`   升级v2.0: ${result.upgraded_to_v2}`)
    console.log(`   质量问题: ${result.quality_issues.length}`)
    console.log(`   输出文件: ${result.output_files.length}`)
    
  } catch (error) {
    console.error('❌ 数据重组失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { DataReorganizer }
