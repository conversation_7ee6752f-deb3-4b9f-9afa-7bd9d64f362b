/**
 * JSDoc 配置文件
 *
 * 用于生成完整的API文档和代码文档
 *
 * @fileoverview JSDoc配置
 * @version 1.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */

module.exports = {
  source: {
    include: [
      './types/',
      './core/',
      './config/',
      './test/',
      './README.md'
    ],
    includePattern: '\\.(js|ts)$',
    exclude: [
      'node_modules/',
      'coverage/',
      'dist/',
      '.jest-cache/',
      '*.test.ts',
      '*.spec.ts'
    ]
  },
  opts: {
    destination: './docs/api/',
    recurse: true,
    readme: './README.md'
  },
  plugins: [
    'plugins/markdown',
    'plugins/summarize'
  ],
  templates: {
    cleverLinks: false,
    monospaceLinks: false
  },
  tags: {
    allowUnknownTags: true,
    dictionaries: ['jsdoc', 'closure']
  },
  markdown: {
    parser: 'gfm'
  }
}
