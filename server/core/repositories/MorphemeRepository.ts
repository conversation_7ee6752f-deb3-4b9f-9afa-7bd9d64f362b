/**
 * 语素仓库类
 *
 * 负责语素数据的加载、索引、查询和智能采样。
 * 支持多维度索引、语义相似度计算和高效的O(1)采样算法。
 *
 * @fileoverview 语素数据仓库核心模块
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */

import type {
  Morpheme,
  MorphemeCategory,
  CulturalContext,
  SampleCriteria
} from '../../types/core'
import { DataLoader, type DataLoadResult } from '../data/DataLoader'
import { DataValidator, type ValidationResult } from '../data/DataValidator'

// ============================================================================
// 接口定义
// ============================================================================

/**
 * 语素仓库统计信息接口
 */
export interface MorphemeRepositoryStats {
  /** 语素总数 */
  total: number
  /** 按类别分布 */
  byCategory: Record<string, number>
  /** 按文化语境分布 */
  byContext: Record<string, number>
  /** 按子分类分布 */
  bySubcategory: Record<string, number>
  /** 平均质量评分 */
  avgQuality: number
  /** 平均使用频率 */
  avgFrequency: number
  /** 索引构建时间 */
  indexBuildTime: number
  /** 最后更新时间 */
  lastUpdated: number
}

/**
 * 语义相似度查询结果接口
 */
export interface SimilarityResult {
  /** 语素 */
  morpheme: Morpheme
  /** 相似度分数 [0-1] */
  similarity: number
}

/**
 * Alias Table 采样器接口
 *
 * 实现O(1)时间复杂度的加权随机采样
 */
interface AliasTable {
  /** 概率数组 */
  prob: number[]
  /** 别名数组 */
  alias: number[]
  /** 采样方法 */
  sample(): number
}

// ============================================================================
// 语素仓库类
// ============================================================================

/**
 * 语素仓库类
 *
 * 提供高效的语素数据管理和查询功能
 */
export class MorphemeRepository {
  // 数据存储
  private morphemes: Map<string, Morpheme> = new Map()

  // 多维度索引系统
  private indices: {
    byCategory: Map<MorphemeCategory, Morpheme[]>
    byContext: Map<CulturalContext, Morpheme[]>
    bySubcategory: Map<string, Morpheme[]>
    byQuality: Map<number, Morpheme[]>
    bySemanticCluster: Map<string, Morpheme[]>
    byTags: Map<string, Morpheme[]>
  } = {
    byCategory: new Map(),
    byContext: new Map(),
    bySubcategory: new Map(),
    byQuality: new Map(),
    bySemanticCluster: new Map(),
    byTags: new Map()
  }

  // O(1)采样算法支持
  private aliasTables: Map<string, AliasTable> = new Map()

  // 组件依赖
  private dataLoader: DataLoader
  private dataValidator: DataValidator

  // 状态管理
  private isInitialized = false
  private lastLoadResult: DataLoadResult | null = null
  private stats: MorphemeRepositoryStats | null = null

  /**
   * 构造函数
   *
   * @param dataLoader 数据加载器实例
   * @param dataValidator 数据验证器实例
   */
  constructor(
    dataLoader?: DataLoader,
    dataValidator?: DataValidator
  ) {
    this.dataLoader = dataLoader || new DataLoader()
    this.dataValidator = dataValidator || new DataValidator()
  }

  /**
   * 初始化语素仓库
   *
   * 加载数据、验证完整性、构建索引和采样表
   *
   * @returns Promise<void>
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('📋 语素仓库已初始化，跳过重复初始化')
      return
    }

    const startTime = Date.now()
    console.log('🚀 开始初始化语素仓库...')

    try {
      // 1. 加载语素数据
      console.log('📁 加载语素数据...')
      this.lastLoadResult = await this.dataLoader.loadAll()

      if (!this.lastLoadResult.validation.passed) {
        throw new Error(`数据验证失败: ${this.lastLoadResult.validation.errors.join(', ')}`)
      }

      // 2. 构建语素映射
      console.log('🗂️ 构建语素映射...')
      this.buildMorphemeMap(this.lastLoadResult.morphemes)

      // 3. 构建多维度索引
      console.log('📊 构建多维度索引...')
      await this.buildIndices()

      // 4. 构建Alias Table采样表
      console.log('🎲 构建采样表...')
      this.buildAliasTables()

      // 5. 计算统计信息
      console.log('📈 计算统计信息...')
      this.calculateStats(Date.now() - startTime)

      this.isInitialized = true

      const initTime = Date.now() - startTime
      console.log(`✅ 语素仓库初始化完成: ${this.morphemes.size}个语素, 耗时${initTime}ms`)

      // 输出统计摘要
      this.logInitializationSummary()

    } catch (error) {
      console.error('❌ 语素仓库初始化失败:', error)
      throw new Error(`语素仓库初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 重新加载数据
   *
   * 用于热重载或数据更新场景
   *
   * @returns Promise<void>
   */
  async reload(): Promise<void> {
    console.log('🔄 重新加载语素数据...')

    // 清空现有数据
    this.morphemes.clear()
    this.clearIndices()
    this.aliasTables.clear()
    this.isInitialized = false

    // 重新初始化
    await this.initialize()
  }

  /**
   * 输出初始化摘要
   *
   * @private
   */
  private logInitializationSummary(): void {
    if (!this.stats) return

    console.log('📊 语素仓库统计摘要:')
    console.log(`   总计: ${this.stats.total} 个语素`)
    console.log(`   平均质量: ${this.stats.avgQuality.toFixed(3)}`)
    console.log(`   平均频率: ${this.stats.avgFrequency.toFixed(3)}`)

    console.log('   类别分布:')
    for (const [category, count] of Object.entries(this.stats.byCategory)) {
      const percentage = ((count / this.stats.total) * 100).toFixed(1)
      console.log(`     ${category}: ${count} (${percentage}%)`)
    }

    console.log('   文化语境分布:')
    for (const [context, count] of Object.entries(this.stats.byContext)) {
      const percentage = ((count / this.stats.total) * 100).toFixed(1)
      console.log(`     ${context}: ${count} (${percentage}%)`)
    }
  }

  /**
   * 构建语素映射
   *
   * 将语素数组转换为高效的Map结构
   *
   * @private
   * @param morphemes 语素数组
   */
  private buildMorphemeMap(morphemes: Morpheme[]): void {
    this.morphemes.clear()

    for (const morpheme of morphemes) {
      this.morphemes.set(morpheme.id, morpheme)
    }

    console.log(`🗂️ 构建语素映射完成: ${this.morphemes.size} 个语素`)
  }

  /**
   * 构建多维度索引
   *
   * 为高效查询构建各种索引结构
   *
   * @private
   * @returns Promise<void>
   */
  private async buildIndices(): Promise<void> {
    const startTime = Date.now()

    // 清空现有索引
    this.clearIndices()

    // 构建各维度索引
    for (const morpheme of this.morphemes.values()) {
      // 1. 按类别索引
      this.addToIndex(this.indices.byCategory, morpheme.category, morpheme)

      // 2. 按文化语境索引
      this.addToIndex(this.indices.byContext, morpheme.cultural_context, morpheme)

      // 3. 按子分类索引
      this.addToIndex(this.indices.bySubcategory, morpheme.subcategory, morpheme)

      // 4. 按质量分数索引（分桶：0.1精度）
      const qualityBucket = Math.round(morpheme.quality_score * 10) / 10
      this.addToIndex(this.indices.byQuality, qualityBucket, morpheme)

      // 5. 按语义聚类索引（基于语义向量的聚类）
      const semanticCluster = this.calculateSemanticCluster(morpheme.semantic_vector)
      this.addToIndex(this.indices.bySemanticCluster, semanticCluster, morpheme)

      // 6. 按标签索引
      for (const tag of morpheme.tags) {
        this.addToIndex(this.indices.byTags, tag, morpheme)
      }
    }

    const indexTime = Date.now() - startTime
    console.log(`📊 多维度索引构建完成: 耗时${indexTime}ms`)

    // 输出索引统计
    this.logIndexStats()
  }

  /**
   * 清空所有索引
   *
   * @private
   */
  private clearIndices(): void {
    this.indices.byCategory.clear()
    this.indices.byContext.clear()
    this.indices.bySubcategory.clear()
    this.indices.byQuality.clear()
    this.indices.bySemanticCluster.clear()
    this.indices.byTags.clear()
  }

  /**
   * 添加到索引
   *
   * @private
   * @param index 索引Map
   * @param key 索引键
   * @param morpheme 语素
   */
  private addToIndex<K>(index: Map<K, Morpheme[]>, key: K, morpheme: Morpheme): void {
    if (!index.has(key)) {
      index.set(key, [])
    }
    index.get(key)!.push(morpheme)
  }

  /**
   * 计算语义聚类
   *
   * 基于语义向量计算聚类标识
   *
   * @private
   * @param semanticVector 语义向量
   * @returns 聚类标识
   */
  private calculateSemanticCluster(semanticVector: number[]): string {
    // 简化的聚类算法：基于向量的主要维度
    const maxIndex = semanticVector.indexOf(Math.max(...semanticVector))
    const avgValue = semanticVector.reduce((sum, val) => sum + val, 0) / semanticVector.length

    // 根据最大值索引和平均值确定聚类
    if (avgValue > 0.7) {
      return `high_${Math.floor(maxIndex / 5)}`
    } else if (avgValue > 0.4) {
      return `medium_${Math.floor(maxIndex / 5)}`
    } else {
      return `low_${Math.floor(maxIndex / 5)}`
    }
  }

  /**
   * 输出索引统计信息
   *
   * @private
   */
  private logIndexStats(): void {
    console.log('📊 索引统计:')
    console.log(`   类别索引: ${this.indices.byCategory.size} 个类别`)
    console.log(`   文化语境索引: ${this.indices.byContext.size} 个语境`)
    console.log(`   子分类索引: ${this.indices.bySubcategory.size} 个子分类`)
    console.log(`   质量分桶索引: ${this.indices.byQuality.size} 个分桶`)
    console.log(`   语义聚类索引: ${this.indices.bySemanticCluster.size} 个聚类`)
    console.log(`   标签索引: ${this.indices.byTags.size} 个标签`)
  }

  /**
   * 构建Alias Table采样表
   *
   * 为每个类别构建O(1)时间复杂度的加权采样表
   *
   * @private
   */
  private buildAliasTables(): void {
    this.aliasTables.clear()

    // 为每个类别构建Alias Table
    for (const [category, morphemes] of this.indices.byCategory.entries()) {
      if (morphemes.length === 0) continue

      const weights = morphemes.map(m => m.usage_frequency * m.quality_score)
      const aliasTable = this.createAliasTable(weights)
      this.aliasTables.set(category, aliasTable)
    }

    // 为每个文化语境构建Alias Table
    for (const [context, morphemes] of this.indices.byContext.entries()) {
      if (morphemes.length === 0) continue

      const weights = morphemes.map(m => m.usage_frequency * m.quality_score)
      const aliasTable = this.createAliasTable(weights)
      this.aliasTables.set(`context_${context}`, aliasTable)
    }

    console.log(`🎲 Alias Table构建完成: ${this.aliasTables.size} 个采样表`)
  }

  /**
   * 创建Alias Table
   *
   * 实现Walker's Alias Method算法
   *
   * @private
   * @param weights 权重数组
   * @returns Alias Table
   */
  private createAliasTable(weights: number[]): AliasTable {
    const n = weights.length
    const prob = new Array(n)
    const alias = new Array(n)

    // 归一化权重
    const sum = weights.reduce((a, b) => a + b, 0)
    if (sum === 0) {
      // 如果所有权重都为0，使用均匀分布
      prob.fill(1.0)
      alias.fill(0)
      return {
        prob,
        alias,
        sample: () => Math.floor(Math.random() * n)
      }
    }

    const normalizedWeights = weights.map(w => w * n / sum)

    // 分离小于1和大于等于1的权重
    const small: number[] = []
    const large: number[] = []

    for (let i = 0; i < n; i++) {
      if (normalizedWeights[i] < 1.0) {
        small.push(i)
      } else {
        large.push(i)
      }
    }

    // 构建Alias Table
    while (small.length > 0 && large.length > 0) {
      const l = small.pop()!
      const g = large.pop()!

      prob[l] = normalizedWeights[l]
      alias[l] = g

      normalizedWeights[g] = normalizedWeights[g] + normalizedWeights[l] - 1.0

      if (normalizedWeights[g] < 1.0) {
        small.push(g)
      } else {
        large.push(g)
      }
    }

    // 处理剩余项
    while (large.length > 0) {
      prob[large.pop()!] = 1.0
    }

    while (small.length > 0) {
      prob[small.pop()!] = 1.0
    }

    return {
      prob,
      alias,
      sample(): number {
        const i = Math.floor(Math.random() * n)
        const r = Math.random()
        return r < prob[i] ? i : alias[i]
      }
    }
  }

  /**
   * 计算统计信息
   *
   * @private
   * @param indexBuildTime 索引构建时间
   */
  private calculateStats(indexBuildTime: number): void {
    const morphemeArray = Array.from(this.morphemes.values())

    // 计算平均质量和频率
    const totalQuality = morphemeArray.reduce((sum, m) => sum + m.quality_score, 0)
    const totalFrequency = morphemeArray.reduce((sum, m) => sum + m.usage_frequency, 0)

    // 按类别统计
    const byCategory: Record<string, number> = {}
    for (const [category, morphemes] of this.indices.byCategory.entries()) {
      byCategory[category] = morphemes.length
    }

    // 按文化语境统计
    const byContext: Record<string, number> = {}
    for (const [context, morphemes] of this.indices.byContext.entries()) {
      byContext[context] = morphemes.length
    }

    // 按子分类统计
    const bySubcategory: Record<string, number> = {}
    for (const [subcategory, morphemes] of this.indices.bySubcategory.entries()) {
      bySubcategory[subcategory] = morphemes.length
    }

    this.stats = {
      total: this.morphemes.size,
      byCategory,
      byContext,
      bySubcategory,
      avgQuality: this.morphemes.size > 0 ? totalQuality / this.morphemes.size : 0,
      avgFrequency: this.morphemes.size > 0 ? totalFrequency / this.morphemes.size : 0,
      indexBuildTime,
      lastUpdated: Date.now()
    }
  }

  /**
   * 根据ID获取语素
   */
  findById(id: string): Morpheme | undefined {
    return this.morphemes.get(id)
  }

  /**
   * 根据类别获取语素
   */
  findByCategory(category: MorphemeCategory): Morpheme[] {
    return this.indices.byCategory.get(category) || []
  }

  /**
   * 根据文化语境获取语素
   */
  findByContext(context: CulturalContext): Morpheme[] {
    return this.indices.byContext.get(context) || []
  }

  /**
   * 根据子分类获取语素
   *
   * @param subcategory 子分类
   * @returns 语素数组
   */
  findBySubcategory(subcategory: string): Morpheme[] {
    return this.indices.bySubcategory.get(subcategory) || []
  }

  /**
   * 根据标签获取语素
   *
   * @param tag 标签
   * @returns 语素数组
   */
  findByTag(tag: string): Morpheme[] {
    return this.indices.byTags.get(tag) || []
  }

  /**
   * 根据语义聚类获取语素
   *
   * @param cluster 聚类标识
   * @returns 语素数组
   */
  findBySemanticCluster(cluster: string): Morpheme[] {
    return this.indices.bySemanticCluster.get(cluster) || []
  }

  /**
   * 查找语义相似的语素
   *
   * @param targetMorpheme 目标语素
   * @param threshold 相似度阈值 [0-1]
   * @param limit 返回数量限制
   * @returns 相似度结果数组
   */
  findSimilar(targetMorpheme: Morpheme, threshold: number = 0.7, limit: number = 10): SimilarityResult[] {
    const results: SimilarityResult[] = []

    for (const morpheme of this.morphemes.values()) {
      if (morpheme.id === targetMorpheme.id) continue

      const similarity = this.calculateSemanticSimilarity(
        targetMorpheme.semantic_vector,
        morpheme.semantic_vector
      )

      if (similarity >= threshold) {
        results.push({ morpheme, similarity })
      }
    }

    // 按相似度降序排序并限制数量
    return results
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
  }

  /**
   * 计算语义相似度
   *
   * 使用余弦相似度算法
   *
   * @param vector1 向量1
   * @param vector2 向量2
   * @returns 相似度 [0-1]
   */
  calculateSemanticSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) {
      throw new Error('语义向量维度不匹配')
    }

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i]
      norm1 += vector1[i] * vector1[i]
      norm2 += vector2[i] * vector2[i]
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  /**
   * 使用Alias Table进行高效采样
   *
   * @param category 类别
   * @param count 采样数量
   * @returns 采样结果
   */
  sampleByCategory(category: MorphemeCategory, count: number = 1): Morpheme[] {
    const morphemes = this.indices.byCategory.get(category)
    const aliasTable = this.aliasTables.get(category)

    if (!morphemes || !aliasTable || morphemes.length === 0) {
      return []
    }

    const results: Morpheme[] = []
    const usedIndices = new Set<number>()

    for (let i = 0; i < count && usedIndices.size < morphemes.length; i++) {
      let index = aliasTable.sample()

      // 避免重复采样
      while (usedIndices.has(index) && usedIndices.size < morphemes.length) {
        index = aliasTable.sample()
      }

      usedIndices.add(index)
      results.push(morphemes[index])
    }

    return results
  }

  /**
   * 使用Alias Table按文化语境采样
   *
   * @param context 文化语境
   * @param count 采样数量
   * @returns 采样结果
   */
  sampleByContext(context: CulturalContext, count: number = 1): Morpheme[] {
    const morphemes = this.indices.byContext.get(context)
    const aliasTable = this.aliasTables.get(`context_${context}`)

    if (!morphemes || !aliasTable || morphemes.length === 0) {
      return []
    }

    const results: Morpheme[] = []
    const usedIndices = new Set<number>()

    for (let i = 0; i < count && usedIndices.size < morphemes.length; i++) {
      let index = aliasTable.sample()

      // 避免重复采样
      while (usedIndices.has(index) && usedIndices.size < morphemes.length) {
        index = aliasTable.sample()
      }

      usedIndices.add(index)
      results.push(morphemes[index])
    }

    return results
  }

  /**
   * 根据质量分数范围获取语素
   */
  findByQualityRange(minScore: number, maxScore: number): Morpheme[] {
    const results: Morpheme[] = []

    for (const [score, morphemes] of this.indices.byQuality.entries()) {
      if (score >= minScore && score <= maxScore) {
        results.push(...morphemes)
      }
    }

    return results
  }

  /**
   * 根据条件采样语素
   */
  sample(criteria: SampleCriteria): Morpheme[] {
    let candidates = Array.from(this.morphemes.values())

    // 按类别过滤
    if (criteria.category) {
      candidates = candidates.filter(m => m.category === criteria.category)
    }

    // 按文化语境过滤
    if (criteria.cultural_context) {
      candidates = candidates.filter(m => m.cultural_context === criteria.cultural_context)
    }

    // 按质量分数过滤
    if (criteria.min_quality_score !== undefined) {
      candidates = candidates.filter(m => m.quality_score >= criteria.min_quality_score!)
    }
    if (criteria.max_quality_score !== undefined) {
      candidates = candidates.filter(m => m.quality_score <= criteria.max_quality_score!)
    }

    // 按标签过滤
    if (criteria.tags && criteria.tags.length > 0) {
      candidates = candidates.filter(m =>
        criteria.tags!.some(tag => m.tags.includes(tag))
      )
    }

    // 排除指定ID
    if (criteria.exclude_ids && criteria.exclude_ids.length > 0) {
      candidates = candidates.filter(m => !criteria.exclude_ids!.includes(m.id))
    }

    // 随机采样
    if (criteria.limit && candidates.length > criteria.limit) {
      // 简单的随机采样，实际实现中可以使用更复杂的采样算法
      const shuffled = [...candidates].sort(() => Math.random() - 0.5)
      candidates = shuffled.slice(0, criteria.limit)
    }

    return candidates
  }

  /**
   * 获取所有语素
   */
  getAll(): Morpheme[] {
    return Array.from(this.morphemes.values())
  }

  /**
   * 获取仓库统计信息
   *
   * @returns 详细统计信息
   */
  getStats(): MorphemeRepositoryStats {
    if (!this.stats) {
      throw new Error('语素仓库未初始化或统计信息不可用')
    }
    return { ...this.stats }
  }

  /**
   * 获取数据加载结果
   *
   * @returns 最后一次数据加载结果
   */
  getLastLoadResult(): DataLoadResult | null {
    return this.lastLoadResult
  }

  /**
   * 检查是否已初始化
   *
   * @returns 初始化状态
   */
  isReady(): boolean {
    return this.isInitialized
  }

  /**
   * 获取语素总数
   *
   * @returns 语素数量
   */
  getCount(): number {
    return this.morphemes.size
  }

  /**
   * 清理资源
   *
   * 清理数据加载器和验证器资源
   */
  destroy(): void {
    this.dataLoader.destroy()
    this.morphemes.clear()
    this.clearIndices()
    this.aliasTables.clear()
    this.isInitialized = false
    this.stats = null
    this.lastLoadResult = null

    console.log('🧹 语素仓库资源已清理')
  }
}