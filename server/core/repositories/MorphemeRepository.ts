/**
 * 语素仓库类
 * 负责语素数据的加载、索引和查询
 */

import type {
  Morpheme,
  MorphemeCategory,
  CulturalContext,
  SampleCriteria
} from '../../types/core'

export class MorphemeRepository {
  private morphemes: Map<string, Morpheme> = new Map()
  private indices: {
    byCategory: Map<MorphemeCategory, Morpheme[]>
    byContext: Map<CulturalContext, Morpheme[]>
    byQuality: Map<number, Morpheme[]>
  } = {
    byCategory: new Map(),
    byContext: new Map(),
    byQuality: new Map()
  }

  private isInitialized = false

  /**
   * 初始化语素仓库
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      // 加载语素数据
      const morphemeData = await this.loadMorphemeData()

      // 构建语素映射和索引
      this.buildMorphemeMap(morphemeData.morphemes)
      this.buildIndices()

      this.isInitialized = true
      console.log(`MorphemeRepository initialized with ${this.morphemes.size} morphemes`)
    } catch (error) {
      console.error('Failed to initialize MorphemeRepository:', error)
      throw error
    }
  }

  /**
   * 加载语素数据文件
   */
  private async loadMorphemeData(): Promise<{ morphemes: Morpheme[] }> {
    try {
      // 在实际环境中，这里会从文件系统加载数据
      // 为了MVP演示，我们使用硬编码的数据
      return {
        morphemes: [
          {
            id: "emotion_001",
            text: "温暖",
            category: "emotions",
            subcategory: "positive_emotions",
            cultural_context: "neutral",
            usage_frequency: 0.85,
            quality_score: 0.9,
            semantic_vector: [0.8, 0.6, 0.7, 0.9, 0.5, 0.4, 0.6, 0.7, 0.8, 0.5, 0.6, 0.7, 0.8, 0.4, 0.5, 0.6, 0.7, 0.8, 0.5, 0.6],
            tags: ["温暖", "正面", "治愈"],
            created_at: 1703145600000,
            source: "emotion_expansion_mvp"
          },
          {
            id: "emotion_002",
            text: "清新",
            category: "emotions",
            subcategory: "artistic_emotions",
            cultural_context: "modern",
            usage_frequency: 0.75,
            quality_score: 0.85,
            semantic_vector: [0.7, 0.8, 0.6, 0.8, 0.6, 0.5, 0.7, 0.8, 0.7, 0.6, 0.7, 0.8, 0.7, 0.5, 0.6, 0.7, 0.8, 0.7, 0.6, 0.7],
            tags: ["清新", "文艺", "现代"],
            created_at: 1703145600000,
            source: "emotion_expansion_mvp"
          },
          {
            id: "emotion_003",
            text: "优雅",
            category: "emotions",
            subcategory: "artistic_emotions",
            cultural_context: "neutral",
            usage_frequency: 0.8,
            quality_score: 0.88,
            semantic_vector: [0.9, 0.7, 0.8, 0.8, 0.7, 0.6, 0.8, 0.9, 0.8, 0.7, 0.8, 0.9, 0.8, 0.6, 0.7, 0.8, 0.9, 0.8, 0.7, 0.8],
            tags: ["优雅", "高贵", "气质"],
            created_at: 1703145600000,
            source: "emotion_expansion_mvp"
          },
          {
            id: "profession_001",
            text: "设计师",
            category: "professions",
            subcategory: "creative_professions",
            cultural_context: "modern",
            usage_frequency: 0.85,
            quality_score: 0.88,
            semantic_vector: [0.5, 0.8, 0.6, 0.4, 0.7, 0.9, 0.8, 0.6, 0.5, 0.7, 0.8, 0.6, 0.4, 0.9, 0.7, 0.5, 0.6, 0.8, 0.7, 0.4],
            tags: ["创意", "设计", "现代"],
            created_at: 1703145600000,
            source: "profession_expansion_mvp"
          },
          {
            id: "profession_002",
            text: "工程师",
            category: "professions",
            subcategory: "technical_professions",
            cultural_context: "modern",
            usage_frequency: 0.9,
            quality_score: 0.85,
            semantic_vector: [0.6, 0.7, 0.8, 0.5, 0.8, 0.9, 0.7, 0.6, 0.8, 0.7, 0.6, 0.8, 0.5, 0.9, 0.8, 0.6, 0.7, 0.8, 0.7, 0.5],
            tags: ["技术", "专业", "现代"],
            created_at: 1703145600000,
            source: "profession_expansion_mvp"
          },
          {
            id: "profession_003",
            text: "艺术家",
            category: "professions",
            subcategory: "creative_professions",
            cultural_context: "neutral",
            usage_frequency: 0.75,
            quality_score: 0.9,
            semantic_vector: [0.8, 0.9, 0.7, 0.6, 0.8, 0.7, 0.9, 0.8, 0.7, 0.8, 0.9, 0.7, 0.6, 0.8, 0.7, 0.9, 0.8, 0.7, 0.8, 0.9],
            tags: ["艺术", "创意", "文化"],
            created_at: 1703145600000,
            source: "profession_expansion_mvp"
          },
          {
            id: "characteristic_001",
            text: "聪明",
            category: "characteristics",
            subcategory: "ability_traits",
            cultural_context: "neutral",
            usage_frequency: 0.9,
            quality_score: 0.85,
            semantic_vector: [0.7, 0.5, 0.8, 0.6, 0.9, 0.5, 0.7, 0.8, 0.6, 0.4, 0.7, 0.9, 0.5, 0.6, 0.8, 0.7, 0.4, 0.5, 0.9, 0.6],
            tags: ["智慧", "能力", "正面"],
            created_at: 1703145600000,
            source: "characteristic_expansion_mvp"
          },
          {
            id: "characteristic_002",
            text: "优秀",
            category: "characteristics",
            subcategory: "quality_traits",
            cultural_context: "neutral",
            usage_frequency: 0.85,
            quality_score: 0.88,
            semantic_vector: [0.8, 0.7, 0.9, 0.6, 0.8, 0.7, 0.9, 0.8, 0.6, 0.7, 0.9, 0.8, 0.6, 0.7, 0.9, 0.8, 0.6, 0.7, 0.9, 0.8],
            tags: ["优秀", "品质", "正面"],
            created_at: 1703145600000,
            source: "characteristic_expansion_mvp"
          }
        ]
      }
    } catch (error) {
      console.error('Failed to load morpheme data:', error)
      throw new Error('Failed to load morpheme data')
    }
  }

  /**
   * 构建语素映射
   */
  private buildMorphemeMap(morphemes: Morpheme[]): void {
    this.morphemes.clear()

    for (const morpheme of morphemes) {
      this.morphemes.set(morpheme.id, morpheme)
    }
  }

  /**
   * 构建索引
   */
  private buildIndices(): void {
    // 清空现有索引
    this.indices.byCategory.clear()
    this.indices.byContext.clear()
    this.indices.byQuality.clear()

    // 构建分类索引
    for (const morpheme of this.morphemes.values()) {
      // 按类别索引
      if (!this.indices.byCategory.has(morpheme.category)) {
        this.indices.byCategory.set(morpheme.category, [])
      }
      this.indices.byCategory.get(morpheme.category)!.push(morpheme)

      // 按文化语境索引
      if (!this.indices.byContext.has(morpheme.cultural_context)) {
        this.indices.byContext.set(morpheme.cultural_context, [])
      }
      this.indices.byContext.get(morpheme.cultural_context)!.push(morpheme)

      // 按质量分数索引（四舍五入到0.1）
      const qualityBucket = Math.round(morpheme.quality_score * 10) / 10
      if (!this.indices.byQuality.has(qualityBucket)) {
        this.indices.byQuality.set(qualityBucket, [])
      }
      this.indices.byQuality.get(qualityBucket)!.push(morpheme)
    }
  }

  /**
   * 根据ID获取语素
   */
  findById(id: string): Morpheme | undefined {
    return this.morphemes.get(id)
  }

  /**
   * 根据类别获取语素
   */
  findByCategory(category: MorphemeCategory): Morpheme[] {
    return this.indices.byCategory.get(category) || []
  }

  /**
   * 根据文化语境获取语素
   */
  findByContext(context: CulturalContext): Morpheme[] {
    return this.indices.byContext.get(context) || []
  }

  /**
   * 根据质量分数范围获取语素
   */
  findByQualityRange(minScore: number, maxScore: number): Morpheme[] {
    const results: Morpheme[] = []

    for (const [score, morphemes] of this.indices.byQuality.entries()) {
      if (score >= minScore && score <= maxScore) {
        results.push(...morphemes)
      }
    }

    return results
  }

  /**
   * 根据条件采样语素
   */
  sample(criteria: SampleCriteria): Morpheme[] {
    let candidates = Array.from(this.morphemes.values())

    // 按类别过滤
    if (criteria.category) {
      candidates = candidates.filter(m => m.category === criteria.category)
    }

    // 按文化语境过滤
    if (criteria.cultural_context) {
      candidates = candidates.filter(m => m.cultural_context === criteria.cultural_context)
    }

    // 按质量分数过滤
    if (criteria.min_quality_score !== undefined) {
      candidates = candidates.filter(m => m.quality_score >= criteria.min_quality_score!)
    }
    if (criteria.max_quality_score !== undefined) {
      candidates = candidates.filter(m => m.quality_score <= criteria.max_quality_score!)
    }

    // 按标签过滤
    if (criteria.tags && criteria.tags.length > 0) {
      candidates = candidates.filter(m =>
        criteria.tags!.some(tag => m.tags.includes(tag))
      )
    }

    // 排除指定ID
    if (criteria.exclude_ids && criteria.exclude_ids.length > 0) {
      candidates = candidates.filter(m => !criteria.exclude_ids!.includes(m.id))
    }

    // 随机采样
    if (criteria.limit && candidates.length > criteria.limit) {
      // 简单的随机采样，实际实现中可以使用更复杂的采样算法
      const shuffled = [...candidates].sort(() => Math.random() - 0.5)
      candidates = shuffled.slice(0, criteria.limit)
    }

    return candidates
  }

  /**
   * 获取所有语素
   */
  getAll(): Morpheme[] {
    return Array.from(this.morphemes.values())
  }

  /**
   * 获取语素总数
   */
  getCount(): number {
    return this.morphemes.size
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    total: number
    byCategory: Record<string, number>
    byContext: Record<string, number>
    avgQuality: number
  } {
    const stats = {
      total: this.morphemes.size,
      byCategory: {} as Record<string, number>,
      byContext: {} as Record<string, number>,
      avgQuality: 0
    }

    let totalQuality = 0
    for (const morpheme of this.morphemes.values()) {
      // 统计类别分布
      stats.byCategory[morpheme.category] = (stats.byCategory[morpheme.category] || 0) + 1

      // 统计文化语境分布
      stats.byContext[morpheme.cultural_context] = (stats.byContext[morpheme.cultural_context] || 0) + 1

      // 累计质量分数
      totalQuality += morpheme.quality_score
    }

    stats.avgQuality = stats.total > 0 ? totalQuality / stats.total : 0

    return stats
  }
}