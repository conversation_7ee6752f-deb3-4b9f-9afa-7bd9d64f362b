/**
 * 语义对齐器
 * 
 * 负责跨语言语义对齐、概念映射、语义相似度计算等功能
 * 确保多语种生成的语义一致性和文化适配性
 * 
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */

import { LanguageCode, SEMANTIC_VECTOR_DIMENSIONS } from '../../types/multilingual.js'
import type {
  UniversalConcept,
  LanguageSpecificMorpheme,
  UniversalSemanticVector
} from '../../types/multilingual.js'

// ============================================================================
// 语义对齐器接口
// ============================================================================

export interface SemanticAlignment {
  /** 源概念ID */
  sourceConceptId: string
  /** 目标语言 */
  targetLanguage: LanguageCode
  /** 对齐的语素列表 */
  alignedMorphemes: LanguageSpecificMorpheme[]
  /** 对齐分数 [0-1] */
  alignmentScore: number
  /** 语义相似度 [0-1] */
  semanticSimilarity: number
  /** 文化适配度 [0-1] */
  culturalFit: number
  /** 对齐置信度 [0-1] */
  confidence: number
}

export interface CrossLingualMapping {
  /** 概念ID */
  conceptId: string
  /** 各语言的语素映射 */
  languageMorphemes: Map<LanguageCode, LanguageSpecificMorpheme[]>
  /** 跨语言一致性评分 [0-1] */
  consistencyScore: number
  /** 最佳对齐语言对 */
  bestAlignments: Array<{
    language1: LanguageCode
    language2: LanguageCode
    score: number
  }>
}

export interface SemanticAlignerConfig {
  /** 语义相似度阈值 */
  semanticSimilarityThreshold: number
  /** 文化适配权重 */
  culturalFitWeight: number
  /** 语义权重 */
  semanticWeight: number
  /** 质量权重 */
  qualityWeight: number
}

// ============================================================================
// 语义对齐器类
// ============================================================================

/**
 * 语义对齐器类
 * 
 * 提供跨语言语义对齐和概念映射功能
 */
export class SemanticAligner {
  private config: SemanticAlignerConfig

  /**
   * 构造函数
   */
  constructor(config: Partial<SemanticAlignerConfig> = {}) {
    this.config = {
      semanticSimilarityThreshold: config.semanticSimilarityThreshold || 0.75,
      culturalFitWeight: config.culturalFitWeight || 0.3,
      semanticWeight: config.semanticWeight || 0.5,
      qualityWeight: config.qualityWeight || 0.2
    }
  }

  /**
   * 计算概念与语素的语义对齐度
   */
  calculateConceptMorphemeAlignment(
    concept: UniversalConcept,
    morpheme: LanguageSpecificMorpheme
  ): SemanticAlignment {
    // 1. 语义相似度计算
    const semanticSimilarity = this.calculateSemanticSimilarity(
      concept.semantic_vector,
      morpheme
    )

    // 2. 文化适配度计算
    const culturalFit = this.calculateCulturalFit(concept, morpheme)

    // 3. 质量一致性计算
    const qualityConsistency = this.calculateQualityConsistency(concept, morpheme)

    // 4. 综合对齐分数
    const alignmentScore = 
      this.config.semanticWeight * semanticSimilarity +
      this.config.culturalFitWeight * culturalFit +
      this.config.qualityWeight * qualityConsistency

    // 5. 计算置信度
    const confidence = this.calculateAlignmentConfidence(
      semanticSimilarity,
      culturalFit,
      qualityConsistency
    )

    return {
      sourceConceptId: concept.concept_id,
      targetLanguage: morpheme.language,
      alignedMorphemes: [morpheme],
      alignmentScore,
      semanticSimilarity,
      culturalFit,
      confidence
    }
  }

  /**
   * 计算语义相似度
   */
  private calculateSemanticSimilarity(
    conceptVector: UniversalSemanticVector,
    morpheme: LanguageSpecificMorpheme
  ): number {
    // 如果语素没有语义向量，使用基于类别的相似度
    if (!morpheme.phonetic_features) {
      return this.calculateCategoricalSimilarity(conceptVector, morpheme)
    }

    // 获取适配的概念向量
    const adaptedConceptVector = this.adaptConceptVector(conceptVector)
    const morphemeVector = this.extractMorphemeSemanticVector(morpheme)

    // 使用余弦相似度计算语义向量相似度
    return this.calculateCosineSimilarity(adaptedConceptVector, morphemeVector)
  }

  /**
   * 适配概念向量到兼容维度
   */
  private adaptConceptVector(conceptVector: UniversalSemanticVector): number[] {
    // 如果有兼容性向量，直接使用
    if (conceptVector.legacy_vector && conceptVector.legacy_vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY) {
      return conceptVector.legacy_vector
    }

    // 否则从高维向量降维到兼容维度
    const targetDim = SEMANTIC_VECTOR_DIMENSIONS.LEGACY
    const sourceDim = conceptVector.vector.length

    if (sourceDim === targetDim) {
      return conceptVector.vector
    }

    // 简单的降维策略：平均池化
    const adapted: number[] = new Array(targetDim).fill(0)
    const poolSize = Math.floor(sourceDim / targetDim)

    for (let i = 0; i < targetDim; i++) {
      let sum = 0
      const start = i * poolSize
      const end = Math.min(start + poolSize, sourceDim)

      for (let j = start; j < end; j++) {
        sum += conceptVector.vector[j]
      }

      adapted[i] = sum / (end - start)
    }

    return adapted
  }

  /**
   * 提取语素的语义向量
   */
  private extractMorphemeSemanticVector(morpheme: LanguageSpecificMorpheme): number[] {
    // 基于语素的各种特征构建语义向量
    const vectorDim = SEMANTIC_VECTOR_DIMENSIONS.LEGACY // 使用兼容性向量维度
    const vector: number[] = new Array(vectorDim).fill(0)
    
    // 基于质量评分填充向量
    const qualityScores = morpheme.language_quality_scores
    vector[0] = qualityScores.naturalness
    vector[1] = qualityScores.fluency
    vector[2] = qualityScores.authenticity
    vector[3] = qualityScores.aesthetic_appeal
    vector[4] = qualityScores.pronunciation_ease
    vector[5] = qualityScores.memorability
    vector[6] = qualityScores.uniqueness
    vector[7] = qualityScores.practicality
    
    // 基于文化语境填充向量
    const cultural = morpheme.cultural_context
    vector[8] = cultural.traditionality
    vector[9] = cultural.modernity
    vector[10] = cultural.formality
    vector[11] = cultural.regionality
    vector[12] = cultural.religious_sensitivity
    
    // 基于语音特征填充向量
    vector[13] = morpheme.phonetic_features.phonetic_harmony
    vector[14] = morpheme.phonetic_features.syllable_count / 5 // 归一化
    
    // 基于使用频率和评分填充向量
    vector[15] = morpheme.usage_frequency
    vector[16] = morpheme.native_speaker_rating
    vector[17] = morpheme.cultural_appropriateness
    vector[18] = morpheme.popularity_trend
    
    // 最后一个维度用于语言特异性
    vector[19] = this.getLanguageSpecificityScore(morpheme.language)
    
    return vector
  }

  /**
   * 获取语言特异性评分
   */
  private getLanguageSpecificityScore(language: LanguageCode): number {
    const scores: Record<LanguageCode, number> = {
      [LanguageCode.ZH_CN]: 0.9,
      [LanguageCode.EN_US]: 0.8,
      [LanguageCode.JA_JP]: 0.85,
      [LanguageCode.KO_KR]: 0.85,
      [LanguageCode.ES_ES]: 0.7,
      [LanguageCode.FR_FR]: 0.7,
      [LanguageCode.DE_DE]: 0.75,
      [LanguageCode.AR_SA]: 0.9
    }
    return scores[language] || 0.5
  }

  /**
   * 计算余弦相似度
   */
  private calculateCosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) {
      return 0
    }

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i]
      norm1 += vector1[i] * vector1[i]
      norm2 += vector2[i] * vector2[i]
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude > 0 ? dotProduct / magnitude : 0
  }

  /**
   * 基于类别的相似度计算
   */
  private calculateCategoricalSimilarity(
    conceptVector: UniversalSemanticVector,
    morpheme: LanguageSpecificMorpheme
  ): number {
    // 基于概念类别和语素的词性标注计算相似度
    const posMapping: Record<string, string[]> = {
      'emotions': ['ADJ', 'NOUN'],
      'professions': ['NOUN'],
      'characteristics': ['ADJ'],
      'objects': ['NOUN'],
      'actions': ['VERB'],
      'concepts': ['NOUN', 'ADJ']
    }

    // 从概念向量推断类别（简化实现）
    const inferredCategory = this.inferCategoryFromVector(conceptVector.vector)
    const expectedPOS = posMapping[inferredCategory] || ['NOUN']
    
    return expectedPOS.includes(morpheme.morphological_info.pos_tag) ? 0.8 : 0.4
  }

  /**
   * 从语义向量推断类别
   */
  private inferCategoryFromVector(vector: number[]): string {
    // 简化的类别推断逻辑
    const maxIndex = vector.indexOf(Math.max(...vector))
    const categories = ['emotions', 'professions', 'characteristics', 'objects', 'actions', 'concepts']
    return categories[maxIndex % categories.length] || 'concepts'
  }

  /**
   * 计算文化适配度
   */
  private calculateCulturalFit(
    concept: UniversalConcept,
    morpheme: LanguageSpecificMorpheme
  ): number {
    // 1. 文化中性度匹配
    const neutralityFit = 1 - Math.abs(concept.cultural_neutrality - 0.5) * 2

    // 2. 跨语言稳定性
    const stabilityBonus = concept.cross_lingual_stability * 0.3

    // 3. 语素的文化适宜性
    const appropriateness = morpheme.cultural_appropriateness

    // 4. 综合文化适配度
    return (neutralityFit * 0.4 + stabilityBonus + appropriateness * 0.3)
  }

  /**
   * 计算质量一致性
   */
  private calculateQualityConsistency(
    concept: UniversalConcept,
    morpheme: LanguageSpecificMorpheme
  ): number {
    // 基于概念的认知属性和语素的质量评分计算一致性
    const conceptQuality = concept.cognitive_attributes.memorability
    const morphemeQuality = morpheme.native_speaker_rating
    
    // 质量分数的一致性（1 - 差值的绝对值）
    return Math.max(0, 1 - Math.abs(conceptQuality - morphemeQuality))
  }

  /**
   * 计算对齐置信度
   */
  private calculateAlignmentConfidence(
    semanticSimilarity: number,
    culturalFit: number,
    qualityConsistency: number
  ): number {
    // 基于各项指标的方差计算置信度
    const scores = [semanticSimilarity, culturalFit, qualityConsistency]
    const mean = scores.reduce((a, b) => a + b, 0) / scores.length
    const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length
    
    // 方差越小，置信度越高
    return Math.max(0, 1 - variance)
  }

  /**
   * 为概念找到最佳语言对齐
   */
  findBestAlignment(
    concept: UniversalConcept,
    candidateMorphemes: LanguageSpecificMorpheme[]
  ): SemanticAlignment | null {
    if (candidateMorphemes.length === 0) {
      return null
    }

    let bestAlignment: SemanticAlignment | null = null
    let bestScore = 0

    for (const morpheme of candidateMorphemes) {
      const alignment = this.calculateConceptMorphemeAlignment(concept, morpheme)
      
      if (alignment.alignmentScore > bestScore && 
          alignment.semanticSimilarity >= this.config.semanticSimilarityThreshold) {
        bestScore = alignment.alignmentScore
        bestAlignment = alignment
      }
    }

    return bestAlignment
  }

  /**
   * 构建跨语言映射
   */
  buildCrossLingualMapping(
    concept: UniversalConcept,
    languageMorphemes: Map<LanguageCode, LanguageSpecificMorpheme[]>
  ): CrossLingualMapping {
    const alignments = new Map<LanguageCode, SemanticAlignment>()
    
    // 为每种语言找到最佳对齐
    for (const [language, morphemes] of languageMorphemes) {
      const bestAlignment = this.findBestAlignment(concept, morphemes)
      if (bestAlignment) {
        alignments.set(language, bestAlignment)
      }
    }

    // 计算跨语言一致性
    const consistencyScore = this.calculateCrossLingualConsistency(alignments)

    // 找到最佳对齐语言对
    const bestAlignments = this.findBestLanguagePairs(alignments)

    return {
      conceptId: concept.concept_id,
      languageMorphemes,
      consistencyScore,
      bestAlignments
    }
  }

  /**
   * 计算跨语言一致性
   */
  private calculateCrossLingualConsistency(
    alignments: Map<LanguageCode, SemanticAlignment>
  ): number {
    const scores = Array.from(alignments.values()).map(a => a.alignmentScore)
    if (scores.length < 2) return 1.0

    const mean = scores.reduce((a, b) => a + b, 0) / scores.length
    const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length
    
    // 一致性 = 平均分数 * (1 - 标准化方差)
    return mean * (1 - Math.min(variance, 1))
  }

  /**
   * 找到最佳语言对齐对
   */
  private findBestLanguagePairs(
    alignments: Map<LanguageCode, SemanticAlignment>
  ): Array<{ language1: LanguageCode; language2: LanguageCode; score: number }> {
    const languages = Array.from(alignments.keys())
    const pairs: Array<{ language1: LanguageCode; language2: LanguageCode; score: number }> = []

    for (let i = 0; i < languages.length; i++) {
      for (let j = i + 1; j < languages.length; j++) {
        const lang1 = languages[i]
        const lang2 = languages[j]
        const alignment1 = alignments.get(lang1)!
        const alignment2 = alignments.get(lang2)!
        
        // 计算语言对的对齐分数
        const pairScore = (alignment1.alignmentScore + alignment2.alignmentScore) / 2
        pairs.push({ language1: lang1, language2: lang2, score: pairScore })
      }
    }

    // 按分数降序排序
    return pairs.sort((a, b) => b.score - a.score)
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SemanticAlignerConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前配置
   */
  getConfig(): SemanticAlignerConfig {
    return { ...this.config }
  }
}
