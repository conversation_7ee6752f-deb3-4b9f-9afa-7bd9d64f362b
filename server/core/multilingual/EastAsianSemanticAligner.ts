/**
 * 东亚语言语义对齐器 (v3.0)
 *
 * 专门处理东亚语言（中文、日语、韩语）的语义对齐算法
 * 考虑汉字文化圈的共同特性和各语言的独特性
 *
 * @fileoverview 东亚语言语义对齐核心算法
 * @version 3.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */

import { LanguageCode } from '../../types/multilingual'
import type {
  LanguageSpecificMorpheme,
  SemanticVector,
  AlignmentScore
} from '../../types/multilingual'
import { EastAsianLanguageProcessor } from './EastAsianLanguageProcessor'

// ============================================================================
// 东亚语言语义对齐接口定义
// ============================================================================

/**
 * 汉字文化圈语义相似度配置
 */
export interface SinoSphereSemanticConfig {
  /** 汉字语义权重 */
  hanzi_semantic_weight: number
  /** 音韵相似度权重 */
  phonetic_similarity_weight: number
  /** 文化概念权重 */
  cultural_concept_weight: number
  /** 历史词源权重 */
  etymological_weight: number
}

/**
 * 东亚语言对齐结果
 */
export interface EastAsianAlignmentResult {
  /** 基础语义相似度 */
  semantic_similarity: number
  /** 汉字文化圈相似度 */
  sino_sphere_similarity: number
  /** 音韵相似度 */
  phonetic_similarity: number
  /** 文化适应度 */
  cultural_adaptation: number
  /** 综合对齐分数 */
  overall_alignment: number
  /** 对齐置信度 */
  confidence: number
  /** 详细分析 */
  analysis: {
    shared_concepts: string[]
    phonetic_patterns: string[]
    cultural_markers: string[]
    alignment_factors: string[]
  }
}

// ============================================================================
// 东亚语言语义对齐器主类
// ============================================================================

/**
 * 东亚语言语义对齐器
 * 
 * 处理中日韩语言间的语义对齐，考虑汉字文化圈特性
 */
export class EastAsianSemanticAligner {
  private readonly processor: EastAsianLanguageProcessor
  private readonly sinoSphereConfig: SinoSphereSemanticConfig

  constructor() {
    this.processor = new EastAsianLanguageProcessor()
    this.sinoSphereConfig = {
      hanzi_semantic_weight: 0.35,
      phonetic_similarity_weight: 0.25,
      cultural_concept_weight: 0.25,
      etymological_weight: 0.15
    }
  }

  /**
   * 计算东亚语言间的语义对齐
   */
  async calculateEastAsianAlignment(
    sourceMorpheme: LanguageSpecificMorpheme,
    targetMorpheme: LanguageSpecificMorpheme
  ): Promise<EastAsianAlignmentResult> {
    // 基础语义相似度
    const semanticSimilarity = await this.calculateSemanticSimilarity(
      sourceMorpheme, 
      targetMorpheme
    )

    // 汉字文化圈相似度
    const sinoSphereSimilarity = this.calculateSinoSphereSimilarity(
      sourceMorpheme, 
      targetMorpheme
    )

    // 音韵相似度
    const phoneticSimilarity = this.calculatePhoneticSimilarity(
      sourceMorpheme, 
      targetMorpheme
    )

    // 文化适应度
    const culturalAdaptation = this.calculateCulturalAdaptation(
      sourceMorpheme, 
      targetMorpheme
    )

    // 综合对齐分数
    const overallAlignment = this.calculateOverallAlignment({
      semantic_similarity: semanticSimilarity,
      sino_sphere_similarity: sinoSphereSimilarity,
      phonetic_similarity: phoneticSimilarity,
      cultural_adaptation: culturalAdaptation
    })

    // 置信度评估
    const confidence = this.calculateAlignmentConfidence(
      sourceMorpheme,
      targetMorpheme,
      overallAlignment
    )

    // 详细分析
    const analysis = this.generateAlignmentAnalysis(
      sourceMorpheme,
      targetMorpheme
    )

    return {
      semantic_similarity: semanticSimilarity,
      sino_sphere_similarity: sinoSphereSimilarity,
      phonetic_similarity: phoneticSimilarity,
      cultural_adaptation: culturalAdaptation,
      overall_alignment: overallAlignment,
      confidence,
      analysis
    }
  }

  /**
   * 计算基础语义相似度
   */
  private async calculateSemanticSimilarity(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): Promise<number> {
    // 如果有相同的概念ID，直接返回高相似度
    if (source.concept_id === target.concept_id) {
      return 0.95
    }

    // 基于语义向量计算相似度（简化实现）
    if (source.semantic_vector && target.semantic_vector) {
      return this.calculateVectorSimilarity(
        source.semantic_vector,
        target.semantic_vector
      )
    }

    // 基于文本特征的相似度
    return this.calculateTextualSimilarity(source.text, target.text)
  }

  /**
   * 计算汉字文化圈相似度
   */
  private calculateSinoSphereSimilarity(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): number {
    let similarity = 0.0
    let factors = 0

    // 汉字语义相似度
    const hanziSimilarity = this.calculateHanziSimilarity(source, target)
    if (hanziSimilarity > 0) {
      similarity += hanziSimilarity * this.sinoSphereConfig.hanzi_semantic_weight
      factors++
    }

    // 词源相似度
    const etymologicalSimilarity = this.calculateEtymologicalSimilarity(source, target)
    if (etymologicalSimilarity > 0) {
      similarity += etymologicalSimilarity * this.sinoSphereConfig.etymological_weight
      factors++
    }

    // 文化概念相似度
    const culturalSimilarity = this.calculateCulturalConceptSimilarity(source, target)
    if (culturalSimilarity > 0) {
      similarity += culturalSimilarity * this.sinoSphereConfig.cultural_concept_weight
      factors++
    }

    return factors > 0 ? similarity / factors : 0.0
  }

  /**
   * 计算音韵相似度
   */
  private calculatePhoneticSimilarity(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): number {
    if (!source.phonetic_features || !target.phonetic_features) {
      return 0.0
    }

    let similarity = 0.0
    let factors = 0

    // IPA转录相似度
    if (source.phonetic_features.ipa_transcription && 
        target.phonetic_features.ipa_transcription) {
      similarity += this.calculateIPASimilarity(
        source.phonetic_features.ipa_transcription,
        target.phonetic_features.ipa_transcription
      )
      factors++
    }

    // 音节/音拍数相似度
    const syllableSource = source.phonetic_features.syllable_count || 
                          source.phonetic_features.mora_count || 0
    const syllableTarget = target.phonetic_features.syllable_count || 
                          target.phonetic_features.mora_count || 0
    
    if (syllableSource > 0 && syllableTarget > 0) {
      const syllableSimilarity = 1 - Math.abs(syllableSource - syllableTarget) / 
                                Math.max(syllableSource, syllableTarget)
      similarity += syllableSimilarity
      factors++
    }

    // 语音和谐度相似度
    if (source.phonetic_features.phonetic_harmony && 
        target.phonetic_features.phonetic_harmony) {
      const harmonySimilarity = 1 - Math.abs(
        source.phonetic_features.phonetic_harmony - 
        target.phonetic_features.phonetic_harmony
      )
      similarity += harmonySimilarity
      factors++
    }

    return factors > 0 ? similarity / factors : 0.0
  }

  /**
   * 计算文化适应度
   */
  private calculateCulturalAdaptation(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): number {
    if (!source.cultural_context || !target.cultural_context) {
      return 0.0
    }

    const sourceCtx = source.cultural_context
    const targetCtx = target.cultural_context

    // 计算各维度的相似度
    const traditionalitySim = 1 - Math.abs(sourceCtx.traditionality - targetCtx.traditionality)
    const modernitySim = 1 - Math.abs(sourceCtx.modernity - targetCtx.modernity)
    const formalitySim = 1 - Math.abs(sourceCtx.formality - targetCtx.formality)
    const regionalitySim = 1 - Math.abs(sourceCtx.regionality - targetCtx.regionality)

    // 加权平均
    return (traditionalitySim * 0.3 + 
            modernitySim * 0.25 + 
            formalitySim * 0.25 + 
            regionalitySim * 0.2)
  }

  /**
   * 计算综合对齐分数
   */
  private calculateOverallAlignment(scores: {
    semantic_similarity: number
    sino_sphere_similarity: number
    phonetic_similarity: number
    cultural_adaptation: number
  }): number {
    // 动态权重：根据语言对调整权重
    const weights = {
      semantic: 0.40,
      sino_sphere: 0.25,
      phonetic: 0.20,
      cultural: 0.15
    }

    return (
      scores.semantic_similarity * weights.semantic +
      scores.sino_sphere_similarity * weights.sino_sphere +
      scores.phonetic_similarity * weights.phonetic +
      scores.cultural_adaptation * weights.cultural
    )
  }

  /**
   * 计算对齐置信度
   */
  private calculateAlignmentConfidence(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme,
    alignment: number
  ): number {
    let confidence = alignment

    // 数据完整性加成
    const sourceCompleteness = this.calculateDataCompleteness(source)
    const targetCompleteness = this.calculateDataCompleteness(target)
    const dataQuality = (sourceCompleteness + targetCompleteness) / 2

    confidence *= (0.7 + 0.3 * dataQuality)

    // 语言对特性加成
    if (this.isHighConfidenceLanguagePair(source.language, target.language)) {
      confidence *= 1.1
    }

    return Math.min(1.0, confidence)
  }

  /**
   * 生成对齐分析
   */
  private generateAlignmentAnalysis(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): EastAsianAlignmentResult['analysis'] {
    const sharedConcepts: string[] = []
    const phoneticPatterns: string[] = []
    const culturalMarkers: string[] = []
    const alignmentFactors: string[] = []

    // 分析共同概念
    if (source.concept_id === target.concept_id) {
      sharedConcepts.push(`共同概念: ${source.concept_id}`)
      alignmentFactors.push('概念一致性')
    }

    // 分析音韵模式
    if (source.phonetic_features && target.phonetic_features) {
      if (source.phonetic_features.syllable_count === target.phonetic_features.syllable_count) {
        phoneticPatterns.push('音节数匹配')
        alignmentFactors.push('音韵结构相似')
      }
    }

    // 分析文化标记
    if (source.cultural_context && target.cultural_context) {
      const traditionalityDiff = Math.abs(
        source.cultural_context.traditionality - target.cultural_context.traditionality
      )
      if (traditionalityDiff < 0.2) {
        culturalMarkers.push('传统性相近')
        alignmentFactors.push('文化适应性')
      }
    }

    return {
      shared_concepts: sharedConcepts,
      phonetic_patterns: phoneticPatterns,
      cultural_markers: culturalMarkers,
      alignment_factors: alignmentFactors
    }
  }

  // ============================================================================
  // 辅助方法
  // ============================================================================

  /**
   * 计算向量相似度
   */
  private calculateVectorSimilarity(vec1: SemanticVector, vec2: SemanticVector): number {
    // 简化实现：余弦相似度
    if (vec1.dimensions.length !== vec2.dimensions.length) return 0.0

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < vec1.dimensions.length; i++) {
      dotProduct += vec1.dimensions[i] * vec2.dimensions[i]
      norm1 += vec1.dimensions[i] * vec1.dimensions[i]
      norm2 += vec2.dimensions[i] * vec2.dimensions[i]
    }

    if (norm1 === 0 || norm2 === 0) return 0.0
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2))
  }

  /**
   * 计算文本相似度
   */
  private calculateTextualSimilarity(text1: string, text2: string): number {
    // 简化实现：基于编辑距离
    const maxLen = Math.max(text1.length, text2.length)
    if (maxLen === 0) return 1.0

    const editDistance = this.calculateEditDistance(text1, text2)
    return 1 - editDistance / maxLen
  }

  /**
   * 计算汉字相似度
   */
  private calculateHanziSimilarity(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): number {
    // 简化实现：检查是否有共同汉字
    const sourceHanzi = this.extractHanzi(source)
    const targetHanzi = this.extractHanzi(target)

    if (!sourceHanzi || !targetHanzi) return 0.0

    const commonChars = sourceHanzi.split('').filter(char => 
      targetHanzi.includes(char)
    ).length

    const totalChars = Math.max(sourceHanzi.length, targetHanzi.length)
    return totalChars > 0 ? commonChars / totalChars : 0.0
  }

  /**
   * 提取汉字
   */
  private extractHanzi(morpheme: LanguageSpecificMorpheme): string | null {
    // 检查alternative_forms中的汉字
    const hanziPattern = /^[\u4E00-\u9FAF]+$/
    return morpheme.alternative_forms?.find(form => hanziPattern.test(form)) || null
  }

  /**
   * 计算词源相似度
   */
  private calculateEtymologicalSimilarity(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): number {
    // 简化实现：基于汉字词源
    const sourceHanzi = this.extractHanzi(source)
    const targetHanzi = this.extractHanzi(target)

    if (sourceHanzi && targetHanzi && sourceHanzi === targetHanzi) {
      return 0.9 // 相同汉字词源
    }

    return 0.0
  }

  /**
   * 计算文化概念相似度
   */
  private calculateCulturalConceptSimilarity(
    source: LanguageSpecificMorpheme,
    target: LanguageSpecificMorpheme
  ): number {
    // 简化实现：基于文化标签匹配
    if (!source.cultural_context || !target.cultural_context) return 0.0

    const sourceSig = source.cultural_context.cultural_significance
    const targetSig = target.cultural_context.cultural_significance

    if (sourceSig && targetSig && sourceSig === targetSig) {
      return 0.8
    }

    return 0.0
  }

  /**
   * 计算IPA相似度
   */
  private calculateIPASimilarity(ipa1: string, ipa2: string): number {
    // 简化实现：基于字符相似度
    return this.calculateTextualSimilarity(ipa1, ipa2)
  }

  /**
   * 计算数据完整性
   */
  private calculateDataCompleteness(morpheme: LanguageSpecificMorpheme): number {
    let score = 0
    let total = 0

    // 检查各字段完整性
    if (morpheme.phonetic_features) score++; total++
    if (morpheme.morphological_info) score++; total++
    if (morpheme.cultural_context) score++; total++
    if (morpheme.alternative_forms?.length) score++; total++

    return total > 0 ? score / total : 0
  }

  /**
   * 检查是否为高置信度语言对
   */
  private isHighConfidenceLanguagePair(lang1: LanguageCode, lang2: LanguageCode): boolean {
    // 中日韩语言对通常有较高的对齐置信度
    const eastAsianLangs = [LanguageCode.ZH_CN, LanguageCode.JA_JP, LanguageCode.KO_KR]
    return eastAsianLangs.includes(lang1) && eastAsianLangs.includes(lang2)
  }

  /**
   * 计算编辑距离
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }

    return matrix[str2.length][str1.length]
  }
}
