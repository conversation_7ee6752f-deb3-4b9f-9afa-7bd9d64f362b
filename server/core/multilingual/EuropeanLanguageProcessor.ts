/**
 * 欧洲语言处理器
 * 
 * 专门处理欧洲语言（西班牙语、法语、德语）的语言特性
 * 包括语音特征、形态学分析、文化语境处理
 * 
 * @fileoverview 欧洲语言处理器实现
 * @version 3.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */

import { LanguageCode } from '../../types/multilingual'
import type {
  LanguageSpecificMorpheme,
  MultiDimensionalCulturalContext,
  PhoneticFeatures,
  MorphologicalInfo
} from '../../types/multilingual'

/**
 * 欧洲语言音韵信息接口
 */
export interface EuropeanPhoneticInfo {
  /** 国际音标转写 */
  ipa_transcription: string
  /** 音节数 */
  syllable_count: number
  /** 重音位置 (1-based) */
  stress_position: number
  /** 是否有鼻音 */
  has_nasal_sounds: boolean
  /** 是否有颤音 */
  has_trill_sounds: boolean
  /** 元音系统复杂度 [1-5] */
  vowel_complexity: number
}

/**
 * 欧洲语言形态学信息接口
 */
export interface EuropeanMorphologicalInfo {
  /** 词根 */
  root: string
  /** 前缀列表 */
  prefixes: string[]
  /** 后缀列表 */
  suffixes: string[]
  /** 词性变化信息 */
  inflection_info?: {
    gender?: 'masculine' | 'feminine' | 'neuter'
    number?: 'singular' | 'plural'
    case?: string
    tense?: string
    mood?: string
  }
  /** 派生词信息 */
  derivation_info?: {
    base_word: string
    derivation_type: string
    productivity: number
  }
}

/**
 * 欧洲语言特性配置
 */
export interface EuropeanLanguageConfig {
  /** 语言代码 */
  language: LanguageCode
  /** 语言家族 */
  language_family: 'Romance' | 'Germanic'
  /** 文字系统 */
  writing_system: 'Latin'
  /** 是否有语法性别 */
  has_grammatical_gender: boolean
  /** 是否有格变 */
  has_case_system: boolean
  /** 动词变位复杂度 [1-5] */
  verb_conjugation_complexity: number
  /** 文化特性权重 */
  cultural_weights: {
    formality: number
    regionality: number
    traditionality: number
    elegance: number
  }
  /** 语音特征权重 */
  phonetic_weights: {
    rhythm: number
    melody: number
    clarity: number
    euphony: number
  }
}

/**
 * 欧洲语言处理器类
 */
export class EuropeanLanguageProcessor {
  private configs: Map<LanguageCode, EuropeanLanguageConfig>

  constructor() {
    this.configs = new Map()
    this.initializeConfigs()
  }

  /**
   * 检查是否为欧洲语言
   */
  isEuropeanLanguage(language: LanguageCode): boolean {
    return this.configs.has(language)
  }

  /**
   * 检查是否支持指定语言
   *
   * @param language 语言代码
   * @returns 是否支持该语言
   */
  supportsLanguage(language: LanguageCode): boolean {
    return this.configs.has(language)
  }

  /**
   * 处理语言特定特征
   *
   * @param morpheme 语素数据
   * @param language 语言代码
   * @returns 处理结果
   */
  processLanguageSpecificFeatures(morpheme: any, language: LanguageCode): {
    phonetic_info: any
    morphological_info: any
    cultural_adjustments: any
  } {
    if (!this.supportsLanguage(language)) {
      throw new Error(`Unsupported European language: ${language}`)
    }

    switch (language) {
      case LanguageCode.ES_ES:
        return this.processSpanishMorpheme(morpheme)
      case LanguageCode.FR_FR:
        return this.processFrenchMorpheme(morpheme)
      case LanguageCode.DE_DE:
        return this.processGermanMorpheme(morpheme)
      default:
        throw new Error(`Unsupported European language: ${language}`)
    }
  }

  /**
   * 获取语言配置
   */
  getLanguageConfig(language: LanguageCode): EuropeanLanguageConfig | null {
    return this.configs.get(language) || null
  }

  /**
   * 处理西班牙语语素
   */
  processSpanishMorpheme(morpheme: LanguageSpecificMorpheme): LanguageSpecificMorpheme {
    const result = {
      phonetic_info: null as any,
      morphological_info: null as any,
      cultural_adjustments: null as any
    }

    // 处理西班牙语音韵特征
    if (morpheme.text) {
      const phoneticInfo = this.extractSpanishPhoneticInfo(morpheme.text)

      if (phoneticInfo) {
        result.phonetic_info = {
          ipa_transcription: phoneticInfo.ipa_transcription,
          syllable_count: phoneticInfo.syllable_count,
          stress_position: phoneticInfo.stress_position,
          has_trill_sounds: phoneticInfo.has_trill_sounds
        }
      }

      // 处理形态学信息
      const morphInfo = this.extractSpanishMorphology(morpheme.text)
      if (morphInfo) {
        result.morphological_info = {
          root: morphInfo.root,
          prefixes: morphInfo.prefixes,
          suffixes: morphInfo.suffixes,
          inflection_info: morphInfo.inflection_info
        }
      }
    }

    // 处理西班牙语文化语境
    result.cultural_adjustments = this.processSpanishCulturalContext(
      morpheme.cultural_context || {},
      morpheme.text
    )

    return result
  }

  /**
   * 处理法语语素
   */
  processFrenchMorpheme(morpheme: LanguageSpecificMorpheme): LanguageSpecificMorpheme {
    const result = {
      phonetic_info: null as any,
      morphological_info: null as any,
      cultural_adjustments: null as any
    }

    // 处理法语音韵特征
    if (morpheme.text) {
      const phoneticInfo = this.extractFrenchPhoneticInfo(morpheme.text)

      if (phoneticInfo) {
        result.phonetic_info = {
          ipa_transcription: phoneticInfo.ipa_transcription,
          syllable_count: phoneticInfo.syllable_count,
          stress_position: phoneticInfo.stress_position,
          has_nasal_sounds: phoneticInfo.has_nasal_sounds,
          vowel_complexity: phoneticInfo.vowel_complexity
        }
      }

      // 处理形态学信息
      const morphInfo = this.extractFrenchMorphology(morpheme.text)
      if (morphInfo) {
        result.morphological_info = {
          root: morphInfo.root,
          prefixes: morphInfo.prefixes,
          suffixes: morphInfo.suffixes,
          inflection_info: morphInfo.inflection_info
        }
      }
    }

    // 处理法语文化语境
    result.cultural_adjustments = this.processFrenchCulturalContext(
      morpheme.cultural_context || {},
      morpheme.text
    )

    return result
  }

  /**
   * 处理德语语素
   */
  processGermanMorpheme(morpheme: LanguageSpecificMorpheme): LanguageSpecificMorpheme {
    const result = {
      phonetic_info: null as any,
      morphological_info: null as any,
      cultural_adjustments: null as any
    }

    // 处理德语音韵特征
    if (morpheme.text) {
      const phoneticInfo = this.extractGermanPhoneticInfo(morpheme.text)

      if (phoneticInfo) {
        result.phonetic_info = {
          ipa_transcription: phoneticInfo.ipa_transcription,
          syllable_count: phoneticInfo.syllable_count,
          stress_position: phoneticInfo.stress_position
        }
      }

      // 处理形态学信息（德语复合词特性）
      const morphInfo = this.extractGermanMorphology(morpheme.text)
      if (morphInfo) {
        result.morphological_info = {
          root: morphInfo.root,
          prefixes: morphInfo.prefixes,
          suffixes: morphInfo.suffixes,
          inflection_info: morphInfo.inflection_info,
          compound_structure: this.analyzeGermanCompound(morpheme.text)
        }
      }
    }

    // 处理德语文化语境
    result.cultural_adjustments = this.processGermanCulturalContext(
      morpheme.cultural_context || {},
      morpheme.text
    )

    return result
  }

  /**
   * 初始化欧洲语言配置
   */
  private initializeConfigs(): void {
    // 西班牙语配置
    this.configs.set(LanguageCode.ES_ES, {
      language: LanguageCode.ES_ES,
      language_family: 'Romance',
      writing_system: 'Latin',
      has_grammatical_gender: true,
      has_case_system: false,
      verb_conjugation_complexity: 4,
      cultural_weights: {
        formality: 0.75,
        regionality: 0.80,
        traditionality: 0.85,
        elegance: 0.80
      },
      phonetic_weights: {
        rhythm: 0.90,
        melody: 0.85,
        clarity: 0.95,
        euphony: 0.80
      }
    })

    // 法语配置
    this.configs.set(LanguageCode.FR_FR, {
      language: LanguageCode.FR_FR,
      language_family: 'Romance',
      writing_system: 'Latin',
      has_grammatical_gender: true,
      has_case_system: false,
      verb_conjugation_complexity: 5,
      cultural_weights: {
        formality: 0.85,
        regionality: 0.70,
        traditionality: 0.90,
        elegance: 0.95
      },
      phonetic_weights: {
        rhythm: 0.80,
        melody: 0.95,
        clarity: 0.85,
        euphony: 0.90
      }
    })

    // 德语配置
    this.configs.set(LanguageCode.DE_DE, {
      language: LanguageCode.DE_DE,
      language_family: 'Germanic',
      writing_system: 'Latin',
      has_grammatical_gender: true,
      has_case_system: true,
      verb_conjugation_complexity: 3,
      cultural_weights: {
        formality: 0.90,
        regionality: 0.75,
        traditionality: 0.85,
        elegance: 0.75
      },
      phonetic_weights: {
        rhythm: 0.85,
        melody: 0.70,
        clarity: 0.90,
        euphony: 0.75
      }
    })
  }

  /**
   * 提取西班牙语音韵信息
   */
  private extractSpanishPhoneticInfo(text: string): EuropeanPhoneticInfo | null {
    if (!text) return null

    return {
      ipa_transcription: this.convertToIPA(text, 'es'),
      syllable_count: this.countSpanishSyllables(text),
      stress_position: this.findSpanishStress(text),
      has_nasal_sounds: /[ñn]/.test(text),
      has_trill_sounds: /[rR]/.test(text),
      vowel_complexity: this.calculateVowelComplexity(text, 'es')
    }
  }

  /**
   * 提取法语音韵信息
   */
  private extractFrenchPhoneticInfo(text: string): EuropeanPhoneticInfo | null {
    if (!text) return null

    return {
      ipa_transcription: this.convertToIPA(text, 'fr'),
      syllable_count: this.countFrenchSyllables(text),
      stress_position: this.findFrenchStress(text),
      has_nasal_sounds: /[ãẽĩõũ]|an|en|in|on|un/.test(text),
      has_trill_sounds: false, // 法语通常没有颤音
      vowel_complexity: this.calculateVowelComplexity(text, 'fr')
    }
  }

  /**
   * 提取德语音韵信息
   */
  private extractGermanPhoneticInfo(text: string): EuropeanPhoneticInfo | null {
    if (!text) return null

    return {
      ipa_transcription: this.convertToIPA(text, 'de'),
      syllable_count: this.countGermanSyllables(text),
      stress_position: this.findGermanStress(text),
      has_nasal_sounds: /[ŋ]|ng/.test(text),
      has_trill_sounds: /[r]/.test(text),
      vowel_complexity: this.calculateVowelComplexity(text, 'de')
    }
  }

  /**
   * 提取西班牙语形态学信息
   */
  private extractSpanishMorphology(text: string): EuropeanMorphologicalInfo | null {
    if (!text) return null

    const prefixes = this.extractPrefixes(text, 'es')
    const suffixes = this.extractSuffixes(text, 'es')
    const root = this.extractRoot(text, prefixes, suffixes)

    return {
      root,
      prefixes,
      suffixes,
      inflection_info: {
        gender: this.detectSpanishGender(text),
        number: this.detectSpanishNumber(text)
      }
    }
  }

  /**
   * 提取法语形态学信息
   */
  private extractFrenchMorphology(text: string): EuropeanMorphologicalInfo | null {
    if (!text) return null

    const prefixes = this.extractPrefixes(text, 'fr')
    const suffixes = this.extractSuffixes(text, 'fr')
    const root = this.extractRoot(text, prefixes, suffixes)

    return {
      root,
      prefixes,
      suffixes,
      inflection_info: {
        gender: this.detectFrenchGender(text),
        number: this.detectFrenchNumber(text)
      }
    }
  }

  /**
   * 提取德语形态学信息
   */
  private extractGermanMorphology(text: string): EuropeanMorphologicalInfo | null {
    if (!text) return null

    const prefixes = this.extractPrefixes(text, 'de')
    const suffixes = this.extractSuffixes(text, 'de')
    const root = this.extractRoot(text, prefixes, suffixes)

    return {
      root,
      prefixes,
      suffixes,
      inflection_info: {
        gender: this.detectGermanGender(text),
        number: this.detectGermanNumber(text),
        case: this.detectGermanCase(text)
      }
    }
  }

  /**
   * 处理西班牙语文化语境
   */
  private processSpanishCulturalContext(
    context: MultiDimensionalCulturalContext,
    text: string
  ): MultiDimensionalCulturalContext {
    const config = this.configs.get(LanguageCode.ES_ES)!

    return {
      ...context,
      formality: this.adjustFormality(context.formality, text, config),
      regionality: this.adjustRegionality(context.regionality, text, 'es'),
      traditionality: this.adjustTraditionality(context.traditionality, text, 'es'),
      aesthetic_value: this.calculateAestheticValue(text, config)
    }
  }

  /**
   * 处理法语文化语境
   */
  private processFrenchCulturalContext(
    context: MultiDimensionalCulturalContext,
    text: string
  ): MultiDimensionalCulturalContext {
    const config = this.configs.get(LanguageCode.FR_FR)!

    return {
      ...context,
      formality: this.adjustFormality(context.formality, text, config),
      regionality: this.adjustRegionality(context.regionality, text, 'fr'),
      traditionality: this.adjustTraditionality(context.traditionality, text, 'fr'),
      aesthetic_value: this.calculateAestheticValue(text, config),
      elegance: this.calculateElegance(text, config)
    }
  }

  /**
   * 处理德语文化语境
   */
  private processGermanCulturalContext(
    context: MultiDimensionalCulturalContext,
    text: string
  ): MultiDimensionalCulturalContext {
    const config = this.configs.get(LanguageCode.DE_DE)!

    return {
      ...context,
      formality: this.adjustFormality(context.formality, text, config),
      regionality: this.adjustRegionality(context.regionality, text, 'de'),
      traditionality: this.adjustTraditionality(context.traditionality, text, 'de'),
      aesthetic_value: this.calculateAestheticValue(text, config),
      precision: this.calculatePrecision(text, config)
    }
  }

  // ============================================================================
  // 辅助方法实现
  // ============================================================================

  /**
   * 转换为国际音标（简化实现）
   */
  private convertToIPA(text: string, language: string): string {
    // 简化实现：返回基础转换
    // 实际实现需要完整的音韵转换规则
    const ipaMap: Record<string, Record<string, string>> = {
      'es': {
        'ñ': 'ɲ',
        'rr': 'r',
        'j': 'x',
        'll': 'ʎ'
      },
      'fr': {
        'j': 'ʒ',
        'ch': 'ʃ',
        'gn': 'ɲ',
        'r': 'ʁ'
      },
      'de': {
        'ch': 'x',
        'sch': 'ʃ',
        'ß': 's',
        'ü': 'y'
      }
    }

    let result = text.toLowerCase()
    const langMap = ipaMap[language] || {}

    for (const [from, to] of Object.entries(langMap)) {
      result = result.replace(new RegExp(from, 'g'), to)
    }

    return `[${result}]`
  }

  /**
   * 计算音节数
   */
  private countSpanishSyllables(text: string): number {
    // 简化实现：基于元音计数
    const vowels = text.match(/[aeiouáéíóú]/gi)
    return vowels ? vowels.length : 1
  }

  private countFrenchSyllables(text: string): number {
    // 简化实现：基于元音计数，考虑法语特殊规则
    const vowels = text.match(/[aeiouyàâäéèêëïîôöùûü]/gi)
    return vowels ? Math.max(vowels.length, 1) : 1
  }

  private countGermanSyllables(text: string): number {
    // 简化实现：基于元音计数
    const vowels = text.match(/[aeiouäöü]/gi)
    return vowels ? vowels.length : 1
  }

  /**
   * 查找重音位置
   */
  private findSpanishStress(text: string): number {
    // 西班牙语重音规则简化实现
    if (/[áéíóú]/.test(text)) {
      const match = text.match(/[áéíóú]/)
      return match ? text.indexOf(match[0]) + 1 : 1
    }

    // 默认重音规则
    const syllables = this.countSpanishSyllables(text)
    return /[ns]$/.test(text) ? Math.max(syllables - 1, 1) : syllables
  }

  private findFrenchStress(text: string): number {
    // 法语通常最后音节重音
    return this.countFrenchSyllables(text)
  }

  private findGermanStress(text: string): number {
    // 德语通常第一音节重音
    return 1
  }

  /**
   * 计算元音复杂度
   */
  private calculateVowelComplexity(text: string, language: string): number {
    const complexityMap: Record<string, number> = {
      'es': 2, // 西班牙语元音系统相对简单
      'fr': 4, // 法语元音系统复杂
      'de': 3  // 德语元音系统中等复杂
    }

    const baseComplexity = complexityMap[language] || 2
    const uniqueVowels = new Set(text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || [])

    return Math.min(baseComplexity + uniqueVowels.size * 0.2, 5)
  }

  /**
   * 提取词缀
   */
  private extractPrefixes(text: string, language: string): string[] {
    const prefixPatterns: Record<string, string[]> = {
      'es': ['anti', 'auto', 'contra', 'des', 'ex', 'inter', 'pre', 'pro', 're', 'sub', 'super'],
      'fr': ['anti', 'auto', 'contre', 'dé', 'ex', 'inter', 'pré', 'pro', 're', 'sous', 'sur'],
      'de': ['anti', 'auto', 'gegen', 'ent', 'ex', 'inter', 'vor', 'pro', 'wieder', 'unter', 'über']
    }

    const patterns = prefixPatterns[language] || []
    const prefixes: string[] = []

    for (const prefix of patterns) {
      if (text.toLowerCase().startsWith(prefix)) {
        prefixes.push(prefix)
        break // 简化：只取第一个匹配的前缀
      }
    }

    return prefixes
  }

  private extractSuffixes(text: string, language: string): string[] {
    const suffixPatterns: Record<string, string[]> = {
      'es': ['ción', 'sión', 'mente', 'idad', 'ismo', 'ista', 'able', 'ible'],
      'fr': ['tion', 'sion', 'ment', 'ité', 'isme', 'iste', 'able', 'ible'],
      'de': ['ung', 'heit', 'keit', 'schaft', 'ismus', 'ist', 'bar', 'lich']
    }

    const patterns = suffixPatterns[language] || []
    const suffixes: string[] = []

    for (const suffix of patterns) {
      if (text.toLowerCase().endsWith(suffix)) {
        suffixes.push(suffix)
        break // 简化：只取第一个匹配的后缀
      }
    }

    return suffixes
  }

  /**
   * 提取词根
   */
  private extractRoot(text: string, prefixes: string[], suffixes: string[]): string {
    let root = text.toLowerCase()

    // 移除前缀
    for (const prefix of prefixes) {
      if (root.startsWith(prefix)) {
        root = root.substring(prefix.length)
        break
      }
    }

    // 移除后缀
    for (const suffix of suffixes) {
      if (root.endsWith(suffix)) {
        root = root.substring(0, root.length - suffix.length)
        break
      }
    }

    return root || text
  }

  /**
   * 检测语法性别
   */
  private detectSpanishGender(text: string): 'masculine' | 'feminine' | undefined {
    const lowerText = text.toLowerCase()
    if (lowerText.endsWith('a') || lowerText.endsWith('ción') || lowerText.endsWith('sión')) {
      return 'feminine'
    }
    if (lowerText.endsWith('o') || lowerText.endsWith('or')) {
      return 'masculine'
    }
    return undefined
  }

  private detectFrenchGender(text: string): 'masculine' | 'feminine' | undefined {
    const lowerText = text.toLowerCase()
    if (lowerText.endsWith('e') || lowerText.endsWith('tion') || lowerText.endsWith('sion')) {
      return 'feminine'
    }
    if (lowerText.endsWith('eau') || lowerText.endsWith('age')) {
      return 'masculine'
    }
    return undefined
  }

  private detectGermanGender(text: string): 'masculine' | 'feminine' | 'neuter' | undefined {
    const lowerText = text.toLowerCase()
    if (lowerText.endsWith('ung') || lowerText.endsWith('heit') || lowerText.endsWith('keit')) {
      return 'feminine'
    }
    if (lowerText.endsWith('er') || lowerText.endsWith('ismus')) {
      return 'masculine'
    }
    if (lowerText.endsWith('chen') || lowerText.endsWith('lein')) {
      return 'neuter'
    }
    return undefined
  }

  /**
   * 检测数量
   */
  private detectSpanishNumber(text: string): 'singular' | 'plural' | undefined {
    return text.endsWith('s') || text.endsWith('es') ? 'plural' : 'singular'
  }

  private detectFrenchNumber(text: string): 'singular' | 'plural' | undefined {
    return text.endsWith('s') || text.endsWith('x') ? 'plural' : 'singular'
  }

  private detectGermanNumber(text: string): 'singular' | 'plural' | undefined {
    if (text.endsWith('e') || text.endsWith('en') || text.endsWith('er') || text.endsWith('s')) {
      return 'plural'
    }
    return 'singular'
  }

  /**
   * 检测德语格变
   */
  private detectGermanCase(text: string): string | undefined {
    // 简化实现：基于词尾判断
    const lowerText = text.toLowerCase()
    if (lowerText.endsWith('s') || lowerText.endsWith('es')) {
      return 'genitive'
    }
    if (lowerText.endsWith('e') || lowerText.endsWith('en')) {
      return 'dative'
    }
    return 'nominative'
  }

  /**
   * 分析德语复合词结构
   */
  private analyzeGermanCompound(text: string): string[] {
    // 简化实现：基于常见复合词模式
    const compounds: string[] = []

    // 检测常见复合词连接元素
    const connectors = ['s', 'es', 'en', 'er', 'n']

    for (const connector of connectors) {
      const parts = text.split(connector)
      if (parts.length > 1 && parts.every(part => part.length > 2)) {
        compounds.push(...parts.filter(part => part.length > 0))
        break
      }
    }

    return compounds.length > 1 ? compounds : [text]
  }

  /**
   * 调整正式度
   */
  private adjustFormality(
    baseFormality: number,
    text: string,
    config: EuropeanLanguageConfig
  ): number {
    const formalityWeight = config.cultural_weights.formality

    // 基于词长和复杂度调整正式度
    const lengthFactor = Math.min(text.length / 10, 1)
    const complexityFactor = (config.verb_conjugation_complexity - 1) / 4

    return Math.min(
      baseFormality + (lengthFactor * complexityFactor * formalityWeight * 0.2),
      1.0
    )
  }

  /**
   * 调整地域性
   */
  private adjustRegionality(baseRegionality: number, text: string, language: string): number {
    // 简化实现：基于特定语言标记
    const regionalMarkers: Record<string, string[]> = {
      'es': ['ñ', 'll', 'rr'],
      'fr': ['ç', 'œ', 'à', 'é'],
      'de': ['ß', 'ä', 'ö', 'ü']
    }

    const markers = regionalMarkers[language] || []
    const markerCount = markers.filter(marker => text.includes(marker)).length

    return Math.min(baseRegionality + (markerCount * 0.1), 1.0)
  }

  /**
   * 调整传统性
   */
  private adjustTraditionality(baseTraditional: number, text: string, language: string): number {
    // 基于词长和复杂度判断传统性
    const lengthFactor = text.length > 8 ? 0.1 : 0
    const complexityFactor = text.match(/[^a-zA-Z]/) ? 0.05 : 0

    return Math.min(baseTraditional + lengthFactor + complexityFactor, 1.0)
  }

  /**
   * 计算美学价值
   */
  private calculateAestheticValue(text: string, config: EuropeanLanguageConfig): number {
    const euphonyWeight = config.phonetic_weights.euphony
    const eleganceWeight = config.cultural_weights.elegance

    // 基于音韵和谐度计算美学价值
    const vowelRatio = (text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []).length / text.length
    const rhythmScore = this.calculateRhythmScore(text)

    return Math.min(
      (vowelRatio * euphonyWeight + rhythmScore * eleganceWeight) / 2,
      1.0
    )
  }

  /**
   * 计算优雅度（法语特有）
   */
  private calculateElegance(text: string, config: EuropeanLanguageConfig): number {
    const eleganceWeight = config.cultural_weights.elegance

    // 基于法语特有的优雅特征
    const silentLetters = (text.match(/[hx]$/gi) || []).length
    const liaisons = (text.match(/[snrt]$/gi) || []).length

    return Math.min(
      eleganceWeight * (1 + (silentLetters + liaisons) * 0.05),
      1.0
    )
  }

  /**
   * 计算精确度（德语特有）
   */
  private calculatePrecision(text: string, config: EuropeanLanguageConfig): number {
    const formalityWeight = config.cultural_weights.formality

    // 基于德语的精确性特征
    const compoundComplexity = this.analyzeGermanCompound(text).length
    const caseMarkers = (text.match(/[A-Z]/g) || []).length

    return Math.min(
      formalityWeight * (1 + (compoundComplexity - 1) * 0.1 + caseMarkers * 0.05),
      1.0
    )
  }

  /**
   * 计算节奏评分
   */
  private calculateRhythmScore(text: string): number {
    // 简化实现：基于音节交替模式
    const syllables = text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []
    const consonants = text.match(/[bcdfghjklmnpqrstvwxyzñç]/gi) || []

    const ratio = syllables.length / (syllables.length + consonants.length)

    // 理想的元音辅音比例约为 0.4-0.6
    return 1 - Math.abs(ratio - 0.5) * 2
  }
}
