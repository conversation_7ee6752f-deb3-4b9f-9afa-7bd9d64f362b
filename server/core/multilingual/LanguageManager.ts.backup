/**
 * 语言管理器
 * 
 * 负责多语种数据加载、语言切换、跨语言映射等核心功能
 * 支持v3.0多语种架构的概念-语言分离模式
 * 
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */

import * as fs from 'fs'
import * as path from 'path'

// 使用process.cwd()作为基础路径，避免import.meta兼容性问题
const __dirname = process.cwd()

// 导入类型定义和枚举
import { LanguageCode, RegisterLevel } from '../../types/multilingual.js'
import type {
  UniversalConcept,
  LanguageSpecificMorpheme,
  ConceptMorphemeMapping
} from '../../types/multilingual.js'

// ============================================================================
// 配置和常量
// ============================================================================

const DATA_DIR = path.join(__dirname, '../../data')

// 支持的语言配置
const SUPPORTED_LANGUAGES: Record<LanguageCode, {
  name: string
  dataFile: string
  conceptFile: string | null
  enabled: boolean
}> = {
  [LanguageCode.ZH_CN]: {
    name: '中文(简体)',
    dataFile: 'morphemes_current.json',
    conceptFile: null, // 中文使用传统格式
    enabled: true
  },
  [LanguageCode.EN_US]: {
    name: 'English (US)',
    dataFile: 'english_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: true
  },
  [LanguageCode.JA_JP]: {
    name: '日本語',
    dataFile: 'japanese_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.KO_KR]: {
    name: '한국어',
    dataFile: 'korean_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.ES_ES]: {
    name: 'Español',
    dataFile: 'spanish_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.FR_FR]: {
    name: 'Français',
    dataFile: 'french_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.DE_DE]: {
    name: 'Deutsch',
    dataFile: 'german_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.AR_SA]: {
    name: 'العربية',
    dataFile: 'arabic_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  }
  // 其他语言将在后续阶段添加
}

// ============================================================================
// 语言管理器接口
// ============================================================================

export interface LanguageManagerConfig {
  /** 默认语言 */
  defaultLanguage: LanguageCode
  /** 启用的语言列表 */
  enabledLanguages: LanguageCode[]
  /** 是否启用缓存 */
  enableCache: boolean
  /** 缓存TTL (秒) */
  cacheTTL: number
}

export interface LanguageDataSet {
  /** 语言代码 */
  language: LanguageCode
  /** 语言特定语素 */
  morphemes: LanguageSpecificMorpheme[]
  /** 通用概念 (仅v3.0格式) */
  concepts?: UniversalConcept[]
  /** 概念-语素映射 */
  conceptMorphemeMapping: Map<string, LanguageSpecificMorpheme[]>
  /** 加载时间戳 */
  loadedAt: number
}

// ============================================================================
// 语言管理器类
// ============================================================================

/**
 * 语言管理器类
 * 
 * 提供多语种数据管理、语言切换、跨语言映射等功能
 */
export class LanguageManager {
  private config: LanguageManagerConfig
  private languageDataSets: Map<LanguageCode, LanguageDataSet> = new Map()
  private conceptsCache: Map<string, UniversalConcept> = new Map()
  private isInitialized = false

  /**
   * 构造函数
   */
  constructor(config: Partial<LanguageManagerConfig> = {}) {
    this.config = {
      defaultLanguage: config.defaultLanguage || LanguageCode.ZH_CN,
      enabledLanguages: config.enabledLanguages || [LanguageCode.ZH_CN], // 暂时只启用中文
      enableCache: config.enableCache ?? true,
      cacheTTL: config.cacheTTL || 3600 // 1小时
    }
  }

  /**
   * 初始化语言管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 语言管理器已初始化，跳过重复初始化')
      return
    }

    const startTime = Date.now()
    console.log('🌍 开始初始化多语种语言管理器...')

    try {
      // 加载启用的语言数据
      for (const language of this.config.enabledLanguages) {
        if (SUPPORTED_LANGUAGES[language]?.enabled) {
          await this.loadLanguageData(language)
        }
      }

      // 构建概念缓存
      this.buildConceptsCache()

      this.isInitialized = true
      const initTime = Date.now() - startTime

      console.log(`✅ 语言管理器初始化完成: ${this.config.enabledLanguages.length}种语言, 耗时${initTime}ms`)
      this.logInitializationSummary()

    } catch (error) {
      console.error('❌ 语言管理器初始化失败:', error)
      throw new Error(`语言管理器初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 加载指定语言的数据
   */
  private async loadLanguageData(language: LanguageCode): Promise<void> {
    const config = SUPPORTED_LANGUAGES[language]
    if (!config) {
      throw new Error(`不支持的语言: ${language}`)
    }

    console.log(`📁 加载${config.name}数据...`)

    try {
      const dataFilePath = path.join(DATA_DIR, config.dataFile)
      
      if (!fs.existsSync(dataFilePath)) {
        throw new Error(`数据文件不存在: ${dataFilePath}`)
      }

      // 加载语素数据
      const morphemeData = JSON.parse(fs.readFileSync(dataFilePath, 'utf8'))
      let morphemes: LanguageSpecificMorpheme[]
      let concepts: UniversalConcept[] | undefined

      if (language === LanguageCode.ZH_CN) {
        // 中文使用传统格式，需要适配
        morphemes = this.adaptChineseMorphemes(morphemeData)
      } else {
        // 其他语言使用v3.0格式
        morphemes = morphemeData as LanguageSpecificMorpheme[]
        
        // 加载通用概念
        if (config.conceptFile) {
          const conceptFilePath = path.join(DATA_DIR, config.conceptFile)
          if (fs.existsSync(conceptFilePath)) {
            concepts = JSON.parse(fs.readFileSync(conceptFilePath, 'utf8'))
          }
        }
      }

      // 构建概念-语素映射
      const conceptMorphemeMapping = this.buildConceptMorphemeMapping(morphemes)

      // 创建语言数据集
      const languageDataSet: LanguageDataSet = {
        language,
        morphemes,
        concepts,
        conceptMorphemeMapping,
        loadedAt: Date.now()
      }

      this.languageDataSets.set(language, languageDataSet)
      console.log(`✅ ${config.name}数据加载完成: ${morphemes.length}个语素`)

    } catch (error) {
      throw new Error(`加载${config.name}数据失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 适配中文语素数据到v3.0格式
   */
  private adaptChineseMorphemes(chineseData: any[]): LanguageSpecificMorpheme[] {
    return chineseData.map((item, index) => ({
      morpheme_id: `zh_${item.id || index}`,
      concept_id: `concept_${item.text}`, // 临时概念ID
      language: LanguageCode.ZH_CN,
      text: item.text,
      alternative_forms: [],
      phonetic_features: {
        ipa_transcription: item.language_properties?.pronunciation || '',
        syllable_count: item.language_properties?.syllable_count || 1,
        tone_pattern: [], // 中文声调信息
        phonetic_harmony: 0.8
      },
      morphological_info: {
        pos_tag: this.mapCategoryToPOS(item.category),
        morphological_type: item.language_properties?.morphological_type || 'root',
        prefixes: [],
        suffixes: []
      },
      syntactic_properties: {
        syntactic_function: ['root'],
        collocation_constraints: [],
        grammatical_features: {}
      },
      cultural_context: {
        traditionality: item.cultural_context === 'ancient' ? 0.9 : 0.3,
        modernity: item.cultural_context === 'modern' ? 0.9 : 0.3,
        formality: 0.5,
        regionality: 0.2,
        religious_sensitivity: 0.1,
        age_appropriateness: ['all'],
        cultural_tags: item.tags || []
      },
      regional_variants: [],
      register_level: RegisterLevel.NEUTRAL,
      language_quality_scores: {
        naturalness: item.quality_metrics?.naturalness || 0.8,
        fluency: 0.9,
        authenticity: 0.9,
        aesthetic_appeal: item.quality_metrics?.aesthetic_appeal || 0.8,
        pronunciation_ease: 0.8,
        memorability: 0.8,
        uniqueness: 0.6,
        practicality: 0.9
      },
      cultural_appropriateness: 0.9,
      native_speaker_rating: item.quality_score || 0.8,
      usage_frequency: item.usage_frequency || 0.5,
      popularity_trend: 0.0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      version: '3.0.0',
      source: 'chinese_morphemes_adapted',
      validation_status: 'validated'
    }))
  }

  /**
   * 映射类别到词性标注
   */
  private mapCategoryToPOS(category: string): string {
    const mapping: Record<string, string> = {
      'emotions': 'ADJ',
      'professions': 'NOUN',
      'characteristics': 'ADJ',
      'objects': 'NOUN',
      'actions': 'VERB',
      'concepts': 'NOUN'
    }
    return mapping[category] || 'NOUN'
  }

  /**
   * 构建概念-语素映射
   */
  private buildConceptMorphemeMapping(morphemes: LanguageSpecificMorpheme[]): Map<string, LanguageSpecificMorpheme[]> {
    const mapping = new Map<string, LanguageSpecificMorpheme[]>()
    
    for (const morpheme of morphemes) {
      const conceptId = morpheme.concept_id
      if (!mapping.has(conceptId)) {
        mapping.set(conceptId, [])
      }
      mapping.get(conceptId)!.push(morpheme)
    }
    
    return mapping
  }

  /**
   * 构建概念缓存
   */
  private buildConceptsCache(): void {
    for (const dataSet of Array.from(this.languageDataSets.values())) {
      if (dataSet.concepts) {
        for (const concept of dataSet.concepts) {
          this.conceptsCache.set(concept.concept_id, concept)
        }
      }
    }
    console.log(`📊 概念缓存构建完成: ${this.conceptsCache.size}个概念`)
  }

  /**
   * 获取指定语言的语素
   */
  getMorphemesByLanguage(language: LanguageCode): LanguageSpecificMorpheme[] {
    const dataSet = this.languageDataSets.get(language)
    if (!dataSet) {
      throw new Error(`语言数据未加载: ${language}`)
    }
    return dataSet.morphemes
  }

  /**
   * 根据概念ID获取指定语言的语素
   */
  getMorphemesByConceptAndLanguage(conceptId: string, language: LanguageCode): LanguageSpecificMorpheme[] {
    const dataSet = this.languageDataSets.get(language)
    if (!dataSet) {
      throw new Error(`语言数据未加载: ${language}`)
    }
    return dataSet.conceptMorphemeMapping.get(conceptId) || []
  }

  /**
   * 获取通用概念
   */
  getConcept(conceptId: string): UniversalConcept | undefined {
    return this.conceptsCache.get(conceptId)
  }

  /**
   * 获取所有支持的语言
   */
  getSupportedLanguages(): LanguageCode[] {
    return this.config.enabledLanguages
  }

  /**
   * 检查语言是否支持
   */
  isLanguageSupported(language: LanguageCode): boolean {
    return this.languageDataSets.has(language)
  }

  /**
   * 获取语言统计信息
   */
  getLanguageStats(language: LanguageCode): { morphemeCount: number; conceptCount: number } {
    const dataSet = this.languageDataSets.get(language)
    if (!dataSet) {
      return { morphemeCount: 0, conceptCount: 0 }
    }
    
    return {
      morphemeCount: dataSet.morphemes.length,
      conceptCount: dataSet.concepts?.length || 0
    }
  }

  /**
   * 重新加载语言数据
   */
  async reloadLanguage(language: LanguageCode): Promise<void> {
    console.log(`🔄 重新加载${language}数据...`)
    await this.loadLanguageData(language)
    this.buildConceptsCache()
  }

  /**
   * 输出初始化摘要
   */
  private logInitializationSummary(): void {
    console.log('\n🌍 多语种语言管理器初始化摘要:')
    console.log(`   默认语言: ${this.config.defaultLanguage}`)
    console.log(`   启用语言: ${this.config.enabledLanguages.join(', ')}`)
    console.log(`   概念总数: ${this.conceptsCache.size}`)
    
    for (const language of this.config.enabledLanguages) {
      const stats = this.getLanguageStats(language)
      console.log(`   ${language}: ${stats.morphemeCount}个语素, ${stats.conceptCount}个概念`)
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized
  }
}
