/**
 * 数据验证器
 * 
 * 提供语素数据的完整性验证、质量检查和一致性校验功能。
 * 支持多层次验证规则和自定义验证策略。
 * 
 * @fileoverview 数据验证核心模块
 * @version 3.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */

import type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'

// ============================================================================
// 验证规则配置
// ============================================================================

/** 验证规则配置 */
const VALIDATION_RULES = {
  /** ID格式正则表达式 */
  id_pattern: /^[a-z_]+_\d{3}$/,
  /** 文本长度限制 */
  text_length: { min: 1, max: 10 },
  /** 子分类命名规则 */
  subcategory_pattern: /^[a-z_]+$/,
  /** 标签数量限制 */
  tags_count: { min: 1, max: 10 },
  /** 语义向量维度 */
  semantic_vector_dimension: 20,
  /** 质量评分范围 */
  quality_range: { min: 0.0, max: 1.0 },
  /** 使用频率范围 */
  frequency_range: { min: 0.0, max: 1.0 },
  /** 音节数量限制 */
  syllable_count: { min: 1, max: 5 },
  /** 字符数量限制 */
  character_count: { min: 1, max: 10 }
} as const

/** 有效的语素类别 */
const VALID_CATEGORIES = Object.values({
  EMOTIONS: 'emotions',
  PROFESSIONS: 'professions',
  CHARACTERISTICS: 'characteristics',
  OBJECTS: 'objects',
  ACTIONS: 'actions',
  CONCEPTS: 'concepts'
})

/** 有效的文化语境 */
const VALID_CONTEXTS = Object.values({
  ANCIENT: 'ancient',
  MODERN: 'modern',
  NEUTRAL: 'neutral'
})

// ============================================================================
// 接口定义
// ============================================================================

/**
 * 验证错误接口
 */
export interface ValidationError {
  /** 错误类型 */
  type: 'error' | 'warning' | 'info'
  /** 错误代码 */
  code: string
  /** 错误消息 */
  message: string
  /** 字段路径 */
  field?: string
  /** 期望值 */
  expected?: any
  /** 实际值 */
  actual?: any
  /** 语素ID */
  morpheme_id?: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 验证是否通过 */
  passed: boolean
  /** 验证的语素数量 */
  total_count: number
  /** 通过验证的语素数量 */
  passed_count: number
  /** 失败的语素数量 */
  failed_count: number
  /** 错误列表 */
  errors: ValidationError[]
  /** 警告列表 */
  warnings: ValidationError[]
  /** 验证耗时 (毫秒) */
  validation_time: number
  /** 验证时间戳 */
  timestamp: number
}

/**
 * 验证配置接口
 */
export interface ValidationConfig {
  /** 是否启用严格模式 */
  strict_mode?: boolean
  /** 是否跳过警告 */
  skip_warnings?: boolean
  /** 自定义验证规则 */
  custom_rules?: ValidationRule[]
  /** 最大错误数量 (超过则停止验证) */
  max_errors?: number
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  /** 规则名称 */
  name: string
  /** 规则描述 */
  description: string
  /** 验证函数 */
  validator: (morpheme: Morpheme) => ValidationError[]
  /** 规则优先级 */
  priority: number
  /** 是否启用 */
  enabled: boolean
}

// ============================================================================
// 数据验证器类
// ============================================================================

/**
 * 数据验证器类
 * 
 * 提供全面的语素数据验证功能
 */
export class DataValidator {
  private config: Required<ValidationConfig>
  private customRules: ValidationRule[]

  /**
   * 构造函数
   * 
   * @param config 验证配置
   */
  constructor(config: ValidationConfig = {}) {
    this.config = {
      strict_mode: config.strict_mode ?? true,
      skip_warnings: config.skip_warnings ?? false,
      custom_rules: config.custom_rules ?? [],
      max_errors: config.max_errors ?? 1000
    }
    
    this.customRules = [...this.config.custom_rules]
  }

  /**
   * 验证语素数据数组
   * 
   * @param morphemes 语素数据数组
   * @returns 验证结果
   */
  async validate(morphemes: Morpheme[]): Promise<ValidationResult> {
    const startTime = Date.now()
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    let passedCount = 0

    console.log(`🔍 开始验证 ${morphemes.length} 个语素...`)

    for (let i = 0; i < morphemes.length; i++) {
      const morpheme = morphemes[i]
      
      try {
        // 基础验证
        const basicErrors = this._validateBasicStructure(morpheme, i)
        
        // 数据类型验证
        const typeErrors = this._validateDataTypes(morpheme, i)
        
        // 业务逻辑验证
        const logicErrors = this._validateBusinessLogic(morpheme, i)
        
        // 一致性验证
        const consistencyErrors = this._validateConsistency(morpheme, i)
        
        // 自定义规则验证
        const customErrors = this._validateCustomRules(morpheme, i)
        
        // 收集所有错误
        const allErrors = [
          ...basicErrors,
          ...typeErrors,
          ...logicErrors,
          ...consistencyErrors,
          ...customErrors
        ]
        
        // 分类错误和警告
        const morphemeErrors = allErrors.filter(e => e.type === 'error')
        const morphemeWarnings = allErrors.filter(e => e.type === 'warning')
        
        errors.push(...morphemeErrors)
        warnings.push(...morphemeWarnings)
        
        // 如果没有错误，则通过验证
        if (morphemeErrors.length === 0) {
          passedCount++
        }
        
        // 检查是否超过最大错误数量
        if (errors.length >= this.config.max_errors) {
          console.warn(`⚠️ 错误数量超过限制 (${this.config.max_errors})，停止验证`)
          break
        }
        
      } catch (error) {
        errors.push({
          type: 'error',
          code: 'VALIDATION_EXCEPTION',
          message: `验证过程中发生异常: ${error instanceof Error ? error.message : String(error)}`,
          morpheme_id: morpheme.id || `index_${i}`
        })
      }
    }

    const validationTime = Date.now() - startTime
    const passed = errors.length === 0

    // 输出验证结果摘要
    if (passed) {
      console.log(`✅ 验证通过: ${passedCount}/${morphemes.length} 个语素, 耗时 ${validationTime}ms`)
    } else {
      console.warn(`❌ 验证失败: ${passedCount}/${morphemes.length} 个语素通过, ${errors.length} 个错误, ${warnings.length} 个警告, 耗时 ${validationTime}ms`)
    }

    return {
      passed,
      total_count: morphemes.length,
      passed_count: passedCount,
      failed_count: morphemes.length - passedCount,
      errors,
      warnings,
      validation_time: validationTime,
      timestamp: Date.now()
    }
  }

  /**
   * 验证基础结构
   * 
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  private _validateBasicStructure(morpheme: Morpheme, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const morphemeId = morpheme.id || `index_${index}`

    // 验证必需字段 (v3.0标准)
    const requiredFields = [
      'id', 'text', 'category', 'subcategory', 'cultural_context',
      'usage_frequency', 'quality_score', 'semantic_vector', 'tags',
      'language_properties', 'quality_metrics', 'created_at', 'source', 'version'
    ]

    for (const field of requiredFields) {
      if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined || morpheme[field as keyof Morpheme] === null) {
        errors.push({
          type: 'error',
          code: 'MISSING_REQUIRED_FIELD',
          message: `缺少必需字段: ${field}`,
          field,
          morpheme_id: morphemeId
        })
      }
    }

    // 所有字段现在都是必需的 (v3.0标准)
    // 不再有可选字段验证

    return errors
  }

  /**
   * 验证数据类型
   * 
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  private _validateDataTypes(morpheme: Morpheme, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const morphemeId = morpheme.id || `index_${index}`

    // 验证字符串字段
    const stringFields = ['id', 'text', 'subcategory', 'source', 'version']
    for (const field of stringFields) {
      const value = morpheme[field as keyof Morpheme]
      if (value !== undefined && typeof value !== 'string') {
        errors.push({
          type: 'error',
          code: 'INVALID_DATA_TYPE',
          message: `字段 ${field} 必须是字符串类型`,
          field,
          expected: 'string',
          actual: typeof value,
          morpheme_id: morphemeId
        })
      }
    }

    // 验证数字字段
    const numberFields = ['usage_frequency', 'quality_score', 'created_at']
    for (const field of numberFields) {
      const value = morpheme[field as keyof Morpheme]
      if (value !== undefined && typeof value !== 'number') {
        errors.push({
          type: 'error',
          code: 'INVALID_DATA_TYPE',
          message: `字段 ${field} 必须是数字类型`,
          field,
          expected: 'number',
          actual: typeof value,
          morpheme_id: morphemeId
        })
      }
    }

    // 验证数组字段
    if (morpheme.semantic_vector !== undefined && !Array.isArray(morpheme.semantic_vector)) {
      errors.push({
        type: 'error',
        code: 'INVALID_DATA_TYPE',
        message: 'semantic_vector 必须是数组类型',
        field: 'semantic_vector',
        expected: 'array',
        actual: typeof morpheme.semantic_vector,
        morpheme_id: morphemeId
      })
    }

    if (morpheme.tags !== undefined && !Array.isArray(morpheme.tags)) {
      errors.push({
        type: 'error',
        code: 'INVALID_DATA_TYPE',
        message: 'tags 必须是数组类型',
        field: 'tags',
        expected: 'array',
        actual: typeof morpheme.tags,
        morpheme_id: morphemeId
      })
    }

    return errors
  }

  /**
   * 验证业务逻辑
   * 
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  private _validateBusinessLogic(morpheme: Morpheme, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const morphemeId = morpheme.id || `index_${index}`

    // 验证ID格式
    if (morpheme.id && !VALIDATION_RULES.id_pattern.test(morpheme.id)) {
      errors.push({
        type: 'error',
        code: 'INVALID_ID_FORMAT',
        message: `ID格式不正确，应为: {category}_{number}`,
        field: 'id',
        expected: VALIDATION_RULES.id_pattern.toString(),
        actual: morpheme.id,
        morpheme_id: morphemeId
      })
    }

    // 验证文本长度
    if (morpheme.text) {
      const textLength = morpheme.text.length
      if (textLength < VALIDATION_RULES.text_length.min || textLength > VALIDATION_RULES.text_length.max) {
        errors.push({
          type: 'error',
          code: 'INVALID_TEXT_LENGTH',
          message: `文本长度必须在 ${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max} 之间`,
          field: 'text',
          expected: `${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max}`,
          actual: textLength,
          morpheme_id: morphemeId
        })
      }
    }

    // 验证类别
    if (morpheme.category && !VALID_CATEGORIES.includes(morpheme.category as any)) {
      errors.push({
        type: 'error',
        code: 'INVALID_CATEGORY',
        message: `无效的语素类别`,
        field: 'category',
        expected: VALID_CATEGORIES,
        actual: morpheme.category,
        morpheme_id: morphemeId
      })
    }

    // 验证文化语境
    if (morpheme.cultural_context && !VALID_CONTEXTS.includes(morpheme.cultural_context as any)) {
      errors.push({
        type: 'error',
        code: 'INVALID_CULTURAL_CONTEXT',
        message: `无效的文化语境`,
        field: 'cultural_context',
        expected: VALID_CONTEXTS,
        actual: morpheme.cultural_context,
        morpheme_id: morphemeId
      })
    }

    // 验证质量评分范围
    if (typeof morpheme.quality_score === 'number') {
      if (morpheme.quality_score < VALIDATION_RULES.quality_range.min || 
          morpheme.quality_score > VALIDATION_RULES.quality_range.max) {
        errors.push({
          type: 'error',
          code: 'INVALID_QUALITY_SCORE',
          message: `质量评分必须在 ${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max} 之间`,
          field: 'quality_score',
          expected: `${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max}`,
          actual: morpheme.quality_score,
          morpheme_id: morphemeId
        })
      }
    }

    // 验证使用频率范围
    if (typeof morpheme.usage_frequency === 'number') {
      if (morpheme.usage_frequency < VALIDATION_RULES.frequency_range.min || 
          morpheme.usage_frequency > VALIDATION_RULES.frequency_range.max) {
        errors.push({
          type: 'error',
          code: 'INVALID_USAGE_FREQUENCY',
          message: `使用频率必须在 ${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max} 之间`,
          field: 'usage_frequency',
          expected: `${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max}`,
          actual: morpheme.usage_frequency,
          morpheme_id: morphemeId
        })
      }
    }

    // 验证语义向量
    if (Array.isArray(morpheme.semantic_vector)) {
      if (morpheme.semantic_vector.length !== VALIDATION_RULES.semantic_vector_dimension) {
        errors.push({
          type: 'error',
          code: 'INVALID_SEMANTIC_VECTOR_DIMENSION',
          message: `语义向量维度必须为 ${VALIDATION_RULES.semantic_vector_dimension}`,
          field: 'semantic_vector',
          expected: VALIDATION_RULES.semantic_vector_dimension,
          actual: morpheme.semantic_vector.length,
          morpheme_id: morphemeId
        })
      }

      // 验证向量元素是否为数字
      for (let i = 0; i < morpheme.semantic_vector.length; i++) {
        if (typeof morpheme.semantic_vector[i] !== 'number') {
          errors.push({
            type: 'error',
            code: 'INVALID_SEMANTIC_VECTOR_ELEMENT',
            message: `语义向量元素必须是数字类型`,
            field: `semantic_vector[${i}]`,
            expected: 'number',
            actual: typeof morpheme.semantic_vector[i],
            morpheme_id: morphemeId
          })
        }
      }
    }

    return errors
  }

  /**
   * 验证一致性
   * 
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  private _validateConsistency(morpheme: Morpheme, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const morphemeId = morpheme.id || `index_${index}`

    // 验证ID与类别的一致性
    if (morpheme.id && morpheme.category) {
      const expectedPrefix = morpheme.category
      if (!morpheme.id.startsWith(expectedPrefix)) {
        errors.push({
          type: 'warning',
          code: 'INCONSISTENT_ID_CATEGORY',
          message: `ID前缀与类别不一致`,
          field: 'id',
          expected: `${expectedPrefix}_xxx`,
          actual: morpheme.id,
          morpheme_id: morphemeId
        })
      }
    }

    // 验证语言属性一致性
    if (morpheme.language_properties && morpheme.text) {
      const actualCharCount = morpheme.text.length
      const declaredCharCount = morpheme.language_properties.character_count

      if (declaredCharCount !== actualCharCount) {
        errors.push({
          type: 'warning',
          code: 'INCONSISTENT_CHARACTER_COUNT',
          message: `声明的字符数与实际不符`,
          field: 'language_properties.character_count',
          expected: actualCharCount,
          actual: declaredCharCount,
          morpheme_id: morphemeId
        })
      }
    }

    return errors
  }

  /**
   * 验证自定义规则
   * 
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  private _validateCustomRules(morpheme: Morpheme, index: number): ValidationError[] {
    const errors: ValidationError[] = []

    for (const rule of this.customRules) {
      if (!rule.enabled) continue

      try {
        const ruleErrors = rule.validator(morpheme)
        errors.push(...ruleErrors)
      } catch (error) {
        errors.push({
          type: 'error',
          code: 'CUSTOM_RULE_ERROR',
          message: `自定义规则 '${rule.name}' 执行失败: ${error instanceof Error ? error.message : String(error)}`,
          morpheme_id: morpheme.id || `index_${index}`
        })
      }
    }

    return errors
  }

  /**
   * 添加自定义验证规则
   * 
   * @param rule 验证规则
   */
  addCustomRule(rule: ValidationRule): void {
    this.customRules.push(rule)
    console.log(`📋 添加自定义验证规则: ${rule.name}`)
  }

  /**
   * 移除自定义验证规则
   * 
   * @param ruleName 规则名称
   */
  removeCustomRule(ruleName: string): void {
    const index = this.customRules.findIndex(rule => rule.name === ruleName)
    if (index !== -1) {
      this.customRules.splice(index, 1)
      console.log(`🗑️ 移除自定义验证规则: ${ruleName}`)
    }
  }
}
