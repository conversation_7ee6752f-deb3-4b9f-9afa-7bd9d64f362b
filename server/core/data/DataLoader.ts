/**
 * 数据加载器
 *
 * 负责从文件系统加载语素数据，支持v3.0多语种架构、数据验证和管理。
 * 实现了热重载、缓存优化和错误恢复机制。
 *
 * @fileoverview 数据加载和管理核心模块 - v3.0多语种架构
 * @version 3.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */

import { promises as fs } from 'fs'
import { join, extname } from 'path'
import { watch } from 'fs'

// 类型导入
import type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'
import { SUPPORTED_VERSIONS } from '../../types/common'
import { DataValidator } from './DataValidator'

// ============================================================================
// 配置常量
// ============================================================================

/** 数据文件目录路径 */
const DATA_DIR = join(process.cwd(), 'data')

/** 支持的数据文件格式 */
const SUPPORTED_FORMATS = ['.json', '.jsonl'] as const

/** 当前数据版本 */
const CURRENT_VERSION = '3.0.0' as const

/** 数据验证配置 */
const VALIDATION_CONFIG = {
  /** 语义向量维度 */
  semantic_vector_dimension: 20,
  /** 最小质量评分 */
  min_quality_score: 0.0,
  /** 最大质量评分 */
  max_quality_score: 1.0,
  /** 必需字段 */
  required_fields: [
    'id', 'text', 'category', 'subcategory', 'cultural_context',
    'usage_frequency', 'quality_score', 'semantic_vector', 'tags',
    'language_properties', 'quality_metrics', 'created_at', 'source', 'version'
  ] as const
} as const

// ============================================================================
// 接口定义
// ============================================================================

/**
 * 数据加载配置接口
 */
export interface DataLoaderConfig {
  /** 数据目录路径 */
  dataDir?: string
  /** 是否启用热重载 */
  enableHotReload?: boolean
  /** 是否启用数据验证 */
  enableValidation?: boolean
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 缓存TTL (秒) */
  cacheTTL?: number
  /** 最大重试次数 */
  maxRetries?: number
}

/**
 * 数据加载结果接口
 */
export interface DataLoadResult {
  /** 加载的语素数据 */
  morphemes: Morpheme[]
  /** 加载统计信息 */
  stats: {
    total_count: number
    by_category: Record<string, number>
    by_context: Record<string, number>
    avg_quality: number
    load_time: number
  }
  /** 验证结果 */
  validation: {
    passed: boolean
    errors: string[]
    warnings: string[]
  }
  /** 加载时间戳 */
  timestamp: number
}

/**
 * 数据验证结果接口
 */
export interface ValidationResult {
  /** 验证是否通过 */
  passed: boolean
  /** 错误信息 */
  errors: string[]
  /** 警告信息 */
  warnings: string[]
  /** 验证的数据项数量 */
  validated_count: number
  /** 验证耗时 (毫秒) */
  validation_time: number
}

// ============================================================================
// 数据加载器类
// ============================================================================

/**
 * 数据加载器类
 * 
 * 提供语素数据的加载、验证、缓存和热重载功能
 */
export class DataLoader {
  private config: Required<DataLoaderConfig>
  private cache: Map<string, { data: Morpheme[], timestamp: number }> = new Map()
  private watchers: Map<string, any> = new Map()
  private loadPromise: Promise<DataLoadResult> | null = null
  private validator: DataValidator

  /**
   * 构造函数
   * 
   * @param config 数据加载配置
   */
  constructor(config: DataLoaderConfig = {}) {
    this.config = {
      dataDir: config.dataDir || DATA_DIR,
      enableHotReload: config.enableHotReload ?? true,
      enableValidation: config.enableValidation ?? true,
      enableCache: config.enableCache ?? true,
      cacheTTL: config.cacheTTL ?? 3600, // 1小时
      maxRetries: config.maxRetries ?? 3
    }

    // 初始化数据验证器
    this.validator = new DataValidator({
      strict_mode: true,
      skip_warnings: false
    })
  }

  /**
   * 加载所有语素数据
   * 
   * @returns 数据加载结果
   */
  async loadAll(): Promise<DataLoadResult> {
    // 防止并发加载
    if (this.loadPromise) {
      return this.loadPromise
    }

    this.loadPromise = this._performLoad()
    
    try {
      const result = await this.loadPromise
      return result
    } finally {
      this.loadPromise = null
    }
  }

  /**
   * 执行数据加载
   *
   * @private
   * @returns 数据加载结果
   */
  private async _performLoad(): Promise<DataLoadResult> {
    return this._performLoadWithRetry(0)
  }

  /**
   * 执行数据加载（带重试机制）
   *
   * @private
   * @param attempt 当前尝试次数
   * @returns 数据加载结果
   */
  private async _performLoadWithRetry(attempt: number): Promise<DataLoadResult> {
    const startTime = Date.now()

    try {
      // 1. 扫描数据文件
      const dataFiles = await this._scanDataFiles()
      
      // 2. 加载数据文件
      const allMorphemes: Morpheme[] = []

      for (const filePath of dataFiles) {
        const morphemes = await this._loadDataFile(filePath)
        allMorphemes.push(...morphemes)
      }

      // 3. 数据验证
      const validation = this.config.enableValidation 
        ? await this._validateData(allMorphemes)
        : { passed: true, errors: [], warnings: [], validated_count: allMorphemes.length, validation_time: 0 }

      // 4. 计算统计信息
      const stats = this._calculateStats(allMorphemes, Date.now() - startTime)

      // 5. 设置热重载
      if (this.config.enableHotReload) {
        await this._setupHotReload(dataFiles)
      }

      const result: DataLoadResult = {
        morphemes: allMorphemes,
        stats,
        validation,
        timestamp: Date.now()
      }

      console.log(`✅ 数据加载完成: ${allMorphemes.length}个语素, 耗时${stats.load_time}ms`)
      
      return result

    } catch (error) {
      // 重试逻辑
      if (attempt < this.config.maxRetries) {
        console.warn(`⚠️ 数据加载失败，正在重试 (${attempt + 1}/${this.config.maxRetries}):`, error)
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1))) // 指数退避
        return this._performLoadWithRetry(attempt + 1)
      }

      console.error('❌ 数据加载失败:', error)
      throw new Error(`数据加载失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 扫描数据文件
   * 
   * @private
   * @returns 数据文件路径列表
   */
  private async _scanDataFiles(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.config.dataDir)
      const dataFiles: string[] = []

      for (const file of files) {
        const filePath = join(this.config.dataDir, file)
        const fileStat = await fs.stat(filePath)

        // 只加载语素数据文件，跳过其他文件
        if (fileStat.isFile() &&
            SUPPORTED_FORMATS.includes(extname(file) as any) &&
            (file.includes('morpheme') || file.includes('语素'))) {
          dataFiles.push(filePath)
        }
      }

      if (dataFiles.length === 0) {
        throw new Error(`未找到数据文件，目录: ${this.config.dataDir}`)
      }

      console.log(`📁 发现${dataFiles.length}个数据文件`)
      return dataFiles

    } catch (error) {
      throw new Error(`扫描数据文件失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 加载单个数据文件
   * 
   * @private
   * @param filePath 文件路径
   * @returns 语素数据数组
   */
  private async _loadDataFile(filePath: string): Promise<Morpheme[]> {
    try {
      // 检查缓存
      if (this.config.enableCache) {
        const cached = this.cache.get(filePath)
        if (cached && Date.now() - cached.timestamp < this.config.cacheTTL * 1000) {
          console.log(`📋 使用缓存数据: ${filePath}`)
          return cached.data
        }
      }

      // 读取文件内容
      const content = await fs.readFile(filePath, 'utf-8')
      let morphemes: Morpheme[]

      // 根据文件格式解析
      if (extname(filePath) === '.json') {
        const parsed = JSON.parse(content)

        // 支持两种格式：直接数组或包含morphemes字段的对象
        if (Array.isArray(parsed)) {
          morphemes = parsed
        } else if (parsed.morphemes && Array.isArray(parsed.morphemes)) {
          morphemes = parsed.morphemes
          console.log(`📋 检测到嵌套格式，提取${morphemes.length}个语素`)
        } else {
          throw new Error(`无效的JSON格式: 期望数组或包含morphemes字段的对象`)
        }
      } else if (extname(filePath) === '.jsonl') {
        morphemes = content
          .split('\n')
          .filter(line => line.trim())
          .map(line => JSON.parse(line))
      } else {
        throw new Error(`不支持的文件格式: ${extname(filePath)}`)
      }

      // 更新缓存
      if (this.config.enableCache) {
        this.cache.set(filePath, {
          data: morphemes,
          timestamp: Date.now()
        })
      }

      console.log(`📄 加载文件: ${filePath} (${morphemes.length}个语素)`)
      return morphemes

    } catch (error) {
      throw new Error(`加载文件失败 ${filePath}: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 验证数据完整性和正确性
   * 
   * @private
   * @param morphemes 语素数据数组
   * @returns 验证结果
   */
  private async _validateData(morphemes: Morpheme[]): Promise<ValidationResult> {
    // 使用DataValidator进行完整验证
    const validationResult = await this.validator.validate(morphemes)

    // 转换格式以保持兼容性
    const errors = validationResult.errors.map(e => `${e.morpheme_id}: ${e.message}`)
    const warnings = validationResult.warnings.map(w => `${w.morpheme_id}: ${w.message}`)

    if (!validationResult.passed) {
      console.warn(`⚠️ 数据验证发现 ${errors.length} 个错误, ${warnings.length} 个警告`)
    } else {
      console.log(`✅ 数据验证通过: ${morphemes.length}个语素, 耗时${validationResult.validation_time}ms`)
    }

    return {
      passed: validationResult.passed,
      errors,
      warnings,
      validated_count: validationResult.total_count,
      validation_time: validationResult.validation_time
    }
  }

  /**
   * 计算统计信息
   * 
   * @private
   * @param morphemes 语素数据数组
   * @param loadTime 加载耗时
   * @returns 统计信息
   */
  private _calculateStats(morphemes: Morpheme[], loadTime: number) {
    const by_category: Record<string, number> = {}
    const by_context: Record<string, number> = {}
    let total_quality = 0

    for (const morpheme of morphemes) {
      // 按类别统计
      by_category[morpheme.category] = (by_category[morpheme.category] || 0) + 1
      
      // 按文化语境统计 (支持v3.0多维度文化语境)
      let contextKey: string
      if (!morpheme.cultural_context) {
        contextKey = 'unknown'
      } else if (typeof morpheme.cultural_context === 'string') {
        // 传统枚举类型
        contextKey = morpheme.cultural_context
      } else if (morpheme.cultural_context.traditionality !== undefined && morpheme.cultural_context.formality !== undefined) {
        // 标准多维度接口
        contextKey = `${morpheme.cultural_context.traditionality}_${morpheme.cultural_context.formality}`
      } else {
        // 自定义对象格式（测试数据兼容）
        const context = morpheme.cultural_context as any
        if (context.ancient !== undefined || context.modern !== undefined || context.neutral !== undefined) {
          contextKey = `ancient:${context.ancient || 0}_modern:${context.modern || 0}_neutral:${context.neutral || 0}`
        } else {
          contextKey = 'unknown'
        }
      }
      by_context[contextKey] = (by_context[contextKey] || 0) + 1
      
      // 累计质量评分
      total_quality += morpheme.quality_score
    }

    return {
      total_count: morphemes.length,
      by_category,
      by_context,
      avg_quality: morphemes.length > 0 ? total_quality / morphemes.length : 0,
      load_time: loadTime
    }
  }

  /**
   * 设置热重载监听
   * 
   * @private
   * @param dataFiles 数据文件路径列表
   */
  private async _setupHotReload(dataFiles: string[]): Promise<void> {
    // 清理现有监听器
    for (const [path, watcher] of this.watchers) {
      if (watcher && typeof watcher.close === 'function') {
        watcher.close()
      }
    }
    this.watchers.clear()

    // 设置新的监听器
    for (const filePath of dataFiles) {
      try {
        const watcher = watch(filePath, (eventType) => {
          if (eventType === 'change') {
            console.log(`🔄 检测到文件变化: ${filePath}`)
            // 清除缓存
            this.cache.delete(filePath)
            // 触发重新加载 (可以添加防抖逻辑)
            this._debounceReload()
          }
        })

        this.watchers.set(filePath, watcher)
      } catch (error) {
        console.warn(`⚠️ 无法监听文件变化: ${filePath}`, error)
      }
    }

    console.log(`👁️ 热重载已启用，监听 ${this.watchers.size} 个文件`)
  }

  /**
   * 防抖重新加载
   * 
   * @private
   */
  private _debounceReload = (() => {
    let timeout: NodeJS.Timeout | null = null
    return () => {
      if (timeout) {
        clearTimeout(timeout)
      }
      timeout = setTimeout(() => {
        console.log('🔄 执行热重载...')
        this.loadAll().catch(error => {
          console.error('❌ 热重载失败:', error)
        })
      }, 1000) // 1秒防抖
    }
  })()



  /**
   * 推断词法类型
   *
   * @private
   * @param category 语素类别
   * @returns 词法类型
   */
  private _inferMorphologicalType(category: string): string {
    const typeMap: Record<string, string> = {
      'emotions': '形容词',
      'professions': '名词',
      'characteristics': '形容词',
      'objects': '名词',
      'actions': '动词',
      'concepts': '名词'
    }

    return typeMap[category] || '未知'
  }

  /**
   * 清理资源
   */
  destroy(): void {
    // 关闭文件监听器
    for (const [path, watcher] of this.watchers) {
      watcher.close()
    }
    this.watchers.clear()

    // 清空缓存
    this.cache.clear()

    console.log('🧹 数据加载器资源已清理')
  }
}
