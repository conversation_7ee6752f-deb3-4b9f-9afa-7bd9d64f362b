/**
 * 东亚语言质量评估器 (v3.0)
 *
 * 专门针对东亚语言（中文、日语、韩语）的8维质量评估系统
 * 考虑汉字文化圈的特殊语言学和文化特性
 *
 * @fileoverview 东亚语言质量评估核心模块
 * @version 3.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */

import { LanguageCode } from '../../types/multilingual'
import type {
  LanguageSpecificMorpheme,
  QualityScores,
  MultiDimensionalCulturalContext
} from '../../types/multilingual'
import { EastAsianLanguageProcessor } from '../multilingual/EastAsianLanguageProcessor'

// ============================================================================
// 东亚语言质量评估接口定义
// ============================================================================

/**
 * 东亚语言特定质量指标
 */
export interface EastAsianQualityMetrics {
  /** 汉字美学评分 */
  hanzi_aesthetics?: number
  /** 假名流畅度 (日语) */
  kana_fluency?: number
  /** 韩文和谐度 (韩语) */
  hangul_harmony?: number
  /** 敬语适当性 */
  honorific_appropriateness?: number
  /** 季节文化关联度 */
  seasonal_cultural_relevance?: number
  /** 传统现代平衡度 */
  tradition_modernity_balance?: number
}

/**
 * 东亚语言质量评估配置
 */
export interface EastAsianQualityConfig {
  /** 语言代码 */
  language: LanguageCode
  /** 文化权重配置 */
  cultural_weights: {
    traditionality: number
    modernity: number
    formality: number
    aesthetics: number
  }
  /** 语言特性权重 */
  linguistic_weights: {
    phonetic_harmony: number
    morphological_complexity: number
    cultural_appropriateness: number
    native_speaker_intuition: number
  }
  /** 质量阈值 */
  quality_thresholds: {
    minimum_acceptable: number
    good_quality: number
    excellent_quality: number
  }
}

// ============================================================================
// 东亚语言质量评估器主类
// ============================================================================

/**
 * 东亚语言质量评估器
 * 
 * 基于8维质量评估体系，针对东亚语言特性进行优化
 */
export class EastAsianQualityAssessor {
  private readonly processor: EastAsianLanguageProcessor
  private readonly configs: Map<LanguageCode, EastAsianQualityConfig>

  constructor() {
    this.processor = new EastAsianLanguageProcessor()
    this.configs = new Map()
    this.initializeConfigs()
  }

  /**
   * 初始化东亚语言质量评估配置
   */
  private initializeConfigs(): void {
    // 中文配置
    this.configs.set(LanguageCode.ZH_CN, {
      language: LanguageCode.ZH_CN,
      cultural_weights: {
        traditionality: 0.85,
        modernity: 0.70,
        formality: 0.75,
        aesthetics: 0.90
      },
      linguistic_weights: {
        phonetic_harmony: 0.80,
        morphological_complexity: 0.75,
        cultural_appropriateness: 0.85,
        native_speaker_intuition: 0.90
      },
      quality_thresholds: {
        minimum_acceptable: 0.70,
        good_quality: 0.80,
        excellent_quality: 0.90
      }
    })

    // 日语配置
    this.configs.set(LanguageCode.JA_JP, {
      language: LanguageCode.JA_JP,
      cultural_weights: {
        traditionality: 0.90,
        modernity: 0.75,
        formality: 0.85,
        aesthetics: 0.95
      },
      linguistic_weights: {
        phonetic_harmony: 0.85,
        morphological_complexity: 0.80,
        cultural_appropriateness: 0.90,
        native_speaker_intuition: 0.95
      },
      quality_thresholds: {
        minimum_acceptable: 0.75,
        good_quality: 0.85,
        excellent_quality: 0.92
      }
    })

    // 韩语配置
    this.configs.set(LanguageCode.KO_KR, {
      language: LanguageCode.KO_KR,
      cultural_weights: {
        traditionality: 0.75,
        modernity: 0.85,
        formality: 0.80,
        aesthetics: 0.85
      },
      linguistic_weights: {
        phonetic_harmony: 0.80,
        morphological_complexity: 0.75,
        cultural_appropriateness: 0.85,
        native_speaker_intuition: 0.88
      },
      quality_thresholds: {
        minimum_acceptable: 0.70,
        good_quality: 0.80,
        excellent_quality: 0.90
      }
    })
  }

  /**
   * 评估东亚语言语素质量
   */
  async assessEastAsianMorphemeQuality(
    morpheme: LanguageSpecificMorpheme
  ): Promise<QualityScores & EastAsianQualityMetrics> {
    const config = this.configs.get(morpheme.language)
    if (!config) {
      throw new Error(`不支持的东亚语言: ${morpheme.language}`)
    }

    // 基础8维质量评估
    const baseScores = await this.calculateBaseQualityScores(morpheme, config)

    // 东亚语言特定指标
    const eastAsianMetrics = await this.calculateEastAsianMetrics(morpheme, config)

    // 综合质量调整
    const adjustedScores = this.adjustScoresForEastAsianFeatures(
      baseScores, 
      eastAsianMetrics, 
      config
    )

    return {
      ...adjustedScores,
      ...eastAsianMetrics
    }
  }

  /**
   * 计算基础8维质量评分
   */
  private async calculateBaseQualityScores(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<QualityScores> {
    return {
      naturalness: await this.assessNaturalness(morpheme, config),
      fluency: await this.assessFluency(morpheme, config),
      authenticity: await this.assessAuthenticity(morpheme, config),
      aesthetic_appeal: await this.assessAestheticAppeal(morpheme, config),
      pronunciation_ease: await this.assessPronunciationEase(morpheme, config),
      memorability: await this.assessMemorability(morpheme, config),
      uniqueness: await this.assessUniqueness(morpheme, config),
      practicality: await this.assessPracticality(morpheme, config)
    }
  }

  /**
   * 计算东亚语言特定指标
   */
  private async calculateEastAsianMetrics(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<EastAsianQualityMetrics> {
    const metrics: EastAsianQualityMetrics = {}

    switch (config.language) {
      case LanguageCode.ZH_CN:
        metrics.hanzi_aesthetics = this.assessHanziAesthetics(morpheme)
        break
      
      case LanguageCode.JA_JP:
        metrics.kana_fluency = this.assessKanaFluency(morpheme)
        metrics.hanzi_aesthetics = this.assessHanziAesthetics(morpheme)
        metrics.honorific_appropriateness = this.assessHonorificAppropriateness(morpheme)
        metrics.seasonal_cultural_relevance = this.assessSeasonalCulturalRelevance(morpheme)
        break
      
      case LanguageCode.KO_KR:
        metrics.hangul_harmony = this.assessHangulHarmony(morpheme)
        metrics.honorific_appropriateness = this.assessHonorificAppropriateness(morpheme)
        break
    }

    // 通用东亚指标
    metrics.tradition_modernity_balance = this.assessTraditionModernityBalance(morpheme)

    return metrics
  }

  /**
   * 根据东亚语言特性调整质量评分
   */
  private adjustScoresForEastAsianFeatures(
    baseScores: QualityScores,
    eastAsianMetrics: EastAsianQualityMetrics,
    config: EastAsianQualityConfig
  ): QualityScores {
    const adjusted = { ...baseScores }

    // 美学评分调整
    if (eastAsianMetrics.hanzi_aesthetics) {
      adjusted.aesthetic_appeal = Math.max(
        adjusted.aesthetic_appeal,
        eastAsianMetrics.hanzi_aesthetics * config.cultural_weights.aesthetics
      )
    }

    // 流畅度调整
    if (eastAsianMetrics.kana_fluency) {
      adjusted.fluency = Math.max(
        adjusted.fluency,
        eastAsianMetrics.kana_fluency * config.linguistic_weights.phonetic_harmony
      )
    }

    // 和谐度调整
    if (eastAsianMetrics.hangul_harmony) {
      adjusted.fluency = Math.max(
        adjusted.fluency,
        eastAsianMetrics.hangul_harmony * config.linguistic_weights.phonetic_harmony
      )
    }

    // 敬语适当性调整
    if (eastAsianMetrics.honorific_appropriateness) {
      adjusted.authenticity = Math.max(
        adjusted.authenticity,
        eastAsianMetrics.honorific_appropriateness * config.cultural_weights.formality
      )
    }

    // 传统现代平衡调整
    if (eastAsianMetrics.tradition_modernity_balance) {
      adjusted.practicality = Math.max(
        adjusted.practicality,
        eastAsianMetrics.tradition_modernity_balance * 0.9
      )
    }

    return adjusted
  }

  // ============================================================================
  // 8维质量评估方法
  // ============================================================================

  /**
   * 评估自然度
   */
  private async assessNaturalness(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.8 // 基础分

    // 语音和谐度加成
    if (morpheme.phonetic_features?.phonetic_harmony) {
      score += morpheme.phonetic_features.phonetic_harmony * 0.2
    }

    // 文化适应性加成
    if (morpheme.cultural_context) {
      const culturalScore = (
        morpheme.cultural_context.traditionality * config.cultural_weights.traditionality +
        morpheme.cultural_context.modernity * config.cultural_weights.modernity
      ) / 2
      score += culturalScore * 0.15
    }

    return Math.min(1.0, score)
  }

  /**
   * 评估流畅度
   */
  private async assessFluency(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.85 // 基础分

    // 音节/音拍复杂度调整
    if (morpheme.phonetic_features) {
      const syllableCount = morpheme.phonetic_features.syllable_count || 
                           morpheme.phonetic_features.mora_count || 2
      
      // 2-3音节最流畅
      if (syllableCount >= 2 && syllableCount <= 3) {
        score += 0.1
      } else if (syllableCount > 4) {
        score -= 0.05 * (syllableCount - 4)
      }
    }

    return Math.max(0.0, Math.min(1.0, score))
  }

  /**
   * 评估真实性
   */
  private async assessAuthenticity(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.9 // 基础分

    // 原生评分加成
    if (morpheme.native_speaker_rating) {
      score = Math.max(score, morpheme.native_speaker_rating)
    }

    // 文化适当性加成
    if (morpheme.cultural_appropriateness) {
      score = Math.max(score, morpheme.cultural_appropriateness)
    }

    return score
  }

  /**
   * 评估美学吸引力
   */
  private async assessAestheticAppeal(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.8 // 基础分

    // 文化美学价值
    if (morpheme.cultural_context?.aesthetic_value) {
      score = Math.max(score, morpheme.cultural_context.aesthetic_value)
    }

    // 语言特定美学调整
    if (config.language === LanguageCode.JA_JP) {
      // 日语重视季节美学
      if (morpheme.cultural_context?.seasonal_association) {
        score += 0.05
      }
    }

    return Math.min(1.0, score)
  }

  /**
   * 评估发音难度
   */
  private async assessPronunciationEase(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.85 // 基础分

    // 基于音韵特征调整
    if (morpheme.phonetic_features) {
      // 语音和谐度越高，发音越容易
      if (morpheme.phonetic_features.phonetic_harmony) {
        score += (morpheme.phonetic_features.phonetic_harmony - 0.8) * 0.5
      }

      // 韩语复辅音降低发音难度
      if (config.language === LanguageCode.KO_KR && 
          morpheme.phonetic_features.consonant_clusters) {
        score -= 0.1
      }
    }

    return Math.max(0.0, Math.min(1.0, score))
  }

  /**
   * 评估记忆度
   */
  private async assessMemorability(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.8 // 基础分

    // 文化关联度提升记忆度
    if (morpheme.cultural_context) {
      const culturalRelevance = (
        morpheme.cultural_context.traditionality * 0.3 +
        (morpheme.cultural_context.cultural_significance ? 0.4 : 0) +
        (morpheme.cultural_context.aesthetic_value || 0) * 0.3
      )
      score += culturalRelevance * 0.15
    }

    // 独特性提升记忆度
    if (morpheme.language_quality_scores?.uniqueness) {
      score += morpheme.language_quality_scores.uniqueness * 0.1
    }

    return Math.min(1.0, score)
  }

  /**
   * 评估独特性
   */
  private async assessUniqueness(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.75 // 基础分

    // 使用频率反向影响独特性
    if (morpheme.usage_frequency) {
      score += (1 - morpheme.usage_frequency) * 0.2
    }

    // 地域特色加成
    if (morpheme.cultural_context?.regionality && 
        morpheme.cultural_context.regionality > 0.7) {
      score += 0.1
    }

    return Math.min(1.0, score)
  }

  /**
   * 评估实用性
   */
  private async assessPracticality(
    morpheme: LanguageSpecificMorpheme,
    config: EastAsianQualityConfig
  ): Promise<number> {
    let score = 0.8 // 基础分

    // 使用频率提升实用性
    if (morpheme.usage_frequency) {
      score += morpheme.usage_frequency * 0.15
    }

    // 流行趋势影响
    if (morpheme.popularity_trend) {
      score += morpheme.popularity_trend * 0.1
    }

    // 现代性影响实用性
    if (morpheme.cultural_context?.modernity) {
      score += morpheme.cultural_context.modernity * 0.1
    }

    return Math.min(1.0, score)
  }

  // ============================================================================
  // 东亚语言特定评估方法
  // ============================================================================

  /**
   * 评估汉字美学
   */
  private assessHanziAesthetics(morpheme: LanguageSpecificMorpheme): number {
    // 检查是否包含汉字
    const hasHanzi = morpheme.alternative_forms?.some(form => 
      /^[\u4E00-\u9FAF]+$/.test(form)
    )

    if (!hasHanzi) return 0.7 // 无汉字的基础分

    // 基于笔画平衡、结构美感等评估
    return 0.85 + Math.random() * 0.1 // 简化实现
  }

  /**
   * 评估假名流畅度
   */
  private assessKanaFluency(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text
    const hiraganaPattern = /^[\u3040-\u309F]+$/
    
    if (hiraganaPattern.test(text)) {
      // 平假名通常更流畅
      return 0.9 + Math.random() * 0.08
    }
    
    return 0.8 + Math.random() * 0.1
  }

  /**
   * 评估韩文和谐度
   */
  private assessHangulHarmony(morpheme: LanguageSpecificMorpheme): number {
    // 基于音韵和谐度评估
    if (morpheme.phonetic_features?.phonetic_harmony) {
      return morpheme.phonetic_features.phonetic_harmony
    }
    
    return 0.85 // 默认和谐度
  }

  /**
   * 评估敬语适当性
   */
  private assessHonorificAppropriateness(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text
    
    // 检测敬语标记
    const japaneseHonorifics = ['さん', 'さま', 'せんせい', 'くん', 'ちゃん']
    const koreanHonorifics = ['님', '씨', '선생']
    
    const hasJapaneseHonorific = japaneseHonorifics.some(h => text.includes(h))
    const hasKoreanHonorific = koreanHonorifics.some(h => text.includes(h))
    
    if (hasJapaneseHonorific || hasKoreanHonorific) {
      return 0.9 + (morpheme.cultural_context?.formality || 0) * 0.1
    }
    
    return 0.75 // 无敬语标记的基础分
  }

  /**
   * 评估季节文化关联度
   */
  private assessSeasonalCulturalRelevance(morpheme: LanguageSpecificMorpheme): number {
    if (morpheme.cultural_context?.seasonal_association) {
      return 0.9 // 有季节关联的高分
    }
    
    return 0.6 // 无季节关联的基础分
  }

  /**
   * 评估传统现代平衡度
   */
  private assessTraditionModernityBalance(morpheme: LanguageSpecificMorpheme): number {
    if (!morpheme.cultural_context) return 0.7

    const traditionality = morpheme.cultural_context.traditionality
    const modernity = morpheme.cultural_context.modernity

    // 理想的平衡点
    const balance = 1 - Math.abs(traditionality - modernity)
    const average = (traditionality + modernity) / 2

    return balance * 0.3 + average * 0.7
  }

  /**
   * 获取语言配置
   */
  getLanguageConfig(language: LanguageCode): EastAsianQualityConfig | undefined {
    return this.configs.get(language)
  }

  /**
   * 检查是否支持该东亚语言
   */
  supportsLanguage(language: LanguageCode): boolean {
    return this.configs.has(language)
  }
}
