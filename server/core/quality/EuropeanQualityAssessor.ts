/**
 * 欧洲语言质量评估器
 * 
 * 专门为欧洲语言（西班牙语、法语、德语）设计的质量评估系统
 * 扩展基础8维质量评估框架，增加欧洲语言特有的评估维度
 * 
 * @fileoverview 欧洲语言质量评估器实现
 * @version 3.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */

import { LanguageCode } from '../../types/multilingual'
import type {
  LanguageSpecificMorpheme,
  QualityScores
} from '../../types/multilingual'

/**
 * 欧洲语言特有质量指标
 */
export interface EuropeanQualityMetrics {
  /** 语音和谐度 [0-1] */
  phonetic_harmony: number
  /** 形态学复杂度适宜性 [0-1] */
  morphological_appropriateness: number
  /** 文化优雅度 [0-1] */
  cultural_elegance: number
  /** 语言纯正度 [0-1] */
  linguistic_authenticity: number
  /** 国际化适应性 [0-1] */
  international_adaptability: number
  /** 发音友好度 [0-1] */
  pronunciation_friendliness: number
}

/**
 * 欧洲语言质量配置
 */
export interface EuropeanQualityConfig {
  /** 语言代码 */
  language: LanguageCode
  /** 文化权重配置 */
  cultural_weights: {
    formality: number
    elegance: number
    traditionality: number
    internationality: number
  }
  /** 语言学权重配置 */
  linguistic_weights: {
    phonetic_beauty: number
    morphological_richness: number
    semantic_precision: number
    cultural_appropriateness: number
  }
  /** 质量阈值配置 */
  quality_thresholds: {
    min_acceptable: number
    good_quality: number
    excellent_quality: number
  }
}

/**
 * 欧洲语言质量评估器类
 */
export class EuropeanQualityAssessor {
  private configs: Map<LanguageCode, EuropeanQualityConfig>

  constructor() {
    this.configs = new Map()
    this.initializeConfigs()
  }

  /**
   * 检查是否支持该语言
   */
  supportsLanguage(language: LanguageCode): boolean {
    return this.configs.has(language)
  }

  /**
   * 评估语素质量 - 支持任意morpheme对象
   *
   * @param morpheme 语素对象
   * @param language 语言代码
   * @returns 质量评估结果
   */
  assessQuality(morpheme: any, language: LanguageCode): EuropeanQualityMetrics {
    if (!this.supportsLanguage(language)) {
      throw new Error(`Unsupported European language: ${language}`)
    }

    const config = this.configs.get(language)!
    return this.calculateEuropeanMetrics(morpheme, config)
  }

  /**
   * 评估欧洲语言语素质量 - 完整版本
   */
  assessQualityComplete(
    morpheme: LanguageSpecificMorpheme,
    language: LanguageCode
  ): QualityScores & { european_metrics: EuropeanQualityMetrics } {
    const config = this.configs.get(language)
    if (!config) {
      throw new Error(`Unsupported European language: ${language}`)
    }

    // 计算基础8维质量评分
    const baseScores = this.calculateBaseQualityScores(morpheme)
    
    // 计算欧洲语言特有指标
    const europeanMetrics = this.calculateEuropeanMetrics(morpheme, config)
    
    // 基于欧洲语言特性调整分数
    const adjustedScores = this.adjustScoresForEuropeanFeatures(
      baseScores,
      europeanMetrics,
      config
    )

    return {
      ...adjustedScores,
      european_metrics: europeanMetrics
    }
  }

  /**
   * 计算基础8维质量评分
   */
  private calculateBaseQualityScores(morpheme: LanguageSpecificMorpheme): QualityScores {
    return {
      naturalness: this.calculateNaturalness(morpheme),
      fluency: this.calculateFluency(morpheme),
      authenticity: this.calculateAuthenticity(morpheme),
      aesthetic_appeal: this.calculateAestheticAppeal(morpheme),
      pronunciation_ease: this.calculatePronunciationEase(morpheme),
      memorability: this.calculateMemorability(morpheme),
      uniqueness: this.calculateUniqueness(morpheme),
      practicality: this.calculatePracticality(morpheme)
    }
  }

  /**
   * 计算欧洲语言特有指标
   */
  private calculateEuropeanMetrics(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): EuropeanQualityMetrics {
    return {
      phonetic_harmony: this.calculatePhoneticHarmony(morpheme, config),
      morphological_appropriateness: this.calculateMorphologicalAppropriateness(morpheme, config),
      cultural_elegance: this.calculateCulturalElegance(morpheme, config),
      linguistic_authenticity: this.calculateLinguisticAuthenticity(morpheme, config),
      international_adaptability: this.calculateInternationalAdaptability(morpheme, config),
      pronunciation_friendliness: this.calculatePronunciationFriendliness(morpheme, config)
    }
  }

  /**
   * 基于欧洲语言特性调整分数
   */
  private adjustScoresForEuropeanFeatures(
    baseScores: QualityScores,
    europeanMetrics: EuropeanQualityMetrics,
    config: EuropeanQualityConfig
  ): QualityScores {
    const culturalWeight = 0.3
    const linguisticWeight = 0.7

    return {
      naturalness: this.adjustScore(
        baseScores.naturalness,
        europeanMetrics.linguistic_authenticity,
        linguisticWeight
      ),
      fluency: this.adjustScore(
        baseScores.fluency,
        europeanMetrics.phonetic_harmony,
        linguisticWeight
      ),
      authenticity: this.adjustScore(
        baseScores.authenticity,
        europeanMetrics.cultural_elegance,
        culturalWeight
      ),
      aesthetic_appeal: this.adjustScore(
        baseScores.aesthetic_appeal,
        (europeanMetrics.phonetic_harmony + europeanMetrics.cultural_elegance) / 2,
        0.5
      ),
      pronunciation_ease: this.adjustScore(
        baseScores.pronunciation_ease,
        europeanMetrics.pronunciation_friendliness,
        linguisticWeight
      ),
      memorability: this.adjustScore(
        baseScores.memorability,
        europeanMetrics.morphological_appropriateness,
        linguisticWeight
      ),
      uniqueness: this.adjustScore(
        baseScores.uniqueness,
        europeanMetrics.linguistic_authenticity,
        culturalWeight
      ),
      practicality: this.adjustScore(
        baseScores.practicality,
        europeanMetrics.international_adaptability,
        0.4
      )
    }
  }

  /**
   * 初始化欧洲语言配置
   */
  private initializeConfigs(): void {
    // 西班牙语配置
    this.configs.set(LanguageCode.ES_ES, {
      language: LanguageCode.ES_ES,
      cultural_weights: {
        formality: 0.75,
        elegance: 0.80,
        traditionality: 0.85,
        internationality: 0.90
      },
      linguistic_weights: {
        phonetic_beauty: 0.90,
        morphological_richness: 0.85,
        semantic_precision: 0.80,
        cultural_appropriateness: 0.85
      },
      quality_thresholds: {
        min_acceptable: 0.60,
        good_quality: 0.75,
        excellent_quality: 0.90
      }
    })

    // 法语配置
    this.configs.set(LanguageCode.FR_FR, {
      language: LanguageCode.FR_FR,
      cultural_weights: {
        formality: 0.85,
        elegance: 0.95,
        traditionality: 0.90,
        internationality: 0.85
      },
      linguistic_weights: {
        phonetic_beauty: 0.95,
        morphological_richness: 0.90,
        semantic_precision: 0.85,
        cultural_appropriateness: 0.90
      },
      quality_thresholds: {
        min_acceptable: 0.65,
        good_quality: 0.80,
        excellent_quality: 0.92
      }
    })

    // 德语配置
    this.configs.set(LanguageCode.DE_DE, {
      language: LanguageCode.DE_DE,
      cultural_weights: {
        formality: 0.90,
        elegance: 0.75,
        traditionality: 0.85,
        internationality: 0.80
      },
      linguistic_weights: {
        phonetic_beauty: 0.75,
        morphological_richness: 0.95,
        semantic_precision: 0.95,
        cultural_appropriateness: 0.85
      },
      quality_thresholds: {
        min_acceptable: 0.65,
        good_quality: 0.78,
        excellent_quality: 0.90
      }
    })
  }

  // ============================================================================
  // 基础质量评分计算方法
  // ============================================================================

  private calculateNaturalness(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''

    // 基于词长和常见模式计算自然度
    const lengthScore = this.calculateLengthScore(text)
    const patternScore = this.calculatePatternScore(text)
    const frequencyScore = morpheme.usage_frequency || 0.5

    return (lengthScore + patternScore + frequencyScore) / 3
  }

  private calculateFluency(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''

    // 基于音韵流畅度计算
    const syllableFlow = this.calculateSyllableFlow(text)
    const consonantClusters = this.penalizeConsonantClusters(text)
    const vowelDistribution = this.calculateVowelDistribution(text)

    return Math.max(0, (syllableFlow + vowelDistribution - consonantClusters) / 2)
  }

  private calculateAuthenticity(morpheme: LanguageSpecificMorpheme): number {
    const culturalContext = morpheme.cultural_context
    if (!culturalContext) return 0.5

    // 基于文化语境计算真实性
    const traditionalityScore = culturalContext.traditionality || 0.5
    const formalityScore = culturalContext.formality || 0.5
    const regionalityScore = culturalContext.regionality || 0.5

    return (traditionalityScore + formalityScore + regionalityScore) / 3
  }

  private calculateAestheticAppeal(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''
    const culturalContext = morpheme.cultural_context

    // 基于美学价值计算
    const phoneticBeauty = this.calculatePhoneticBeauty(text)
    const visualAppeal = this.calculateVisualAppeal(text)
    const culturalAesthetics = culturalContext?.aesthetic_value || 0.5

    return (phoneticBeauty + visualAppeal + culturalAesthetics) / 3
  }

  private calculatePronunciationEase(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''
    const phoneticFeatures = morpheme.phonetic_features

    // 基于发音难度计算
    const syllableComplexity = this.calculateSyllableComplexity(text)
    const consonantDifficulty = this.calculateConsonantDifficulty(text)
    const stressPattern = phoneticFeatures?.stress_position ? 0.8 : 0.6

    return Math.max(0, 1 - (syllableComplexity + consonantDifficulty) / 2 + stressPattern * 0.2)
  }

  private calculateMemorability(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''

    // 基于记忆友好度计算
    const lengthScore = this.calculateMemoryLengthScore(text)
    const rhythmScore = this.calculateRhythmScore(text)
    const uniquenessScore = this.calculatePatternUniqueness(text)

    return (lengthScore + rhythmScore + uniquenessScore) / 3
  }

  private calculateUniqueness(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''

    // 基于独特性计算
    const rarityScore = 1 - (morpheme.usage_frequency || 0.5)
    const structuralUniqueness = this.calculateStructuralUniqueness(text)
    const semanticRarity = morpheme.semantic_rarity || 0.5

    return (rarityScore + structuralUniqueness + semanticRarity) / 3
  }

  private calculatePracticality(morpheme: LanguageSpecificMorpheme): number {
    const text = morpheme.text || ''

    // 基于实用性计算
    const usabilityScore = this.calculateUsabilityScore(text)
    const internationalScore = this.calculateInternationalScore(text)
    const accessibilityScore = this.calculateAccessibilityScore(text)

    return (usabilityScore + internationalScore + accessibilityScore) / 3
  }

  // ============================================================================
  // 欧洲语言特有指标计算方法
  // ============================================================================

  private calculatePhoneticHarmony(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const text = morpheme.text || ''
    const phoneticWeight = config.linguistic_weights.phonetic_beauty

    // 计算音韵和谐度
    const vowelHarmony = this.calculateVowelHarmony(text)
    const consonantBalance = this.calculateConsonantBalance(text)
    const rhythmicFlow = this.calculateRhythmicFlow(text)

    return ((vowelHarmony + consonantBalance + rhythmicFlow) / 3) * phoneticWeight
  }

  private calculateMorphologicalAppropriateness(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const morphInfo = morpheme.morphological_info
    const morphWeight = config.linguistic_weights.morphological_richness

    if (!morphInfo) return 0.5 * morphWeight

    // 评估形态学适宜性
    const structureScore = this.evaluateMorphologicalStructure(morphInfo)
    const complexityScore = this.evaluateComplexityAppropriateness(morphInfo)
    const authenticityScore = this.evaluateMorphologicalAuthenticity(morphInfo)

    return ((structureScore + complexityScore + authenticityScore) / 3) * morphWeight
  }

  private calculateCulturalElegance(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const culturalContext = morpheme.cultural_context
    const eleganceWeight = config.cultural_weights.elegance

    if (!culturalContext) return 0.5 * eleganceWeight

    // 计算文化优雅度
    const formalityElegance = this.calculateFormalityElegance(culturalContext)
    const traditionalElegance = this.calculateTraditionalElegance(culturalContext)
    const aestheticElegance = culturalContext.aesthetic_value || 0.5

    return ((formalityElegance + traditionalElegance + aestheticElegance) / 3) * eleganceWeight
  }

  private calculateLinguisticAuthenticity(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const authWeight = config.linguistic_weights.cultural_appropriateness

    // 计算语言纯正度
    const nativeScore = this.calculateNativeScore(morpheme)
    const historicalScore = this.calculateHistoricalScore(morpheme)
    const usageScore = morpheme.usage_frequency || 0.5

    return ((nativeScore + historicalScore + usageScore) / 3) * authWeight
  }

  private calculateInternationalAdaptability(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const text = morpheme.text || ''
    const intlWeight = config.cultural_weights.internationality

    // 计算国际化适应性
    const latinScore = this.calculateLatinCompatibility(text)
    const pronunciationScore = this.calculateInternationalPronunciation(text)
    const recognitionScore = this.calculateInternationalRecognition(text)

    return ((latinScore + pronunciationScore + recognitionScore) / 3) * intlWeight
  }

  private calculatePronunciationFriendliness(
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const text = morpheme.text || ''
    const phoneticFeatures = morpheme.phonetic_features

    // 计算发音友好度
    const simplicityScore = this.calculatePronunciationSimplicity(text)
    const clarityScore = this.calculatePronunciationClarity(text, phoneticFeatures)
    const learnabilityScore = this.calculatePronunciationLearnability(text)

    return (simplicityScore + clarityScore + learnabilityScore) / 3
  }

  // ============================================================================
  // 辅助计算方法
  // ============================================================================

  private adjustScore(baseScore: number, modifier: number, weight: number): number {
    return Math.min(1.0, Math.max(0.0, baseScore + (modifier - 0.5) * weight))
  }

  private calculateLengthScore(text: string): number {
    // 理想长度为4-8个字符
    const length = text.length
    if (length >= 4 && length <= 8) return 1.0
    if (length >= 3 && length <= 10) return 0.8
    if (length >= 2 && length <= 12) return 0.6
    return 0.4
  }

  private calculatePatternScore(text: string): number {
    // 基于常见语言模式评分
    const hasVowels = /[aeiouáéíóúàâäéèêëïîôöùûüäöü]/i.test(text)
    const hasConsonants = /[bcdfghjklmnpqrstvwxyzñç]/i.test(text)
    const balancedPattern = hasVowels && hasConsonants

    return balancedPattern ? 0.9 : 0.5
  }

  private calculateSyllableFlow(text: string): number {
    // 简化的音节流畅度计算
    const vowels = text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []
    const consonants = text.match(/[bcdfghjklmnpqrstvwxyzñç]/gi) || []

    if (vowels.length === 0) return 0.2

    const ratio = consonants.length / vowels.length
    // 理想的辅音/元音比例约为1.2-1.8
    return ratio >= 1.2 && ratio <= 1.8 ? 1.0 : Math.max(0.3, 1 - Math.abs(ratio - 1.5) / 2)
  }

  private penalizeConsonantClusters(text: string): number {
    // 惩罚过多的辅音聚集
    const clusters = text.match(/[bcdfghjklmnpqrstvwxyzñç]{3,}/gi) || []
    return clusters.length * 0.2
  }

  private calculateVowelDistribution(text: string): number {
    // 计算元音分布均匀度
    const vowelPositions: number[] = []
    for (let i = 0; i < text.length; i++) {
      if (/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/i.test(text[i])) {
        vowelPositions.push(i)
      }
    }

    if (vowelPositions.length <= 1) return 0.5

    // 计算元音间距的标准差
    const intervals = vowelPositions.slice(1).map((pos, i) => pos - vowelPositions[i])
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length

    // 标准差越小，分布越均匀
    return Math.max(0.2, 1 - Math.sqrt(variance) / text.length)
  }

  private calculatePhoneticBeauty(text: string): number {
    // 基于音韵美感的简化计算
    const vowelRatio = (text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []).length / text.length
    const rhythmScore = this.calculateRhythmScore(text)
    const euphonyScore = this.calculateEuphonyScore(text)

    return (vowelRatio * 0.4 + rhythmScore * 0.3 + euphonyScore * 0.3)
  }

  private calculateVisualAppeal(text: string): number {
    // 基于视觉美感的简化计算
    const lengthAppeal = this.calculateLengthScore(text)
    const symmetryScore = this.calculateSymmetryScore(text)
    const uniquenessScore = this.calculateVisualUniqueness(text)

    return (lengthAppeal + symmetryScore + uniquenessScore) / 3
  }

  private calculateSyllableComplexity(text: string): number {
    // 计算音节复杂度
    const complexClusters = text.match(/[bcdfghjklmnpqrstvwxyzñç]{2,}/gi) || []
    const diphthongs = text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]{2,}/gi) || []

    return Math.min(1.0, (complexClusters.length * 0.3 + diphthongs.length * 0.2) / text.length)
  }

  private calculateConsonantDifficulty(text: string): number {
    // 计算辅音发音难度
    const difficultSounds = text.match(/[xjñçß]/gi) || []
    const clusters = text.match(/[bcdfghjklmnpqrstvwxyzñç]{3,}/gi) || []

    return Math.min(1.0, (difficultSounds.length * 0.4 + clusters.length * 0.6) / text.length)
  }

  private calculateMemoryLengthScore(text: string): number {
    // 记忆友好的长度评分
    const length = text.length
    if (length >= 3 && length <= 7) return 1.0
    if (length >= 2 && length <= 9) return 0.8
    return Math.max(0.3, 1 - Math.abs(length - 5) / 10)
  }

  private calculateRhythmScore(text: string): number {
    // 计算节奏感
    const syllablePattern = this.extractSyllablePattern(text)
    const regularityScore = this.calculatePatternRegularity(syllablePattern)

    return regularityScore
  }

  private calculatePatternUniqueness(text: string): number {
    // 计算模式独特性
    const repeatedChars = text.match(/(.)\1+/g) || []
    const uniqueChars = new Set(text.toLowerCase()).size

    const repetitionPenalty = repeatedChars.length * 0.2
    const diversityBonus = uniqueChars / text.length

    return Math.max(0.2, diversityBonus - repetitionPenalty)
  }

  private calculateStructuralUniqueness(text: string): number {
    // 计算结构独特性
    const pattern = this.extractStructuralPattern(text)
    const commonPatterns = ['cvcv', 'vcvc', 'ccvc', 'cvcc']

    const isCommon = commonPatterns.some(p => pattern.includes(p))
    return isCommon ? 0.4 : 0.8
  }

  private calculateUsabilityScore(text: string): number {
    // 计算可用性评分
    const lengthScore = this.calculateLengthScore(text)
    const typingScore = this.calculateTypingEase(text)
    const readabilityScore = this.calculateReadabilityScore(text)

    return (lengthScore + typingScore + readabilityScore) / 3
  }

  private calculateInternationalScore(text: string): number {
    // 计算国际化评分
    const latinOnly = /^[a-zA-Záéíóúàâäéèêëïîôöùûüäöüñç]+$/.test(text)
    const commonChars = /^[a-zA-Z]+$/.test(text)

    if (commonChars) return 1.0
    if (latinOnly) return 0.8
    return 0.5
  }

  private calculateAccessibilityScore(text: string): number {
    // 计算可访问性评分
    const pronunciationScore = 1 - this.calculatePronunciationDifficulty(text)
    const visualScore = this.calculateVisualClarity(text)
    const memoryScore = this.calculateMemoryFriendliness(text)

    return (pronunciationScore + visualScore + memoryScore) / 3
  }

  // ============================================================================
  // 更多辅助方法（简化实现）
  // ============================================================================

  private calculateEuphonyScore(text: string): number {
    // 简化的音韵和谐度计算
    const pleasantSounds = text.match(/[aeioulmnr]/gi) || []
    const harshSounds = text.match(/[kgxqz]/gi) || []

    return Math.max(0.2, (pleasantSounds.length - harshSounds.length * 0.5) / text.length)
  }

  private calculateSymmetryScore(text: string): number {
    // 简化的对称性计算
    const reversed = text.split('').reverse().join('')
    let matches = 0

    for (let i = 0; i < Math.min(text.length, 3); i++) {
      if (text[i] === reversed[i]) matches++
    }

    return matches / Math.min(text.length, 3)
  }

  private calculateVisualUniqueness(text: string): number {
    // 视觉独特性
    const specialChars = text.match(/[áéíóúàâäéèêëïîôöùûüäöüñç]/gi) || []
    const uniqueRatio = new Set(text.toLowerCase()).size / text.length

    return Math.min(1.0, uniqueRatio + specialChars.length * 0.1)
  }

  private extractSyllablePattern(text: string): string {
    // 提取音节模式（简化）
    return text.replace(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi, 'V')
               .replace(/[bcdfghjklmnpqrstvwxyzñç]/gi, 'C')
  }

  private calculatePatternRegularity(pattern: string): number {
    // 计算模式规律性
    const chunks = pattern.match(/.{1,2}/g) || []
    const uniqueChunks = new Set(chunks).size

    return uniqueChunks < chunks.length ? 0.8 : 0.5
  }

  private extractStructuralPattern(text: string): string {
    // 提取结构模式
    return this.extractSyllablePattern(text).toLowerCase()
  }

  private calculateTypingEase(text: string): number {
    // 打字便利性（简化）
    const commonKeys = text.match(/[aeioustnrlhd]/gi) || []
    return Math.min(1.0, commonKeys.length / text.length + 0.2)
  }

  private calculateReadabilityScore(text: string): number {
    // 可读性评分
    const lengthScore = this.calculateLengthScore(text)
    const clarityScore = /^[a-zA-Záéíóúàâäéèêëïîôöùûüäöüñç]+$/.test(text) ? 1.0 : 0.7

    return (lengthScore + clarityScore) / 2
  }

  private calculatePronunciationDifficulty(text: string): number {
    // 发音难度
    const difficultCombos = text.match(/[xjqz]|ch|sch|gn|ñ/gi) || []
    const clusters = text.match(/[bcdfghjklmnpqrstvwxyzñç]{3,}/gi) || []

    return Math.min(1.0, (difficultCombos.length + clusters.length * 2) / text.length)
  }

  private calculateVisualClarity(text: string): number {
    // 视觉清晰度
    const confusableChars = text.match(/[il1o0]/gi) || []
    return Math.max(0.3, 1 - confusableChars.length / text.length)
  }

  private calculateMemoryFriendliness(text: string): number {
    // 记忆友好度
    const lengthScore = this.calculateMemoryLengthScore(text)
    const patternScore = this.calculatePatternMemorability(text)

    return (lengthScore + patternScore) / 2
  }

  private calculatePatternMemorability(text: string): number {
    // 模式记忆度
    const repeatingElements = text.match(/(.)\1/g) || []
    const rhythmicPattern = this.hasRhythmicPattern(text)

    return Math.min(1.0, repeatingElements.length * 0.3 + (rhythmicPattern ? 0.4 : 0))
  }

  private hasRhythmicPattern(text: string): boolean {
    // 检查是否有节奏模式
    const pattern = this.extractSyllablePattern(text)
    return /^(CV)+$|^(VC)+$|^(CVC)+$/.test(pattern)
  }

  // 欧洲语言特有指标的简化实现
  private calculateVowelHarmony(text: string): number {
    const vowels = text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []
    const frontVowels = vowels.filter(v => /[eéèêëiíîï]/i.test(v)).length
    const backVowels = vowels.filter(v => /[oóôöuúûü]/i.test(v)).length

    if (vowels.length === 0) return 0.5

    const harmony = Math.abs(frontVowels - backVowels) / vowels.length
    return Math.max(0.3, 1 - harmony)
  }

  private calculateConsonantBalance(text: string): number {
    const consonants = text.match(/[bcdfghjklmnpqrstvwxyzñç]/gi) || []
    const stops = consonants.filter(c => /[pbtdkgqc]/i.test(c)).length
    const fricatives = consonants.filter(c => /[fvszxjh]/i.test(c)).length
    const liquids = consonants.filter(c => /[lr]/i.test(c)).length

    if (consonants.length === 0) return 0.5

    const balance = (stops + fricatives + liquids) / consonants.length
    return Math.min(1.0, balance)
  }

  private calculateRhythmicFlow(text: string): number {
    const pattern = this.extractSyllablePattern(text)
    const alternating = /^(CV)+$|^(VC)+$/.test(pattern)
    const balanced = this.calculateSyllableFlow(text)

    return alternating ? Math.min(1.0, balanced + 0.2) : balanced
  }

  private evaluateMorphologicalStructure(morphInfo: any): number {
    // 简化的形态学结构评估
    const hasRoot = morphInfo.root && morphInfo.root.length > 0
    const hasPrefixes = morphInfo.prefixes && morphInfo.prefixes.length > 0
    const hasSuffixes = morphInfo.suffixes && morphInfo.suffixes.length > 0

    let score = hasRoot ? 0.6 : 0.3
    if (hasPrefixes) score += 0.2
    if (hasSuffixes) score += 0.2

    return Math.min(1.0, score)
  }

  private evaluateComplexityAppropriateness(morphInfo: any): number {
    // 复杂度适宜性评估
    const totalAffixes = (morphInfo.prefixes?.length || 0) + (morphInfo.suffixes?.length || 0)

    // 适中的复杂度最佳
    if (totalAffixes === 1 || totalAffixes === 2) return 1.0
    if (totalAffixes === 0 || totalAffixes === 3) return 0.7
    return 0.4
  }

  private evaluateMorphologicalAuthenticity(morphInfo: any): number {
    // 形态学真实性评估
    const hasInflection = morphInfo.inflection_info && Object.keys(morphInfo.inflection_info).length > 0
    const hasDerivation = morphInfo.derivation_info && Object.keys(morphInfo.derivation_info).length > 0

    return hasInflection || hasDerivation ? 0.8 : 0.5
  }

  private calculateFormalityElegance(culturalContext: any): number {
    return Math.min(1.0, (culturalContext.formality || 0.5) * 1.2)
  }

  private calculateTraditionalElegance(culturalContext: any): number {
    return Math.min(1.0, (culturalContext.traditionality || 0.5) * 1.1)
  }

  private calculateNativeScore(morpheme: LanguageSpecificMorpheme): number {
    // 本土化程度评分
    const culturalContext = morpheme.cultural_context
    return culturalContext?.regionality || 0.5
  }

  private calculateHistoricalScore(morpheme: LanguageSpecificMorpheme): number {
    // 历史传承评分
    const culturalContext = morpheme.cultural_context
    return culturalContext?.traditionality || 0.5
  }

  private calculateLatinCompatibility(text: string): number {
    // 拉丁字母兼容性
    return /^[a-zA-Záéíóúàâäéèêëïîôöùûüäöüñç]+$/.test(text) ? 1.0 : 0.3
  }

  private calculateInternationalPronunciation(text: string): number {
    // 国际发音友好度
    const difficulty = this.calculatePronunciationDifficulty(text)
    return 1 - difficulty
  }

  private calculateInternationalRecognition(text: string): number {
    // 国际认知度
    const commonChars = /^[a-zA-Z]+$/.test(text)
    const length = text.length

    return commonChars && length >= 3 && length <= 8 ? 0.9 : 0.6
  }

  private calculatePronunciationSimplicity(text: string): number {
    return 1 - this.calculateSyllableComplexity(text)
  }

  private calculatePronunciationClarity(text: string, phoneticFeatures: any): number {
    const clarityScore = this.calculateVisualClarity(text)
    const stressClarity = phoneticFeatures?.stress_position ? 0.8 : 0.6

    return (clarityScore + stressClarity) / 2
  }

  private calculatePronunciationLearnability(text: string): number {
    const lengthScore = this.calculateMemoryLengthScore(text)
    const patternScore = this.calculatePatternScore(text)

    return (lengthScore + patternScore) / 2
  }
}
