/**
 * 核心生成引擎
 *
 * 负责协调各个组件完成用户名生成，支持新的数据加载机制、
 * 多维度索引查询和高效的采样算法。
 *
 * @fileoverview 核心生成引擎
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */

import {
  type GeneratedUsername,
  type GenerationContext,
  type QualityScore,
  type MorphemeComponent,
  type Morpheme,
  type EngineStats,
  MorphemeCategory
} from '../../types/core'
import { MorphemeRepository } from '../repositories/MorphemeRepository'
import { DataLoader } from '../data/DataLoader'
import { DataValidator } from '../data/DataValidator'
import { LanguageManager } from '../multilingual/LanguageManager'
import { SemanticAligner } from '../multilingual/SemanticAligner'
import { LanguageCode } from '../../types/multilingual'

/**
 * 核心生成引擎类
 *
 * 提供高效的用户名生成功能，支持多种创意模式、智能质量评估和多语种生成
 */
export class CoreGenerationEngine {
  private morphemeRepo: MorphemeRepository
  private languageManager: LanguageManager
  private semanticAligner: SemanticAligner
  private isInitialized = false
  private generationCount = 0
  private totalGenerationTime = 0

  /**
   * 构造函数
   *
   * 初始化生成引擎和依赖组件，支持多语种功能
   */
  constructor() {
    // 使用新的数据加载和验证机制
    const dataLoader = new DataLoader({
      enableHotReload: true,
      enableValidation: true,
      enableCache: true
    })

    const dataValidator = new DataValidator({
      strict_mode: true,
      skip_warnings: false
    })

    this.morphemeRepo = new MorphemeRepository(dataLoader, dataValidator)

    // 初始化多语种组件
    this.languageManager = new LanguageManager({
      defaultLanguage: LanguageCode.ZH_CN,
      enabledLanguages: [LanguageCode.ZH_CN], // 暂时只启用中文
      enableCache: true,
      cacheTTL: 3600
    })

    this.semanticAligner = new SemanticAligner({
      semanticSimilarityThreshold: 0.75,
      culturalFitWeight: 0.3,
      semanticWeight: 0.5,
      qualityWeight: 0.2
    })
  }

  /**
   * 初始化生成引擎
   *
   * 加载语素数据、构建索引、验证数据完整性，初始化多语种组件
   *
   * @returns Promise<void>
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 核心生成引擎已初始化，跳过重复初始化')
      return
    }

    const startTime = Date.now()
    console.log('🚀 开始初始化多语种核心生成引擎...')

    try {
      // 1. 初始化语素仓库（传统中文数据）
      await this.morphemeRepo.initialize()

      // 验证仓库状态
      if (!this.morphemeRepo.isReady()) {
        throw new Error('语素仓库初始化失败')
      }

      // 2. 初始化多语种语言管理器
      await this.languageManager.initialize()

      // 验证语言管理器状态
      if (!this.languageManager.isReady()) {
        throw new Error('语言管理器初始化失败')
      }

      // 获取初始化统计信息
      const stats = this.morphemeRepo.getStats()
      const supportedLanguages = this.languageManager.getSupportedLanguages()

      this.isInitialized = true
      const initTime = Date.now() - startTime

      console.log(`✅ 多语种核心生成引擎初始化完成: 耗时${initTime}ms`)
      console.log(`📊 传统语素: ${stats.total}个, 分布: ${Object.entries(stats.byCategory).map(([k, v]) => `${k}:${v}`).join(', ')}`)
      console.log(`🌍 支持语言: ${supportedLanguages.join(', ')}`)

      // 输出各语言统计
      for (const lang of supportedLanguages) {
        const langStats = this.languageManager.getLanguageStats(lang)
        console.log(`   ${lang}: ${langStats.morphemeCount}个语素, ${langStats.conceptCount}个概念`)
      }

    } catch (error) {
      console.error('❌ 多语种核心生成引擎初始化失败:', error)
      throw new Error(`多语种核心生成引擎初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 重新加载数据
   *
   * 用于热重载场景，支持多语种数据重载
   *
   * @returns Promise<void>
   */
  async reload(): Promise<void> {
    console.log('🔄 重新加载多语种核心生成引擎...')
    this.isInitialized = false
    await this.morphemeRepo.reload()

    // 重新加载所有支持的语言
    const supportedLanguages = this.languageManager.getSupportedLanguages()
    for (const language of supportedLanguages) {
      await this.languageManager.reloadLanguage(language)
    }

    await this.initialize()
  }

  /**
   * 生成用户名 (传统中文模式)
   *
   * 支持1-10个用户名的批量生成，专注单用户场景
   *
   * @param context 生成上下文
   * @param count 生成数量 (1-10)
   * @returns 生成的用户名数组
   */
  async generate(context: GenerationContext, count: number = 5): Promise<GeneratedUsername[]> {
    return this.generateMultilingual(context, count, LanguageCode.ZH_CN)
  }

  /**
   * 多语种用户名生成
   *
   * 支持多语种的核心生成方法，基于v3.0多语种架构
   *
   * @param context 生成上下文
   * @param count 生成数量 (1-10)
   * @param language 目标语言
   * @returns Promise<GeneratedUsername[]>
   */
  async generateMultilingual(
    context: GenerationContext,
    count: number = 5,
    language: LanguageCode = LanguageCode.ZH_CN
  ): Promise<GeneratedUsername[]> {
    // 验证参数
    if (count < 1 || count > 10) {
      throw new Error('生成数量必须在1-10之间')
    }

    if (!this.isInitialized) {
      await this.initialize()
    }

    if (!this.languageManager.isLanguageSupported(language)) {
      throw new Error(`不支持的语言: ${language}`)
    }

    const startTime = Date.now()
    const results: GeneratedUsername[] = []
    const maxRetries = count * 3 // 最大重试次数

    console.log(`🌍 开始生成${count}个${language}用户名...`)

    try {
      let attempts = 0
      while (results.length < count && attempts < maxRetries) {
        const username = language === LanguageCode.ZH_CN
          ? await this.generateSingle(context) // 使用传统中文生成
          : await this.generateMultilingualSingle(context, language) // 使用多语种生成

        if (username) {
          // 检查重复
          const isDuplicate = results.some(existing => existing.text === username.text)
          if (!isDuplicate) {
            results.push(username)
          }
        }
        attempts++
      }

      const generationTime = Date.now() - startTime
      this.generationCount += results.length
      this.totalGenerationTime += generationTime

      console.log(`✅ ${language}生成完成: ${results.length}/${count}个用户名, 耗时${generationTime}ms`)

      if (results.length < count) {
        console.warn(`⚠️ 仅生成了${results.length}个用户名，未达到目标${count}个`)
      }

      return results
    } catch (error) {
      console.error(`❌ ${language}用户名生成失败:`, error)
      throw new Error(`${language}用户名生成失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 生成单个用户名 (传统中文模式)
   */
  private async generateSingle(context: GenerationContext): Promise<GeneratedUsername | null> {
    const generationStart = Date.now()

    try {
      // 简化的生成逻辑 - MVP版本
      const pattern = this.selectPattern(context)
      const components = await this.selectMorphemes(pattern, context)

      if (components.length === 0) {
        return null
      }

      const text = this.combineComponents(components)
      const qualityScore = this.evaluateQuality(text, components, context)

      // 检查质量阈值
      if (qualityScore.overall < context.quality_threshold) {
        return null
      }

      return {
        text,
        pattern: pattern.name,
        quality_score: qualityScore,
        explanation: this.generateExplanation(text, components, pattern),
        components,
        metadata: {
          cultural_fit: this.calculateCulturalFitScore(text, components, context),
          creativity: this.calculateCreativityScore(text, components, context),
          memorability: this.calculateMemorabilityScore(text, components, context),
          uniqueness: this.calculateUniquenessScore(text, components, context),
          generation_time: Date.now() - generationStart
        }
      }
    } catch (error) {
      console.error('Failed to generate single username:', error)
      return null
    }
  }

  /**
   * 生成单个多语种用户名
   *
   * 基于v3.0多语种架构的单个用户名生成
   */
  private async generateMultilingualSingle(
    context: GenerationContext,
    language: LanguageCode
  ): Promise<GeneratedUsername | null> {
    const generationStart = Date.now()

    try {
      // 1. 获取目标语言的语素
      const languageMorphemes = this.languageManager.getMorphemesByLanguage(language)
      if (languageMorphemes.length === 0) {
        console.warn(`⚠️ ${language}语言没有可用语素`)
        return null
      }

      // 2. 选择创意模式（多语种适配）
      const pattern = this.selectMultilingualPattern(context, language)

      // 3. 基于语义对齐选择语素
      const components = await this.selectMultilingualMorphemes(pattern, context, language)

      if (components.length === 0) {
        return null
      }

      // 4. 组合语素
      const text = this.combineMultilingualComponents(components, language)

      // 5. 多语种质量评估
      const qualityScore = this.evaluateMultilingualQuality(text, components, context, language)

      // 检查质量阈值
      if (qualityScore.overall < context.quality_threshold) {
        return null
      }

      return {
        text,
        pattern: pattern.name,
        quality_score: qualityScore,
        explanation: this.generateMultilingualExplanation(text, components, pattern, language),
        components: this.adaptComponentsToTraditionalFormat(components),
        metadata: {
          cultural_fit: this.calculateMultilingualCulturalFit(text, components, context, language),
          creativity: this.calculateMultilingualCreativity(text, components, context, language),
          memorability: this.calculateMultilingualMemorability(text, components, context, language),
          uniqueness: this.calculateMultilingualUniqueness(text, components, context, language),
          generation_time: Date.now() - generationStart
        }
      }
    } catch (error) {
      console.error(`Failed to generate single ${language} username:`, error)
      return null
    }
  }

  /**
   * 选择创意模式 (传统中文)
   */
  private selectPattern(context: GenerationContext): { name: string; template: string } {
    // MVP版本使用简化的模式选择
    const patterns = [
      { name: '形容词+名词', template: '{adjective}{noun}' },
      { name: '职业特色', template: '{characteristic}的{profession}' },
      { name: '可爱萌系', template: '小{adjective}' }
    ]

    // 简单随机选择
    return patterns[Math.floor(Math.random() * patterns.length)]
  }

  /**
   * 选择多语种创意模式
   */
  private selectMultilingualPattern(
    context: GenerationContext,
    language: LanguageCode
  ): { name: string; template: string } {
    // 根据语言选择适合的模式
    const languagePatterns: Record<LanguageCode, Array<{ name: string; template: string }>> = {
      [LanguageCode.ZH_CN]: [
        { name: '形容词+名词', template: '{adjective}{noun}' },
        { name: '职业特色', template: '{characteristic}的{profession}' },
        { name: '可爱萌系', template: '小{adjective}' }
      ],
      [LanguageCode.EN_US]: [
        { name: 'Adjective+Noun', template: '{adjective}{noun}' },
        { name: 'Professional Style', template: '{profession}{characteristic}' },
        { name: 'Creative Blend', template: '{characteristic}{emotion}' }
      ],
      [LanguageCode.JA_JP]: [
        { name: '形容詞+名詞', template: '{adjective}{noun}' },
        { name: '職業系', template: '{profession}{characteristic}' }
      ],
      [LanguageCode.KO_KR]: [
        { name: '형용사+명사', template: '{adjective}{noun}' },
        { name: '직업형', template: '{profession}{characteristic}' }
      ],
      [LanguageCode.ES_ES]: [
        { name: 'Adjetivo+Sustantivo', template: '{adjective}{noun}' },
        { name: 'Estilo Profesional', template: '{profession}{characteristic}' }
      ],
      [LanguageCode.FR_FR]: [
        { name: 'Adjectif+Nom', template: '{adjective}{noun}' },
        { name: 'Style Professionnel', template: '{profession}{characteristic}' }
      ],
      [LanguageCode.DE_DE]: [
        { name: 'Adjektiv+Substantiv', template: '{adjective}{noun}' },
        { name: 'Berufsstil', template: '{profession}{characteristic}' }
      ],
      [LanguageCode.AR_SA]: [
        { name: 'صفة+اسم', template: '{adjective}{noun}' },
        { name: 'نمط مهني', template: '{profession}{characteristic}' }
      ]
    }

    const patterns = languagePatterns[language] || languagePatterns[LanguageCode.EN_US]
    return patterns[Math.floor(Math.random() * patterns.length)]
  }

  /**
   * 选择语素
   *
   * 使用新的采样算法和多维度索引进行智能语素选择
   *
   * @private
   * @param pattern 创意模式
   * @param context 生成上下文
   * @returns 语素组件数组
   */
  private async selectMorphemes(pattern: { name: string; template: string }, context: GenerationContext): Promise<MorphemeComponent[]> {
    const components: MorphemeComponent[] = []

    try {
      // 使用新的高效采样算法
      if (pattern.template.includes('{adjective}')) {
        // 优先从情感类语素中采样
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.EMOTIONS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: 0,
            role: 'modifier',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      if (pattern.template.includes('{noun}') || pattern.template.includes('{profession}')) {
        // 从职业类语素中采样
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.PROFESSIONS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: 1,
            role: 'root',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      if (pattern.template.includes('{characteristic}')) {
        // 从特征类语素中采样
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.CHARACTERISTICS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: 0,
            role: 'modifier',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      // 如果需要更多语素，可以从其他类别补充
      if (components.length < 2 && pattern.template.includes('{object}')) {
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.OBJECTS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: components.length,
            role: 'complement',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      // 根据文化偏好进行二次筛选
      if (context.cultural_preference && context.cultural_preference !== 'neutral') {
        return this.filterByCulturalPreference(components, context.cultural_preference)
      }

      return components
    } catch (error) {
      console.error('❌ 语素选择失败:', error)
      return []
    }
  }

  /**
   * 计算语素贡献分数
   *
   * @private
   * @param morpheme 语素
   * @param context 生成上下文
   * @returns 贡献分数
   */
  private calculateContributionScore(morpheme: Morpheme, context: GenerationContext): number {
    let score = morpheme.quality_score * 0.6 + morpheme.usage_frequency * 0.4

    // 文化适配加成
    if (context.cultural_preference === morpheme.cultural_context) {
      score *= 1.2
    } else if (morpheme.cultural_context === 'neutral') {
      score *= 1.1
    }

    // 质量阈值加成
    if (morpheme.quality_score >= context.quality_threshold) {
      score *= 1.1
    }

    return Math.min(score, 1.0)
  }

  /**
   * 根据文化偏好筛选语素
   *
   * @private
   * @param components 语素组件数组
   * @param culturalPreference 文化偏好
   * @returns 筛选后的语素组件数组
   */
  private filterByCulturalPreference(components: MorphemeComponent[], culturalPreference: string): MorphemeComponent[] {
    // 优先保留匹配文化偏好的语素
    const preferred = components.filter(c =>
      c.morpheme.cultural_context === culturalPreference ||
      c.morpheme.cultural_context === 'neutral'
    )

    // 如果筛选后语素不足，保留原始结果
    return preferred.length >= 1 ? preferred : components
  }

  /**
   * 选择多语种语素
   *
   * 基于语义对齐和概念映射选择目标语言的语素
   */
  private async selectMultilingualMorphemes(
    pattern: { name: string; template: string },
    context: GenerationContext,
    language: LanguageCode
  ): Promise<import('../../types/multilingual').LanguageSpecificMorpheme[]> {
    const selectedMorphemes: import('../../types/multilingual').LanguageSpecificMorpheme[] = []

    try {
      // 获取目标语言的所有语素
      const languageMorphemes = this.languageManager.getMorphemesByLanguage(language)

      // 根据模式模板选择语素
      if (pattern.template.includes('{adjective}') || pattern.template.includes('{emotion}')) {
        // 选择情感类语素
        const emotionMorphemes = languageMorphemes.filter(m =>
          m.morphological_info.pos_tag === 'ADJ' ||
          m.cultural_context.cultural_tags.some(tag =>
            ['emotion', 'feeling', 'mood', 'sentiment'].includes(tag.toLowerCase())
          )
        )

        if (emotionMorphemes.length > 0) {
          const selected = this.selectBestMorphemeByQuality(emotionMorphemes, context)
          if (selected) selectedMorphemes.push(selected)
        }
      }

      if (pattern.template.includes('{noun}') || pattern.template.includes('{profession}')) {
        // 选择职业类语素
        const professionMorphemes = languageMorphemes.filter(m =>
          m.morphological_info.pos_tag === 'NOUN' ||
          m.cultural_context.cultural_tags.some(tag =>
            ['profession', 'job', 'career', 'work'].includes(tag.toLowerCase())
          )
        )

        if (professionMorphemes.length > 0) {
          const selected = this.selectBestMorphemeByQuality(professionMorphemes, context)
          if (selected) selectedMorphemes.push(selected)
        }
      }

      if (pattern.template.includes('{characteristic}')) {
        // 选择特征类语素
        const characteristicMorphemes = languageMorphemes.filter(m =>
          m.morphological_info.pos_tag === 'ADJ' ||
          m.cultural_context.cultural_tags.some(tag =>
            ['characteristic', 'trait', 'quality', 'attribute'].includes(tag.toLowerCase())
          )
        )

        if (characteristicMorphemes.length > 0) {
          const selected = this.selectBestMorphemeByQuality(characteristicMorphemes, context)
          if (selected) selectedMorphemes.push(selected)
        }
      }

      // 如果没有选到足够的语素，随机补充
      if (selectedMorphemes.length === 0) {
        const randomMorpheme = languageMorphemes[Math.floor(Math.random() * languageMorphemes.length)]
        selectedMorphemes.push(randomMorpheme)
      }

      return selectedMorphemes
    } catch (error) {
      console.error(`❌ 选择${language}语素失败:`, error)
      return []
    }
  }

  /**
   * 根据质量选择最佳语素
   */
  private selectBestMorphemeByQuality(
    morphemes: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext
  ): import('../../types/multilingual').LanguageSpecificMorpheme | null {
    if (morphemes.length === 0) return null

    // 根据质量评分和文化适配度排序
    const scored = morphemes.map(morpheme => ({
      morpheme,
      score: this.calculateMultilingualMorphemeScore(morpheme, context)
    }))

    scored.sort((a, b) => b.score - a.score)
    return scored[0].morpheme
  }

  /**
   * 计算多语种语素评分
   */
  private calculateMultilingualMorphemeScore(
    morpheme: import('../../types/multilingual').LanguageSpecificMorpheme,
    context: GenerationContext
  ): number {
    let score = 0

    // 基础质量评分 (40%)
    const qualityScores = morpheme.language_quality_scores
    const avgQuality = (
      qualityScores.naturalness +
      qualityScores.fluency +
      qualityScores.authenticity +
      qualityScores.aesthetic_appeal +
      qualityScores.pronunciation_ease +
      qualityScores.memorability +
      qualityScores.uniqueness +
      qualityScores.practicality
    ) / 8
    score += avgQuality * 0.4

    // 文化适配度 (30%)
    score += morpheme.cultural_appropriateness * 0.3

    // 使用频率 (20%)
    score += morpheme.usage_frequency * 0.2

    // 母语者评分 (10%)
    score += morpheme.native_speaker_rating * 0.1

    return Math.min(score, 1.0)
  }

  /**
   * 组合语素 (传统中文)
   */
  private combineComponents(components: MorphemeComponent[]): string {
    // 按位置排序
    const sorted = components.sort((a, b) => a.position - b.position)

    // 简单拼接
    return sorted.map(c => c.morpheme.text).join('')
  }

  /**
   * 组合多语种语素
   */
  private combineMultilingualComponents(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    language: LanguageCode
  ): string {
    if (components.length === 0) return ''

    // 根据语言特性进行组合
    switch (language) {
      case LanguageCode.EN_US:
        // 英文：首字母大写，驼峰命名
        return components.map((morpheme, index) => {
          const text = morpheme.text
          return index === 0
            ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
            : text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
        }).join('')

      case LanguageCode.JA_JP:
      case LanguageCode.KO_KR:
        // 日韩文：直接拼接
        return components.map(m => m.text).join('')

      case LanguageCode.ES_ES:
      case LanguageCode.FR_FR:
      case LanguageCode.DE_DE:
        // 欧洲语言：首字母大写，驼峰命名
        return components.map((morpheme, index) => {
          const text = morpheme.text
          return index === 0
            ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
            : text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
        }).join('')

      case LanguageCode.AR_SA:
        // 阿拉伯文：从右到左，但用户名通常从左到右显示
        return components.map(m => m.text).join('')

      default:
        // 默认：简单拼接
        return components.map(m => m.text).join('')
    }
  }

  /**
   * 适配组件到传统格式
   *
   * 将多语种语素适配为传统MorphemeComponent格式以保持兼容性
   */
  private adaptComponentsToTraditionalFormat(
    multilingualMorphemes: import('../../types/multilingual').LanguageSpecificMorpheme[]
  ): MorphemeComponent[] {
    return multilingualMorphemes.map((morpheme, index) => ({
      morpheme: {
        id: morpheme.morpheme_id,
        text: morpheme.text,
        category: this.mapPOSToCategory(morpheme.morphological_info.pos_tag),
        subcategory: morpheme.morphological_info.morphological_type,
        cultural_context: this.mapCulturalContext(morpheme.cultural_context),
        usage_frequency: morpheme.usage_frequency,
        quality_score: morpheme.native_speaker_rating,
        semantic_vector: this.extractSemanticVector(morpheme),
        tags: morpheme.cultural_context.cultural_tags,
        language_properties: {
          syllable_count: morpheme.phonetic_features.syllable_count,
          character_count: morpheme.text.length,
          phonetic_features: [morpheme.phonetic_features.ipa_transcription],
          morphological_type: morpheme.morphological_info.morphological_type,
          pronunciation: morpheme.phonetic_features.ipa_transcription
        },
        quality_metrics: {
          naturalness: morpheme.language_quality_scores.naturalness,
          frequency: morpheme.usage_frequency,
          acceptability: morpheme.cultural_appropriateness,
          aesthetic_appeal: morpheme.language_quality_scores.aesthetic_appeal
        },
        created_at: Date.now(),
        source: morpheme.source,
        version: morpheme.version as any
      },
      position: index,
      role: index === 0 ? 'root' : 'modifier',
      contribution_score: morpheme.native_speaker_rating
    }))
  }

  /**
   * 映射词性到类别
   */
  private mapPOSToCategory(posTag: string): MorphemeCategory {
    const mapping: Record<string, MorphemeCategory> = {
      'ADJ': MorphemeCategory.CHARACTERISTICS,
      'NOUN': MorphemeCategory.PROFESSIONS,
      'VERB': MorphemeCategory.ACTIONS,
      'ADV': MorphemeCategory.CHARACTERISTICS
    }
    return mapping[posTag] || MorphemeCategory.CONCEPTS
  }

  /**
   * 映射文化语境
   */
  private mapCulturalContext(
    culturalContext: import('../../types/multilingual').LanguageSpecificMorpheme['cultural_context']
  ): import('../../types/core').CulturalContext {
    // 基于传统性和现代性评分映射
    if (culturalContext.traditionality > 0.7) {
      return 'ancient' as any
    } else if (culturalContext.modernity > 0.7) {
      return 'modern' as any
    } else {
      return 'neutral' as any
    }
  }

  /**
   * 提取语义向量
   */
  private extractSemanticVector(
    morpheme: import('../../types/multilingual').LanguageSpecificMorpheme
  ): number[] {
    // 基于语素的各种特征构建20维语义向量
    const vector: number[] = new Array(20).fill(0)

    const qualityScores = morpheme.language_quality_scores
    vector[0] = qualityScores.naturalness
    vector[1] = qualityScores.fluency
    vector[2] = qualityScores.authenticity
    vector[3] = qualityScores.aesthetic_appeal
    vector[4] = qualityScores.pronunciation_ease
    vector[5] = qualityScores.memorability
    vector[6] = qualityScores.uniqueness
    vector[7] = qualityScores.practicality

    const cultural = morpheme.cultural_context
    vector[8] = cultural.traditionality
    vector[9] = cultural.modernity
    vector[10] = cultural.formality
    vector[11] = cultural.regionality
    vector[12] = cultural.religious_sensitivity

    vector[13] = morpheme.phonetic_features.phonetic_harmony
    vector[14] = morpheme.phonetic_features.syllable_count / 5
    vector[15] = morpheme.usage_frequency
    vector[16] = morpheme.native_speaker_rating
    vector[17] = morpheme.cultural_appropriateness
    vector[18] = morpheme.popularity_trend
    vector[19] = this.getLanguageSpecificityScore(morpheme.language)

    return vector
  }

  /**
   * 计算语言特异性评分
   */
  private getLanguageSpecificityScore(language: LanguageCode): number {
    // 基于语言的特异性评分
    const languageScores: Record<LanguageCode, number> = {
      [LanguageCode.ZH_CN]: 0.8, // 中文特异性较高
      [LanguageCode.EN_US]: 0.6, // 英文通用性较强
      [LanguageCode.JA_JP]: 0.9, // 日文特异性很高
      [LanguageCode.KO_KR]: 0.85, // 韩文特异性高
      [LanguageCode.ES_ES]: 0.5, // 西班牙文通用性强
      [LanguageCode.FR_FR]: 0.55, // 法文通用性较强
      [LanguageCode.DE_DE]: 0.65, // 德文特异性中等
      [LanguageCode.AR_SA]: 0.95  // 阿拉伯文特异性最高
    }

    return languageScores[language] || 0.5
  }

  /**
   * 多语种质量评估
   */
  private evaluateMultilingualQuality(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): QualityScore {
    const startTime = Date.now()

    // 基于多语种语素的质量评分计算8维度评分
    const creativity = this.calculateMultilingualCreativity(text, components, context, language)
    const memorability = this.calculateMultilingualMemorability(text, components, context, language)
    const cultural_fit = this.calculateMultilingualCulturalFit(text, components, context, language)
    const uniqueness = this.calculateMultilingualUniqueness(text, components, context, language)
    const pronunciation = this.calculateMultilingualPronunciation(text, components, context, language)
    const semantic_coherence = this.calculateMultilingualSemanticCoherence(text, components, context, language)
    const aesthetic_appeal = this.calculateMultilingualAestheticAppeal(text, components, context, language)
    const practical_usability = this.calculateMultilingualPracticalUsability(text, components, context, language)

    // 计算综合评分
    const weights = {
      creativity: 0.15,
      memorability: 0.20,
      cultural_fit: 0.15,
      uniqueness: 0.12,
      pronunciation: 0.13,
      semantic_coherence: 0.10,
      aesthetic_appeal: 0.08,
      practical_usability: 0.07
    }

    const overall = (
      creativity * weights.creativity +
      memorability * weights.memorability +
      cultural_fit * weights.cultural_fit +
      uniqueness * weights.uniqueness +
      pronunciation * weights.pronunciation +
      semantic_coherence * weights.semantic_coherence +
      aesthetic_appeal * weights.aesthetic_appeal +
      practical_usability * weights.practical_usability
    )

    const confidence = this.calculateMultilingualEvaluationConfidence(text, components, context, language)
    const { issues, suggestions } = this.analyzeMultilingualQualityIssues(text, components, language, {
      creativity, memorability, cultural_fit, uniqueness,
      pronunciation, semantic_coherence, aesthetic_appeal, practical_usability
    })

    return {
      overall: Math.min(Math.max(overall, 0), 1),
      dimensions: {
        creativity,
        memorability,
        cultural_fit,
        uniqueness,
        pronunciation,
        semantic_coherence,
        aesthetic_appeal,
        practical_usability
      },
      confidence,
      evaluation_time: Date.now() - startTime,
      algorithm_version: '3.0.0-multilingual',
      issues,
      suggestions,
      timestamp: Date.now()
    }
  }

  /**
   * 多语种创意性评分
   */
  private calculateMultilingualCreativity(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    let score = 0

    // 1. 语素组合新颖性 (40%)
    const posTypes = new Set(components.map(c => c.morphological_info.pos_tag))
    const diversityScore = posTypes.size / Math.min(components.length, 3)
    score += diversityScore * 0.4

    // 2. 跨文化创新性 (30%)
    const culturalDiversity = this.calculateCulturalDiversity(components)
    score += culturalDiversity * 0.3

    // 3. 语言特色创新 (20%)
    const languageSpecificScore = this.calculateLanguageSpecificCreativity(text, language)
    score += languageSpecificScore * 0.2

    // 4. 长度适配性 (10%)
    const lengthScore = this.calculateLanguageSpecificLengthScore(text, language)
    score += lengthScore * 0.1

    return Math.min(score, 1)
  }

  /**
   * 多语种记忆性评分
   */
  private calculateMultilingualMemorability(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    let score = 0

    // 1. 语言特定长度记忆性 (35%)
    const lengthScore = this.calculateLanguageSpecificMemorabilityLength(text, language)
    score += lengthScore * 0.35

    // 2. 音韵记忆性 (25%)
    const phoneticScore = this.calculateMultilingualPhoneticMemorability(components, language)
    score += phoneticScore * 0.25

    // 3. 语义记忆性 (25%)
    const semanticScore = this.calculateMultilingualSemanticMemorability(components)
    score += semanticScore * 0.25

    // 4. 文化记忆性 (15%)
    const culturalScore = this.calculateCulturalMemorability(components, language)
    score += culturalScore * 0.15

    return Math.min(score, 1)
  }

  /**
   * 多语种文化适配度评分
   */
  private calculateMultilingualCulturalFit(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    let score = 0

    // 1. 语言文化一致性 (50%)
    const avgCulturalAppropriatenesss = components.reduce((sum, c) => sum + c.cultural_appropriateness, 0) / components.length
    score += avgCulturalAppropriatenesss * 0.5

    // 2. 跨文化和谐性 (30%)
    const harmonyScore = this.calculateMultilingualCulturalHarmony(components)
    score += harmonyScore * 0.3

    // 3. 现代适应性 (20%)
    const modernityScore = this.calculateMultilingualModernityScore(components, language)
    score += modernityScore * 0.2

    return Math.min(score, 1)
  }

  /**
   * 多语种独特性评分
   */
  private calculateMultilingualUniqueness(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    let score = 0

    // 1. 语素稀有性 (40%)
    const rarityScores = components.map(c => 1 - c.usage_frequency)
    const avgRarity = rarityScores.reduce((sum, r) => sum + r, 0) / rarityScores.length
    score += avgRarity * 0.4

    // 2. 语言特定独特性 (35%)
    const languageUniqueness = this.calculateLanguageSpecificUniqueness(text, language)
    score += languageUniqueness * 0.35

    // 3. 组合独特性 (25%)
    const combinationScore = this.calculateMultilingualCombinationUniqueness(components)
    score += combinationScore * 0.25

    return Math.min(score, 1)
  }

  /**
   * 多语种发音友好度评分
   */
  private calculateMultilingualPronunciation(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    // 基于语素的发音友好度评分
    const pronunciationScores = components.map(c => c.language_quality_scores.pronunciation_ease)
    return pronunciationScores.reduce((sum, score) => sum + score, 0) / pronunciationScores.length
  }

  /**
   * 多语种语义连贯性评分
   */
  private calculateMultilingualSemanticCoherence(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    if (components.length < 2) return 0.8

    // 基于语素间的语义相关性
    let totalCoherence = 0
    let pairCount = 0

    for (let i = 0; i < components.length - 1; i++) {
      for (let j = i + 1; j < components.length; j++) {
        const coherence = this.calculateMultilingualSemanticRelatedness(components[i], components[j])
        totalCoherence += coherence
        pairCount++
      }
    }

    return pairCount > 0 ? totalCoherence / pairCount : 0.5
  }

  /**
   * 多语种美学吸引力评分
   */
  private calculateMultilingualAestheticAppeal(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    // 基于语素的美学评分
    const aestheticScores = components.map(c => c.language_quality_scores.aesthetic_appeal)
    return aestheticScores.reduce((sum, score) => sum + score, 0) / aestheticScores.length
  }

  /**
   * 多语种实用性评分
   */
  private calculateMultilingualPracticalUsability(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    // 基于语素的实用性评分
    const practicalityScores = components.map(c => c.language_quality_scores.practicality)
    return practicalityScores.reduce((sum, score) => sum + score, 0) / practicalityScores.length
  }

  /**
   * 多语种解释生成
   */
  private generateMultilingualExplanation(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    pattern: { name: string; template: string },
    language: LanguageCode
  ): string {
    const languageNames: Record<LanguageCode, string> = {
      [LanguageCode.ZH_CN]: '中文',
      [LanguageCode.EN_US]: 'English',
      [LanguageCode.JA_JP]: '日本語',
      [LanguageCode.KO_KR]: '한국어',
      [LanguageCode.ES_ES]: 'Español',
      [LanguageCode.FR_FR]: 'Français',
      [LanguageCode.DE_DE]: 'Deutsch',
      [LanguageCode.AR_SA]: 'العربية'
    }

    const langName = languageNames[language] || language
    const morphemeTexts = components.map(c => c.text).join(' + ')

    return `${langName}用户名"${text}"采用${pattern.name}模式，由语素[${morphemeTexts}]组合而成，体现了${language}语言的文化特色和语言美感。`
  }

  // ============================================================================
  // 多语种辅助方法
  // ============================================================================

  /**
   * 计算文化多样性
   */
  private calculateCulturalDiversity(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[]
  ): number {
    const culturalScores = components.map(c => ({
      traditionality: c.cultural_context.traditionality,
      modernity: c.cultural_context.modernity,
      formality: c.cultural_context.formality
    }))

    // 计算文化维度的方差作为多样性指标
    const traditionalityVariance = this.calculateVariance(culturalScores.map(c => c.traditionality))
    const modernityVariance = this.calculateVariance(culturalScores.map(c => c.modernity))
    const formalityVariance = this.calculateVariance(culturalScores.map(c => c.formality))

    return (traditionalityVariance + modernityVariance + formalityVariance) / 3
  }

  /**
   * 计算语言特定创意性
   */
  private calculateLanguageSpecificCreativity(text: string, language: LanguageCode): number {
    // 根据不同语言的特点计算创意性
    switch (language) {
      case LanguageCode.EN_US:
        // 英文：驼峰命名、词汇组合创新
        return this.hasCapitalizedWords(text) ? 0.8 : 0.6
      case LanguageCode.JA_JP:
        // 日文：假名汉字混合的创新性
        return this.hasKanjiHiraganaMix(text) ? 0.9 : 0.7
      case LanguageCode.KO_KR:
        // 韩文：韩文字符的组合创新
        return this.hasKoreanCharacters(text) ? 0.8 : 0.6
      default:
        return 0.7
    }
  }

  /**
   * 计算语言特定长度评分
   */
  private calculateLanguageSpecificLengthScore(text: string, language: LanguageCode): number {
    const length = text.length

    // 不同语言的最佳长度范围
    const optimalRanges: Record<LanguageCode, { min: number; max: number }> = {
      [LanguageCode.ZH_CN]: { min: 2, max: 4 },
      [LanguageCode.EN_US]: { min: 6, max: 12 },
      [LanguageCode.JA_JP]: { min: 3, max: 6 },
      [LanguageCode.KO_KR]: { min: 3, max: 6 },
      [LanguageCode.ES_ES]: { min: 6, max: 12 },
      [LanguageCode.FR_FR]: { min: 6, max: 12 },
      [LanguageCode.DE_DE]: { min: 6, max: 15 },
      [LanguageCode.AR_SA]: { min: 4, max: 8 }
    }

    const range = optimalRanges[language] || { min: 4, max: 10 }

    if (length >= range.min && length <= range.max) {
      return 1.0
    } else if (length < range.min) {
      return Math.max(0.3, length / range.min)
    } else {
      return Math.max(0.3, range.max / length)
    }
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    return variance
  }

  /**
   * 检查是否有大写单词
   */
  private hasCapitalizedWords(text: string): boolean {
    return /[A-Z]/.test(text)
  }

  /**
   * 检查是否有汉字假名混合
   */
  private hasKanjiHiraganaMix(text: string): boolean {
    const hasKanji = /[\u4e00-\u9faf]/.test(text)
    const hasHiragana = /[\u3040-\u309f]/.test(text)
    return hasKanji && hasHiragana
  }

  /**
   * 检查是否有韩文字符
   */
  private hasKoreanCharacters(text: string): boolean {
    return /[\uac00-\ud7af]/.test(text)
  }

  // 其他简化的多语种辅助方法
  private calculateLanguageSpecificMemorabilityLength(text: string, language: LanguageCode): number {
    return this.calculateLanguageSpecificLengthScore(text, language)
  }

  private calculateMultilingualPhoneticMemorability(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    language: LanguageCode
  ): number {
    const phoneticScores = components.map(c => c.phonetic_features.phonetic_harmony)
    return phoneticScores.reduce((sum, score) => sum + score, 0) / phoneticScores.length
  }

  private calculateMultilingualSemanticMemorability(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[]
  ): number {
    const memoryScores = components.map(c => c.language_quality_scores.memorability)
    return memoryScores.reduce((sum, score) => sum + score, 0) / memoryScores.length
  }

  private calculateCulturalMemorability(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    language: LanguageCode
  ): number {
    return components.reduce((sum, c) => sum + c.cultural_appropriateness, 0) / components.length
  }

  private calculateMultilingualCulturalHarmony(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[]
  ): number {
    if (components.length < 2) return 1.0

    // 计算文化属性的一致性
    const culturalAttributes = components.map(c => [
      c.cultural_context.traditionality,
      c.cultural_context.modernity,
      c.cultural_context.formality
    ])

    let totalHarmony = 0
    for (let i = 0; i < culturalAttributes[0].length; i++) {
      const values = culturalAttributes.map(attrs => attrs[i])
      const variance = this.calculateVariance(values)
      totalHarmony += (1 - Math.min(variance, 1))
    }

    return totalHarmony / culturalAttributes[0].length
  }

  private calculateMultilingualModernityScore(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    language: LanguageCode
  ): number {
    const modernityScores = components.map(c => c.cultural_context.modernity)
    return modernityScores.reduce((sum, score) => sum + score, 0) / modernityScores.length
  }

  private calculateLanguageSpecificUniqueness(text: string, language: LanguageCode): number {
    // 基于语言特定的独特性计算
    return 0.7 // 简化实现
  }

  private calculateMultilingualCombinationUniqueness(
    components: import('../../types/multilingual').LanguageSpecificMorpheme[]
  ): number {
    // 基于语素组合的独特性
    const uniquenessScores = components.map(c => c.language_quality_scores.uniqueness)
    return uniquenessScores.reduce((sum, score) => sum + score, 0) / uniquenessScores.length
  }

  private calculateMultilingualSemanticRelatedness(
    morpheme1: import('../../types/multilingual').LanguageSpecificMorpheme,
    morpheme2: import('../../types/multilingual').LanguageSpecificMorpheme
  ): number {
    // 基于文化标签的相关性
    const tags1 = new Set(morpheme1.cultural_context.cultural_tags)
    const tags2 = new Set(morpheme2.cultural_context.cultural_tags)
    const intersection = new Set([...tags1].filter(tag => tags2.has(tag)))
    const union = new Set([...tags1, ...tags2])

    return union.size > 0 ? intersection.size / union.size : 0.5
  }

  private calculateMultilingualEvaluationConfidence(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    context: GenerationContext,
    language: LanguageCode
  ): number {
    // 基于语素质量的置信度
    const qualityScores = components.map(c => c.native_speaker_rating)
    const avgQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
    const variance = this.calculateVariance(qualityScores)

    return avgQuality * (1 - Math.min(variance, 1))
  }

  private analyzeMultilingualQualityIssues(
    text: string,
    components: import('../../types/multilingual').LanguageSpecificMorpheme[],
    language: LanguageCode,
    dimensions: any
  ): { issues: string[]; suggestions: string[] } {
    const issues: string[] = []
    const suggestions: string[] = []

    // 基于维度评分分析问题
    if (dimensions.pronunciation < 0.6) {
      issues.push(`${language}发音难度较高`)
      suggestions.push(`选择发音更简单的${language}语素`)
    }

    if (dimensions.cultural_fit < 0.7) {
      issues.push(`${language}文化适配度不足`)
      suggestions.push(`增强${language}文化特色元素`)
    }

    if (dimensions.memorability < 0.6) {
      issues.push(`${language}记忆性较低`)
      suggestions.push(`优化${language}用户名长度和结构`)
    }

    return { issues, suggestions }
  }

  /**
   * 评估质量
   *
   * 基于8维度质量评估体系和第一性原理的精确质量评分算法
   *
   * @private
   * @param text 生成的用户名文本
   * @param components 语素组件数组
   * @param context 生成上下文
   * @returns 详细的质量评分结果
   */
  private evaluateQuality(text: string, components: MorphemeComponent[], context: GenerationContext): QualityScore {
    const startTime = Date.now()

    // 计算8个维度的质量评分
    const creativity = this.calculateCreativityScore(text, components, context)
    const memorability = this.calculateMemorabilityScore(text, components, context)
    const cultural_fit = this.calculateCulturalFitScore(text, components, context)
    const uniqueness = this.calculateUniquenessScore(text, components, context)
    const pronunciation = this.calculatePronunciationScore(text, components, context)
    const semantic_coherence = this.calculateSemanticCoherenceScore(text, components, context)
    const aesthetic_appeal = this.calculateAestheticAppealScore(text, components, context)
    const practical_usability = this.calculatePracticalUsabilityScore(text, components, context)

    // 计算综合评分 - 基于认知心理学权重分配
    const weights = {
      creativity: 0.15,        // 创意性 - 用户表达个性的需求
      memorability: 0.20,      // 记忆性 - 认知便利性的核心
      cultural_fit: 0.15,      // 文化适配 - 社交归属感需求
      uniqueness: 0.12,        // 独特性 - 个体差异化需求
      pronunciation: 0.13,     // 发音友好 - 交流便利性
      semantic_coherence: 0.10, // 语义连贯 - 逻辑理解需求
      aesthetic_appeal: 0.08,   // 美学吸引 - 审美愉悦需求
      practical_usability: 0.07 // 实用性 - 使用便利性
    }

    const overall = (
      creativity * weights.creativity +
      memorability * weights.memorability +
      cultural_fit * weights.cultural_fit +
      uniqueness * weights.uniqueness +
      pronunciation * weights.pronunciation +
      semantic_coherence * weights.semantic_coherence +
      aesthetic_appeal * weights.aesthetic_appeal +
      practical_usability * weights.practical_usability
    )

    // 计算评估置信度
    const confidence = this.calculateEvaluationConfidence(text, components, context)

    // 识别问题和建议
    const { issues, suggestions } = this.analyzeQualityIssues(text, components, {
      creativity, memorability, cultural_fit, uniqueness,
      pronunciation, semantic_coherence, aesthetic_appeal, practical_usability
    })

    const evaluationTime = Date.now() - startTime

    return {
      overall: Math.min(Math.max(overall, 0), 1), // 确保在[0,1]范围内
      dimensions: {
        creativity,
        memorability,
        cultural_fit,
        uniqueness,
        pronunciation,
        semantic_coherence,
        aesthetic_appeal,
        practical_usability
      },
      confidence,
      evaluation_time: evaluationTime,
      algorithm_version: '2.0.0',
      issues,
      suggestions,
      timestamp: Date.now()
    }
  }

  /**
   * 计算创意性评分
   *
   * 基于语素组合的新颖性、语义距离和创新模式
   *
   * @private
   */
  private calculateCreativityScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 语素组合新颖性 (40%)
    const categoryDiversity = new Set(components.map(c => c.morpheme.category)).size
    const maxCategories = Math.min(components.length, 3)
    const diversityScore = categoryDiversity / maxCategories
    score += diversityScore * 0.4

    // 2. 语义距离 (30%) - 不同语义向量的距离表示创意性
    if (components.length >= 2) {
      const semanticDistances = []
      for (let i = 0; i < components.length - 1; i++) {
        const dist = this.calculateSemanticDistance(
          components[i].morpheme.semantic_vector,
          components[i + 1].morpheme.semantic_vector
        )
        semanticDistances.push(dist)
      }
      const avgDistance = semanticDistances.reduce((sum, d) => sum + d, 0) / semanticDistances.length
      score += Math.min(avgDistance, 1) * 0.3
    } else {
      score += 0.15 // 单语素的基础创意分
    }

    // 3. 文化创新性 (20%) - 跨文化语境的组合
    const culturalContexts = new Set(components.map(c => c.morpheme.cultural_context))
    if (culturalContexts.size > 1) {
      score += 0.2
    } else {
      score += 0.1
    }

    // 4. 长度创新性 (10%) - 适中长度更有创意空间
    const lengthScore = text.length >= 3 && text.length <= 8 ? 1 : 0.5
    score += lengthScore * 0.1

    return Math.min(score, 1)
  }

  /**
   * 计算记忆性评分
   *
   * 基于认知负荷理论和记忆心理学原理
   *
   * @private
   */
  private calculateMemorabilityScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 长度记忆性 (35%) - 基于Miller's Rule (7±2)
    const length = text.length
    let lengthScore = 0
    if (length >= 2 && length <= 4) {
      lengthScore = 1.0 // 最佳记忆长度
    } else if (length >= 5 && length <= 6) {
      lengthScore = 0.9
    } else if (length >= 7 && length <= 8) {
      lengthScore = 0.7
    } else {
      lengthScore = 0.4
    }
    score += lengthScore * 0.35

    // 2. 音韵记忆性 (25%) - 基于语音相似性和节奏
    const phoneticScore = this.calculatePhoneticMemorability(text, components)
    score += phoneticScore * 0.25

    // 3. 语义记忆性 (25%) - 具象概念比抽象概念更易记忆
    const semanticScore = this.calculateSemanticMemorability(components)
    score += semanticScore * 0.25

    // 4. 结构记忆性 (15%) - 规律性结构更易记忆
    const structureScore = this.calculateStructuralMemorability(text, components)
    score += structureScore * 0.15

    return Math.min(score, 1)
  }

  /**
   * 计算文化适配度评分
   *
   * 基于文化心理学和社会认同理论
   *
   * @private
   */
  private calculateCulturalFitScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 直接文化匹配 (50%)
    const targetContext = context.cultural_preference || 'neutral'
    const matchingComponents = components.filter(c =>
      c.morpheme.cultural_context === targetContext ||
      c.morpheme.cultural_context === 'neutral'
    )
    const directMatchScore = matchingComponents.length / components.length
    score += directMatchScore * 0.5

    // 2. 文化和谐性 (30%) - 不同文化语境的和谐程度
    const culturalContexts = components.map(c =>
      typeof c.morpheme.cultural_context === 'string'
        ? c.morpheme.cultural_context
        : `${c.morpheme.cultural_context.traditionality}_${c.morpheme.cultural_context.formality}`
    )
    const harmonyScore = this.calculateCulturalHarmony(culturalContexts)
    score += harmonyScore * 0.3

    // 3. 时代适应性 (20%) - 现代使用场景的适应度
    const modernityScore = this.calculateModernityScore(components)
    score += modernityScore * 0.2

    return Math.min(score, 1)
  }

  /**
   * 计算独特性评分
   *
   * 基于信息论和统计稀有性
   *
   * @private
   */
  private calculateUniquenessScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 语素稀有性 (40%) - 基于使用频率的反向计算
    const rarityScores = components.map(c => 1 - c.morpheme.usage_frequency)
    const avgRarity = rarityScores.reduce((sum, r) => sum + r, 0) / rarityScores.length
    score += avgRarity * 0.4

    // 2. 组合独特性 (35%) - 语素组合的稀有程度
    const combinationScore = this.calculateCombinationUniqueness(components)
    score += combinationScore * 0.35

    // 3. 长度独特性 (15%) - 非常见长度的加分
    const lengthUniqueness = this.calculateLengthUniqueness(text.length)
    score += lengthUniqueness * 0.15

    // 4. 字符独特性 (10%) - 特殊字符或罕见字符
    const charUniqueness = this.calculateCharacterUniqueness(text)
    score += charUniqueness * 0.1

    return Math.min(score, 1)
  }

  /**
   * 计算发音友好度评分
   *
   * 基于语音学和发音便利性原理
   *
   * @private
   */
  private calculatePronunciationScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 音节流畅性 (40%) - 音节组合的流畅程度
    const syllableFlow = this.calculateSyllableFlow(components)
    score += syllableFlow * 0.4

    // 2. 声调和谐性 (30%) - 中文声调的和谐程度
    const toneHarmony = this.calculateToneHarmony(components)
    score += toneHarmony * 0.3

    // 3. 发音难度 (20%) - 基于音素复杂度
    const pronunciationDifficulty = this.calculatePronunciationDifficulty(text)
    score += (1 - pronunciationDifficulty) * 0.2

    // 4. 国际化友好度 (10%) - 跨语言发音便利性
    const internationalFriendliness = this.calculateInternationalPronunciation(text)
    score += internationalFriendliness * 0.1

    return Math.min(score, 1)
  }

  /**
   * 计算语义连贯性评分
   *
   * 基于语义学和认知连贯性理论
   *
   * @private
   */
  private calculateSemanticCoherenceScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 语义相关性 (50%) - 语素间的语义关联度
    if (components.length >= 2) {
      const semanticRelatedness = this.calculateSemanticRelatedness(components)
      score += semanticRelatedness * 0.5
    } else {
      score += 0.25 // 单语素的基础连贯性
    }

    // 2. 概念层次一致性 (30%) - 抽象层次的一致性
    const conceptualConsistency = this.calculateConceptualConsistency(components)
    score += conceptualConsistency * 0.3

    // 3. 语法合理性 (20%) - 语法结构的合理性
    const grammaticalReasonableness = this.calculateGrammaticalReasonableness(components)
    score += grammaticalReasonableness * 0.2

    return Math.min(score, 1)
  }

  /**
   * 计算美学吸引力评分
   *
   * 基于美学心理学和视觉认知理论
   *
   * @private
   */
  private calculateAestheticAppealScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 字形美感 (35%) - 汉字字形的美学价值
    const visualAesthetics = this.calculateVisualAesthetics(text)
    score += visualAesthetics * 0.35

    // 2. 音韵美感 (30%) - 音韵的美学价值
    const phoneticAesthetics = this.calculatePhoneticAesthetics(components)
    score += phoneticAesthetics * 0.3

    // 3. 意境美感 (25%) - 语义意境的美学价值
    const semanticAesthetics = this.calculateSemanticAesthetics(components)
    score += semanticAesthetics * 0.25

    // 4. 整体和谐性 (10%) - 整体的美学和谐度
    const overallHarmony = this.calculateOverallHarmony(text, components)
    score += overallHarmony * 0.1

    return Math.min(score, 1)
  }

  /**
   * 计算实用性评分
   *
   * 基于使用便利性和实际应用场景
   *
   * @private
   */
  private calculatePracticalUsabilityScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0

    // 1. 输入便利性 (30%) - 键盘输入的便利程度
    const inputConvenience = this.calculateInputConvenience(text)
    score += inputConvenience * 0.3

    // 2. 平台兼容性 (25%) - 各平台的兼容性
    const platformCompatibility = this.calculatePlatformCompatibility(text)
    score += platformCompatibility * 0.25

    // 3. 搜索友好性 (25%) - 搜索引擎友好程度
    const searchFriendliness = this.calculateSearchFriendliness(text)
    score += searchFriendliness * 0.25

    // 4. 传播便利性 (20%) - 口头传播的便利性
    const communicationEase = this.calculateCommunicationEase(text)
    score += communicationEase * 0.2

    return Math.min(score, 1)
  }

  /**
   * 生成解释
   */
  private generateExplanation(text: string, components: MorphemeComponent[], pattern: { name: string }): string {
    const morphemeTexts = components.map(c => c.morpheme.text).join('、')
    return `使用${pattern.name}模式，结合语素：${morphemeTexts}，生成具有${components[0]?.morpheme.tags[0] || '特色'}风格的用户名"${text}"`
  }

  // ============================================================================
  // 辅助计算方法
  // ============================================================================

  /**
   * 计算语义距离
   *
   * @private
   */
  private calculateSemanticDistance(vector1: number[], vector2: number[]): number {
    // 使用欧几里得距离
    let sum = 0
    for (let i = 0; i < Math.min(vector1.length, vector2.length); i++) {
      sum += Math.pow(vector1[i] - vector2[i], 2)
    }
    return Math.sqrt(sum) / Math.sqrt(vector1.length) // 归一化
  }

  /**
   * 计算语音记忆性
   *
   * @private
   */
  private calculatePhoneticMemorability(text: string, components: MorphemeComponent[]): number {
    // 简化实现：基于音节数量和重复音素
    const syllableCount = components.reduce((sum, c) => sum + (c.morpheme.language_properties?.syllable_count || 1), 0)

    // 2-4个音节最易记忆
    if (syllableCount >= 2 && syllableCount <= 4) {
      return 0.9
    } else if (syllableCount <= 6) {
      return 0.7
    } else {
      return 0.5
    }
  }

  /**
   * 计算语义记忆性
   *
   * @private
   */
  private calculateSemanticMemorability(components: MorphemeComponent[]): number {
    // 具象概念比抽象概念更易记忆
    const concreteCategories = ['professions', 'objects', 'actions']
    const concreteCount = components.filter(c => concreteCategories.includes(c.morpheme.category)).length
    return concreteCount / components.length
  }

  /**
   * 计算结构记忆性
   *
   * @private
   */
  private calculateStructuralMemorability(text: string, components: MorphemeComponent[]): number {
    // 规律性结构更易记忆
    if (components.length === 2) {
      return 0.9 // 双字结构最规律
    } else if (components.length === 3) {
      return 0.8
    } else {
      return 0.6
    }
  }

  /**
   * 计算文化和谐性
   *
   * @private
   */
  private calculateCulturalHarmony(contexts: string[]): number {
    const uniqueContexts = new Set(contexts)

    // 单一文化语境最和谐
    if (uniqueContexts.size === 1) {
      return 1.0
    }

    // 包含neutral的组合较和谐
    if (uniqueContexts.has('neutral')) {
      return 0.8
    }

    // 多文化混合
    return 0.6
  }

  /**
   * 计算现代性评分
   *
   * @private
   */
  private calculateModernityScore(components: MorphemeComponent[]): number {
    const modernCount = components.filter(c => {
      const context = c.morpheme.cultural_context
      if (typeof context === 'string') {
        return context === 'modern' || context === 'neutral'
      } else {
        // 对于多维度文化语境，检查现代性指标
        return context.modernity >= 0.7 || context.traditionality <= 0.3
      }
    }).length
    return modernCount / components.length
  }

  /**
   * 计算组合独特性
   *
   * @private
   */
  private calculateCombinationUniqueness(components: MorphemeComponent[]): number {
    // 基于类别组合的稀有性
    const categories = components.map(c => c.morpheme.category).sort()
    const combination = categories.join('-')

    // 常见组合降分，罕见组合加分
    const commonCombinations = ['emotions-professions', 'characteristics-professions']
    if (commonCombinations.includes(combination)) {
      return 0.6
    } else {
      return 0.8
    }
  }

  /**
   * 计算长度独特性
   *
   * @private
   */
  private calculateLengthUniqueness(length: number): number {
    // 2-4字符最常见，5-6字符较独特，7+字符很独特但可能过长
    if (length >= 2 && length <= 4) {
      return 0.3 // 常见长度
    } else if (length >= 5 && length <= 6) {
      return 0.8 // 较独特
    } else if (length >= 7 && length <= 8) {
      return 0.9 // 很独特
    } else {
      return 0.5 // 过短或过长
    }
  }

  /**
   * 计算字符独特性
   *
   * @private
   */
  private calculateCharacterUniqueness(text: string): number {
    // 简化实现：检查是否包含生僻字或特殊字符
    const commonChars = '的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞'

    let uniqueScore = 0
    for (const char of text) {
      if (!commonChars.includes(char)) {
        uniqueScore += 0.2
      }
    }

    return Math.min(uniqueScore / text.length, 1)
  }

  /**
   * 计算音节流畅性
   *
   * @private
   */
  private calculateSyllableFlow(components: MorphemeComponent[]): number {
    // 简化实现：基于音节数量的流畅性
    const totalSyllables = components.reduce((sum, c) =>
      sum + (c.morpheme.language_properties?.syllable_count || 1), 0)

    // 2-4音节最流畅
    if (totalSyllables >= 2 && totalSyllables <= 4) {
      return 0.9
    } else if (totalSyllables <= 6) {
      return 0.7
    } else {
      return 0.5
    }
  }

  /**
   * 计算声调和谐性
   *
   * @private
   */
  private calculateToneHarmony(components: MorphemeComponent[]): number {
    // 简化实现：假设大部分组合都有基本的声调和谐性
    return 0.7
  }

  /**
   * 计算发音难度
   *
   * @private
   */
  private calculatePronunciationDifficulty(text: string): number {
    // 基于字符数量的简化计算
    const length = text.length
    if (length <= 4) {
      return 0.2 // 低难度
    } else if (length <= 6) {
      return 0.4 // 中等难度
    } else {
      return 0.7 // 高难度
    }
  }

  /**
   * 计算国际化发音友好度
   *
   * @private
   */
  private calculateInternationalPronunciation(text: string): number {
    // 简化实现：较短的用户名通常更国际化友好
    return text.length <= 6 ? 0.8 : 0.5
  }

  /**
   * 计算语义相关性
   *
   * @private
   */
  private calculateSemanticRelatedness(components: MorphemeComponent[]): number {
    if (components.length < 2) return 1.0

    // 计算语义向量的相似度
    let totalSimilarity = 0
    let pairCount = 0

    for (let i = 0; i < components.length - 1; i++) {
      for (let j = i + 1; j < components.length; j++) {
        const similarity = this.morphemeRepo.calculateSemanticSimilarity(
          components[i].morpheme.semantic_vector,
          components[j].morpheme.semantic_vector
        )
        totalSimilarity += similarity
        pairCount++
      }
    }

    return pairCount > 0 ? totalSimilarity / pairCount : 0.5
  }

  /**
   * 计算概念层次一致性
   *
   * @private
   */
  private calculateConceptualConsistency(components: MorphemeComponent[]): number {
    // 检查语素是否在相似的抽象层次
    const categories = components.map(c => c.morpheme.category)
    const abstractCategories = ['emotions', 'concepts', 'characteristics']
    const concreteCategories = ['professions', 'objects', 'actions']

    const abstractCount = categories.filter(c => abstractCategories.includes(c)).length
    const concreteCount = categories.filter(c => concreteCategories.includes(c)).length

    // 同一抽象层次的一致性更高
    if (abstractCount === components.length || concreteCount === components.length) {
      return 0.9
    } else {
      return 0.6 // 混合抽象层次
    }
  }

  /**
   * 计算语法合理性
   *
   * @private
   */
  private calculateGrammaticalReasonableness(components: MorphemeComponent[]): number {
    // 简化实现：基于词性组合的合理性
    const morphTypes = components.map(c => c.morpheme.language_properties?.morphological_type || '未知')

    // 常见的合理组合
    const reasonableCombinations = [
      ['形容词', '名词'],
      ['名词', '名词'],
      ['动词', '名词']
    ]

    if (morphTypes.length === 2) {
      const combination = [morphTypes[0], morphTypes[1]]
      const isReasonable = reasonableCombinations.some(rc =>
        rc[0] === combination[0] && rc[1] === combination[1]
      )
      return isReasonable ? 0.9 : 0.6
    }

    return 0.7 // 默认合理性
  }

  /**
   * 计算视觉美学
   *
   * @private
   */
  private calculateVisualAesthetics(text: string): number {
    // 简化实现：基于字符的视觉平衡性
    const length = text.length

    // 2-4字符视觉最平衡
    if (length >= 2 && length <= 4) {
      return 0.9
    } else if (length <= 6) {
      return 0.7
    } else {
      return 0.5
    }
  }

  /**
   * 计算语音美学
   *
   * @private
   */
  private calculatePhoneticAesthetics(components: MorphemeComponent[]): number {
    // 基于音韵的美学价值
    return this.calculateSyllableFlow(components) * 0.8 + this.calculateToneHarmony(components) * 0.2
  }

  /**
   * 计算语义美学
   *
   * @private
   */
  private calculateSemanticAesthetics(components: MorphemeComponent[]): number {
    // 基于语义的美学价值
    const poeticCategories = ['emotions', 'concepts', 'objects']
    const poeticCount = components.filter(c => poeticCategories.includes(c.morpheme.category)).length
    return poeticCount / components.length
  }

  /**
   * 计算整体和谐性
   *
   * @private
   */
  private calculateOverallHarmony(text: string, components: MorphemeComponent[]): number {
    // 综合各方面的和谐性
    const lengthHarmony = text.length >= 2 && text.length <= 6 ? 1 : 0.6
    const categoryHarmony = this.calculateConceptualConsistency(components)
    return (lengthHarmony + categoryHarmony) / 2
  }

  /**
   * 计算输入便利性
   *
   * @private
   */
  private calculateInputConvenience(text: string): number {
    // 基于常用字符的输入便利性
    return text.length <= 8 ? 0.8 : 0.5
  }

  /**
   * 计算平台兼容性
   *
   * @private
   */
  private calculatePlatformCompatibility(text: string): number {
    // 简化实现：中文字符在大多数平台都兼容
    return 0.9
  }

  /**
   * 计算搜索友好性
   *
   * @private
   */
  private calculateSearchFriendliness(text: string): number {
    // 较短的用户名搜索更友好
    return text.length <= 6 ? 0.9 : 0.6
  }

  /**
   * 计算传播便利性
   *
   * @private
   */
  private calculateCommunicationEase(text: string): number {
    // 基于长度和发音的传播便利性
    const lengthScore = text.length <= 4 ? 1 : (text.length <= 6 ? 0.8 : 0.5)
    return lengthScore
  }

  /**
   * 计算评估置信度
   *
   * @private
   */
  private calculateEvaluationConfidence(text: string, components: MorphemeComponent[], context: GenerationContext): number {
    let confidence = 0.8 // 基础置信度

    // 语素数量影响置信度
    if (components.length >= 2) {
      confidence += 0.1
    }

    // 语素质量影响置信度
    const avgQuality = components.reduce((sum, c) => sum + c.morpheme.quality_score, 0) / components.length
    confidence += (avgQuality - 0.5) * 0.2

    // 长度合理性影响置信度
    if (text.length >= 2 && text.length <= 6) {
      confidence += 0.1
    }

    return Math.min(Math.max(confidence, 0.3), 1.0)
  }

  /**
   * 分析质量问题和建议
   *
   * @private
   */
  private analyzeQualityIssues(text: string, components: MorphemeComponent[], dimensions: any): { issues: string[], suggestions: string[] } {
    const issues: string[] = []
    const suggestions: string[] = []

    // 检查各维度的问题
    if (dimensions.memorability < 0.6) {
      issues.push('记忆性较低')
      suggestions.push('考虑使用更短或更有规律的组合')
    }

    if (dimensions.pronunciation < 0.6) {
      issues.push('发音不够友好')
      suggestions.push('选择音节更流畅的语素组合')
    }

    if (dimensions.cultural_fit < 0.7) {
      issues.push('文化适配度不足')
      suggestions.push('选择更符合目标文化语境的语素')
    }

    if (dimensions.semantic_coherence < 0.6) {
      issues.push('语义连贯性不足')
      suggestions.push('选择语义更相关的语素组合')
    }

    if (text.length > 8) {
      issues.push('用户名过长')
      suggestions.push('考虑使用更简洁的语素组合')
    }

    if (text.length < 2) {
      issues.push('用户名过短')
      suggestions.push('考虑添加更多语素以增加表达力')
    }

    return { issues, suggestions }
  }

  /**
   * 获取引擎统计信息 (包含多语种信息)
   *
   * @returns 详细的引擎统计信息
   */
  getStats(): EngineStats {
    const avgGenerationTime = this.generationCount > 0
      ? this.totalGenerationTime / this.generationCount
      : 0

    const morphemeStats = this.morphemeRepo.getStats()

    return {
      morpheme_count: morphemeStats.total,
      morpheme_stats: morphemeStats,
      engine_status: this.isInitialized ? 'ready' : 'not_initialized',
      total_generations: this.generationCount,
      avg_generation_time: avgGenerationTime,
      success_rate: 1.0
    }
  }

  /**
   * 获取多语种统计信息
   *
   * @returns 多语种引擎统计信息
   */
  getMultilingualStats(): {
    supportedLanguages: LanguageCode[]
    languageStats: Record<LanguageCode, { morphemeCount: number; conceptCount: number }>
    totalConcepts: number
    isMultilingualReady: boolean
  } {
    const supportedLanguages = this.languageManager.getSupportedLanguages()
    const languageStats: Record<LanguageCode, { morphemeCount: number; conceptCount: number }> = {
      [LanguageCode.ZH_CN]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.EN_US]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.JA_JP]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.KO_KR]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.ES_ES]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.FR_FR]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.DE_DE]: {
        morphemeCount: 0,
        conceptCount: 0
      },
      [LanguageCode.AR_SA]: {
        morphemeCount: 0,
        conceptCount: 0
      }
    }

    for (const language of supportedLanguages) {
      languageStats[language] = this.languageManager.getLanguageStats(language)
    }

    return {
      supportedLanguages,
      languageStats,
      totalConcepts: Object.values(languageStats).reduce((sum, stats) => sum + stats.conceptCount, 0),
      isMultilingualReady: this.languageManager.isReady()
    }
  }

  /**
   * 获取语素仓库统计信息
   *
   * @returns 语素仓库统计信息
   */
  getMorphemeStats() {
    return this.morphemeRepo.getStats()
  }

  /**
   * 检查引擎是否就绪 (包含多语种检查)
   *
   * @returns 就绪状态
   */
  isReady(): boolean {
    return this.isInitialized &&
           this.morphemeRepo.isReady() &&
           this.languageManager.isReady()
  }

  /**
   * 检查特定语言是否支持
   *
   * @param language 语言代码
   * @returns 是否支持该语言
   */
  isLanguageSupported(language: LanguageCode): boolean {
    return this.languageManager.isLanguageSupported(language)
  }

  /**
   * 获取支持的语言列表
   *
   * @returns 支持的语言代码数组
   */
  getSupportedLanguages(): LanguageCode[] {
    return this.languageManager.getSupportedLanguages()
  }

  /**
   * 清理资源
   *
   * 清理引擎和依赖组件的资源，包含多语种组件
   */
  destroy(): void {
    this.morphemeRepo.destroy()
    // 多语种组件无需特殊清理，由垃圾回收处理
    this.isInitialized = false
    this.generationCount = 0
    this.totalGenerationTime = 0

    console.log('🧹 多语种核心生成引擎资源已清理')
  }
}