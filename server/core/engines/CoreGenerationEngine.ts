/**
 * 核心生成引擎
 * 负责协调各个组件完成用户名生成
 */

import type {
  GeneratedUsername,
  GenerationContext,
  QualityScore,
  MorphemeComponent,
  Morpheme,
  EngineStats
} from '../../types/core'
import { MorphemeRepository } from '../repositories/MorphemeRepository'


export class CoreGenerationEngine {
  private morphemeRepo: MorphemeRepository
  private isInitialized = false

  constructor() {
    this.morphemeRepo = new MorphemeRepository()
  }

  /**
   * 初始化生成引擎
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      await this.morphemeRepo.initialize()
      this.isInitialized = true
      console.log('CoreGenerationEngine initialized successfully')
    } catch (error) {
      console.error('Failed to initialize CoreGenerationEngine:', error)
      throw error
    }
  }

  /**
   * 生成用户名
   */
  async generate(context: GenerationContext, count: number = 5): Promise<GeneratedUsername[]> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const startTime = Date.now()
    const results: GeneratedUsername[] = []

    try {
      for (let i = 0; i < count; i++) {
        const username = await this.generateSingle(context)
        if (username) {
          results.push(username)
        }
      }

      console.log(`Generated ${results.length} usernames in ${Date.now() - startTime}ms`)
      return results
    } catch (error) {
      console.error('Failed to generate usernames:', error)
      throw error
    }
  }

  /**
   * 生成单个用户名
   */
  private async generateSingle(context: GenerationContext): Promise<GeneratedUsername | null> {
    const generationStart = Date.now()

    try {
      // 简化的生成逻辑 - MVP版本
      const pattern = this.selectPattern(context)
      const components = await this.selectMorphemes(pattern, context)

      if (components.length === 0) {
        return null
      }

      const text = this.combineComponents(components)
      const qualityScore = this.evaluateQuality(text, components, context)

      // 检查质量阈值
      if (qualityScore.overall < context.quality_threshold) {
        return null
      }

      return {
        text,
        pattern: pattern.name,
        quality_score: qualityScore,
        explanation: this.generateExplanation(text, components, pattern),
        components,
        metadata: {
          cultural_fit: this.calculateCulturalFit(components, context),
          creativity: this.calculateCreativity(components, pattern),
          memorability: this.calculateMemorability(text),
          uniqueness: this.calculateUniqueness(text),
          generation_time: Date.now() - generationStart
        }
      }
    } catch (error) {
      console.error('Failed to generate single username:', error)
      return null
    }
  }

  /**
   * 选择创意模式
   */
  private selectPattern(context: GenerationContext): { name: string; template: string } {
    // MVP版本使用简化的模式选择
    const patterns = [
      { name: '形容词+名词', template: '{adjective}{noun}' },
      { name: '职业特色', template: '{characteristic}的{profession}' },
      { name: '可爱萌系', template: '小{adjective}' }
    ]

    // 简单随机选择
    return patterns[Math.floor(Math.random() * patterns.length)]
  }

  /**
   * 选择语素
   */
  private async selectMorphemes(pattern: { name: string; template: string }, context: GenerationContext): Promise<MorphemeComponent[]> {
    const components: MorphemeComponent[] = []

    try {
      if (pattern.template.includes('{adjective}')) {
        const adjectives = this.morphemeRepo.findByCategory('emotions')
        if (adjectives.length > 0) {
          const selected = adjectives[Math.floor(Math.random() * adjectives.length)]
          components.push({
            morpheme: selected,
            position: 0,
            role: 'modifier',
            contribution_score: 0.8
          })
        }
      }

      if (pattern.template.includes('{noun}') || pattern.template.includes('{profession}')) {
        const nouns = this.morphemeRepo.findByCategory('professions')
        if (nouns.length > 0) {
          const selected = nouns[Math.floor(Math.random() * nouns.length)]
          components.push({
            morpheme: selected,
            position: 1,
            role: 'root',
            contribution_score: 0.9
          })
        }
      }

      if (pattern.template.includes('{characteristic}')) {
        const characteristics = this.morphemeRepo.findByCategory('characteristics')
        if (characteristics.length > 0) {
          const selected = characteristics[Math.floor(Math.random() * characteristics.length)]
          components.push({
            morpheme: selected,
            position: 0,
            role: 'modifier',
            contribution_score: 0.85
          })
        }
      }

      return components
    } catch (error) {
      console.error('Failed to select morphemes:', error)
      return []
    }
  }

  /**
   * 组合语素
   */
  private combineComponents(components: MorphemeComponent[]): string {
    // 按位置排序
    const sorted = components.sort((a, b) => a.position - b.position)

    // 简单拼接
    return sorted.map(c => c.morpheme.text).join('')
  }

  /**
   * 评估质量
   */
  private evaluateQuality(text: string, components: MorphemeComponent[], context: GenerationContext): QualityScore {
    // 简化的质量评估
    const avgMorphemeQuality = components.reduce((sum, c) => sum + c.morpheme.quality_score, 0) / components.length

    return {
      overall: Math.min(avgMorphemeQuality * 0.9, 1.0), // 稍微降低整体分数
      dimensions: {
        humor_creativity: avgMorphemeQuality * 0.8,
        cultural_resonance: avgMorphemeQuality * 0.9,
        linguistic_beauty: avgMorphemeQuality * 0.85,
        memorability: Math.min(text.length <= 6 ? 0.9 : 0.7, 1.0),
        uniqueness: 0.8 // 简化计算
      },
      issues: [],
      suggestions: []
    }
  }

  /**
   * 生成解释
   */
  private generateExplanation(text: string, components: MorphemeComponent[], pattern: { name: string }): string {
    const morphemeTexts = components.map(c => c.morpheme.text).join('、')
    return `使用${pattern.name}模式，结合语素：${morphemeTexts}，生成具有${components[0]?.morpheme.tags[0] || '特色'}风格的用户名"${text}"`
  }

  /**
   * 计算文化适配度
   */
  private calculateCulturalFit(components: MorphemeComponent[], context: GenerationContext): number {
    const contextMatch = components.filter(c =>
      c.morpheme.cultural_context === context.cultural_preference ||
      c.morpheme.cultural_context === 'neutral'
    ).length

    return contextMatch / components.length
  }

  /**
   * 计算创意度
   */
  private calculateCreativity(components: MorphemeComponent[], pattern: { name: string }): number {
    const avgCreativity = components.reduce((sum, c) => sum + c.morpheme.quality_score, 0) / components.length
    return Math.min(avgCreativity * 0.9, 1.0)
  }

  /**
   * 计算记忆度
   */
  private calculateMemorability(text: string): number {
    // 简化计算：长度适中的用户名更容易记忆
    const length = text.length
    if (length >= 2 && length <= 6) {
      return 0.9
    } else if (length <= 8) {
      return 0.7
    } else {
      return 0.5
    }
  }

  /**
   * 计算独特性
   */
  private calculateUniqueness(text: string): number {
    // 简化计算：假设所有生成的用户名都有一定独特性
    return 0.8
  }

  /**
   * 获取统计信息
   */
  getStats(): EngineStats {
    return {
      morpheme_count: this.morphemeRepo.getCount(),
      morpheme_stats: this.morphemeRepo.getStats(),
      engine_status: this.isInitialized ? 'ready' : 'not_initialized'
    }
  }
}