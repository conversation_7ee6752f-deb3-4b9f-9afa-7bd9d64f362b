/**
 * 核心生成引擎
 *
 * 负责协调各个组件完成用户名生成，支持新的数据加载机制、
 * 多维度索引查询和高效的采样算法。
 *
 * @fileoverview 核心生成引擎
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */

import {
  type GeneratedUsername,
  type GenerationContext,
  type QualityScore,
  type MorphemeComponent,
  type Morpheme,
  type EngineStats,
  MorphemeCategory
} from '../../types/core'
import { MorphemeRepository } from '../repositories/MorphemeRepository'
import { DataLoader } from '../data/DataLoader'
import { DataValidator } from '../data/DataValidator'

/**
 * 核心生成引擎类
 *
 * 提供高效的用户名生成功能，支持多种创意模式和智能质量评估
 */
export class CoreGenerationEngine {
  private morphemeRepo: MorphemeRepository
  private isInitialized = false
  private generationCount = 0
  private totalGenerationTime = 0

  /**
   * 构造函数
   *
   * 初始化生成引擎和依赖组件
   */
  constructor() {
    // 使用新的数据加载和验证机制
    const dataLoader = new DataLoader({
      enableHotReload: true,
      enableValidation: true,
      enableCache: true
    })

    const dataValidator = new DataValidator({
      strict_mode: true,
      skip_warnings: false
    })

    this.morphemeRepo = new MorphemeRepository(dataLoader, dataValidator)
  }

  /**
   * 初始化生成引擎
   *
   * 加载语素数据、构建索引、验证数据完整性
   *
   * @returns Promise<void>
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🔄 核心生成引擎已初始化，跳过重复初始化')
      return
    }

    const startTime = Date.now()
    console.log('🚀 开始初始化核心生成引擎...')

    try {
      // 初始化语素仓库
      await this.morphemeRepo.initialize()

      // 验证仓库状态
      if (!this.morphemeRepo.isReady()) {
        throw new Error('语素仓库初始化失败')
      }

      // 获取初始化统计信息
      const stats = this.morphemeRepo.getStats()

      this.isInitialized = true
      const initTime = Date.now() - startTime

      console.log(`✅ 核心生成引擎初始化完成: ${stats.total}个语素, 耗时${initTime}ms`)
      console.log(`📊 语素分布: ${Object.entries(stats.byCategory).map(([k, v]) => `${k}:${v}`).join(', ')}`)

    } catch (error) {
      console.error('❌ 核心生成引擎初始化失败:', error)
      throw new Error(`核心生成引擎初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 重新加载数据
   *
   * 用于热重载场景
   *
   * @returns Promise<void>
   */
  async reload(): Promise<void> {
    console.log('🔄 重新加载核心生成引擎...')
    this.isInitialized = false
    await this.morphemeRepo.reload()
    await this.initialize()
  }

  /**
   * 生成用户名
   *
   * 支持1-10个用户名的批量生成，专注单用户场景
   *
   * @param context 生成上下文
   * @param count 生成数量 (1-10)
   * @returns 生成的用户名数组
   */
  async generate(context: GenerationContext, count: number = 5): Promise<GeneratedUsername[]> {
    // 验证参数
    if (count < 1 || count > 10) {
      throw new Error('生成数量必须在1-10之间')
    }

    if (!this.isInitialized) {
      await this.initialize()
    }

    const startTime = Date.now()
    const results: GeneratedUsername[] = []
    const maxRetries = count * 3 // 最大重试次数

    console.log(`🎯 开始生成${count}个用户名...`)

    try {
      let attempts = 0
      while (results.length < count && attempts < maxRetries) {
        const username = await this.generateSingle(context)
        if (username) {
          // 检查重复
          const isDuplicate = results.some(existing => existing.text === username.text)
          if (!isDuplicate) {
            results.push(username)
          }
        }
        attempts++
      }

      const generationTime = Date.now() - startTime
      this.generationCount += results.length
      this.totalGenerationTime += generationTime

      console.log(`✅ 生成完成: ${results.length}/${count}个用户名, 耗时${generationTime}ms`)

      if (results.length < count) {
        console.warn(`⚠️ 仅生成了${results.length}个用户名，未达到目标${count}个`)
      }

      return results
    } catch (error) {
      console.error('❌ 用户名生成失败:', error)
      throw new Error(`用户名生成失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 生成单个用户名
   */
  private async generateSingle(context: GenerationContext): Promise<GeneratedUsername | null> {
    const generationStart = Date.now()

    try {
      // 简化的生成逻辑 - MVP版本
      const pattern = this.selectPattern(context)
      const components = await this.selectMorphemes(pattern, context)

      if (components.length === 0) {
        return null
      }

      const text = this.combineComponents(components)
      const qualityScore = this.evaluateQuality(text, components, context)

      // 检查质量阈值
      if (qualityScore.overall < context.quality_threshold) {
        return null
      }

      return {
        text,
        pattern: pattern.name,
        quality_score: qualityScore,
        explanation: this.generateExplanation(text, components, pattern),
        components,
        metadata: {
          cultural_fit: this.calculateCulturalFit(components, context),
          creativity: this.calculateCreativity(components, pattern),
          memorability: this.calculateMemorability(text),
          uniqueness: this.calculateUniqueness(text),
          generation_time: Date.now() - generationStart
        }
      }
    } catch (error) {
      console.error('Failed to generate single username:', error)
      return null
    }
  }

  /**
   * 选择创意模式
   */
  private selectPattern(context: GenerationContext): { name: string; template: string } {
    // MVP版本使用简化的模式选择
    const patterns = [
      { name: '形容词+名词', template: '{adjective}{noun}' },
      { name: '职业特色', template: '{characteristic}的{profession}' },
      { name: '可爱萌系', template: '小{adjective}' }
    ]

    // 简单随机选择
    return patterns[Math.floor(Math.random() * patterns.length)]
  }

  /**
   * 选择语素
   *
   * 使用新的采样算法和多维度索引进行智能语素选择
   *
   * @private
   * @param pattern 创意模式
   * @param context 生成上下文
   * @returns 语素组件数组
   */
  private async selectMorphemes(pattern: { name: string; template: string }, context: GenerationContext): Promise<MorphemeComponent[]> {
    const components: MorphemeComponent[] = []

    try {
      // 使用新的高效采样算法
      if (pattern.template.includes('{adjective}')) {
        // 优先从情感类语素中采样
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.EMOTIONS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: 0,
            role: 'modifier',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      if (pattern.template.includes('{noun}') || pattern.template.includes('{profession}')) {
        // 从职业类语素中采样
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.PROFESSIONS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: 1,
            role: 'root',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      if (pattern.template.includes('{characteristic}')) {
        // 从特征类语素中采样
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.CHARACTERISTICS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: 0,
            role: 'modifier',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      // 如果需要更多语素，可以从其他类别补充
      if (components.length < 2 && pattern.template.includes('{object}')) {
        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.OBJECTS, 1)
        if (sampled.length > 0) {
          components.push({
            morpheme: sampled[0],
            position: components.length,
            role: 'complement',
            contribution_score: this.calculateContributionScore(sampled[0], context)
          })
        }
      }

      // 根据文化偏好进行二次筛选
      if (context.cultural_preference && context.cultural_preference !== 'neutral') {
        return this.filterByCulturalPreference(components, context.cultural_preference)
      }

      return components
    } catch (error) {
      console.error('❌ 语素选择失败:', error)
      return []
    }
  }

  /**
   * 计算语素贡献分数
   *
   * @private
   * @param morpheme 语素
   * @param context 生成上下文
   * @returns 贡献分数
   */
  private calculateContributionScore(morpheme: Morpheme, context: GenerationContext): number {
    let score = morpheme.quality_score * 0.6 + morpheme.usage_frequency * 0.4

    // 文化适配加成
    if (context.cultural_preference === morpheme.cultural_context) {
      score *= 1.2
    } else if (morpheme.cultural_context === 'neutral') {
      score *= 1.1
    }

    // 质量阈值加成
    if (morpheme.quality_score >= context.quality_threshold) {
      score *= 1.1
    }

    return Math.min(score, 1.0)
  }

  /**
   * 根据文化偏好筛选语素
   *
   * @private
   * @param components 语素组件数组
   * @param culturalPreference 文化偏好
   * @returns 筛选后的语素组件数组
   */
  private filterByCulturalPreference(components: MorphemeComponent[], culturalPreference: string): MorphemeComponent[] {
    // 优先保留匹配文化偏好的语素
    const preferred = components.filter(c =>
      c.morpheme.cultural_context === culturalPreference ||
      c.morpheme.cultural_context === 'neutral'
    )

    // 如果筛选后语素不足，保留原始结果
    return preferred.length >= 1 ? preferred : components
  }

  /**
   * 组合语素
   */
  private combineComponents(components: MorphemeComponent[]): string {
    // 按位置排序
    const sorted = components.sort((a, b) => a.position - b.position)

    // 简单拼接
    return sorted.map(c => c.morpheme.text).join('')
  }

  /**
   * 评估质量
   */
  private evaluateQuality(text: string, components: MorphemeComponent[], context: GenerationContext): QualityScore {
    // 简化的质量评估
    const avgMorphemeQuality = components.reduce((sum, c) => sum + c.morpheme.quality_score, 0) / components.length

    return {
      confidence: this.calculateConfidence(text),
      evaluation_time: Date.now(),
      algorithm_version: '',
      timestamp: Date.now(),
      overall: Math.min(avgMorphemeQuality * 0.9, 1.0), // 稍微降低整体分数
      dimensions: {
        creativity: avgMorphemeQuality * 0.8,
        cultural_fit: avgMorphemeQuality * 0.9,
        aesthetic_appeal: avgMorphemeQuality * 0.85,
        memorability: this.calculateMemorability(text),
        uniqueness: this.calculateUniqueness(text),
        pronunciation: this.calculatePronunciation(text),
        semantic_coherence: this.calculateSemanticCoherence(text),
        practical_usability: this.calculatePracticalUsability(text)
      },
      issues: [],
      suggestions: []
    }
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(text: string): number {
    // 简单的置信度计算
    return 0.8
  }

  /**
   * 计算发音友好度
   */
  private calculatePronunciation(text: string): number {
    // 简单的发音友好度计算
    return 0.7
  }

  /**
   * 计算语义连贯性
   */
  private calculateSemanticCoherence(text: string): number {
    // 简单的语义连贯性计算
    return 0.6
  }

  /**
   * 计算实用性
   */
  private calculatePracticalUsability(text: string): number {
    // 简单的实用性计算
    return 0.5
  }

  /**
   * 生成解释
   */
  private generateExplanation(text: string, components: MorphemeComponent[], pattern: { name: string }): string {
    const morphemeTexts = components.map(c => c.morpheme.text).join('、')
    return `使用${pattern.name}模式，结合语素：${morphemeTexts}，生成具有${components[0]?.morpheme.tags[0] || '特色'}风格的用户名"${text}"`
  }

  /**
   * 计算文化适配度
   */
  private calculateCulturalFit(components: MorphemeComponent[], context: GenerationContext): number {
    const contextMatch = components.filter(c =>
      c.morpheme.cultural_context === context.cultural_preference ||
      c.morpheme.cultural_context === 'neutral'
    ).length

    return contextMatch / components.length
  }

  /**
   * 计算创意度
   */
  private calculateCreativity(components: MorphemeComponent[], pattern: { name: string }): number {
    const avgCreativity = components.reduce((sum, c) => sum + c.morpheme.quality_score, 0) / components.length
    return Math.min(avgCreativity * 0.9, 1.0)
  }

  /**
   * 计算记忆度
   */
  private calculateMemorability(text: string): number {
    // 简化计算：长度适中的用户名更容易记忆
    const length = text.length
    if (length >= 2 && length <= 6) {
      return 0.9
    } else if (length <= 8) {
      return 0.7
    } else {
      return 0.5
    }
  }

  /**
   * 计算独特性
   */
  private calculateUniqueness(text: string): number {
    // 简化计算：假设所有生成的用户名都有一定独特性
    return 0.8
  }

  /**
   * 获取引擎统计信息
   *
   * @returns 详细的引擎统计信息
   */
  getStats(): EngineStats {
    const avgGenerationTime = this.generationCount > 0
      ? this.totalGenerationTime / this.generationCount
      : 0

    return {
      morpheme_count: this.morphemeRepo.getStats().total,
      engine_status: this.isInitialized ? 'ready' : 'not_initialized',
      morpheme_stats: this.morphemeRepo.getStats()
    }
  }

  /**
   * 获取语素仓库统计信息
   *
   * @returns 语素仓库统计信息
   */
  getMorphemeStats() {
    return this.morphemeRepo.getStats()
  }

  /**
   * 检查引擎是否就绪
   *
   * @returns 就绪状态
   */
  isReady(): boolean {
    return this.isInitialized && this.morphemeRepo.isReady()
  }

  /**
   * 清理资源
   *
   * 清理引擎和依赖组件的资源
   */
  destroy(): void {
    this.morphemeRepo.destroy()
    this.isInitialized = false
    this.generationCount = 0
    this.totalGenerationTime = 0

    console.log('🧹 核心生成引擎资源已清理')
  }
}