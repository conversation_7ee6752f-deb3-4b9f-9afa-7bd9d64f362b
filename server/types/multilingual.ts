/**
 * 多语种数据模型定义 (v3.0)
 * 
 * 基于概念-语言分离架构的多语种数据类型定义
 * 支持8种主流语言的语素生成和跨语言语义对齐
 * 
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */

// ============================================================================
// 语言代码和基础类型
// ============================================================================

/**
 * 支持的语言代码枚举
 */
export enum LanguageCode {
  /** 中文 (简体) */
  ZH_CN = 'zh-CN',
  /** 英文 (美式) */
  EN_US = 'en-US',
  /** 日文 */
  JA_JP = 'ja-JP',
  /** 韩文 */
  KO_KR = 'ko-KR',
  /** 西班牙文 */
  ES_ES = 'es-ES',
  /** 法文 */
  FR_FR = 'fr-FR',
  /** 德文 */
  DE_DE = 'de-DE',
  /** 阿拉伯文 */
  AR_SA = 'ar-SA'
}

/**
 * 概念类别枚举 (语言无关)
 */
export enum ConceptCategory {
  /** 情感概念 */
  EMOTIONS = 'emotions',
  /** 职业概念 */
  PROFESSIONS = 'professions',
  /** 特征概念 */
  CHARACTERISTICS = 'characteristics',
  /** 物体概念 */
  OBJECTS = 'objects',
  /** 动作概念 */
  ACTIONS = 'actions',
  /** 抽象概念 */
  CONCEPTS = 'concepts'
}

/**
 * 语域层次枚举
 */
export enum RegisterLevel {
  /** 正式语域 */
  FORMAL = 'formal',
  /** 中性语域 */
  NEUTRAL = 'neutral',
  /** 非正式语域 */
  INFORMAL = 'informal',
  /** 口语化语域 */
  COLLOQUIAL = 'colloquial'
}

// ============================================================================
// 通用概念数据模型
// ============================================================================

/**
 * 通用语义向量接口
 */
export interface UniversalSemanticVector {
  /** 512维语义向量 (基于mBERT/XLM-R) */
  vector: number[]
  /** 向量模型版本 */
  model_version: string
  /** 向量置信度 [0-1] */
  confidence: number
  /** 最后更新时间 */
  updated_at: string
}

/**
 * 多维度文化语境接口
 */
export interface CulturalContext {
  /** 传统性程度 [0-1] */
  traditionality: number
  /** 现代性程度 [0-1] */
  modernity: number
  /** 正式性程度 [0-1] */
  formality: number
  /** 地域特色程度 [0-1] */
  regionality: number
  /** 宗教敏感性 [0-1] */
  religious_sensitivity: number
  /** 年龄适宜性标签 */
  age_appropriateness: string[]
  /** 文化标签 */
  cultural_tags: string[]
}

/**
 * 通用概念接口 - 语言无关的抽象概念
 */
export interface UniversalConcept {
  /** 全局唯一概念ID */
  concept_id: string
  /** 语义向量 */
  semantic_vector: UniversalSemanticVector
  /** 概念类别 */
  concept_category: ConceptCategory
  /** 抽象程度 [0-1] */
  abstraction_level: number
  /** 文化中性度 [0-1] */
  cultural_neutrality: number
  /** 跨语言稳定性 [0-1] */
  cross_lingual_stability: number
  
  // 认知属性
  /** 认知负荷 [0-1] */
  cognitive_load: number
  /** 记忆性评分 [0-1] */
  memorability_score: number
  /** 情感价值 [-1,1] */
  emotional_valence: number
  /** 认知属性集合 */
  cognitive_attributes: {
    memorability: number
    cognitive_load: number
    emotional_valence: number
  }
  
  // 关系映射
  /** 相关概念ID列表 */
  related_concepts: string[]
  /** 层次父概念ID */
  hierarchical_parent?: string
  /** 层次子概念ID列表 */
  hierarchical_children: string[]
  
  // 元数据
  created_at: string
  updated_at: string
  version: string
}

// ============================================================================
// 语言特定实现数据模型
// ============================================================================

/**
 * 语音特征接口
 */
export interface PhoneticFeatures {
  /** IPA音标 */
  ipa_transcription: string
  /** 音节数量 */
  syllable_count: number
  /** 重音模式 (英文等) */
  stress_pattern?: string
  /** 声调信息 (中文等) */
  tone_pattern?: string[]
  /** 语音和谐性评分 [0-1] */
  phonetic_harmony: number
}

/**
 * 词法信息接口
 */
export interface MorphologicalInfo {
  /** 词性标注 (Universal Dependencies) */
  pos_tag: string
  /** 词法类型 */
  morphological_type: string
  /** 词根信息 */
  root?: string
  /** 前缀列表 */
  prefixes: string[]
  /** 后缀列表 */
  suffixes: string[]
  /** 屈折变化信息 */
  inflection_info?: Record<string, string>
}

/**
 * 句法属性接口
 */
export interface SyntacticProperties {
  /** 句法功能 */
  syntactic_function: string[]
  /** 搭配限制 */
  collocation_constraints: string[]
  /** 语法特征 */
  grammatical_features: Record<string, string>
}

/**
 * 地区变体接口
 */
export interface RegionalVariant {
  /** 地区代码 */
  region_code: string
  /** 变体文本 */
  variant_text: string
  /** 使用频率 [0-1] */
  usage_frequency: number
  /** 地区特色程度 [0-1] */
  regional_specificity: number
}

/**
 * 语言特定质量评分接口
 */
export interface LanguageQualityScores {
  /** 自然度 [0-1] */
  naturalness: number
  /** 流畅度 [0-1] */
  fluency: number
  /** 地道性 [0-1] */
  authenticity: number
  /** 美学吸引力 [0-1] */
  aesthetic_appeal: number
  /** 发音友好度 [0-1] */
  pronunciation_ease: number
  /** 记忆性 [0-1] */
  memorability: number
  /** 独特性 [0-1] */
  uniqueness: number
  /** 实用性 [0-1] */
  practicality: number
}

/**
 * 语言特定语素接口 - 概念在特定语言中的实现
 */
export interface LanguageSpecificMorpheme {
  /** 语素唯一ID */
  morpheme_id: string
  /** 对应的通用概念ID */
  concept_id: string
  /** 语言代码 */
  language: LanguageCode
  
  // 语言形式
  /** 文本形式 */
  text: string
  /** 替代形式列表 */
  alternative_forms: string[]
  
  // 语言学属性
  /** 语音特征 */
  phonetic_features: PhoneticFeatures
  /** 词法信息 */
  morphological_info: MorphologicalInfo
  /** 句法属性 */
  syntactic_properties: SyntacticProperties
  
  // 文化适配
  /** 文化语境 */
  cultural_context: CulturalContext
  /** 地区变体 */
  regional_variants: RegionalVariant[]
  /** 语域层次 */
  register_level: RegisterLevel
  
  // 质量指标
  /** 语言特定质量评分 */
  language_quality_scores: LanguageQualityScores
  /** 文化适宜性 [0-1] */
  cultural_appropriateness: number
  /** 母语者评分 [0-1] */
  native_speaker_rating: number
  
  // 使用统计
  /** 使用频率 [0-1] */
  usage_frequency: number
  /** 流行度趋势 */
  popularity_trend: number
  
  // 元数据
  created_at: string
  updated_at: string
  version: string
  /** 数据来源 */
  source: string
  /** 验证状态 */
  validation_status: 'pending' | 'validated' | 'rejected'
}

// ============================================================================
// 跨语言对齐和质量评估
// ============================================================================

/**
 * 语义对齐结果接口
 */
export interface AlignmentResult {
  /** 源概念 */
  source_concept: UniversalConcept
  /** 目标语言 */
  target_language: LanguageCode
  /** 对齐的语素列表 */
  aligned_morphemes: LanguageSpecificMorpheme[]
  /** 对齐置信度 [0-1] */
  confidence_score: number
  /** 语义距离 [0-1] */
  semantic_distance: number
  /** 文化适配说明 */
  cultural_adaptation_notes: string[]
  /** 对齐时间戳 */
  aligned_at: string
}

/**
 * 批量对齐结果接口
 */
export interface BatchAlignmentResult {
  /** 对齐结果列表 */
  alignments: AlignmentResult[]
  /** 总体成功率 [0-1] */
  overall_success_rate: number
  /** 平均置信度 [0-1] */
  average_confidence: number
  /** 处理时间 (毫秒) */
  processing_time: number
  /** 错误信息 */
  errors: string[]
}

/**
 * 跨语言一致性报告接口
 */
export interface ConsistencyReport {
  /** 概念ID */
  concept_id: string
  /** 检查的语言列表 */
  languages: LanguageCode[]
  /** 语义一致性评分 [0-1] */
  semantic_consistency: number
  /** 文化一致性评分 [0-1] */
  cultural_consistency: number
  /** 质量一致性评分 [0-1] */
  quality_consistency: number
  /** 不一致项详情 */
  inconsistencies: {
    type: 'semantic' | 'cultural' | 'quality'
    description: string
    affected_languages: LanguageCode[]
    severity: 'low' | 'medium' | 'high'
  }[]
  /** 建议改进措施 */
  improvement_suggestions: string[]
  /** 检查时间戳 */
  checked_at: string
}

/**
 * 通用质量评估接口
 */
export interface UniversalQualityAssessment {
  /** 概念ID */
  concept_id: string
  /** 创意性评分 [0-1] */
  creativity: number
  /** 记忆性评分 [0-1] */
  memorability: number
  /** 独特性评分 [0-1] */
  uniqueness: number
  /** 语义连贯性评分 [0-1] */
  semantic_coherence: number
  /** 跨语言适应性评分 [0-1] */
  cross_lingual_adaptability: number
  /** 综合质量评分 [0-1] */
  overall_quality: number
  /** 评估时间戳 */
  assessed_at: string
}

/**
 * 多语种用户名生成请求接口
 */
export interface MultilingualGenerationRequest {
  /** 目标语言 */
  target_language: LanguageCode
  /** 概念偏好 */
  concept_preferences?: {
    categories: ConceptCategory[]
    cultural_context?: Partial<CulturalContext>
    abstraction_level?: [number, number] // 范围
    emotional_valence?: [number, number] // 范围
  }
  /** 跨语言选项 */
  cross_lingual_options?: {
    /** 是否启用跨语言对比 */
    enable_comparison: boolean
    /** 对比语言列表 */
    comparison_languages: LanguageCode[]
    /** 语义一致性要求 [0-1] */
    semantic_consistency_threshold: number
  }
  /** 生成数量 */
  count: number
  /** 质量阈值 [0-1] */
  quality_threshold: number
}

/**
 * 多语种用户名生成结果接口
 */
export interface MultilingualGenerationResult {
  /** 目标语言 */
  target_language: LanguageCode
  /** 生成的用户名列表 */
  usernames: {
    /** 用户名文本 */
    text: string
    /** 使用的语素组合 */
    morpheme_combination: LanguageSpecificMorpheme[]
    /** 对应的概念组合 */
    concept_combination: UniversalConcept[]
    /** 质量评估 */
    quality_assessment: UniversalQualityAssessment
    /** 跨语言对比 (如果启用) */
    cross_lingual_comparison?: {
      language: LanguageCode
      equivalent_text: string
      semantic_similarity: number
    }[]
  }[]
  /** 生成统计 */
  generation_stats: {
    /** 请求处理时间 (毫秒) */
    processing_time: number
    /** 成功生成数量 */
    successful_count: number
    /** 平均质量评分 */
    average_quality: number
    /** 语义多样性评分 [0-1] */
    semantic_diversity: number
  }
  /** 生成时间戳 */
  generated_at: string
}

// ============================================================================
// 数据迁移和兼容性
// ============================================================================

/**
 * v2.0到v3.0数据迁移映射接口
 */
export interface MigrationMapping {
  /** v2.0语素ID */
  v2_morpheme_id: string
  /** 对应的v3.0概念ID */
  v3_concept_id: string
  /** 对应的v3.0语素ID */
  v3_morpheme_id: string
  /** 迁移置信度 [0-1] */
  migration_confidence: number
  /** 需要人工审核 */
  requires_manual_review: boolean
  /** 迁移说明 */
  migration_notes: string[]
}

/**
 * 向后兼容性配置接口
 */
export interface BackwardCompatibilityConfig {
  /** 是否启用v2.0兼容模式 */
  enable_v2_compatibility: boolean
  /** v2.0 API端点映射 */
  v2_api_mappings: Record<string, string>
  /** 数据格式转换规则 */
  format_conversion_rules: Record<string, any>
  /** 兼容性警告阈值 */
  compatibility_warning_threshold: number
}

/**
 * 概念-语素映射接口
 */
export interface ConceptMorphemeMapping {
  /** 概念ID */
  conceptId: string
  /** 对应的语素列表 */
  morphemes: LanguageSpecificMorpheme[]
  /** 对齐分数 */
  alignmentScore: number
  /** 最后更新时间 */
  lastUpdated: string
}
