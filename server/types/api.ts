/**
 * API接口类型定义
 * 定义namer-v6项目的API请求和响应结构
 */

import type {
  GeneratedUsername,
  QualityScore,
  CulturalContext,
  StylePreference,
  PerformanceMetrics,
  CreativePattern,
  LengthPreference
} from './core'

/**
 * 统一API响应格式
 */
export interface APIResponse<T = unknown> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: Record<string, unknown>
  }
  meta: {
    timestamp: number
    request_id: string
    execution_time: number
    version: string
  }
}

/**
 * 用户名生成请求
 */
export interface GenerateRequest {
  count?: number                    // 生成数量，默认5，最大20
  language?: string                 // 语言代码，默认zh-CN
  cultural_preference?: CulturalContext
  style_preference?: StylePreference
  length_preference?: LengthPreference
  creativity_level?: number         // 创意程度: 0.1-1.0
  quality_threshold?: number        // 质量阈值: 0.1-1.0
  user_id?: string                  // 用户ID，用于个性化
  context?: {
    occasion?: string               // 使用场合
    target_audience?: string        // 目标受众
    keywords?: string[]             // 关键词
  }
  patterns?: string[]               // 指定创意模式
  exclude_patterns?: string[]       // 排除的模式
}

/**
 * 用户名生成响应
 */
export interface GenerateResponse {
  usernames: GeneratedUsername[]
  generation_time: number           // 生成耗时(ms)
  cache_hit: boolean               // 是否命中缓存
  recommendations?: string[]        // 推荐的相关用户名
  patterns_used: string[]          // 使用的模式列表
  total_attempts: number           // 总尝试次数
}

/**
 * 批量生成请求
 */
export interface BatchGenerateRequest {
  requests: GenerateRequest[]       // 批量请求
  parallel?: boolean               // 是否并行处理，默认true
  timeout?: number                 // 超时时间(ms)，默认30000
}

/**
 * 批量生成响应
 */
export interface BatchGenerateResponse {
  results: (GenerateResponse | null)[]  // 结果数组，失败为null
  success_count: number            // 成功数量
  total_time: number              // 总耗时
  errors?: BatchError[]           // 错误信息
}

/**
 * 批量错误信息
 */
export interface BatchError {
  index: number
  error: {
    code: string
    message: string
    details?: Record<string, unknown>
  }
}

/**
 * 质量评估请求
 */
export interface EvaluateRequest {
  usernames: string[]              // 待评估的用户名
  language?: string                // 语言代码
  cultural_context?: CulturalContext
  detailed?: boolean               // 是否返回详细评估
}

/**
 * 质量评估响应
 */
export interface EvaluateResponse {
  evaluations: UsernameEvaluation[]
  average_score: number
  evaluation_time: number
  summary: {
    excellent_count: number        // 优秀数量 (>0.8)
    good_count: number            // 良好数量 (0.6-0.8)
    average_count: number         // 一般数量 (0.4-0.6)
    poor_count: number            // 较差数量 (<0.4)
  }
}

/**
 * 用户名评估结果
 */
export interface UsernameEvaluation {
  username: string
  overall_score: number            // 总体评分 0-1
  quality_score: QualityScore
  suggestions?: string[]           // 改进建议
  similar_usernames?: string[]     // 相似用户名推荐
}

/**
 * 健康检查响应
 */
export interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: number
  version: string
  uptime: number
  checks: {
    memory: HealthCheck
    cache: HealthCheck
    data_integrity: HealthCheck
    performance: HealthCheck
  }
}

/**
 * 健康检查详情
 */
export interface HealthCheckDetails {
  [key: string]: string | number | boolean | null | undefined | HealthCheckDetails | HealthCheckDetails[]
}

/**
 * 健康检查项
 */
export interface HealthCheck {
  status: 'pass' | 'fail' | 'warn'
  response_time?: number
  details?: HealthCheckDetails
  message?: string
}

/**
 * 性能指标响应
 */
export interface MetricsResponse {
  performance: PerformanceMetrics
  system_info: {
    node_version: string
    memory_usage: NodeJS.MemoryUsage
    uptime: number
    platform: string
  }
  api_stats: {
    total_requests: number
    successful_requests: number
    failed_requests: number
    avg_response_time: number
  }
}

/**
 * 创意模式列表响应
 */
export interface PatternsResponse {
  patterns: CreativePattern[]
  total_count: number
  categories: {
    [category: string]: number
  }
}

/**
 * 错误代码枚举
 */
export enum ErrorCode {
  // 通用错误 (1000-1999)
  INVALID_PARAMETERS = 'E1001',
  MISSING_REQUIRED_FIELD = 'E1002',
  INVALID_FORMAT = 'E1003',
  VALIDATION_FAILED = 'E1004',

  // 业务错误 (3000-3999)
  GENERATION_FAILED = 'E3001',
  QUALITY_TOO_LOW = 'E3002',
  NO_SUITABLE_PATTERNS = 'E3003',
  INSUFFICIENT_MORPHEMES = 'E3004',

  // 系统错误 (5000-5999)
  INTERNAL_ERROR = 'E5001',
  SERVICE_UNAVAILABLE = 'E5002',
  TIMEOUT = 'E5003',
  MEMORY_LIMIT_EXCEEDED = 'E5004'
}

/**
 * 请求验证规则
 */
export type ValidationValue = string | number | boolean | null | undefined

export interface ValidationRule {
  field: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  enum?: ValidationValue[]
  custom?: (value: unknown) => boolean | string
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page?: number
  limit?: number
  sort?: string
  order?: 'asc' | 'desc'
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    has_next: boolean
    has_prev: boolean
  }
}