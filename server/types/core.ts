/**
 * namer-v6 核心数据类型定义
 *
 * 本文件定义了namer-v6项目的核心数据结构和接口，
 * 支持多语言扩展、智能语义分析和基于第一性原理的创意模式。
 * v3.0版本：支持多语种架构，概念-语言分离设计
 *
 * @fileoverview 核心数据类型定义 (v3.0兼容)
 * @version 3.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */

import type { MorphemeVersion } from "./common"
import type {
  LanguageCode,
  ConceptCategory,
  MultiDimensionalCulturalContext
} from "./multilingual"
import { SEMANTIC_VECTOR_DIMENSIONS } from "./multilingual"

// ============================================================================
// v3.0兼容性工具函数
// ============================================================================

/**
 * 验证语义向量维度是否符合v3.0标准
 * @param vector 语义向量
 * @returns 是否为有效的v3.0向量
 */
export function isValidSemanticVector(vector: number[]): boolean {
  return vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY ||
         vector.length === SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL
}

/**
 * 获取语义向量的版本类型
 * @param vector 语义向量
 * @returns 向量版本类型
 */
export function getSemanticVectorVersion(vector: number[]): 'v2.0' | 'v3.0' | 'unknown' {
  if (vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY) return 'v2.0'
  if (vector.length === SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL) return 'v3.0'
  return 'unknown'
}

// ============================================================================
// 基础枚举定义
// ============================================================================

/**
 * 语素类别枚举
 *
 * 定义了语素的主要分类，用于语义组织和检索优化。
 * 基于语言学和认知心理学原理进行分类。
 */
export enum MorphemeCategory {
  /** 情感类语素 - 表达情感状态、感受和情绪 */
  EMOTIONS = 'emotions',
  /** 职业类语素 - 表达职业身份、专业领域和工作角色 */
  PROFESSIONS = 'professions',
  /** 特征类语素 - 表达个性特征、能力属性和品质特点 */
  CHARACTERISTICS = 'characteristics',
  /** 物体类语素 - 表达具体或抽象的事物、对象 */
  OBJECTS = 'objects',
  /** 动作类语素 - 表达行为、动作状态和活动 */
  ACTIONS = 'actions',
  /** 概念类语素 - 表达抽象概念、理念和思想 */
  CONCEPTS = 'concepts'
}

/**
 * 文化语境枚举
 *
 * 定义了语素的文化背景属性，用于文化适配和风格匹配。
 * 基于文化人类学和社会心理学理论进行分类。
 */
export enum CulturalContext {
  /** 古典文化语境 - 传统、典雅、深厚的文化特色 */
  ANCIENT = 'ancient',
  /** 现代文化语境 - 时尚、创新、国际化的文化特色 */
  MODERN = 'modern',
  /** 中性文化语境 - 通用、平衡、包容的文化特色 */
  NEUTRAL = 'neutral'
}

// ============================================================================
// 语素相关接口定义
// ============================================================================

/**
 * 语素质量指标接口
 *
 * 定义了语素质量评估的各项指标，用于智能筛选和排序
 */
export interface MorphemeQualityMetrics {
  /** 自然度评分 [0-1] - 语素在语言中的自然程度 */
  naturalness: number
  /** 使用频率 [0-1] - 语素在实际使用中的频率 */
  frequency: number
  /** 可接受度 [0-1] - 用户对语素的接受程度 */
  acceptability: number
  /** 美学吸引力 [0-1] - 语素的美学价值和吸引力 */
  aesthetic_appeal: number
}

/**
 * 语素语言属性接口 (v3.0扩展)
 *
 * 定义了语素的语言学特征，用于语音分析和组合优化。
 * v3.0版本支持国际音标和多语种语言学特征。
 */
export interface MorphemeLanguageProperties {
  /** 音节数量 - 用于音韵分析 */
  syllable_count: number
  /** 字符数量 - 用于长度控制 */
  character_count: number
  /** 语音特征标签 - 用于音韵和谐性检查 */
  phonetic_features: string[]
  /** 词法类型 - 词性和语法功能 */
  morphological_type: string
  /** 发音标记 - 拼音或音标 */
  pronunciation?: string
  /** IPA国际音标 (v3.0新增) */
  ipa_transcription?: string
  /** 重音模式 (v3.0新增，用于英文等语言) */
  stress_pattern?: string
  /** 声调信息 (v3.0新增，用于中文等声调语言) */
  tone_pattern?: string[]
  /** 语音和谐性评分 [0-1] (v3.0新增) */
  phonetic_harmony?: number
}

/**
 * 语素数据结构 (v3.0兼容)
 *
 * 核心语素数据结构，包含语义、文化、质量等多维度属性。
 * 支持多语言扩展和智能分析。v3.0版本支持概念-语言分离架构。
 */
export interface Morpheme {
  /** 语素唯一标识符，格式：{category}_{序号} */
  id: string
  /** 对应的通用概念ID (v3.0新增) */
  concept_id?: string
  /** 语言代码 (v3.0新增，用于多语种支持) */
  language?: LanguageCode
  /** 语素文本内容 */
  text: string
  /** 语素主分类 */
  category: MorphemeCategory
  /** 语素子分类，用于更精细的分类管理 */
  subcategory: string
  /** 文化语境属性 (支持传统枚举和多维度对象) */
  cultural_context: CulturalContext | MultiDimensionalCulturalContext
  /** 使用频率 [0-1]，影响采样权重 */
  usage_frequency: number
  /** 综合质量评分 [0-1] */
  quality_score: number
  /** 语义向量，支持20维(v2.0)和512维(v3.0) */
  semantic_vector: number[]
  /** 语义标签，支持多维度检索和分类 */
  tags: string[]
  /** 语言属性 */
  language_properties: MorphemeLanguageProperties
  /** 质量指标 */
  quality_metrics: MorphemeQualityMetrics
  /** 创建时间戳 */
  created_at: number
  /** 数据来源标识 */
  source: string
  /** 数据版本，用于版本管理和兼容性检查 */
  version: MorphemeVersion
  /** 源文件 (用于数据重组脚本) */
  _source_file?: string
}

// ============================================================================
// 创意模式相关接口定义 (基于第一性原理设计)
// ============================================================================

/**
 * 模式原型枚举
 *
 * 基于第一性原理和用户心理需求分析定义的8种创意模式原型
 */
export enum PatternArchetype {
  /** 专业型 - 强调能力和专业性，满足身份表达需求 */
  PROFESSIONAL = 'professional',
  /** 艺术型 - 强调创意和美感，满足美学追求需求 */
  ARTISTIC = 'artistic',
  /** 幽默型 - 强调趣味和亲和力，满足社交润滑需求 */
  HUMOROUS = 'humorous',
  /** 优雅型 - 强调品味和气质，满足品味展示需求 */
  ELEGANT = 'elegant',
  /** 创新型 - 强调前瞻和独特性，满足独特性需求 */
  INNOVATIVE = 'innovative',
  /** 传统型 - 强调文化和底蕴，满足文化认同需求 */
  TRADITIONAL = 'traditional',
  /** 简约型 - 强调简洁和纯粹，满足认知便利需求 */
  MINIMALIST = 'minimalist',
  /** 表达型 - 强调个性和情感，满足情感共鸣需求 */
  EXPRESSIVE = 'expressive'
}

/**
 * 心理需求映射接口
 *
 * 定义了创意模式与用户心理需求的映射关系
 */
export interface PsychologicalNeeds {
  /** 身份表达需求 [0-1] - 通过用户名表达个人身份和角色 */
  identity_expression: number
  /** 社交归属需求 [0-1] - 通过用户名获得群体认同和归属感 */
  social_belonging: number
  /** 美学愉悦需求 [0-1] - 通过用户名获得美学享受和愉悦感 */
  aesthetic_pleasure: number
  /** 认知便利需求 [0-1] - 用户名易于理解、记忆和传播 */
  cognitive_ease: number
  /** 情感共鸣需求 [0-1] - 通过用户名产生情感连接和共鸣 */
  emotional_resonance: number
}

/**
 * 使用场景适配接口
 *
 * 定义了创意模式在不同使用场景下的适用度
 */
export interface UsageScenarios {
  /** 专业场景适用度 [0-1] - 工作、商务等正式场合 */
  professional: number
  /** 社交场景适用度 [0-1] - 社交媒体、朋友圈等社交场合 */
  social: number
  /** 创意场景适用度 [0-1] - 艺术创作、设计等创意场合 */
  creative: number
  /** 正式场景适用度 [0-1] - 官方、学术等正式场合 */
  formal: number
  /** 休闲场景适用度 [0-1] - 游戏、娱乐等休闲场合 */
  casual: number
}

/**
 * 文化共鸣接口
 *
 * 定义了创意模式与不同文化背景的共鸣程度
 */
export interface CulturalResonance {
  /** 传统文化共鸣 [0-1] - 与传统文化的契合度 */
  traditional: number
  /** 现代文化共鸣 [0-1] - 与现代文化的契合度 */
  modern: number
  /** 国际化适应 [0-1] - 跨文化理解和接受度 */
  international: number
  /** 本土化特色 [0-1] - 本土文化特色和认同度 */
  local: number
}

/**
 * 创意模式心理学属性接口
 *
 * 定义了创意模式的心理学特征和适用性
 */
export interface CreativePatternPsychology {
  /** 心理需求映射 */
  psychological_needs: PsychologicalNeeds
  /** 适用场景 */
  usage_scenarios: UsageScenarios
  /** 文化共鸣 */
  cultural_resonance: CulturalResonance
}

/**
 * 模式规则接口
 *
 * 定义了创意模式的生成规则和约束条件
 */
export interface PatternRule {
  /** 执行步骤序号 */
  step: number
  /** 规则动作类型 */
  action: 'select' | 'combine' | 'transform' | 'validate'
  /** 规则参数 */
  parameters: Record<string, any>
  /** 失败回退规则 */
  fallback?: PatternRule
  /** 规则权重 */
  weight: number
  /** 规则描述 */
  description?: string
}

/**
 * 创意模式数据结构
 *
 * 基于第一性原理设计的创意模式完整定义，
 * 包含心理学基础、生成规则和效果评估
 */
export interface CreativePattern {
  /** 模式唯一标识符 */
  id: string
  /** 模式名称 */
  name: string
  /** 模式描述 */
  description: string
  /** 模式原型 */
  archetype: PatternArchetype
  /** 全局权重 [0-1] */
  weight: number
  /** 心理学属性 */
  psychology: CreativePatternPsychology
  /** 生成规则集合 */
  rules: PatternRule[]
  /** 约束条件 */
  constraints: CompatibilityRule[]
  /** 效果评分 [0-1] */
  effectiveness_score: number
  /** 用户偏好评分 [0-1] */
  user_preference_score: number
  /** 生成示例 */
  examples: string[]
  /** 反例（避免生成的类型） */
  anti_examples: string[]
  /** 创建时间戳 */
  created_at: number
  /** 模式版本 */
  version: string
  /** 模式代数（进化代数） */
  generation?: number
  /** 父模式ID（用于进化追踪） */
  parents?: string[]
}

/**
 * 语素数量约束
 */
interface MorphemeCountConstraint {
  min?: number
  max?: number
  exact?: number
}

/**
 * 类别要求约束
 */
interface CategoryRequiredConstraint {
  category: MorphemeCategory
  min_count?: number
  max_count?: number
  exact_count?: number
}

/**
 * 文化匹配约束
 */
interface CulturalMatchConstraint {
  context: CulturalContext | CulturalContext[]
  required: boolean
}

/**
 * 质量阈值约束
 */
interface QualityThresholdConstraint {
  min_score: number
  max_score?: number
}

/**
 * 模式规则
 */
export interface PatternRule {
  type: 'morpheme_count' | 'category_required' | 'cultural_match' | 'quality_threshold'
  constraint: MorphemeCountConstraint | CategoryRequiredConstraint | CulturalMatchConstraint | QualityThresholdConstraint
  weight: number
}

// ============================================================================
// 质量评估相关接口定义 (8维度评估体系)
// ============================================================================

/**
 * 质量评估维度接口
 *
 * 基于认知心理学和语言学理论定义的8维度质量评估体系
 */
export interface QualityDimensions {
  /** 创意性 [0-1] - 用户名的创新性和独特性 */
  creativity: number
  /** 记忆性 [0-1] - 用户名的易记性和传播性 */
  memorability: number
  /** 文化适配度 [0-1] - 与目标文化的契合程度 */
  cultural_fit: number
  /** 独特性 [0-1] - 用户名的稀有性和区分度 */
  uniqueness: number
  /** 发音友好度 [0-1] - 用户名的发音难易程度 */
  pronunciation: number
  /** 语义连贯性 [0-1] - 语素组合的语义逻辑性 */
  semantic_coherence: number
  /** 美学吸引力 [0-1] - 用户名的美学价值和吸引力 */
  aesthetic_appeal: number
  /** 实用性 [0-1] - 用户名在实际使用中的便利性 */
  practical_usability: number
}

/**
 * 质量评分结构
 *
 * 完整的质量评估结果，包含综合评分、分维度评分和改进建议
 */
export interface QualityScore {
  /** 综合质量评分 [0-1] */
  overall: number
  /** 分维度评分 */
  dimensions: QualityDimensions
  /** 评估置信度 [0-1] */
  confidence: number
  /** 评估耗时 (毫秒) */
  evaluation_time: number
  /** 算法版本 */
  algorithm_version: string
  /** 发现的问题 */
  issues: string[]
  /** 改进建议 */
  suggestions: string[]
  /** 评估时间戳 */
  timestamp: number
}

/**
 * 生成的用户名结构
 */
export interface GeneratedUsername {
  text: string
  pattern: string
  quality_score: QualityScore
  explanation: string
  components: MorphemeComponent[]
  metadata: {
    cultural_fit: number
    creativity: number
    memorability: number
    uniqueness: number
    generation_time: number
  }
}

/**
 * 语素组件
 */
export interface MorphemeComponent {
  morpheme: Morpheme
  position: number
  role: 'prefix' | 'root' | 'suffix' | 'modifier' | 'complement'
  contribution_score: number
}

/**
 * 兼容性规则
 */
export interface CompatibilityRule {
  id: string
  name: string
  description: string
  type: 'category_combination' | 'cultural_harmony' | 'semantic_coherence' | 'phonetic_beauty'
  conditions: CompatibilityCondition[]
  score_modifier: number
  is_blocking: boolean
}

/**
 * 兼容性条件值类型
 */
type CompatibilityConditionValue = 
  | string 
  | number 
  | boolean 
  | string[] 
  | number[]
  | MorphemeCategory 
  | CulturalContext
  | null
  | undefined

/**
 * 兼容性条件
 */
export interface CompatibilityCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than'
  value: CompatibilityConditionValue
  weight: number
}

/**
 * 生成上下文 (v3.0多语种支持)
 */
export interface GenerationContext {
  user_preferences?: UserPreferences
  cultural_preference: CulturalContext
  style_preference: StylePreference
  creativity_level: number
  quality_threshold: number
  patterns?: string[]
  exclude_patterns?: string[]
  context_keywords?: string[]

  // v3.0多语种新增字段
  target_language?: LanguageCode
  concept_preferences?: {
    categories?: ConceptCategory[]
    cultural_context?: CulturalContext | MultiDimensionalCulturalContext
    abstraction_level?: number
  }
  multilingual_options?: {
    enable_cross_lingual?: boolean
    fallback_languages?: LanguageCode[]
    maintain_semantic_consistency?: boolean
    translation_preference?: 'literal' | 'semantic' | 'cultural'
  }
  cultural_sensitivity?: number
}

/**
 * 用户偏好
 */
export interface UserPreferences {
  favorite_patterns: string[]
  cultural_preference: CulturalContext
  style_preference: StylePreference
  creativity_level: number
  quality_threshold: number
  excluded_categories: MorphemeCategory[]
  preferred_length: 'short' | 'medium' | 'long'
}

/**
 * 风格偏好枚举
 */
export type StylePreference =
  | 'humorous'      // 幽默风趣
  | 'artistic'      // 文艺雅致
  | 'cute'          // 可爱萌系
  | 'cool'          // 酷炫帅气
  | 'elegant'       // 优雅高贵
  | 'playful'       // 活泼俏皮
  | 'professional'  // 专业型

/**
 * 长度偏好枚举
 */
export type LengthPreference = 'short' | 'medium' | 'long'

/**
 * 采样条件
 */
export interface SampleCriteria {
  category?: MorphemeCategory
  cultural_context?: CulturalContext
  min_quality_score?: number
  max_quality_score?: number
  tags?: string[]
  exclude_ids?: string[]
  limit?: number
  random_seed?: number
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  response_time: {
    avg: number
    p95: number
    p99: number
    max: number
  }
  throughput: {
    requests_per_second: number
    concurrent_users: number
  }
  resource_usage: {
    memory_mb: number
    cpu_percent: number
  }
  cache_metrics: {
    hit_rate: number
    miss_rate: number
    eviction_rate: number
  }
  quality_metrics: {
    avg_quality_score: number
    generation_success_rate: number
  }
  timestamp: number
}

/**
 * 缓存键生成参数
 */
export interface CacheKeyParams {
  cultural_preference: CulturalContext
  style_preference: StylePreference
  creativity_level: number
  quality_threshold: number
  patterns?: string[]
  count: number
}

/**
 * 语义相似度计算结果
 */
export interface SemanticSimilarity {
  score: number
  dimensions: number[]
  explanation: string
}

/**
 * 算法配置
 */
export interface AlgorithmConfig {
  max_generation_attempts: number
  quality_threshold: number
  cache_ttl_seconds: number
  max_cache_size: number
  semantic_similarity_threshold: number
  cultural_weight: number
  creativity_weight: number
  quality_weight: number
}

/**
 * 引擎统计信息 morpheme 语素
 */
export interface EngineStats {
  morpheme_count: number
  morpheme_stats: {
    total: number
    byCategory: Record<string, number>
    byContext: Record<string, number>
    avgQuality: number
  }
  engine_status: 'ready' | 'not_initialized'
  total_generations: number
  avg_generation_time: number
  success_rate: number
}