/**
 * 核心数据类型定义
 * 定义namer-v6项目的核心数据结构和接口
 */

/**
 * 语素数据结构
 */
export interface Morpheme {
  id: string
  text: string
  category: MorphemeCategory
  subcategory: string
  cultural_context: CulturalContext
  usage_frequency: number
  quality_score: number
  semantic_vector: number[]
  tags: string[]
  created_at: number
  source: string
}

/**
 * 语素类别枚举
 */
export type MorphemeCategory =
  | 'emotions'        // 情感类
  | 'professions'     // 职业类
  | 'characteristics' // 特征类
  | 'objects'         // 物品类
  | 'actions'         // 动作类
  | 'concepts'        // 概念类
  | 'creative'        // 创意类

/**
 * 文化语境枚举
 */
export type CulturalContext =
  | 'ancient'   // 传统文化
  | 'modern'    // 现代流行
  | 'neutral'   // 中性通用

/**
 * 创意模式数据结构
 */
export interface CreativePattern {
  id: string
  name: string
  description: string
  template: string
  weight: number
  examples: string[]
  rules: PatternRule[]
  cultural_preference?: CulturalContext[]
  min_quality_score: number
}

/**
 * 模式规则
 */
export interface PatternRule {
  type: 'morpheme_count' | 'category_required' | 'cultural_match' | 'quality_threshold'
  constraint: any
  weight: number
}

/**
 * 质量评分结构
 */
export interface QualityScore {
  overall: number
  dimensions: {
    humor_creativity: number      // 幽默创意性
    cultural_resonance: number    // 文化共鸣度
    linguistic_beauty: number     // 语言美感度
    memorability: number          // 记忆传播力
    uniqueness: number            // 独特性
  }
  issues: string[]
  suggestions: string[]
}

/**
 * 生成的用户名结构
 */
export interface GeneratedUsername {
  text: string
  pattern: string
  quality_score: QualityScore
  explanation: string
  components: MorphemeComponent[]
  metadata: {
    cultural_fit: number
    creativity: number
    memorability: number
    uniqueness: number
    generation_time: number
  }
}

/**
 * 语素组件
 */
export interface MorphemeComponent {
  morpheme: Morpheme
  position: number
  role: 'prefix' | 'root' | 'suffix' | 'modifier'
  contribution_score: number
}

/**
 * 兼容性规则
 */
export interface CompatibilityRule {
  id: string
  name: string
  description: string
  type: 'category_combination' | 'cultural_harmony' | 'semantic_coherence' | 'phonetic_beauty'
  conditions: CompatibilityCondition[]
  score_modifier: number
  is_blocking: boolean
}

/**
 * 兼容性条件
 */
export interface CompatibilityCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than'
  value: any
  weight: number
}

/**
 * 生成上下文
 */
export interface GenerationContext {
  user_preferences?: UserPreferences
  cultural_preference: CulturalContext
  style_preference: StylePreference
  creativity_level: number
  quality_threshold: number
  patterns?: string[]
  exclude_patterns?: string[]
  context_keywords?: string[]
}

/**
 * 用户偏好
 */
export interface UserPreferences {
  favorite_patterns: string[]
  cultural_preference: CulturalContext
  style_preference: StylePreference
  creativity_level: number
  quality_threshold: number
  excluded_categories: MorphemeCategory[]
  preferred_length: 'short' | 'medium' | 'long'
}

/**
 * 风格偏好枚举
 */
export type StylePreference =
  | 'humorous'   // 幽默风趣
  | 'artistic'   // 文艺雅致
  | 'cute'       // 可爱萌系
  | 'cool'       // 酷炫帅气
  | 'elegant'    // 优雅高贵
  | 'playful'    // 活泼俏皮

/**
 * 采样条件
 */
export interface SampleCriteria {
  category?: MorphemeCategory
  cultural_context?: CulturalContext
  min_quality_score?: number
  max_quality_score?: number
  tags?: string[]
  exclude_ids?: string[]
  limit?: number
  random_seed?: number
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  response_time: {
    avg: number
    p95: number
    p99: number
    max: number
  }
  throughput: {
    requests_per_second: number
    concurrent_users: number
  }
  resource_usage: {
    memory_mb: number
    cpu_percent: number
  }
  cache_metrics: {
    hit_rate: number
    miss_rate: number
    eviction_rate: number
  }
  quality_metrics: {
    avg_quality_score: number
    generation_success_rate: number
  }
  timestamp: number
}

/**
 * 缓存键生成参数
 */
export interface CacheKeyParams {
  cultural_preference: CulturalContext
  style_preference: StylePreference
  creativity_level: number
  quality_threshold: number
  patterns?: string[]
  count: number
}

/**
 * 语义相似度计算结果
 */
export interface SemanticSimilarity {
  score: number
  dimensions: number[]
  explanation: string
}

/**
 * 算法配置
 */
export interface AlgorithmConfig {
  max_generation_attempts: number
  quality_threshold: number
  cache_ttl_seconds: number
  max_cache_size: number
  semantic_similarity_threshold: number
  cultural_weight: number
  creativity_weight: number
  quality_weight: number
}