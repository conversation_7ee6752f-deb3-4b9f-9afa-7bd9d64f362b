/**
 * 欧洲语言质量评估器单元测试
 * 
 * 测试西班牙语、法语、德语的质量评估功能
 * 包括基础8维评分和欧洲语言特有指标
 * 
 * @fileoverview 欧洲语言质量评估器测试套件
 * @version 3.0.0
 * @since 2025-06-24
 */

// Jest globals are available without import
import { EuropeanQualityAssessor } from '../core/quality/EuropeanQualityAssessor'
import { LanguageCode, RegisterLevel } from '../types/multilingual'

// 测试专用的简化接口，匹配实际数据文件结构
interface TestMorpheme {
  morpheme_id: string
  text: string
  universal_concept_id: string
  concept_category: string
  semantic_vector: number[]
  phonetic_features?: {
    ipa_transcription?: string
    syllable_count?: number
    stress_position?: number
    romanization?: string
  }
  morphological_info?: {
    root?: string
    prefixes?: string[]
    suffixes?: string[]
    word_class?: string
    inflection_info?: {
      gender?: string
      number?: string
      case?: string
    }
    derivation_info?: {
      base_word?: string
      derivation_type?: string
      productivity?: number
    }
    compound_structure?: string[]
  }
  cultural_context?: {
    formality?: number
    regionality?: number
    traditionality?: number
    modernity?: number
    aesthetic_value?: number
    religious_sensitivity?: number
    age_appropriateness?: string[]
    cultural_tags?: string[]
  }
  usage_frequency: number
  semantic_rarity: number
  register_level: RegisterLevel
  created_at: string
  updated_at: string
}

describe('EuropeanQualityAssessor', () => {
  let assessor: EuropeanQualityAssessor

  beforeEach(() => {
    assessor = new EuropeanQualityAssessor()
  })

  describe('Language Support', () => {
    it('should support Spanish language', () => {
      expect(assessor.supportsLanguage(LanguageCode.ES_ES)).toBe(true)
    })

    it('should support French language', () => {
      expect(assessor.supportsLanguage(LanguageCode.FR_FR)).toBe(true)
    })

    it('should support German language', () => {
      expect(assessor.supportsLanguage(LanguageCode.DE_DE)).toBe(true)
    })

    it('should not support unsupported languages', () => {
      expect(assessor.supportsLanguage(LanguageCode.EN_US)).toBe(false)
      expect(assessor.supportsLanguage(LanguageCode.JA_JP)).toBe(false)
    })
  })

  describe('Spanish Quality Assessment', () => {
    const spanishMorpheme: TestMorpheme = {
      morpheme_id: 'test_es_001',
      text: 'corazón',
      universal_concept_id: 'test_concept_001',
      concept_category: 'emotion',
      semantic_vector: [0.9, 0.8, 0.2, 0.1, 0.8, 0.9, 0.85, 0.7],
      phonetic_features: {
        ipa_transcription: '[koɾaˈθon]',
        syllable_count: 3,
        stress_position: 3,
        romanization: 'corazon'
      },
      morphological_info: {
        root: 'corazón',
        prefixes: [],
        suffixes: [],
        inflection_info: {
          gender: 'masculine',
          number: 'singular'
        },
        word_class: 'noun'
      },
      cultural_context: {
        formality: 0.6,
        regionality: 0.9,
        traditionality: 0.95,
        modernity: 0.5,
        aesthetic_value: 0.95,
        religious_sensitivity: 0.3,
        age_appropriateness: ['all'],
        cultural_tags: ['emotion', 'traditional']
      },
      usage_frequency: 0.9,
      semantic_rarity: 0.2,
      register_level: RegisterLevel.NEUTRAL,
      created_at: '2025-06-24T00:00:00Z',
      updated_at: '2025-06-24T00:00:00Z'
    }

    it('should assess Spanish morpheme quality', () => {
      const result = assessor.assessQuality(spanishMorpheme, LanguageCode.ES_ES)

      expect(result).toBeDefined()
      expect(result.phonetic_harmony).toBeGreaterThanOrEqual(0)
      expect(result.phonetic_harmony).toBeLessThanOrEqual(1)
      expect(result.morphological_appropriateness).toBeGreaterThanOrEqual(0)
      expect(result.morphological_appropriateness).toBeLessThanOrEqual(1)
      expect(result.cultural_elegance).toBeGreaterThanOrEqual(0)
      expect(result.cultural_elegance).toBeLessThanOrEqual(1)
      expect(result.linguistic_authenticity).toBeGreaterThanOrEqual(0)
      expect(result.linguistic_authenticity).toBeLessThanOrEqual(1)
    })

    it('should include European-specific metrics for Spanish', () => {
      const result = assessor.assessQuality(spanishMorpheme, LanguageCode.ES_ES)

      expect(result.phonetic_harmony).toBeGreaterThanOrEqual(0)
      expect(result.phonetic_harmony).toBeLessThanOrEqual(1)
      expect(result.morphological_appropriateness).toBeGreaterThanOrEqual(0)
      expect(result.morphological_appropriateness).toBeLessThanOrEqual(1)
      expect(result.cultural_elegance).toBeGreaterThanOrEqual(0)
      expect(result.cultural_elegance).toBeLessThanOrEqual(1)
      expect(result.international_adaptability).toBeGreaterThanOrEqual(0)
    })

    it('should give high scores for traditional Spanish words', () => {
      const result = assessor.assessQuality(spanishMorpheme, LanguageCode.ES_ES)

      expect(result.linguistic_authenticity).toBeGreaterThan(0.6)
      expect(result.cultural_elegance).toBeGreaterThan(0.5)
    })
  })

  describe('French Quality Assessment', () => {
    const frenchMorpheme: TestMorpheme = {
      morpheme_id: 'test_fr_001',
      text: 'amour',
      universal_concept_id: 'test_concept_002',
      concept_category: 'emotion',
      semantic_vector: [0.95, 0.9, 0.2, 0.1, 0.8, 0.95, 0.9, 0.7],
      phonetic_features: {
        ipa_transcription: '[amuʁ]',
        syllable_count: 2,
        stress_position: 2,
        romanization: 'amour'
      },
      morphological_info: {
        root: 'amour',
        prefixes: [],
        suffixes: [],
        inflection_info: {
          gender: 'masculine',
          number: 'singular'
        },
        word_class: 'noun'
      },
      cultural_context: {
        formality: 0.8,
        regionality: 0.9,
        traditionality: 0.95,
        modernity: 0.8,
        aesthetic_value: 0.98
      },
      usage_frequency: 0.9,
      semantic_rarity: 0.2,
      register_level: RegisterLevel.NEUTRAL,
      created_at: '2025-06-24T00:00:00Z',
      updated_at: '2025-06-24T00:00:00Z'
    }

    it('should assess French morpheme quality', () => {
      const result = assessor.assessQuality(frenchMorpheme, LanguageCode.FR_FR)

      expect(result).toBeDefined()
      expect(result.phonetic_harmony).toBeGreaterThanOrEqual(0)
      expect(result.phonetic_harmony).toBeLessThanOrEqual(1)
      expect(result.cultural_elegance).toBeGreaterThanOrEqual(0)
      expect(result.cultural_elegance).toBeLessThanOrEqual(1)
    })

    it('should give high elegance scores for French words', () => {
      const result = assessor.assessQuality(frenchMorpheme, LanguageCode.FR_FR)

      expect(result.cultural_elegance).toBeGreaterThan(0.7)
      expect(result.pronunciation_friendliness).toBeGreaterThan(0.6)
    })

    it('should assess French phonetic harmony', () => {
      const result = assessor.assessQuality(frenchMorpheme, LanguageCode.FR_FR)

      expect(result.phonetic_harmony).toBeGreaterThan(0.4)
    })
  })

  describe('German Quality Assessment', () => {
    const germanMorpheme: TestMorpheme = {
      morpheme_id: 'test_de_001',
      text: 'Freiheit',
      universal_concept_id: 'test_concept_003',
      concept_category: 'abstract',
      semantic_vector: [0.9, 0.8, 0.5, 0.4, 0.9, 0.8, 0.8, 0.8],
      phonetic_features: {
        ipa_transcription: '[ˈfʁaɪhaɪt]',
        syllable_count: 2,
        stress_position: 1,
        romanization: 'Freiheit'
      },
      morphological_info: {
        root: 'frei',
        prefixes: [],
        suffixes: ['heit'],
        inflection_info: {
          gender: 'feminine',
          number: 'singular',
          case: 'nominative'
        },
        derivation_info: {
          base_word: 'frei',
          derivation_type: 'nominalization',
          productivity: 0.9
        },
        word_class: 'noun'
      },
      cultural_context: {
        formality: 0.9,
        regionality: 0.6,
        traditionality: 0.8,
        modernity: 0.9,
        aesthetic_value: 0.8
      },
      usage_frequency: 0.8,
      semantic_rarity: 0.4,
      register_level: RegisterLevel.FORMAL,
      created_at: '2025-06-24T00:00:00Z',
      updated_at: '2025-06-24T00:00:00Z'
    }

    it('should assess German morpheme quality', () => {
      const result = assessor.assessQuality(germanMorpheme, LanguageCode.DE_DE)

      expect(result).toBeDefined()
      expect(result.phonetic_harmony).toBeGreaterThanOrEqual(0)
      expect(result.phonetic_harmony).toBeLessThanOrEqual(1)
      expect(result.morphological_appropriateness).toBeGreaterThanOrEqual(0)
      expect(result.morphological_appropriateness).toBeLessThanOrEqual(1)
    })

    it('should give high precision scores for German words', () => {
      const result = assessor.assessQuality(germanMorpheme, LanguageCode.DE_DE)

      expect(result.morphological_appropriateness).toBeGreaterThan(0.5)
    })

    it('should assess German morphological complexity', () => {
      const result = assessor.assessQuality(germanMorpheme, LanguageCode.DE_DE)

      expect(result.morphological_appropriateness).toBeDefined()
      expect(result.morphological_appropriateness).toBeGreaterThan(0.4)
    })

    it('should handle German compound words', () => {
      const compoundMorpheme = {
        ...germanMorpheme,
        text: 'Sonnenschein',
        morphological_info: {
          ...germanMorpheme.morphological_info,
          compound_structure: ['Sonne', 'Schein']
        }
      }

      const result = assessor.assessQuality(compoundMorpheme, LanguageCode.DE_DE)

      expect(result).toBeDefined()
      expect(result.morphological_appropriateness).toBeGreaterThan(0.3)
    })
  })

  describe('Cross-Language Comparison', () => {
    const testMorphemes = {
      spanish: {
        morpheme_id: 'test_es_joy',
        text: 'alegría',
        universal_concept_id: 'joy_001',
        concept_category: 'emotion',
        semantic_vector: [0.9, 0.8, 0.1, 0.2, 0.7, 0.8, 0.9, 0.6],
        cultural_context: {
          formality: 0.7,
          regionality: 0.8,
          traditionality: 0.85,
          modernity: 0.6,
          aesthetic_value: 0.9
        },
        usage_frequency: 0.85,
        semantic_rarity: 0.3,
        register_level: 'neutral' as const,
        created_at: Date.now(),
        updated_at: Date.now()
      },
      french: {
        morpheme_id: 'test_fr_joy',
        text: 'joie',
        universal_concept_id: 'joy_001',
        concept_category: 'emotion',
        semantic_vector: [0.9, 0.85, 0.1, 0.2, 0.7, 0.9, 0.85, 0.6],
        cultural_context: {
          formality: 0.7,
          regionality: 0.8,
          traditionality: 0.85,
          modernity: 0.7,
          aesthetic_value: 0.9
        },
        usage_frequency: 0.85,
        semantic_rarity: 0.3,
        register_level: 'neutral' as const,
        created_at: Date.now(),
        updated_at: Date.now()
      },
      german: {
        morpheme_id: 'test_de_joy',
        text: 'Freude',
        universal_concept_id: 'joy_001',
        concept_category: 'emotion',
        semantic_vector: [0.9, 0.8, 0.1, 0.2, 0.7, 0.85, 0.8, 0.6],
        cultural_context: {
          formality: 0.7,
          regionality: 0.8,
          traditionality: 0.85,
          modernity: 0.7,
          aesthetic_value: 0.8
        },
        usage_frequency: 0.85,
        semantic_rarity: 0.3,
        register_level: 'neutral' as const,
        created_at: Date.now(),
        updated_at: Date.now()
      }
    }

    it('should provide consistent quality assessment across languages', () => {
      const spanishResult = assessor.assessQuality(testMorphemes.spanish, LanguageCode.ES_ES)
      const frenchResult = assessor.assessQuality(testMorphemes.french, LanguageCode.FR_FR)
      const germanResult = assessor.assessQuality(testMorphemes.german, LanguageCode.DE_DE)

      // All should have reasonable quality scores
      expect(spanishResult.phonetic_harmony).toBeGreaterThan(0.3)
      expect(frenchResult.phonetic_harmony).toBeGreaterThan(0.3)
      expect(germanResult.phonetic_harmony).toBeGreaterThan(0.3)

      // French should score highest on elegance
      expect(frenchResult.cultural_elegance)
        .toBeGreaterThanOrEqual(spanishResult.cultural_elegance)
    })
  })

  describe('Error Handling', () => {
    it('should throw error for unsupported language', () => {
      const morpheme: TestMorpheme = {
        morpheme_id: 'test_001',
        text: 'test',
        universal_concept_id: 'test_concept',
        concept_category: 'test',
        semantic_vector: [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
        usage_frequency: 0.5,
        semantic_rarity: 0.5,
        register_level: RegisterLevel.NEUTRAL,
        created_at: '2025-06-24T00:00:00Z',
        updated_at: '2025-06-24T00:00:00Z'
      }

      expect(() => {
        assessor.assessQuality(morpheme, LanguageCode.EN_US)
      }).toThrow('Unsupported European language')
    })

    it('should handle missing cultural context gracefully', () => {
      const morpheme: TestMorpheme = {
        morpheme_id: 'test_001',
        text: 'test',
        universal_concept_id: 'test_concept',
        concept_category: 'test',
        semantic_vector: [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
        usage_frequency: 0.5,
        semantic_rarity: 0.5,
        register_level: RegisterLevel.NEUTRAL,
        created_at: '2025-06-24T00:00:00Z',
        updated_at: '2025-06-24T00:00:00Z'
      }

      expect(() => {
        assessor.assessQuality(morpheme, LanguageCode.ES_ES)
      }).not.toThrow()

      const result = assessor.assessQuality(morpheme, LanguageCode.ES_ES)
      expect(result.linguistic_authenticity).toBeGreaterThanOrEqual(0)
    })
  })
})
