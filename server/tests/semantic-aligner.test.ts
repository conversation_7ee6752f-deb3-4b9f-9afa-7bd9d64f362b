/**
 * SemanticAligner 测试套件
 * 
 * 测试语义对齐器的核心功能，包括：
 * - 语义对齐器初始化和配置
 * - 概念-语素语义对齐计算
 * - 语义相似度计算
 * - 文化适配度评估
 * - 跨语言映射构建
 * - 最佳对齐查找
 * - 错误处理和边界条件
 * 
 * @fileoverview SemanticAligner 功能测试
 * @version 3.0.0
 * @since 2025-06-25
 * <AUTHOR> team
 */

import { jest } from '@jest/globals'
import { SemanticAligner, SemanticAlignerConfig, SemanticAlignment, CrossLingualMapping } from '../core/multilingual/SemanticAligner'
import { LanguageCode, ConceptCategory, RegisterLevel, SEMANTIC_VECTOR_DIMENSIONS } from '../types/multilingual'
import type { LanguageSpecificMorpheme, UniversalConcept, UniversalSemanticVector } from '../types/multilingual'

describe('SemanticAligner', () => {
  let semanticAligner: SemanticAligner
  
  // Mock数据 - 通用概念
  const mockUniversalConcept: UniversalConcept = {
    concept_id: "concept_wisdom",
    semantic_vector: {
      vector: Array(512).fill(0.5),
      model_version: "mBERT-v1.0",
      confidence: 0.9,
      updated_at: new Date().toISOString(),
      legacy_vector: Array(20).fill(0.5)
    },
    concept_category: ConceptCategory.CHARACTERISTICS,
    abstraction_level: 0.8,
    cultural_neutrality: 0.7,
    cross_lingual_stability: 0.9,
    cognitive_load: 0.6,
    memorability_score: 0.8,
    emotional_valence: 0.3,
    cognitive_attributes: {
      memorability: 0.8,
      cognitive_load: 0.6,
      emotional_valence: 0.3
    },
    related_concepts: ["concept_intelligence", "concept_knowledge"],
    hierarchical_parent: "concept_mental_attributes",
    hierarchical_children: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: "3.0.0"
  }

  // Mock数据 - 语言特定语素
  const mockEnglishMorpheme: LanguageSpecificMorpheme = {
    morpheme_id: "en_001",
    concept_id: "concept_wisdom",
    language: LanguageCode.EN_US,
    text: "wisdom",
    alternative_forms: ["wise", "sagacity"],
    phonetic_features: {
      ipa_transcription: "/ˈwɪzdəm/",
      syllable_count: 2,
      tone_pattern: [],
      phonetic_harmony: 0.8
    },
    morphological_info: {
      pos_tag: "NOUN",
      morphological_type: "root",
      prefixes: [],
      suffixes: []
    },
    syntactic_properties: {
      syntactic_function: ["subject", "object"],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      traditionality: 0.7,
      modernity: 0.6,
      formality: 0.8,
      regionality: 0.3,
      religious_sensitivity: 0.2,
      age_appropriateness: ["all"],
      cultural_tags: ["wisdom", "knowledge"]
    },
    regional_variants: [],
    register_level: RegisterLevel.FORMAL,
    language_quality_scores: {
      naturalness: 0.9,
      fluency: 0.9,
      authenticity: 0.9,
      aesthetic_appeal: 0.8,
      pronunciation_ease: 0.8,
      memorability: 0.8,
      uniqueness: 0.7,
      practicality: 0.9
    },
    cultural_appropriateness: 0.9,
    native_speaker_rating: 0.9,
    usage_frequency: 0.8,
    popularity_trend: 0.1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: "3.0.0",
    source: "english_morphemes_v3",
    validation_status: "validated"
  }

  const mockChineseMorpheme: LanguageSpecificMorpheme = {
    morpheme_id: "zh_001",
    concept_id: "concept_wisdom",
    language: LanguageCode.ZH_CN,
    text: "智慧",
    alternative_forms: ["智", "慧"],
    phonetic_features: {
      ipa_transcription: "/ʈʂɨ̀.xwêɪ̯/",
      syllable_count: 2,
      tone_pattern: ["4", "4"], // 去声，去声
      phonetic_harmony: 0.7
    },
    morphological_info: {
      pos_tag: "NOUN",
      morphological_type: "compound",
      prefixes: [],
      suffixes: []
    },
    syntactic_properties: {
      syntactic_function: ["subject", "object"],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      traditionality: 0.9,
      modernity: 0.7,
      formality: 0.8,
      regionality: 0.2,
      religious_sensitivity: 0.1,
      age_appropriateness: ["all"],
      cultural_tags: ["wisdom", "philosophy", "traditional"]
    },
    regional_variants: [],
    register_level: RegisterLevel.FORMAL,
    language_quality_scores: {
      naturalness: 0.9,
      fluency: 0.9,
      authenticity: 0.95,
      aesthetic_appeal: 0.9,
      pronunciation_ease: 0.7,
      memorability: 0.9,
      uniqueness: 0.8,
      practicality: 0.8
    },
    cultural_appropriateness: 0.95,
    native_speaker_rating: 0.9,
    usage_frequency: 0.8,
    popularity_trend: 0.0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: "3.0.0",
    source: "chinese_morphemes_adapted",
    validation_status: "validated"
  }

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()
  })

  describe('Constructor', () => {
    it('应该使用默认配置创建SemanticAligner实例', () => {
      semanticAligner = new SemanticAligner()
      
      expect(semanticAligner).toBeInstanceOf(SemanticAligner)
    })

    it('应该使用自定义配置创建SemanticAligner实例', () => {
      const customConfig: SemanticAlignerConfig = {
        semanticSimilarityThreshold: 0.8,
        culturalFitWeight: 0.4,
        semanticWeight: 0.4,
        qualityWeight: 0.2
      }
      
      semanticAligner = new SemanticAligner(customConfig)
      
      expect(semanticAligner).toBeInstanceOf(SemanticAligner)
    })
  })

  describe('Concept-Morpheme Alignment', () => {
    beforeEach(() => {
      semanticAligner = new SemanticAligner({
        semanticSimilarityThreshold: 0.7,
        culturalFitWeight: 0.3,
        semanticWeight: 0.5,
        qualityWeight: 0.2
      })
    })

    it('应该计算概念与语素的语义对齐度', () => {
      const alignment = semanticAligner.calculateConceptMorphemeAlignment(
        mockUniversalConcept,
        mockEnglishMorpheme
      )
      
      expect(alignment).toBeDefined()
      expect(alignment.sourceConceptId).toBe('concept_wisdom')
      expect(alignment.targetLanguage).toBe(LanguageCode.EN_US)
      expect(alignment.alignedMorphemes).toHaveLength(1)
      expect(alignment.alignedMorphemes[0]).toBe(mockEnglishMorpheme)
      expect(alignment.alignmentScore).toBeGreaterThan(0)
      expect(alignment.alignmentScore).toBeLessThanOrEqual(1)
      expect(alignment.semanticSimilarity).toBeGreaterThan(0)
      expect(alignment.semanticSimilarity).toBeLessThanOrEqual(1)
      expect(alignment.culturalFit).toBeGreaterThan(0)
      expect(alignment.culturalFit).toBeLessThanOrEqual(1)
      expect(alignment.confidence).toBeGreaterThan(0)
      expect(alignment.confidence).toBeLessThanOrEqual(1)
    })

    it('应该为中文语素计算对齐度', () => {
      const alignment = semanticAligner.calculateConceptMorphemeAlignment(
        mockUniversalConcept,
        mockChineseMorpheme
      )
      
      expect(alignment).toBeDefined()
      expect(alignment.sourceConceptId).toBe('concept_wisdom')
      expect(alignment.targetLanguage).toBe(LanguageCode.ZH_CN)
      expect(alignment.alignedMorphemes[0]).toBe(mockChineseMorpheme)
      
      // 中文语素应该有较高的文化适配度
      expect(alignment.culturalFit).toBeGreaterThan(0.7)
    })

    it('应该正确计算综合对齐分数', () => {
      const alignment = semanticAligner.calculateConceptMorphemeAlignment(
        mockUniversalConcept,
        mockEnglishMorpheme
      )
      
      // 验证对齐分数是语义、文化和质量权重的加权平均
      const expectedScore = 
        0.5 * alignment.semanticSimilarity +
        0.3 * alignment.culturalFit +
        0.2 * (alignment.alignmentScore - 0.5 * alignment.semanticSimilarity - 0.3 * alignment.culturalFit) / 0.2
      
      expect(alignment.alignmentScore).toBeCloseTo(expectedScore, 2)
    })
  })

  describe('Best Alignment Finding', () => {
    beforeEach(() => {
      semanticAligner = new SemanticAligner({
        semanticSimilarityThreshold: 0.6,
        culturalFitWeight: 0.3,
        semanticWeight: 0.5,
        qualityWeight: 0.2
      })
    })

    it('应该找到最佳对齐语素', () => {
      const candidateMorphemes = [mockEnglishMorpheme, mockChineseMorpheme]
      
      const bestAlignment = semanticAligner.findBestAlignment(
        mockUniversalConcept,
        candidateMorphemes
      )
      
      expect(bestAlignment).toBeDefined()
      expect(bestAlignment?.sourceConceptId).toBe('concept_wisdom')
      expect(bestAlignment?.alignedMorphemes).toHaveLength(1)
      expect(bestAlignment?.alignmentScore).toBeGreaterThan(0.6)
    })

    it('应该处理空候选列表', () => {
      const bestAlignment = semanticAligner.findBestAlignment(
        mockUniversalConcept,
        []
      )
      
      expect(bestAlignment).toBeNull()
    })

    it('应该处理低于阈值的对齐', () => {
      // 使用高阈值，使所有候选都不符合要求
      const highThresholdAligner = new SemanticAligner({
        semanticSimilarityThreshold: 0.99,
        culturalFitWeight: 0.3,
        semanticWeight: 0.5,
        qualityWeight: 0.2
      })
      
      const bestAlignment = highThresholdAligner.findBestAlignment(
        mockUniversalConcept,
        [mockEnglishMorpheme]
      )
      
      expect(bestAlignment).toBeNull()
    })
  })

  describe('Cross-Lingual Mapping', () => {
    beforeEach(() => {
      semanticAligner = new SemanticAligner()
    })

    it('应该构建跨语言映射', () => {
      const languageMorphemes = new Map<LanguageCode, LanguageSpecificMorpheme[]>()
      languageMorphemes.set(LanguageCode.EN_US, [mockEnglishMorpheme])
      languageMorphemes.set(LanguageCode.ZH_CN, [mockChineseMorpheme])
      
      const crossLingualMapping = semanticAligner.buildCrossLingualMapping(
        mockUniversalConcept,
        languageMorphemes
      )
      
      expect(crossLingualMapping).toBeDefined()
      expect(crossLingualMapping.conceptId).toBe('concept_wisdom')
      expect(crossLingualMapping.languageMorphemes.size).toBe(2)
      expect(crossLingualMapping.languageMorphemes.get(LanguageCode.EN_US)).toEqual([mockEnglishMorpheme])
      expect(crossLingualMapping.languageMorphemes.get(LanguageCode.ZH_CN)).toEqual([mockChineseMorpheme])
      expect(crossLingualMapping.consistencyScore).toBeGreaterThan(0)
      expect(crossLingualMapping.consistencyScore).toBeLessThanOrEqual(1)
      expect(crossLingualMapping.bestAlignments).toBeDefined()
      expect(crossLingualMapping.bestAlignments.length).toBeGreaterThan(0)
    })

    it('应该处理单语言映射', () => {
      const languageMorphemes = new Map<LanguageCode, LanguageSpecificMorpheme[]>()
      languageMorphemes.set(LanguageCode.EN_US, [mockEnglishMorpheme])
      
      const crossLingualMapping = semanticAligner.buildCrossLingualMapping(
        mockUniversalConcept,
        languageMorphemes
      )
      
      expect(crossLingualMapping.languageMorphemes.size).toBe(1)
      expect(crossLingualMapping.bestAlignments).toHaveLength(0) // 单语言无法形成语言对
    })

    it('应该处理空映射', () => {
      const languageMorphemes = new Map<LanguageCode, LanguageSpecificMorpheme[]>()
      
      const crossLingualMapping = semanticAligner.buildCrossLingualMapping(
        mockUniversalConcept,
        languageMorphemes
      )
      
      expect(crossLingualMapping.languageMorphemes.size).toBe(0)
      expect(crossLingualMapping.consistencyScore).toBe(1.0) // 空映射时返回1.0
      expect(crossLingualMapping.bestAlignments).toHaveLength(0)
    })
  })
})
