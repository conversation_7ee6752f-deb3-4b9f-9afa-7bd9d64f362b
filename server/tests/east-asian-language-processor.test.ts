/**
 * EastAsianLanguageProcessor 测试套件
 * 
 * 测试东亚语言处理器的核心功能，包括：
 * - 中文、日语、韩语的语言特性处理
 * - 汉字、假名、韩文的信息提取
 * - 敬语系统处理
 * - 语音特征分析
 * 
 * @fileoverview 东亚语言处理器测试
 * @version 3.0.0
 */

import { EastAsianLanguageProcessor } from '../core/multilingual/EastAsianLanguageProcessor'
import { LanguageCode, ConceptCategory, RegisterLevel } from '../types/multilingual'
import type {
  LanguageSpecificMorpheme,
  PhoneticFeatures,
  MorphologicalInfo,
  MultiDimensionalCulturalContext
} from '../types/multilingual'

describe('EastAsianLanguageProcessor', () => {
  let processor: EastAsianLanguageProcessor

  beforeEach(() => {
    processor = new EastAsianLanguageProcessor()
  })

  describe('Constructor', () => {
    it('应该正确初始化东亚语言处理器', () => {
      expect(processor).toBeInstanceOf(EastAsianLanguageProcessor)
    })

    it('应该初始化所有东亚语言配置', () => {
      expect(processor.isEastAsianLanguage(LanguageCode.ZH_CN)).toBe(true)
      expect(processor.isEastAsianLanguage(LanguageCode.JA_JP)).toBe(true)
      expect(processor.isEastAsianLanguage(LanguageCode.KO_KR)).toBe(true)
      expect(processor.isEastAsianLanguage(LanguageCode.EN_US)).toBe(false)
    })
  })

  describe('Language Configuration', () => {
    it('应该返回中文配置', () => {
      const config = processor.getLanguageConfig(LanguageCode.ZH_CN)
      expect(config).toBeDefined()
      expect(config?.language).toBe(LanguageCode.ZH_CN)
      expect(config?.uses_hanzi).toBe(true)
      expect(config?.has_tones).toBe(true)
      expect(config?.has_honorifics).toBe(false)
      expect(config?.syllable_counting).toBe('syllable')
      expect(config?.writing_systems).toContain('logographic')
    })

    it('应该返回日语配置', () => {
      const config = processor.getLanguageConfig(LanguageCode.JA_JP)
      expect(config).toBeDefined()
      expect(config?.language).toBe(LanguageCode.JA_JP)
      expect(config?.uses_hanzi).toBe(true)
      expect(config?.has_tones).toBe(false)
      expect(config?.has_honorifics).toBe(true)
      expect(config?.syllable_counting).toBe('mora')
      expect(config?.writing_systems).toContain('logographic')
      expect(config?.writing_systems).toContain('syllabic')
    })

    it('应该返回韩语配置', () => {
      const config = processor.getLanguageConfig(LanguageCode.KO_KR)
      expect(config).toBeDefined()
      expect(config?.language).toBe(LanguageCode.KO_KR)
      expect(config?.uses_hanzi).toBe(false)
      expect(config?.has_tones).toBe(false)
      expect(config?.has_honorifics).toBe(true)
      expect(config?.syllable_counting).toBe('syllable')
      expect(config?.writing_systems).toContain('alphabetic')
    })

    it('应该为不支持的语言返回undefined', () => {
      const config = processor.getLanguageConfig(LanguageCode.EN_US)
      expect(config).toBeUndefined()
    })
  })

  // 创建测试用的日语语素
  const createJapaneseMorpheme = (text: string, alternatives: string[] = []): LanguageSpecificMorpheme => ({
    morpheme_id: 'test-jp-1',
    text,
    language: LanguageCode.JA_JP,
    concept_id: 'test-concept',
    register_level: RegisterLevel.NEUTRAL,
    alternative_forms: alternatives,
    phonetic_features: {
      ipa_transcription: 'kawa',
      syllable_count: 2,
      stress_pattern: 'HL',
      tone_pattern: [],
      phonetic_harmony: 0.8
    },
    morphological_info: {
      pos_tag: 'NOUN',
      morphological_type: 'simple',
      prefixes: [],
      suffixes: [],
      compound_structure: []
    },
    syntactic_properties: {
      syntactic_function: ['subject', 'object'],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      formality: 0.5,
      traditionality: 0.5,
      regionality: 0.5,
      modernity: 0.5,
      religious_sensitivity: 0.5,
      age_appropriateness: ['all'],
      cultural_tags: ['neutral']
    },
    regional_variants: [],
    language_quality_scores: {
      naturalness: 0.8,
      fluency: 0.9,
      authenticity: 0.8,
      aesthetic_appeal: 0.7,
      pronunciation_ease: 0.8,
      memorability: 0.7,
      uniqueness: 0.6,
      practicality: 0.8
    },
    cultural_appropriateness: 0.8,
    native_speaker_rating: 0.8,
    usage_frequency: 0.7,
    popularity_trend: 0.6,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: '3.0.0',
    source: 'test',
    validation_status: 'validated' as const
  })

  describe('Japanese Morpheme Processing', () => {
    it('应该处理平假名文本', () => {
      const morpheme = createJapaneseMorpheme('かわ', ['カワ', '川'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.phonetic_features?.mora_count).toBe(2)
      expect(processed.phonetic_features?.accent_pattern).toBe('HL')
      expect(processed.phonetic_features?.romanization).toBe('かわ')
    })

    it('应该处理片假名文本', () => {
      const morpheme = createJapaneseMorpheme('カワ', ['かわ', '川'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.phonetic_features?.mora_count).toBe(2)
      expect(processed.phonetic_features?.accent_pattern).toBe('HL')
      // 当有平假名替代形式时，会优先使用平假名生成罗马音
      expect(processed.phonetic_features?.romanization).toBe('かわ')
    })

    it('应该处理汉字信息', () => {
      const morpheme = createJapaneseMorpheme('かわ', ['川'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.morphological_info?.kanji_info).toBeDefined()
      expect(processed.morphological_info?.kanji_info?.kanji).toBe('川')
      expect(processed.morphological_info?.kanji_info?.stroke_count).toBeGreaterThan(0)
      expect(processed.morphological_info?.kanji_info?.radical).toBe('川')
    })

    it('应该处理敬语标记', () => {
      const morpheme = createJapaneseMorpheme('田中さん')
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.8)
      expect(processed.cultural_context.traditionality).toBeGreaterThanOrEqual(0.85)
    })

    it('应该处理长音标记', () => {
      const morpheme = createJapaneseMorpheme('コーヒー')
      const processed = processor.processJapaneseMorpheme(morpheme)

      // 长音标记应该被检测到
      expect(processed.text).toContain('ー')
    })

    it('应该处理促音标记', () => {
      const morpheme = createJapaneseMorpheme('がっこう')
      const processed = processor.processJapaneseMorpheme(morpheme)

      // 促音标记应该被检测到
      expect(processed.text).toContain('っ')
    })

    it('应该处理不包含假名的文本', () => {
      const morpheme = createJapaneseMorpheme('test', [])
      const processed = processor.processJapaneseMorpheme(morpheme)

      // 应该保持原始文本不变
      expect(processed.text).toBe('test')
      expect(processed.phonetic_features?.mora_count).toBeUndefined()
    })

    it('应该处理多种敬语标记', () => {
      const honorifics = ['さん', 'さま', 'せんせい', 'くん', 'ちゃん']

      honorifics.forEach(honorific => {
        const morpheme = createJapaneseMorpheme(`田中${honorific}`)
        const processed = processor.processJapaneseMorpheme(morpheme)

        expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.8)
        expect(processed.cultural_context.traditionality).toBeGreaterThanOrEqual(0.85)
      })
    })
  })

  // 创建测试用的韩语语素
  const createKoreanMorpheme = (text: string, alternatives: string[] = []): LanguageSpecificMorpheme => ({
    morpheme_id: 'test-ko-1',
    text,
    language: LanguageCode.KO_KR,
    concept_id: 'test-concept',
    register_level: RegisterLevel.NEUTRAL,
    alternative_forms: alternatives,
    phonetic_features: {
      ipa_transcription: 'hangul',
      syllable_count: 2,
      stress_pattern: 'HL',
      tone_pattern: [],
      phonetic_harmony: 0.8
    },
    morphological_info: {
      pos_tag: 'NOUN',
      morphological_type: 'simple',
      prefixes: [],
      suffixes: [],
      compound_structure: []
    },
    syntactic_properties: {
      syntactic_function: ['subject', 'object'],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      formality: 0.5,
      traditionality: 0.5,
      regionality: 0.5,
      modernity: 0.5,
      religious_sensitivity: 0.5,
      age_appropriateness: ['all'],
      cultural_tags: ['neutral']
    },
    regional_variants: [],
    language_quality_scores: {
      naturalness: 0.8,
      fluency: 0.9,
      authenticity: 0.8,
      aesthetic_appeal: 0.7,
      pronunciation_ease: 0.8,
      memorability: 0.7,
      uniqueness: 0.6,
      practicality: 0.8
    },
    cultural_appropriateness: 0.8,
    native_speaker_rating: 0.8,
    usage_frequency: 0.7,
    popularity_trend: 0.6,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: '3.0.0',
    source: 'test',
    validation_status: 'validated' as const
  })

  describe('Korean Morpheme Processing', () => {
    it('应该处理韩文文本', () => {
      const morpheme = createKoreanMorpheme('한글', ['漢字'])
      const processed = processor.processKoreanMorpheme(morpheme)

      expect(processed.phonetic_features?.hangul_structure).toBeDefined()
      expect(processed.phonetic_features?.consonant_clusters).toBeDefined()
    })

    it('应该处理汉字词汇', () => {
      const morpheme = createKoreanMorpheme('한글', ['漢字'])
      const processed = processor.processKoreanMorpheme(morpheme)

      expect(processed.morphological_info?.hanja_info).toBeDefined()
      expect(processed.morphological_info?.hanja_info?.hanja).toBe('漢字')
      expect(processed.morphological_info?.hanja_info?.usage_frequency).toBe(0.8)
    })

    it('应该处理韩语敬语标记', () => {
      const morpheme = createKoreanMorpheme('김선생님')
      const processed = processor.processKoreanMorpheme(morpheme)

      expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.85)
      expect(processed.cultural_context.modernity).toBeGreaterThanOrEqual(0.80)
    })

    it('应该处理复辅音', () => {
      const morpheme = createKoreanMorpheme('까치') // 包含ㄲ复辅音
      const processed = processor.processKoreanMorpheme(morpheme)

      // 检查是否正确检测到复辅音
      expect(processed.phonetic_features?.consonant_clusters).toBeDefined()
    })

    it('应该处理不包含汉字的韩文', () => {
      const morpheme = createKoreanMorpheme('사랑', [])
      const processed = processor.processKoreanMorpheme(morpheme)

      // 没有汉字时，hanja_info应该不存在或者hanja字段为undefined
      if (processed.morphological_info?.hanja_info) {
        expect(processed.morphological_info.hanja_info.hanja).toBeUndefined()
      }
    })

    it('应该处理多种敬语标记', () => {
      const honorifics = ['님', '씨', '선생']

      honorifics.forEach(honorific => {
        const morpheme = createKoreanMorpheme(`김${honorific}`)
        const processed = processor.processKoreanMorpheme(morpheme)

        expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.85)
        expect(processed.cultural_context.modernity).toBeGreaterThanOrEqual(0.80)
      })
    })

    it('应该处理非韩文文本', () => {
      const morpheme = createKoreanMorpheme('test', [])
      const processed = processor.processKoreanMorpheme(morpheme)

      // 应该保持原始文本不变
      expect(processed.text).toBe('test')
      expect(processed.phonetic_features?.hangul_structure).toBeUndefined()
    })
  })

  describe('Private Methods Testing', () => {
    it('应该正确计算重音模式', () => {
      // 通过日语处理测试私有方法
      const morpheme1 = createJapaneseMorpheme('あ', [])
      const processed1 = processor.processJapaneseMorpheme(morpheme1)
      expect(processed1.phonetic_features?.accent_pattern).toBe('H')

      const morpheme2 = createJapaneseMorpheme('あい', [])
      const processed2 = processor.processJapaneseMorpheme(morpheme2)
      expect(processed2.phonetic_features?.accent_pattern).toBe('HL')

      const morpheme3 = createJapaneseMorpheme('あいう', [])
      const processed3 = processor.processJapaneseMorpheme(morpheme3)
      expect(processed3.phonetic_features?.accent_pattern).toBe('HLL')

      const morpheme4 = createJapaneseMorpheme('あいうえ', [])
      const processed4 = processor.processJapaneseMorpheme(morpheme4)
      expect(processed4.phonetic_features?.accent_pattern).toBe('HLLL')
    })

    it('应该正确估算笔画数', () => {
      const morpheme = createJapaneseMorpheme('かわ', ['川'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.morphological_info?.kanji_info?.stroke_count).toBeGreaterThan(0)
      expect(processed.morphological_info?.kanji_info?.stroke_count).toBeLessThanOrEqual(20)
    })

    it('应该正确分析韩文音节结构', () => {
      const morpheme1 = createKoreanMorpheme('가', [])
      const processed1 = processor.processKoreanMorpheme(morpheme1)
      expect(processed1.phonetic_features?.hangul_structure).toBe('CV')

      const morpheme2 = createKoreanMorpheme('가나', [])
      const processed2 = processor.processKoreanMorpheme(morpheme2)
      expect(processed2.phonetic_features?.hangul_structure).toBe('CV-CV')

      const morpheme3 = createKoreanMorpheme('가나다', [])
      const processed3 = processor.processKoreanMorpheme(morpheme3)
      expect(processed3.phonetic_features?.hangul_structure).toBe('CVC')
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('应该处理空文本', () => {
      const morpheme = createJapaneseMorpheme('', [])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.text).toBe('')
      expect(processed.phonetic_features?.mora_count).toBeUndefined()
    })

    it('应该处理空的alternative_forms', () => {
      const morpheme = createJapaneseMorpheme('かわ')
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.text).toBe('かわ')
      expect(processed.morphological_info?.kanji_info).toBeUndefined()
    })

    it('应该处理混合文字', () => {
      const morpheme = createJapaneseMorpheme('かわABC', ['川'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      // 应该仍然处理汉字信息
      expect(processed.morphological_info?.kanji_info).toBeDefined()
    })

    it('应该处理无效的汉字', () => {
      const morpheme = createJapaneseMorpheme('かわ', ['ABC'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      // 不应该有汉字信息
      expect(processed.morphological_info?.kanji_info).toBeUndefined()
    })

    it('应该处理韩文中的无效汉字', () => {
      const morpheme = createKoreanMorpheme('한글', ['ABC'])
      const processed = processor.processKoreanMorpheme(morpheme)

      // 不应该有汉字信息
      expect(processed.morphological_info?.hanja_info?.hanja).toBeUndefined()
    })

    it('应该处理长文本的笔画数估算', () => {
      const morpheme = createJapaneseMorpheme('かわ', ['川水火土'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      // 笔画数应该有上限
      expect(processed.morphological_info?.kanji_info?.stroke_count).toBeLessThanOrEqual(20)
    })

    it('应该处理单字符汉字的部首提取', () => {
      const morpheme = createJapaneseMorpheme('かわ', ['川'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.morphological_info?.kanji_info?.radical).toBe('川')
    })

    it('应该处理复辅音检测的边界情况', () => {
      const normalKorean = createKoreanMorpheme('사랑', [])
      const processedNormal = processor.processKoreanMorpheme(normalKorean)
      expect(processedNormal.phonetic_features?.consonant_clusters).toBeDefined()

      const clusterKorean = createKoreanMorpheme('쌀', []) // 包含ㅆ
      const processedCluster = processor.processKoreanMorpheme(clusterKorean)
      expect(processedCluster.phonetic_features?.consonant_clusters).toBeDefined()
    })
  })

  describe('Cultural Context Processing', () => {
    it('应该保持原有文化语境值', () => {
      const morpheme = createJapaneseMorpheme('テスト')
      morpheme.cultural_context.formality = 0.9
      morpheme.cultural_context.traditionality = 0.9

      const processed = processor.processJapaneseMorpheme(morpheme)

      // 没有敬语标记，应该保持原值
      expect(processed.cultural_context.formality).toBe(0.9)
      expect(processed.cultural_context.traditionality).toBe(0.9)
    })

    it('应该提升敬语相关的文化语境值', () => {
      const morpheme = createJapaneseMorpheme('田中さん')
      morpheme.cultural_context.formality = 0.3
      morpheme.cultural_context.traditionality = 0.3

      const processed = processor.processJapaneseMorpheme(morpheme)

      // 有敬语标记，应该提升到最小值
      expect(processed.cultural_context.formality).toBe(0.8)
      expect(processed.cultural_context.traditionality).toBe(0.85)
    })

    it('应该处理韩语敬语的文化语境', () => {
      const morpheme = createKoreanMorpheme('김선생님')
      morpheme.cultural_context.formality = 0.3
      morpheme.cultural_context.modernity = 0.3

      const processed = processor.processKoreanMorpheme(morpheme)

      expect(processed.cultural_context.formality).toBe(0.85)
      expect(processed.cultural_context.modernity).toBe(0.80)
    })
  })

  describe('Integration Tests', () => {
    it('应该完整处理复杂的日语语素', () => {
      const morpheme = createJapaneseMorpheme('おはようございます', ['お早う御座います'])
      const processed = processor.processJapaneseMorpheme(morpheme)

      expect(processed.phonetic_features?.mora_count).toBeGreaterThan(0)
      expect(processed.phonetic_features?.accent_pattern).toBeDefined()
      expect(processed.phonetic_features?.romanization).toBeDefined()
      expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.5)
    })

    it('应该完整处理复杂的韩语语素', () => {
      const morpheme = createKoreanMorpheme('안녕하세요', ['安寧'])
      const processed = processor.processKoreanMorpheme(morpheme)

      expect(processed.phonetic_features?.hangul_structure).toBeDefined()
      expect(processed.phonetic_features?.consonant_clusters).toBeDefined()
      expect(processed.morphological_info?.hanja_info).toBeDefined()
    })
  })
})
