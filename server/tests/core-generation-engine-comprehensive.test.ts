/**
 * CoreGenerationEngine 全面测试套件
 * 
 * 测试核心生成引擎的所有功能，包括初始化、生成、多语种支持、质量评估等
 * 
 * @fileoverview 核心生成引擎全面测试
 * @version 1.0.0
 * @since 2025-06-25
 */

import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import {
  GenerationContext,
  MorphemeCategory,
  CulturalContext,
  QualityScore
} from '../types/core'
import { LanguageCode } from '../types/multilingual'

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    readdir: jest.fn(),
    stat: jest.fn(),
    readFile: jest.fn()
  }
}))

// Mock path module
jest.mock('path', () => ({
  join: jest.fn((...args) => args.join('/')),
  extname: jest.fn((path) => {
    const parts = path.split('.')
    return parts.length > 1 ? `.${parts[parts.length - 1]}` : ''
  })
}))

describe('CoreGenerationEngine - 全面测试', () => {
  let engine: CoreGenerationEngine
  let mockContext: GenerationContext

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
    
    // Mock文件系统操作
    const fs = require('fs')
    fs.promises.readdir.mockResolvedValue(['emotions_morpheme.json'])
    fs.promises.stat.mockResolvedValue({ isFile: () => true })
    fs.promises.readFile.mockResolvedValue(JSON.stringify([{
      id: 'emo_001',
      text: '悦',
      category: MorphemeCategory.EMOTIONS,
      cultural_context: CulturalContext.MODERN,
      semantic_vector: new Array(512).fill(0.1),
      quality_score: 0.85,
      frequency: 0.7,
      phonetic_features: {
        pinyin: 'yuè',
        tone: 4,
        syllable_count: 1,
        phoneme_complexity: 0.3
      },
      morphological_info: {
        part_of_speech: 'adjective',
        morpheme_type: 'root',
        derivation_rules: []
      },
      usage_examples: ['悦耳', '喜悦'],
      metadata: {
        source: 'test',
        confidence: 0.9,
        last_updated: Date.now(),
        version: '1.0.0'
      }
    }]))

    engine = new CoreGenerationEngine()
    
    mockContext = {
      themes: [MorphemeCategory.EMOTIONS],
      cultural_context: CulturalContext.MODERN,
      creativity_level: 0.7,
      quality_threshold: 0.7,
      length_preference: { min: 2, max: 4 },
      avoid_patterns: [],
      user_preferences: {
        favorite_patterns: [],
        cultural_preference: CulturalContext.MODERN,
        style_preference: 'balanced',
        creativity_level: 0.7,
        quality_threshold: 0.7,
        excluded_categories: [],
        preferred_length: 'medium'
      },
      cultural_preference: CulturalContext.MODERN,
      style_preference: 'balanced'
    }
  })

  afterEach(() => {
    if (engine) {
      engine.destroy()
    }
  })

  describe('构造函数和初始化', () => {
    test('应该成功创建引擎实例', () => {
      expect(engine).toBeInstanceOf(CoreGenerationEngine)
      expect(engine.isReady()).toBe(false)
    })

    test('应该成功初始化引擎', async () => {
      await engine.initialize()
      expect(engine.isReady()).toBe(true)
    })

    test('应该跳过重复初始化', async () => {
      await engine.initialize()
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      
      await engine.initialize()
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('已初始化，跳过重复初始化')
      )
      consoleSpy.mockRestore()
    })

    test('应该处理初始化失败', async () => {
      const fs = require('fs')
      fs.promises.readdir.mockRejectedValue(new Error('文件系统错误'))
      
      await expect(engine.initialize()).rejects.toThrow('多语种核心生成引擎初始化失败')
    })
  })

  describe('多语种支持', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    test('应该支持中文语言', () => {
      expect(engine.isLanguageSupported(LanguageCode.ZH_CN)).toBe(true)
    })

    test('应该返回支持的语言列表', () => {
      const languages = engine.getSupportedLanguages()
      expect(languages).toContain(LanguageCode.ZH_CN)
      expect(Array.isArray(languages)).toBe(true)
    })

    test('应该获取多语种统计信息', () => {
      const stats = engine.getMultilingualStats()
      expect(stats).toHaveProperty('supportedLanguages')
      expect(stats).toHaveProperty('languageStats')
      expect(stats).toHaveProperty('totalConcepts')
      expect(stats).toHaveProperty('isMultilingualReady')
    })
  })

  describe('用户名生成', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    test('应该生成单个用户名', async () => {
      const results = await engine.generate(mockContext)

      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      const result = results[0]
      expect(result.text).toBeTruthy()
      expect(result.quality_score).toHaveProperty('overall')
      expect(result.quality_score.overall).toBeGreaterThanOrEqual(0)
      expect(result.quality_score.overall).toBeLessThanOrEqual(1)
      expect(result.components).toBeInstanceOf(Array)
      expect(result.metadata).toHaveProperty('generation_time')
    })

    test('应该生成多个用户名', async () => {
      const results = await engine.generateMultilingual(mockContext, 3, LanguageCode.ZH_CN)

      expect(results).toHaveLength(3)
      results.forEach(result => {
        expect(result.text).toBeTruthy()
        expect(result.quality_score.overall).toBeGreaterThanOrEqual(mockContext.quality_threshold)
      })
    })

    test('应该处理生成数量限制', async () => {
      await expect(engine.generateMultilingual(mockContext, 0, LanguageCode.ZH_CN)).rejects.toThrow('生成数量必须在1-10之间')
      await expect(engine.generateMultilingual(mockContext, 11, LanguageCode.ZH_CN)).rejects.toThrow('生成数量必须在1-10之间')
    })

    test('应该支持多语种生成', async () => {
      const results = await engine.generateMultilingual(mockContext, 2, LanguageCode.ZH_CN)
      
      expect(results).toHaveLength(2)
      results.forEach(result => {
        expect(result.text).toBeTruthy()
        expect(result.quality_score.overall).toBeGreaterThanOrEqual(0)
      })
    })

    test('应该处理不支持的语言', async () => {
      await expect(
        engine.generateMultilingual(mockContext, 1, LanguageCode.EN_US)
      ).rejects.toThrow('不支持的语言')
    })
  })

  describe('质量评估', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    test('应该评估用户名质量', async () => {
      const results = await engine.generate(mockContext)
      const result = results[0]
      const quality = result.quality_score

      expect(quality).toHaveProperty('overall')
      expect(quality).toHaveProperty('dimensions')
      expect(quality).toHaveProperty('confidence')
      expect(quality).toHaveProperty('evaluation_time')
      expect(quality).toHaveProperty('algorithm_version')

      expect(quality.dimensions).toHaveProperty('creativity')
      expect(quality.dimensions).toHaveProperty('memorability')
      expect(quality.dimensions).toHaveProperty('cultural_fit')
      expect(quality.dimensions).toHaveProperty('uniqueness')
      expect(quality.dimensions).toHaveProperty('pronunciation')
      expect(quality.dimensions).toHaveProperty('semantic_coherence')
      expect(quality.dimensions).toHaveProperty('aesthetic_appeal')
      expect(quality.dimensions).toHaveProperty('practical_usability')
    })

    test('应该提供质量改进建议', async () => {
      const results = await engine.generate(mockContext)
      const result = results[0]
      const quality = result.quality_score

      expect(quality).toHaveProperty('issues')
      expect(quality).toHaveProperty('suggestions')
      expect(Array.isArray(quality.issues)).toBe(true)
      expect(Array.isArray(quality.suggestions)).toBe(true)
    })
  })

  describe('统计信息', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    test('应该获取引擎统计信息', () => {
      const stats = engine.getStats()
      
      expect(stats).toHaveProperty('morpheme_stats')
      expect(stats).toHaveProperty('generation_stats')
      expect(stats).toHaveProperty('engine_status')
      expect(stats).toHaveProperty('multilingual_stats')
      expect(stats).toHaveProperty('performance_metrics')
    })

    test('应该获取语素仓库统计信息', () => {
      const stats = engine.getMorphemeStats()
      
      expect(stats).toHaveProperty('total')
      expect(stats).toHaveProperty('byCategory')
      expect(stats).toHaveProperty('avgQuality')
      expect(stats).toHaveProperty('avgFrequency')
    })

    test('应该在生成后更新统计信息', async () => {
      const statsBefore = engine.getStats()

      await engine.generate(mockContext)

      const statsAfter = engine.getStats()
      expect(statsAfter.morpheme_stats.total).toBeGreaterThanOrEqual(
        statsBefore.morpheme_stats.total
      )
    })
  })

  describe('资源管理', () => {
    test('应该支持重新加载', async () => {
      await engine.initialize()
      expect(engine.isReady()).toBe(true)
      
      await engine.reload()
      expect(engine.isReady()).toBe(true)
    })

    test('应该正确清理资源', async () => {
      await engine.initialize()
      expect(engine.isReady()).toBe(true)
      
      engine.destroy()
      expect(engine.isReady()).toBe(false)
    })
  })

  describe('错误处理', () => {
    test('应该处理未初始化的生成请求', async () => {
      // 不初始化引擎，直接生成
      const result = await engine.generate(mockContext)
      expect(result).toBeDefined() // 应该自动初始化
    })

    test('应该处理无效的生成上下文', async () => {
      await engine.initialize()
      
      const invalidContext = {
        ...mockContext,
        quality_threshold: 1.5 // 无效的质量阈值
      }
      
      // 应该能处理并调整无效参数
      const result = await engine.generate(invalidContext)
      expect(result).toBeDefined()
    })
  })
})
