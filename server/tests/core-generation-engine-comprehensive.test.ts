/**
 * CoreGenerationEngine 真实功能测试
 *
 * 测试核心生成引擎的实际功能，不使用mock，直接调用真实的数据和方法
 *
 * @fileoverview 核心生成引擎真实功能测试
 * @version 3.0.0
 * @since 2025-06-25
 */

import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import {
  GenerationContext,
  MorphemeCategory,
  CulturalContext,
  QualityScore
} from '../types/core'
import { LanguageCode, ConceptCategory } from '../types/multilingual'

describe('CoreGenerationEngine - 真实功能测试', () => {
  let engine: CoreGenerationEngine
  let testContext: GenerationContext

  beforeAll(async () => {
    // 创建真实的引擎实例，不使用mock
    engine = new CoreGenerationEngine()

    // 设置测试上下文
    testContext = {
      cultural_preference: CulturalContext.MODERN,
      style_preference: 'artistic',
      creativity_level: 0.7,
      quality_threshold: 0.6,
      patterns: [],
      exclude_patterns: [],
      context_keywords: ['情感', '现代'],
      user_preferences: {
        favorite_patterns: [],
        cultural_preference: CulturalContext.MODERN,
        style_preference: 'artistic',
        creativity_level: 0.7,
        quality_threshold: 0.6,
        excluded_categories: [],
        preferred_length: 'medium'
      },
      concept_preferences: {
        categories: [ConceptCategory.EMOTIONS],
        cultural_context: CulturalContext.MODERN,
        abstraction_level: 0.5
      },
      target_language: LanguageCode.ZH_CN,
      cultural_sensitivity: 0.8
    }
  }, 60000) // 增加超时时间以适应真实数据加载

  afterAll(() => {
    if (engine) {
      engine.destroy()
    }
  })

  describe('引擎初始化', () => {
    test('应该成功创建引擎实例', () => {
      expect(engine).toBeInstanceOf(CoreGenerationEngine)
      expect(engine.isReady()).toBe(false)
    })

    test('应该成功初始化引擎', async () => {
      await engine.initialize()
      expect(engine.isReady()).toBe(true)
    }, 30000)

    test('应该获取引擎统计信息', async () => {
      await engine.initialize()
      const stats = engine.getStats()

      expect(stats).toHaveProperty('morpheme_count')
      expect(stats).toHaveProperty('engine_status')
      expect(stats.engine_status).toBe('ready')
      expect(stats.morpheme_count).toBeGreaterThan(0)
    }, 30000)

    test('应该跳过重复初始化', async () => {
      await engine.initialize()
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()

      await engine.initialize()

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('已初始化，跳过重复初始化')
      )
      consoleSpy.mockRestore()
    }, 30000)
  })

  describe('多语种支持', () => {
    beforeAll(async () => {
      await engine.initialize()
    }, 30000)

    test('应该支持中文语言', () => {
      expect(engine.isLanguageSupported(LanguageCode.ZH_CN)).toBe(true)
    })

    test('应该返回支持的语言列表', () => {
      const languages = engine.getSupportedLanguages()
      expect(languages).toContain(LanguageCode.ZH_CN)
      expect(Array.isArray(languages)).toBe(true)
    })

    test('应该获取多语种统计信息', () => {
      const stats = engine.getMultilingualStats()

      expect(stats).toHaveProperty('supportedLanguages')
      expect(stats).toHaveProperty('languageStats')
      expect(stats).toHaveProperty('totalConcepts')
      expect(stats).toHaveProperty('isMultilingualReady')
      expect(Array.isArray(stats.supportedLanguages)).toBe(true)
    })
  })

  describe('用户名生成', () => {
    beforeAll(async () => {
      await engine.initialize()
    }, 30000)

    test('应该生成指定数量的用户名', async () => {
      const count = 3
      const results = await engine.generate(testContext, count)

      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)
      expect(results.length).toBeLessThanOrEqual(count)

      // 验证每个生成的用户名结构
      results.forEach(username => {
        expect(username).toHaveProperty('text')
        expect(username).toHaveProperty('quality_score')
        expect(username).toHaveProperty('explanation')
        expect(username).toHaveProperty('components')
        expect(username).toHaveProperty('metadata')

        expect(typeof username.text).toBe('string')
        expect(username.text.length).toBeGreaterThan(0)
        expect(username.quality_score.overall).toBeGreaterThanOrEqual(testContext.quality_threshold)
      })
    }, 20000)

    test('应该处理不同的生成数量', async () => {
      const testCounts = [1, 2, 5]

      for (const count of testCounts) {
        const results = await engine.generate(testContext, count)
        expect(results.length).toBeGreaterThan(0)
        expect(results.length).toBeLessThanOrEqual(count)
      }
    }, 25000)

    test('应该验证生成数量范围', async () => {
      // 测试无效的生成数量
      await expect(engine.generate(testContext, 0)).rejects.toThrow('生成数量必须在1-10之间')
      await expect(engine.generate(testContext, 11)).rejects.toThrow('生成数量必须在1-10之间')
    })

    test('应该支持多语种生成', async () => {
      const results = await engine.generateMultilingual(testContext, 2, LanguageCode.ZH_CN)

      expect(Array.isArray(results)).toBe(true)
      expect(results.length).toBeGreaterThan(0)

      results.forEach(username => {
        expect(username.text).toBeTruthy()
        expect(username.quality_score.overall).toBeGreaterThanOrEqual(testContext.quality_threshold)
      })
    }, 15000)

    test('应该处理不支持的语言', async () => {
      const unsupportedLanguage = 'xx-XX' as LanguageCode

      if (!engine.isLanguageSupported(unsupportedLanguage)) {
        await expect(
          engine.generateMultilingual(testContext, 1, unsupportedLanguage)
        ).rejects.toThrow('不支持的语言')
      }
    })
  })

  describe('质量评估', () => {
    beforeAll(async () => {
      await engine.initialize()
    }, 30000)

    test('生成的用户名应该满足质量阈值', async () => {
      const highQualityContext = {
        ...testContext,
        quality_threshold: 0.8
      }

      const results = await engine.generate(highQualityContext, 2)

      results.forEach(username => {
        expect(username.quality_score.overall).toBeGreaterThanOrEqual(0.8)
        expect(username.quality_score.dimensions).toHaveProperty('creativity')
        expect(username.quality_score.dimensions).toHaveProperty('memorability')
        expect(username.quality_score.dimensions).toHaveProperty('cultural_fit')
      })
    }, 15000)

    test('应该包含详细的质量维度', async () => {
      const results = await engine.generate(testContext, 1)

      if (results.length > 0) {
        const qualityScore = results[0].quality_score
        const dimensions = qualityScore.dimensions

        expect(dimensions).toHaveProperty('creativity')
        expect(dimensions).toHaveProperty('memorability')
        expect(dimensions).toHaveProperty('cultural_fit')
        expect(dimensions).toHaveProperty('uniqueness')
        expect(dimensions).toHaveProperty('pronunciation')
        expect(dimensions).toHaveProperty('semantic_coherence')
        expect(dimensions).toHaveProperty('aesthetic_appeal')
        expect(dimensions).toHaveProperty('practical_usability')

        // 验证所有维度都在合理范围内
        Object.values(dimensions).forEach(score => {
          expect(score).toBeGreaterThanOrEqual(0)
          expect(score).toBeLessThanOrEqual(1)
        })
      }
    }, 15000)

    test('应该包含质量评估元数据', async () => {
      const results = await engine.generate(testContext, 1)

      if (results.length > 0) {
        const qualityScore = results[0].quality_score

        expect(qualityScore).toHaveProperty('confidence')
        expect(qualityScore).toHaveProperty('evaluation_time')
        expect(qualityScore).toHaveProperty('algorithm_version')
        expect(qualityScore).toHaveProperty('timestamp')

        expect(qualityScore.confidence).toBeGreaterThanOrEqual(0)
        expect(qualityScore.confidence).toBeLessThanOrEqual(1)
        expect(qualityScore.evaluation_time).toBeGreaterThan(0)
        expect(typeof qualityScore.algorithm_version).toBe('string')
        expect(qualityScore.timestamp).toBeGreaterThan(0)
      }
    }, 15000)
  })

  describe('性能测试', () => {
    beforeAll(async () => {
      await engine.initialize()
    }, 30000)

    test('应该在合理时间内完成生成', async () => {
      const startTime = Date.now()
      const results = await engine.generate(testContext, 5)
      const endTime = Date.now()

      const generationTime = endTime - startTime
      expect(generationTime).toBeLessThan(10000) // 10秒内完成
      expect(results.length).toBeGreaterThan(0)
    }, 15000)

    test('应该记录生成统计信息', async () => {
      const statsBefore = engine.getStats()
      await engine.generate(testContext, 2)
      const statsAfter = engine.getStats()

      expect(statsAfter.total_generations).toBeGreaterThanOrEqual(statsBefore.total_generations)
    }, 10000)

    test('应该获取语素仓库统计信息', () => {
      const morphemeStats = engine.getMorphemeStats()

      expect(morphemeStats).toHaveProperty('total')
      expect(morphemeStats).toHaveProperty('byCategory')
      expect(morphemeStats.total).toBeGreaterThan(0)
      expect(typeof morphemeStats.byCategory).toBe('object')
    })
  })

  describe('错误处理', () => {
    beforeAll(async () => {
      await engine.initialize()
    }, 30000)

    test('应该处理未初始化的引擎', async () => {
      const newEngine = new CoreGenerationEngine()

      // 未初始化的引擎应该自动初始化
      const results = await newEngine.generate(testContext, 1)
      expect(Array.isArray(results)).toBe(true)

      newEngine.destroy()
    }, 20000)

    test('应该处理极端质量阈值', async () => {
      const extremeContext = {
        ...testContext,
        quality_threshold: 0.99
      }

      const results = await engine.generate(extremeContext, 1)
      expect(Array.isArray(results)).toBe(true)
    }, 15000)

    test('应该处理不同的创意级别', async () => {
      const lowCreativityContext = {
        ...testContext,
        creativity_level: 0.3
      }

      const highCreativityContext = {
        ...testContext,
        creativity_level: 0.9
      }

      const lowResults = await engine.generate(lowCreativityContext, 2)
      const highResults = await engine.generate(highCreativityContext, 2)

      expect(lowResults.length).toBeGreaterThan(0)
      expect(highResults.length).toBeGreaterThan(0)

      // 验证创意级别影响生成结果
      lowResults.forEach(username => {
        expect(username.metadata.creativity).toBeDefined()
      })

      highResults.forEach(username => {
        expect(username.metadata.creativity).toBeDefined()
      })
    }, 20000)
  })
})
