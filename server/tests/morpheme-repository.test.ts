/**
 * MorphemeRepository 测试套件
 * 
 * 测试语素仓库的数据管理、索引、查询等功能
 * 
 * @fileoverview 语素仓库测试
 * @version 1.0.0
 * @since 2025-06-25
 */

import { MorphemeRepository } from '../core/repositories/MorphemeRepository'
import { DataLoader } from '../core/data/DataLoader'
import { DataValidator } from '../core/data/DataValidator'
import { 
  MorphemeCategory, 
  CulturalContext,
  Morpheme
} from '../types/core'

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    readdir: jest.fn(),
    stat: jest.fn(),
    readFile: jest.fn()
  }
}))

// Mock path module
jest.mock('path', () => ({
  join: jest.fn((...args) => args.join('/')),
  extname: jest.fn((path) => {
    const parts = path.split('.')
    return parts.length > 1 ? `.${parts[parts.length - 1]}` : ''
  })
}))

describe('MorphemeRepository', () => {
  let repository: MorphemeRepository
  let dataLoader: DataLoader
  let dataValidator: DataValidator
  let mockMorphemes: Morpheme[]

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks()
    
    // 创建测试数据
    mockMorphemes = [
      {
        id: 'emo_001',
        text: '悦',
        category: MorphemeCategory.EMOTIONS,
        cultural_context: CulturalContext.MODERN,
        semantic_vector: new Array(512).fill(0.1),
        quality_score: 0.85,

        phonetic_features: {
          pinyin: 'yuè',
          tone: 4,
          syllable_count: 1,
          phoneme_complexity: 0.3
        },
        morphological_info: {
          part_of_speech: 'adjective',
          morpheme_type: 'root',
          derivation_rules: []
        },
        usage_examples: ['悦耳', '喜悦'],
        metadata: {
          source: 'test',
          confidence: 0.9,
          last_updated: Date.now(),
          version: '1.0.0'
        }
      },
      {
        id: 'nat_001',
        text: '山',
        category: MorphemeCategory.NATURE_ELEMENTS,
        cultural_context: CulturalContext.ANCIENT,
        semantic_vector: new Array(512).fill(0.2),
        quality_score: 0.9,

        phonetic_features: {
          pinyin: 'shān',
          tone: 1,
          syllable_count: 1,
          phoneme_complexity: 0.4
        },
        morphological_info: {
          part_of_speech: 'noun',
          morpheme_type: 'root',
          derivation_rules: []
        },
        usage_examples: ['高山', '山峰'],
        metadata: {
          source: 'test',
          confidence: 0.95,
          last_updated: Date.now(),
          version: '1.0.0'
        }
      }
    ]

    // Mock文件系统操作
    const fs = require('fs')
    fs.promises.readdir.mockResolvedValue(['test_morpheme.json'])
    fs.promises.stat.mockResolvedValue({ isFile: () => true })
    fs.promises.readFile.mockResolvedValue(JSON.stringify(mockMorphemes))

    // 创建依赖组件
    dataLoader = new DataLoader({
      enableHotReload: false,
      enableValidation: true,
      enableCache: false
    })

    dataValidator = new DataValidator({
      strict_mode: true,
      skip_warnings: false
    })

    repository = new MorphemeRepository(dataLoader, dataValidator)
  })

  afterEach(() => {
    if (repository) {
      repository.destroy()
    }
  })

  describe('构造函数和初始化', () => {
    test('应该成功创建仓库实例', () => {
      expect(repository).toBeInstanceOf(MorphemeRepository)
      expect(repository.isReady()).toBe(false)
    })

    test('应该成功初始化仓库', async () => {
      await repository.initialize()
      expect(repository.isReady()).toBe(true)
    })

    test('应该跳过重复初始化', async () => {
      await repository.initialize()
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      
      await repository.initialize()
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('已初始化')
      )
      consoleSpy.mockRestore()
    })

    test('应该处理初始化失败', async () => {
      const fs = require('fs')
      fs.promises.readdir.mockRejectedValue(new Error('文件系统错误'))
      
      await expect(repository.initialize()).rejects.toThrow()
    })
  })

  describe('数据查询', () => {
    beforeEach(async () => {
      await repository.initialize()
    })

    test('应该根据类别查询语素', () => {
      const emotions = repository.findByCategory(MorphemeCategory.EMOTIONS)
      expect(Array.isArray(emotions)).toBe(true)

      const nature = repository.findByCategory(MorphemeCategory.NATURE_ELEMENTS)
      expect(Array.isArray(nature)).toBe(true)
    })

    test('应该根据质量范围查询语素', () => {
      const highQuality = repository.findByQualityRange(0.8, 1.0)
      expect(Array.isArray(highQuality)).toBe(true)

      const mediumQuality = repository.findByQualityRange(0.7, 0.89)
      expect(Array.isArray(mediumQuality)).toBe(true)
    })

    test('应该支持语义相似性查询', () => {
      const mockMorpheme = mockMorphemes[0]
      const similar = repository.findSimilar(mockMorpheme, 1, 0.8)

      expect(Array.isArray(similar)).toBe(true)
    })
  })

  describe('统计信息', () => {
    beforeEach(async () => {
      await repository.initialize()
    })

    test('应该获取基本统计信息', () => {
      const stats = repository.getStats()
      
      expect(stats).toHaveProperty('total')
      expect(stats).toHaveProperty('byCategory')
      expect(stats).toHaveProperty('byCulturalContext')
      expect(stats).toHaveProperty('avgQuality')
      expect(stats).toHaveProperty('avgFrequency')
      expect(stats).toHaveProperty('qualityDistribution')
      expect(stats).toHaveProperty('frequencyDistribution')
      
      expect(stats.total).toBe(2)
      expect(stats.byCategory[MorphemeCategory.EMOTIONS]).toBeGreaterThanOrEqual(0)
      expect(stats.byCategory[MorphemeCategory.NATURE_ELEMENTS]).toBeGreaterThanOrEqual(0)
    })

    test('应该计算正确的平均值', () => {
      const stats = repository.getStats()

      expect(typeof stats.avgQuality).toBe('number')
      expect(stats.avgQuality).toBeGreaterThanOrEqual(0)
      expect(stats.avgQuality).toBeLessThanOrEqual(1)
    })

    test('应该提供质量分布信息', () => {
      const stats = repository.getStats()
      
      expect(stats.qualityDistribution).toHaveProperty('high')
      expect(stats.qualityDistribution).toHaveProperty('medium')
      expect(stats.qualityDistribution).toHaveProperty('low')
      
      // 两个语素都是高质量 (>= 0.8)
      expect(stats.qualityDistribution.high).toBe(2)
      expect(stats.qualityDistribution.medium).toBe(0)
      expect(stats.qualityDistribution.low).toBe(0)
    })
  })

  describe('数据管理', () => {
    beforeEach(async () => {
      await repository.initialize()
    })

    test('应该支持重新加载数据', async () => {
      expect(repository.getStats().total).toBe(2)
      
      await repository.reload()
      
      expect(repository.isReady()).toBe(true)
      expect(repository.getStats().total).toBe(2)
    })

    test('应该正确清理资源', () => {
      expect(repository.isReady()).toBe(true)
      
      repository.destroy()
      
      expect(repository.isReady()).toBe(false)
    })
  })

  describe('边界情况处理', () => {
    beforeEach(async () => {
      await repository.initialize()
    })

    test('应该处理空查询结果', () => {
      const results = repository.findByCategory(MorphemeCategory.PROFESSION)
      expect(results).toHaveLength(0)
    })

    test('应该处理无效的采样数量', () => {
      const samples = repository.sampleRandom(0)
      expect(samples).toHaveLength(0)
      
      const largeSamples = repository.sampleRandom(100)
      expect(largeSamples.length).toBeLessThanOrEqual(2) // 最多返回所有数据
    })

    test('应该处理无效的质量范围', () => {
      const results = repository.findByQualityRange(1.5, 2.0) // 无效范围
      expect(results).toHaveLength(0)
    })

    test('应该处理无效的语义向量', () => {
      const invalidVector = new Array(256).fill(0.1) // 错误维度
      const results = repository.findSimilar(invalidVector, 1, 0.8)
      expect(results).toHaveLength(0)
    })
  })
})
