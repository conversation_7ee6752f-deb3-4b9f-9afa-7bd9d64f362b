/**
 * 欧洲语言处理器单元测试
 * 
 * 测试西班牙语、法语、德语的语言处理功能
 * 包括语音分析、形态学分析、文化语境处理等
 * 
 * @fileoverview 欧洲语言处理器测试套件
 * @version 3.0.0
 * @since 2025-06-24
 */

// Jest globals are available without import
import { EuropeanLanguageProcessor } from '../core/multilingual/EuropeanLanguageProcessor'
import { LanguageCode, RegisterLevel } from '../types/multilingual'

// 测试专用的简化接口，匹配实际数据文件结构
interface TestMorpheme {
  morpheme_id: string
  text: string
  universal_concept_id: string
  concept_category: string
  semantic_vector: number[]
  phonetic_features?: {
    ipa_transcription?: string
    syllable_count?: number
    stress_position?: number
    romanization?: string
  }
  morphological_info?: {
    root?: string
    prefixes?: string[]
    suffixes?: string[]
    word_class?: string
    inflection_info?: {
      gender?: string
      number?: string
      case?: string
    }
    derivation_info?: {
      base_word?: string
      derivation_type?: string
      productivity?: number
    }
    compound_structure?: string[]
  }
  cultural_context?: {
    formality?: number
    regionality?: number
    traditionality?: number
    modernity?: number
    aesthetic_value?: number
    religious_sensitivity?: number
    age_appropriateness?: string[]
    cultural_tags?: string[]
  }
  usage_frequency: number
  semantic_rarity: number
  register_level: RegisterLevel
  created_at: string
  updated_at: string
}

describe('EuropeanLanguageProcessor', () => {
  let processor: EuropeanLanguageProcessor

  beforeEach(() => {
    processor = new EuropeanLanguageProcessor()
  })

  describe('Language Support', () => {
    it('should support Spanish language', () => {
      expect(processor.supportsLanguage(LanguageCode.ES_ES)).toBe(true)
    })

    it('should support French language', () => {
      expect(processor.supportsLanguage(LanguageCode.FR_FR)).toBe(true)
    })

    it('should support German language', () => {
      expect(processor.supportsLanguage(LanguageCode.DE_DE)).toBe(true)
    })

    it('should not support unsupported languages', () => {
      expect(processor.supportsLanguage(LanguageCode.EN_US)).toBe(false)
      expect(processor.supportsLanguage(LanguageCode.JA_JP)).toBe(false)
    })
  })

  describe('Spanish Language Processing', () => {
    const spanishMorpheme: TestMorpheme = {
      morpheme_id: 'test_es_001',
      text: 'corazón',
      universal_concept_id: 'test_concept_001',
      concept_category: 'emotion',
      semantic_vector: [0.9, 0.8, 0.2, 0.1, 0.8, 0.9, 0.85, 0.7],
      phonetic_features: {
        ipa_transcription: '[koɾaˈθon]',
        syllable_count: 3,
        stress_position: 3,
        romanization: 'corazon'
      },
      morphological_info: {
        root: 'corazón',
        prefixes: [],
        suffixes: [],
        word_class: 'noun',
        inflection_info: {
          gender: 'masculine',
          number: 'singular'
        }
      },
      cultural_context: {
        formality: 0.6,
        regionality: 0.9,
        traditionality: 0.95,
        modernity: 0.5,
        aesthetic_value: 0.95,
        religious_sensitivity: 0.3,
        age_appropriateness: ['all'],
        cultural_tags: ['emotion', 'traditional']
      },
      usage_frequency: 0.9,
      semantic_rarity: 0.2,
      register_level: RegisterLevel.NEUTRAL,
      created_at: '2025-06-24T00:00:00Z',
      updated_at: '2025-06-24T00:00:00Z'
    }

    it('should process Spanish morpheme correctly', () => {
      const result = processor.processLanguageSpecificFeatures(
        spanishMorpheme,
        LanguageCode.ES_ES
      )

      expect(result).toBeDefined()
      expect(result.phonetic_info).toBeDefined()
      expect(result.morphological_info).toBeDefined()
      expect(result.cultural_adjustments).toBeDefined()
    })

    it('should extract Spanish phonetic features', () => {
      const result = processor.processLanguageSpecificFeatures(
        spanishMorpheme,
        LanguageCode.ES_ES
      )

      expect(result.phonetic_info?.ipa_transcription).toBe('[corazón]')
      expect(result.phonetic_info?.syllable_count).toBe(3)
      expect(result.phonetic_info?.stress_position).toBe(6)
      expect(result.phonetic_info?.has_trill_sounds).toBe(true)
    })

    it('should detect Spanish gender correctly', () => {
      const feminineMorpheme = { ...spanishMorpheme, text: 'alegría' }
      const result = processor.processLanguageSpecificFeatures(
        feminineMorpheme,
        LanguageCode.ES_ES
      )

      expect(result.morphological_info?.inflection_info?.gender).toBe('feminine')
    })

    it('should adjust Spanish cultural context', () => {
      const result = processor.processLanguageSpecificFeatures(
        spanishMorpheme,
        LanguageCode.ES_ES
      )

      expect(result.cultural_adjustments).toBeDefined()
      expect(result.cultural_adjustments?.formality).toBeGreaterThanOrEqual(0)
      expect(result.cultural_adjustments?.formality).toBeLessThanOrEqual(1)
    })
  })

  describe('French Language Processing', () => {
    const frenchMorpheme: TestMorpheme = {
      morpheme_id: 'test_fr_001',
      text: 'amour',
      universal_concept_id: 'test_concept_002',
      concept_category: 'emotion',
      semantic_vector: [0.95, 0.9, 0.2, 0.1, 0.8, 0.95, 0.9, 0.7],
      phonetic_features: {
        ipa_transcription: '[amuʁ]',
        syllable_count: 2,
        stress_position: 2,
        romanization: 'amour'
      },
      morphological_info: {
        root: 'amour',
        prefixes: [],
        suffixes: [],
        word_class: 'noun'
      },
      cultural_context: {
        formality: 0.8,
        regionality: 0.9,
        traditionality: 0.95,
        modernity: 0.8,
        aesthetic_value: 0.98
      },
      usage_frequency: 0.9,
      semantic_rarity: 0.2,
      register_level: RegisterLevel.NEUTRAL,
      created_at: '2025-06-24T00:00:00Z',
      updated_at: '2025-06-24T00:00:00Z'
    }

    it('should process French morpheme correctly', () => {
      const result = processor.processLanguageSpecificFeatures(
        frenchMorpheme,
        LanguageCode.FR_FR
      )

      expect(result).toBeDefined()
      expect(result.phonetic_info).toBeDefined()
      expect(result.morphological_info).toBeDefined()
      expect(result.cultural_adjustments).toBeDefined()
    })

    it('should extract French phonetic features', () => {
      const result = processor.processLanguageSpecificFeatures(
        frenchMorpheme,
        LanguageCode.FR_FR
      )

      expect(result.phonetic_info?.ipa_transcription).toBe('[amouʁ]')
      expect(result.phonetic_info?.syllable_count).toBe(3)
      expect(result.phonetic_info?.stress_position).toBe(3)
      expect(result.phonetic_info?.vowel_complexity).toBeGreaterThan(0)
    })

    it('should detect French gender correctly', () => {
      const feminineMorpheme = { ...frenchMorpheme, text: 'joie' }
      const result = processor.processLanguageSpecificFeatures(
        feminineMorpheme,
        LanguageCode.FR_FR
      )

      expect(result.morphological_info?.inflection_info?.gender).toBe('feminine')
    })

    it('should calculate French elegance', () => {
      const result = processor.processLanguageSpecificFeatures(
        frenchMorpheme,
        LanguageCode.FR_FR
      )

      expect(result.cultural_adjustments?.aesthetic_value).toBeDefined()
      expect(result.cultural_adjustments?.aesthetic_value).toBeGreaterThan(0.5)
    })
  })

  describe('German Language Processing', () => {
    const germanMorpheme: TestMorpheme = {
      morpheme_id: 'test_de_001',
      text: 'Freiheit',
      universal_concept_id: 'test_concept_003',
      concept_category: 'abstract',
      semantic_vector: [0.9, 0.8, 0.5, 0.4, 0.9, 0.8, 0.8, 0.8],
      phonetic_features: {
        ipa_transcription: '[ˈfʁaɪhaɪt]',
        syllable_count: 2,
        stress_position: 1,
        romanization: 'Freiheit'
      },
      morphological_info: {
        root: 'frei',
        prefixes: [],
        suffixes: ['heit'],
        word_class: 'noun'
      },
      cultural_context: {
        formality: 0.9,
        regionality: 0.6,
        traditionality: 0.8,
        modernity: 0.9,
        aesthetic_value: 0.8
      },
      usage_frequency: 0.8,
      semantic_rarity: 0.4,
      register_level: RegisterLevel.FORMAL,
      created_at: '2025-06-24T00:00:00Z',
      updated_at: '2025-06-24T00:00:00Z'
    }

    it('should process German morpheme correctly', () => {
      const result = processor.processLanguageSpecificFeatures(
        germanMorpheme,
        LanguageCode.DE_DE
      )

      expect(result).toBeDefined()
      expect(result.phonetic_info).toBeDefined()
      expect(result.morphological_info).toBeDefined()
      expect(result.cultural_adjustments).toBeDefined()
    })

    it('should extract German phonetic features', () => {
      const result = processor.processLanguageSpecificFeatures(
        germanMorpheme,
        LanguageCode.DE_DE
      )

      expect(result.phonetic_info?.ipa_transcription).toBe('[freiheit]')
      expect(result.phonetic_info?.syllable_count).toBe(4)
      expect(result.phonetic_info?.stress_position).toBe(1)
    })

    it('should detect German gender correctly', () => {
      const result = processor.processLanguageSpecificFeatures(
        germanMorpheme,
        LanguageCode.DE_DE
      )

      expect(result.morphological_info?.inflection_info?.gender).toBe('feminine')
    })

    it('should analyze German compound structure', () => {
      const compoundMorpheme = { ...germanMorpheme, text: 'Sonnenschein' }
      const result = processor.processLanguageSpecificFeatures(
        compoundMorpheme,
        LanguageCode.DE_DE
      )

      expect(result.morphological_info?.compound_structure).toBeDefined()
      expect(Array.isArray(result.morphological_info?.compound_structure)).toBe(true)
    })

    it('should calculate German precision', () => {
      const result = processor.processLanguageSpecificFeatures(
        germanMorpheme,
        LanguageCode.DE_DE
      )

      expect(result.cultural_adjustments?.precision).toBeDefined()
      expect(result.cultural_adjustments?.precision).toBeGreaterThan(0.5)
    })
  })

  describe('Error Handling', () => {
    it('should throw error for unsupported language', () => {
      const morpheme: TestMorpheme = {
        morpheme_id: 'test_001',
        text: 'test',
        universal_concept_id: 'test_concept',
        concept_category: 'test',
        semantic_vector: [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
        usage_frequency: 0.5,
        semantic_rarity: 0.5,
        register_level: RegisterLevel.NEUTRAL,
        created_at: '2025-06-24T00:00:00Z',
        updated_at: '2025-06-24T00:00:00Z'
      }

      expect(() => {
        processor.processLanguageSpecificFeatures(morpheme, LanguageCode.EN_US)
      }).toThrow('Unsupported European language')
    })

    it('should handle missing morphological info gracefully', () => {
      const morpheme: TestMorpheme = {
        morpheme_id: 'test_001',
        text: 'test',
        universal_concept_id: 'test_concept',
        concept_category: 'test',
        semantic_vector: [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
        usage_frequency: 0.5,
        semantic_rarity: 0.5,
        register_level: RegisterLevel.NEUTRAL,
        created_at: '2025-06-24T00:00:00Z',
        updated_at: '2025-06-24T00:00:00Z'
      }

      expect(() => {
        processor.processLanguageSpecificFeatures(morpheme, LanguageCode.ES_ES)
      }).not.toThrow()
    })
  })
})
