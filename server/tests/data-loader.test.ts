/**
 * DataLoader 单元测试
 * 
 * 测试数据加载器的功能，包括文件加载、数据验证、缓存机制等
 * 
 * @fileoverview 数据加载器测试套件
 * @version 1.0.0
 * @since 2025-06-25
 */

import { DataLoader } from '../core/data/DataLoader'
import { MorphemeCategory } from '../types/core'
import { promises as fs } from 'fs'
import { join } from 'path'

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn()
  },
  watch: jest.fn()
}))

const mockFs = fs as jest.Mocked<typeof fs>

describe('DataLoader', () => {
  let dataLoader: DataLoader

  beforeEach(() => {
    jest.clearAllMocks()
    
    dataLoader = new DataLoader({
      enableHotReload: false,
      enableValidation: true,
      enableCache: true,
      cacheTTL: 3600
    })
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Constructor', () => {
    it('should create DataLoader with default configuration', () => {
      const loader = new DataLoader()
      expect(loader).toBeInstanceOf(DataLoader)
    })

    it('should create DataLoader with custom configuration', () => {
      const config = {
        enableHotReload: true,
        enableValidation: false,
        enableCache: false
      }
      
      const loader = new DataLoader(config)
      expect(loader).toBeInstanceOf(DataLoader)
    })
  })

  describe('File Loading', () => {
    it('should load JSON files successfully', async () => {
      const mockMorphemeData = [
        {
          id: 'emotions_001',
          text: '快乐',
          category: MorphemeCategory.EMOTIONS,
          subcategory: 'positive',
          cultural_context: { ancient: 0.3, modern: 0.7, neutral: 0.5 },
          usage_frequency: 0.8,
          quality_score: 0.9,
          semantic_vector: new Array(20).fill(0.1),
          tags: ['positive', 'emotion'],
          language_properties: {},
          quality_metrics: {},
          created_at: '2025-06-25',
          source: 'test',
          version: '3.0.0'
        }
      ]

      mockFs.readdir.mockResolvedValue(['emotions.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockMorphemeData))

      const result = await dataLoader.loadAll()

      expect(result.morphemes).toHaveLength(1)
      expect(result.morphemes[0].id).toBe('emotions_001')
      expect(result.stats.total_count).toBe(1)
      expect(result.validation.passed).toBe(true)
    })

    it('should handle file reading errors', async () => {
      mockFs.readdir.mockRejectedValue(new Error('Directory not found'))

      await expect(dataLoader.loadAll()).rejects.toThrow('Directory not found')
    })

    it('should handle invalid JSON files', async () => {
      mockFs.readdir.mockResolvedValue(['invalid.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue('invalid json content')

      await expect(dataLoader.loadAll()).rejects.toThrow()
    })

    it('should filter supported file formats', async () => {
      mockFs.readdir.mockResolvedValue([
        'emotions.json',
        'professions.jsonl',
        'readme.txt',
        'config.yaml'
      ] as any)

      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue('[]')

      await dataLoader.loadAll()

      // Should only read .json and .jsonl files
      expect(mockFs.readFile).toHaveBeenCalledTimes(2)
    })
  })

  describe('Data Validation', () => {
    it('should validate morpheme data structure', async () => {
      const validMorpheme = {
        id: 'emotions_001',
        text: '快乐',
        category: MorphemeCategory.EMOTIONS,
        subcategory: 'positive',
        cultural_context: { ancient: 0.3, modern: 0.7, neutral: 0.5 },
        usage_frequency: 0.8,
        quality_score: 0.9,
        semantic_vector: new Array(20).fill(0.1),
        tags: ['positive', 'emotion'],
        language_properties: {},
        quality_metrics: {},
        created_at: '2025-06-25',
        source: 'test',
        version: '3.0.0'
      }

      mockFs.readdir.mockResolvedValue(['emotions.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify([validMorpheme]))

      const result = await dataLoader.loadAll()

      expect(result.validation.passed).toBe(true)
      expect(result.validation.errors).toHaveLength(0)
    })

    it('should detect invalid morpheme data', async () => {
      const invalidMorpheme = {
        id: 'invalid_id_format',
        text: '',
        category: 'invalid_category',
        // Missing required fields
      }

      mockFs.readdir.mockResolvedValue(['invalid.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify([invalidMorpheme]))

      const result = await dataLoader.loadAll()

      expect(result.validation.passed).toBe(false)
      expect(result.validation.errors.length).toBeGreaterThan(0)
    })

    it('should handle validation with warnings', async () => {
      const morphemeWithWarnings = {
        id: 'emotions_001',
        text: '快乐',
        category: MorphemeCategory.EMOTIONS,
        subcategory: 'positive',
        cultural_context: { ancient: 0.3, modern: 0.7, neutral: 0.5 },
        usage_frequency: 0.8,
        quality_score: 0.5, // Low quality score (warning)
        semantic_vector: new Array(20).fill(0.1),
        tags: ['positive'],
        language_properties: {},
        quality_metrics: {},
        created_at: '2025-06-25',
        source: 'test',
        version: '3.0.0'
      }

      mockFs.readdir.mockResolvedValue(['emotions.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify([morphemeWithWarnings]))

      const result = await dataLoader.loadAll()

      expect(result.validation.warnings.length).toBeGreaterThan(0)
    })
  })

  describe('Caching', () => {
    it('should cache loaded data', async () => {
      const mockData = [{
        id: 'emotions_001',
        text: '快乐',
        category: MorphemeCategory.EMOTIONS,
        subcategory: 'positive',
        cultural_context: { ancient: 0.3, modern: 0.7, neutral: 0.5 },
        usage_frequency: 0.8,
        quality_score: 0.9,
        semantic_vector: new Array(20).fill(0.1),
        tags: ['positive'],
        language_properties: {},
        quality_metrics: {},
        created_at: '2025-06-25',
        source: 'test',
        version: '3.0.0'
      }]

      mockFs.readdir.mockResolvedValue(['emotions.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockData))

      // First load
      await dataLoader.loadAll()
      
      // Second load should use cache
      await dataLoader.loadAll()

      // File should only be read once due to caching
      expect(mockFs.readFile).toHaveBeenCalledTimes(1)
    })

    it('should respect cache TTL', async () => {
      const shortTTLLoader = new DataLoader({
        enableCache: true,
        cacheTTL: 0.001 // Very short TTL
      })

      const mockData = [{ id: 'test_001', text: 'test' }]
      mockFs.readdir.mockResolvedValue(['test.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockData))

      // First load
      await shortTTLLoader.loadAll()
      
      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 10))
      
      // Second load should reload from file
      await shortTTLLoader.loadAll()

      expect(mockFs.readFile).toHaveBeenCalledTimes(2)
    })
  })

  describe('Statistics Generation', () => {
    it('should generate correct statistics', async () => {
      const mockData = [
        {
          id: 'emotions_001',
          text: '快乐',
          category: MorphemeCategory.EMOTIONS,
          subcategory: 'positive',
          cultural_context: { ancient: 0.3, modern: 0.7, neutral: 0.5 },
          usage_frequency: 0.8,
          quality_score: 0.9,
          semantic_vector: new Array(20).fill(0.1),
          tags: ['positive'],
          language_properties: {},
          quality_metrics: {},
          created_at: '2025-06-25',
          source: 'test',
          version: '3.0.0'
        },
        {
          id: 'professions_001',
          text: '医生',
          category: MorphemeCategory.PROFESSIONS,
          subcategory: 'medical',
          cultural_context: { ancient: 0.2, modern: 0.8, neutral: 0.6 },
          usage_frequency: 0.7,
          quality_score: 0.8,
          semantic_vector: new Array(20).fill(0.1),
          tags: ['profession'],
          language_properties: {},
          quality_metrics: {},
          created_at: '2025-06-25',
          source: 'test',
          version: '3.0.0'
        }
      ]

      mockFs.readdir.mockResolvedValue(['data.json'] as any)
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue(JSON.stringify(mockData))

      const result = await dataLoader.loadAll()

      expect(result.stats.total_count).toBe(2)
      expect(result.stats.by_category[MorphemeCategory.EMOTIONS]).toBe(1)
      expect(result.stats.by_category[MorphemeCategory.PROFESSIONS]).toBe(1)
      expect(result.stats.avg_quality).toBe(0.85)
    })
  })

  describe('Error Recovery', () => {
    it('should retry on transient failures', async () => {
      const retryLoader = new DataLoader({ maxRetries: 3 })
      
      mockFs.readdir
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce(['emotions.json'] as any)
      
      mockFs.stat.mockResolvedValue({ isFile: () => true } as any)
      mockFs.readFile.mockResolvedValue('[]')

      await expect(retryLoader.loadAll()).resolves.toBeDefined()
      expect(mockFs.readdir).toHaveBeenCalledTimes(3)
    })

    it('should fail after max retries', async () => {
      const retryLoader = new DataLoader({ maxRetries: 2 })
      
      mockFs.readdir.mockRejectedValue(new Error('Persistent failure'))

      await expect(retryLoader.loadAll()).rejects.toThrow('Persistent failure')
      expect(mockFs.readdir).toHaveBeenCalledTimes(3) // Initial + 2 retries
    })
  })
})
