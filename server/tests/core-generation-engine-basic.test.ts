/**
 * CoreGenerationEngine 基础功能测试
 * 
 * 测试核心生成引擎的基本功能，使用mock数据避免数据验证问题
 * 目标：提升core/engines/模块测试覆盖率
 * 
 * @fileoverview 核心生成引擎基础功能测试
 * @version 3.0.0
 * @since 2025-06-25
 */

import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import {
  GenerationContext,
  MorphemeCategory,
  CulturalContext
} from '../types/core'
import { LanguageCode, ConceptCategory } from '../types/multilingual'

// Mock fs模块以避免真实文件系统依赖
jest.mock('fs', () => ({
  promises: {
    readdir: jest.fn().mockResolvedValue(['test_morphemes.json']),
    stat: jest.fn().mockResolvedValue({ isFile: () => true }),
    readFile: jest.fn().mockResolvedValue(JSON.stringify([
      {
        id: "test_001",
        text: "测试",
        category: "emotions",
        subcategory: "positive_emotions",
        cultural_context: "modern",
        usage_frequency: 0.8,
        quality_score: 0.9,
        semantic_vector: Array(20).fill(0.5),
        tags: ["test"],
        language_properties: {
          pronunciation: "cèshì",
          syllable_count: 2,
          morphological_type: "root"
        },
        quality_metrics: {
          naturalness: 0.8,
          aesthetic_appeal: 0.7
        },
        created_at: Date.now(),
        source: "test",
        version: "3.0.0"
      }
    ]))
  },
  existsSync: jest.fn().mockReturnValue(true),
  readFileSync: jest.fn().mockReturnValue(JSON.stringify([
    {
      id: "test_001",
      text: "测试",
      category: "emotions",
      subcategory: "positive_emotions",
      cultural_context: "modern",
      usage_frequency: 0.8,
      quality_score: 0.9,
      semantic_vector: Array(20).fill(0.5),
      tags: ["test"],
      language_properties: {
        pronunciation: "cèshì",
        syllable_count: 2,
        morphological_type: "root"
      },
      quality_metrics: {
        naturalness: 0.8,
        aesthetic_appeal: 0.7
      },
      created_at: Date.now(),
      source: "test",
      version: "3.0.0"
    }
  ]))
}))

describe('CoreGenerationEngine - 基础功能测试', () => {
  let engine: CoreGenerationEngine
  let testContext: GenerationContext

  beforeEach(() => {
    // 创建新的引擎实例
    engine = new CoreGenerationEngine()

    // 设置测试上下文
    testContext = {
      cultural_preference: CulturalContext.MODERN,
      style_preference: 'artistic',
      creativity_level: 0.7,
      quality_threshold: 0.6,
      patterns: [],
      exclude_patterns: [],
      context_keywords: ['情感', '现代'],
      user_preferences: {
        favorite_patterns: [],
        cultural_preference: CulturalContext.MODERN,
        style_preference: 'artistic',
        creativity_level: 0.7,
        quality_threshold: 0.6,
        excluded_categories: [],
        preferred_length: 'medium'
      },
      concept_preferences: {
        categories: [ConceptCategory.EMOTIONS],
        cultural_context: CulturalContext.MODERN,
        abstraction_level: 0.5
      },
      target_language: LanguageCode.ZH_CN
    }
  })

  describe('引擎初始化', () => {
    test('应该能够创建引擎实例', () => {
      expect(engine).toBeInstanceOf(CoreGenerationEngine)
    })

    test('应该能够检查初始化状态', () => {
      expect(engine.isReady()).toBe(false)
    })

    test('应该能够获取引擎统计信息', async () => {
      // 需要先初始化引擎才能获取统计信息
      await engine.initialize()
      const stats = engine.getStats()
      expect(stats).toBeDefined()
      expect(typeof stats.morpheme_count).toBe('number')
      expect(typeof stats.total_generations).toBe('number')
      expect(typeof stats.avg_generation_time).toBe('number')
      expect(typeof stats.success_rate).toBe('number')
      await engine.destroy()
    })
  })

  describe('引擎方法调用', () => {
    test('应该能够调用generate方法', async () => {
      // 测试正常生成行为
      const result = await engine.generate(testContext)
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeGreaterThan(0)
      if (result.length > 0) {
        expect(result[0]).toHaveProperty('text')
        expect(result[0]).toHaveProperty('quality_score')
        expect(result[0]).toHaveProperty('pattern')
      }
    })

    test('应该能够调用generateMultilingual方法', async () => {
      // 测试正常多语种生成行为
      const result = await engine.generateMultilingual(testContext, 5, LanguageCode.ZH_CN)
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeGreaterThan(0)
      if (result.length > 0) {
        expect(result[0]).toHaveProperty('text')
        expect(result[0]).toHaveProperty('quality_score')
        expect(result[0]).toHaveProperty('pattern')
      }
    })

    test('应该能够调用destroy方法', async () => {
      expect(() => engine.destroy()).not.toThrow()
    })
  })

  describe('参数验证', () => {
    test('应该验证generate方法的参数', async () => {
      // 测试空参数 - 引擎会处理并返回空数组
      const result = await engine.generate(null as any)
      expect(Array.isArray(result)).toBe(true)
    })

    test('应该验证generateMultilingual方法的参数', async () => {
      // 测试空参数 - 引擎会处理并返回空数组
      const result = await engine.generateMultilingual(null as any, 5, LanguageCode.ZH_CN)
      expect(Array.isArray(result)).toBe(true)
    })

    test('应该验证生成数量参数', async () => {
      // 测试无效的生成数量
      await expect(engine.generateMultilingual(testContext, 0, LanguageCode.ZH_CN)).rejects.toThrow('生成数量必须在1-10之间')
      await expect(engine.generateMultilingual(testContext, 11, LanguageCode.ZH_CN)).rejects.toThrow('生成数量必须在1-10之间')
    })
  })

  describe('错误处理', () => {
    test('应该处理初始化', async () => {
      // 测试初始化过程
      const mockEngine = new CoreGenerationEngine()
      await expect(mockEngine.initialize()).resolves.not.toThrow()
      await mockEngine.destroy()
    })

    test('应该处理生成过程', async () => {
      // 测试生成过程 - 引擎会自动初始化
      const result = await engine.generate(testContext)
      expect(Array.isArray(result)).toBe(true)
    })
  })

  describe('状态管理', () => {
    test('应该正确管理引擎状态', async () => {
      expect(engine.isReady()).toBe(false)

      // 初始化后再获取统计信息
      await engine.initialize()
      const stats = engine.getStats()
      expect(stats).toBeDefined()
      expect(stats.morpheme_count).toBeGreaterThanOrEqual(0)
      await engine.destroy()
    })

    test('应该能够重复调用destroy', () => {
      engine.destroy()
      expect(() => engine.destroy()).not.toThrow()
    })
  })

  describe('配置测试', () => {
    test('应该能够处理不同的生成上下文', async () => {
      const contexts = [
        { ...testContext, creativity_level: 0.1 },
        { ...testContext, creativity_level: 0.9 },
        { ...testContext, quality_threshold: 0.1 },
        { ...testContext, quality_threshold: 0.9 }
      ]

      for (const context of contexts) {
        const result = await engine.generate(context)
        expect(Array.isArray(result)).toBe(true)
        // 不同配置可能产生不同数量的结果，但都应该是数组
      }
    })

    test('应该能够处理不同的生成数量', async () => {
      const counts = [1, 3, 5]

      for (const count of counts) {
        const result = await engine.generateMultilingual(testContext, count, LanguageCode.ZH_CN)
        expect(Array.isArray(result)).toBe(true)
        // 由于只有一个测试语素，可能无法生成足够数量，但不应该出错
      }
    })
  })
})
