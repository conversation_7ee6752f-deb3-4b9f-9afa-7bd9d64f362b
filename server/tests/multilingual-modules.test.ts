/**
 * 多语种模块测试套件
 * 
 * 测试LanguageManager、SemanticAligner等多语种相关组件
 * 
 * @fileoverview 多语种模块测试
 * @version 1.0.0
 * @since 2025-06-25
 */

import { LanguageManager } from '../core/multilingual/LanguageManager'
import { LanguageCode } from '../types/multilingual'
import { 
  MorphemeCategory, 
  CulturalContext,
  Morpheme
} from '../types/core'

describe('LanguageManager', () => {
  let languageManager: LanguageManager

  beforeEach(() => {
    languageManager = new LanguageManager({
      defaultLanguage: LanguageCode.ZH_CN,
      enabledLanguages: [LanguageCode.ZH_CN, LanguageCode.EN_US],
      enableCache: true,
      cacheTTL: 3600
    })
  })

  afterEach(() => {
    if (languageManager) {
      languageManager.destroy?.()
    }
  })

  describe('构造函数和初始化', () => {
    test('应该成功创建语言管理器实例', () => {
      expect(languageManager).toBeInstanceOf(LanguageManager)
    })

    test('应该成功初始化语言管理器', async () => {
      await languageManager.initialize()
      expect(languageManager.isReady()).toBe(true)
    })

    test('应该设置正确的默认语言', () => {
      expect(languageManager.getDefaultLanguage()).toBe(LanguageCode.ZH_CN)
    })

    test('应该返回启用的语言列表', () => {
      const languages = languageManager.getSupportedLanguages()
      expect(languages).toContain(LanguageCode.ZH_CN)
      expect(languages).toContain(LanguageCode.EN_US)
    })
  })

  describe('语言支持检查', () => {
    beforeEach(async () => {
      await languageManager.initialize()
    })

    test('应该正确检查语言支持状态', () => {
      expect(languageManager.isLanguageSupported(LanguageCode.ZH_CN)).toBe(true)
      expect(languageManager.isLanguageSupported(LanguageCode.EN_US)).toBe(true)
      expect(languageManager.isLanguageSupported(LanguageCode.JA_JP)).toBe(false)
    })

    test('应该返回语言统计信息', () => {
      const stats = languageManager.getStats()
      expect(typeof stats).toBe('object')
    })
  })

  describe('语素管理', () => {
    beforeEach(async () => {
      await languageManager.initialize()
    })

    test('应该获取指定语言的语素', () => {
      const morphemes = languageManager.getMorphemesByLanguage(LanguageCode.ZH_CN)
      expect(Array.isArray(morphemes)).toBe(true)
    })

    test('应该根据类别获取语素', () => {
      const morphemes = languageManager.getMorphemesByCategory(
        LanguageCode.ZH_CN, 
        MorphemeCategory.EMOTIONS
      )
      expect(Array.isArray(morphemes)).toBe(true)
    })

    test('应该根据文化语境获取语素', () => {
      const morphemes = languageManager.getMorphemesByCulturalContext(
        LanguageCode.ZH_CN,
        CulturalContext.MODERN
      )
      expect(Array.isArray(morphemes)).toBe(true)
    })
  })

  describe('语言重载', () => {
    beforeEach(async () => {
      await languageManager.initialize()
    })

    test('应该支持重载特定语言', async () => {
      await expect(
        languageManager.reloadLanguage(LanguageCode.ZH_CN)
      ).resolves.not.toThrow()
    })

    test('应该处理重载不支持的语言', async () => {
      await expect(
        languageManager.reloadLanguage(LanguageCode.JA_JP)
      ).rejects.toThrow()
    })
  })
})

