/**
 * 简单的CoreGenerationEngine导入测试
 * 用于验证基本导入和实例化是否正常工作
 */

describe('CoreGenerationEngine - 基础导入测试', () => {
  test('应该能够导入CoreGenerationEngine', () => {
    // 尝试动态导入以避免编译时错误
    expect(() => {
      const { CoreGenerationEngine } = require('../core/engines/CoreGenerationEngine')
      expect(CoreGenerationEngine).toBeDefined()
    }).not.toThrow()
  })

  test('应该能够创建CoreGenerationEngine实例', () => {
    const { CoreGenerationEngine } = require('../core/engines/CoreGenerationEngine')
    const engine = new CoreGenerationEngine()
    expect(engine).toBeInstanceOf(CoreGenerationEngine)
  })
})
