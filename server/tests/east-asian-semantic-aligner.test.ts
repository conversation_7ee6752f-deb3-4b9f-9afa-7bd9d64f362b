/**
 * EastAsianSemanticAligner 测试套件
 * 
 * 测试东亚语言语义对齐器的核心功能，包括：
 * - 中日韩语言间的语义对齐
 * - 汉字文化圈相似度计算
 * - 音韵相似度分析
 * - 文化适应度评估
 * 
 * @fileoverview 东亚语言语义对齐器测试
 * @version 3.0.0
 * @since 2025-06-25
 */

import { EastAsianSemanticAligner } from '../core/multilingual/EastAsianSemanticAligner'
import { LanguageCode, RegisterLevel } from '../types/multilingual'
import type { LanguageSpecificMorpheme, SemanticVector } from '../types/multilingual'

describe('EastAsianSemanticAligner', () => {
  let aligner: EastAsianSemanticAligner

  beforeEach(() => {
    aligner = new EastAsianSemanticAligner()
  })

  // 创建测试用的语义向量
  const createSemanticVector = (dimensions: number[]): SemanticVector => ({
    dimensions,
    model_version: 'test-v1.0',
    generated_at: new Date().toISOString()
  })

  // 创建测试用的中文语素
  const createChineseMorpheme = (text: string, conceptId: string = 'test-concept'): LanguageSpecificMorpheme => ({
    morpheme_id: 'test-zh-1',
    text,
    language: LanguageCode.ZH_CN,
    concept_id: conceptId,
    register_level: RegisterLevel.NEUTRAL,
    alternative_forms: [],
    phonetic_features: {
      ipa_transcription: 'test',
      syllable_count: 2,
      phonetic_harmony: 0.8,
      tone_pattern: ['1', '2']
    },
    morphological_info: {
      pos_tag: 'NOUN',
      morphological_type: 'simple',
      prefixes: [],
      suffixes: [],
      kanji_info: {
        kanji: text,
        stroke_count: 10,
        radical: '心',
        meaning: 'heart'
      }
    },
    syntactic_properties: {
      syntactic_function: ['subject', 'object'],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      formality: 0.5,
      traditionality: 0.8,
      regionality: 0.5,
      modernity: 0.5,
      religious_sensitivity: 0.5,
      age_appropriateness: ['all'],
      cultural_tags: ['traditional', 'emotion']
    },
    regional_variants: [],
    language_quality_scores: {
      naturalness: 0.9,
      fluency: 0.9,
      authenticity: 0.9,
      aesthetic_appeal: 0.8,
      pronunciation_ease: 0.7,
      memorability: 0.8,
      uniqueness: 0.7,
      practicality: 0.8
    },
    semantic_vector: createSemanticVector([0.8, 0.6, 0.4, 0.2, 0.1]),
    cultural_appropriateness: 0.9,
    native_speaker_rating: 0.9,
    usage_frequency: 0.8,
    popularity_trend: 0.7,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: '3.0.0',
    source: 'test',
    validation_status: 'validated' as const
  })

  // 创建测试用的日语语素
  const createJapaneseMorpheme = (text: string, conceptId: string = 'test-concept'): LanguageSpecificMorpheme => ({
    morpheme_id: 'test-ja-1',
    text,
    language: LanguageCode.JA_JP,
    concept_id: conceptId,
    register_level: RegisterLevel.NEUTRAL,
    alternative_forms: [],
    phonetic_features: {
      ipa_transcription: 'kokoro',
      syllable_count: 3,
      phonetic_harmony: 0.8,
      mora_count: 3,
      accent_pattern: 'LHL',
      romanization: 'kokoro'
    },
    morphological_info: {
      pos_tag: 'NOUN',
      morphological_type: 'simple',
      prefixes: [],
      suffixes: [],
      kanji_info: {
        kanji: '心',
        stroke_count: 4,
        radical: '心',
        meaning: 'heart',
        on_reading: ['シン'],
        kun_reading: ['こころ']
      }
    },
    syntactic_properties: {
      syntactic_function: ['subject', 'object'],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      formality: 0.6,
      traditionality: 0.8,
      regionality: 0.5,
      modernity: 0.5,
      religious_sensitivity: 0.6,
      age_appropriateness: ['all'],
      cultural_tags: ['traditional', 'emotion', 'zen']
    },
    regional_variants: [],
    language_quality_scores: {
      naturalness: 0.9,
      fluency: 0.9,
      authenticity: 0.9,
      aesthetic_appeal: 0.9,
      pronunciation_ease: 0.8,
      memorability: 0.9,
      uniqueness: 0.8,
      practicality: 0.8
    },
    semantic_vector: createSemanticVector([0.8, 0.6, 0.4, 0.2, 0.1]),
    cultural_appropriateness: 0.9,
    native_speaker_rating: 0.9,
    usage_frequency: 0.8,
    popularity_trend: 0.7,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: '3.0.0',
    source: 'test',
    validation_status: 'validated' as const
  })

  // 创建测试用的韩语语素
  const createKoreanMorpheme = (text: string, conceptId: string = 'test-concept'): LanguageSpecificMorpheme => ({
    morpheme_id: 'test-ko-1',
    text,
    language: LanguageCode.KO_KR,
    concept_id: conceptId,
    register_level: RegisterLevel.NEUTRAL,
    alternative_forms: [],
    phonetic_features: {
      ipa_transcription: 'maɯm',
      syllable_count: 1,
      phonetic_harmony: 0.8,
      hangul_structure: 'CVC',
      consonant_clusters: false
    },
    morphological_info: {
      pos_tag: 'NOUN',
      morphological_type: 'simple',
      prefixes: [],
      suffixes: [],
      hanja_info: {
        hanja: '心',
        meaning: 'heart',
        usage_frequency: 0.8
      }
    },
    syntactic_properties: {
      syntactic_function: ['subject', 'object'],
      collocation_constraints: [],
      grammatical_features: {}
    },
    cultural_context: {
      formality: 0.6,
      traditionality: 0.8,
      regionality: 0.5,
      modernity: 0.5,
      religious_sensitivity: 0.5,
      age_appropriateness: ['all'],
      cultural_tags: ['traditional', 'emotion']
    },
    regional_variants: [],
    language_quality_scores: {
      naturalness: 0.9,
      fluency: 0.9,
      authenticity: 0.9,
      aesthetic_appeal: 0.8,
      pronunciation_ease: 0.9,
      memorability: 0.8,
      uniqueness: 0.7,
      practicality: 0.9
    },
    semantic_vector: createSemanticVector([0.8, 0.6, 0.4, 0.2, 0.1]),
    cultural_appropriateness: 0.9,
    native_speaker_rating: 0.9,
    usage_frequency: 0.8,
    popularity_trend: 0.7,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    version: '3.0.0',
    source: 'test',
    validation_status: 'validated' as const
  })

  describe('Constructor', () => {
    it('应该正确初始化东亚语言语义对齐器', () => {
      expect(aligner).toBeInstanceOf(EastAsianSemanticAligner)
    })
  })

  describe('East Asian Alignment Calculation', () => {
    it('应该计算中日语言对齐', async () => {
      const chineseMorpheme = createChineseMorpheme('心', 'heart-concept')
      chineseMorpheme.alternative_forms = ['心'] // 添加汉字到alternative_forms

      const japaneseMorpheme = createJapaneseMorpheme('心', 'heart-concept')
      japaneseMorpheme.alternative_forms = ['心'] // 添加汉字到alternative_forms

      const result = await aligner.calculateEastAsianAlignment(chineseMorpheme, japaneseMorpheme)

      expect(result.semantic_similarity).toBeGreaterThan(0.8) // 相同概念ID
      expect(result.sino_sphere_similarity).toBeGreaterThanOrEqual(0) // 汉字文化圈
      expect(result.phonetic_similarity).toBeGreaterThanOrEqual(0)
      expect(result.cultural_adaptation).toBeGreaterThanOrEqual(0)
      expect(result.overall_alignment).toBeGreaterThan(0.5)
    })

    it('应该计算中韩语言对齐', async () => {
      const chineseMorpheme = createChineseMorpheme('心', 'heart-concept')
      chineseMorpheme.alternative_forms = ['心'] // 添加汉字到alternative_forms

      const koreanMorpheme = createKoreanMorpheme('마음', 'heart-concept')
      koreanMorpheme.alternative_forms = ['心'] // 韩语也有汉字词源

      const result = await aligner.calculateEastAsianAlignment(chineseMorpheme, koreanMorpheme)

      expect(result.semantic_similarity).toBeGreaterThan(0.8) // 相同概念ID
      expect(result.sino_sphere_similarity).toBeGreaterThanOrEqual(0) // 汉字文化圈影响
      expect(result.overall_alignment).toBeGreaterThan(0.5)
    })

    it('应该计算日韩语言对齐', async () => {
      const japaneseMorpheme = createJapaneseMorpheme('心', 'heart-concept')
      const koreanMorpheme = createKoreanMorpheme('마음', 'heart-concept')

      const result = await aligner.calculateEastAsianAlignment(japaneseMorpheme, koreanMorpheme)

      expect(result.semantic_similarity).toBeGreaterThan(0.8) // 相同概念ID
      expect(result.sino_sphere_similarity).toBeGreaterThanOrEqual(0)
      expect(result.overall_alignment).toBeGreaterThan(0.5)
    })
  })

  describe('Semantic Similarity', () => {
    it('应该为相同概念ID返回高相似度', async () => {
      const morpheme1 = createChineseMorpheme('爱', 'love-concept')
      const morpheme2 = createJapaneseMorpheme('愛', 'love-concept')

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.semantic_similarity).toBeGreaterThan(0.9)
    })

    it('应该基于语义向量计算相似度', async () => {
      const morpheme1 = createChineseMorpheme('快乐', 'happiness-concept')
      morpheme1.semantic_vector = createSemanticVector([0.9, 0.8, 0.7, 0.6, 0.5])
      
      const morpheme2 = createJapaneseMorpheme('幸せ', 'joy-concept')
      morpheme2.semantic_vector = createSemanticVector([0.8, 0.7, 0.6, 0.5, 0.4])

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.semantic_similarity).toBeGreaterThan(0.5)
      expect(result.semantic_similarity).toBeLessThan(1.0)
    })
  })

  describe('Sino-Sphere Similarity', () => {
    it('应该检测汉字相似度', async () => {
      const chineseMorpheme = createChineseMorpheme('美丽', 'beauty-concept')
      chineseMorpheme.alternative_forms = ['美丽'] // 添加汉字到alternative_forms

      const japaneseMorpheme = createJapaneseMorpheme('美しい', 'beauty-concept')
      japaneseMorpheme.alternative_forms = ['美'] // 共同汉字"美"

      const result = await aligner.calculateEastAsianAlignment(chineseMorpheme, japaneseMorpheme)

      expect(result.sino_sphere_similarity).toBeGreaterThanOrEqual(0) // 共同汉字"美"
    })

    it('应该处理文化概念相似度', async () => {
      const morpheme1 = createChineseMorpheme('禅', 'zen-concept')
      morpheme1.cultural_context.cultural_tags = ['zen', 'buddhism', 'meditation']
      morpheme1.alternative_forms = ['禅'] // 添加汉字

      const morpheme2 = createJapaneseMorpheme('禅', 'zen-concept')
      morpheme2.cultural_context.cultural_tags = ['zen', 'buddhism', 'meditation']
      morpheme2.alternative_forms = ['禅'] // 添加汉字

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.sino_sphere_similarity).toBeGreaterThanOrEqual(0)
      expect(result.cultural_adaptation).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Phonetic Similarity', () => {
    it('应该计算IPA转录相似度', async () => {
      const morpheme1 = createChineseMorpheme('妈', 'mother-concept')
      morpheme1.phonetic_features.ipa_transcription = 'ma'
      
      const morpheme2 = createJapaneseMorpheme('ママ', 'mother-concept')
      morpheme2.phonetic_features.ipa_transcription = 'mama'

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.phonetic_similarity).toBeGreaterThan(0.3)
    })

    it('应该处理缺失音韵特征', async () => {
      const morpheme1 = createChineseMorpheme('测试', 'test-concept')
      morpheme1.phonetic_features = {
        ipa_transcription: '',
        syllable_count: 2,
        phonetic_harmony: 0.5
      }
      
      const morpheme2 = createJapaneseMorpheme('テスト', 'test-concept')

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.phonetic_similarity).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Edge Cases', () => {
    it('应该处理空文本', async () => {
      const morpheme1 = createChineseMorpheme('', 'empty-concept')
      const morpheme2 = createJapaneseMorpheme('', 'empty-concept')

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.overall_alignment).toBeGreaterThanOrEqual(0)
      expect(result.overall_alignment).toBeLessThanOrEqual(1)
    })

    it('应该处理不同概念ID', async () => {
      const morpheme1 = createChineseMorpheme('水', 'water-concept')
      const morpheme2 = createJapaneseMorpheme('火', 'fire-concept')

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.semantic_similarity).toBeGreaterThanOrEqual(0) // 基于文本相似度
      expect(result.overall_alignment).toBeGreaterThanOrEqual(0)
    })

    it('应该处理缺失语义向量', async () => {
      const morpheme1 = createChineseMorpheme('测试', 'test-concept')
      morpheme1.semantic_vector = undefined
      
      const morpheme2 = createJapaneseMorpheme('テスト', 'different-concept')
      morpheme2.semantic_vector = undefined

      const result = await aligner.calculateEastAsianAlignment(morpheme1, morpheme2)

      expect(result.overall_alignment).toBeGreaterThanOrEqual(0)
      expect(result.overall_alignment).toBeLessThanOrEqual(1)
    })
  })
})
