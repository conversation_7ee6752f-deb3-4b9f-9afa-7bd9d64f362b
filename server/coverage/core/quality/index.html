
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/quality</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> core/quality</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.81% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>133/418</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">30.32% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>84/277</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.23% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>39/102</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.51% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>121/384</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="EastAsianQualityAssessor.ts"><a href="EastAsianQualityAssessor.ts.html">EastAsianQualityAssessor.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="131" class="abs low">0/131</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="84" class="abs low">0/84</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="127" class="abs low">0/127</td>
	</tr>

<tr>
	<td class="file low" data-value="EuropeanQualityAssessor.ts"><a href="EuropeanQualityAssessor.ts.html">EuropeanQualityAssessor.ts</a></td>
	<td data-value="46.34" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 46%"></div><div class="cover-empty" style="width: 54%"></div></div>
	</td>
	<td data-value="46.34" class="pct low">46.34%</td>
	<td data-value="287" class="abs low">133/287</td>
	<td data-value="43.52" class="pct low">43.52%</td>
	<td data-value="193" class="abs low">84/193</td>
	<td data-value="50.64" class="pct medium">50.64%</td>
	<td data-value="77" class="abs medium">39/77</td>
	<td data-value="47.08" class="pct low">47.08%</td>
	<td data-value="257" class="abs low">121/257</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T08:41:57.976Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    