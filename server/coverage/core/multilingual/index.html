
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/multilingual</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> core/multilingual</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">23.82% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>157/659</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.98% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>75/417</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.91% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>24/134</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">23.12% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>139/601</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="EastAsianLanguageProcessor.ts"><a href="EastAsianLanguageProcessor.ts.html">EastAsianLanguageProcessor.ts</a></td>
	<td data-value="8.43" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.43" class="pct low">8.43%</td>
	<td data-value="83" class="abs low">7/83</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="58" class="abs low">0/58</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="24" class="abs low">2/24</td>
	<td data-value="10.14" class="pct low">10.14%</td>
	<td data-value="69" class="abs low">7/69</td>
	</tr>

<tr>
	<td class="file high" data-value="EastAsianSemanticAligner.ts"><a href="EastAsianSemanticAligner.ts.html">EastAsianSemanticAligner.ts</a></td>
	<td data-value="93.16" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 93%"></div><div class="cover-empty" style="width: 7%"></div></div>
	</td>
	<td data-value="93.16" class="pct high">93.16%</td>
	<td data-value="161" class="abs high">150/161</td>
	<td data-value="73.52" class="pct medium">73.52%</td>
	<td data-value="102" class="abs medium">75/102</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="22" class="abs high">22/22</td>
	<td data-value="94.96" class="pct high">94.96%</td>
	<td data-value="139" class="abs high">132/139</td>
	</tr>

<tr>
	<td class="file low" data-value="EuropeanLanguageProcessor.ts"><a href="EuropeanLanguageProcessor.ts.html">EuropeanLanguageProcessor.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="197" class="abs low">0/197</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="153" class="abs low">0/153</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="47" class="abs low">0/47</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="188" class="abs low">0/188</td>
	</tr>

<tr>
	<td class="file low" data-value="LanguageManager.ts"><a href="LanguageManager.ts.html">LanguageManager.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="86" class="abs low">0/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="65" class="abs low">0/65</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="85" class="abs low">0/85</td>
	</tr>

<tr>
	<td class="file low" data-value="SemanticAligner.ts"><a href="SemanticAligner.ts.html">SemanticAligner.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="132" class="abs low">0/132</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="120" class="abs low">0/120</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T16:57:29.994Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    