
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/multilingual</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> core/multilingual</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.23% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>252/659</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">34.05% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>142/417</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50.74% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>68/134</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40.09% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>241/601</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="EastAsianLanguageProcessor.ts"><a href="EastAsianLanguageProcessor.ts.html">EastAsianLanguageProcessor.ts</a></td>
	<td data-value="89.15" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.15" class="pct high">89.15%</td>
	<td data-value="83" class="abs high">74/83</td>
	<td data-value="65.51" class="pct medium">65.51%</td>
	<td data-value="58" class="abs medium">38/58</td>
	<td data-value="95.83" class="pct high">95.83%</td>
	<td data-value="24" class="abs high">23/24</td>
	<td data-value="95.65" class="pct high">95.65%</td>
	<td data-value="69" class="abs high">66/69</td>
	</tr>

<tr>
	<td class="file low" data-value="EastAsianSemanticAligner.ts"><a href="EastAsianSemanticAligner.ts.html">EastAsianSemanticAligner.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="158" class="abs low">0/158</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="102" class="abs low">0/102</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="136" class="abs low">0/136</td>
	</tr>

<tr>
	<td class="file high" data-value="EuropeanLanguageProcessor.ts"><a href="EuropeanLanguageProcessor.ts.html">EuropeanLanguageProcessor.ts</a></td>
	<td data-value="89.44" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.44" class="pct high">89.44%</td>
	<td data-value="199" class="abs high">178/199</td>
	<td data-value="67.97" class="pct medium">67.97%</td>
	<td data-value="153" class="abs medium">104/153</td>
	<td data-value="95.74" class="pct high">95.74%</td>
	<td data-value="47" class="abs high">45/47</td>
	<td data-value="92.1" class="pct high">92.1%</td>
	<td data-value="190" class="abs high">175/190</td>
	</tr>

<tr>
	<td class="file low" data-value="LanguageManager.ts"><a href="LanguageManager.ts.html">LanguageManager.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="87" class="abs low">0/87</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="65" class="abs low">0/65</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="86" class="abs low">0/86</td>
	</tr>

<tr>
	<td class="file low" data-value="SemanticAligner.ts"><a href="SemanticAligner.ts.html">SemanticAligner.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="132" class="abs low">0/132</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="120" class="abs low">0/120</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T06:54:48.059Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    