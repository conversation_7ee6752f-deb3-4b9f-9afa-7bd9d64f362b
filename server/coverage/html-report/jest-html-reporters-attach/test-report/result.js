window.jest_html_reporters_callback__({"numFailedTestSuites":2,"numFailedTests":10,"numPassedTestSuites":6,"numPassedTests":98,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":8,"numTotalTests":108,"startTime":1750836746683,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836751072,"loadTestEnvironmentEnd":1750836747464,"loadTestEnvironmentStart":1750836747077,"runtime":3607,"setupAfterEnvEnd":1750836747975,"setupAfterEnvStart":1750836747975,"setupFilesEnd":1750836747465,"setupFilesStart":1750836747465,"slow":false,"start":1750836747465},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":31,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 CulturalContext 枚举","status":"passed","title":"应该正确定义 CulturalContext 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 RegisterLevel 枚举","status":"passed","title":"应该正确定义 RegisterLevel 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持枚举值的类型检查","status":"passed","title":"应该支持枚举值的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持语言代码的类型检查","status":"passed","title":"应该支持语言代码的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持寄存器级别的类型检查","status":"passed","title":"应该支持寄存器级别的类型检查"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该正确配置语义向量维度","status":"passed","title":"应该正确配置语义向量维度"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该支持向量维度的数学运算","status":"passed","title":"应该支持向量维度的数学运算"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持多语言标签映射","status":"passed","title":"应该支持多语言标签映射"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持文化适应性配置","status":"passed","title":"应该支持文化适应性配置"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持语音特征配置","status":"passed","title":"应该支持语音特征配置"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该支持8维度质量评分","status":"passed","title":"应该支持8维度质量评分"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该计算平均质量评分","status":"passed","title":"应该计算平均质量评分"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理无效的枚举值","status":"passed","title":"应该处理无效的枚举值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理边界值","status":"passed","title":"应该处理边界值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理空数组和空对象","status":"passed","title":"应该处理空数组和空对象"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836751270,"loadTestEnvironmentEnd":1750836747489,"loadTestEnvironmentStart":1750836747073,"runtime":3780,"setupAfterEnvEnd":1750836747985,"setupAfterEnvStart":1750836747985,"setupFilesEnd":1750836747490,"setupFilesStart":1750836747490,"slow":false,"start":1750836747490},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-quality-assessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":7,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":17,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should assess Spanish morpheme quality","status":"passed","title":"should assess Spanish morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should include European-specific metrics for Spanish","status":"passed","title":"should include European-specific metrics for Spanish"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should give high scores for traditional Spanish words","status":"passed","title":"should give high scores for traditional Spanish words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French morpheme quality","status":"passed","title":"should assess French morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should give high elegance scores for French words","status":"passed","title":"should give high elegance scores for French words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French phonetic harmony","status":"passed","title":"should assess French phonetic harmony"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morpheme quality","status":"passed","title":"should assess German morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should give high precision scores for German words","status":"passed","title":"should give high precision scores for German words"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morphological complexity","status":"passed","title":"should assess German morphological complexity"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should handle German compound words","status":"passed","title":"should handle German compound words"},{"ancestorTitles":["EuropeanQualityAssessor","Cross-Language Comparison"],"duration":5,"failureMessages":[],"fullName":"EuropeanQualityAssessor Cross-Language Comparison should provide consistent quality assessment across languages","status":"passed","title":"should provide consistent quality assessment across languages"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":170,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should handle missing cultural context gracefully","status":"passed","title":"should handle missing cultural context gracefully"}]},{"numFailingTests":0,"numPassingTests":19,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836751393,"loadTestEnvironmentEnd":1750836751115,"loadTestEnvironmentStart":1750836751105,"runtime":278,"setupAfterEnvEnd":1750836751235,"setupAfterEnvStart":1750836751235,"setupFilesEnd":1750836751115,"setupFilesStart":1750836751115,"slow":false,"start":1750836751115},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-language-processor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":3,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should process Spanish morpheme correctly","status":"passed","title":"should process Spanish morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should extract Spanish phonetic features","status":"passed","title":"should extract Spanish phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should detect Spanish gender correctly","status":"passed","title":"should detect Spanish gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should adjust Spanish cultural context","status":"passed","title":"should adjust Spanish cultural context"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":6,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should process French morpheme correctly","status":"passed","title":"should process French morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should extract French phonetic features","status":"passed","title":"should extract French phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should detect French gender correctly","status":"passed","title":"should detect French gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should calculate French elegance","status":"passed","title":"should calculate French elegance"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should process German morpheme correctly","status":"passed","title":"should process German morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should extract German phonetic features","status":"passed","title":"should extract German phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should detect German gender correctly","status":"passed","title":"should detect German gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should analyze German compound structure","status":"passed","title":"should analyze German compound structure"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should calculate German precision","status":"passed","title":"should calculate German precision"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":89,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should handle missing morphological info gracefully","status":"passed","title":"should handle missing morphological info gracefully"}]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836751496,"loadTestEnvironmentEnd":1750836751330,"loadTestEnvironmentStart":1750836751317,"runtime":166,"setupAfterEnvEnd":1750836751433,"setupAfterEnvStart":1750836751433,"setupFilesEnd":1750836751330,"setupFilesStart":1750836751330,"slow":false,"start":1750836751330},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/unit/EastAsianLanguageProcessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该正确识别东亚语言","status":"passed","title":"应该正确识别东亚语言"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的日语配置","status":"passed","title":"应该返回正确的日语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的韩语配置","status":"passed","title":"应该返回正确的韩语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":3,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的假名信息","status":"passed","title":"应该正确处理日语语素的假名信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":8,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的汉字信息","status":"passed","title":"应该正确处理日语语素的汉字信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语敬语系统","status":"passed","title":"应该正确处理日语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的韩文信息","status":"passed","title":"应该正确处理韩语语素的韩文信息"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的汉字词汇","status":"passed","title":"应该正确处理韩语语素的汉字词汇"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语敬语系统","status":"passed","title":"应该正确处理韩语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","语言特性分析"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言特性分析 应该正确计算音拍数","status":"passed","title":"应该正确计算音拍数"}]},{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836751577,"loadTestEnvironmentEnd":1750836751432,"loadTestEnvironmentStart":1750836751425,"runtime":145,"setupAfterEnvEnd":1750836751516,"setupAfterEnvStart":1750836751516,"setupFilesEnd":1750836751432,"setupFilesStart":1750836751432,"slow":false,"start":1750836751432},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 Morpheme 类型","status":"passed","title":"应该正确定义 Morpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GenerationContext 类型","status":"passed","title":"应该正确定义 GenerationContext 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GeneratedUsername 类型","status":"passed","title":"应该正确定义 GeneratedUsername 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 UniversalConcept 类型","status":"passed","title":"应该正确定义 UniversalConcept 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageSpecificMorpheme 类型","status":"passed","title":"应该正确定义 LanguageSpecificMorpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":5,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持传统 CulturalContext 枚举","status":"passed","title":"应该支持传统 CulturalContext 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持多维度 CulturalContext 接口","status":"passed","title":"应该支持多维度 CulturalContext 接口"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持语义向量的向后兼容","status":"passed","title":"应该支持语义向量的向后兼容"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该正确推断函数返回类型","status":"passed","title":"应该正确推断函数返回类型"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该支持泛型类型约束","status":"passed","title":"应该支持泛型类型约束"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该防止类型错误的赋值","status":"passed","title":"应该防止类型错误的赋值"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该确保必需字段的存在","status":"passed","title":"应该确保必需字段的存在"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":6,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该正确计算8维度质量评分","status":"passed","title":"应该正确计算8维度质量评分"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":6,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该验证多语种质量评分的一致性","status":"passed","title":"应该验证多语种质量评分的一致性"}]},{"numFailingTests":0,"numPassingTests":15,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836751581,"loadTestEnvironmentEnd":1750836747487,"loadTestEnvironmentStart":1750836747084,"runtime":4090,"setupAfterEnvEnd":1750836748007,"setupAfterEnvStart":1750836748007,"setupFilesEnd":1750836747491,"setupFilesStart":1750836747491,"slow":false,"start":1750836747491},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/data-validator.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["DataValidator","Constructor"],"duration":9,"failureMessages":[],"fullName":"DataValidator Constructor should create DataValidator with default configuration","status":"passed","title":"should create DataValidator with default configuration"},{"ancestorTitles":["DataValidator","Constructor"],"duration":1,"failureMessages":[],"fullName":"DataValidator Constructor should create DataValidator with custom configuration","status":"passed","title":"should create DataValidator with custom configuration"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":133,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should validate a valid morpheme","status":"passed","title":"should validate a valid morpheme"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":14,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid ID format","status":"passed","title":"should detect invalid ID format"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":9,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid text length","status":"passed","title":"should detect invalid text length"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":11,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid category","status":"passed","title":"should detect invalid category"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":9,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid quality score","status":"passed","title":"should detect invalid quality score"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":15,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid semantic vector dimension","status":"passed","title":"should detect invalid semantic vector dimension"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":12,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect missing required fields","status":"passed","title":"should detect missing required fields"},{"ancestorTitles":["DataValidator","Batch Validation"],"duration":11,"failureMessages":[],"fullName":"DataValidator Batch Validation should validate multiple morphemes","status":"passed","title":"should validate multiple morphemes"},{"ancestorTitles":["DataValidator","Batch Validation"],"duration":17,"failureMessages":[],"fullName":"DataValidator Batch Validation should handle mixed valid and invalid morphemes","status":"passed","title":"should handle mixed valid and invalid morphemes"},{"ancestorTitles":["DataValidator","Warning Detection"],"duration":3,"failureMessages":[],"fullName":"DataValidator Warning Detection should detect inconsistent ID and category as warnings","status":"passed","title":"should detect inconsistent ID and category as warnings"},{"ancestorTitles":["DataValidator","Warning Detection"],"duration":8,"failureMessages":[],"fullName":"DataValidator Warning Detection should detect inconsistent character count as warnings","status":"passed","title":"should detect inconsistent character count as warnings"},{"ancestorTitles":["DataValidator","Custom Rules"],"duration":23,"failureMessages":[],"fullName":"DataValidator Custom Rules should support adding custom validation rules","status":"passed","title":"should support adding custom validation rules"},{"ancestorTitles":["DataValidator","Performance"],"duration":14,"failureMessages":[],"fullName":"DataValidator Performance should validate large batches efficiently","status":"passed","title":"should validate large batches efficiently"}]},{"numFailingTests":10,"numPassingTests":4,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750836752125,"loadTestEnvironmentEnd":1750836747494,"loadTestEnvironmentStart":1750836747065,"runtime":4630,"setupAfterEnvEnd":1750836747993,"setupAfterEnvStart":1750836747993,"setupFilesEnd":1750836747495,"setupFilesStart":1750836747495,"slow":false,"start":1750836747495},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts","failureMessage":"  ● DataLoader › File Loading › should load JSON files successfully\n\n    expect(received).toHaveLength(expected)\n\n    Expected length: 1\n    Received length: 152\n    Received array:  [{\"alternative_forms\": [Array], \"concept_id\": \"concept_warm\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_warm_001\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"warm\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_fresh\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_fresh_002\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.88, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"fresh\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.75, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_elegant\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_elegant_003\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"elegant\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.7, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_healing\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_healing_004\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.86, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"healing\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.65, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_poetic\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_poetic_005\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.84, \"phonetic_features\": [Object], \"popularity_trend\": -0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"poetic\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.6, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_adorable\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_adorable_006\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"informal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"adorable\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_designer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_designer_007\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"designer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_engineer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_engineer_008\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"engineer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.9, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_artist\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_artist_009\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.91, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"artist\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_creative\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_creative_010\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_characteristics\", \"syntactic_properties\": [Object], \"text\": \"creative\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, …]\n\n      89 |       const result = await dataLoader.loadAll()\n      90 |\n    > 91 |       expect(result.morphemes).toHaveLength(1)\n         |                                ^\n      92 |       expect(result.morphemes[0].id).toBe('emotions_001')\n      93 |       expect(result.stats.total_count).toBe(1)\n      94 |       expect(result.validation.passed).toBe(true)\n\n      at Object.<anonymous> (tests/data-loader.test.ts:91:32)\n\n  ● DataLoader › File Loading › should handle file reading errors\n\n    expect(received).rejects.toThrow()\n\n    Received promise resolved instead of rejected\n    Resolved to value: {\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 48, \"total_count\": 152}, \"timestamp\": 1750836751443, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 1, \"warnings\": [Array]}}\n\n       98 |       mockFs.readdir.mockRejectedValue(new Error('Directory not found'))\n       99 |\n    > 100 |       await expect(dataLoader.loadAll()).rejects.toThrow('Directory not found')\n          |             ^\n      101 |     })\n      102 |\n      103 |     it('should handle invalid JSON files', async () => {\n\n      at expect (../node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n      at Object.<anonymous> (tests/data-loader.test.ts:100:13)\n\n  ● DataLoader › File Loading › should handle invalid JSON files\n\n    expect(received).rejects.toThrow()\n\n    Received promise resolved instead of rejected\n    Resolved to value: {\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 65, \"total_count\": 152}, \"timestamp\": 1750836751536, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 1, \"warnings\": [Array]}}\n\n      106 |       mockFs.readFile.mockResolvedValue('invalid json content')\n      107 |\n    > 108 |       await expect(dataLoader.loadAll()).rejects.toThrow()\n          |             ^\n      109 |     })\n      110 |\n      111 |     it('should filter supported file formats', async () => {\n\n      at expect (../node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n      at Object.<anonymous> (tests/data-loader.test.ts:108:13)\n\n  ● DataLoader › File Loading › should filter supported file formats\n\n    expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\n    Expected number of calls: 2\n    Received number of calls: 0\n\n      123 |\n      124 |       // Should only read .json and .jsonl files\n    > 125 |       expect(mockFs.readFile).toHaveBeenCalledTimes(2)\n          |                               ^\n      126 |     })\n      127 |   })\n      128 |\n\n      at Object.<anonymous> (tests/data-loader.test.ts:125:31)\n\n  ● DataLoader › Data Validation › should validate morpheme data structure\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: true\n    Received: false\n\n      152 |       const result = await dataLoader.loadAll()\n      153 |\n    > 154 |       expect(result.validation.passed).toBe(true)\n          |                                        ^\n      155 |       expect(result.validation.errors).toHaveLength(0)\n      156 |     })\n      157 |\n\n      at Object.<anonymous> (tests/data-loader.test.ts:154:40)\n\n  ● DataLoader › Caching › should cache loaded data\n\n    expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\n    Expected number of calls: 1\n    Received number of calls: 0\n\n      232 |\n      233 |       // File should only be read once due to caching\n    > 234 |       expect(mockFs.readFile).toHaveBeenCalledTimes(1)\n          |                               ^\n      235 |     })\n      236 |\n      237 |     it('should respect cache TTL', async () => {\n\n      at Object.<anonymous> (tests/data-loader.test.ts:234:31)\n\n  ● DataLoader › Caching › should respect cache TTL\n\n    数据加载失败: Cannot read properties of undefined (reading 'close')\n\n      209 |     } catch (error) {\n      210 |       console.error('❌ 数据加载失败:', error)\n    > 211 |       throw new Error(`数据加载失败: ${error instanceof Error ? error.message : String(error)}`)\n          |             ^\n      212 |     }\n      213 |   }\n      214 |\n\n      at DataLoader._performLoad (core/data/DataLoader.ts:211:13)\n      at DataLoader.loadAll (core/data/DataLoader.ts:157:22)\n      at Object.<anonymous> (tests/data-loader.test.ts:255:7)\n\n  ● DataLoader › Statistics Generation › should generate correct statistics\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: 2\n    Received: 152\n\n      302 |       const result = await dataLoader.loadAll()\n      303 |\n    > 304 |       expect(result.stats.total_count).toBe(2)\n          |                                        ^\n      305 |       expect(result.stats.by_category[MorphemeCategory.EMOTIONS]).toBe(1)\n      306 |       expect(result.stats.by_category[MorphemeCategory.PROFESSIONS]).toBe(1)\n      307 |       expect(result.stats.avg_quality).toBe(0.85)\n\n      at Object.<anonymous> (tests/data-loader.test.ts:304:40)\n\n  ● DataLoader › Error Recovery › should retry on transient failures\n\n    expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\n    Expected number of calls: 3\n    Received number of calls: 0\n\n      322 |\n      323 |       await expect(retryLoader.loadAll()).resolves.toBeDefined()\n    > 324 |       expect(mockFs.readdir).toHaveBeenCalledTimes(3)\n          |                              ^\n      325 |     })\n      326 |\n      327 |     it('should fail after max retries', async () => {\n\n      at Object.<anonymous> (tests/data-loader.test.ts:324:30)\n\n  ● DataLoader › Error Recovery › should fail after max retries\n\n    expect(received).rejects.toThrow()\n\n    Received promise resolved instead of rejected\n    Resolved to value: {\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 30, \"total_count\": 152}, \"timestamp\": 1750836752071, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 0, \"warnings\": [Array]}}\n\n      330 |       mockFs.readdir.mockRejectedValue(new Error('Persistent failure'))\n      331 |\n    > 332 |       await expect(retryLoader.loadAll()).rejects.toThrow('Persistent failure')\n          |             ^\n      333 |       expect(mockFs.readdir).toHaveBeenCalledTimes(3) // Initial + 2 retries\n      334 |     })\n      335 |   })\n\n      at expect (../node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n      at Object.<anonymous> (tests/data-loader.test.ts:332:13)\n","testResults":[{"ancestorTitles":["DataLoader","Constructor"],"duration":8,"failureMessages":[],"fullName":"DataLoader Constructor should create DataLoader with default configuration","status":"passed","title":"should create DataLoader with default configuration"},{"ancestorTitles":["DataLoader","Constructor"],"duration":2,"failureMessages":[],"fullName":"DataLoader Constructor should create DataLoader with custom configuration","status":"passed","title":"should create DataLoader with custom configuration"},{"ancestorTitles":["DataLoader","File Loading"],"duration":285,"failureMessages":["Error: expect(received).toHaveLength(expected)\n\nExpected length: 1\nReceived length: 152\nReceived array:  [{\"alternative_forms\": [Array], \"concept_id\": \"concept_warm\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_warm_001\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"warm\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_fresh\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_fresh_002\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.88, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"fresh\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.75, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_elegant\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_elegant_003\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"elegant\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.7, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_healing\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_healing_004\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.86, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"healing\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.65, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_poetic\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_poetic_005\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.84, \"phonetic_features\": [Object], \"popularity_trend\": -0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"poetic\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.6, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_adorable\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_adorable_006\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"informal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"adorable\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_designer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_designer_007\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"designer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_engineer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_engineer_008\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"engineer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.9, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_artist\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_artist_009\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.91, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"artist\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_creative\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_creative_010\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_characteristics\", \"syntactic_properties\": [Object], \"text\": \"creative\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, …]\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:91:32)"],"fullName":"DataLoader File Loading should load JSON files successfully","status":"failed","title":"should load JSON files successfully"},{"ancestorTitles":["DataLoader","File Loading"],"duration":70,"failureMessages":["Error: expect(received).rejects.toThrow()\n\nReceived promise resolved instead of rejected\nResolved to value: {\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 48, \"total_count\": 152}, \"timestamp\": 1750836751443, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 1, \"warnings\": [Array]}}\n    at expect (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:100:13)\n    at Promise.finally.completed (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at _runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:494:12)"],"fullName":"DataLoader File Loading should handle file reading errors","status":"failed","title":"should handle file reading errors"},{"ancestorTitles":["DataLoader","File Loading"],"duration":90,"failureMessages":["Error: expect(received).rejects.toThrow()\n\nReceived promise resolved instead of rejected\nResolved to value: {\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 65, \"total_count\": 152}, \"timestamp\": 1750836751536, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 1, \"warnings\": [Array]}}\n    at expect (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:108:13)\n    at Promise.finally.completed (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at _runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:494:12)"],"fullName":"DataLoader File Loading should handle invalid JSON files","status":"failed","title":"should handle invalid JSON files"},{"ancestorTitles":["DataLoader","File Loading"],"duration":107,"failureMessages":["Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 2\nReceived number of calls: 0\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:125:31)"],"fullName":"DataLoader File Loading should filter supported file formats","status":"failed","title":"should filter supported file formats"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":46,"failureMessages":["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: true\nReceived: false\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:154:40)"],"fullName":"DataLoader Data Validation should validate morpheme data structure","status":"failed","title":"should validate morpheme data structure"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":40,"failureMessages":[],"fullName":"DataLoader Data Validation should detect invalid morpheme data","status":"passed","title":"should detect invalid morpheme data"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":44,"failureMessages":[],"fullName":"DataLoader Data Validation should handle validation with warnings","status":"passed","title":"should handle validation with warnings"},{"ancestorTitles":["DataLoader","Caching"],"duration":48,"failureMessages":["Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:234:31)"],"fullName":"DataLoader Caching should cache loaded data","status":"failed","title":"should cache loaded data"},{"ancestorTitles":["DataLoader","Caching"],"duration":110,"failureMessages":["Error: 数据加载失败: Cannot read properties of undefined (reading 'close')\n    at DataLoader._performLoad (/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts:211:13)\n    at DataLoader.loadAll (/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts:157:22)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:255:7)"],"fullName":"DataLoader Caching should respect cache TTL","status":"failed","title":"should respect cache TTL"},{"ancestorTitles":["DataLoader","Statistics Generation"],"duration":30,"failureMessages":["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 2\nReceived: 152\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:304:40)"],"fullName":"DataLoader Statistics Generation should generate correct statistics","status":"failed","title":"should generate correct statistics"},{"ancestorTitles":["DataLoader","Error Recovery"],"duration":42,"failureMessages":["Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 3\nReceived number of calls: 0\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:324:30)"],"fullName":"DataLoader Error Recovery should retry on transient failures","status":"failed","title":"should retry on transient failures"},{"ancestorTitles":["DataLoader","Error Recovery"],"duration":40,"failureMessages":["Error: expect(received).rejects.toThrow()\n\nReceived promise resolved instead of rejected\nResolved to value: {\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 30, \"total_count\": 152}, \"timestamp\": 1750836752071, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 0, \"warnings\": [Array]}}\n    at expect (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:332:13)\n    at Promise.finally.completed (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at _runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:494:12)"],"fullName":"DataLoader Error Recovery should fail after max retries","status":"failed","title":"should fail after max retries"}]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/core-generation-engine.test.ts","failureMessage":"  ● Test suite failed to run\n\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m73\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m73\u001b[0m       ;(engine as any).morphemeRepo = mockMorphemeRepo\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m74\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m74\u001b[0m       ;(engine as any).languageManager = mockLanguageManager\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m75\u001b[0m       ;(engine as any).semanticAligner = mockSemanticAligner\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m77\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m77\u001b[0m       await expect(engine.initialize()).resolves.not.toThrow()\n    \u001b[7m  \u001b[0m \u001b[91m                   ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m87\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m87\u001b[0m       ;(engine as any).morphemeRepo = mockMorphemeRepo\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m89\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m89\u001b[0m       await expect(engine.initialize()).rejects.toThrow()\n    \u001b[7m  \u001b[0m \u001b[91m                   ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m114\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m114\u001b[0m       ;(engine as any).morphemeRepo = mockMorphemeRepo\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m115\u001b[0m       ;(engine as any).languageManager = mockLanguageManager\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m116\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m116\u001b[0m       ;(engine as any).semanticAligner = mockSemanticAligner\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m119\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m119\u001b[0m       await engine.initialize()\n    \u001b[7m   \u001b[0m \u001b[91m            ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m122\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m122\u001b[0m       await engine.initialize()\n    \u001b[7m   \u001b[0m \u001b[91m            ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m170\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m170\u001b[0m       ;(engine as any).morphemeRepo = mockMorphemeRepo\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m171\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m171\u001b[0m       ;(engine as any).languageManager = mockLanguageManager\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m172\u001b[0m       ;(engine as any).semanticAligner = mockSemanticAligner\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m174\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m174\u001b[0m       await engine.initialize()\n    \u001b[7m   \u001b[0m \u001b[91m            ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m187\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m187\u001b[0m       ;(engine as any).generateSingle = jest.fn().mockResolvedValue({\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m203\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m203\u001b[0m       const result = await (engine as any).generateSingle(context)\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m217\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m217\u001b[0m       ;(engine as any).generateSingle = jest.fn().mockRejectedValue(new Error('Generation failed'))\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m219\u001b[0m:\u001b[93m21\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m219\u001b[0m       await expect((engine as any).generateSingle(context)).rejects.toThrow('Generation failed')\n    \u001b[7m   \u001b[0m \u001b[91m                    ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m226\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m226\u001b[0m       ;(engine as any).getStats = jest.fn().mockReturnValue({\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m234\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m234\u001b[0m       const stats = (engine as any).getStats()\n    \u001b[7m   \u001b[0m \u001b[91m                     ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m243\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m243\u001b[0m       ;(engine as any).generationCount = 50\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m244\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m244\u001b[0m       ;(engine as any).totalGenerationTime = 6000\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m246\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m246\u001b[0m       const avgTime = (engine as any).totalGenerationTime / (engine as any).generationCount\n    \u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m246\u001b[0m:\u001b[93m62\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m246\u001b[0m       const avgTime = (engine as any).totalGenerationTime / (engine as any).generationCount\n    \u001b[7m   \u001b[0m \u001b[91m                                                             ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m260\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m260\u001b[0m       ;(engine as any).validateContext = jest.fn().mockReturnValue({\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m265\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m265\u001b[0m       const validation = (engine as any).validateContext(invalidContext)\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m276\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m276\u001b[0m       ;(engine as any).morphemeRepo = mockMorphemeRepo\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m285\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m285\u001b[0m       ;(engine as any).cleanup = jest.fn().mockResolvedValue(undefined)\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m287\u001b[0m:\u001b[93m21\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m287\u001b[0m       await expect((engine as any).cleanup()).resolves.not.toThrow()\n    \u001b[7m   \u001b[0m \u001b[91m                    ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m292\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m292\u001b[0m       ;(engine as any).cleanup = jest.fn().mockRejectedValue(new Error('Cleanup failed'))\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine.test.ts\u001b[0m:\u001b[93m294\u001b[0m:\u001b[93m21\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'engine'.\n\n    \u001b[7m294\u001b[0m       await expect((engine as any).cleanup()).rejects.toThrow('Cleanup failed')\n    \u001b[7m   \u001b[0m \u001b[91m                    ~~~~~~\u001b[0m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":true,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":1335571525,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750836752971,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})