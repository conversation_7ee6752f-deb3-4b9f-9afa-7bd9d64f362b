window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":10,"numPassedTestSuites":7,"numPassedTests":110,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":8,"numTotalTests":120,"startTime":1750837504764,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509197,"loadTestEnvironmentEnd":1750837505698,"loadTestEnvironmentStart":1750837505254,"runtime":3497,"setupAfterEnvEnd":1750837506198,"setupAfterEnvStart":1750837506198,"setupFilesEnd":1750837505700,"setupFilesStart":1750837505700,"slow":false,"start":1750837505700},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-quality-assessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":17,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":9,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should assess Spanish morpheme quality","status":"passed","title":"should assess Spanish morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should include European-specific metrics for Spanish","status":"passed","title":"should include European-specific metrics for Spanish"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":8,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should give high scores for traditional Spanish words","status":"passed","title":"should give high scores for traditional Spanish words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French morpheme quality","status":"passed","title":"should assess French morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should give high elegance scores for French words","status":"passed","title":"should give high elegance scores for French words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French phonetic harmony","status":"passed","title":"should assess French phonetic harmony"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morpheme quality","status":"passed","title":"should assess German morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should give high precision scores for German words","status":"passed","title":"should give high precision scores for German words"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morphological complexity","status":"passed","title":"should assess German morphological complexity"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should handle German compound words","status":"passed","title":"should handle German compound words"},{"ancestorTitles":["EuropeanQualityAssessor","Cross-Language Comparison"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Cross-Language Comparison should provide consistent quality assessment across languages","status":"passed","title":"should provide consistent quality assessment across languages"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":140,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should handle missing cultural context gracefully","status":"passed","title":"should handle missing cultural context gracefully"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509440,"loadTestEnvironmentEnd":1750837505745,"loadTestEnvironmentStart":1750837505295,"runtime":3694,"setupAfterEnvEnd":1750837506278,"setupAfterEnvStart":1750837506278,"setupFilesEnd":1750837505746,"setupFilesStart":1750837505746,"slow":false,"start":1750837505746},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":8,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 CulturalContext 枚举","status":"passed","title":"应该正确定义 CulturalContext 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":15,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 RegisterLevel 枚举","status":"passed","title":"应该正确定义 RegisterLevel 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":3,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持枚举值的类型检查","status":"passed","title":"应该支持枚举值的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持语言代码的类型检查","status":"passed","title":"应该支持语言代码的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":3,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持寄存器级别的类型检查","status":"passed","title":"应该支持寄存器级别的类型检查"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该正确配置语义向量维度","status":"passed","title":"应该正确配置语义向量维度"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":6,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该支持向量维度的数学运算","status":"passed","title":"应该支持向量维度的数学运算"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持多语言标签映射","status":"passed","title":"应该支持多语言标签映射"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持文化适应性配置","status":"passed","title":"应该支持文化适应性配置"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持语音特征配置","status":"passed","title":"应该支持语音特征配置"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该支持8维度质量评分","status":"passed","title":"应该支持8维度质量评分"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该计算平均质量评分","status":"passed","title":"应该计算平均质量评分"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理无效的枚举值","status":"passed","title":"应该处理无效的枚举值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理边界值","status":"passed","title":"应该处理边界值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":3,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理空数组和空对象","status":"passed","title":"应该处理空数组和空对象"}]},{"numFailingTests":0,"numPassingTests":15,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509464,"loadTestEnvironmentEnd":1750837505717,"loadTestEnvironmentStart":1750837505254,"runtime":3745,"setupAfterEnvEnd":1750837506264,"setupAfterEnvStart":1750837506264,"setupFilesEnd":1750837505719,"setupFilesStart":1750837505719,"slow":false,"start":1750837505719},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/data-validator.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["DataValidator","Constructor"],"duration":10,"failureMessages":[],"fullName":"DataValidator Constructor should create DataValidator with default configuration","status":"passed","title":"should create DataValidator with default configuration"},{"ancestorTitles":["DataValidator","Constructor"],"duration":1,"failureMessages":[],"fullName":"DataValidator Constructor should create DataValidator with custom configuration","status":"passed","title":"should create DataValidator with custom configuration"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":132,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should validate a valid morpheme","status":"passed","title":"should validate a valid morpheme"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":22,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid ID format","status":"passed","title":"should detect invalid ID format"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":15,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid text length","status":"passed","title":"should detect invalid text length"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":48,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid category","status":"passed","title":"should detect invalid category"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":17,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid quality score","status":"passed","title":"should detect invalid quality score"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":11,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid semantic vector dimension","status":"passed","title":"should detect invalid semantic vector dimension"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":18,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect missing required fields","status":"passed","title":"should detect missing required fields"},{"ancestorTitles":["DataValidator","Batch Validation"],"duration":11,"failureMessages":[],"fullName":"DataValidator Batch Validation should validate multiple morphemes","status":"passed","title":"should validate multiple morphemes"},{"ancestorTitles":["DataValidator","Batch Validation"],"duration":13,"failureMessages":[],"fullName":"DataValidator Batch Validation should handle mixed valid and invalid morphemes","status":"passed","title":"should handle mixed valid and invalid morphemes"},{"ancestorTitles":["DataValidator","Warning Detection"],"duration":9,"failureMessages":[],"fullName":"DataValidator Warning Detection should detect inconsistent ID and category as warnings","status":"passed","title":"should detect inconsistent ID and category as warnings"},{"ancestorTitles":["DataValidator","Warning Detection"],"duration":6,"failureMessages":[],"fullName":"DataValidator Warning Detection should detect inconsistent character count as warnings","status":"passed","title":"should detect inconsistent character count as warnings"},{"ancestorTitles":["DataValidator","Custom Rules"],"duration":18,"failureMessages":[],"fullName":"DataValidator Custom Rules should support adding custom validation rules","status":"passed","title":"should support adding custom validation rules"},{"ancestorTitles":["DataValidator","Performance"],"duration":8,"failureMessages":[],"fullName":"DataValidator Performance should validate large batches efficiently","status":"passed","title":"should validate large batches efficiently"}]},{"numFailingTests":0,"numPassingTests":12,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509502,"loadTestEnvironmentEnd":1750837509318,"loadTestEnvironmentStart":1750837509289,"runtime":184,"setupAfterEnvEnd":1750837509478,"setupAfterEnvStart":1750837509478,"setupFilesEnd":1750837509318,"setupFilesStart":1750837509318,"slow":false,"start":1750837509318},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/core-generation-engine.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["CoreGenerationEngine","Constructor"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Constructor should create engine instance with default configuration","status":"passed","title":"should create engine instance with default configuration"},{"ancestorTitles":["CoreGenerationEngine","Constructor"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Constructor should initialize with proper dependencies","status":"passed","title":"should initialize with proper dependencies"},{"ancestorTitles":["CoreGenerationEngine","Initialization"],"duration":2,"failureMessages":[],"fullName":"CoreGenerationEngine Initialization should initialize successfully","status":"passed","title":"should initialize successfully"},{"ancestorTitles":["CoreGenerationEngine","Initialization"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Initialization should handle initialization failure gracefully","status":"passed","title":"should handle initialization failure gracefully"},{"ancestorTitles":["CoreGenerationEngine","Initialization"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Initialization should skip re-initialization if already initialized","status":"passed","title":"should skip re-initialization if already initialized"},{"ancestorTitles":["CoreGenerationEngine","Generation Methods"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine Generation Methods should generate username successfully","status":"passed","title":"should generate username successfully"},{"ancestorTitles":["CoreGenerationEngine","Generation Methods"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine Generation Methods should handle generation errors gracefully","status":"passed","title":"should handle generation errors gracefully"},{"ancestorTitles":["CoreGenerationEngine","Statistics and Performance"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Statistics and Performance should provide generation statistics","status":"passed","title":"should provide generation statistics"},{"ancestorTitles":["CoreGenerationEngine","Statistics and Performance"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Statistics and Performance should calculate average generation time","status":"passed","title":"should calculate average generation time"},{"ancestorTitles":["CoreGenerationEngine","Validation"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Validation should validate generation context","status":"passed","title":"should validate generation context"},{"ancestorTitles":["CoreGenerationEngine","Cleanup"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine Cleanup should cleanup resources successfully","status":"passed","title":"should cleanup resources successfully"},{"ancestorTitles":["CoreGenerationEngine","Cleanup"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Cleanup should handle cleanup errors gracefully","status":"passed","title":"should handle cleanup errors gracefully"}]},{"numFailingTests":0,"numPassingTests":19,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509851,"loadTestEnvironmentEnd":1750837509488,"loadTestEnvironmentStart":1750837509471,"runtime":363,"setupAfterEnvEnd":1750837509628,"setupAfterEnvStart":1750837509628,"setupFilesEnd":1750837509488,"setupFilesStart":1750837509488,"slow":false,"start":1750837509488},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-language-processor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":7,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should process Spanish morpheme correctly","status":"passed","title":"should process Spanish morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should extract Spanish phonetic features","status":"passed","title":"should extract Spanish phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should detect Spanish gender correctly","status":"passed","title":"should detect Spanish gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":3,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should adjust Spanish cultural context","status":"passed","title":"should adjust Spanish cultural context"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":3,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should process French morpheme correctly","status":"passed","title":"should process French morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should extract French phonetic features","status":"passed","title":"should extract French phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should detect French gender correctly","status":"passed","title":"should detect French gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should calculate French elegance","status":"passed","title":"should calculate French elegance"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":8,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should process German morpheme correctly","status":"passed","title":"should process German morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should extract German phonetic features","status":"passed","title":"should extract German phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should detect German gender correctly","status":"passed","title":"should detect German gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should analyze German compound structure","status":"passed","title":"should analyze German compound structure"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should calculate German precision","status":"passed","title":"should calculate German precision"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":130,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should handle missing morphological info gracefully","status":"passed","title":"should handle missing morphological info gracefully"}]},{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509919,"loadTestEnvironmentEnd":1750837509748,"loadTestEnvironmentStart":1750837509725,"runtime":171,"setupAfterEnvEnd":1750837509853,"setupAfterEnvStart":1750837509853,"setupFilesEnd":1750837509748,"setupFilesStart":1750837509748,"slow":false,"start":1750837509748},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 Morpheme 类型","status":"passed","title":"应该正确定义 Morpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GenerationContext 类型","status":"passed","title":"应该正确定义 GenerationContext 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GeneratedUsername 类型","status":"passed","title":"应该正确定义 GeneratedUsername 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 UniversalConcept 类型","status":"passed","title":"应该正确定义 UniversalConcept 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageSpecificMorpheme 类型","status":"passed","title":"应该正确定义 LanguageSpecificMorpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持传统 CulturalContext 枚举","status":"passed","title":"应该支持传统 CulturalContext 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持多维度 CulturalContext 接口","status":"passed","title":"应该支持多维度 CulturalContext 接口"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持语义向量的向后兼容","status":"passed","title":"应该支持语义向量的向后兼容"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该正确推断函数返回类型","status":"passed","title":"应该正确推断函数返回类型"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该支持泛型类型约束","status":"passed","title":"应该支持泛型类型约束"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该防止类型错误的赋值","status":"passed","title":"应该防止类型错误的赋值"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该确保必需字段的存在","status":"passed","title":"应该确保必需字段的存在"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":7,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该正确计算8维度质量评分","status":"passed","title":"应该正确计算8维度质量评分"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该验证多语种质量评分的一致性","status":"passed","title":"应该验证多语种质量评分的一致性"}]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837509920,"loadTestEnvironmentEnd":1750837509724,"loadTestEnvironmentStart":1750837509711,"runtime":196,"setupAfterEnvEnd":1750837509850,"setupAfterEnvStart":1750837509850,"setupFilesEnd":1750837509724,"setupFilesStart":1750837509724,"slow":false,"start":1750837509724},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/unit/EastAsianLanguageProcessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":5,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该正确识别东亚语言","status":"passed","title":"应该正确识别东亚语言"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":5,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的日语配置","status":"passed","title":"应该返回正确的日语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的韩语配置","status":"passed","title":"应该返回正确的韩语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的假名信息","status":"passed","title":"应该正确处理日语语素的假名信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":5,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的汉字信息","status":"passed","title":"应该正确处理日语语素的汉字信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语敬语系统","status":"passed","title":"应该正确处理日语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的韩文信息","status":"passed","title":"应该正确处理韩语语素的韩文信息"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的汉字词汇","status":"passed","title":"应该正确处理韩语语素的汉字词汇"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语敬语系统","status":"passed","title":"应该正确处理韩语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","语言特性分析"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言特性分析 应该正确计算音拍数","status":"passed","title":"应该正确计算音拍数"}]},{"numFailingTests":10,"numPassingTests":4,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750837510392,"loadTestEnvironmentEnd":1750837505715,"loadTestEnvironmentStart":1750837505251,"runtime":4675,"setupAfterEnvEnd":1750837506264,"setupAfterEnvStart":1750837506264,"setupFilesEnd":1750837505717,"setupFilesStart":1750837505717,"slow":false,"start":1750837505717},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › File Loading › should load JSON files successfully\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveLength\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected length: \u001b[32m1\u001b[39m\n    Received length: \u001b[31m152\u001b[39m\n    Received array:  \u001b[31m[{\"alternative_forms\": [Array], \"concept_id\": \"concept_warm\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_warm_001\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"warm\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_fresh\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_fresh_002\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.88, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"fresh\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.75, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_elegant\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_elegant_003\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"elegant\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.7, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_healing\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_healing_004\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.86, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"healing\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.65, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_poetic\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_poetic_005\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.84, \"phonetic_features\": [Object], \"popularity_trend\": -0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"poetic\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.6, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_adorable\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_adorable_006\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"informal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"adorable\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_designer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_designer_007\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"designer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_engineer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_engineer_008\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"engineer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.9, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_artist\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_artist_009\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.91, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"artist\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_creative\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_creative_010\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_characteristics\", \"syntactic_properties\": [Object], \"text\": \"creative\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, …]\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 89 |\u001b[39m       \u001b[36mconst\u001b[39m result \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m dataLoader\u001b[33m.\u001b[39mloadAll()\u001b[22m\n\u001b[2m     \u001b[90m 90 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 91 |\u001b[39m       expect(result\u001b[33m.\u001b[39mmorphemes)\u001b[33m.\u001b[39mtoHaveLength(\u001b[35m1\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 92 |\u001b[39m       expect(result\u001b[33m.\u001b[39mmorphemes[\u001b[35m0\u001b[39m]\u001b[33m.\u001b[39mid)\u001b[33m.\u001b[39mtoBe(\u001b[32m'emotions_001'\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 93 |\u001b[39m       expect(result\u001b[33m.\u001b[39mstats\u001b[33m.\u001b[39mtotal_count)\u001b[33m.\u001b[39mtoBe(\u001b[35m1\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 94 |\u001b[39m       expect(result\u001b[33m.\u001b[39mvalidation\u001b[33m.\u001b[39mpassed)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:91:32)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › File Loading › should handle file reading errors\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\n    Received promise resolved instead of rejected\n    Resolved to value: \u001b[31m{\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 81, \"total_count\": 152}, \"timestamp\": 1750837509584, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 1, \"warnings\": [Array]}}\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m  98 |\u001b[39m       mockFs\u001b[33m.\u001b[39mreaddir\u001b[33m.\u001b[39mmockRejectedValue(\u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Directory not found'\u001b[39m))\u001b[22m\n\u001b[2m     \u001b[90m  99 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 100 |\u001b[39m       \u001b[36mawait\u001b[39m expect(dataLoader\u001b[33m.\u001b[39mloadAll())\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[32m'Directory not found'\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 101 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 102 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 103 |\u001b[39m     it(\u001b[32m'should handle invalid JSON files'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat expect (\u001b[22m\u001b[2m../node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js\u001b[2m:2113:15)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:100:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › File Loading › should handle invalid JSON files\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\n    Received promise resolved instead of rejected\n    Resolved to value: \u001b[31m{\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 45, \"total_count\": 152}, \"timestamp\": 1750837509654, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 3, \"warnings\": [Array]}}\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 106 |\u001b[39m       mockFs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockResolvedValue(\u001b[32m'invalid json content'\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 107 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 108 |\u001b[39m       \u001b[36mawait\u001b[39m expect(dataLoader\u001b[33m.\u001b[39mloadAll())\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow()\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 109 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 110 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 111 |\u001b[39m     it(\u001b[32m'should filter supported file formats'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat expect (\u001b[22m\u001b[2m../node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js\u001b[2m:2113:15)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:108:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › File Loading › should filter supported file formats\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected number of calls: \u001b[32m2\u001b[39m\n    Received number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 123 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 124 |\u001b[39m       \u001b[90m// Should only read .json and .jsonl files\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 125 |\u001b[39m       expect(mockFs\u001b[33m.\u001b[39mreadFile)\u001b[33m.\u001b[39mtoHaveBeenCalledTimes(\u001b[35m2\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 126 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 127 |\u001b[39m   })\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:125:31)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › Data Validation › should validate morpheme data structure\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mtrue\u001b[39m\n    Received: \u001b[31mfalse\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 152 |\u001b[39m       \u001b[36mconst\u001b[39m result \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m dataLoader\u001b[33m.\u001b[39mloadAll()\u001b[22m\n\u001b[2m     \u001b[90m 153 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 154 |\u001b[39m       expect(result\u001b[33m.\u001b[39mvalidation\u001b[33m.\u001b[39mpassed)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 155 |\u001b[39m       expect(result\u001b[33m.\u001b[39mvalidation\u001b[33m.\u001b[39merrors)\u001b[33m.\u001b[39mtoHaveLength(\u001b[35m0\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 156 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 157 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:154:40)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › Caching › should cache loaded data\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected number of calls: \u001b[32m1\u001b[39m\n    Received number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 232 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 233 |\u001b[39m       \u001b[90m// File should only be read once due to caching\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 234 |\u001b[39m       expect(mockFs\u001b[33m.\u001b[39mreadFile)\u001b[33m.\u001b[39mtoHaveBeenCalledTimes(\u001b[35m1\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 235 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 236 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 237 |\u001b[39m     it(\u001b[32m'should respect cache TTL'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:234:31)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › Caching › should respect cache TTL\u001b[39m\u001b[22m\n\n    数据加载失败: Cannot read properties of undefined (reading 'close')\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 209 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\u001b[22m\n\u001b[2m     \u001b[90m 210 |\u001b[39m       console\u001b[33m.\u001b[39merror(\u001b[32m'❌ 数据加载失败:'\u001b[39m\u001b[33m,\u001b[39m error)\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 211 |\u001b[39m       \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`数据加载失败: ${error instanceof Error ? error.message : String(error)}`\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 212 |\u001b[39m     }\u001b[22m\n\u001b[2m     \u001b[90m 213 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 214 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat DataLoader._performLoad (\u001b[22m\u001b[2mcore/data/DataLoader.ts\u001b[2m:211:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat DataLoader.loadAll (\u001b[22m\u001b[2mcore/data/DataLoader.ts\u001b[2m:157:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:255:7)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › Statistics Generation › should generate correct statistics\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m2\u001b[39m\n    Received: \u001b[31m152\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 302 |\u001b[39m       \u001b[36mconst\u001b[39m result \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m dataLoader\u001b[33m.\u001b[39mloadAll()\u001b[22m\n\u001b[2m     \u001b[90m 303 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 304 |\u001b[39m       expect(result\u001b[33m.\u001b[39mstats\u001b[33m.\u001b[39mtotal_count)\u001b[33m.\u001b[39mtoBe(\u001b[35m2\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 305 |\u001b[39m       expect(result\u001b[33m.\u001b[39mstats\u001b[33m.\u001b[39mby_category[\u001b[33mMorphemeCategory\u001b[39m\u001b[33m.\u001b[39m\u001b[33mEMOTIONS\u001b[39m])\u001b[33m.\u001b[39mtoBe(\u001b[35m1\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 306 |\u001b[39m       expect(result\u001b[33m.\u001b[39mstats\u001b[33m.\u001b[39mby_category[\u001b[33mMorphemeCategory\u001b[39m\u001b[33m.\u001b[39m\u001b[33mPROFESSIONS\u001b[39m])\u001b[33m.\u001b[39mtoBe(\u001b[35m1\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 307 |\u001b[39m       expect(result\u001b[33m.\u001b[39mstats\u001b[33m.\u001b[39mavg_quality)\u001b[33m.\u001b[39mtoBe(\u001b[35m0.85\u001b[39m)\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:304:40)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › Error Recovery › should retry on transient failures\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected number of calls: \u001b[32m3\u001b[39m\n    Received number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 322 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 323 |\u001b[39m       \u001b[36mawait\u001b[39m expect(retryLoader\u001b[33m.\u001b[39mloadAll())\u001b[33m.\u001b[39mresolves\u001b[33m.\u001b[39mtoBeDefined()\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 324 |\u001b[39m       expect(mockFs\u001b[33m.\u001b[39mreaddir)\u001b[33m.\u001b[39mtoHaveBeenCalledTimes(\u001b[35m3\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 325 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 326 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 327 |\u001b[39m     it(\u001b[32m'should fail after max retries'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:324:30)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mDataLoader › Error Recovery › should fail after max retries\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\n    Received promise resolved instead of rejected\n    Resolved to value: \u001b[31m{\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 37, \"total_count\": 152}, \"timestamp\": 1750837510286, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 0, \"warnings\": [Array]}}\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 330 |\u001b[39m       mockFs\u001b[33m.\u001b[39mreaddir\u001b[33m.\u001b[39mmockRejectedValue(\u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Persistent failure'\u001b[39m))\u001b[22m\n\u001b[2m     \u001b[90m 331 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 332 |\u001b[39m       \u001b[36mawait\u001b[39m expect(retryLoader\u001b[33m.\u001b[39mloadAll())\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[32m'Persistent failure'\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 333 |\u001b[39m       expect(mockFs\u001b[33m.\u001b[39mreaddir)\u001b[33m.\u001b[39mtoHaveBeenCalledTimes(\u001b[35m3\u001b[39m) \u001b[90m// Initial + 2 retries\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 334 |\u001b[39m     })\u001b[22m\n\u001b[2m     \u001b[90m 335 |\u001b[39m   })\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat expect (\u001b[22m\u001b[2m../node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js\u001b[2m:2113:15)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/data-loader.test.ts\u001b[39m\u001b[0m\u001b[2m:332:13)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["DataLoader","Constructor"],"duration":13,"failureMessages":[],"fullName":"DataLoader Constructor should create DataLoader with default configuration","status":"passed","title":"should create DataLoader with default configuration"},{"ancestorTitles":["DataLoader","Constructor"],"duration":1,"failureMessages":[],"fullName":"DataLoader Constructor should create DataLoader with custom configuration","status":"passed","title":"should create DataLoader with custom configuration"},{"ancestorTitles":["DataLoader","File Loading"],"duration":363,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveLength\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected length: \u001b[32m1\u001b[39m\nReceived length: \u001b[31m152\u001b[39m\nReceived array:  \u001b[31m[{\"alternative_forms\": [Array], \"concept_id\": \"concept_warm\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_warm_001\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"warm\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_fresh\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_fresh_002\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.88, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"fresh\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.75, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_elegant\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_elegant_003\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"elegant\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.7, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_healing\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_healing_004\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.86, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"healing\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.65, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_poetic\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.88, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_poetic_005\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.84, \"phonetic_features\": [Object], \"popularity_trend\": -0.1, \"regional_variants\": [Array], \"register_level\": \"formal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"poetic\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.6, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_adorable\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.9, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_adorable_006\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.87, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"informal\", \"source\": \"english_morphemes_v3_emotions\", \"syntactic_properties\": [Object], \"text\": \"adorable\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_designer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_designer_007\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"designer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_engineer\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_engineer_008\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.92, \"phonetic_features\": [Object], \"popularity_trend\": 0.2, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"engineer\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.9, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_artist\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.92, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_artist_009\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.91, \"phonetic_features\": [Object], \"popularity_trend\": 0.1, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_professions\", \"syntactic_properties\": [Object], \"text\": \"artist\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.8, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, {\"alternative_forms\": [Array], \"concept_id\": \"concept_creative\", \"created_at\": \"2025-06-24T08:20:00.000Z\", \"cultural_appropriateness\": 0.95, \"cultural_context\": [Object], \"language\": \"en-US\", \"language_quality_scores\": [Object], \"morpheme_id\": \"en_creative_010\", \"morphological_info\": [Object], \"native_speaker_rating\": 0.9, \"phonetic_features\": [Object], \"popularity_trend\": 0.3, \"regional_variants\": [Array], \"register_level\": \"neutral\", \"source\": \"english_morphemes_v3_characteristics\", \"syntactic_properties\": [Object], \"text\": \"creative\", \"updated_at\": \"2025-06-24T08:20:00.000Z\", \"usage_frequency\": 0.85, \"validation_status\": \"validated\", \"version\": \"3.0.0\"}, …]\u001b[39m\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:91:32)"],"fullName":"DataLoader File Loading should load JSON files successfully","status":"failed","title":"should load JSON files successfully"},{"ancestorTitles":["DataLoader","File Loading"],"duration":100,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\nReceived promise resolved instead of rejected\nResolved to value: \u001b[31m{\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 81, \"total_count\": 152}, \"timestamp\": 1750837509584, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 1, \"warnings\": [Array]}}\u001b[39m\n    at expect (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:100:13)\n    at Promise.finally.completed (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at _runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:494:12)"],"fullName":"DataLoader File Loading should handle file reading errors","status":"failed","title":"should handle file reading errors"},{"ancestorTitles":["DataLoader","File Loading"],"duration":61,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\nReceived promise resolved instead of rejected\nResolved to value: \u001b[31m{\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 45, \"total_count\": 152}, \"timestamp\": 1750837509654, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 3, \"warnings\": [Array]}}\u001b[39m\n    at expect (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:108:13)\n    at Promise.finally.completed (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at _runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:494:12)"],"fullName":"DataLoader File Loading should handle invalid JSON files","status":"failed","title":"should handle invalid JSON files"},{"ancestorTitles":["DataLoader","File Loading"],"duration":51,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected number of calls: \u001b[32m2\u001b[39m\nReceived number of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:125:31)"],"fullName":"DataLoader File Loading should filter supported file formats","status":"failed","title":"should filter supported file formats"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":97,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:154:40)"],"fullName":"DataLoader Data Validation should validate morpheme data structure","status":"failed","title":"should validate morpheme data structure"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":68,"failureMessages":[],"fullName":"DataLoader Data Validation should detect invalid morpheme data","status":"passed","title":"should detect invalid morpheme data"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":51,"failureMessages":[],"fullName":"DataLoader Data Validation should handle validation with warnings","status":"passed","title":"should handle validation with warnings"},{"ancestorTitles":["DataLoader","Caching"],"duration":50,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected number of calls: \u001b[32m1\u001b[39m\nReceived number of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:234:31)"],"fullName":"DataLoader Caching should cache loaded data","status":"failed","title":"should cache loaded data"},{"ancestorTitles":["DataLoader","Caching"],"duration":144,"failureMessages":["Error: 数据加载失败: Cannot read properties of undefined (reading 'close')\n    at DataLoader._performLoad (/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts:211:13)\n    at DataLoader.loadAll (/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts:157:22)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:255:7)"],"fullName":"DataLoader Caching should respect cache TTL","status":"failed","title":"should respect cache TTL"},{"ancestorTitles":["DataLoader","Statistics Generation"],"duration":55,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m152\u001b[39m\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:304:40)"],"fullName":"DataLoader Statistics Generation should generate correct statistics","status":"failed","title":"should generate correct statistics"},{"ancestorTitles":["DataLoader","Error Recovery"],"duration":50,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected number of calls: \u001b[32m3\u001b[39m\nReceived number of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:324:30)"],"fullName":"DataLoader Error Recovery should retry on transient failures","status":"failed","title":"should retry on transient failures"},{"ancestorTitles":["DataLoader","Error Recovery"],"duration":50,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m()\u001b[22m\n\nReceived promise resolved instead of rejected\nResolved to value: \u001b[31m{\"morphemes\": [[Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], [Object], …], \"stats\": {\"avg_quality\": NaN, \"by_category\": [Object], \"by_context\": [Object], \"load_time\": 37, \"total_count\": 152}, \"timestamp\": 1750837510286, \"validation\": {\"errors\": [Array], \"passed\": false, \"validated_count\": 152, \"validation_time\": 0, \"warnings\": [Array]}}\u001b[39m\n    at expect (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/expect@30.0.2/node_modules/expect/build/index.js:2113:15)\n    at Object.<anonymous> (/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts:332:13)\n    at Promise.finally.completed (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at _runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-circus@30.0.2/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-runner@30.0.2/node_modules/jest-runner/build/testWorker.js:494:12)"],"fullName":"DataLoader Error Recovery should fail after max retries","status":"failed","title":"should fail after max retries"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":-1809022008,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750837510477,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})