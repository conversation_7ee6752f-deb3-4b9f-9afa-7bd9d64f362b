window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":3,"numPassedTests":69,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":3,"numTotalTests":69,"startTime":1750870662884,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":37,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750870666754,"loadTestEnvironmentEnd":1750870663474,"loadTestEnvironmentStart":1750870663197,"runtime":3279,"setupAfterEnvEnd":1750870663743,"setupAfterEnvStart":1750870663743,"setupFilesEnd":1750870663475,"setupFilesStart":1750870663475,"slow":false,"start":1750870663475},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/east-asian-language-processor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EastAsianLanguageProcessor","Constructor"],"duration":6,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Constructor 应该正确初始化东亚语言处理器","status":"passed","title":"应该正确初始化东亚语言处理器"},{"ancestorTitles":["EastAsianLanguageProcessor","Constructor"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Constructor 应该初始化所有东亚语言配置","status":"passed","title":"应该初始化所有东亚语言配置"},{"ancestorTitles":["EastAsianLanguageProcessor","Language Configuration"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Language Configuration 应该返回中文配置","status":"passed","title":"应该返回中文配置"},{"ancestorTitles":["EastAsianLanguageProcessor","Language Configuration"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Language Configuration 应该返回日语配置","status":"passed","title":"应该返回日语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","Language Configuration"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Language Configuration 应该返回韩语配置","status":"passed","title":"应该返回韩语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","Language Configuration"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Language Configuration 应该为不支持的语言返回undefined","status":"passed","title":"应该为不支持的语言返回undefined"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理平假名文本","status":"passed","title":"应该处理平假名文本"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理片假名文本","status":"passed","title":"应该处理片假名文本"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理汉字信息","status":"passed","title":"应该处理汉字信息"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理敬语标记","status":"passed","title":"应该处理敬语标记"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理长音标记","status":"passed","title":"应该处理长音标记"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理促音标记","status":"passed","title":"应该处理促音标记"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理不包含假名的文本","status":"passed","title":"应该处理不包含假名的文本"},{"ancestorTitles":["EastAsianLanguageProcessor","Japanese Morpheme Processing"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Japanese Morpheme Processing 应该处理多种敬语标记","status":"passed","title":"应该处理多种敬语标记"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理韩文文本","status":"passed","title":"应该处理韩文文本"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理汉字词汇","status":"passed","title":"应该处理汉字词汇"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理韩语敬语标记","status":"passed","title":"应该处理韩语敬语标记"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理复辅音","status":"passed","title":"应该处理复辅音"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理不包含汉字的韩文","status":"passed","title":"应该处理不包含汉字的韩文"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理多种敬语标记","status":"passed","title":"应该处理多种敬语标记"},{"ancestorTitles":["EastAsianLanguageProcessor","Korean Morpheme Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Korean Morpheme Processing 应该处理非韩文文本","status":"passed","title":"应该处理非韩文文本"},{"ancestorTitles":["EastAsianLanguageProcessor","Private Methods Testing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Private Methods Testing 应该正确计算重音模式","status":"passed","title":"应该正确计算重音模式"},{"ancestorTitles":["EastAsianLanguageProcessor","Private Methods Testing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Private Methods Testing 应该正确估算笔画数","status":"passed","title":"应该正确估算笔画数"},{"ancestorTitles":["EastAsianLanguageProcessor","Private Methods Testing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Private Methods Testing 应该正确分析韩文音节结构","status":"passed","title":"应该正确分析韩文音节结构"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理空文本","status":"passed","title":"应该处理空文本"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理空的alternative_forms","status":"passed","title":"应该处理空的alternative_forms"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理混合文字","status":"passed","title":"应该处理混合文字"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理无效的汉字","status":"passed","title":"应该处理无效的汉字"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理韩文中的无效汉字","status":"passed","title":"应该处理韩文中的无效汉字"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理长文本的笔画数估算","status":"passed","title":"应该处理长文本的笔画数估算"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理单字符汉字的部首提取","status":"passed","title":"应该处理单字符汉字的部首提取"},{"ancestorTitles":["EastAsianLanguageProcessor","Edge Cases and Error Handling"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Edge Cases and Error Handling 应该处理复辅音检测的边界情况","status":"passed","title":"应该处理复辅音检测的边界情况"},{"ancestorTitles":["EastAsianLanguageProcessor","Cultural Context Processing"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Cultural Context Processing 应该保持原有文化语境值","status":"passed","title":"应该保持原有文化语境值"},{"ancestorTitles":["EastAsianLanguageProcessor","Cultural Context Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Cultural Context Processing 应该提升敬语相关的文化语境值","status":"passed","title":"应该提升敬语相关的文化语境值"},{"ancestorTitles":["EastAsianLanguageProcessor","Cultural Context Processing"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Cultural Context Processing 应该处理韩语敬语的文化语境","status":"passed","title":"应该处理韩语敬语的文化语境"},{"ancestorTitles":["EastAsianLanguageProcessor","Integration Tests"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Integration Tests 应该完整处理复杂的日语语素","status":"passed","title":"应该完整处理复杂的日语语素"},{"ancestorTitles":["EastAsianLanguageProcessor","Integration Tests"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor Integration Tests 应该完整处理复杂的韩语语素","status":"passed","title":"应该完整处理复杂的韩语语素"}]},{"numFailingTests":0,"numPassingTests":13,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750870666840,"loadTestEnvironmentEnd":1750870663459,"loadTestEnvironmentStart":1750870663199,"runtime":3380,"setupAfterEnvEnd":1750870663734,"setupAfterEnvStart":1750870663734,"setupFilesEnd":1750870663460,"setupFilesStart":1750870663460,"slow":false,"start":1750870663460},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/east-asian-semantic-aligner.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EastAsianSemanticAligner","Constructor"],"duration":4,"failureMessages":[],"fullName":"EastAsianSemanticAligner Constructor 应该正确初始化东亚语言语义对齐器","status":"passed","title":"应该正确初始化东亚语言语义对齐器"},{"ancestorTitles":["EastAsianSemanticAligner","East Asian Alignment Calculation"],"duration":2,"failureMessages":[],"fullName":"EastAsianSemanticAligner East Asian Alignment Calculation 应该计算中日语言对齐","status":"passed","title":"应该计算中日语言对齐"},{"ancestorTitles":["EastAsianSemanticAligner","East Asian Alignment Calculation"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner East Asian Alignment Calculation 应该计算中韩语言对齐","status":"passed","title":"应该计算中韩语言对齐"},{"ancestorTitles":["EastAsianSemanticAligner","East Asian Alignment Calculation"],"duration":0,"failureMessages":[],"fullName":"EastAsianSemanticAligner East Asian Alignment Calculation 应该计算日韩语言对齐","status":"passed","title":"应该计算日韩语言对齐"},{"ancestorTitles":["EastAsianSemanticAligner","Semantic Similarity"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Semantic Similarity 应该为相同概念ID返回高相似度","status":"passed","title":"应该为相同概念ID返回高相似度"},{"ancestorTitles":["EastAsianSemanticAligner","Semantic Similarity"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Semantic Similarity 应该基于语义向量计算相似度","status":"passed","title":"应该基于语义向量计算相似度"},{"ancestorTitles":["EastAsianSemanticAligner","Sino-Sphere Similarity"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Sino-Sphere Similarity 应该检测汉字相似度","status":"passed","title":"应该检测汉字相似度"},{"ancestorTitles":["EastAsianSemanticAligner","Sino-Sphere Similarity"],"duration":5,"failureMessages":[],"fullName":"EastAsianSemanticAligner Sino-Sphere Similarity 应该处理文化概念相似度","status":"passed","title":"应该处理文化概念相似度"},{"ancestorTitles":["EastAsianSemanticAligner","Phonetic Similarity"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Phonetic Similarity 应该计算IPA转录相似度","status":"passed","title":"应该计算IPA转录相似度"},{"ancestorTitles":["EastAsianSemanticAligner","Phonetic Similarity"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Phonetic Similarity 应该处理缺失音韵特征","status":"passed","title":"应该处理缺失音韵特征"},{"ancestorTitles":["EastAsianSemanticAligner","Edge Cases"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Edge Cases 应该处理空文本","status":"passed","title":"应该处理空文本"},{"ancestorTitles":["EastAsianSemanticAligner","Edge Cases"],"duration":0,"failureMessages":[],"fullName":"EastAsianSemanticAligner Edge Cases 应该处理不同概念ID","status":"passed","title":"应该处理不同概念ID"},{"ancestorTitles":["EastAsianSemanticAligner","Edge Cases"],"duration":1,"failureMessages":[],"fullName":"EastAsianSemanticAligner Edge Cases 应该处理缺失语义向量","status":"passed","title":"应该处理缺失语义向量"}]},{"numFailingTests":0,"numPassingTests":19,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750870666902,"loadTestEnvironmentEnd":1750870663483,"loadTestEnvironmentStart":1750870663206,"runtime":3419,"setupAfterEnvEnd":1750870663776,"setupAfterEnvStart":1750870663776,"setupFilesEnd":1750870663483,"setupFilesStart":1750870663483,"slow":false,"start":1750870663483},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-language-processor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":6,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should process Spanish morpheme correctly","status":"passed","title":"should process Spanish morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should extract Spanish phonetic features","status":"passed","title":"should extract Spanish phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should detect Spanish gender correctly","status":"passed","title":"should detect Spanish gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should adjust Spanish cultural context","status":"passed","title":"should adjust Spanish cultural context"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should process French morpheme correctly","status":"passed","title":"should process French morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should extract French phonetic features","status":"passed","title":"should extract French phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should detect French gender correctly","status":"passed","title":"should detect French gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should calculate French elegance","status":"passed","title":"should calculate French elegance"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should process German morpheme correctly","status":"passed","title":"should process German morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should extract German phonetic features","status":"passed","title":"should extract German phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should detect German gender correctly","status":"passed","title":"should detect German gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should analyze German compound structure","status":"passed","title":"should analyze German compound structure"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should calculate German precision","status":"passed","title":"should calculate German precision"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":66,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should handle missing morphological info gracefully","status":"passed","title":"should handle missing morphological info gracefully"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage/multilingual","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":["tests/east-asian-language-processor.test.ts","tests/european-language-processor.test.ts","tests/east-asian-semantic-aligner.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":-1868502259,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["tests/east-asian-language-processor.test.ts","tests/european-language-processor.test.ts","tests/east-asian-semantic-aligner.test.ts"],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../multilingual/lcov-report/index.html"},"endTime":1750870666975,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})