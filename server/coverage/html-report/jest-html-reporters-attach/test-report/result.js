window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":15,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":15,"startTime":1750863327607,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":15,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750863333043,"loadTestEnvironmentEnd":1750863327944,"loadTestEnvironmentStart":1750863327893,"runtime":5098,"setupAfterEnvEnd":1750863328153,"setupAfterEnvStart":1750863328153,"setupFilesEnd":1750863327945,"setupFilesStart":1750863327945,"slow":true,"start":1750863327945},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/core-generation-engine-basic.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","引擎初始化"],"duration":5,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 引擎初始化 应该能够创建引擎实例","status":"passed","title":"应该能够创建引擎实例"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","引擎初始化"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 引擎初始化 应该能够检查初始化状态","status":"passed","title":"应该能够检查初始化状态"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","引擎初始化"],"duration":477,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 引擎初始化 应该能够获取引擎统计信息","status":"passed","title":"应该能够获取引擎统计信息"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","引擎方法调用"],"duration":162,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 引擎方法调用 应该能够调用generate方法","status":"passed","title":"应该能够调用generate方法"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","引擎方法调用"],"duration":114,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 引擎方法调用 应该能够调用generateMultilingual方法","status":"passed","title":"应该能够调用generateMultilingual方法"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","引擎方法调用"],"duration":10,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 引擎方法调用 应该能够调用destroy方法","status":"passed","title":"应该能够调用destroy方法"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","参数验证"],"duration":400,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 参数验证 应该验证generate方法的参数","status":"passed","title":"应该验证generate方法的参数"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","参数验证"],"duration":389,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 参数验证 应该验证generateMultilingual方法的参数","status":"passed","title":"应该验证generateMultilingual方法的参数"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","参数验证"],"duration":5,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 参数验证 应该验证生成数量参数","status":"passed","title":"应该验证生成数量参数"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","错误处理"],"duration":106,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 错误处理 应该处理初始化","status":"passed","title":"应该处理初始化"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","错误处理"],"duration":120,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 错误处理 应该处理生成过程","status":"passed","title":"应该处理生成过程"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","状态管理"],"duration":86,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 状态管理 应该正确管理引擎状态","status":"passed","title":"应该正确管理引擎状态"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","状态管理"],"duration":9,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 状态管理 应该能够重复调用destroy","status":"passed","title":"应该能够重复调用destroy"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","配置测试"],"duration":191,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 配置测试 应该能够处理不同的生成上下文","status":"passed","title":"应该能够处理不同的生成上下文"},{"ancestorTitles":["CoreGenerationEngine - 基础功能测试","配置测试"],"duration":150,"failureMessages":[],"fullName":"CoreGenerationEngine - 基础功能测试 配置测试 应该能够处理不同的生成数量","status":"passed","title":"应该能够处理不同的生成数量"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":["server/tests/core-generation-engine-basic.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":613855447,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["server/tests/core-generation-engine-basic.test.ts"],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750863333109,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})