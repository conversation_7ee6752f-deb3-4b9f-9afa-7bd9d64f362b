window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":5,"numPassedTests":79,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":5,"numTotalTests":79,"startTime":1750834478048,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750834482540,"loadTestEnvironmentEnd":1750834478907,"loadTestEnvironmentStart":1750834478489,"runtime":3631,"setupAfterEnvEnd":1750834479469,"setupAfterEnvStart":1750834479469,"setupFilesEnd":1750834478909,"setupFilesStart":1750834478909,"slow":false,"start":1750834478909},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":8,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 Morpheme 类型","status":"passed","title":"应该正确定义 Morpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GenerationContext 类型","status":"passed","title":"应该正确定义 GenerationContext 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":16,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GeneratedUsername 类型","status":"passed","title":"应该正确定义 GeneratedUsername 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 UniversalConcept 类型","status":"passed","title":"应该正确定义 UniversalConcept 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageSpecificMorpheme 类型","status":"passed","title":"应该正确定义 LanguageSpecificMorpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持传统 CulturalContext 枚举","status":"passed","title":"应该支持传统 CulturalContext 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持多维度 CulturalContext 接口","status":"passed","title":"应该支持多维度 CulturalContext 接口"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持语义向量的向后兼容","status":"passed","title":"应该支持语义向量的向后兼容"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该正确推断函数返回类型","status":"passed","title":"应该正确推断函数返回类型"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该支持泛型类型约束","status":"passed","title":"应该支持泛型类型约束"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该防止类型错误的赋值","status":"passed","title":"应该防止类型错误的赋值"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该确保必需字段的存在","status":"passed","title":"应该确保必需字段的存在"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":4,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该正确计算8维度质量评分","status":"passed","title":"应该正确计算8维度质量评分"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该验证多语种质量评分的一致性","status":"passed","title":"应该验证多语种质量评分的一致性"}]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750834482573,"loadTestEnvironmentEnd":1750834478926,"loadTestEnvironmentStart":1750834478495,"runtime":3645,"setupAfterEnvEnd":1750834479486,"setupAfterEnvStart":1750834479486,"setupFilesEnd":1750834478928,"setupFilesStart":1750834478928,"slow":false,"start":1750834478928},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/unit/EastAsianLanguageProcessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":8,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该正确识别东亚语言","status":"passed","title":"应该正确识别东亚语言"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的日语配置","status":"passed","title":"应该返回正确的日语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":6,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的韩语配置","status":"passed","title":"应该返回正确的韩语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":3,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的假名信息","status":"passed","title":"应该正确处理日语语素的假名信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的汉字信息","status":"passed","title":"应该正确处理日语语素的汉字信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":0,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语敬语系统","status":"passed","title":"应该正确处理日语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的韩文信息","status":"passed","title":"应该正确处理韩语语素的韩文信息"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的汉字词汇","status":"passed","title":"应该正确处理韩语语素的汉字词汇"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":5,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语敬语系统","status":"passed","title":"应该正确处理韩语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","语言特性分析"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言特性分析 应该正确计算音拍数","status":"passed","title":"应该正确计算音拍数"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750834482677,"loadTestEnvironmentEnd":1750834478893,"loadTestEnvironmentStart":1750834478468,"runtime":3782,"setupAfterEnvEnd":1750834479447,"setupAfterEnvStart":1750834479447,"setupFilesEnd":1750834478895,"setupFilesStart":1750834478895,"slow":false,"start":1750834478895},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-quality-assessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":30,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":4,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should assess Spanish morpheme quality","status":"passed","title":"should assess Spanish morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":3,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should include European-specific metrics for Spanish","status":"passed","title":"should include European-specific metrics for Spanish"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should give high scores for traditional Spanish words","status":"passed","title":"should give high scores for traditional Spanish words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French morpheme quality","status":"passed","title":"should assess French morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":4,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should give high elegance scores for French words","status":"passed","title":"should give high elegance scores for French words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French phonetic harmony","status":"passed","title":"should assess French phonetic harmony"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morpheme quality","status":"passed","title":"should assess German morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should give high precision scores for German words","status":"passed","title":"should give high precision scores for German words"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morphological complexity","status":"passed","title":"should assess German morphological complexity"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":3,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should handle German compound words","status":"passed","title":"should handle German compound words"},{"ancestorTitles":["EuropeanQualityAssessor","Cross-Language Comparison"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Cross-Language Comparison should provide consistent quality assessment across languages","status":"passed","title":"should provide consistent quality assessment across languages"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":157,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":9,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should handle missing cultural context gracefully","status":"passed","title":"should handle missing cultural context gracefully"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750834482858,"loadTestEnvironmentEnd":1750834478992,"loadTestEnvironmentStart":1750834478555,"runtime":3865,"setupAfterEnvEnd":1750834479653,"setupAfterEnvStart":1750834479653,"setupFilesEnd":1750834478993,"setupFilesStart":1750834478993,"slow":false,"start":1750834478993},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":27,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 CulturalContext 枚举","status":"passed","title":"应该正确定义 CulturalContext 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 RegisterLevel 枚举","status":"passed","title":"应该正确定义 RegisterLevel 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持枚举值的类型检查","status":"passed","title":"应该支持枚举值的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持语言代码的类型检查","status":"passed","title":"应该支持语言代码的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持寄存器级别的类型检查","status":"passed","title":"应该支持寄存器级别的类型检查"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该正确配置语义向量维度","status":"passed","title":"应该正确配置语义向量维度"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该支持向量维度的数学运算","status":"passed","title":"应该支持向量维度的数学运算"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持多语言标签映射","status":"passed","title":"应该支持多语言标签映射"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持文化适应性配置","status":"passed","title":"应该支持文化适应性配置"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持语音特征配置","status":"passed","title":"应该支持语音特征配置"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该支持8维度质量评分","status":"passed","title":"应该支持8维度质量评分"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该计算平均质量评分","status":"passed","title":"应该计算平均质量评分"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理无效的枚举值","status":"passed","title":"应该处理无效的枚举值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理边界值","status":"passed","title":"应该处理边界值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理空数组和空对象","status":"passed","title":"应该处理空数组和空对象"}]},{"numFailingTests":0,"numPassingTests":19,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750834482903,"loadTestEnvironmentEnd":1750834482594,"loadTestEnvironmentStart":1750834482577,"runtime":309,"setupAfterEnvEnd":1750834482724,"setupAfterEnvStart":1750834482724,"setupFilesEnd":1750834482594,"setupFilesStart":1750834482594,"slow":false,"start":1750834482594},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-language-processor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should process Spanish morpheme correctly","status":"passed","title":"should process Spanish morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should extract Spanish phonetic features","status":"passed","title":"should extract Spanish phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should detect Spanish gender correctly","status":"passed","title":"should detect Spanish gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should adjust Spanish cultural context","status":"passed","title":"should adjust Spanish cultural context"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should process French morpheme correctly","status":"passed","title":"should process French morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should extract French phonetic features","status":"passed","title":"should extract French phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should detect French gender correctly","status":"passed","title":"should detect French gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should calculate French elegance","status":"passed","title":"should calculate French elegance"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should process German morpheme correctly","status":"passed","title":"should process German morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should extract German phonetic features","status":"passed","title":"should extract German phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should detect German gender correctly","status":"passed","title":"should detect German gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should analyze German compound structure","status":"passed","title":"should analyze German compound structure"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should calculate German precision","status":"passed","title":"should calculate German precision"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":109,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should handle missing morphological info gracefully","status":"passed","title":"should handle missing morphological info gracefully"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":5822399,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750834483018,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})