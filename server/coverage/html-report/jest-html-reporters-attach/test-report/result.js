window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":2,"numPassedTests":33,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":2,"numTotalTests":33,"startTime":1750783872906,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750783878880,"loadTestEnvironmentEnd":1750783873669,"loadTestEnvironmentStart":1750783873318,"runtime":5210,"setupAfterEnvEnd":1750783874017,"setupAfterEnvStart":1750783874017,"setupFilesEnd":1750783873670,"setupFilesStart":1750783873670,"slow":true,"start":1750783873670},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":10,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 Morpheme 类型","status":"passed","title":"应该正确定义 Morpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GenerationContext 类型","status":"passed","title":"应该正确定义 GenerationContext 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GeneratedUsername 类型","status":"passed","title":"应该正确定义 GeneratedUsername 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 UniversalConcept 类型","status":"passed","title":"应该正确定义 UniversalConcept 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageSpecificMorpheme 类型","status":"passed","title":"应该正确定义 LanguageSpecificMorpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持传统 CulturalContext 枚举","status":"passed","title":"应该支持传统 CulturalContext 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持多维度 CulturalContext 接口","status":"passed","title":"应该支持多维度 CulturalContext 接口"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持语义向量的向后兼容","status":"passed","title":"应该支持语义向量的向后兼容"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该正确推断函数返回类型","status":"passed","title":"应该正确推断函数返回类型"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该支持泛型类型约束","status":"passed","title":"应该支持泛型类型约束"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该防止类型错误的赋值","status":"passed","title":"应该防止类型错误的赋值"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该确保必需字段的存在","status":"passed","title":"应该确保必需字段的存在"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该正确计算8维度质量评分","status":"passed","title":"应该正确计算8维度质量评分"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":2,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该验证多语种质量评分的一致性","status":"passed","title":"应该验证多语种质量评分的一致性"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750783878907,"loadTestEnvironmentEnd":1750783873670,"loadTestEnvironmentStart":1750783873310,"runtime":5236,"setupAfterEnvEnd":1750783874021,"setupAfterEnvStart":1750783874021,"setupFilesEnd":1750783873671,"setupFilesStart":1750783873671,"slow":true,"start":1750783873671},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":6,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 CulturalContext 枚举","status":"passed","title":"应该正确定义 CulturalContext 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":3,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 RegisterLevel 枚举","status":"passed","title":"应该正确定义 RegisterLevel 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持枚举值的类型检查","status":"passed","title":"应该支持枚举值的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持语言代码的类型检查","status":"passed","title":"应该支持语言代码的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持寄存器级别的类型检查","status":"passed","title":"应该支持寄存器级别的类型检查"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该正确配置语义向量维度","status":"passed","title":"应该正确配置语义向量维度"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该支持向量维度的数学运算","status":"passed","title":"应该支持向量维度的数学运算"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持多语言标签映射","status":"passed","title":"应该支持多语言标签映射"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持文化适应性配置","status":"passed","title":"应该支持文化适应性配置"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持语音特征配置","status":"passed","title":"应该支持语音特征配置"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":9,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该支持8维度质量评分","status":"passed","title":"应该支持8维度质量评分"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该计算平均质量评分","status":"passed","title":"应该计算平均质量评分"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理无效的枚举值","status":"passed","title":"应该处理无效的枚举值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理边界值","status":"passed","title":"应该处理边界值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":0,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理空数组和空对象","status":"passed","title":"应该处理空数组和空对象"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":true,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":1045080585,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750783878997,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})