window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":0,"numPassedTests":0,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":0,"startTime":1750829765356,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-language-processor.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[96mtests/european-language-processor.test.ts\u001b[0m:\u001b[93m176\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'LanguageSpecificMorpheme'.\n\n    \u001b[7m176\u001b[0m     const frenchMorpheme: LanguageSpecificMorpheme = {\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/european-language-processor.test.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'LanguageSpecificMorpheme'.\n\n    \u001b[7m254\u001b[0m     const germanMorpheme: LanguageSpecificMorpheme = {\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/european-language-processor.test.ts\u001b[0m:\u001b[93m342\u001b[0m:\u001b[93m23\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'LanguageSpecificMorpheme'.\n\n    \u001b[7m342\u001b[0m       const morpheme: LanguageSpecificMorpheme = {\n    \u001b[7m   \u001b[0m \u001b[91m                      ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/european-language-processor.test.ts\u001b[0m:\u001b[93m361\u001b[0m:\u001b[93m23\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'LanguageSpecificMorpheme'.\n\n    \u001b[7m361\u001b[0m       const morpheme: LanguageSpecificMorpheme = {\n    \u001b[7m   \u001b[0m \u001b[91m                      ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n","testResults":[]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":["tests/european-language-processor.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":1683682191,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["tests/european-language-processor.test.ts"],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750829774757,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})