window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":2,"numPassedTests":40,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":2,"numTotalTests":40,"startTime":1750867447897,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":11,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750867452379,"loadTestEnvironmentEnd":1750867448590,"loadTestEnvironmentStart":1750867448281,"runtime":3788,"setupAfterEnvEnd":1750867448949,"setupAfterEnvStart":1750867448949,"setupFilesEnd":1750867448591,"setupFilesStart":1750867448591,"slow":false,"start":1750867448591},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/semantic-aligner.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["SemanticAligner","Constructor"],"duration":9,"failureMessages":[],"fullName":"SemanticAligner Constructor 应该使用默认配置创建SemanticAligner实例","status":"passed","title":"应该使用默认配置创建SemanticAligner实例"},{"ancestorTitles":["SemanticAligner","Constructor"],"duration":0,"failureMessages":[],"fullName":"SemanticAligner Constructor 应该使用自定义配置创建SemanticAligner实例","status":"passed","title":"应该使用自定义配置创建SemanticAligner实例"},{"ancestorTitles":["SemanticAligner","Concept-Morpheme Alignment"],"duration":3,"failureMessages":[],"fullName":"SemanticAligner Concept-Morpheme Alignment 应该计算概念与语素的语义对齐度","status":"passed","title":"应该计算概念与语素的语义对齐度"},{"ancestorTitles":["SemanticAligner","Concept-Morpheme Alignment"],"duration":1,"failureMessages":[],"fullName":"SemanticAligner Concept-Morpheme Alignment 应该为中文语素计算对齐度","status":"passed","title":"应该为中文语素计算对齐度"},{"ancestorTitles":["SemanticAligner","Concept-Morpheme Alignment"],"duration":2,"failureMessages":[],"fullName":"SemanticAligner Concept-Morpheme Alignment 应该正确计算综合对齐分数","status":"passed","title":"应该正确计算综合对齐分数"},{"ancestorTitles":["SemanticAligner","Best Alignment Finding"],"duration":1,"failureMessages":[],"fullName":"SemanticAligner Best Alignment Finding 应该找到最佳对齐语素","status":"passed","title":"应该找到最佳对齐语素"},{"ancestorTitles":["SemanticAligner","Best Alignment Finding"],"duration":1,"failureMessages":[],"fullName":"SemanticAligner Best Alignment Finding 应该处理空候选列表","status":"passed","title":"应该处理空候选列表"},{"ancestorTitles":["SemanticAligner","Best Alignment Finding"],"duration":0,"failureMessages":[],"fullName":"SemanticAligner Best Alignment Finding 应该处理低于阈值的对齐","status":"passed","title":"应该处理低于阈值的对齐"},{"ancestorTitles":["SemanticAligner","Cross-Lingual Mapping"],"duration":4,"failureMessages":[],"fullName":"SemanticAligner Cross-Lingual Mapping 应该构建跨语言映射","status":"passed","title":"应该构建跨语言映射"},{"ancestorTitles":["SemanticAligner","Cross-Lingual Mapping"],"duration":1,"failureMessages":[],"fullName":"SemanticAligner Cross-Lingual Mapping 应该处理单语言映射","status":"passed","title":"应该处理单语言映射"},{"ancestorTitles":["SemanticAligner","Cross-Lingual Mapping"],"duration":0,"failureMessages":[],"fullName":"SemanticAligner Cross-Lingual Mapping 应该处理空映射","status":"passed","title":"应该处理空映射"}]},{"numFailingTests":0,"numPassingTests":29,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750867453372,"loadTestEnvironmentEnd":1750867448592,"loadTestEnvironmentStart":1750867448274,"runtime":4779,"setupAfterEnvEnd":1750867448949,"setupAfterEnvStart":1750867448949,"setupFilesEnd":1750867448593,"setupFilesStart":1750867448593,"slow":false,"start":1750867448593},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/language-manager.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["LanguageManager","Constructor"],"duration":5,"failureMessages":[],"fullName":"LanguageManager Constructor 应该使用默认配置创建LanguageManager实例","status":"passed","title":"应该使用默认配置创建LanguageManager实例"},{"ancestorTitles":["LanguageManager","Constructor"],"duration":0,"failureMessages":[],"fullName":"LanguageManager Constructor 应该使用自定义配置创建LanguageManager实例","status":"passed","title":"应该使用自定义配置创建LanguageManager实例"},{"ancestorTitles":["LanguageManager","Initialization"],"duration":59,"failureMessages":[],"fullName":"LanguageManager Initialization 应该成功初始化语言管理器","status":"passed","title":"应该成功初始化语言管理器"},{"ancestorTitles":["LanguageManager","Initialization"],"duration":29,"failureMessages":[],"fullName":"LanguageManager Initialization 应该跳过重复初始化","status":"passed","title":"应该跳过重复初始化"},{"ancestorTitles":["LanguageManager","Initialization"],"duration":65,"failureMessages":[],"fullName":"LanguageManager Initialization 应该处理初始化错误","status":"passed","title":"应该处理初始化错误"},{"ancestorTitles":["LanguageManager","Language Data Loading"],"duration":21,"failureMessages":[],"fullName":"LanguageManager Language Data Loading 应该成功加载中文语素数据","status":"passed","title":"应该成功加载中文语素数据"},{"ancestorTitles":["LanguageManager","Language Data Loading"],"duration":16,"failureMessages":[],"fullName":"LanguageManager Language Data Loading 应该成功加载英文语素数据","status":"passed","title":"应该成功加载英文语素数据"},{"ancestorTitles":["LanguageManager","Language Data Loading"],"duration":20,"failureMessages":[],"fullName":"LanguageManager Language Data Loading 应该处理文件不存在的错误","status":"passed","title":"应该处理文件不存在的错误"},{"ancestorTitles":["LanguageManager","Data Querying"],"duration":20,"failureMessages":[],"fullName":"LanguageManager Data Querying 应该根据语言获取语素","status":"passed","title":"应该根据语言获取语素"},{"ancestorTitles":["LanguageManager","Data Querying"],"duration":16,"failureMessages":[],"fullName":"LanguageManager Data Querying 应该根据概念ID和语言获取语素","status":"passed","title":"应该根据概念ID和语言获取语素"},{"ancestorTitles":["LanguageManager","Data Querying"],"duration":40,"failureMessages":[],"fullName":"LanguageManager Data Querying 应该获取通用概念","status":"passed","title":"应该获取通用概念"},{"ancestorTitles":["LanguageManager","Data Querying"],"duration":17,"failureMessages":[],"fullName":"LanguageManager Data Querying 应该处理未加载语言的查询","status":"passed","title":"应该处理未加载语言的查询"},{"ancestorTitles":["LanguageManager","Language Support"],"duration":28,"failureMessages":[],"fullName":"LanguageManager Language Support 应该检查语言是否支持","status":"passed","title":"应该检查语言是否支持"},{"ancestorTitles":["LanguageManager","Language Support"],"duration":14,"failureMessages":[],"fullName":"LanguageManager Language Support 应该获取支持的语言列表","status":"passed","title":"应该获取支持的语言列表"},{"ancestorTitles":["LanguageManager","Language Support"],"duration":23,"failureMessages":[],"fullName":"LanguageManager Language Support 应该获取语言统计信息","status":"passed","title":"应该获取语言统计信息"},{"ancestorTitles":["LanguageManager","Language Support"],"duration":13,"failureMessages":[],"fullName":"LanguageManager Language Support 应该处理未支持语言的统计查询","status":"passed","title":"应该处理未支持语言的统计查询"},{"ancestorTitles":["LanguageManager","Language Reloading"],"duration":14,"failureMessages":[],"fullName":"LanguageManager Language Reloading 应该成功重新加载语言数据","status":"passed","title":"应该成功重新加载语言数据"},{"ancestorTitles":["LanguageManager","Language Reloading"],"duration":23,"failureMessages":[],"fullName":"LanguageManager Language Reloading 应该处理重新加载时的错误","status":"passed","title":"应该处理重新加载时的错误"},{"ancestorTitles":["LanguageManager","Chinese Morpheme Adaptation"],"duration":18,"failureMessages":[],"fullName":"LanguageManager Chinese Morpheme Adaptation 应该正确适配中文语素到v3.0格式","status":"passed","title":"应该正确适配中文语素到v3.0格式"},{"ancestorTitles":["LanguageManager","Chinese Morpheme Adaptation"],"duration":84,"failureMessages":[],"fullName":"LanguageManager Chinese Morpheme Adaptation 应该正确映射类别到词性标注","status":"passed","title":"应该正确映射类别到词性标注"},{"ancestorTitles":["LanguageManager","Concept-Morpheme Mapping"],"duration":42,"failureMessages":[],"fullName":"LanguageManager Concept-Morpheme Mapping 应该正确构建概念-语素映射","status":"passed","title":"应该正确构建概念-语素映射"},{"ancestorTitles":["LanguageManager","Concept-Morpheme Mapping"],"duration":11,"failureMessages":[],"fullName":"LanguageManager Concept-Morpheme Mapping 应该处理不存在的概念ID查询","status":"passed","title":"应该处理不存在的概念ID查询"},{"ancestorTitles":["LanguageManager","Concept-Morpheme Mapping"],"duration":30,"failureMessages":[],"fullName":"LanguageManager Concept-Morpheme Mapping 应该正确缓存通用概念","status":"passed","title":"应该正确缓存通用概念"},{"ancestorTitles":["LanguageManager","Concept-Morpheme Mapping"],"duration":16,"failureMessages":[],"fullName":"LanguageManager Concept-Morpheme Mapping 应该处理不存在的概念查询","status":"passed","title":"应该处理不存在的概念查询"},{"ancestorTitles":["LanguageManager","Error Handling"],"duration":10,"failureMessages":[],"fullName":"LanguageManager Error Handling 应该处理JSON解析错误","status":"passed","title":"应该处理JSON解析错误"},{"ancestorTitles":["LanguageManager","Error Handling"],"duration":13,"failureMessages":[],"fullName":"LanguageManager Error Handling 应该处理不支持的语言","status":"passed","title":"应该处理不支持的语言"},{"ancestorTitles":["LanguageManager","Error Handling"],"duration":9,"failureMessages":[],"fullName":"LanguageManager Error Handling 应该处理文件读取错误","status":"passed","title":"应该处理文件读取错误"},{"ancestorTitles":["LanguageManager","Ready State"],"duration":0,"failureMessages":[],"fullName":"LanguageManager Ready State 应该在初始化前返回false","status":"passed","title":"应该在初始化前返回false"},{"ancestorTitles":["LanguageManager","Ready State"],"duration":8,"failureMessages":[],"fullName":"LanguageManager Ready State 应该在初始化后返回true","status":"passed","title":"应该在初始化后返回true"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/multilingual/**/*.ts"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":["tests/language-manager.test.ts","tests/semantic-aligner.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":1055498016,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["tests/language-manager.test.ts","tests/semantic-aligner.test.ts"],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750867453470,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})