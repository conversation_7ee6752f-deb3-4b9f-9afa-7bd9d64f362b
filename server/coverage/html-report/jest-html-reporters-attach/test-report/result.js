window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":17,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":17,"startTime":1750833448469,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750833450343,"loadTestEnvironmentEnd":1750833448781,"loadTestEnvironmentStart":1750833448726,"runtime":1561,"setupAfterEnvEnd":1750833448974,"setupAfterEnvStart":1750833448974,"setupFilesEnd":1750833448782,"setupFilesStart":1750833448782,"slow":false,"start":1750833448782},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-quality-assessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":4,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":3,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should assess Spanish morpheme quality","status":"passed","title":"should assess Spanish morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should include European-specific metrics for Spanish","status":"passed","title":"should include European-specific metrics for Spanish"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should give high scores for traditional Spanish words","status":"passed","title":"should give high scores for traditional Spanish words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French morpheme quality","status":"passed","title":"should assess French morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should give high elegance scores for French words","status":"passed","title":"should give high elegance scores for French words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French phonetic harmony","status":"passed","title":"should assess French phonetic harmony"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morpheme quality","status":"passed","title":"should assess German morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should give high precision scores for German words","status":"passed","title":"should give high precision scores for German words"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morphological complexity","status":"passed","title":"should assess German morphological complexity"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":3,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should handle German compound words","status":"passed","title":"should handle German compound words"},{"ancestorTitles":["EuropeanQualityAssessor","Cross-Language Comparison"],"duration":4,"failureMessages":[],"fullName":"EuropeanQualityAssessor Cross-Language Comparison should provide consistent quality assessment across languages","status":"passed","title":"should provide consistent quality assessment across languages"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":87,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should handle missing cultural context gracefully","status":"passed","title":"should handle missing cultural context gracefully"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":["tests/european-quality-assessor.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":-332550189,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["tests/european-quality-assessor.test.ts"],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750833450376,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})