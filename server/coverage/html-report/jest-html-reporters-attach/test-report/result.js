window.jest_html_reporters_callback__({"numFailedTestSuites":4,"numFailedTests":0,"numPassedTestSuites":8,"numPassedTests":120,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":4,"numTodoTests":0,"numTotalTestSuites":12,"numTotalTests":120,"startTime":1750840890410,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":19,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840894440,"loadTestEnvironmentEnd":1750840891242,"loadTestEnvironmentStart":1750840890822,"runtime":3197,"setupAfterEnvEnd":1750840891686,"setupAfterEnvStart":1750840891686,"setupFilesEnd":1750840891243,"setupFilesStart":1750840891243,"slow":false,"start":1750840891243},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-language-processor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":8,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":9,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanLanguageProcessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":5,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should process Spanish morpheme correctly","status":"passed","title":"should process Spanish morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should extract Spanish phonetic features","status":"passed","title":"should extract Spanish phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should detect Spanish gender correctly","status":"passed","title":"should detect Spanish gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","Spanish Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Spanish Language Processing should adjust Spanish cultural context","status":"passed","title":"should adjust Spanish cultural context"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should process French morpheme correctly","status":"passed","title":"should process French morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should extract French phonetic features","status":"passed","title":"should extract French phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should detect French gender correctly","status":"passed","title":"should detect French gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","French Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor French Language Processing should calculate French elegance","status":"passed","title":"should calculate French elegance"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":2,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should process German morpheme correctly","status":"passed","title":"should process German morpheme correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should extract German phonetic features","status":"passed","title":"should extract German phonetic features"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":0,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should detect German gender correctly","status":"passed","title":"should detect German gender correctly"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should analyze German compound structure","status":"passed","title":"should analyze German compound structure"},{"ancestorTitles":["EuropeanLanguageProcessor","German Language Processing"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor German Language Processing should calculate German precision","status":"passed","title":"should calculate German precision"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":83,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanLanguageProcessor","Error Handling"],"duration":1,"failureMessages":[],"fullName":"EuropeanLanguageProcessor Error Handling should handle missing morphological info gracefully","status":"passed","title":"should handle missing morphological info gracefully"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840894684,"loadTestEnvironmentEnd":1750840891262,"loadTestEnvironmentStart":1750840890817,"runtime":3421,"setupAfterEnvEnd":1750840891691,"setupAfterEnvStart":1750840891691,"setupFilesEnd":1750840891263,"setupFilesStart":1750840891263,"slow":false,"start":1750840891263},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/european-quality-assessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":5,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support Spanish language","status":"passed","title":"should support Spanish language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support French language","status":"passed","title":"should support French language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should support German language","status":"passed","title":"should support German language"},{"ancestorTitles":["EuropeanQualityAssessor","Language Support"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Language Support should not support unsupported languages","status":"passed","title":"should not support unsupported languages"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":27,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should assess Spanish morpheme quality","status":"passed","title":"should assess Spanish morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should include European-specific metrics for Spanish","status":"passed","title":"should include European-specific metrics for Spanish"},{"ancestorTitles":["EuropeanQualityAssessor","Spanish Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor Spanish Quality Assessment should give high scores for traditional Spanish words","status":"passed","title":"should give high scores for traditional Spanish words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":3,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French morpheme quality","status":"passed","title":"should assess French morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should give high elegance scores for French words","status":"passed","title":"should give high elegance scores for French words"},{"ancestorTitles":["EuropeanQualityAssessor","French Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor French Quality Assessment should assess French phonetic harmony","status":"passed","title":"should assess French phonetic harmony"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":1,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morpheme quality","status":"passed","title":"should assess German morpheme quality"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":0,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should give high precision scores for German words","status":"passed","title":"should give high precision scores for German words"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":2,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should assess German morphological complexity","status":"passed","title":"should assess German morphological complexity"},{"ancestorTitles":["EuropeanQualityAssessor","German Quality Assessment"],"duration":5,"failureMessages":[],"fullName":"EuropeanQualityAssessor German Quality Assessment should handle German compound words","status":"passed","title":"should handle German compound words"},{"ancestorTitles":["EuropeanQualityAssessor","Cross-Language Comparison"],"duration":7,"failureMessages":[],"fullName":"EuropeanQualityAssessor Cross-Language Comparison should provide consistent quality assessment across languages","status":"passed","title":"should provide consistent quality assessment across languages"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":121,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should throw error for unsupported language","status":"passed","title":"should throw error for unsupported language"},{"ancestorTitles":["EuropeanQualityAssessor","Error Handling"],"duration":3,"failureMessages":[],"fullName":"EuropeanQualityAssessor Error Handling should handle missing cultural context gracefully","status":"passed","title":"should handle missing cultural context gracefully"}]},{"numFailingTests":0,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840894754,"loadTestEnvironmentEnd":1750840894512,"loadTestEnvironmentStart":1750840894498,"runtime":242,"setupAfterEnvEnd":1750840894653,"setupAfterEnvStart":1750840894653,"setupFilesEnd":1750840894512,"setupFilesStart":1750840894512,"slow":false,"start":1750840894512},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 CulturalContext 枚举","status":"passed","title":"应该正确定义 CulturalContext 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义 RegisterLevel 枚举","status":"passed","title":"应该正确定义 RegisterLevel 枚举"},{"ancestorTitles":["基础功能测试","类型定义测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 类型定义测试 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持枚举值的类型检查","status":"passed","title":"应该支持枚举值的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":6,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持语言代码的类型检查","status":"passed","title":"应该支持语言代码的类型检查"},{"ancestorTitles":["基础功能测试","类型兼容性测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 类型兼容性测试 应该支持寄存器级别的类型检查","status":"passed","title":"应该支持寄存器级别的类型检查"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该正确配置语义向量维度","status":"passed","title":"应该正确配置语义向量维度"},{"ancestorTitles":["基础功能测试","配置常量测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 配置常量测试 应该支持向量维度的数学运算","status":"passed","title":"应该支持向量维度的数学运算"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持多语言标签映射","status":"passed","title":"应该支持多语言标签映射"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持文化适应性配置","status":"passed","title":"应该支持文化适应性配置"},{"ancestorTitles":["基础功能测试","数据结构验证"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 数据结构验证 应该支持语音特征配置","status":"passed","title":"应该支持语音特征配置"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该支持8维度质量评分","status":"passed","title":"应该支持8维度质量评分"},{"ancestorTitles":["基础功能测试","质量评估系统测试"],"duration":2,"failureMessages":[],"fullName":"基础功能测试 质量评估系统测试 应该计算平均质量评分","status":"passed","title":"应该计算平均质量评分"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":1,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理无效的枚举值","status":"passed","title":"应该处理无效的枚举值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":4,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理边界值","status":"passed","title":"应该处理边界值"},{"ancestorTitles":["基础功能测试","错误处理测试"],"duration":3,"failureMessages":[],"fullName":"基础功能测试 错误处理测试 应该处理空数组和空对象","status":"passed","title":"应该处理空数组和空对象"}]},{"numFailingTests":0,"numPassingTests":15,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840894889,"loadTestEnvironmentEnd":1750840891312,"loadTestEnvironmentStart":1750840890819,"runtime":3575,"setupAfterEnvEnd":1750840891812,"setupAfterEnvStart":1750840891812,"setupFilesEnd":1750840891314,"setupFilesStart":1750840891314,"slow":false,"start":1750840891314},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/data-validator.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["DataValidator","Constructor"],"duration":21,"failureMessages":[],"fullName":"DataValidator Constructor should create DataValidator with default configuration","status":"passed","title":"should create DataValidator with default configuration"},{"ancestorTitles":["DataValidator","Constructor"],"duration":1,"failureMessages":[],"fullName":"DataValidator Constructor should create DataValidator with custom configuration","status":"passed","title":"should create DataValidator with custom configuration"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":145,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should validate a valid morpheme","status":"passed","title":"should validate a valid morpheme"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":20,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid ID format","status":"passed","title":"should detect invalid ID format"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":19,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid text length","status":"passed","title":"should detect invalid text length"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":35,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid category","status":"passed","title":"should detect invalid category"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":22,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid quality score","status":"passed","title":"should detect invalid quality score"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":17,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect invalid semantic vector dimension","status":"passed","title":"should detect invalid semantic vector dimension"},{"ancestorTitles":["DataValidator","Single Morpheme Validation"],"duration":15,"failureMessages":[],"fullName":"DataValidator Single Morpheme Validation should detect missing required fields","status":"passed","title":"should detect missing required fields"},{"ancestorTitles":["DataValidator","Batch Validation"],"duration":6,"failureMessages":[],"fullName":"DataValidator Batch Validation should validate multiple morphemes","status":"passed","title":"should validate multiple morphemes"},{"ancestorTitles":["DataValidator","Batch Validation"],"duration":9,"failureMessages":[],"fullName":"DataValidator Batch Validation should handle mixed valid and invalid morphemes","status":"passed","title":"should handle mixed valid and invalid morphemes"},{"ancestorTitles":["DataValidator","Warning Detection"],"duration":6,"failureMessages":[],"fullName":"DataValidator Warning Detection should detect inconsistent ID and category as warnings","status":"passed","title":"should detect inconsistent ID and category as warnings"},{"ancestorTitles":["DataValidator","Warning Detection"],"duration":6,"failureMessages":[],"fullName":"DataValidator Warning Detection should detect inconsistent character count as warnings","status":"passed","title":"should detect inconsistent character count as warnings"},{"ancestorTitles":["DataValidator","Custom Rules"],"duration":18,"failureMessages":[],"fullName":"DataValidator Custom Rules should support adding custom validation rules","status":"passed","title":"should support adding custom validation rules"},{"ancestorTitles":["DataValidator","Performance"],"duration":11,"failureMessages":[],"fullName":"DataValidator Performance should validate large batches efficiently","status":"passed","title":"should validate large batches efficiently"}]},{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840894955,"loadTestEnvironmentEnd":1750840894814,"loadTestEnvironmentStart":1750840894800,"runtime":141,"setupAfterEnvEnd":1750840894895,"setupAfterEnvStart":1750840894895,"setupFilesEnd":1750840894814,"setupFilesStart":1750840894814,"slow":false,"start":1750840894814},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/unit/EastAsianLanguageProcessor.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":7,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该正确识别东亚语言","status":"passed","title":"应该正确识别东亚语言"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的日语配置","status":"passed","title":"应该返回正确的日语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","语言配置管理"],"duration":9,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言配置管理 应该返回正确的韩语配置","status":"passed","title":"应该返回正确的韩语配置"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的假名信息","status":"passed","title":"应该正确处理日语语素的假名信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语语素的汉字信息","status":"passed","title":"应该正确处理日语语素的汉字信息"},{"ancestorTitles":["EastAsianLanguageProcessor","日语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 日语语素处理 应该正确处理日语敬语系统","status":"passed","title":"应该正确处理日语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":2,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的韩文信息","status":"passed","title":"应该正确处理韩语语素的韩文信息"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语语素的汉字词汇","status":"passed","title":"应该正确处理韩语语素的汉字词汇"},{"ancestorTitles":["EastAsianLanguageProcessor","韩语语素处理"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 韩语语素处理 应该正确处理韩语敬语系统","status":"passed","title":"应该正确处理韩语敬语系统"},{"ancestorTitles":["EastAsianLanguageProcessor","语言特性分析"],"duration":1,"failureMessages":[],"fullName":"EastAsianLanguageProcessor 语言特性分析 应该正确计算音拍数","status":"passed","title":"应该正确计算音拍数"}]},{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840894960,"loadTestEnvironmentEnd":1750840894794,"loadTestEnvironmentStart":1750840894779,"runtime":166,"setupAfterEnvEnd":1750840894900,"setupAfterEnvStart":1750840894899,"setupFilesEnd":1750840894794,"setupFilesStart":1750840894794,"slow":false,"start":1750840894794},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":3,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 Morpheme 类型","status":"passed","title":"应该正确定义 Morpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GenerationContext 类型","status":"passed","title":"应该正确定义 GenerationContext 类型"},{"ancestorTitles":["TypeScript 类型安全测试","核心类型定义"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 核心类型定义 应该正确定义 GeneratedUsername 类型","status":"passed","title":"应该正确定义 GeneratedUsername 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageCode 枚举","status":"passed","title":"应该正确定义 LanguageCode 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 UniversalConcept 类型","status":"passed","title":"应该正确定义 UniversalConcept 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义 LanguageSpecificMorpheme 类型","status":"passed","title":"应该正确定义 LanguageSpecificMorpheme 类型"},{"ancestorTitles":["TypeScript 类型安全测试","多语种类型定义"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 多语种类型定义 应该正确定义语义向量维度常量","status":"passed","title":"应该正确定义语义向量维度常量"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持传统 CulturalContext 枚举","status":"passed","title":"应该支持传统 CulturalContext 枚举"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持多维度 CulturalContext 接口","status":"passed","title":"应该支持多维度 CulturalContext 接口"},{"ancestorTitles":["TypeScript 类型安全测试","类型兼容性测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型兼容性测试 应该支持语义向量的向后兼容","status":"passed","title":"应该支持语义向量的向后兼容"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":0,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该正确推断函数返回类型","status":"passed","title":"应该正确推断函数返回类型"},{"ancestorTitles":["TypeScript 类型安全测试","类型推断测试"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 类型推断测试 应该支持泛型类型约束","status":"passed","title":"应该支持泛型类型约束"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该防止类型错误的赋值","status":"passed","title":"应该防止类型错误的赋值"},{"ancestorTitles":["TypeScript 类型安全测试","编译时类型检查"],"duration":1,"failureMessages":[],"fullName":"TypeScript 类型安全测试 编译时类型检查 应该确保必需字段的存在","status":"passed","title":"应该确保必需字段的存在"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":4,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该正确计算8维度质量评分","status":"passed","title":"应该正确计算8维度质量评分"},{"ancestorTitles":["TypeScript 类型安全测试","质量评估系统准确性测试"],"duration":4,"failureMessages":[],"fullName":"TypeScript 类型安全测试 质量评估系统准确性测试 应该验证多语种质量评分的一致性","status":"passed","title":"应该验证多语种质量评分的一致性"}]},{"numFailingTests":0,"numPassingTests":12,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840895076,"loadTestEnvironmentEnd":1750840894935,"loadTestEnvironmentStart":1750840894921,"runtime":141,"setupAfterEnvEnd":1750840895049,"setupAfterEnvStart":1750840895049,"setupFilesEnd":1750840894935,"setupFilesStart":1750840894935,"slow":false,"start":1750840894935},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/core-generation-engine.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["CoreGenerationEngine","Constructor"],"duration":2,"failureMessages":[],"fullName":"CoreGenerationEngine Constructor should create engine instance with default configuration","status":"passed","title":"should create engine instance with default configuration"},{"ancestorTitles":["CoreGenerationEngine","Constructor"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Constructor should initialize with proper dependencies","status":"passed","title":"should initialize with proper dependencies"},{"ancestorTitles":["CoreGenerationEngine","Initialization"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Initialization should initialize successfully","status":"passed","title":"should initialize successfully"},{"ancestorTitles":["CoreGenerationEngine","Initialization"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine Initialization should handle initialization failure gracefully","status":"passed","title":"should handle initialization failure gracefully"},{"ancestorTitles":["CoreGenerationEngine","Initialization"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine Initialization should skip re-initialization if already initialized","status":"passed","title":"should skip re-initialization if already initialized"},{"ancestorTitles":["CoreGenerationEngine","Generation Methods"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Generation Methods should generate username successfully","status":"passed","title":"should generate username successfully"},{"ancestorTitles":["CoreGenerationEngine","Generation Methods"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Generation Methods should handle generation errors gracefully","status":"passed","title":"should handle generation errors gracefully"},{"ancestorTitles":["CoreGenerationEngine","Statistics and Performance"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Statistics and Performance should provide generation statistics","status":"passed","title":"should provide generation statistics"},{"ancestorTitles":["CoreGenerationEngine","Statistics and Performance"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Statistics and Performance should calculate average generation time","status":"passed","title":"should calculate average generation time"},{"ancestorTitles":["CoreGenerationEngine","Validation"],"duration":0,"failureMessages":[],"fullName":"CoreGenerationEngine Validation should validate generation context","status":"passed","title":"should validate generation context"},{"ancestorTitles":["CoreGenerationEngine","Cleanup"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Cleanup should cleanup resources successfully","status":"passed","title":"should cleanup resources successfully"},{"ancestorTitles":["CoreGenerationEngine","Cleanup"],"duration":1,"failureMessages":[],"fullName":"CoreGenerationEngine Cleanup should handle cleanup errors gracefully","status":"passed","title":"should handle cleanup errors gracefully"}]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/quality-modules.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[96mtests/quality-modules.test.ts\u001b[0m:\u001b[93m76\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NATURE_ELEMENTS' does not exist on type 'typeof MorphemeCategory'.\n\n    \u001b[7m76\u001b[0m       MorphemeCategory.NATURE_ELEMENTS,\n    \u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~\u001b[0m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/multilingual-modules.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[96mtests/multilingual-modules.test.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m23\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'destroy' does not exist on type 'LanguageManager'.\n\n    \u001b[7m33\u001b[0m       languageManager.destroy?.()\n    \u001b[7m  \u001b[0m \u001b[91m                      ~~~~~~~\u001b[0m\n    \u001b[96mtests/multilingual-modules.test.ts\u001b[0m:\u001b[93m48\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDefaultLanguage' does not exist on type 'LanguageManager'.\n\n    \u001b[7m48\u001b[0m       expect(languageManager.getDefaultLanguage()).toBe(LanguageCode.ZH_CN)\n    \u001b[7m  \u001b[0m \u001b[91m                             ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/multilingual-modules.test.ts\u001b[0m:\u001b[93m70\u001b[0m:\u001b[93m37\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getStats' does not exist on type 'LanguageManager'.\n\n    \u001b[7m70\u001b[0m       const stats = languageManager.getStats()\n    \u001b[7m  \u001b[0m \u001b[91m                                    ~~~~~~~~\u001b[0m\n    \u001b[96mtests/multilingual-modules.test.ts\u001b[0m:\u001b[93m86\u001b[0m:\u001b[93m41\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getMorphemesByCategory' does not exist on type 'LanguageManager'.\n\n    \u001b[7m86\u001b[0m       const morphemes = languageManager.getMorphemesByCategory(\n    \u001b[7m  \u001b[0m \u001b[91m                                        ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/multilingual-modules.test.ts\u001b[0m:\u001b[93m94\u001b[0m:\u001b[93m41\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getMorphemesByCulturalContext' does not exist on type 'LanguageManager'.\n\n    \u001b[7m94\u001b[0m       const morphemes = languageManager.getMorphemesByCulturalContext(\n    \u001b[7m  \u001b[0m \u001b[91m                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/morpheme-repository.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m58\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'phonetic_features' does not exist in type 'Morpheme'.\n\n    \u001b[7m58\u001b[0m         phonetic_features: {\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m80\u001b[0m:\u001b[93m36\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NATURE_ELEMENTS' does not exist on type 'typeof MorphemeCategory'.\n\n    \u001b[7m80\u001b[0m         category: MorphemeCategory.NATURE_ELEMENTS,\n    \u001b[7m  \u001b[0m \u001b[91m                                   ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m85\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'phonetic_features' does not exist in type 'Morpheme'.\n\n    \u001b[7m85\u001b[0m         phonetic_features: {\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m173\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NATURE_ELEMENTS' does not exist on type 'typeof MorphemeCategory'.\n\n    \u001b[7m173\u001b[0m       const nature = repository.findByCategory(MorphemeCategory.NATURE_ELEMENTS)\n    \u001b[7m   \u001b[0m \u001b[91m                                                                ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m211\u001b[0m:\u001b[93m48\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NATURE_ELEMENTS' does not exist on type 'typeof MorphemeCategory'.\n\n    \u001b[7m211\u001b[0m       expect(stats.byCategory[MorphemeCategory.NATURE_ELEMENTS]).toBeGreaterThanOrEqual(0)\n    \u001b[7m   \u001b[0m \u001b[91m                                               ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m225\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'qualityDistribution' does not exist on type 'MorphemeRepositoryStats'.\n\n    \u001b[7m225\u001b[0m       expect(stats.qualityDistribution).toHaveProperty('high')\n    \u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m226\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'qualityDistribution' does not exist on type 'MorphemeRepositoryStats'.\n\n    \u001b[7m226\u001b[0m       expect(stats.qualityDistribution).toHaveProperty('medium')\n    \u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m227\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'qualityDistribution' does not exist on type 'MorphemeRepositoryStats'.\n\n    \u001b[7m227\u001b[0m       expect(stats.qualityDistribution).toHaveProperty('low')\n    \u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m230\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'qualityDistribution' does not exist on type 'MorphemeRepositoryStats'.\n\n    \u001b[7m230\u001b[0m       expect(stats.qualityDistribution.high).toBe(2)\n    \u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m231\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'qualityDistribution' does not exist on type 'MorphemeRepositoryStats'.\n\n    \u001b[7m231\u001b[0m       expect(stats.qualityDistribution.medium).toBe(0)\n    \u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'qualityDistribution' does not exist on type 'MorphemeRepositoryStats'.\n\n    \u001b[7m232\u001b[0m       expect(stats.qualityDistribution.low).toBe(0)\n    \u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m265\u001b[0m:\u001b[93m66\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'PROFESSION' does not exist on type 'typeof MorphemeCategory'. Did you mean 'PROFESSIONS'?\n\n    \u001b[7m265\u001b[0m       const results = repository.findByCategory(MorphemeCategory.PROFESSION)\n    \u001b[7m   \u001b[0m \u001b[91m                                                                 ~~~~~~~~~~\u001b[0m\n\n      \u001b[96mtypes/core.ts\u001b[0m:\u001b[93m61\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m61\u001b[0m   PROFESSIONS = 'professions',\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~\u001b[0m\n        'PROFESSIONS' is declared here.\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m270\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sampleRandom' does not exist on type 'MorphemeRepository'.\n\n    \u001b[7m270\u001b[0m       const samples = repository.sampleRandom(0)\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m273\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sampleRandom' does not exist on type 'MorphemeRepository'.\n\n    \u001b[7m273\u001b[0m       const largeSamples = repository.sampleRandom(100)\n    \u001b[7m   \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/morpheme-repository.test.ts\u001b[0m:\u001b[93m284\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type 'any[]' is not assignable to parameter of type 'Morpheme'.\n      Type 'any[]' is missing the following properties from type 'Morpheme': id, text, category, subcategory, and 10 more.\n\n    \u001b[7m284\u001b[0m       const results = repository.findSimilar(invalidVector, 1, 0.8)\n    \u001b[7m   \u001b[0m \u001b[91m                                             ~~~~~~~~~~~~~\u001b[0m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/core-generation-engine-comprehensive.test.ts","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[96mtests/core-generation-engine-comprehensive.test.ts\u001b[0m:\u001b[93m90\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType '\"balanced\"' is not assignable to type 'StylePreference'.\n\n    \u001b[7m90\u001b[0m         style_preference: 'balanced',\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtests/core-generation-engine-comprehensive.test.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType '\"balanced\"' is not assignable to type 'StylePreference'.\n\n    \u001b[7m97\u001b[0m       style_preference: 'balanced'\n    \u001b[7m  \u001b[0m \u001b[91m      ~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96mtypes/core.ts\u001b[0m:\u001b[93m499\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m499\u001b[0m   style_preference: StylePreference\n        \u001b[7m   \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~\u001b[0m\n        The expected type comes from property 'style_preference' which is declared here on type 'GenerationContext'\n","testResults":[]},{"numFailingTests":0,"numPassingTests":14,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750840912916,"loadTestEnvironmentEnd":1750840891244,"loadTestEnvironmentStart":1750840890803,"runtime":21671,"setupAfterEnvEnd":1750840891682,"setupAfterEnvStart":1750840891682,"setupFilesEnd":1750840891245,"setupFilesStart":1750840891245,"slow":true,"start":1750840891245},"testFilePath":"/home/<USER>/develop/workspace/namer-v6/server/tests/data-loader.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["DataLoader","Constructor"],"duration":9,"failureMessages":[],"fullName":"DataLoader Constructor should create DataLoader with default configuration","status":"passed","title":"should create DataLoader with default configuration"},{"ancestorTitles":["DataLoader","Constructor"],"duration":1,"failureMessages":[],"fullName":"DataLoader Constructor should create DataLoader with custom configuration","status":"passed","title":"should create DataLoader with custom configuration"},{"ancestorTitles":["DataLoader","File Loading"],"duration":145,"failureMessages":[],"fullName":"DataLoader File Loading should load JSON files successfully","status":"passed","title":"should load JSON files successfully"},{"ancestorTitles":["DataLoader","File Loading"],"duration":6141,"failureMessages":[],"fullName":"DataLoader File Loading should handle file reading errors","status":"passed","title":"should handle file reading errors"},{"ancestorTitles":["DataLoader","File Loading"],"duration":6027,"failureMessages":[],"fullName":"DataLoader File Loading should handle invalid JSON files","status":"passed","title":"should handle invalid JSON files"},{"ancestorTitles":["DataLoader","File Loading"],"duration":17,"failureMessages":[],"fullName":"DataLoader File Loading should filter supported file formats","status":"passed","title":"should filter supported file formats"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":14,"failureMessages":[],"fullName":"DataLoader Data Validation should validate morpheme data structure","status":"passed","title":"should validate morpheme data structure"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":24,"failureMessages":[],"fullName":"DataLoader Data Validation should detect invalid morpheme data","status":"passed","title":"should detect invalid morpheme data"},{"ancestorTitles":["DataLoader","Data Validation"],"duration":35,"failureMessages":[],"fullName":"DataLoader Data Validation should handle validation with warnings","status":"passed","title":"should handle validation with warnings"},{"ancestorTitles":["DataLoader","Caching"],"duration":38,"failureMessages":[],"fullName":"DataLoader Caching should cache loaded data","status":"passed","title":"should cache loaded data"},{"ancestorTitles":["DataLoader","Caching"],"duration":49,"failureMessages":[],"fullName":"DataLoader Caching should respect cache TTL","status":"passed","title":"should respect cache TTL"},{"ancestorTitles":["DataLoader","Statistics Generation"],"duration":18,"failureMessages":[],"fullName":"DataLoader Statistics Generation should generate correct statistics","status":"passed","title":"should generate correct statistics"},{"ancestorTitles":["DataLoader","Error Recovery"],"duration":3020,"failureMessages":[],"fullName":"DataLoader Error Recovery should retry on transient failures","status":"passed","title":"should retry on transient failures"},{"ancestorTitles":["DataLoader","Error Recovery"],"duration":3014,"failureMessages":[],"fullName":"DataLoader Error Recovery should fail after max retries","status":"passed","title":"should fail after max retries"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":true,"collectCoverageFrom":["core/**/*.ts","types/**/*.ts","config/**/*.ts","!**/*.d.ts","!**/node_modules/**","!**/test/**","!**/dist/**"],"coverageDirectory":"/home/<USER>/develop/workspace/namer-v6/server/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","html","lcov","json"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":85,"statements":85},"./core/engines/":{"branches":85,"functions":90,"lines":90,"statements":90},"./core/multilingual/":{"branches":85,"functions":90,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":true,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":4,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/jest-html-reporters@3.1.7/node_modules/jest-html-reporters/index.js",{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"hideIcon":false,"pageTitle":"namer-v6 测试报告"}]],"rootDir":"/home/<USER>/develop/workspace/namer-v6/server","runInBand":false,"runTestsByPath":false,"seed":-936305815,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testSequencer":"/home/<USER>/develop/workspace/namer-v6/node_modules/.pnpm/@jest+test-sequencer@30.0.2/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false,"coverageLinkPath":"../lcov-report/index.html"},"endTime":1750840913019,"_reporterOptions":{"publicPath":"./coverage/html-report","filename":"test-report.html","expand":true,"pageTitle":"namer-v6 测试报告","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})