
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.37% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>656/2396</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.04% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>384/1322</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.8% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>139/500</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.46% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>626/2199</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="config"><a href="config/index.html">config</a></td>
	<td data-value="48.27" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 48%"></div><div class="cover-empty" style="width: 52%"></div></div>
	</td>
	<td data-value="48.27" class="pct low">48.27%</td>
	<td data-value="29" class="abs low">14/29</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="9" class="abs low">1/9</td>
	<td data-value="48.27" class="pct low">48.27%</td>
	<td data-value="29" class="abs low">14/29</td>
	</tr>

<tr>
	<td class="file medium" data-value="core/data"><a href="core/data/index.html">core/data</a></td>
	<td data-value="83.13" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.13" class="pct medium">83.13%</td>
	<td data-value="255" class="abs medium">212/255</td>
	<td data-value="75.25" class="pct medium">75.25%</td>
	<td data-value="194" class="abs medium">146/194</td>
	<td data-value="75.75" class="pct medium">75.75%</td>
	<td data-value="33" class="abs medium">25/33</td>
	<td data-value="83.33" class="pct medium">83.33%</td>
	<td data-value="246" class="abs medium">205/246</td>
	</tr>

<tr>
	<td class="file low" data-value="core/engines"><a href="core/engines/index.html">core/engines</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="709" class="abs low">0/709</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="312" class="abs low">0/312</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="164" class="abs low">0/164</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="637" class="abs low">0/637</td>
	</tr>

<tr>
	<td class="file low" data-value="core/multilingual"><a href="core/multilingual/index.html">core/multilingual</a></td>
	<td data-value="38.29" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 38%"></div><div class="cover-empty" style="width: 62%"></div></div>
	</td>
	<td data-value="38.29" class="pct low">38.29%</td>
	<td data-value="658" class="abs low">252/658</td>
	<td data-value="34.05" class="pct low">34.05%</td>
	<td data-value="417" class="abs low">142/417</td>
	<td data-value="50.74" class="pct medium">50.74%</td>
	<td data-value="134" class="abs medium">68/134</td>
	<td data-value="40.16" class="pct low">40.16%</td>
	<td data-value="600" class="abs low">241/600</td>
	</tr>

<tr>
	<td class="file low" data-value="core/quality"><a href="core/quality/index.html">core/quality</a></td>
	<td data-value="31.81" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 31%"></div><div class="cover-empty" style="width: 69%"></div></div>
	</td>
	<td data-value="31.81" class="pct low">31.81%</td>
	<td data-value="418" class="abs low">133/418</td>
	<td data-value="30.32" class="pct low">30.32%</td>
	<td data-value="277" class="abs low">84/277</td>
	<td data-value="38.23" class="pct low">38.23%</td>
	<td data-value="102" class="abs low">39/102</td>
	<td data-value="31.51" class="pct low">31.51%</td>
	<td data-value="384" class="abs low">121/384</td>
	</tr>

<tr>
	<td class="file low" data-value="core/repositories"><a href="core/repositories/index.html">core/repositories</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="262" class="abs low">0/262</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="102" class="abs low">0/102</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="240" class="abs low">0/240</td>
	</tr>

<tr>
	<td class="file medium" data-value="types"><a href="types/index.html">types</a></td>
	<td data-value="69.23" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.23" class="pct medium">69.23%</td>
	<td data-value="65" class="abs medium">45/65</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="20" class="abs medium">12/20</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="9" class="abs medium">6/9</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="63" class="abs medium">45/63</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T08:41:58.090Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    