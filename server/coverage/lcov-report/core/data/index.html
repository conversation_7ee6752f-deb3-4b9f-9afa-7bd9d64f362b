
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/data</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> core/data</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.34% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>205/252</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.41% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>149/195</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">65.51% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>19/29</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.63% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>200/245</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="DataLoader.ts"><a href="DataLoader.ts.html">DataLoader.ts</a></td>
	<td data-value="77.77" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 77%"></div><div class="cover-empty" style="width: 23%"></div></div>
	</td>
	<td data-value="77.77" class="pct medium">77.77%</td>
	<td data-value="135" class="abs medium">105/135</td>
	<td data-value="74.72" class="pct medium">74.72%</td>
	<td data-value="91" class="abs medium">68/91</td>
	<td data-value="52.94" class="pct medium">52.94%</td>
	<td data-value="17" class="abs medium">9/17</td>
	<td data-value="77.61" class="pct medium">77.61%</td>
	<td data-value="134" class="abs medium">104/134</td>
	</tr>

<tr>
	<td class="file high" data-value="DataValidator.ts"><a href="DataValidator.ts.html">DataValidator.ts</a></td>
	<td data-value="85.47" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.47" class="pct high">85.47%</td>
	<td data-value="117" class="abs high">100/117</td>
	<td data-value="77.88" class="pct medium">77.88%</td>
	<td data-value="104" class="abs medium">81/104</td>
	<td data-value="83.33" class="pct medium">83.33%</td>
	<td data-value="12" class="abs medium">10/12</td>
	<td data-value="86.48" class="pct high">86.48%</td>
	<td data-value="111" class="abs high">96/111</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T07:32:37.979Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    