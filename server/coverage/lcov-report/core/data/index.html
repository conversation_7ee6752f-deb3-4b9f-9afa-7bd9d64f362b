
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/data</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> core/data</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.8% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>178/255</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">47.42% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>92/194</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.63% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>21/33</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.32% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>173/246</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="DataLoader.ts"><a href="DataLoader.ts.html">DataLoader.ts</a></td>
	<td data-value="65.94" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 65%"></div><div class="cover-empty" style="width: 35%"></div></div>
	</td>
	<td data-value="65.94" class="pct medium">65.94%</td>
	<td data-value="138" class="abs medium">91/138</td>
	<td data-value="34.44" class="pct low">34.44%</td>
	<td data-value="90" class="abs low">31/90</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="21" class="abs medium">12/21</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="135" class="abs medium">90/135</td>
	</tr>

<tr>
	<td class="file medium" data-value="DataValidator.ts"><a href="DataValidator.ts.html">DataValidator.ts</a></td>
	<td data-value="74.35" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 74%"></div><div class="cover-empty" style="width: 26%"></div></div>
	</td>
	<td data-value="74.35" class="pct medium">74.35%</td>
	<td data-value="117" class="abs medium">87/117</td>
	<td data-value="58.65" class="pct medium">58.65%</td>
	<td data-value="104" class="abs medium">61/104</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="12" class="abs medium">9/12</td>
	<td data-value="74.77" class="pct medium">74.77%</td>
	<td data-value="111" class="abs medium">83/111</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T14:55:38.011Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    