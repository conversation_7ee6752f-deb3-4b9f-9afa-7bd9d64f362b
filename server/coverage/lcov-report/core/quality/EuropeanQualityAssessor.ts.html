
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/quality/EuropeanQualityAssessor.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">core/quality</a> EuropeanQualityAssessor.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/285</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/193</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/77</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/255</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * 欧洲语言质量评估器
 * 
 * 专门为欧洲语言（西班牙语、法语、德语）设计的质量评估系统
 * 扩展基础8维质量评估框架，增加欧洲语言特有的评估维度
 * 
 * @fileoverview 欧洲语言质量评估器实现
 * @version 3.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */
&nbsp;
import { LanguageCode } from '../../types/multilingual'
import type {
  LanguageSpecificMorpheme,
  QualityScores
} from '../../types/multilingual'
&nbsp;
/**
 * 欧洲语言特有质量指标
 */
export interface EuropeanQualityMetrics {
  /** 语音和谐度 [0-1] */
  phonetic_harmony: number
  /** 形态学复杂度适宜性 [0-1] */
  morphological_appropriateness: number
  /** 文化优雅度 [0-1] */
  cultural_elegance: number
  /** 语言纯正度 [0-1] */
  linguistic_authenticity: number
  /** 国际化适应性 [0-1] */
  international_adaptability: number
  /** 发音友好度 [0-1] */
  pronunciation_friendliness: number
}
&nbsp;
/**
 * 欧洲语言质量配置
 */
export interface EuropeanQualityConfig {
  /** 语言代码 */
  language: LanguageCode
  /** 文化权重配置 */
  cultural_weights: {
    formality: number
    elegance: number
    traditionality: number
    internationality: number
  }
  /** 语言学权重配置 */
  linguistic_weights: {
    phonetic_beauty: number
    morphological_richness: number
    semantic_precision: number
    cultural_appropriateness: number
  }
  /** 质量阈值配置 */
  quality_thresholds: {
    min_acceptable: number
    good_quality: number
    excellent_quality: number
  }
}
&nbsp;
/**
 * 欧洲语言质量评估器类
 */
export class EuropeanQualityAssessor {
  private configs: Map&lt;LanguageCode, EuropeanQualityConfig&gt;
&nbsp;
<span class="fstat-no" title="function not covered" >  constructor() {</span>
<span class="cstat-no" title="statement not covered" >    this.configs = new Map()</span>
<span class="cstat-no" title="statement not covered" >    this.initializeConfigs()</span>
  }
&nbsp;
  /**
   * 检查是否支持该语言
   */
<span class="fstat-no" title="function not covered" >  supportsLanguage(</span>language: LanguageCode): boolean {
<span class="cstat-no" title="statement not covered" >    return this.configs.has(language)</span>
  }
&nbsp;
  /**
   * 评估语素质量 - 支持任意morpheme对象
   *
   * @param morpheme 语素对象
   * @param language 语言代码
   * @returns 质量评估结果
   */
<span class="fstat-no" title="function not covered" >  assessQuality(</span>morpheme: any, language: LanguageCode): EuropeanQualityMetrics {
<span class="cstat-no" title="statement not covered" >    if (!this.supportsLanguage(language)) {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`Unsupported European language: ${language}`)</span>
    }
&nbsp;
    const config = <span class="cstat-no" title="statement not covered" >this.configs.get(language)!</span>
<span class="cstat-no" title="statement not covered" >    return this.calculateEuropeanMetrics(morpheme, config)</span>
  }
&nbsp;
  /**
   * 评估欧洲语言语素质量 - 完整版本
   */
<span class="fstat-no" title="function not covered" >  assessQualityComplete(</span>
    morpheme: LanguageSpecificMorpheme,
    language: LanguageCode
  ): QualityScores &amp; { european_metrics: EuropeanQualityMetrics } {
    const config = <span class="cstat-no" title="statement not covered" >this.configs.get(language)</span>
<span class="cstat-no" title="statement not covered" >    if (!config) {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`Unsupported European language: ${language}`)</span>
    }
&nbsp;
    // 计算基础8维质量评分
    const baseScores = <span class="cstat-no" title="statement not covered" >this.calculateBaseQualityScores(morpheme)</span>
    
    // 计算欧洲语言特有指标
    const europeanMetrics = <span class="cstat-no" title="statement not covered" >this.calculateEuropeanMetrics(morpheme, config)</span>
    
    // 基于欧洲语言特性调整分数
    const adjustedScores = <span class="cstat-no" title="statement not covered" >this.adjustScoresForEuropeanFeatures(</span>
      baseScores,
      europeanMetrics,
      config
    )
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
      ...adjustedScores,
      european_metrics: europeanMetrics
    }
  }
&nbsp;
  /**
   * 计算基础8维质量评分
   */
  private <span class="fstat-no" title="function not covered" >calculateBaseQualityScores(</span>morpheme: LanguageSpecificMorpheme): QualityScores {
<span class="cstat-no" title="statement not covered" >    return {</span>
      naturalness: this.calculateNaturalness(morpheme),
      fluency: this.calculateFluency(morpheme),
      authenticity: this.calculateAuthenticity(morpheme),
      aesthetic_appeal: this.calculateAestheticAppeal(morpheme),
      pronunciation_ease: this.calculatePronunciationEase(morpheme),
      memorability: this.calculateMemorability(morpheme),
      uniqueness: this.calculateUniqueness(morpheme),
      practicality: this.calculatePracticality(morpheme)
    }
  }
&nbsp;
  /**
   * 计算欧洲语言特有指标
   */
  private <span class="fstat-no" title="function not covered" >calculateEuropeanMetrics(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): EuropeanQualityMetrics {
<span class="cstat-no" title="statement not covered" >    return {</span>
      phonetic_harmony: this.calculatePhoneticHarmony(morpheme, config),
      morphological_appropriateness: this.calculateMorphologicalAppropriateness(morpheme, config),
      cultural_elegance: this.calculateCulturalElegance(morpheme, config),
      linguistic_authenticity: this.calculateLinguisticAuthenticity(morpheme, config),
      international_adaptability: this.calculateInternationalAdaptability(morpheme, config),
      pronunciation_friendliness: this.calculatePronunciationFriendliness(morpheme, config)
    }
  }
&nbsp;
  /**
   * 基于欧洲语言特性调整分数
   */
  private <span class="fstat-no" title="function not covered" >adjustScoresForEuropeanFeatures(</span>
    baseScores: QualityScores,
    europeanMetrics: EuropeanQualityMetrics,
    config: EuropeanQualityConfig
  ): QualityScores {
    const culturalWeight = <span class="cstat-no" title="statement not covered" >0.3</span>
    const linguisticWeight = <span class="cstat-no" title="statement not covered" >0.7</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
      naturalness: this.adjustScore(
        baseScores.naturalness,
        europeanMetrics.linguistic_authenticity,
        linguisticWeight
      ),
      fluency: this.adjustScore(
        baseScores.fluency,
        europeanMetrics.phonetic_harmony,
        linguisticWeight
      ),
      authenticity: this.adjustScore(
        baseScores.authenticity,
        europeanMetrics.cultural_elegance,
        culturalWeight
      ),
      aesthetic_appeal: this.adjustScore(
        baseScores.aesthetic_appeal,
        (europeanMetrics.phonetic_harmony + europeanMetrics.cultural_elegance) / 2,
        0.5
      ),
      pronunciation_ease: this.adjustScore(
        baseScores.pronunciation_ease,
        europeanMetrics.pronunciation_friendliness,
        linguisticWeight
      ),
      memorability: this.adjustScore(
        baseScores.memorability,
        europeanMetrics.morphological_appropriateness,
        linguisticWeight
      ),
      uniqueness: this.adjustScore(
        baseScores.uniqueness,
        europeanMetrics.linguistic_authenticity,
        culturalWeight
      ),
      practicality: this.adjustScore(
        baseScores.practicality,
        europeanMetrics.international_adaptability,
        0.4
      )
    }
  }
&nbsp;
  /**
   * 初始化欧洲语言配置
   */
  private <span class="fstat-no" title="function not covered" >initializeConfigs(</span>): void {
    // 西班牙语配置
<span class="cstat-no" title="statement not covered" >    this.configs.set(LanguageCode.ES_ES, {</span>
      language: LanguageCode.ES_ES,
      cultural_weights: {
        formality: 0.75,
        elegance: 0.80,
        traditionality: 0.85,
        internationality: 0.90
      },
      linguistic_weights: {
        phonetic_beauty: 0.90,
        morphological_richness: 0.85,
        semantic_precision: 0.80,
        cultural_appropriateness: 0.85
      },
      quality_thresholds: {
        min_acceptable: 0.60,
        good_quality: 0.75,
        excellent_quality: 0.90
      }
    })
&nbsp;
    // 法语配置
<span class="cstat-no" title="statement not covered" >    this.configs.set(LanguageCode.FR_FR, {</span>
      language: LanguageCode.FR_FR,
      cultural_weights: {
        formality: 0.85,
        elegance: 0.95,
        traditionality: 0.90,
        internationality: 0.85
      },
      linguistic_weights: {
        phonetic_beauty: 0.95,
        morphological_richness: 0.90,
        semantic_precision: 0.85,
        cultural_appropriateness: 0.90
      },
      quality_thresholds: {
        min_acceptable: 0.65,
        good_quality: 0.80,
        excellent_quality: 0.92
      }
    })
&nbsp;
    // 德语配置
<span class="cstat-no" title="statement not covered" >    this.configs.set(LanguageCode.DE_DE, {</span>
      language: LanguageCode.DE_DE,
      cultural_weights: {
        formality: 0.90,
        elegance: 0.75,
        traditionality: 0.85,
        internationality: 0.80
      },
      linguistic_weights: {
        phonetic_beauty: 0.75,
        morphological_richness: 0.95,
        semantic_precision: 0.95,
        cultural_appropriateness: 0.85
      },
      quality_thresholds: {
        min_acceptable: 0.65,
        good_quality: 0.78,
        excellent_quality: 0.90
      }
    })
  }
&nbsp;
  // ============================================================================
  // 基础质量评分计算方法
  // ============================================================================
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateNaturalness(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
&nbsp;
    // 基于词长和常见模式计算自然度
    const lengthScore = <span class="cstat-no" title="statement not covered" >this.calculateLengthScore(text)</span>
    const patternScore = <span class="cstat-no" title="statement not covered" >this.calculatePatternScore(text)</span>
    const frequencyScore = <span class="cstat-no" title="statement not covered" >morpheme.usage_frequency || 0.5</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthScore + patternScore + frequencyScore) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateFluency(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
&nbsp;
    // 基于音韵流畅度计算
    const syllableFlow = <span class="cstat-no" title="statement not covered" >this.calculateSyllableFlow(text)</span>
    const consonantClusters = <span class="cstat-no" title="statement not covered" >this.penalizeConsonantClusters(text)</span>
    const vowelDistribution = <span class="cstat-no" title="statement not covered" >this.calculateVowelDistribution(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.max(0, (syllableFlow + vowelDistribution - consonantClusters) / 2)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateAuthenticity(</span>morpheme: LanguageSpecificMorpheme): number {
    const culturalContext = <span class="cstat-no" title="statement not covered" >morpheme.cultural_context</span>
<span class="cstat-no" title="statement not covered" >    if (!culturalContext) <span class="cstat-no" title="statement not covered" >return 0.5</span></span>
&nbsp;
    // 基于文化语境计算真实性
    const traditionalityScore = <span class="cstat-no" title="statement not covered" >culturalContext.traditionality || 0.5</span>
    const formalityScore = <span class="cstat-no" title="statement not covered" >culturalContext.formality || 0.5</span>
    const regionalityScore = <span class="cstat-no" title="statement not covered" >culturalContext.regionality || 0.5</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (traditionalityScore + formalityScore + regionalityScore) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateAestheticAppeal(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
    const culturalContext = <span class="cstat-no" title="statement not covered" >morpheme.cultural_context</span>
&nbsp;
    // 基于美学价值计算
    const phoneticBeauty = <span class="cstat-no" title="statement not covered" >this.calculatePhoneticBeauty(text)</span>
    const visualAppeal = <span class="cstat-no" title="statement not covered" >this.calculateVisualAppeal(text)</span>
    const culturalAesthetics = <span class="cstat-no" title="statement not covered" >culturalContext?.aesthetic_value || 0.5</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (phoneticBeauty + visualAppeal + culturalAesthetics) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePronunciationEase(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
    const phoneticFeatures = <span class="cstat-no" title="statement not covered" >morpheme.phonetic_features</span>
&nbsp;
    // 基于发音难度计算
    const syllableComplexity = <span class="cstat-no" title="statement not covered" >this.calculateSyllableComplexity(text)</span>
    const consonantDifficulty = <span class="cstat-no" title="statement not covered" >this.calculateConsonantDifficulty(text)</span>
    const stressPattern = <span class="cstat-no" title="statement not covered" >phoneticFeatures?.stress_position ? 0.8 : 0.6</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.max(0, 1 - (syllableComplexity + consonantDifficulty) / 2 + stressPattern * 0.2)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateMemorability(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
&nbsp;
    // 基于记忆友好度计算
    const lengthScore = <span class="cstat-no" title="statement not covered" >this.calculateMemoryLengthScore(text)</span>
    const rhythmScore = <span class="cstat-no" title="statement not covered" >this.calculateRhythmScore(text)</span>
    const uniquenessScore = <span class="cstat-no" title="statement not covered" >this.calculatePatternUniqueness(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthScore + rhythmScore + uniquenessScore) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateUniqueness(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
&nbsp;
    // 基于独特性计算
    const rarityScore = <span class="cstat-no" title="statement not covered" >1 - (morpheme.usage_frequency || 0.5)</span>
    const structuralUniqueness = <span class="cstat-no" title="statement not covered" >this.calculateStructuralUniqueness(text)</span>
    const semanticRarity = <span class="cstat-no" title="statement not covered" >morpheme.semantic_rarity || 0.5</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (rarityScore + structuralUniqueness + semanticRarity) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePracticality(</span>morpheme: LanguageSpecificMorpheme): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
&nbsp;
    // 基于实用性计算
    const usabilityScore = <span class="cstat-no" title="statement not covered" >this.calculateUsabilityScore(text)</span>
    const internationalScore = <span class="cstat-no" title="statement not covered" >this.calculateInternationalScore(text)</span>
    const accessibilityScore = <span class="cstat-no" title="statement not covered" >this.calculateAccessibilityScore(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (usabilityScore + internationalScore + accessibilityScore) / 3</span>
  }
&nbsp;
  // ============================================================================
  // 欧洲语言特有指标计算方法
  // ============================================================================
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePhoneticHarmony(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
    const phoneticWeight = <span class="cstat-no" title="statement not covered" >config.linguistic_weights.phonetic_beauty</span>
&nbsp;
    // 计算音韵和谐度
    const vowelHarmony = <span class="cstat-no" title="statement not covered" >this.calculateVowelHarmony(text)</span>
    const consonantBalance = <span class="cstat-no" title="statement not covered" >this.calculateConsonantBalance(text)</span>
    const rhythmicFlow = <span class="cstat-no" title="statement not covered" >this.calculateRhythmicFlow(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return ((vowelHarmony + consonantBalance + rhythmicFlow) / 3) * phoneticWeight</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateMorphologicalAppropriateness(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const morphInfo = <span class="cstat-no" title="statement not covered" >morpheme.morphological_info</span>
    const morphWeight = <span class="cstat-no" title="statement not covered" >config.linguistic_weights.morphological_richness</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!morphInfo) <span class="cstat-no" title="statement not covered" >return 0.5 * morphWeight</span></span>
&nbsp;
    // 评估形态学适宜性
    const structureScore = <span class="cstat-no" title="statement not covered" >this.evaluateMorphologicalStructure(morphInfo)</span>
    const complexityScore = <span class="cstat-no" title="statement not covered" >this.evaluateComplexityAppropriateness(morphInfo)</span>
    const authenticityScore = <span class="cstat-no" title="statement not covered" >this.evaluateMorphologicalAuthenticity(morphInfo)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return ((structureScore + complexityScore + authenticityScore) / 3) * morphWeight</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateCulturalElegance(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const culturalContext = <span class="cstat-no" title="statement not covered" >morpheme.cultural_context</span>
    const eleganceWeight = <span class="cstat-no" title="statement not covered" >config.cultural_weights.elegance</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!culturalContext) <span class="cstat-no" title="statement not covered" >return 0.5 * eleganceWeight</span></span>
&nbsp;
    // 计算文化优雅度
    const formalityElegance = <span class="cstat-no" title="statement not covered" >this.calculateFormalityElegance(culturalContext)</span>
    const traditionalElegance = <span class="cstat-no" title="statement not covered" >this.calculateTraditionalElegance(culturalContext)</span>
    const aestheticElegance = <span class="cstat-no" title="statement not covered" >culturalContext.aesthetic_value || 0.5</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return ((formalityElegance + traditionalElegance + aestheticElegance) / 3) * eleganceWeight</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateLinguisticAuthenticity(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const authWeight = <span class="cstat-no" title="statement not covered" >config.linguistic_weights.cultural_appropriateness</span>
&nbsp;
    // 计算语言纯正度
    const nativeScore = <span class="cstat-no" title="statement not covered" >this.calculateNativeScore(morpheme)</span>
    const historicalScore = <span class="cstat-no" title="statement not covered" >this.calculateHistoricalScore(morpheme)</span>
    const usageScore = <span class="cstat-no" title="statement not covered" >morpheme.usage_frequency || 0.5</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return ((nativeScore + historicalScore + usageScore) / 3) * authWeight</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateInternationalAdaptability(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
    const intlWeight = <span class="cstat-no" title="statement not covered" >config.cultural_weights.internationality</span>
&nbsp;
    // 计算国际化适应性
    const latinScore = <span class="cstat-no" title="statement not covered" >this.calculateLatinCompatibility(text)</span>
    const pronunciationScore = <span class="cstat-no" title="statement not covered" >this.calculateInternationalPronunciation(text)</span>
    const recognitionScore = <span class="cstat-no" title="statement not covered" >this.calculateInternationalRecognition(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return ((latinScore + pronunciationScore + recognitionScore) / 3) * intlWeight</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePronunciationFriendliness(</span>
    morpheme: LanguageSpecificMorpheme,
    config: EuropeanQualityConfig
  ): number {
    const text = <span class="cstat-no" title="statement not covered" >morpheme.text || ''</span>
    const phoneticFeatures = <span class="cstat-no" title="statement not covered" >morpheme.phonetic_features</span>
&nbsp;
    // 计算发音友好度
    const simplicityScore = <span class="cstat-no" title="statement not covered" >this.calculatePronunciationSimplicity(text)</span>
    const clarityScore = <span class="cstat-no" title="statement not covered" >this.calculatePronunciationClarity(text, phoneticFeatures)</span>
    const learnabilityScore = <span class="cstat-no" title="statement not covered" >this.calculatePronunciationLearnability(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (simplicityScore + clarityScore + learnabilityScore) / 3</span>
  }
&nbsp;
  // ============================================================================
  // 辅助计算方法
  // ============================================================================
&nbsp;
  private <span class="fstat-no" title="function not covered" >adjustScore(</span>baseScore: number, modifier: number, weight: number): number {
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, Math.max(0.0, baseScore + (modifier - 0.5) * weight))</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateLengthScore(</span>text: string): number {
    // 理想长度为4-8个字符
    const length = <span class="cstat-no" title="statement not covered" >text.length</span>
<span class="cstat-no" title="statement not covered" >    if (length &gt;= 4 &amp;&amp; length &lt;= 8) <span class="cstat-no" title="statement not covered" >return 1.0</span></span>
<span class="cstat-no" title="statement not covered" >    if (length &gt;= 3 &amp;&amp; length &lt;= 10) <span class="cstat-no" title="statement not covered" >return 0.8</span></span>
<span class="cstat-no" title="statement not covered" >    if (length &gt;= 2 &amp;&amp; length &lt;= 12) <span class="cstat-no" title="statement not covered" >return 0.6</span></span>
<span class="cstat-no" title="statement not covered" >    return 0.4</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePatternScore(</span>text: string): number {
    // 基于常见语言模式评分
    const hasVowels = <span class="cstat-no" title="statement not covered" >/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/i.test(text)</span>
    const hasConsonants = <span class="cstat-no" title="statement not covered" >/[bcdfghjklmnpqrstvwxyzñç]/i.test(text)</span>
    const balancedPattern = <span class="cstat-no" title="statement not covered" >hasVowels &amp;&amp; hasConsonants</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return balancedPattern ? 0.9 : 0.5</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateSyllableFlow(</span>text: string): number {
    // 简化的音节流畅度计算
    const vowels = <span class="cstat-no" title="statement not covered" >text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []</span>
    const consonants = <span class="cstat-no" title="statement not covered" >text.match(/[bcdfghjklmnpqrstvwxyzñç]/gi) || []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (vowels.length === 0) <span class="cstat-no" title="statement not covered" >return 0.2</span></span>
&nbsp;
    const ratio = <span class="cstat-no" title="statement not covered" >consonants.length / vowels.length</span>
    // 理想的辅音/元音比例约为1.2-1.8
<span class="cstat-no" title="statement not covered" >    return ratio &gt;= 1.2 &amp;&amp; ratio &lt;= 1.8 ? 1.0 : Math.max(0.3, 1 - Math.abs(ratio - 1.5) / 2)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >penalizeConsonantClusters(</span>text: string): number {
    // 惩罚过多的辅音聚集
    const clusters = <span class="cstat-no" title="statement not covered" >text.match(/[bcdfghjklmnpqrstvwxyzñç]{3,}/gi) || []</span>
<span class="cstat-no" title="statement not covered" >    return clusters.length * 0.2</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateVowelDistribution(</span>text: string): number {
    // 计算元音分布均匀度
    const vowelPositions: number[] = <span class="cstat-no" title="statement not covered" >[]</span>
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; text.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >      if (/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/i.test(text[i])) {</span>
<span class="cstat-no" title="statement not covered" >        vowelPositions.push(i)</span>
      }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (vowelPositions.length &lt;= 1) <span class="cstat-no" title="statement not covered" >return 0.5</span></span>
&nbsp;
    // 计算元音间距的标准差
    const intervals = <span class="cstat-no" title="statement not covered" >vowelPositions.slice(1).map(<span class="fstat-no" title="function not covered" >(p</span>os, i) =&gt; <span class="cstat-no" title="statement not covered" >pos - vowelPositions[i])</span></span>
    const avgInterval = <span class="cstat-no" title="statement not covered" >intervals.reduce(<span class="fstat-no" title="function not covered" >(s</span>um, interval) =&gt; <span class="cstat-no" title="statement not covered" >sum + interval,</span> 0) / intervals.length</span>
    const variance = <span class="cstat-no" title="statement not covered" >intervals.reduce(<span class="fstat-no" title="function not covered" >(s</span>um, interval) =&gt; <span class="cstat-no" title="statement not covered" >sum + Math.pow(interval - avgInterval, 2),</span> 0) / intervals.length</span>
&nbsp;
    // 标准差越小，分布越均匀
<span class="cstat-no" title="statement not covered" >    return Math.max(0.2, 1 - Math.sqrt(variance) / text.length)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePhoneticBeauty(</span>text: string): number {
    // 基于音韵美感的简化计算
    const vowelRatio = <span class="cstat-no" title="statement not covered" >(text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []).length / text.length</span>
    const rhythmScore = <span class="cstat-no" title="statement not covered" >this.calculateRhythmScore(text)</span>
    const euphonyScore = <span class="cstat-no" title="statement not covered" >this.calculateEuphonyScore(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (vowelRatio * 0.4 + rhythmScore * 0.3 + euphonyScore * 0.3)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateVisualAppeal(</span>text: string): number {
    // 基于视觉美感的简化计算
    const lengthAppeal = <span class="cstat-no" title="statement not covered" >this.calculateLengthScore(text)</span>
    const symmetryScore = <span class="cstat-no" title="statement not covered" >this.calculateSymmetryScore(text)</span>
    const uniquenessScore = <span class="cstat-no" title="statement not covered" >this.calculateVisualUniqueness(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthAppeal + symmetryScore + uniquenessScore) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateSyllableComplexity(</span>text: string): number {
    // 计算音节复杂度
    const complexClusters = <span class="cstat-no" title="statement not covered" >text.match(/[bcdfghjklmnpqrstvwxyzñç]{2,}/gi) || []</span>
    const diphthongs = <span class="cstat-no" title="statement not covered" >text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]{2,}/gi) || []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, (complexClusters.length * 0.3 + diphthongs.length * 0.2) / text.length)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateConsonantDifficulty(</span>text: string): number {
    // 计算辅音发音难度
    const difficultSounds = <span class="cstat-no" title="statement not covered" >text.match(/[xjñçß]/gi) || []</span>
    const clusters = <span class="cstat-no" title="statement not covered" >text.match(/[bcdfghjklmnpqrstvwxyzñç]{3,}/gi) || []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, (difficultSounds.length * 0.4 + clusters.length * 0.6) / text.length)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateMemoryLengthScore(</span>text: string): number {
    // 记忆友好的长度评分
    const length = <span class="cstat-no" title="statement not covered" >text.length</span>
<span class="cstat-no" title="statement not covered" >    if (length &gt;= 3 &amp;&amp; length &lt;= 7) <span class="cstat-no" title="statement not covered" >return 1.0</span></span>
<span class="cstat-no" title="statement not covered" >    if (length &gt;= 2 &amp;&amp; length &lt;= 9) <span class="cstat-no" title="statement not covered" >return 0.8</span></span>
<span class="cstat-no" title="statement not covered" >    return Math.max(0.3, 1 - Math.abs(length - 5) / 10)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateRhythmScore(</span>text: string): number {
    // 计算节奏感
    const syllablePattern = <span class="cstat-no" title="statement not covered" >this.extractSyllablePattern(text)</span>
    const regularityScore = <span class="cstat-no" title="statement not covered" >this.calculatePatternRegularity(syllablePattern)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return regularityScore</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePatternUniqueness(</span>text: string): number {
    // 计算模式独特性
    const repeatedChars = <span class="cstat-no" title="statement not covered" >text.match(/(.)\1+/g) || []</span>
    const uniqueChars = <span class="cstat-no" title="statement not covered" >new Set(text.toLowerCase()).size</span>
&nbsp;
    const repetitionPenalty = <span class="cstat-no" title="statement not covered" >repeatedChars.length * 0.2</span>
    const diversityBonus = <span class="cstat-no" title="statement not covered" >uniqueChars / text.length</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.max(0.2, diversityBonus - repetitionPenalty)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateStructuralUniqueness(</span>text: string): number {
    // 计算结构独特性
    const pattern = <span class="cstat-no" title="statement not covered" >this.extractStructuralPattern(text)</span>
    const commonPatterns = <span class="cstat-no" title="statement not covered" >['cvcv', 'vcvc', 'ccvc', 'cvcc']</span>
&nbsp;
    const isCommon = <span class="cstat-no" title="statement not covered" >commonPatterns.some(<span class="fstat-no" title="function not covered" >p </span>=&gt; <span class="cstat-no" title="statement not covered" >pattern.includes(p))</span></span>
<span class="cstat-no" title="statement not covered" >    return isCommon ? 0.4 : 0.8</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateUsabilityScore(</span>text: string): number {
    // 计算可用性评分
    const lengthScore = <span class="cstat-no" title="statement not covered" >this.calculateLengthScore(text)</span>
    const typingScore = <span class="cstat-no" title="statement not covered" >this.calculateTypingEase(text)</span>
    const readabilityScore = <span class="cstat-no" title="statement not covered" >this.calculateReadabilityScore(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthScore + typingScore + readabilityScore) / 3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateInternationalScore(</span>text: string): number {
    // 计算国际化评分
    const latinOnly = <span class="cstat-no" title="statement not covered" >/^[a-zA-Záéíóúàâäéèêëïîôöùûüäöüñç]+$/.test(text)</span>
    const commonChars = <span class="cstat-no" title="statement not covered" >/^[a-zA-Z]+$/.test(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (commonChars) <span class="cstat-no" title="statement not covered" >return 1.0</span></span>
<span class="cstat-no" title="statement not covered" >    if (latinOnly) <span class="cstat-no" title="statement not covered" >return 0.8</span></span>
<span class="cstat-no" title="statement not covered" >    return 0.5</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateAccessibilityScore(</span>text: string): number {
    // 计算可访问性评分
    const pronunciationScore = <span class="cstat-no" title="statement not covered" >1 - this.calculatePronunciationDifficulty(text)</span>
    const visualScore = <span class="cstat-no" title="statement not covered" >this.calculateVisualClarity(text)</span>
    const memoryScore = <span class="cstat-no" title="statement not covered" >this.calculateMemoryFriendliness(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (pronunciationScore + visualScore + memoryScore) / 3</span>
  }
&nbsp;
  // ============================================================================
  // 更多辅助方法（简化实现）
  // ============================================================================
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateEuphonyScore(</span>text: string): number {
    // 简化的音韵和谐度计算
    const pleasantSounds = <span class="cstat-no" title="statement not covered" >text.match(/[aeioulmnr]/gi) || []</span>
    const harshSounds = <span class="cstat-no" title="statement not covered" >text.match(/[kgxqz]/gi) || []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.max(0.2, (pleasantSounds.length - harshSounds.length * 0.5) / text.length)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateSymmetryScore(</span>text: string): number {
    // 简化的对称性计算
    const reversed = <span class="cstat-no" title="statement not covered" >text.split('').reverse().join('')</span>
    let matches = <span class="cstat-no" title="statement not covered" >0</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; Math.min(text.length, 3); i++) {</span>
<span class="cstat-no" title="statement not covered" >      if (text[i] === reversed[i]) <span class="cstat-no" title="statement not covered" >matches++</span></span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    return matches / Math.min(text.length, 3)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateVisualUniqueness(</span>text: string): number {
    // 视觉独特性
    const specialChars = <span class="cstat-no" title="statement not covered" >text.match(/[áéíóúàâäéèêëïîôöùûüäöüñç]/gi) || []</span>
    const uniqueRatio = <span class="cstat-no" title="statement not covered" >new Set(text.toLowerCase()).size / text.length</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, uniqueRatio + specialChars.length * 0.1)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >extractSyllablePattern(</span>text: string): string {
    // 提取音节模式（简化）
<span class="cstat-no" title="statement not covered" >    return text.replace(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi, 'V')</span>
               .replace(/[bcdfghjklmnpqrstvwxyzñç]/gi, 'C')
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePatternRegularity(</span>pattern: string): number {
    // 计算模式规律性
    const chunks = <span class="cstat-no" title="statement not covered" >pattern.match(/.{1,2}/g) || []</span>
    const uniqueChunks = <span class="cstat-no" title="statement not covered" >new Set(chunks).size</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return uniqueChunks &lt; chunks.length ? 0.8 : 0.5</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >extractStructuralPattern(</span>text: string): string {
    // 提取结构模式
<span class="cstat-no" title="statement not covered" >    return this.extractSyllablePattern(text).toLowerCase()</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateTypingEase(</span>text: string): number {
    // 打字便利性（简化）
    const commonKeys = <span class="cstat-no" title="statement not covered" >text.match(/[aeioustnrlhd]/gi) || []</span>
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, commonKeys.length / text.length + 0.2)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateReadabilityScore(</span>text: string): number {
    // 可读性评分
    const lengthScore = <span class="cstat-no" title="statement not covered" >this.calculateLengthScore(text)</span>
    const clarityScore = <span class="cstat-no" title="statement not covered" >/^[a-zA-Záéíóúàâäéèêëïîôöùûüäöüñç]+$/.test(text) ? 1.0 : 0.7</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthScore + clarityScore) / 2</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePronunciationDifficulty(</span>text: string): number {
    // 发音难度
    const difficultCombos = <span class="cstat-no" title="statement not covered" >text.match(/[xjqz]|ch|sch|gn|ñ/gi) || []</span>
    const clusters = <span class="cstat-no" title="statement not covered" >text.match(/[bcdfghjklmnpqrstvwxyzñç]{3,}/gi) || []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, (difficultCombos.length + clusters.length * 2) / text.length)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateVisualClarity(</span>text: string): number {
    // 视觉清晰度
    const confusableChars = <span class="cstat-no" title="statement not covered" >text.match(/[il1o0]/gi) || []</span>
<span class="cstat-no" title="statement not covered" >    return Math.max(0.3, 1 - confusableChars.length / text.length)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateMemoryFriendliness(</span>text: string): number {
    // 记忆友好度
    const lengthScore = <span class="cstat-no" title="statement not covered" >this.calculateMemoryLengthScore(text)</span>
    const patternScore = <span class="cstat-no" title="statement not covered" >this.calculatePatternMemorability(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthScore + patternScore) / 2</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePatternMemorability(</span>text: string): number {
    // 模式记忆度
    const repeatingElements = <span class="cstat-no" title="statement not covered" >text.match(/(.)\1/g) || []</span>
    const rhythmicPattern = <span class="cstat-no" title="statement not covered" >this.hasRhythmicPattern(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, repeatingElements.length * 0.3 + (rhythmicPattern ? 0.4 : 0))</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >hasRhythmicPattern(</span>text: string): boolean {
    // 检查是否有节奏模式
    const pattern = <span class="cstat-no" title="statement not covered" >this.extractSyllablePattern(text)</span>
<span class="cstat-no" title="statement not covered" >    return /^(CV)+$|^(VC)+$|^(CVC)+$/.test(pattern)</span>
  }
&nbsp;
  // 欧洲语言特有指标的简化实现
  private <span class="fstat-no" title="function not covered" >calculateVowelHarmony(</span>text: string): number {
    const vowels = <span class="cstat-no" title="statement not covered" >text.match(/[aeiouáéíóúàâäéèêëïîôöùûüäöü]/gi) || []</span>
    const frontVowels = <span class="cstat-no" title="statement not covered" >vowels.filter(<span class="fstat-no" title="function not covered" >v </span>=&gt; <span class="cstat-no" title="statement not covered" >/[eéèêëiíîï]/i.test(v))</span>.length</span>
    const backVowels = <span class="cstat-no" title="statement not covered" >vowels.filter(<span class="fstat-no" title="function not covered" >v </span>=&gt; <span class="cstat-no" title="statement not covered" >/[oóôöuúûü]/i.test(v))</span>.length</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (vowels.length === 0) <span class="cstat-no" title="statement not covered" >return 0.5</span></span>
&nbsp;
    const harmony = <span class="cstat-no" title="statement not covered" >Math.abs(frontVowels - backVowels) / vowels.length</span>
<span class="cstat-no" title="statement not covered" >    return Math.max(0.3, 1 - harmony)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateConsonantBalance(</span>text: string): number {
    const consonants = <span class="cstat-no" title="statement not covered" >text.match(/[bcdfghjklmnpqrstvwxyzñç]/gi) || []</span>
    const stops = <span class="cstat-no" title="statement not covered" >consonants.filter(<span class="fstat-no" title="function not covered" >c </span>=&gt; <span class="cstat-no" title="statement not covered" >/[pbtdkgqc]/i.test(c))</span>.length</span>
    const fricatives = <span class="cstat-no" title="statement not covered" >consonants.filter(<span class="fstat-no" title="function not covered" >c </span>=&gt; <span class="cstat-no" title="statement not covered" >/[fvszxjh]/i.test(c))</span>.length</span>
    const liquids = <span class="cstat-no" title="statement not covered" >consonants.filter(<span class="fstat-no" title="function not covered" >c </span>=&gt; <span class="cstat-no" title="statement not covered" >/[lr]/i.test(c))</span>.length</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (consonants.length === 0) <span class="cstat-no" title="statement not covered" >return 0.5</span></span>
&nbsp;
    const balance = <span class="cstat-no" title="statement not covered" >(stops + fricatives + liquids) / consonants.length</span>
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, balance)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateRhythmicFlow(</span>text: string): number {
    const pattern = <span class="cstat-no" title="statement not covered" >this.extractSyllablePattern(text)</span>
    const alternating = <span class="cstat-no" title="statement not covered" >/^(CV)+$|^(VC)+$/.test(pattern)</span>
    const balanced = <span class="cstat-no" title="statement not covered" >this.calculateSyllableFlow(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return alternating ? Math.min(1.0, balanced + 0.2) : balanced</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >evaluateMorphologicalStructure(</span>morphInfo: any): number {
    // 简化的形态学结构评估
    const hasRoot = <span class="cstat-no" title="statement not covered" >morphInfo.root &amp;&amp; morphInfo.root.length &gt; 0</span>
    const hasPrefixes = <span class="cstat-no" title="statement not covered" >morphInfo.prefixes &amp;&amp; morphInfo.prefixes.length &gt; 0</span>
    const hasSuffixes = <span class="cstat-no" title="statement not covered" >morphInfo.suffixes &amp;&amp; morphInfo.suffixes.length &gt; 0</span>
&nbsp;
    let score = <span class="cstat-no" title="statement not covered" >hasRoot ? 0.6 : 0.3</span>
<span class="cstat-no" title="statement not covered" >    if (hasPrefixes) <span class="cstat-no" title="statement not covered" >score += 0.2</span></span>
<span class="cstat-no" title="statement not covered" >    if (hasSuffixes) <span class="cstat-no" title="statement not covered" >score += 0.2</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, score)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >evaluateComplexityAppropriateness(</span>morphInfo: any): number {
    // 复杂度适宜性评估
    const totalAffixes = <span class="cstat-no" title="statement not covered" >(morphInfo.prefixes?.length || 0) + (morphInfo.suffixes?.length || 0)</span>
&nbsp;
    // 适中的复杂度最佳
<span class="cstat-no" title="statement not covered" >    if (totalAffixes === 1 || totalAffixes === 2) <span class="cstat-no" title="statement not covered" >return 1.0</span></span>
<span class="cstat-no" title="statement not covered" >    if (totalAffixes === 0 || totalAffixes === 3) <span class="cstat-no" title="statement not covered" >return 0.7</span></span>
<span class="cstat-no" title="statement not covered" >    return 0.4</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >evaluateMorphologicalAuthenticity(</span>morphInfo: any): number {
    // 形态学真实性评估
    const hasInflection = <span class="cstat-no" title="statement not covered" >morphInfo.inflection_info &amp;&amp; Object.keys(morphInfo.inflection_info).length &gt; 0</span>
    const hasDerivation = <span class="cstat-no" title="statement not covered" >morphInfo.derivation_info &amp;&amp; Object.keys(morphInfo.derivation_info).length &gt; 0</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return hasInflection || hasDerivation ? 0.8 : 0.5</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateFormalityElegance(</span>culturalContext: any): number {
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, (culturalContext.formality || 0.5) * 1.2)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateTraditionalElegance(</span>culturalContext: any): number {
<span class="cstat-no" title="statement not covered" >    return Math.min(1.0, (culturalContext.traditionality || 0.5) * 1.1)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateNativeScore(</span>morpheme: LanguageSpecificMorpheme): number {
    // 本土化程度评分
    const culturalContext = <span class="cstat-no" title="statement not covered" >morpheme.cultural_context</span>
<span class="cstat-no" title="statement not covered" >    return culturalContext?.regionality || 0.5</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateHistoricalScore(</span>morpheme: LanguageSpecificMorpheme): number {
    // 历史传承评分
    const culturalContext = <span class="cstat-no" title="statement not covered" >morpheme.cultural_context</span>
<span class="cstat-no" title="statement not covered" >    return culturalContext?.traditionality || 0.5</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateLatinCompatibility(</span>text: string): number {
    // 拉丁字母兼容性
<span class="cstat-no" title="statement not covered" >    return /^[a-zA-Záéíóúàâäéèêëïîôöùûüäöüñç]+$/.test(text) ? 1.0 : 0.3</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateInternationalPronunciation(</span>text: string): number {
    // 国际发音友好度
    const difficulty = <span class="cstat-no" title="statement not covered" >this.calculatePronunciationDifficulty(text)</span>
<span class="cstat-no" title="statement not covered" >    return 1 - difficulty</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculateInternationalRecognition(</span>text: string): number {
    // 国际认知度
    const commonChars = <span class="cstat-no" title="statement not covered" >/^[a-zA-Z]+$/.test(text)</span>
    const length = <span class="cstat-no" title="statement not covered" >text.length</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return commonChars &amp;&amp; length &gt;= 3 &amp;&amp; length &lt;= 8 ? 0.9 : 0.6</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePronunciationSimplicity(</span>text: string): number {
<span class="cstat-no" title="statement not covered" >    return 1 - this.calculateSyllableComplexity(text)</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePronunciationClarity(</span>text: string, phoneticFeatures: any): number {
    const clarityScore = <span class="cstat-no" title="statement not covered" >this.calculateVisualClarity(text)</span>
    const stressClarity = <span class="cstat-no" title="statement not covered" >phoneticFeatures?.stress_position ? 0.8 : 0.6</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (clarityScore + stressClarity) / 2</span>
  }
&nbsp;
  private <span class="fstat-no" title="function not covered" >calculatePronunciationLearnability(</span>text: string): number {
    const lengthScore = <span class="cstat-no" title="statement not covered" >this.calculateMemoryLengthScore(text)</span>
    const patternScore = <span class="cstat-no" title="statement not covered" >this.calculatePatternScore(text)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (lengthScore + patternScore) / 2</span>
  }
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-25T14:55:38.011Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    