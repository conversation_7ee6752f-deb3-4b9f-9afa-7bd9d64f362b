/**
 * 应用常量配置
 *
 * 包含API配置、生成参数、验证规则、缓存设置等全局常量
 *
 * @fileoverview 应用常量配置文件
 * @version 1.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */

import { CulturalContext } from "../types/core"

export const STYLE_PREFERENCES = ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional'] as const

// API 相关常量
export const API = {
  // API 版本
  VERSION: '1.0.0-mvp',
  // 请求ID前缀
  REQUEST_ID_PREFIX: 'req_',
  // 默认语言
  DEFAULT_LANGUAGE: 'zh-CN'
} as const

// 生成相关常量
export const GENERATION = {
  // 每次请求最大生成数量
  MAX_GENERATE_COUNT: 3,
  // 默认生成数量
  DEFAULT_GENERATE_COUNT: 1,
  // 默认创意程度
  DEFAULT_CREATIVITY_LEVEL: 0.7,
  // 默认质量阈值
  DEFAULT_QUALITY_THRESHOLD: 0.5,
  // 默认长度偏好
  DEFAULT_LENGTH_PREFERENCE: 'medium' as const,
  // 默认文化偏好
  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,
  // 默认风格偏好
  DEFAULT_STYLE_PREFERENCE: 'humorous' as const,
  // 默认最大重试次数
  MAX_RETRY_ATTEMPTS: 3,
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 10000
} as const

// 验证相关常量
export const VALIDATION = {
  // 生成数量范围
  COUNT: {
    MIN: 1,
    MAX: 20
  },
  // 创意程度范围
  CREATIVITY_LEVEL: {
    MIN: 0.1,
    MAX: 1.0
  },
  // 质量阈值范围
  QUALITY_THRESHOLD: {
    MIN: 0.1,
    MAX: 1.0
  },
  // 默认错误代码
  ERROR_CODES: {
    INVALID_PARAMETERS: 'E1001',
    MISSING_REQUIRED_FIELD: 'E1002',
    VALIDATION_FAILED: 'E1003',
    GENERATION_FAILED: 'E3001',
    INTERNAL_ERROR: 'E5001'
  },
  // 文化偏好选项
  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'] as const,
  // 风格偏好选项
  STYLE_PREFERENCES: STYLE_PREFERENCES,
  // 长度偏好选项
  LENGTH_PREFERENCES: ['short', 'medium', 'long'] as const
} as const

// 缓存相关常量
export const CACHE = {
  // 默认缓存时间（秒）
  DEFAULT_TTL: 3600,
  // 最大缓存条目数
  MAX_ITEMS: 1000,
  // 缓存键前缀
  KEY_PREFIX: 'namer:'
} as const

// 性能相关常量
export const PERFORMANCE = {
  // 慢查询阈值（毫秒）
  SLOW_QUERY_THRESHOLD: 500,
  // 监控采样率（0-1）
  MONITORING_SAMPLE_RATE: 0.1
} as const

// 错误消息
export const ERROR_MESSAGES = {
  VALIDATION: {
    INVALID_COUNT: (min: number, max: number) => `count must be a number between ${min} and ${max}`,
    INVALID_CREATIVITY_LEVEL: (min: number, max: number) => `creativity_level must be a number between ${min} and ${max}`,
    INVALID_QUALITY_THRESHOLD: (min: number, max: number) => `quality_threshold must be a number between ${min} and ${max}`,
    INVALID_CULTURAL_PREFERENCE: (validValues: readonly string[]) => `cultural_preference must be one of: ${validValues.join(', ')}`,
    INVALID_STYLE_PREFERENCE: (validValues: readonly string[]) => `style_preference must be one of: ${validValues.join(', ')}`,
    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array'
  },
  INTERNAL: {
    GENERATION_FAILED: 'Failed to generate usernames',
    VALIDATION_FAILED: 'Request validation failed',
    INTERNAL_ERROR: 'Internal server error'
  }
} as const
