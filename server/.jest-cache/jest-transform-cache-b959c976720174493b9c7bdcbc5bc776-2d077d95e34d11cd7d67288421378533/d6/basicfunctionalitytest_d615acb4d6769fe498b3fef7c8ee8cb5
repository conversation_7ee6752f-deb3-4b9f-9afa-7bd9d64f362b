ebdc33c45e60706d0552cd2598429bb0
"use strict";
/**
 * 基础功能测试
 *
 * 测试核心类型定义和基本功能的正确性
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const core_js_1 = require("../types/core.js");
const multilingual_js_1 = require("../types/multilingual.js");
(0, globals_1.describe)('基础功能测试', () => {
    (0, globals_1.describe)('类型定义测试', () => {
        (0, globals_1.it)('应该正确定义 CulturalContext 枚举', () => {
            (0, globals_1.expect)(core_js_1.CulturalContext.ANCIENT).toBe('ancient');
            (0, globals_1.expect)(core_js_1.CulturalContext.MODERN).toBe('modern');
            (0, globals_1.expect)(core_js_1.CulturalContext.NEUTRAL).toBe('neutral');
        });
        (0, globals_1.it)('应该正确定义 LanguageCode 枚举', () => {
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.ZH_CN).toBe('zh-CN');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.EN_US).toBe('en-US');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.JA_JP).toBe('ja-JP');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.KO_KR).toBe('ko-KR');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.ES_ES).toBe('es-ES');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.FR_FR).toBe('fr-FR');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.DE_DE).toBe('de-DE');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.AR_SA).toBe('ar-SA');
        });
        (0, globals_1.it)('应该正确定义 RegisterLevel 枚举', () => {
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.FORMAL).toBe('formal');
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.NEUTRAL).toBe('neutral');
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.INFORMAL).toBe('informal');
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.COLLOQUIAL).toBe('colloquial');
        });
        (0, globals_1.it)('应该正确定义语义向量维度常量', () => {
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBe(20);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBe(512);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(512);
        });
    });
    (0, globals_1.describe)('类型兼容性测试', () => {
        (0, globals_1.it)('应该支持枚举值的类型检查', () => {
            const culturalContexts = Object.values(core_js_1.CulturalContext);
            (0, globals_1.expect)(culturalContexts).toHaveLength(3);
            (0, globals_1.expect)(culturalContexts).toContain('ancient');
            (0, globals_1.expect)(culturalContexts).toContain('modern');
            (0, globals_1.expect)(culturalContexts).toContain('neutral');
        });
        (0, globals_1.it)('应该支持语言代码的类型检查', () => {
            const languageCodes = Object.values(multilingual_js_1.LanguageCode);
            (0, globals_1.expect)(languageCodes).toHaveLength(8);
            (0, globals_1.expect)(languageCodes).toContain('zh-CN');
            (0, globals_1.expect)(languageCodes).toContain('en-US');
        });
        (0, globals_1.it)('应该支持寄存器级别的类型检查', () => {
            const registerLevels = Object.values(multilingual_js_1.RegisterLevel);
            (0, globals_1.expect)(registerLevels).toHaveLength(4);
            (0, globals_1.expect)(registerLevels).toContain('formal');
            (0, globals_1.expect)(registerLevels).toContain('neutral');
            (0, globals_1.expect)(registerLevels).toContain('informal');
            (0, globals_1.expect)(registerLevels).toContain('colloquial');
        });
    });
    (0, globals_1.describe)('配置常量测试', () => {
        (0, globals_1.it)('应该正确配置语义向量维度', () => {
            // 测试向量维度配置的一致性
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeLessThan(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL);
            // 测试维度值的合理性
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeGreaterThan(0);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBeGreaterThan(0);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBeGreaterThan(0);
        });
        (0, globals_1.it)('应该支持向量维度的数学运算', () => {
            const legacyVector = new Array(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).fill(0.1);
            const multilingualVector = new Array(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).fill(0.1);
            (0, globals_1.expect)(legacyVector).toHaveLength(20);
            (0, globals_1.expect)(multilingualVector).toHaveLength(512);
            // 测试向量的基本数学属性
            (0, globals_1.expect)(legacyVector.every(v => v === 0.1)).toBe(true);
            (0, globals_1.expect)(multilingualVector.every(v => v === 0.1)).toBe(true);
        });
    });
    (0, globals_1.describe)('数据结构验证', () => {
        (0, globals_1.it)('应该支持多语言标签映射', () => {
            const multilingualLabels = {
                [multilingual_js_1.LanguageCode.ZH_CN]: '测试',
                [multilingual_js_1.LanguageCode.EN_US]: 'test',
                [multilingual_js_1.LanguageCode.JA_JP]: 'テスト'
            };
            (0, globals_1.expect)(multilingualLabels[multilingual_js_1.LanguageCode.ZH_CN]).toBe('测试');
            (0, globals_1.expect)(multilingualLabels[multilingual_js_1.LanguageCode.EN_US]).toBe('test');
            (0, globals_1.expect)(multilingualLabels[multilingual_js_1.LanguageCode.JA_JP]).toBe('テスト');
        });
        (0, globals_1.it)('应该支持文化适应性配置', () => {
            const culturalAdaptation = {
                traditionality: 0.6,
                modernity: 0.8,
                formality: 0.5,
                regionality: 0.7,
                religious_sensitivity: 0.3,
                age_appropriateness: ['adult', 'teen'],
                cultural_tags: ['positive', 'modern']
            };
            // 验证数值范围
            (0, globals_1.expect)(culturalAdaptation.traditionality).toBeGreaterThanOrEqual(0);
            (0, globals_1.expect)(culturalAdaptation.traditionality).toBeLessThanOrEqual(1);
            (0, globals_1.expect)(culturalAdaptation.modernity).toBeGreaterThanOrEqual(0);
            (0, globals_1.expect)(culturalAdaptation.modernity).toBeLessThanOrEqual(1);
            // 验证数组属性
            (0, globals_1.expect)(Array.isArray(culturalAdaptation.age_appropriateness)).toBe(true);
            (0, globals_1.expect)(Array.isArray(culturalAdaptation.cultural_tags)).toBe(true);
        });
        (0, globals_1.it)('应该支持语音特征配置', () => {
            const phoneticFeatures = {
                ipa_transcription: '/test/',
                syllable_count: 1,
                tone_pattern: ['1'],
                phonetic_harmony: 0.8
            };
            (0, globals_1.expect)(phoneticFeatures.ipa_transcription).toMatch(/^\/.*\/$/);
            (0, globals_1.expect)(phoneticFeatures.syllable_count).toBeGreaterThan(0);
            (0, globals_1.expect)(Array.isArray(phoneticFeatures.tone_pattern)).toBe(true);
            (0, globals_1.expect)(phoneticFeatures.phonetic_harmony).toBeGreaterThanOrEqual(0);
            (0, globals_1.expect)(phoneticFeatures.phonetic_harmony).toBeLessThanOrEqual(1);
        });
    });
    (0, globals_1.describe)('质量评估系统测试', () => {
        (0, globals_1.it)('应该支持8维度质量评分', () => {
            const qualityDimensions = {
                creativity: 0.9,
                memorability: 0.8,
                cultural_fit: 0.9,
                aesthetic_appeal: 0.8,
                pronunciation_ease: 0.8,
                uniqueness: 0.7,
                practicality: 0.8,
                semantic_richness: 0.8
            };
            // 验证所有维度都在有效范围内
            Object.values(qualityDimensions).forEach(score => {
                (0, globals_1.expect)(score).toBeGreaterThanOrEqual(0);
                (0, globals_1.expect)(score).toBeLessThanOrEqual(1);
            });
            // 验证维度数量
            (0, globals_1.expect)(Object.keys(qualityDimensions)).toHaveLength(8);
        });
        (0, globals_1.it)('应该计算平均质量评分', () => {
            const dimensions = {
                creativity: 0.9,
                memorability: 0.8,
                cultural_fit: 0.9,
                aesthetic_appeal: 0.8,
                pronunciation_ease: 0.8,
                uniqueness: 0.7,
                practicality: 0.8,
                semantic_richness: 0.8
            };
            const average = Object.values(dimensions).reduce((sum, score) => sum + score, 0) / 8;
            (0, globals_1.expect)(average).toBeCloseTo(0.8125, 4);
            (0, globals_1.expect)(average).toBeGreaterThan(0.7);
            (0, globals_1.expect)(average).toBeLessThan(0.9);
        });
    });
    (0, globals_1.describe)('错误处理测试', () => {
        (0, globals_1.it)('应该处理无效的枚举值', () => {
            // TypeScript 编译时会捕获这些错误，但我们可以测试运行时行为
            const validCulturalContext = core_js_1.CulturalContext.MODERN;
            (0, globals_1.expect)(Object.values(core_js_1.CulturalContext)).toContain(validCulturalContext);
        });
        (0, globals_1.it)('应该处理边界值', () => {
            // 测试质量评分的边界值
            const minScore = 0;
            const maxScore = 1;
            (0, globals_1.expect)(minScore).toBeGreaterThanOrEqual(0);
            (0, globals_1.expect)(minScore).toBeLessThanOrEqual(1);
            (0, globals_1.expect)(maxScore).toBeGreaterThanOrEqual(0);
            (0, globals_1.expect)(maxScore).toBeLessThanOrEqual(1);
        });
        (0, globals_1.it)('应该处理空数组和空对象', () => {
            const emptyArray = [];
            const emptyObject = {};
            (0, globals_1.expect)(Array.isArray(emptyArray)).toBe(true);
            (0, globals_1.expect)(emptyArray).toHaveLength(0);
            (0, globals_1.expect)(typeof emptyObject).toBe('object');
            (0, globals_1.expect)(Object.keys(emptyObject)).toHaveLength(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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