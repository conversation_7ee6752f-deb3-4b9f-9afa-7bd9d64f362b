5cb41c1982080320d2cb2279136735f2
/* istanbul ignore next */
function cov_1ym1naerw() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts";
  var hash = "ea9515dd4fa7174959481946f4e1647a84119c6e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 34
        },
        end: {
          line: 2,
          column: 70
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts",
      mappings: "AAAA,mBAAmB;AACnB,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAU,CAAA",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/common.ts"],
      sourcesContent: ["// \u652F\u6301\u7684\u7248\u672C\u5E38\u91CF (v3.0\u66F4\u65B0)\nexport const SUPPORTED_VERSIONS = ['1.0.0', '1.1.0', '2.0.0', '3.0.0'] as const\n\n// \u4ECE\u5E38\u91CF\u63A8\u5BFC\u51FA\u7248\u672C\u7C7B\u578B\nexport type MorphemeVersion = typeof SUPPORTED_VERSIONS[number]\n\n\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ea9515dd4fa7174959481946f4e1647a84119c6e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ym1naerw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ym1naerw();
// 支持的版本常量 (v3.0更新)
export const SUPPORTED_VERSIONS =
/* istanbul ignore next */
(cov_1ym1naerw().s[0]++, ['1.0.0', '1.1.0', '2.0.0', '3.0.0']);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTVVBQT1JURURfVkVSU0lPTlMiLCJjb3ZfMXltMW5hZXJ3IiwicyJdLCJzb3VyY2VzIjpbIi9ob21lL3AvZGV2ZWxvcC93b3Jrc3BhY2UvbmFtZXItdjYvc2VydmVyL3R5cGVzL2NvbW1vbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDmlK/mjIHnmoTniYjmnKzluLjph48gKHYzLjDmm7TmlrApXG5leHBvcnQgY29uc3QgU1VQUE9SVEVEX1ZFUlNJT05TID0gWycxLjAuMCcsICcxLjEuMCcsICcyLjAuMCcsICczLjAuMCddIGFzIGNvbnN0XG5cbi8vIOS7juW4uOmHj+aOqOWvvOWHuueJiOacrOexu+Wei1xuZXhwb3J0IHR5cGUgTW9ycGhlbWVWZXJzaW9uID0gdHlwZW9mIFNVUFBPUlRFRF9WRVJTSU9OU1tudW1iZXJdXG5cblxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQSxPQUFPLE1BQU1BLGtCQUFrQjtBQUFBO0FBQUEsQ0FBQUMsYUFBQSxHQUFBQyxDQUFBLE9BQUcsQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLE9BQU8sRUFBRSxPQUFPLENBQVUiLCJpZ25vcmVMaXN0IjpbXX0=