cdd6df91ba077a37b81f849411eef527
/* istanbul ignore next */
function cov_6si821j0y() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/LanguageManager.ts";
  var hash = "1541e12704b7a95716cc18501780212987cc03df";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/LanguageManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 15,
          column: 19
        },
        end: {
          line: 15,
          column: 49
        }
      },
      "1": {
        start: {
          line: 16,
          column: 18
        },
        end: {
          line: 16,
          column: 42
        }
      },
      "2": {
        start: {
          line: 22,
          column: 17
        },
        end: {
          line: 22,
          column: 51
        }
      },
      "3": {
        start: {
          line: 24,
          column: 28
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "4": {
        start: {
          line: 85,
          column: 23
        },
        end: {
          line: 85,
          column: 32
        }
      },
      "5": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 29
        }
      },
      "6": {
        start: {
          line: 87,
          column: 20
        },
        end: {
          line: 87,
          column: 25
        }
      },
      "7": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 97,
          column: 10
        }
      },
      "8": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 106,
          column: 9
        }
      },
      "9": {
        start: {
          line: 104,
          column: 12
        },
        end: {
          line: 104,
          column: 48
        }
      },
      "10": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 105,
          column: 19
        }
      },
      "11": {
        start: {
          line: 107,
          column: 26
        },
        end: {
          line: 107,
          column: 36
        }
      },
      "12": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 43
        }
      },
      "13": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "14": {
        start: {
          line: 111,
          column: 12
        },
        end: {
          line: 115,
          column: 13
        }
      },
      "15": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 114,
          column: 17
        }
      },
      "16": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 113,
          column: 58
        }
      },
      "17": {
        start: {
          line: 117,
          column: 12
        },
        end: {
          line: 117,
          column: 38
        }
      },
      "18": {
        start: {
          line: 118,
          column: 12
        },
        end: {
          line: 118,
          column: 38
        }
      },
      "19": {
        start: {
          line: 119,
          column: 29
        },
        end: {
          line: 119,
          column: 51
        }
      },
      "20": {
        start: {
          line: 120,
          column: 12
        },
        end: {
          line: 120,
          column: 100
        }
      },
      "21": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 44
        }
      },
      "22": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 124,
          column: 50
        }
      },
      "23": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 101
        }
      },
      "24": {
        start: {
          line: 132,
          column: 23
        },
        end: {
          line: 132,
          column: 52
        }
      },
      "25": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 135,
          column: 9
        }
      },
      "26": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 51
        }
      },
      "27": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 136,
          column: 48
        }
      },
      "28": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 176,
          column: 9
        }
      },
      "29": {
        start: {
          line: 138,
          column: 33
        },
        end: {
          line: 138,
          column: 69
        }
      },
      "30": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 141,
          column: 13
        }
      },
      "31": {
        start: {
          line: 140,
          column: 16
        },
        end: {
          line: 140,
          column: 60
        }
      },
      "32": {
        start: {
          line: 143,
          column: 33
        },
        end: {
          line: 143,
          column: 82
        }
      },
      "33": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 160,
          column: 13
        }
      },
      "34": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 69
        }
      },
      "35": {
        start: {
          line: 152,
          column: 16
        },
        end: {
          line: 152,
          column: 41
        }
      },
      "36": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 159,
          column: 17
        }
      },
      "37": {
        start: {
          line: 155,
          column: 44
        },
        end: {
          line: 155,
          column: 83
        }
      },
      "38": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 158,
          column: 21
        }
      },
      "39": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 88
        }
      },
      "40": {
        start: {
          line: 162,
          column: 43
        },
        end: {
          line: 162,
          column: 86
        }
      },
      "41": {
        start: {
          line: 164,
          column: 36
        },
        end: {
          line: 170,
          column: 13
        }
      },
      "42": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 65
        }
      },
      "43": {
        start: {
          line: 172,
          column: 12
        },
        end: {
          line: 172,
          column: 74
        }
      },
      "44": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 175,
          column: 111
        }
      },
      "45": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 235,
          column: 12
        }
      },
      "46": {
        start: {
          line: 182,
          column: 49
        },
        end: {
          line: 235,
          column: 9
        }
      },
      "47": {
        start: {
          line: 241,
          column: 24
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "48": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 249,
          column: 43
        }
      },
      "49": {
        start: {
          line: 255,
          column: 24
        },
        end: {
          line: 255,
          column: 33
        }
      },
      "50": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "51": {
        start: {
          line: 257,
          column: 30
        },
        end: {
          line: 257,
          column: 49
        }
      },
      "52": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 260,
          column: 13
        }
      },
      "53": {
        start: {
          line: 259,
          column: 16
        },
        end: {
          line: 259,
          column: 43
        }
      },
      "54": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 50
        }
      },
      "55": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 263,
          column: 23
        }
      },
      "56": {
        start: {
          line: 269,
          column: 8
        },
        end: {
          line: 275,
          column: 9
        }
      },
      "57": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 274,
          column: 13
        }
      },
      "58": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 273,
          column: 17
        }
      },
      "59": {
        start: {
          line: 272,
          column: 20
        },
        end: {
          line: 272,
          column: 72
        }
      },
      "60": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 276,
          column: 66
        }
      },
      "61": {
        start: {
          line: 282,
          column: 24
        },
        end: {
          line: 282,
          column: 59
        }
      },
      "62": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 285,
          column: 9
        }
      },
      "63": {
        start: {
          line: 284,
          column: 12
        },
        end: {
          line: 284,
          column: 52
        }
      },
      "64": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 286,
          column: 33
        }
      },
      "65": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 292,
          column: 59
        }
      },
      "66": {
        start: {
          line: 293,
          column: 8
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "67": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 294,
          column: 52
        }
      },
      "68": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 296,
          column: 67
        }
      },
      "69": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 302,
          column: 49
        }
      },
      "70": {
        start: {
          line: 308,
          column: 8
        },
        end: {
          line: 308,
          column: 44
        }
      },
      "71": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 314,
          column: 51
        }
      },
      "72": {
        start: {
          line: 320,
          column: 24
        },
        end: {
          line: 320,
          column: 59
        }
      },
      "73": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 323,
          column: 9
        }
      },
      "74": {
        start: {
          line: 322,
          column: 12
        },
        end: {
          line: 322,
          column: 57
        }
      },
      "75": {
        start: {
          line: 324,
          column: 8
        },
        end: {
          line: 327,
          column: 10
        }
      },
      "76": {
        start: {
          line: 333,
          column: 8
        },
        end: {
          line: 333,
          column: 47
        }
      },
      "77": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 334,
          column: 46
        }
      },
      "78": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 335,
          column: 34
        }
      },
      "79": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 341,
          column: 43
        }
      },
      "80": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 342,
          column: 63
        }
      },
      "81": {
        start: {
          line: 343,
          column: 8
        },
        end: {
          line: 343,
          column: 75
        }
      },
      "82": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 344,
          column: 59
        }
      },
      "83": {
        start: {
          line: 345,
          column: 8
        },
        end: {
          line: 348,
          column: 9
        }
      },
      "84": {
        start: {
          line: 346,
          column: 26
        },
        end: {
          line: 346,
          column: 57
        }
      },
      "85": {
        start: {
          line: 347,
          column: 12
        },
        end: {
          line: 347,
          column: 95
        }
      },
      "86": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 354,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 91,
            column: 5
          }
        },
        loc: {
          start: {
            line: 91,
            column: 29
          },
          end: {
            line: 98,
            column: 5
          }
        },
        line: 91
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 102,
            column: 5
          }
        },
        loc: {
          start: {
            line: 102,
            column: 23
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 102
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        },
        loc: {
          start: {
            line: 131,
            column: 37
          },
          end: {
            line: 177,
            column: 5
          }
        },
        line: 131
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 181,
            column: 5
          }
        },
        loc: {
          start: {
            line: 181,
            column: 39
          },
          end: {
            line: 236,
            column: 5
          }
        },
        line: 181
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 182,
            column: 31
          },
          end: {
            line: 182,
            column: 32
          }
        },
        loc: {
          start: {
            line: 182,
            column: 49
          },
          end: {
            line: 235,
            column: 9
          }
        },
        line: 182
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        },
        loc: {
          start: {
            line: 240,
            column: 31
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 240
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        loc: {
          start: {
            line: 254,
            column: 43
          },
          end: {
            line: 264,
            column: 5
          }
        },
        line: 254
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 268,
            column: 4
          },
          end: {
            line: 268,
            column: 5
          }
        },
        loc: {
          start: {
            line: 268,
            column: 25
          },
          end: {
            line: 277,
            column: 5
          }
        },
        line: 268
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 5
          }
        },
        loc: {
          start: {
            line: 281,
            column: 37
          },
          end: {
            line: 287,
            column: 5
          }
        },
        line: 281
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 291,
            column: 4
          },
          end: {
            line: 291,
            column: 5
          }
        },
        loc: {
          start: {
            line: 291,
            column: 58
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 291
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 301,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        loc: {
          start: {
            line: 301,
            column: 26
          },
          end: {
            line: 303,
            column: 5
          }
        },
        line: 301
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 307,
            column: 5
          }
        },
        loc: {
          start: {
            line: 307,
            column: 28
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 307
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 313,
            column: 4
          },
          end: {
            line: 313,
            column: 5
          }
        },
        loc: {
          start: {
            line: 313,
            column: 34
          },
          end: {
            line: 315,
            column: 5
          }
        },
        line: 313
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 319,
            column: 4
          },
          end: {
            line: 319,
            column: 5
          }
        },
        loc: {
          start: {
            line: 319,
            column: 31
          },
          end: {
            line: 328,
            column: 5
          }
        },
        line: 319
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 332,
            column: 5
          }
        },
        loc: {
          start: {
            line: 332,
            column: 35
          },
          end: {
            line: 336,
            column: 5
          }
        },
        line: 332
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 340,
            column: 4
          },
          end: {
            line: 340,
            column: 5
          }
        },
        loc: {
          start: {
            line: 340,
            column: 31
          },
          end: {
            line: 349,
            column: 5
          }
        },
        line: 340
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 353,
            column: 5
          }
        },
        loc: {
          start: {
            line: 353,
            column: 14
          },
          end: {
            line: 355,
            column: 5
          }
        },
        line: 353
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 91,
            column: 16
          },
          end: {
            line: 91,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 91,
            column: 25
          },
          end: {
            line: 91,
            column: 27
          }
        }],
        line: 91
      },
      "1": {
        loc: {
          start: {
            line: 93,
            column: 29
          },
          end: {
            line: 93,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 93,
            column: 29
          },
          end: {
            line: 93,
            column: 51
          }
        }, {
          start: {
            line: 93,
            column: 55
          },
          end: {
            line: 93,
            column: 73
          }
        }],
        line: 93
      },
      "2": {
        loc: {
          start: {
            line: 94,
            column: 30
          },
          end: {
            line: 94,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 30
          },
          end: {
            line: 94,
            column: 53
          }
        }, {
          start: {
            line: 94,
            column: 57
          },
          end: {
            line: 94,
            column: 77
          }
        }],
        line: 94
      },
      "3": {
        loc: {
          start: {
            line: 95,
            column: 25
          },
          end: {
            line: 95,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 95,
            column: 25
          },
          end: {
            line: 95,
            column: 43
          }
        }, {
          start: {
            line: 95,
            column: 47
          },
          end: {
            line: 95,
            column: 51
          }
        }],
        line: 95
      },
      "4": {
        loc: {
          start: {
            line: 96,
            column: 22
          },
          end: {
            line: 96,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 22
          },
          end: {
            line: 96,
            column: 37
          }
        }, {
          start: {
            line: 96,
            column: 41
          },
          end: {
            line: 96,
            column: 45
          }
        }],
        line: 96
      },
      "5": {
        loc: {
          start: {
            line: 103,
            column: 8
          },
          end: {
            line: 106,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 8
          },
          end: {
            line: 106,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "6": {
        loc: {
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 114,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 114,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "7": {
        loc: {
          start: {
            line: 125,
            column: 43
          },
          end: {
            line: 125,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 125,
            column: 68
          },
          end: {
            line: 125,
            column: 81
          }
        }, {
          start: {
            line: 125,
            column: 84
          },
          end: {
            line: 125,
            column: 97
          }
        }],
        line: 125
      },
      "8": {
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "9": {
        loc: {
          start: {
            line: 139,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 12
          },
          end: {
            line: 141,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "10": {
        loc: {
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 160,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 12
          },
          end: {
            line: 160,
            column: 13
          }
        }, {
          start: {
            line: 150,
            column: 17
          },
          end: {
            line: 160,
            column: 13
          }
        }],
        line: 146
      },
      "11": {
        loc: {
          start: {
            line: 154,
            column: 16
          },
          end: {
            line: 159,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 16
          },
          end: {
            line: 159,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "12": {
        loc: {
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 158,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 158,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "13": {
        loc: {
          start: {
            line: 175,
            column: 53
          },
          end: {
            line: 175,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 175,
            column: 78
          },
          end: {
            line: 175,
            column: 91
          }
        }, {
          start: {
            line: 175,
            column: 94
          },
          end: {
            line: 175,
            column: 107
          }
        }],
        line: 175
      },
      "14": {
        loc: {
          start: {
            line: 183,
            column: 31
          },
          end: {
            line: 183,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 31
          },
          end: {
            line: 183,
            column: 38
          }
        }, {
          start: {
            line: 183,
            column: 42
          },
          end: {
            line: 183,
            column: 47
          }
        }],
        line: 183
      },
      "15": {
        loc: {
          start: {
            line: 189,
            column: 35
          },
          end: {
            line: 189,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 189,
            column: 35
          },
          end: {
            line: 189,
            column: 74
          }
        }, {
          start: {
            line: 189,
            column: 78
          },
          end: {
            line: 189,
            column: 80
          }
        }],
        line: 189
      },
      "16": {
        loc: {
          start: {
            line: 190,
            column: 32
          },
          end: {
            line: 190,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 32
          },
          end: {
            line: 190,
            column: 72
          }
        }, {
          start: {
            line: 190,
            column: 76
          },
          end: {
            line: 190,
            column: 77
          }
        }],
        line: 190
      },
      "17": {
        loc: {
          start: {
            line: 196,
            column: 36
          },
          end: {
            line: 196,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 196,
            column: 36
          },
          end: {
            line: 196,
            column: 80
          }
        }, {
          start: {
            line: 196,
            column: 84
          },
          end: {
            line: 196,
            column: 90
          }
        }],
        line: 196
      },
      "18": {
        loc: {
          start: {
            line: 206,
            column: 32
          },
          end: {
            line: 206,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 206,
            column: 70
          },
          end: {
            line: 206,
            column: 73
          }
        }, {
          start: {
            line: 206,
            column: 76
          },
          end: {
            line: 206,
            column: 79
          }
        }],
        line: 206
      },
      "19": {
        loc: {
          start: {
            line: 207,
            column: 27
          },
          end: {
            line: 207,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 207,
            column: 64
          },
          end: {
            line: 207,
            column: 67
          }
        }, {
          start: {
            line: 207,
            column: 70
          },
          end: {
            line: 207,
            column: 73
          }
        }],
        line: 207
      },
      "20": {
        loc: {
          start: {
            line: 212,
            column: 31
          },
          end: {
            line: 212,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 31
          },
          end: {
            line: 212,
            column: 40
          }
        }, {
          start: {
            line: 212,
            column: 44
          },
          end: {
            line: 212,
            column: 46
          }
        }],
        line: 212
      },
      "21": {
        loc: {
          start: {
            line: 217,
            column: 29
          },
          end: {
            line: 217,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 217,
            column: 29
          },
          end: {
            line: 217,
            column: 62
          }
        }, {
          start: {
            line: 217,
            column: 66
          },
          end: {
            line: 217,
            column: 69
          }
        }],
        line: 217
      },
      "22": {
        loc: {
          start: {
            line: 220,
            column: 34
          },
          end: {
            line: 220,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 220,
            column: 34
          },
          end: {
            line: 220,
            column: 72
          }
        }, {
          start: {
            line: 220,
            column: 76
          },
          end: {
            line: 220,
            column: 79
          }
        }],
        line: 220
      },
      "23": {
        loc: {
          start: {
            line: 227,
            column: 35
          },
          end: {
            line: 227,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 35
          },
          end: {
            line: 227,
            column: 53
          }
        }, {
          start: {
            line: 227,
            column: 57
          },
          end: {
            line: 227,
            column: 60
          }
        }],
        line: 227
      },
      "24": {
        loc: {
          start: {
            line: 228,
            column: 29
          },
          end: {
            line: 228,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 29
          },
          end: {
            line: 228,
            column: 49
          }
        }, {
          start: {
            line: 228,
            column: 53
          },
          end: {
            line: 228,
            column: 56
          }
        }],
        line: 228
      },
      "25": {
        loc: {
          start: {
            line: 249,
            column: 15
          },
          end: {
            line: 249,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 15
          },
          end: {
            line: 249,
            column: 32
          }
        }, {
          start: {
            line: 249,
            column: 36
          },
          end: {
            line: 249,
            column: 42
          }
        }],
        line: 249
      },
      "26": {
        loc: {
          start: {
            line: 258,
            column: 12
          },
          end: {
            line: 260,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 12
          },
          end: {
            line: 260,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "27": {
        loc: {
          start: {
            line: 270,
            column: 12
          },
          end: {
            line: 274,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 12
          },
          end: {
            line: 274,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "28": {
        loc: {
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "29": {
        loc: {
          start: {
            line: 293,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 8
          },
          end: {
            line: 295,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 293
      },
      "30": {
        loc: {
          start: {
            line: 296,
            column: 15
          },
          end: {
            line: 296,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 296,
            column: 15
          },
          end: {
            line: 296,
            column: 60
          }
        }, {
          start: {
            line: 296,
            column: 64
          },
          end: {
            line: 296,
            column: 66
          }
        }],
        line: 296
      },
      "31": {
        loc: {
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 323,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 323,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "32": {
        loc: {
          start: {
            line: 326,
            column: 26
          },
          end: {
            line: 326,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 326,
            column: 26
          },
          end: {
            line: 326,
            column: 50
          }
        }, {
          start: {
            line: 326,
            column: 54
          },
          end: {
            line: 326,
            column: 55
          }
        }],
        line: 326
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/LanguageManager.ts",
      mappings: "AAAA;;;;;;;;;GASG;AAEH,OAAO,KAAK,EAAE,MAAM,IAAI,CAAA;AACxB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAA;AAC5B,OAAO,EAAE,aAAa,EAAE,MAAM,KAAK,CAAA;AAEnC,sBAAsB;AACtB,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACjD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;AAE1C,YAAY;AACZ,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAA;AAOzE,+EAA+E;AAC/E,QAAQ;AACR,+EAA+E;AAE/E,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAA;AAEnD,UAAU;AACV,MAAM,mBAAmB,GAKpB;IACH,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,wBAAwB;QAClC,WAAW,EAAE,IAAI,EAAE,WAAW;QAC9B,OAAO,EAAE,IAAI;KACd;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,cAAc;QACpB,QAAQ,EAAE,2BAA2B;QACrC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,IAAI;KACd;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,KAAK;QACX,QAAQ,EAAE,4BAA4B;QACtC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,KAAK;KACf;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,KAAK;QACX,QAAQ,EAAE,0BAA0B;QACpC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,KAAK;KACf;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,2BAA2B;QACrC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,KAAK;KACf;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE,0BAA0B;QACpC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,KAAK;KACf;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,0BAA0B;QACpC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,KAAK;KACf;IACD,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QACpB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,0BAA0B;QACpC,WAAW,EAAE,4BAA4B;QACzC,OAAO,EAAE,KAAK;KACf;IACD,eAAe;CAChB,CAAA;AA8BD,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,eAAe;IAClB,MAAM,CAAuB;IAC7B,gBAAgB,GAAuC,IAAI,GAAG,EAAE,CAAA;IAChE,aAAa,GAAkC,IAAI,GAAG,EAAE,CAAA;IACxD,aAAa,GAAG,KAAK,CAAA;IAE7B;;OAEG;IACH,YAAY,SAAyC,EAAE;QACrD,IAAI,CAAC,MAAM,GAAG;YACZ,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,YAAY,CAAC,KAAK;YAC7D,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,UAAU;YAC7E,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI;YACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM;SACzC,CAAA;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;YACnC,OAAM;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;QAElC,IAAI,CAAC;YACH,YAAY;YACZ,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBACpD,IAAI,mBAAmB,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC;oBAC3C,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;gBACvC,CAAC;YACH,CAAC;YAED,SAAS;YACT,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAEzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAEvC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,UAAU,QAAQ,IAAI,CAAC,CAAA;YACvF,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAA;YACrC,MAAM,IAAI,KAAK,CAAC,eAAe,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QAC1F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,QAAsB;QACnD,MAAM,MAAM,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAA;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,WAAW,QAAQ,EAAE,CAAC,CAAA;QACxC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,OAAO,CAAC,CAAA;QAEvC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAA;YAEzD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,EAAE,CAAC,CAAA;YAC7C,CAAC;YAED,SAAS;YACT,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAA;YACtE,IAAI,SAAqC,CAAA;YACzC,IAAI,QAAwC,CAAA;YAE5C,IAAI,QAAQ,KAAK,YAAY,CAAC,KAAK,EAAE,CAAC;gBACpC,gBAAgB;gBAChB,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAA;YACtD,CAAC;iBAAM,CAAC;gBACN,eAAe;gBACf,SAAS,GAAG,YAA0C,CAAA;gBAEtD,SAAS;gBACT,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBACvB,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC,CAAA;oBAC/D,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;wBACnC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAA;oBACjE,CAAC;gBACH,CAAC;YACH,CAAC;YAED,YAAY;YACZ,MAAM,sBAAsB,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAA;YAE1E,UAAU;YACV,MAAM,eAAe,GAAoB;gBACvC,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,sBAAsB;gBACtB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;aACrB,CAAA;YAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAA;YACpD,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,WAAW,SAAS,CAAC,MAAM,KAAK,CAAC,CAAA;QAE/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,IAAI,SAAS,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACpG,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,WAAkB;QAC9C,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACvC,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,KAAK,EAAE;YACrC,UAAU,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS;YAC7C,QAAQ,EAAE,YAAY,CAAC,KAAK;YAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,iBAAiB,EAAE,EAAE;YACrB,iBAAiB,EAAE;gBACjB,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,EAAE,aAAa,IAAI,EAAE;gBAChE,cAAc,EAAE,IAAI,CAAC,mBAAmB,EAAE,cAAc,IAAI,CAAC;gBAC7D,YAAY,EAAE,EAAE,EAAE,SAAS;gBAC3B,gBAAgB,EAAE,GAAG;aACtB;YACD,kBAAkB,EAAE;gBAClB,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC7C,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,kBAAkB,IAAI,MAAM;gBAC1E,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,EAAE;aACb;YACD,oBAAoB,EAAE;gBACpB,kBAAkB,EAAE,CAAC,MAAM,CAAC;gBAC5B,uBAAuB,EAAE,EAAE;gBAC3B,oBAAoB,EAAE,EAAE;aACzB;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,IAAI,CAAC,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBAC/D,SAAS,EAAE,IAAI,CAAC,gBAAgB,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBACzD,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,qBAAqB,EAAE,GAAG;gBAC1B,mBAAmB,EAAE,CAAC,KAAK,CAAC;gBAC5B,aAAa,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;aAC/B;YACD,iBAAiB,EAAE,EAAE;YACrB,cAAc,EAAE,aAAa,CAAC,OAAO;YACrC,uBAAuB,EAAE;gBACvB,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,WAAW,IAAI,GAAG;gBACrD,OAAO,EAAE,GAAG;gBACZ,YAAY,EAAE,GAAG;gBACjB,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,gBAAgB,IAAI,GAAG;gBAC/D,kBAAkB,EAAE,GAAG;gBACvB,YAAY,EAAE,GAAG;gBACjB,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,GAAG;aAClB;YACD,wBAAwB,EAAE,GAAG;YAC7B,qBAAqB,EAAE,IAAI,CAAC,aAAa,IAAI,GAAG;YAChD,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,GAAG;YAC5C,gBAAgB,EAAE,GAAG;YACrB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,2BAA2B;YACnC,iBAAiB,EAAE,WAAW;SAC/B,CAAC,CAAC,CAAA;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,MAAM,OAAO,GAA2B;YACtC,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,MAAM;YACrB,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,MAAM;YACjB,UAAU,EAAE,MAAM;SACnB,CAAA;QACD,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAA;IACpC,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,SAAqC;QACvE,MAAM,OAAO,GAAG,IAAI,GAAG,EAAsC,CAAA;QAE7D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAA;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;YAC5B,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACxC,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;YACrD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACvC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;gBACrD,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,QAAsB;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,YAAY,QAAQ,EAAE,CAAC,CAAA;QACzC,CAAC;QACD,OAAO,OAAO,CAAC,SAAS,CAAA;IAC1B,CAAC;IAED;;OAEG;IACH,gCAAgC,CAAC,SAAiB,EAAE,QAAsB;QACxE,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,YAAY,QAAQ,EAAE,CAAC,CAAA;QACzC,CAAC;QACD,OAAO,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAA;IAC5D,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAsB;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAsB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAA;QAC9C,CAAC;QAED,OAAO;YACL,aAAa,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM;YACvC,YAAY,EAAE,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC;SAC5C,CAAA;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAsB;QACzC,OAAO,CAAC,GAAG,CAAC,UAAU,QAAQ,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;QAClC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAA;QACtD,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAClE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAA;QAElD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;YAC7C,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,QAAQ,KAAK,CAAC,YAAY,KAAK,CAAC,CAAA;QACpF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/LanguageManager.ts"],
      sourcesContent: ["/**\n * \u8BED\u8A00\u7BA1\u7406\u5668\n * \n * \u8D1F\u8D23\u591A\u8BED\u79CD\u6570\u636E\u52A0\u8F7D\u3001\u8BED\u8A00\u5207\u6362\u3001\u8DE8\u8BED\u8A00\u6620\u5C04\u7B49\u6838\u5FC3\u529F\u80FD\n * \u652F\u6301v3.0\u591A\u8BED\u79CD\u67B6\u6784\u7684\u6982\u5FF5-\u8BED\u8A00\u5206\u79BB\u6A21\u5F0F\n * \n * <AUTHOR> team\n * @version 3.0.0\n * @created 2025-06-24\n */\n\nimport * as fs from 'fs'\nimport * as path from 'path'\nimport { fileURLToPath } from 'url'\n\n// ES\u6A21\u5757\u4E2D\u7684__dirname\u66FF\u4EE3\u65B9\u6848\nconst __filename = fileURLToPath(import.meta.url)\nconst __dirname = path.dirname(__filename)\n\n// \u5BFC\u5165\u7C7B\u578B\u5B9A\u4E49\u548C\u679A\u4E3E\nimport { LanguageCode, RegisterLevel } from '../../types/multilingual.js'\nimport type {\n  UniversalConcept,\n  LanguageSpecificMorpheme,\n  ConceptMorphemeMapping\n} from '../../types/multilingual.js'\n\n// ============================================================================\n// \u914D\u7F6E\u548C\u5E38\u91CF\n// ============================================================================\n\nconst DATA_DIR = path.join(__dirname, '../../data')\n\n// \u652F\u6301\u7684\u8BED\u8A00\u914D\u7F6E\nconst SUPPORTED_LANGUAGES: Record<LanguageCode, {\n  name: string\n  dataFile: string\n  conceptFile: string | null\n  enabled: boolean\n}> = {\n  [LanguageCode.ZH_CN]: {\n    name: '\u4E2D\u6587(\u7B80\u4F53)',\n    dataFile: 'morphemes_current.json',\n    conceptFile: null, // \u4E2D\u6587\u4F7F\u7528\u4F20\u7EDF\u683C\u5F0F\n    enabled: true\n  },\n  [LanguageCode.EN_US]: {\n    name: 'English (US)',\n    dataFile: 'english_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: true\n  },\n  [LanguageCode.JA_JP]: {\n    name: '\u65E5\u672C\u8A9E',\n    dataFile: 'japanese_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.KO_KR]: {\n    name: '\uD55C\uAD6D\uC5B4',\n    dataFile: 'korean_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.ES_ES]: {\n    name: 'Espa\xF1ol',\n    dataFile: 'spanish_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.FR_FR]: {\n    name: 'Fran\xE7ais',\n    dataFile: 'french_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.DE_DE]: {\n    name: 'Deutsch',\n    dataFile: 'german_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.AR_SA]: {\n    name: '\u0627\u0644\u0639\u0631\u0628\u064A\u0629',\n    dataFile: 'arabic_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  }\n  // \u5176\u4ED6\u8BED\u8A00\u5C06\u5728\u540E\u7EED\u9636\u6BB5\u6DFB\u52A0\n}\n\n// ============================================================================\n// \u8BED\u8A00\u7BA1\u7406\u5668\u63A5\u53E3\n// ============================================================================\n\nexport interface LanguageManagerConfig {\n  /** \u9ED8\u8BA4\u8BED\u8A00 */\n  defaultLanguage: LanguageCode\n  /** \u542F\u7528\u7684\u8BED\u8A00\u5217\u8868 */\n  enabledLanguages: LanguageCode[]\n  /** \u662F\u5426\u542F\u7528\u7F13\u5B58 */\n  enableCache: boolean\n  /** \u7F13\u5B58TTL (\u79D2) */\n  cacheTTL: number\n}\n\nexport interface LanguageDataSet {\n  /** \u8BED\u8A00\u4EE3\u7801 */\n  language: LanguageCode\n  /** \u8BED\u8A00\u7279\u5B9A\u8BED\u7D20 */\n  morphemes: LanguageSpecificMorpheme[]\n  /** \u901A\u7528\u6982\u5FF5 (\u4EC5v3.0\u683C\u5F0F) */\n  concepts?: UniversalConcept[]\n  /** \u6982\u5FF5-\u8BED\u7D20\u6620\u5C04 */\n  conceptMorphemeMapping: Map<string, LanguageSpecificMorpheme[]>\n  /** \u52A0\u8F7D\u65F6\u95F4\u6233 */\n  loadedAt: number\n}\n\n// ============================================================================\n// \u8BED\u8A00\u7BA1\u7406\u5668\u7C7B\n// ============================================================================\n\n/**\n * \u8BED\u8A00\u7BA1\u7406\u5668\u7C7B\n * \n * \u63D0\u4F9B\u591A\u8BED\u79CD\u6570\u636E\u7BA1\u7406\u3001\u8BED\u8A00\u5207\u6362\u3001\u8DE8\u8BED\u8A00\u6620\u5C04\u7B49\u529F\u80FD\n */\nexport class LanguageManager {\n  private config: LanguageManagerConfig\n  private languageDataSets: Map<LanguageCode, LanguageDataSet> = new Map()\n  private conceptsCache: Map<string, UniversalConcept> = new Map()\n  private isInitialized = false\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   */\n  constructor(config: Partial<LanguageManagerConfig> = {}) {\n    this.config = {\n      defaultLanguage: config.defaultLanguage || LanguageCode.ZH_CN,\n      enabledLanguages: config.enabledLanguages || [LanguageCode.ZH_CN], // \u6682\u65F6\u53EA\u542F\u7528\u4E2D\u6587\n      enableCache: config.enableCache ?? true,\n      cacheTTL: config.cacheTTL || 3600 // 1\u5C0F\u65F6\n    }\n  }\n\n  /**\n   * \u521D\u59CB\u5316\u8BED\u8A00\u7BA1\u7406\u5668\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('\uD83D\uDD04 \u8BED\u8A00\u7BA1\u7406\u5668\u5DF2\u521D\u59CB\u5316\uFF0C\u8DF3\u8FC7\u91CD\u590D\u521D\u59CB\u5316')\n      return\n    }\n\n    const startTime = Date.now()\n    console.log('\uD83C\uDF0D \u5F00\u59CB\u521D\u59CB\u5316\u591A\u8BED\u79CD\u8BED\u8A00\u7BA1\u7406\u5668...')\n\n    try {\n      // \u52A0\u8F7D\u542F\u7528\u7684\u8BED\u8A00\u6570\u636E\n      for (const language of this.config.enabledLanguages) {\n        if (SUPPORTED_LANGUAGES[language]?.enabled) {\n          await this.loadLanguageData(language)\n        }\n      }\n\n      // \u6784\u5EFA\u6982\u5FF5\u7F13\u5B58\n      this.buildConceptsCache()\n\n      this.isInitialized = true\n      const initTime = Date.now() - startTime\n\n      console.log(`\u2705 \u8BED\u8A00\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210: ${this.config.enabledLanguages.length}\u79CD\u8BED\u8A00, \u8017\u65F6${initTime}ms`)\n      this.logInitializationSummary()\n\n    } catch (error) {\n      console.error('\u274C \u8BED\u8A00\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:', error)\n      throw new Error(`\u8BED\u8A00\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u52A0\u8F7D\u6307\u5B9A\u8BED\u8A00\u7684\u6570\u636E\n   */\n  private async loadLanguageData(language: LanguageCode): Promise<void> {\n    const config = SUPPORTED_LANGUAGES[language]\n    if (!config) {\n      throw new Error(`\u4E0D\u652F\u6301\u7684\u8BED\u8A00: ${language}`)\n    }\n\n    console.log(`\uD83D\uDCC1 \u52A0\u8F7D${config.name}\u6570\u636E...`)\n\n    try {\n      const dataFilePath = path.join(DATA_DIR, config.dataFile)\n      \n      if (!fs.existsSync(dataFilePath)) {\n        throw new Error(`\u6570\u636E\u6587\u4EF6\u4E0D\u5B58\u5728: ${dataFilePath}`)\n      }\n\n      // \u52A0\u8F7D\u8BED\u7D20\u6570\u636E\n      const morphemeData = JSON.parse(fs.readFileSync(dataFilePath, 'utf8'))\n      let morphemes: LanguageSpecificMorpheme[]\n      let concepts: UniversalConcept[] | undefined\n\n      if (language === LanguageCode.ZH_CN) {\n        // \u4E2D\u6587\u4F7F\u7528\u4F20\u7EDF\u683C\u5F0F\uFF0C\u9700\u8981\u9002\u914D\n        morphemes = this.adaptChineseMorphemes(morphemeData)\n      } else {\n        // \u5176\u4ED6\u8BED\u8A00\u4F7F\u7528v3.0\u683C\u5F0F\n        morphemes = morphemeData as LanguageSpecificMorpheme[]\n        \n        // \u52A0\u8F7D\u901A\u7528\u6982\u5FF5\n        if (config.conceptFile) {\n          const conceptFilePath = path.join(DATA_DIR, config.conceptFile)\n          if (fs.existsSync(conceptFilePath)) {\n            concepts = JSON.parse(fs.readFileSync(conceptFilePath, 'utf8'))\n          }\n        }\n      }\n\n      // \u6784\u5EFA\u6982\u5FF5-\u8BED\u7D20\u6620\u5C04\n      const conceptMorphemeMapping = this.buildConceptMorphemeMapping(morphemes)\n\n      // \u521B\u5EFA\u8BED\u8A00\u6570\u636E\u96C6\n      const languageDataSet: LanguageDataSet = {\n        language,\n        morphemes,\n        concepts,\n        conceptMorphemeMapping,\n        loadedAt: Date.now()\n      }\n\n      this.languageDataSets.set(language, languageDataSet)\n      console.log(`\u2705 ${config.name}\u6570\u636E\u52A0\u8F7D\u5B8C\u6210: ${morphemes.length}\u4E2A\u8BED\u7D20`)\n\n    } catch (error) {\n      throw new Error(`\u52A0\u8F7D${config.name}\u6570\u636E\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u9002\u914D\u4E2D\u6587\u8BED\u7D20\u6570\u636E\u5230v3.0\u683C\u5F0F\n   */\n  private adaptChineseMorphemes(chineseData: any[]): LanguageSpecificMorpheme[] {\n    return chineseData.map((item, index) => ({\n      morpheme_id: `zh_${item.id || index}`,\n      concept_id: `concept_${item.text}`, // \u4E34\u65F6\u6982\u5FF5ID\n      language: LanguageCode.ZH_CN,\n      text: item.text,\n      alternative_forms: [],\n      phonetic_features: {\n        ipa_transcription: item.language_properties?.pronunciation || '',\n        syllable_count: item.language_properties?.syllable_count || 1,\n        tone_pattern: [], // \u4E2D\u6587\u58F0\u8C03\u4FE1\u606F\n        phonetic_harmony: 0.8\n      },\n      morphological_info: {\n        pos_tag: this.mapCategoryToPOS(item.category),\n        morphological_type: item.language_properties?.morphological_type || 'root',\n        prefixes: [],\n        suffixes: []\n      },\n      syntactic_properties: {\n        syntactic_function: ['root'],\n        collocation_constraints: [],\n        grammatical_features: {}\n      },\n      cultural_context: {\n        traditionality: item.cultural_context === 'ancient' ? 0.9 : 0.3,\n        modernity: item.cultural_context === 'modern' ? 0.9 : 0.3,\n        formality: 0.5,\n        regionality: 0.2,\n        religious_sensitivity: 0.1,\n        age_appropriateness: ['all'],\n        cultural_tags: item.tags || []\n      },\n      regional_variants: [],\n      register_level: RegisterLevel.NEUTRAL,\n      language_quality_scores: {\n        naturalness: item.quality_metrics?.naturalness || 0.8,\n        fluency: 0.9,\n        authenticity: 0.9,\n        aesthetic_appeal: item.quality_metrics?.aesthetic_appeal || 0.8,\n        pronunciation_ease: 0.8,\n        memorability: 0.8,\n        uniqueness: 0.6,\n        practicality: 0.9\n      },\n      cultural_appropriateness: 0.9,\n      native_speaker_rating: item.quality_score || 0.8,\n      usage_frequency: item.usage_frequency || 0.5,\n      popularity_trend: 0.0,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      version: '3.0.0',\n      source: 'chinese_morphemes_adapted',\n      validation_status: 'validated'\n    }))\n  }\n\n  /**\n   * \u6620\u5C04\u7C7B\u522B\u5230\u8BCD\u6027\u6807\u6CE8\n   */\n  private mapCategoryToPOS(category: string): string {\n    const mapping: Record<string, string> = {\n      'emotions': 'ADJ',\n      'professions': 'NOUN',\n      'characteristics': 'ADJ',\n      'objects': 'NOUN',\n      'actions': 'VERB',\n      'concepts': 'NOUN'\n    }\n    return mapping[category] || 'NOUN'\n  }\n\n  /**\n   * \u6784\u5EFA\u6982\u5FF5-\u8BED\u7D20\u6620\u5C04\n   */\n  private buildConceptMorphemeMapping(morphemes: LanguageSpecificMorpheme[]): Map<string, LanguageSpecificMorpheme[]> {\n    const mapping = new Map<string, LanguageSpecificMorpheme[]>()\n    \n    for (const morpheme of morphemes) {\n      const conceptId = morpheme.concept_id\n      if (!mapping.has(conceptId)) {\n        mapping.set(conceptId, [])\n      }\n      mapping.get(conceptId)!.push(morpheme)\n    }\n    \n    return mapping\n  }\n\n  /**\n   * \u6784\u5EFA\u6982\u5FF5\u7F13\u5B58\n   */\n  private buildConceptsCache(): void {\n    for (const dataSet of this.languageDataSets.values()) {\n      if (dataSet.concepts) {\n        for (const concept of dataSet.concepts) {\n          this.conceptsCache.set(concept.concept_id, concept)\n        }\n      }\n    }\n    console.log(`\uD83D\uDCCA \u6982\u5FF5\u7F13\u5B58\u6784\u5EFA\u5B8C\u6210: ${this.conceptsCache.size}\u4E2A\u6982\u5FF5`)\n  }\n\n  /**\n   * \u83B7\u53D6\u6307\u5B9A\u8BED\u8A00\u7684\u8BED\u7D20\n   */\n  getMorphemesByLanguage(language: LanguageCode): LanguageSpecificMorpheme[] {\n    const dataSet = this.languageDataSets.get(language)\n    if (!dataSet) {\n      throw new Error(`\u8BED\u8A00\u6570\u636E\u672A\u52A0\u8F7D: ${language}`)\n    }\n    return dataSet.morphemes\n  }\n\n  /**\n   * \u6839\u636E\u6982\u5FF5ID\u83B7\u53D6\u6307\u5B9A\u8BED\u8A00\u7684\u8BED\u7D20\n   */\n  getMorphemesByConceptAndLanguage(conceptId: string, language: LanguageCode): LanguageSpecificMorpheme[] {\n    const dataSet = this.languageDataSets.get(language)\n    if (!dataSet) {\n      throw new Error(`\u8BED\u8A00\u6570\u636E\u672A\u52A0\u8F7D: ${language}`)\n    }\n    return dataSet.conceptMorphemeMapping.get(conceptId) || []\n  }\n\n  /**\n   * \u83B7\u53D6\u901A\u7528\u6982\u5FF5\n   */\n  getConcept(conceptId: string): UniversalConcept | undefined {\n    return this.conceptsCache.get(conceptId)\n  }\n\n  /**\n   * \u83B7\u53D6\u6240\u6709\u652F\u6301\u7684\u8BED\u8A00\n   */\n  getSupportedLanguages(): LanguageCode[] {\n    return this.config.enabledLanguages\n  }\n\n  /**\n   * \u68C0\u67E5\u8BED\u8A00\u662F\u5426\u652F\u6301\n   */\n  isLanguageSupported(language: LanguageCode): boolean {\n    return this.languageDataSets.has(language)\n  }\n\n  /**\n   * \u83B7\u53D6\u8BED\u8A00\u7EDF\u8BA1\u4FE1\u606F\n   */\n  getLanguageStats(language: LanguageCode): { morphemeCount: number; conceptCount: number } {\n    const dataSet = this.languageDataSets.get(language)\n    if (!dataSet) {\n      return { morphemeCount: 0, conceptCount: 0 }\n    }\n    \n    return {\n      morphemeCount: dataSet.morphemes.length,\n      conceptCount: dataSet.concepts?.length || 0\n    }\n  }\n\n  /**\n   * \u91CD\u65B0\u52A0\u8F7D\u8BED\u8A00\u6570\u636E\n   */\n  async reloadLanguage(language: LanguageCode): Promise<void> {\n    console.log(`\uD83D\uDD04 \u91CD\u65B0\u52A0\u8F7D${language}\u6570\u636E...`)\n    await this.loadLanguageData(language)\n    this.buildConceptsCache()\n  }\n\n  /**\n   * \u8F93\u51FA\u521D\u59CB\u5316\u6458\u8981\n   */\n  private logInitializationSummary(): void {\n    console.log('\\n\uD83C\uDF0D \u591A\u8BED\u79CD\u8BED\u8A00\u7BA1\u7406\u5668\u521D\u59CB\u5316\u6458\u8981:')\n    console.log(`   \u9ED8\u8BA4\u8BED\u8A00: ${this.config.defaultLanguage}`)\n    console.log(`   \u542F\u7528\u8BED\u8A00: ${this.config.enabledLanguages.join(', ')}`)\n    console.log(`   \u6982\u5FF5\u603B\u6570: ${this.conceptsCache.size}`)\n    \n    for (const language of this.config.enabledLanguages) {\n      const stats = this.getLanguageStats(language)\n      console.log(`   ${language}: ${stats.morphemeCount}\u4E2A\u8BED\u7D20, ${stats.conceptCount}\u4E2A\u6982\u5FF5`)\n    }\n  }\n\n  /**\n   * \u68C0\u67E5\u662F\u5426\u5DF2\u521D\u59CB\u5316\n   */\n  isReady(): boolean {\n    return this.isInitialized\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1541e12704b7a95716cc18501780212987cc03df"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_6si821j0y = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6si821j0y();
/**
 * 语言管理器
 *
 * 负责多语种数据加载、语言切换、跨语言映射等核心功能
 * 支持v3.0多语种架构的概念-语言分离模式
 *
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
// ES模块中的__dirname替代方案
const __filename =
/* istanbul ignore next */
(cov_6si821j0y().s[0]++, fileURLToPath(import.meta.url));
const __dirname =
/* istanbul ignore next */
(cov_6si821j0y().s[1]++, path.dirname(__filename));
// 导入类型定义和枚举
import { LanguageCode, RegisterLevel } from '../../types/multilingual.js';
// ============================================================================
// 配置和常量
// ============================================================================
const DATA_DIR =
/* istanbul ignore next */
(cov_6si821j0y().s[2]++, path.join(__dirname, '../../data'));
// 支持的语言配置
const SUPPORTED_LANGUAGES =
/* istanbul ignore next */
(cov_6si821j0y().s[3]++, {
  [LanguageCode.ZH_CN]: {
    name: '中文(简体)',
    dataFile: 'morphemes_current.json',
    conceptFile: null,
    // 中文使用传统格式
    enabled: true
  },
  [LanguageCode.EN_US]: {
    name: 'English (US)',
    dataFile: 'english_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: true
  },
  [LanguageCode.JA_JP]: {
    name: '日本語',
    dataFile: 'japanese_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.KO_KR]: {
    name: '한국어',
    dataFile: 'korean_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.ES_ES]: {
    name: 'Español',
    dataFile: 'spanish_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.FR_FR]: {
    name: 'Français',
    dataFile: 'french_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.DE_DE]: {
    name: 'Deutsch',
    dataFile: 'german_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  },
  [LanguageCode.AR_SA]: {
    name: 'العربية',
    dataFile: 'arabic_morphemes_v3.json',
    conceptFile: 'universal_concepts_v3.json',
    enabled: false
  }
  // 其他语言将在后续阶段添加
});
// ============================================================================
// 语言管理器类
// ============================================================================
/**
 * 语言管理器类
 *
 * 提供多语种数据管理、语言切换、跨语言映射等功能
 */
export class LanguageManager {
  config;
  languageDataSets =
  /* istanbul ignore next */
  (cov_6si821j0y().s[4]++, new Map());
  conceptsCache =
  /* istanbul ignore next */
  (cov_6si821j0y().s[5]++, new Map());
  isInitialized =
  /* istanbul ignore next */
  (cov_6si821j0y().s[6]++, false);
  /**
   * 构造函数
   */
  constructor(config =
  /* istanbul ignore next */
  (cov_6si821j0y().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_6si821j0y().f[0]++;
    cov_6si821j0y().s[7]++;
    this.config = {
      defaultLanguage:
      /* istanbul ignore next */
      (cov_6si821j0y().b[1][0]++, config.defaultLanguage) ||
      /* istanbul ignore next */
      (cov_6si821j0y().b[1][1]++, LanguageCode.ZH_CN),
      enabledLanguages:
      /* istanbul ignore next */
      (cov_6si821j0y().b[2][0]++, config.enabledLanguages) ||
      /* istanbul ignore next */
      (cov_6si821j0y().b[2][1]++, [LanguageCode.ZH_CN]),
      // 暂时只启用中文
      enableCache:
      /* istanbul ignore next */
      (cov_6si821j0y().b[3][0]++, config.enableCache) ??
      /* istanbul ignore next */
      (cov_6si821j0y().b[3][1]++, true),
      cacheTTL:
      /* istanbul ignore next */
      (cov_6si821j0y().b[4][0]++, config.cacheTTL) ||
      /* istanbul ignore next */
      (cov_6si821j0y().b[4][1]++, 3600) // 1小时
    };
  }
  /**
   * 初始化语言管理器
   */
  async initialize() {
    /* istanbul ignore next */
    cov_6si821j0y().f[1]++;
    cov_6si821j0y().s[8]++;
    if (this.isInitialized) {
      /* istanbul ignore next */
      cov_6si821j0y().b[5][0]++;
      cov_6si821j0y().s[9]++;
      console.log('🔄 语言管理器已初始化，跳过重复初始化');
      /* istanbul ignore next */
      cov_6si821j0y().s[10]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_6si821j0y().b[5][1]++;
    }
    const startTime =
    /* istanbul ignore next */
    (cov_6si821j0y().s[11]++, Date.now());
    /* istanbul ignore next */
    cov_6si821j0y().s[12]++;
    console.log('🌍 开始初始化多语种语言管理器...');
    /* istanbul ignore next */
    cov_6si821j0y().s[13]++;
    try {
      /* istanbul ignore next */
      cov_6si821j0y().s[14]++;
      // 加载启用的语言数据
      for (const language of this.config.enabledLanguages) {
        /* istanbul ignore next */
        cov_6si821j0y().s[15]++;
        if (SUPPORTED_LANGUAGES[language]?.enabled) {
          /* istanbul ignore next */
          cov_6si821j0y().b[6][0]++;
          cov_6si821j0y().s[16]++;
          await this.loadLanguageData(language);
        } else
        /* istanbul ignore next */
        {
          cov_6si821j0y().b[6][1]++;
        }
      }
      // 构建概念缓存
      /* istanbul ignore next */
      cov_6si821j0y().s[17]++;
      this.buildConceptsCache();
      /* istanbul ignore next */
      cov_6si821j0y().s[18]++;
      this.isInitialized = true;
      const initTime =
      /* istanbul ignore next */
      (cov_6si821j0y().s[19]++, Date.now() - startTime);
      /* istanbul ignore next */
      cov_6si821j0y().s[20]++;
      console.log(`✅ 语言管理器初始化完成: ${this.config.enabledLanguages.length}种语言, 耗时${initTime}ms`);
      /* istanbul ignore next */
      cov_6si821j0y().s[21]++;
      this.logInitializationSummary();
    } catch (error) {
      /* istanbul ignore next */
      cov_6si821j0y().s[22]++;
      console.error('❌ 语言管理器初始化失败:', error);
      /* istanbul ignore next */
      cov_6si821j0y().s[23]++;
      throw new Error(`语言管理器初始化失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_6si821j0y().b[7][0]++, error.message) :
      /* istanbul ignore next */
      (cov_6si821j0y().b[7][1]++, String(error))}`);
    }
  }
  /**
   * 加载指定语言的数据
   */
  async loadLanguageData(language) {
    /* istanbul ignore next */
    cov_6si821j0y().f[2]++;
    const config =
    /* istanbul ignore next */
    (cov_6si821j0y().s[24]++, SUPPORTED_LANGUAGES[language]);
    /* istanbul ignore next */
    cov_6si821j0y().s[25]++;
    if (!config) {
      /* istanbul ignore next */
      cov_6si821j0y().b[8][0]++;
      cov_6si821j0y().s[26]++;
      throw new Error(`不支持的语言: ${language}`);
    } else
    /* istanbul ignore next */
    {
      cov_6si821j0y().b[8][1]++;
    }
    cov_6si821j0y().s[27]++;
    console.log(`📁 加载${config.name}数据...`);
    /* istanbul ignore next */
    cov_6si821j0y().s[28]++;
    try {
      const dataFilePath =
      /* istanbul ignore next */
      (cov_6si821j0y().s[29]++, path.join(DATA_DIR, config.dataFile));
      /* istanbul ignore next */
      cov_6si821j0y().s[30]++;
      if (!fs.existsSync(dataFilePath)) {
        /* istanbul ignore next */
        cov_6si821j0y().b[9][0]++;
        cov_6si821j0y().s[31]++;
        throw new Error(`数据文件不存在: ${dataFilePath}`);
      } else
      /* istanbul ignore next */
      {
        cov_6si821j0y().b[9][1]++;
      }
      // 加载语素数据
      const morphemeData =
      /* istanbul ignore next */
      (cov_6si821j0y().s[32]++, JSON.parse(fs.readFileSync(dataFilePath, 'utf8')));
      let morphemes;
      let concepts;
      /* istanbul ignore next */
      cov_6si821j0y().s[33]++;
      if (language === LanguageCode.ZH_CN) {
        /* istanbul ignore next */
        cov_6si821j0y().b[10][0]++;
        cov_6si821j0y().s[34]++;
        // 中文使用传统格式，需要适配
        morphemes = this.adaptChineseMorphemes(morphemeData);
      } else {
        /* istanbul ignore next */
        cov_6si821j0y().b[10][1]++;
        cov_6si821j0y().s[35]++;
        // 其他语言使用v3.0格式
        morphemes = morphemeData;
        // 加载通用概念
        /* istanbul ignore next */
        cov_6si821j0y().s[36]++;
        if (config.conceptFile) {
          /* istanbul ignore next */
          cov_6si821j0y().b[11][0]++;
          const conceptFilePath =
          /* istanbul ignore next */
          (cov_6si821j0y().s[37]++, path.join(DATA_DIR, config.conceptFile));
          /* istanbul ignore next */
          cov_6si821j0y().s[38]++;
          if (fs.existsSync(conceptFilePath)) {
            /* istanbul ignore next */
            cov_6si821j0y().b[12][0]++;
            cov_6si821j0y().s[39]++;
            concepts = JSON.parse(fs.readFileSync(conceptFilePath, 'utf8'));
          } else
          /* istanbul ignore next */
          {
            cov_6si821j0y().b[12][1]++;
          }
        } else
        /* istanbul ignore next */
        {
          cov_6si821j0y().b[11][1]++;
        }
      }
      // 构建概念-语素映射
      const conceptMorphemeMapping =
      /* istanbul ignore next */
      (cov_6si821j0y().s[40]++, this.buildConceptMorphemeMapping(morphemes));
      // 创建语言数据集
      const languageDataSet =
      /* istanbul ignore next */
      (cov_6si821j0y().s[41]++, {
        language,
        morphemes,
        concepts,
        conceptMorphemeMapping,
        loadedAt: Date.now()
      });
      /* istanbul ignore next */
      cov_6si821j0y().s[42]++;
      this.languageDataSets.set(language, languageDataSet);
      /* istanbul ignore next */
      cov_6si821j0y().s[43]++;
      console.log(`✅ ${config.name}数据加载完成: ${morphemes.length}个语素`);
    } catch (error) {
      /* istanbul ignore next */
      cov_6si821j0y().s[44]++;
      throw new Error(`加载${config.name}数据失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_6si821j0y().b[13][0]++, error.message) :
      /* istanbul ignore next */
      (cov_6si821j0y().b[13][1]++, String(error))}`);
    }
  }
  /**
   * 适配中文语素数据到v3.0格式
   */
  adaptChineseMorphemes(chineseData) {
    /* istanbul ignore next */
    cov_6si821j0y().f[3]++;
    cov_6si821j0y().s[45]++;
    return chineseData.map((item, index) => {
      /* istanbul ignore next */
      cov_6si821j0y().f[4]++;
      cov_6si821j0y().s[46]++;
      return {
        morpheme_id: `zh_${
        /* istanbul ignore next */
        (cov_6si821j0y().b[14][0]++, item.id) ||
        /* istanbul ignore next */
        (cov_6si821j0y().b[14][1]++, index)}`,
        concept_id: `concept_${item.text}`,
        // 临时概念ID
        language: LanguageCode.ZH_CN,
        text: item.text,
        alternative_forms: [],
        phonetic_features: {
          ipa_transcription:
          /* istanbul ignore next */
          (cov_6si821j0y().b[15][0]++, item.language_properties?.pronunciation) ||
          /* istanbul ignore next */
          (cov_6si821j0y().b[15][1]++, ''),
          syllable_count:
          /* istanbul ignore next */
          (cov_6si821j0y().b[16][0]++, item.language_properties?.syllable_count) ||
          /* istanbul ignore next */
          (cov_6si821j0y().b[16][1]++, 1),
          tone_pattern: [],
          // 中文声调信息
          phonetic_harmony: 0.8
        },
        morphological_info: {
          pos_tag: this.mapCategoryToPOS(item.category),
          morphological_type:
          /* istanbul ignore next */
          (cov_6si821j0y().b[17][0]++, item.language_properties?.morphological_type) ||
          /* istanbul ignore next */
          (cov_6si821j0y().b[17][1]++, 'root'),
          prefixes: [],
          suffixes: []
        },
        syntactic_properties: {
          syntactic_function: ['root'],
          collocation_constraints: [],
          grammatical_features: {}
        },
        cultural_context: {
          traditionality: item.cultural_context === 'ancient' ?
          /* istanbul ignore next */
          (cov_6si821j0y().b[18][0]++, 0.9) :
          /* istanbul ignore next */
          (cov_6si821j0y().b[18][1]++, 0.3),
          modernity: item.cultural_context === 'modern' ?
          /* istanbul ignore next */
          (cov_6si821j0y().b[19][0]++, 0.9) :
          /* istanbul ignore next */
          (cov_6si821j0y().b[19][1]++, 0.3),
          formality: 0.5,
          regionality: 0.2,
          religious_sensitivity: 0.1,
          age_appropriateness: ['all'],
          cultural_tags:
          /* istanbul ignore next */
          (cov_6si821j0y().b[20][0]++, item.tags) ||
          /* istanbul ignore next */
          (cov_6si821j0y().b[20][1]++, [])
        },
        regional_variants: [],
        register_level: RegisterLevel.NEUTRAL,
        language_quality_scores: {
          naturalness:
          /* istanbul ignore next */
          (cov_6si821j0y().b[21][0]++, item.quality_metrics?.naturalness) ||
          /* istanbul ignore next */
          (cov_6si821j0y().b[21][1]++, 0.8),
          fluency: 0.9,
          authenticity: 0.9,
          aesthetic_appeal:
          /* istanbul ignore next */
          (cov_6si821j0y().b[22][0]++, item.quality_metrics?.aesthetic_appeal) ||
          /* istanbul ignore next */
          (cov_6si821j0y().b[22][1]++, 0.8),
          pronunciation_ease: 0.8,
          memorability: 0.8,
          uniqueness: 0.6,
          practicality: 0.9
        },
        cultural_appropriateness: 0.9,
        native_speaker_rating:
        /* istanbul ignore next */
        (cov_6si821j0y().b[23][0]++, item.quality_score) ||
        /* istanbul ignore next */
        (cov_6si821j0y().b[23][1]++, 0.8),
        usage_frequency:
        /* istanbul ignore next */
        (cov_6si821j0y().b[24][0]++, item.usage_frequency) ||
        /* istanbul ignore next */
        (cov_6si821j0y().b[24][1]++, 0.5),
        popularity_trend: 0.0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        version: '3.0.0',
        source: 'chinese_morphemes_adapted',
        validation_status: 'validated'
      };
    });
  }
  /**
   * 映射类别到词性标注
   */
  mapCategoryToPOS(category) {
    /* istanbul ignore next */
    cov_6si821j0y().f[5]++;
    const mapping =
    /* istanbul ignore next */
    (cov_6si821j0y().s[47]++, {
      'emotions': 'ADJ',
      'professions': 'NOUN',
      'characteristics': 'ADJ',
      'objects': 'NOUN',
      'actions': 'VERB',
      'concepts': 'NOUN'
    });
    /* istanbul ignore next */
    cov_6si821j0y().s[48]++;
    return /* istanbul ignore next */(cov_6si821j0y().b[25][0]++, mapping[category]) ||
    /* istanbul ignore next */
    (cov_6si821j0y().b[25][1]++, 'NOUN');
  }
  /**
   * 构建概念-语素映射
   */
  buildConceptMorphemeMapping(morphemes) {
    /* istanbul ignore next */
    cov_6si821j0y().f[6]++;
    const mapping =
    /* istanbul ignore next */
    (cov_6si821j0y().s[49]++, new Map());
    /* istanbul ignore next */
    cov_6si821j0y().s[50]++;
    for (const morpheme of morphemes) {
      const conceptId =
      /* istanbul ignore next */
      (cov_6si821j0y().s[51]++, morpheme.concept_id);
      /* istanbul ignore next */
      cov_6si821j0y().s[52]++;
      if (!mapping.has(conceptId)) {
        /* istanbul ignore next */
        cov_6si821j0y().b[26][0]++;
        cov_6si821j0y().s[53]++;
        mapping.set(conceptId, []);
      } else
      /* istanbul ignore next */
      {
        cov_6si821j0y().b[26][1]++;
      }
      cov_6si821j0y().s[54]++;
      mapping.get(conceptId).push(morpheme);
    }
    /* istanbul ignore next */
    cov_6si821j0y().s[55]++;
    return mapping;
  }
  /**
   * 构建概念缓存
   */
  buildConceptsCache() {
    /* istanbul ignore next */
    cov_6si821j0y().f[7]++;
    cov_6si821j0y().s[56]++;
    for (const dataSet of this.languageDataSets.values()) {
      /* istanbul ignore next */
      cov_6si821j0y().s[57]++;
      if (dataSet.concepts) {
        /* istanbul ignore next */
        cov_6si821j0y().b[27][0]++;
        cov_6si821j0y().s[58]++;
        for (const concept of dataSet.concepts) {
          /* istanbul ignore next */
          cov_6si821j0y().s[59]++;
          this.conceptsCache.set(concept.concept_id, concept);
        }
      } else
      /* istanbul ignore next */
      {
        cov_6si821j0y().b[27][1]++;
      }
    }
    /* istanbul ignore next */
    cov_6si821j0y().s[60]++;
    console.log(`📊 概念缓存构建完成: ${this.conceptsCache.size}个概念`);
  }
  /**
   * 获取指定语言的语素
   */
  getMorphemesByLanguage(language) {
    /* istanbul ignore next */
    cov_6si821j0y().f[8]++;
    const dataSet =
    /* istanbul ignore next */
    (cov_6si821j0y().s[61]++, this.languageDataSets.get(language));
    /* istanbul ignore next */
    cov_6si821j0y().s[62]++;
    if (!dataSet) {
      /* istanbul ignore next */
      cov_6si821j0y().b[28][0]++;
      cov_6si821j0y().s[63]++;
      throw new Error(`语言数据未加载: ${language}`);
    } else
    /* istanbul ignore next */
    {
      cov_6si821j0y().b[28][1]++;
    }
    cov_6si821j0y().s[64]++;
    return dataSet.morphemes;
  }
  /**
   * 根据概念ID获取指定语言的语素
   */
  getMorphemesByConceptAndLanguage(conceptId, language) {
    /* istanbul ignore next */
    cov_6si821j0y().f[9]++;
    const dataSet =
    /* istanbul ignore next */
    (cov_6si821j0y().s[65]++, this.languageDataSets.get(language));
    /* istanbul ignore next */
    cov_6si821j0y().s[66]++;
    if (!dataSet) {
      /* istanbul ignore next */
      cov_6si821j0y().b[29][0]++;
      cov_6si821j0y().s[67]++;
      throw new Error(`语言数据未加载: ${language}`);
    } else
    /* istanbul ignore next */
    {
      cov_6si821j0y().b[29][1]++;
    }
    cov_6si821j0y().s[68]++;
    return /* istanbul ignore next */(cov_6si821j0y().b[30][0]++, dataSet.conceptMorphemeMapping.get(conceptId)) ||
    /* istanbul ignore next */
    (cov_6si821j0y().b[30][1]++, []);
  }
  /**
   * 获取通用概念
   */
  getConcept(conceptId) {
    /* istanbul ignore next */
    cov_6si821j0y().f[10]++;
    cov_6si821j0y().s[69]++;
    return this.conceptsCache.get(conceptId);
  }
  /**
   * 获取所有支持的语言
   */
  getSupportedLanguages() {
    /* istanbul ignore next */
    cov_6si821j0y().f[11]++;
    cov_6si821j0y().s[70]++;
    return this.config.enabledLanguages;
  }
  /**
   * 检查语言是否支持
   */
  isLanguageSupported(language) {
    /* istanbul ignore next */
    cov_6si821j0y().f[12]++;
    cov_6si821j0y().s[71]++;
    return this.languageDataSets.has(language);
  }
  /**
   * 获取语言统计信息
   */
  getLanguageStats(language) {
    /* istanbul ignore next */
    cov_6si821j0y().f[13]++;
    const dataSet =
    /* istanbul ignore next */
    (cov_6si821j0y().s[72]++, this.languageDataSets.get(language));
    /* istanbul ignore next */
    cov_6si821j0y().s[73]++;
    if (!dataSet) {
      /* istanbul ignore next */
      cov_6si821j0y().b[31][0]++;
      cov_6si821j0y().s[74]++;
      return {
        morphemeCount: 0,
        conceptCount: 0
      };
    } else
    /* istanbul ignore next */
    {
      cov_6si821j0y().b[31][1]++;
    }
    cov_6si821j0y().s[75]++;
    return {
      morphemeCount: dataSet.morphemes.length,
      conceptCount:
      /* istanbul ignore next */
      (cov_6si821j0y().b[32][0]++, dataSet.concepts?.length) ||
      /* istanbul ignore next */
      (cov_6si821j0y().b[32][1]++, 0)
    };
  }
  /**
   * 重新加载语言数据
   */
  async reloadLanguage(language) {
    /* istanbul ignore next */
    cov_6si821j0y().f[14]++;
    cov_6si821j0y().s[76]++;
    console.log(`🔄 重新加载${language}数据...`);
    /* istanbul ignore next */
    cov_6si821j0y().s[77]++;
    await this.loadLanguageData(language);
    /* istanbul ignore next */
    cov_6si821j0y().s[78]++;
    this.buildConceptsCache();
  }
  /**
   * 输出初始化摘要
   */
  logInitializationSummary() {
    /* istanbul ignore next */
    cov_6si821j0y().f[15]++;
    cov_6si821j0y().s[79]++;
    console.log('\n🌍 多语种语言管理器初始化摘要:');
    /* istanbul ignore next */
    cov_6si821j0y().s[80]++;
    console.log(`   默认语言: ${this.config.defaultLanguage}`);
    /* istanbul ignore next */
    cov_6si821j0y().s[81]++;
    console.log(`   启用语言: ${this.config.enabledLanguages.join(', ')}`);
    /* istanbul ignore next */
    cov_6si821j0y().s[82]++;
    console.log(`   概念总数: ${this.conceptsCache.size}`);
    /* istanbul ignore next */
    cov_6si821j0y().s[83]++;
    for (const language of this.config.enabledLanguages) {
      const stats =
      /* istanbul ignore next */
      (cov_6si821j0y().s[84]++, this.getLanguageStats(language));
      /* istanbul ignore next */
      cov_6si821j0y().s[85]++;
      console.log(`   ${language}: ${stats.morphemeCount}个语素, ${stats.conceptCount}个概念`);
    }
  }
  /**
   * 检查是否已初始化
   */
  isReady() {
    /* istanbul ignore next */
    cov_6si821j0y().f[16]++;
    cov_6si821j0y().s[86]++;
    return this.isInitialized;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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