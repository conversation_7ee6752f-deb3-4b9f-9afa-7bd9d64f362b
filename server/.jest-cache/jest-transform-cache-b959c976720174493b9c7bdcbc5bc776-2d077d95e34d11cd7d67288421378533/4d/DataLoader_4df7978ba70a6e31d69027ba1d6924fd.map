{"version": 3, "names": ["cov_2i3pzto8wf", "actualCoverage", "readFile", "readdir", "stat", "join", "extname", "watch", "DATA_DIR", "s", "process", "cwd", "SUPPORTED_FORMATS", "CURRENT_VERSION", "VALIDATION_CONFIG", "semantic_vector_dimension", "min_quality_score", "max_quality_score", "required_fields", "DataLoader", "config", "cache", "Map", "watchers", "loadPromise", "constructor", "b", "f", "dataDir", "enableHotReload", "enableValidation", "enableCache", "cacheTTL", "maxRetries", "loadAll", "_performLoad", "result", "startTime", "Date", "now", "dataFiles", "_scanDataFiles", "allMorphemes", "filePath", "morphemes", "_loadDataFile", "push", "validation", "_validateData", "passed", "errors", "warnings", "validated_count", "length", "validation_time", "stats", "_calculateStats", "_setupHotReload", "timestamp", "console", "log", "load_time", "error", "Error", "message", "String", "files", "file", "fileStat", "isFile", "includes", "cached", "get", "data", "content", "parsed", "JSON", "parse", "Array", "isArray", "split", "filter", "line", "trim", "map", "set", "i", "morpheme", "prefix", "id", "field", "undefined", "usage_frequency", "quality_score", "semantic_vector", "version", "warn", "loadTime", "by_category", "by_context", "total_quality", "category", "<PERSON><PERSON>ey", "cultural_context", "traditionality", "formality", "total_count", "avg_quality", "path", "watcher", "close", "clear", "eventType", "delete", "_debounceReload", "size", "timeout", "clearTimeout", "setTimeout", "catch", "_inferMorphologicalType", "typeMap", "destroy"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts"], "sourcesContent": ["/**\n * 数据加载器\n *\n * 负责从文件系统加载语素数据，支持v3.0多语种架构、数据验证和管理。\n * 实现了热重载、缓存优化和错误恢复机制。\n *\n * @fileoverview 数据加载和管理核心模块 - v3.0多语种架构\n * @version 3.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport { readFile, readdir, stat } from 'fs/promises'\nimport { join, extname } from 'path'\nimport { watch } from 'fs'\n\n// 类型导入\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\nimport { SUPPORTED_VERSIONS } from '../../types/common'\n\n// ============================================================================\n// 配置常量\n// ============================================================================\n\n/** 数据文件目录路径 */\nconst DATA_DIR = join(process.cwd(), 'data')\n\n/** 支持的数据文件格式 */\nconst SUPPORTED_FORMATS = ['.json', '.jsonl'] as const\n\n/** 当前数据版本 */\nconst CURRENT_VERSION = '3.0.0' as const\n\n/** 数据验证配置 */\nconst VALIDATION_CONFIG = {\n  /** 语义向量维度 */\n  semantic_vector_dimension: 20,\n  /** 最小质量评分 */\n  min_quality_score: 0.0,\n  /** 最大质量评分 */\n  max_quality_score: 1.0,\n  /** 必需字段 */\n  required_fields: [\n    'id', 'text', 'category', 'subcategory', 'cultural_context',\n    'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n    'language_properties', 'quality_metrics', 'created_at', 'source', 'version'\n  ] as const\n} as const\n\n// ============================================================================\n// 接口定义\n// ============================================================================\n\n/**\n * 数据加载配置接口\n */\nexport interface DataLoaderConfig {\n  /** 数据目录路径 */\n  dataDir?: string\n  /** 是否启用热重载 */\n  enableHotReload?: boolean\n  /** 是否启用数据验证 */\n  enableValidation?: boolean\n  /** 是否启用缓存 */\n  enableCache?: boolean\n  /** 缓存TTL (秒) */\n  cacheTTL?: number\n  /** 最大重试次数 */\n  maxRetries?: number\n}\n\n/**\n * 数据加载结果接口\n */\nexport interface DataLoadResult {\n  /** 加载的语素数据 */\n  morphemes: Morpheme[]\n  /** 加载统计信息 */\n  stats: {\n    total_count: number\n    by_category: Record<string, number>\n    by_context: Record<string, number>\n    avg_quality: number\n    load_time: number\n  }\n  /** 验证结果 */\n  validation: {\n    passed: boolean\n    errors: string[]\n    warnings: string[]\n  }\n  /** 加载时间戳 */\n  timestamp: number\n}\n\n/**\n * 数据验证结果接口\n */\nexport interface ValidationResult {\n  /** 验证是否通过 */\n  passed: boolean\n  /** 错误信息 */\n  errors: string[]\n  /** 警告信息 */\n  warnings: string[]\n  /** 验证的数据项数量 */\n  validated_count: number\n  /** 验证耗时 (毫秒) */\n  validation_time: number\n}\n\n// ============================================================================\n// 数据加载器类\n// ============================================================================\n\n/**\n * 数据加载器类\n * \n * 提供语素数据的加载、验证、缓存和热重载功能\n */\nexport class DataLoader {\n  private config: Required<DataLoaderConfig>\n  private cache: Map<string, { data: Morpheme[], timestamp: number }> = new Map()\n  private watchers: Map<string, any> = new Map()\n  private loadPromise: Promise<DataLoadResult> | null = null\n\n  /**\n   * 构造函数\n   * \n   * @param config 数据加载配置\n   */\n  constructor(config: DataLoaderConfig = {}) {\n    this.config = {\n      dataDir: config.dataDir || DATA_DIR,\n      enableHotReload: config.enableHotReload ?? true,\n      enableValidation: config.enableValidation ?? true,\n      enableCache: config.enableCache ?? true,\n      cacheTTL: config.cacheTTL ?? 3600, // 1小时\n      maxRetries: config.maxRetries ?? 3\n    }\n  }\n\n  /**\n   * 加载所有语素数据\n   * \n   * @returns 数据加载结果\n   */\n  async loadAll(): Promise<DataLoadResult> {\n    // 防止并发加载\n    if (this.loadPromise) {\n      return this.loadPromise\n    }\n\n    this.loadPromise = this._performLoad()\n    \n    try {\n      const result = await this.loadPromise\n      return result\n    } finally {\n      this.loadPromise = null\n    }\n  }\n\n  /**\n   * 执行数据加载\n   * \n   * @private\n   * @returns 数据加载结果\n   */\n  private async _performLoad(): Promise<DataLoadResult> {\n    const startTime = Date.now()\n    \n    try {\n      // 1. 扫描数据文件\n      const dataFiles = await this._scanDataFiles()\n      \n      // 2. 加载数据文件\n      const allMorphemes: Morpheme[] = []\n\n      for (const filePath of dataFiles) {\n        const morphemes = await this._loadDataFile(filePath)\n        allMorphemes.push(...morphemes)\n      }\n\n      // 3. 数据验证\n      const validation = this.config.enableValidation \n        ? await this._validateData(allMorphemes)\n        : { passed: true, errors: [], warnings: [], validated_count: allMorphemes.length, validation_time: 0 }\n\n      // 4. 计算统计信息\n      const stats = this._calculateStats(allMorphemes, Date.now() - startTime)\n\n      // 5. 设置热重载\n      if (this.config.enableHotReload) {\n        await this._setupHotReload(dataFiles)\n      }\n\n      const result: DataLoadResult = {\n        morphemes: allMorphemes,\n        stats,\n        validation,\n        timestamp: Date.now()\n      }\n\n      console.log(`✅ 数据加载完成: ${allMorphemes.length}个语素, 耗时${stats.load_time}ms`)\n      \n      return result\n\n    } catch (error) {\n      console.error('❌ 数据加载失败:', error)\n      throw new Error(`数据加载失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 扫描数据文件\n   * \n   * @private\n   * @returns 数据文件路径列表\n   */\n  private async _scanDataFiles(): Promise<string[]> {\n    try {\n      const files = await readdir(this.config.dataDir)\n      const dataFiles: string[] = []\n\n      for (const file of files) {\n        const filePath = join(this.config.dataDir, file)\n        const fileStat = await stat(filePath)\n\n        // 只加载语素数据文件，跳过其他文件\n        if (fileStat.isFile() &&\n            SUPPORTED_FORMATS.includes(extname(file) as any) &&\n            (file.includes('morpheme') || file.includes('语素'))) {\n          dataFiles.push(filePath)\n        }\n      }\n\n      if (dataFiles.length === 0) {\n        throw new Error(`未找到数据文件，目录: ${this.config.dataDir}`)\n      }\n\n      console.log(`📁 发现${dataFiles.length}个数据文件`)\n      return dataFiles\n\n    } catch (error) {\n      throw new Error(`扫描数据文件失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 加载单个数据文件\n   * \n   * @private\n   * @param filePath 文件路径\n   * @returns 语素数据数组\n   */\n  private async _loadDataFile(filePath: string): Promise<Morpheme[]> {\n    try {\n      // 检查缓存\n      if (this.config.enableCache) {\n        const cached = this.cache.get(filePath)\n        if (cached && Date.now() - cached.timestamp < this.config.cacheTTL * 1000) {\n          console.log(`📋 使用缓存数据: ${filePath}`)\n          return cached.data\n        }\n      }\n\n      // 读取文件内容\n      const content = await readFile(filePath, 'utf-8')\n      let morphemes: Morpheme[]\n\n      // 根据文件格式解析\n      if (extname(filePath) === '.json') {\n        const parsed = JSON.parse(content)\n\n        // 支持两种格式：直接数组或包含morphemes字段的对象\n        if (Array.isArray(parsed)) {\n          morphemes = parsed\n        } else if (parsed.morphemes && Array.isArray(parsed.morphemes)) {\n          morphemes = parsed.morphemes\n          console.log(`📋 检测到嵌套格式，提取${morphemes.length}个语素`)\n        } else {\n          throw new Error(`无效的JSON格式: 期望数组或包含morphemes字段的对象`)\n        }\n      } else if (extname(filePath) === '.jsonl') {\n        morphemes = content\n          .split('\\n')\n          .filter(line => line.trim())\n          .map(line => JSON.parse(line))\n      } else {\n        throw new Error(`不支持的文件格式: ${extname(filePath)}`)\n      }\n\n      // 更新缓存\n      if (this.config.enableCache) {\n        this.cache.set(filePath, {\n          data: morphemes,\n          timestamp: Date.now()\n        })\n      }\n\n      console.log(`📄 加载文件: ${filePath} (${morphemes.length}个语素)`)\n      return morphemes\n\n    } catch (error) {\n      throw new Error(`加载文件失败 ${filePath}: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 验证数据完整性和正确性\n   * \n   * @private\n   * @param morphemes 语素数据数组\n   * @returns 验证结果\n   */\n  private async _validateData(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: string[] = []\n    const warnings: string[] = []\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      const prefix = `语素[${i}](${morpheme.id || 'unknown'})`\n\n      // 验证必需字段\n      for (const field of VALIDATION_CONFIG.required_fields) {\n        if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined) {\n          errors.push(`${prefix}: 缺少必需字段 '${field}'`)\n        }\n      }\n\n      // 验证数据类型和范围\n      if (typeof morpheme.usage_frequency !== 'number' || \n          morpheme.usage_frequency < 0 || morpheme.usage_frequency > 1) {\n        errors.push(`${prefix}: usage_frequency 必须是 [0-1] 范围内的数字`)\n      }\n\n      if (typeof morpheme.quality_score !== 'number' || \n          morpheme.quality_score < VALIDATION_CONFIG.min_quality_score || \n          morpheme.quality_score > VALIDATION_CONFIG.max_quality_score) {\n        errors.push(`${prefix}: quality_score 必须是 [${VALIDATION_CONFIG.min_quality_score}-${VALIDATION_CONFIG.max_quality_score}] 范围内的数字`)\n      }\n\n      // 验证语义向量\n      if (!Array.isArray(morpheme.semantic_vector) || \n          morpheme.semantic_vector.length !== VALIDATION_CONFIG.semantic_vector_dimension) {\n        errors.push(`${prefix}: semantic_vector 必须是长度为 ${VALIDATION_CONFIG.semantic_vector_dimension} 的数字数组`)\n      }\n\n      // 验证版本\n      if (morpheme.version && morpheme.version !== CURRENT_VERSION) {\n        warnings.push(`${prefix}: 版本 '${morpheme.version}' 不是当前版本 ${CURRENT_VERSION}，建议升级数据`)\n      }\n    }\n\n    const validation_time = Date.now() - startTime\n    const passed = errors.length === 0\n\n    if (!passed) {\n      console.warn(`⚠️ 数据验证发现 ${errors.length} 个错误, ${warnings.length} 个警告`)\n    } else {\n      console.log(`✅ 数据验证通过: ${morphemes.length}个语素, 耗时${validation_time}ms`)\n    }\n\n    return {\n      passed,\n      errors,\n      warnings,\n      validated_count: morphemes.length,\n      validation_time\n    }\n  }\n\n  /**\n   * 计算统计信息\n   * \n   * @private\n   * @param morphemes 语素数据数组\n   * @param loadTime 加载耗时\n   * @returns 统计信息\n   */\n  private _calculateStats(morphemes: Morpheme[], loadTime: number) {\n    const by_category: Record<string, number> = {}\n    const by_context: Record<string, number> = {}\n    let total_quality = 0\n\n    for (const morpheme of morphemes) {\n      // 按类别统计\n      by_category[morpheme.category] = (by_category[morpheme.category] || 0) + 1\n      \n      // 按文化语境统计 (支持v3.0多维度文化语境)\n      const contextKey = typeof morpheme.cultural_context === 'string'\n        ? morpheme.cultural_context\n        : `${morpheme.cultural_context.traditionality}_${morpheme.cultural_context.formality}`\n      by_context[contextKey] = (by_context[contextKey] || 0) + 1\n      \n      // 累计质量评分\n      total_quality += morpheme.quality_score\n    }\n\n    return {\n      total_count: morphemes.length,\n      by_category,\n      by_context,\n      avg_quality: morphemes.length > 0 ? total_quality / morphemes.length : 0,\n      load_time: loadTime\n    }\n  }\n\n  /**\n   * 设置热重载监听\n   * \n   * @private\n   * @param dataFiles 数据文件路径列表\n   */\n  private async _setupHotReload(dataFiles: string[]): Promise<void> {\n    // 清理现有监听器\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // 设置新的监听器\n    for (const filePath of dataFiles) {\n      try {\n        const watcher = watch(filePath, (eventType) => {\n          if (eventType === 'change') {\n            console.log(`🔄 检测到文件变化: ${filePath}`)\n            // 清除缓存\n            this.cache.delete(filePath)\n            // 触发重新加载 (可以添加防抖逻辑)\n            this._debounceReload()\n          }\n        })\n\n        this.watchers.set(filePath, watcher)\n      } catch (error) {\n        console.warn(`⚠️ 无法监听文件变化: ${filePath}`, error)\n      }\n    }\n\n    console.log(`👁️ 热重载已启用，监听 ${this.watchers.size} 个文件`)\n  }\n\n  /**\n   * 防抖重新加载\n   * \n   * @private\n   */\n  private _debounceReload = (() => {\n    let timeout: NodeJS.Timeout | null = null\n    return () => {\n      if (timeout) {\n        clearTimeout(timeout)\n      }\n      timeout = setTimeout(() => {\n        console.log('🔄 执行热重载...')\n        this.loadAll().catch(error => {\n          console.error('❌ 热重载失败:', error)\n        })\n      }, 1000) // 1秒防抖\n    }\n  })()\n\n\n\n  /**\n   * 推断词法类型\n   *\n   * @private\n   * @param category 语素类别\n   * @returns 词法类型\n   */\n  private _inferMorphologicalType(category: string): string {\n    const typeMap: Record<string, string> = {\n      'emotions': '形容词',\n      'professions': '名词',\n      'characteristics': '形容词',\n      'objects': '名词',\n      'actions': '动词',\n      'concepts': '名词'\n    }\n\n    return typeMap[category] || '未知'\n  }\n\n  /**\n   * 清理资源\n   */\n  destroy(): void {\n    // 关闭文件监听器\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // 清空缓存\n    this.cache.clear()\n\n    console.log('🧹 数据加载器资源已清理')\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBO;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AArBP;;;;;;;;;;;AAYA,SAASE,QAAQ,EAAEC,OAAO,EAAEC,IAAI,QAAQ,aAAa;AACrD,SAASC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,SAASC,KAAK,QAAQ,IAAI;AAM1B;AACA;AACA;AAEA;AACA,MAAMC,QAAQ;AAAA;AAAA,CAAAR,cAAA,GAAAS,CAAA,OAAGJ,IAAI,CAACK,OAAO,CAACC,GAAG,EAAE,EAAE,MAAM,CAAC;AAE5C;AACA,MAAMC,iBAAiB;AAAA;AAAA,CAAAZ,cAAA,GAAAS,CAAA,OAAG,CAAC,OAAO,EAAE,QAAQ,CAAU;AAEtD;AACA,MAAMI,eAAe;AAAA;AAAA,CAAAb,cAAA,GAAAS,CAAA,OAAG,OAAgB;AAExC;AACA,MAAMK,iBAAiB;AAAA;AAAA,CAAAd,cAAA,GAAAS,CAAA,OAAG;EACxB;EACAM,yBAAyB,EAAE,EAAE;EAC7B;EACAC,iBAAiB,EAAE,GAAG;EACtB;EACAC,iBAAiB,EAAE,GAAG;EACtB;EACAC,eAAe,EAAE,CACf,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,EAC7D,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS;CAErE;AAgEV;AACA;AACA;AAEA;;;;;AAKA,OAAM,MAAOC,UAAU;EACbC,MAAM;EACNC,KAAK;EAAA;EAAA,CAAArB,cAAA,GAAAS,CAAA,OAAyD,IAAIa,GAAG,EAAE;EACvEC,QAAQ;EAAA;EAAA,CAAAvB,cAAA,GAAAS,CAAA,OAAqB,IAAIa,GAAG,EAAE;EACtCE,WAAW;EAAA;EAAA,CAAAxB,cAAA,GAAAS,CAAA,OAAmC,IAAI;EAE1D;;;;;EAKAgB,YAAYL,MAAA;EAAA;EAAA,CAAApB,cAAA,GAAA0B,CAAA,UAA2B,EAAE;IAAA;IAAA1B,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAS,CAAA;IACvC,IAAI,CAACW,MAAM,GAAG;MACZQ,OAAO;MAAE;MAAA,CAAA5B,cAAA,GAAA0B,CAAA,UAAAN,MAAM,CAACQ,OAAO;MAAA;MAAA,CAAA5B,cAAA,GAAA0B,CAAA,UAAIlB,QAAQ;MACnCqB,eAAe;MAAE;MAAA,CAAA7B,cAAA,GAAA0B,CAAA,UAAAN,MAAM,CAACS,eAAe;MAAA;MAAA,CAAA7B,cAAA,GAAA0B,CAAA,UAAI,IAAI;MAC/CI,gBAAgB;MAAE;MAAA,CAAA9B,cAAA,GAAA0B,CAAA,UAAAN,MAAM,CAACU,gBAAgB;MAAA;MAAA,CAAA9B,cAAA,GAAA0B,CAAA,UAAI,IAAI;MACjDK,WAAW;MAAE;MAAA,CAAA/B,cAAA,GAAA0B,CAAA,UAAAN,MAAM,CAACW,WAAW;MAAA;MAAA,CAAA/B,cAAA,GAAA0B,CAAA,UAAI,IAAI;MACvCM,QAAQ;MAAE;MAAA,CAAAhC,cAAA,GAAA0B,CAAA,UAAAN,MAAM,CAACY,QAAQ;MAAA;MAAA,CAAAhC,cAAA,GAAA0B,CAAA,UAAI,IAAI;MAAE;MACnCO,UAAU;MAAE;MAAA,CAAAjC,cAAA,GAAA0B,CAAA,UAAAN,MAAM,CAACa,UAAU;MAAA;MAAA,CAAAjC,cAAA,GAAA0B,CAAA,UAAI,CAAC;KACnC;EACH;EAEA;;;;;EAKA,MAAMQ,OAAOA,CAAA;IAAA;IAAAlC,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAS,CAAA;IACX;IACA,IAAI,IAAI,CAACe,WAAW,EAAE;MAAA;MAAAxB,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAS,CAAA;MACpB,OAAO,IAAI,CAACe,WAAW;IACzB,CAAC;IAAA;IAAA;MAAAxB,cAAA,GAAA0B,CAAA;IAAA;IAAA1B,cAAA,GAAAS,CAAA;IAED,IAAI,CAACe,WAAW,GAAG,IAAI,CAACW,YAAY,EAAE;IAAA;IAAAnC,cAAA,GAAAS,CAAA;IAEtC,IAAI;MACF,MAAM2B,MAAM;MAAA;MAAA,CAAApC,cAAA,GAAAS,CAAA,QAAG,MAAM,IAAI,CAACe,WAAW;MAAA;MAAAxB,cAAA,GAAAS,CAAA;MACrC,OAAO2B,MAAM;IACf,CAAC,SAAS;MAAA;MAAApC,cAAA,GAAAS,CAAA;MACR,IAAI,CAACe,WAAW,GAAG,IAAI;IACzB;EACF;EAEA;;;;;;EAMQ,MAAMW,YAAYA,CAAA;IAAA;IAAAnC,cAAA,GAAA2B,CAAA;IACxB,MAAMU,SAAS;IAAA;IAAA,CAAArC,cAAA,GAAAS,CAAA,QAAG6B,IAAI,CAACC,GAAG,EAAE;IAAA;IAAAvC,cAAA,GAAAS,CAAA;IAE5B,IAAI;MACF;MACA,MAAM+B,SAAS;MAAA;MAAA,CAAAxC,cAAA,GAAAS,CAAA,QAAG,MAAM,IAAI,CAACgC,cAAc,EAAE;MAE7C;MACA,MAAMC,YAAY;MAAA;MAAA,CAAA1C,cAAA,GAAAS,CAAA,QAAe,EAAE;MAAA;MAAAT,cAAA,GAAAS,CAAA;MAEnC,KAAK,MAAMkC,QAAQ,IAAIH,SAAS,EAAE;QAChC,MAAMI,SAAS;QAAA;QAAA,CAAA5C,cAAA,GAAAS,CAAA,QAAG,MAAM,IAAI,CAACoC,aAAa,CAACF,QAAQ,CAAC;QAAA;QAAA3C,cAAA,GAAAS,CAAA;QACpDiC,YAAY,CAACI,IAAI,CAAC,GAAGF,SAAS,CAAC;MACjC;MAEA;MACA,MAAMG,UAAU;MAAA;MAAA,CAAA/C,cAAA,GAAAS,CAAA,QAAG,IAAI,CAACW,MAAM,CAACU,gBAAgB;MAAA;MAAA,CAAA9B,cAAA,GAAA0B,CAAA,UAC3C,MAAM,IAAI,CAACsB,aAAa,CAACN,YAAY,CAAC;MAAA;MAAA,CAAA1C,cAAA,GAAA0B,CAAA,UACtC;QAAEuB,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,eAAe,EAAEV,YAAY,CAACW,MAAM;QAAEC,eAAe,EAAE;MAAC,CAAE;MAExG;MACA,MAAMC,KAAK;MAAA;MAAA,CAAAvD,cAAA,GAAAS,CAAA,QAAG,IAAI,CAAC+C,eAAe,CAACd,YAAY,EAAEJ,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,CAAC;MAExE;MAAA;MAAArC,cAAA,GAAAS,CAAA;MACA,IAAI,IAAI,CAACW,MAAM,CAACS,eAAe,EAAE;QAAA;QAAA7B,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAC/B,MAAM,IAAI,CAACgD,eAAe,CAACjB,SAAS,CAAC;MACvC,CAAC;MAAA;MAAA;QAAAxC,cAAA,GAAA0B,CAAA;MAAA;MAED,MAAMU,MAAM;MAAA;MAAA,CAAApC,cAAA,GAAAS,CAAA,QAAmB;QAC7BmC,SAAS,EAAEF,YAAY;QACvBa,KAAK;QACLR,UAAU;QACVW,SAAS,EAAEpB,IAAI,CAACC,GAAG;OACpB;MAAA;MAAAvC,cAAA,GAAAS,CAAA;MAEDkD,OAAO,CAACC,GAAG,CAAC,aAAalB,YAAY,CAACW,MAAM,UAAUE,KAAK,CAACM,SAAS,IAAI,CAAC;MAAA;MAAA7D,cAAA,GAAAS,CAAA;MAE1E,OAAO2B,MAAM;IAEf,CAAC,CAAC,OAAO0B,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAS,CAAA;MACdkD,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MAAA;MAAA9D,cAAA,GAAAS,CAAA;MACjC,MAAM,IAAIsD,KAAK,CAAC,WAAWD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAA/D,cAAA,GAAA0B,CAAA,WAAGoC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAhE,cAAA,GAAA0B,CAAA,WAAGuC,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IACtF;EACF;EAEA;;;;;;EAMQ,MAAMrB,cAAcA,CAAA;IAAA;IAAAzC,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAS,CAAA;IAC1B,IAAI;MACF,MAAMyD,KAAK;MAAA;MAAA,CAAAlE,cAAA,GAAAS,CAAA,QAAG,MAAMN,OAAO,CAAC,IAAI,CAACiB,MAAM,CAACQ,OAAO,CAAC;MAChD,MAAMY,SAAS;MAAA;MAAA,CAAAxC,cAAA,GAAAS,CAAA,QAAa,EAAE;MAAA;MAAAT,cAAA,GAAAS,CAAA;MAE9B,KAAK,MAAM0D,IAAI,IAAID,KAAK,EAAE;QACxB,MAAMvB,QAAQ;QAAA;QAAA,CAAA3C,cAAA,GAAAS,CAAA,QAAGJ,IAAI,CAAC,IAAI,CAACe,MAAM,CAACQ,OAAO,EAAEuC,IAAI,CAAC;QAChD,MAAMC,QAAQ;QAAA;QAAA,CAAApE,cAAA,GAAAS,CAAA,QAAG,MAAML,IAAI,CAACuC,QAAQ,CAAC;QAErC;QAAA;QAAA3C,cAAA,GAAAS,CAAA;QACA;QAAI;QAAA,CAAAT,cAAA,GAAA0B,CAAA,WAAA0C,QAAQ,CAACC,MAAM,EAAE;QAAA;QAAA,CAAArE,cAAA,GAAA0B,CAAA,WACjBd,iBAAiB,CAAC0D,QAAQ,CAAChE,OAAO,CAAC6D,IAAI,CAAQ,CAAC;QAC/C;QAAA,CAAAnE,cAAA,GAAA0B,CAAA,WAAAyC,IAAI,CAACG,QAAQ,CAAC,UAAU,CAAC;QAAA;QAAA,CAAAtE,cAAA,GAAA0B,CAAA,WAAIyC,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAC,EAAE;UAAA;UAAAtE,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UACtD+B,SAAS,CAACM,IAAI,CAACH,QAAQ,CAAC;QAC1B,CAAC;QAAA;QAAA;UAAA3C,cAAA,GAAA0B,CAAA;QAAA;MACH;MAAC;MAAA1B,cAAA,GAAAS,CAAA;MAED,IAAI+B,SAAS,CAACa,MAAM,KAAK,CAAC,EAAE;QAAA;QAAArD,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAC1B,MAAM,IAAIsD,KAAK,CAAC,eAAe,IAAI,CAAC3C,MAAM,CAACQ,OAAO,EAAE,CAAC;MACvD,CAAC;MAAA;MAAA;QAAA5B,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MAEDkD,OAAO,CAACC,GAAG,CAAC,QAAQpB,SAAS,CAACa,MAAM,OAAO,CAAC;MAAA;MAAArD,cAAA,GAAAS,CAAA;MAC5C,OAAO+B,SAAS;IAElB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAS,CAAA;MACd,MAAM,IAAIsD,KAAK,CAAC,aAAaD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAA/D,cAAA,GAAA0B,CAAA,WAAGoC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAhE,cAAA,GAAA0B,CAAA,WAAGuC,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IACxF;EACF;EAEA;;;;;;;EAOQ,MAAMjB,aAAaA,CAACF,QAAgB;IAAA;IAAA3C,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAS,CAAA;IAC1C,IAAI;MAAA;MAAAT,cAAA,GAAAS,CAAA;MACF;MACA,IAAI,IAAI,CAACW,MAAM,CAACW,WAAW,EAAE;QAAA;QAAA/B,cAAA,GAAA0B,CAAA;QAC3B,MAAM6C,MAAM;QAAA;QAAA,CAAAvE,cAAA,GAAAS,CAAA,QAAG,IAAI,CAACY,KAAK,CAACmD,GAAG,CAAC7B,QAAQ,CAAC;QAAA;QAAA3C,cAAA,GAAAS,CAAA;QACvC;QAAI;QAAA,CAAAT,cAAA,GAAA0B,CAAA,WAAA6C,MAAM;QAAA;QAAA,CAAAvE,cAAA,GAAA0B,CAAA,WAAIY,IAAI,CAACC,GAAG,EAAE,GAAGgC,MAAM,CAACb,SAAS,GAAG,IAAI,CAACtC,MAAM,CAACY,QAAQ,GAAG,IAAI,GAAE;UAAA;UAAAhC,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UACzEkD,OAAO,CAACC,GAAG,CAAC,cAAcjB,QAAQ,EAAE,CAAC;UAAA;UAAA3C,cAAA,GAAAS,CAAA;UACrC,OAAO8D,MAAM,CAACE,IAAI;QACpB,CAAC;QAAA;QAAA;UAAAzE,cAAA,GAAA0B,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAA1B,cAAA,GAAA0B,CAAA;MAAA;MAED;MACA,MAAMgD,OAAO;MAAA;MAAA,CAAA1E,cAAA,GAAAS,CAAA,QAAG,MAAMP,QAAQ,CAACyC,QAAQ,EAAE,OAAO,CAAC;MACjD,IAAIC,SAAqB;MAEzB;MAAA;MAAA5C,cAAA,GAAAS,CAAA;MACA,IAAIH,OAAO,CAACqC,QAAQ,CAAC,KAAK,OAAO,EAAE;QAAA;QAAA3C,cAAA,GAAA0B,CAAA;QACjC,MAAMiD,MAAM;QAAA;QAAA,CAAA3E,cAAA,GAAAS,CAAA,QAAGmE,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;QAElC;QAAA;QAAA1E,cAAA,GAAAS,CAAA;QACA,IAAIqE,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UACzBmC,SAAS,GAAG+B,MAAM;QACpB,CAAC,MAAM;UAAA;UAAA3E,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UAAA;UAAI;UAAA,CAAAT,cAAA,GAAA0B,CAAA,WAAAiD,MAAM,CAAC/B,SAAS;UAAA;UAAA,CAAA5C,cAAA,GAAA0B,CAAA,WAAIoD,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC/B,SAAS,CAAC,GAAE;YAAA;YAAA5C,cAAA,GAAA0B,CAAA;YAAA1B,cAAA,GAAAS,CAAA;YAC9DmC,SAAS,GAAG+B,MAAM,CAAC/B,SAAS;YAAA;YAAA5C,cAAA,GAAAS,CAAA;YAC5BkD,OAAO,CAACC,GAAG,CAAC,gBAAgBhB,SAAS,CAACS,MAAM,KAAK,CAAC;UACpD,CAAC,MAAM;YAAA;YAAArD,cAAA,GAAA0B,CAAA;YAAA1B,cAAA,GAAAS,CAAA;YACL,MAAM,IAAIsD,KAAK,CAAC,kCAAkC,CAAC;UACrD;QAAA;MACF,CAAC,MAAM;QAAA;QAAA/D,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAAA,IAAIH,OAAO,CAACqC,QAAQ,CAAC,KAAK,QAAQ,EAAE;UAAA;UAAA3C,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UACzCmC,SAAS,GAAG8B,OAAO,CAChBM,KAAK,CAAC,IAAI,CAAC,CACXC,MAAM,CAACC,IAAI,IAAI;YAAA;YAAAlF,cAAA,GAAA2B,CAAA;YAAA3B,cAAA,GAAAS,CAAA;YAAA,OAAAyE,IAAI,CAACC,IAAI,EAAE;UAAF,CAAE,CAAC,CAC3BC,GAAG,CAACF,IAAI,IAAI;YAAA;YAAAlF,cAAA,GAAA2B,CAAA;YAAA3B,cAAA,GAAAS,CAAA;YAAA,OAAAmE,IAAI,CAACC,KAAK,CAACK,IAAI,CAAC;UAAD,CAAC,CAAC;QAClC,CAAC,MAAM;UAAA;UAAAlF,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UACL,MAAM,IAAIsD,KAAK,CAAC,aAAazD,OAAO,CAACqC,QAAQ,CAAC,EAAE,CAAC;QACnD;MAAA;MAEA;MAAA;MAAA3C,cAAA,GAAAS,CAAA;MACA,IAAI,IAAI,CAACW,MAAM,CAACW,WAAW,EAAE;QAAA;QAAA/B,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAC3B,IAAI,CAACY,KAAK,CAACgE,GAAG,CAAC1C,QAAQ,EAAE;UACvB8B,IAAI,EAAE7B,SAAS;UACfc,SAAS,EAAEpB,IAAI,CAACC,GAAG;SACpB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAAvC,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MAEDkD,OAAO,CAACC,GAAG,CAAC,YAAYjB,QAAQ,KAAKC,SAAS,CAACS,MAAM,MAAM,CAAC;MAAA;MAAArD,cAAA,GAAAS,CAAA;MAC5D,OAAOmC,SAAS;IAElB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA;MAAA9D,cAAA,GAAAS,CAAA;MACd,MAAM,IAAIsD,KAAK,CAAC,UAAUpB,QAAQ,KAAKmB,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAA/D,cAAA,GAAA0B,CAAA,WAAGoC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAhE,cAAA,GAAA0B,CAAA,WAAGuC,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IAClG;EACF;EAEA;;;;;;;EAOQ,MAAMd,aAAaA,CAACJ,SAAqB;IAAA;IAAA5C,cAAA,GAAA2B,CAAA;IAC/C,MAAMU,SAAS;IAAA;IAAA,CAAArC,cAAA,GAAAS,CAAA,QAAG6B,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMW,MAAM;IAAA;IAAA,CAAAlD,cAAA,GAAAS,CAAA,QAAa,EAAE;IAC3B,MAAM0C,QAAQ;IAAA;IAAA,CAAAnD,cAAA,GAAAS,CAAA,QAAa,EAAE;IAAA;IAAAT,cAAA,GAAAS,CAAA;IAE7B,KAAK,IAAI6E,CAAC;IAAA;IAAA,CAAAtF,cAAA,GAAAS,CAAA,QAAG,CAAC,GAAE6E,CAAC,GAAG1C,SAAS,CAACS,MAAM,EAAEiC,CAAC,EAAE,EAAE;MACzC,MAAMC,QAAQ;MAAA;MAAA,CAAAvF,cAAA,GAAAS,CAAA,QAAGmC,SAAS,CAAC0C,CAAC,CAAC;MAC7B,MAAME,MAAM;MAAA;MAAA,CAAAxF,cAAA,GAAAS,CAAA,QAAG,MAAM6E,CAAC;MAAK;MAAA,CAAAtF,cAAA,GAAA0B,CAAA,WAAA6D,QAAQ,CAACE,EAAE;MAAA;MAAA,CAAAzF,cAAA,GAAA0B,CAAA,WAAI,SAAS,IAAG;MAEtD;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MACA,KAAK,MAAMiF,KAAK,IAAI5E,iBAAiB,CAACI,eAAe,EAAE;QAAA;QAAAlB,cAAA,GAAAS,CAAA;QACrD;QAAI;QAAA,CAAAT,cAAA,GAAA0B,CAAA,aAAEgE,KAAK,IAAIH,QAAQ,CAAC;QAAA;QAAA,CAAAvF,cAAA,GAAA0B,CAAA,WAAI6D,QAAQ,CAACG,KAAuB,CAAC,KAAKC,SAAS,GAAE;UAAA;UAAA3F,cAAA,GAAA0B,CAAA;UAAA1B,cAAA,GAAAS,CAAA;UAC3EyC,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,aAAaE,KAAK,GAAG,CAAC;QAC7C,CAAC;QAAA;QAAA;UAAA1F,cAAA,GAAA0B,CAAA;QAAA;MACH;MAEA;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MACA;MAAI;MAAA,CAAAT,cAAA,GAAA0B,CAAA,kBAAO6D,QAAQ,CAACK,eAAe,KAAK,QAAQ;MAAA;MAAA,CAAA5F,cAAA,GAAA0B,CAAA,WAC5C6D,QAAQ,CAACK,eAAe,GAAG,CAAC;MAAA;MAAA,CAAA5F,cAAA,GAAA0B,CAAA,WAAI6D,QAAQ,CAACK,eAAe,GAAG,CAAC,GAAE;QAAA;QAAA5F,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAChEyC,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,oCAAoC,CAAC;MAC5D,CAAC;MAAA;MAAA;QAAAxF,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MAED;MAAI;MAAA,CAAAT,cAAA,GAAA0B,CAAA,kBAAO6D,QAAQ,CAACM,aAAa,KAAK,QAAQ;MAAA;MAAA,CAAA7F,cAAA,GAAA0B,CAAA,WAC1C6D,QAAQ,CAACM,aAAa,GAAG/E,iBAAiB,CAACE,iBAAiB;MAAA;MAAA,CAAAhB,cAAA,GAAA0B,CAAA,WAC5D6D,QAAQ,CAACM,aAAa,GAAG/E,iBAAiB,CAACG,iBAAiB,GAAE;QAAA;QAAAjB,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAChEyC,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,wBAAwB1E,iBAAiB,CAACE,iBAAiB,IAAIF,iBAAiB,CAACG,iBAAiB,UAAU,CAAC;MACpI,CAAC;MAAA;MAAA;QAAAjB,cAAA,GAAA0B,CAAA;MAAA;MAED;MAAA1B,cAAA,GAAAS,CAAA;MACA;MAAI;MAAA,CAAAT,cAAA,GAAA0B,CAAA,YAACoD,KAAK,CAACC,OAAO,CAACQ,QAAQ,CAACO,eAAe,CAAC;MAAA;MAAA,CAAA9F,cAAA,GAAA0B,CAAA,WACxC6D,QAAQ,CAACO,eAAe,CAACzC,MAAM,KAAKvC,iBAAiB,CAACC,yBAAyB,GAAE;QAAA;QAAAf,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QACnFyC,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,4BAA4B1E,iBAAiB,CAACC,yBAAyB,QAAQ,CAAC;MACvG,CAAC;MAAA;MAAA;QAAAf,cAAA,GAAA0B,CAAA;MAAA;MAED;MAAA1B,cAAA,GAAAS,CAAA;MACA;MAAI;MAAA,CAAAT,cAAA,GAAA0B,CAAA,WAAA6D,QAAQ,CAACQ,OAAO;MAAA;MAAA,CAAA/F,cAAA,GAAA0B,CAAA,WAAI6D,QAAQ,CAACQ,OAAO,KAAKlF,eAAe,GAAE;QAAA;QAAAb,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QAC5D0C,QAAQ,CAACL,IAAI,CAAC,GAAG0C,MAAM,SAASD,QAAQ,CAACQ,OAAO,YAAYlF,eAAe,SAAS,CAAC;MACvF,CAAC;MAAA;MAAA;QAAAb,cAAA,GAAA0B,CAAA;MAAA;IACH;IAEA,MAAM4B,eAAe;IAAA;IAAA,CAAAtD,cAAA,GAAAS,CAAA,QAAG6B,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;IAC9C,MAAMY,MAAM;IAAA;IAAA,CAAAjD,cAAA,GAAAS,CAAA,QAAGyC,MAAM,CAACG,MAAM,KAAK,CAAC;IAAA;IAAArD,cAAA,GAAAS,CAAA;IAElC,IAAI,CAACwC,MAAM,EAAE;MAAA;MAAAjD,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAS,CAAA;MACXkD,OAAO,CAACqC,IAAI,CAAC,aAAa9C,MAAM,CAACG,MAAM,SAASF,QAAQ,CAACE,MAAM,MAAM,CAAC;IACxE,CAAC,MAAM;MAAA;MAAArD,cAAA,GAAA0B,CAAA;MAAA1B,cAAA,GAAAS,CAAA;MACLkD,OAAO,CAACC,GAAG,CAAC,aAAahB,SAAS,CAACS,MAAM,UAAUC,eAAe,IAAI,CAAC;IACzE;IAAC;IAAAtD,cAAA,GAAAS,CAAA;IAED,OAAO;MACLwC,MAAM;MACNC,MAAM;MACNC,QAAQ;MACRC,eAAe,EAAER,SAAS,CAACS,MAAM;MACjCC;KACD;EACH;EAEA;;;;;;;;EAQQE,eAAeA,CAACZ,SAAqB,EAAEqD,QAAgB;IAAA;IAAAjG,cAAA,GAAA2B,CAAA;IAC7D,MAAMuE,WAAW;IAAA;IAAA,CAAAlG,cAAA,GAAAS,CAAA,QAA2B,EAAE;IAC9C,MAAM0F,UAAU;IAAA;IAAA,CAAAnG,cAAA,GAAAS,CAAA,QAA2B,EAAE;IAC7C,IAAI2F,aAAa;IAAA;IAAA,CAAApG,cAAA,GAAAS,CAAA,QAAG,CAAC;IAAA;IAAAT,cAAA,GAAAS,CAAA;IAErB,KAAK,MAAM8E,QAAQ,IAAI3C,SAAS,EAAE;MAAA;MAAA5C,cAAA,GAAAS,CAAA;MAChC;MACAyF,WAAW,CAACX,QAAQ,CAACc,QAAQ,CAAC,GAAG;MAAC;MAAA,CAAArG,cAAA,GAAA0B,CAAA,WAAAwE,WAAW,CAACX,QAAQ,CAACc,QAAQ,CAAC;MAAA;MAAA,CAAArG,cAAA,GAAA0B,CAAA,WAAI,CAAC,KAAI,CAAC;MAE1E;MACA,MAAM4E,UAAU;MAAA;MAAA,CAAAtG,cAAA,GAAAS,CAAA,QAAG,OAAO8E,QAAQ,CAACgB,gBAAgB,KAAK,QAAQ;MAAA;MAAA,CAAAvG,cAAA,GAAA0B,CAAA,WAC5D6D,QAAQ,CAACgB,gBAAgB;MAAA;MAAA,CAAAvG,cAAA,GAAA0B,CAAA,WACzB,GAAG6D,QAAQ,CAACgB,gBAAgB,CAACC,cAAc,IAAIjB,QAAQ,CAACgB,gBAAgB,CAACE,SAAS,EAAE;MAAA;MAAAzG,cAAA,GAAAS,CAAA;MACxF0F,UAAU,CAACG,UAAU,CAAC,GAAG;MAAC;MAAA,CAAAtG,cAAA,GAAA0B,CAAA,WAAAyE,UAAU,CAACG,UAAU,CAAC;MAAA;MAAA,CAAAtG,cAAA,GAAA0B,CAAA,WAAI,CAAC,KAAI,CAAC;MAE1D;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MACA2F,aAAa,IAAIb,QAAQ,CAACM,aAAa;IACzC;IAAC;IAAA7F,cAAA,GAAAS,CAAA;IAED,OAAO;MACLiG,WAAW,EAAE9D,SAAS,CAACS,MAAM;MAC7B6C,WAAW;MACXC,UAAU;MACVQ,WAAW,EAAE/D,SAAS,CAACS,MAAM,GAAG,CAAC;MAAA;MAAA,CAAArD,cAAA,GAAA0B,CAAA,WAAG0E,aAAa,GAAGxD,SAAS,CAACS,MAAM;MAAA;MAAA,CAAArD,cAAA,GAAA0B,CAAA,WAAG,CAAC;MACxEmC,SAAS,EAAEoC;KACZ;EACH;EAEA;;;;;;EAMQ,MAAMxC,eAAeA,CAACjB,SAAmB;IAAA;IAAAxC,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAS,CAAA;IAC/C;IACA,KAAK,MAAM,CAACmG,IAAI,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACtF,QAAQ,EAAE;MAAA;MAAAvB,cAAA,GAAAS,CAAA;MAC3CoG,OAAO,CAACC,KAAK,EAAE;IACjB;IAAC;IAAA9G,cAAA,GAAAS,CAAA;IACD,IAAI,CAACc,QAAQ,CAACwF,KAAK,EAAE;IAErB;IAAA;IAAA/G,cAAA,GAAAS,CAAA;IACA,KAAK,MAAMkC,QAAQ,IAAIH,SAAS,EAAE;MAAA;MAAAxC,cAAA,GAAAS,CAAA;MAChC,IAAI;QACF,MAAMoG,OAAO;QAAA;QAAA,CAAA7G,cAAA,GAAAS,CAAA,SAAGF,KAAK,CAACoC,QAAQ,EAAGqE,SAAS,IAAI;UAAA;UAAAhH,cAAA,GAAA2B,CAAA;UAAA3B,cAAA,GAAAS,CAAA;UAC5C,IAAIuG,SAAS,KAAK,QAAQ,EAAE;YAAA;YAAAhH,cAAA,GAAA0B,CAAA;YAAA1B,cAAA,GAAAS,CAAA;YAC1BkD,OAAO,CAACC,GAAG,CAAC,eAAejB,QAAQ,EAAE,CAAC;YACtC;YAAA;YAAA3C,cAAA,GAAAS,CAAA;YACA,IAAI,CAACY,KAAK,CAAC4F,MAAM,CAACtE,QAAQ,CAAC;YAC3B;YAAA;YAAA3C,cAAA,GAAAS,CAAA;YACA,IAAI,CAACyG,eAAe,EAAE;UACxB,CAAC;UAAA;UAAA;YAAAlH,cAAA,GAAA0B,CAAA;UAAA;QACH,CAAC,CAAC;QAAA;QAAA1B,cAAA,GAAAS,CAAA;QAEF,IAAI,CAACc,QAAQ,CAAC8D,GAAG,CAAC1C,QAAQ,EAAEkE,OAAO,CAAC;MACtC,CAAC,CAAC,OAAO/C,KAAK,EAAE;QAAA;QAAA9D,cAAA,GAAAS,CAAA;QACdkD,OAAO,CAACqC,IAAI,CAAC,gBAAgBrD,QAAQ,EAAE,EAAEmB,KAAK,CAAC;MACjD;IACF;IAAC;IAAA9D,cAAA,GAAAS,CAAA;IAEDkD,OAAO,CAACC,GAAG,CAAC,iBAAiB,IAAI,CAACrC,QAAQ,CAAC4F,IAAI,MAAM,CAAC;EACxD;EAEA;;;;;EAKQD,eAAe;EAAA;EAAA,CAAAlH,cAAA,GAAAS,CAAA,SAAG,CAAC,MAAK;IAAA;IAAAT,cAAA,GAAA2B,CAAA;IAC9B,IAAIyF,OAAO;IAAA;IAAA,CAAApH,cAAA,GAAAS,CAAA,SAA0B,IAAI;IAAA;IAAAT,cAAA,GAAAS,CAAA;IACzC,OAAO,MAAK;MAAA;MAAAT,cAAA,GAAA2B,CAAA;MAAA3B,cAAA,GAAAS,CAAA;MACV,IAAI2G,OAAO,EAAE;QAAA;QAAApH,cAAA,GAAA0B,CAAA;QAAA1B,cAAA,GAAAS,CAAA;QACX4G,YAAY,CAACD,OAAO,CAAC;MACvB,CAAC;MAAA;MAAA;QAAApH,cAAA,GAAA0B,CAAA;MAAA;MAAA1B,cAAA,GAAAS,CAAA;MACD2G,OAAO,GAAGE,UAAU,CAAC,MAAK;QAAA;QAAAtH,cAAA,GAAA2B,CAAA;QAAA3B,cAAA,GAAAS,CAAA;QACxBkD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAAA;QAAA5D,cAAA,GAAAS,CAAA;QAC1B,IAAI,CAACyB,OAAO,EAAE,CAACqF,KAAK,CAACzD,KAAK,IAAG;UAAA;UAAA9D,cAAA,GAAA2B,CAAA;UAAA3B,cAAA,GAAAS,CAAA;UAC3BkD,OAAO,CAACG,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC,EAAC;IACX,CAAC;EACH,CAAC,EAAC,CAAE;EAIJ;;;;;;;EAOQ0D,uBAAuBA,CAACnB,QAAgB;IAAA;IAAArG,cAAA,GAAA2B,CAAA;IAC9C,MAAM8F,OAAO;IAAA;IAAA,CAAAzH,cAAA,GAAAS,CAAA,SAA2B;MACtC,UAAU,EAAE,KAAK;MACjB,aAAa,EAAE,IAAI;MACnB,iBAAiB,EAAE,KAAK;MACxB,SAAS,EAAE,IAAI;MACf,SAAS,EAAE,IAAI;MACf,UAAU,EAAE;KACb;IAAA;IAAAT,cAAA,GAAAS,CAAA;IAED,OAAO,2BAAAT,cAAA,GAAA0B,CAAA,WAAA+F,OAAO,CAACpB,QAAQ,CAAC;IAAA;IAAA,CAAArG,cAAA,GAAA0B,CAAA,WAAI,IAAI;EAClC;EAEA;;;EAGAgG,OAAOA,CAAA;IAAA;IAAA1H,cAAA,GAAA2B,CAAA;IAAA3B,cAAA,GAAAS,CAAA;IACL;IACA,KAAK,MAAM,CAACmG,IAAI,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACtF,QAAQ,EAAE;MAAA;MAAAvB,cAAA,GAAAS,CAAA;MAC3CoG,OAAO,CAACC,KAAK,EAAE;IACjB;IAAC;IAAA9G,cAAA,GAAAS,CAAA;IACD,IAAI,CAACc,QAAQ,CAACwF,KAAK,EAAE;IAErB;IAAA;IAAA/G,cAAA,GAAAS,CAAA;IACA,IAAI,CAACY,KAAK,CAAC0F,KAAK,EAAE;IAAA;IAAA/G,cAAA,GAAAS,CAAA;IAElBkD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B", "ignoreList": []}