d126b11e0b70cf86673b7317c75d0e9c
/* istanbul ignore next */
function cov_2i3pzto8wf() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts";
  var hash = "5414a9a1ad7b424f9da77b616772ce954d41ad5c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts",
    statementMap: {
      "0": {
        start: {
          line: 19,
          column: 17
        },
        end: {
          line: 19,
          column: 44
        }
      },
      "1": {
        start: {
          line: 21,
          column: 26
        },
        end: {
          line: 21,
          column: 45
        }
      },
      "2": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 31
        }
      },
      "3": {
        start: {
          line: 25,
          column: 26
        },
        end: {
          line: 38,
          column: 1
        }
      },
      "4": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 21
        }
      },
      "5": {
        start: {
          line: 50,
          column: 15
        },
        end: {
          line: 50,
          column: 24
        }
      },
      "6": {
        start: {
          line: 51,
          column: 18
        },
        end: {
          line: 51,
          column: 22
        }
      },
      "7": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 65,
          column: 10
        }
      },
      "8": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 76,
          column: 9
        }
      },
      "9": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 36
        }
      },
      "10": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 47
        }
      },
      "11": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 84,
          column: 9
        }
      },
      "12": {
        start: {
          line: 79,
          column: 27
        },
        end: {
          line: 79,
          column: 49
        }
      },
      "13": {
        start: {
          line: 80,
          column: 12
        },
        end: {
          line: 80,
          column: 26
        }
      },
      "14": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 36
        }
      },
      "15": {
        start: {
          line: 93,
          column: 26
        },
        end: {
          line: 93,
          column: 36
        }
      },
      "16": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "17": {
        start: {
          line: 96,
          column: 30
        },
        end: {
          line: 96,
          column: 57
        }
      },
      "18": {
        start: {
          line: 98,
          column: 33
        },
        end: {
          line: 98,
          column: 35
        }
      },
      "19": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 102,
          column: 13
        }
      },
      "20": {
        start: {
          line: 100,
          column: 34
        },
        end: {
          line: 100,
          column: 68
        }
      },
      "21": {
        start: {
          line: 101,
          column: 16
        },
        end: {
          line: 101,
          column: 48
        }
      },
      "22": {
        start: {
          line: 104,
          column: 31
        },
        end: {
          line: 106,
          column: 118
        }
      },
      "23": {
        start: {
          line: 108,
          column: 26
        },
        end: {
          line: 108,
          column: 84
        }
      },
      "24": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 112,
          column: 13
        }
      },
      "25": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 111,
          column: 54
        }
      },
      "26": {
        start: {
          line: 113,
          column: 27
        },
        end: {
          line: 118,
          column: 13
        }
      },
      "27": {
        start: {
          line: 119,
          column: 12
        },
        end: {
          line: 119,
          column: 87
        }
      },
      "28": {
        start: {
          line: 120,
          column: 12
        },
        end: {
          line: 120,
          column: 26
        }
      },
      "29": {
        start: {
          line: 123,
          column: 12
        },
        end: {
          line: 123,
          column: 46
        }
      },
      "30": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 124,
          column: 97
        }
      },
      "31": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 155,
          column: 9
        }
      },
      "32": {
        start: {
          line: 135,
          column: 26
        },
        end: {
          line: 135,
          column: 60
        }
      },
      "33": {
        start: {
          line: 136,
          column: 30
        },
        end: {
          line: 136,
          column: 32
        }
      },
      "34": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 146,
          column: 13
        }
      },
      "35": {
        start: {
          line: 138,
          column: 33
        },
        end: {
          line: 138,
          column: 64
        }
      },
      "36": {
        start: {
          line: 139,
          column: 33
        },
        end: {
          line: 139,
          column: 53
        }
      },
      "37": {
        start: {
          line: 141,
          column: 16
        },
        end: {
          line: 145,
          column: 17
        }
      },
      "38": {
        start: {
          line: 144,
          column: 20
        },
        end: {
          line: 144,
          column: 45
        }
      },
      "39": {
        start: {
          line: 147,
          column: 12
        },
        end: {
          line: 149,
          column: 13
        }
      },
      "40": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 70
        }
      },
      "41": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 57
        }
      },
      "42": {
        start: {
          line: 151,
          column: 12
        },
        end: {
          line: 151,
          column: 29
        }
      },
      "43": {
        start: {
          line: 154,
          column: 12
        },
        end: {
          line: 154,
          column: 99
        }
      },
      "44": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 213,
          column: 9
        }
      },
      "45": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 173,
          column: 13
        }
      },
      "46": {
        start: {
          line: 168,
          column: 31
        },
        end: {
          line: 168,
          column: 55
        }
      },
      "47": {
        start: {
          line: 169,
          column: 16
        },
        end: {
          line: 172,
          column: 17
        }
      },
      "48": {
        start: {
          line: 170,
          column: 20
        },
        end: {
          line: 170,
          column: 58
        }
      },
      "49": {
        start: {
          line: 171,
          column: 20
        },
        end: {
          line: 171,
          column: 39
        }
      },
      "50": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 61
        }
      },
      "51": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 200,
          column: 13
        }
      },
      "52": {
        start: {
          line: 179,
          column: 31
        },
        end: {
          line: 179,
          column: 50
        }
      },
      "53": {
        start: {
          line: 181,
          column: 16
        },
        end: {
          line: 190,
          column: 17
        }
      },
      "54": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 182,
          column: 39
        }
      },
      "55": {
        start: {
          line: 184,
          column: 21
        },
        end: {
          line: 190,
          column: 17
        }
      },
      "56": {
        start: {
          line: 185,
          column: 20
        },
        end: {
          line: 185,
          column: 49
        }
      },
      "57": {
        start: {
          line: 186,
          column: 20
        },
        end: {
          line: 186,
          column: 71
        }
      },
      "58": {
        start: {
          line: 189,
          column: 20
        },
        end: {
          line: 189,
          column: 72
        }
      },
      "59": {
        start: {
          line: 192,
          column: 17
        },
        end: {
          line: 200,
          column: 13
        }
      },
      "60": {
        start: {
          line: 193,
          column: 16
        },
        end: {
          line: 196,
          column: 51
        }
      },
      "61": {
        start: {
          line: 195,
          column: 36
        },
        end: {
          line: 195,
          column: 47
        }
      },
      "62": {
        start: {
          line: 196,
          column: 33
        },
        end: {
          line: 196,
          column: 49
        }
      },
      "63": {
        start: {
          line: 199,
          column: 16
        },
        end: {
          line: 199,
          column: 66
        }
      },
      "64": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 207,
          column: 13
        }
      },
      "65": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 206,
          column: 19
        }
      },
      "66": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 208,
          column: 73
        }
      },
      "67": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 29
        }
      },
      "68": {
        start: {
          line: 212,
          column: 12
        },
        end: {
          line: 212,
          column: 109
        }
      },
      "69": {
        start: {
          line: 223,
          column: 26
        },
        end: {
          line: 223,
          column: 36
        }
      },
      "70": {
        start: {
          line: 224,
          column: 23
        },
        end: {
          line: 224,
          column: 25
        }
      },
      "71": {
        start: {
          line: 225,
          column: 25
        },
        end: {
          line: 225,
          column: 27
        }
      },
      "72": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 254,
          column: 9
        }
      },
      "73": {
        start: {
          line: 226,
          column: 21
        },
        end: {
          line: 226,
          column: 22
        }
      },
      "74": {
        start: {
          line: 227,
          column: 29
        },
        end: {
          line: 227,
          column: 41
        }
      },
      "75": {
        start: {
          line: 228,
          column: 27
        },
        end: {
          line: 228,
          column: 66
        }
      },
      "76": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 234,
          column: 13
        }
      },
      "77": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 233,
          column: 17
        }
      },
      "78": {
        start: {
          line: 232,
          column: 20
        },
        end: {
          line: 232,
          column: 64
        }
      },
      "79": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 239,
          column: 13
        }
      },
      "80": {
        start: {
          line: 238,
          column: 16
        },
        end: {
          line: 238,
          column: 75
        }
      },
      "81": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 244,
          column: 13
        }
      },
      "82": {
        start: {
          line: 243,
          column: 16
        },
        end: {
          line: 243,
          column: 147
        }
      },
      "83": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 249,
          column: 13
        }
      },
      "84": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 118
        }
      },
      "85": {
        start: {
          line: 251,
          column: 12
        },
        end: {
          line: 253,
          column: 13
        }
      },
      "86": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 252,
          column: 102
        }
      },
      "87": {
        start: {
          line: 255,
          column: 32
        },
        end: {
          line: 255,
          column: 54
        }
      },
      "88": {
        start: {
          line: 256,
          column: 23
        },
        end: {
          line: 256,
          column: 42
        }
      },
      "89": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "90": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 258,
          column: 83
        }
      },
      "91": {
        start: {
          line: 261,
          column: 12
        },
        end: {
          line: 261,
          column: 84
        }
      },
      "92": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 269,
          column: 10
        }
      },
      "93": {
        start: {
          line: 280,
          column: 28
        },
        end: {
          line: 280,
          column: 30
        }
      },
      "94": {
        start: {
          line: 281,
          column: 27
        },
        end: {
          line: 281,
          column: 29
        }
      },
      "95": {
        start: {
          line: 282,
          column: 28
        },
        end: {
          line: 282,
          column: 29
        }
      },
      "96": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 293,
          column: 9
        }
      },
      "97": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 87
        }
      },
      "98": {
        start: {
          line: 287,
          column: 31
        },
        end: {
          line: 289,
          column: 102
        }
      },
      "99": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 290,
          column: 71
        }
      },
      "100": {
        start: {
          line: 292,
          column: 12
        },
        end: {
          line: 292,
          column: 52
        }
      },
      "101": {
        start: {
          line: 294,
          column: 8
        },
        end: {
          line: 300,
          column: 10
        }
      },
      "102": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 312,
          column: 9
        }
      },
      "103": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 311,
          column: 28
        }
      },
      "104": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 313,
          column: 30
        }
      },
      "105": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 331,
          column: 9
        }
      },
      "106": {
        start: {
          line: 316,
          column: 12
        },
        end: {
          line: 330,
          column: 13
        }
      },
      "107": {
        start: {
          line: 317,
          column: 32
        },
        end: {
          line: 325,
          column: 18
        }
      },
      "108": {
        start: {
          line: 318,
          column: 20
        },
        end: {
          line: 324,
          column: 21
        }
      },
      "109": {
        start: {
          line: 319,
          column: 24
        },
        end: {
          line: 319,
          column: 63
        }
      },
      "110": {
        start: {
          line: 321,
          column: 24
        },
        end: {
          line: 321,
          column: 52
        }
      },
      "111": {
        start: {
          line: 323,
          column: 24
        },
        end: {
          line: 323,
          column: 47
        }
      },
      "112": {
        start: {
          line: 326,
          column: 16
        },
        end: {
          line: 326,
          column: 53
        }
      },
      "113": {
        start: {
          line: 329,
          column: 16
        },
        end: {
          line: 329,
          column: 64
        }
      },
      "114": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 332,
          column: 63
        }
      },
      "115": {
        start: {
          line: 339,
          column: 22
        },
        end: {
          line: 352,
          column: 8
        }
      },
      "116": {
        start: {
          line: 340,
          column: 22
        },
        end: {
          line: 340,
          column: 26
        }
      },
      "117": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 351,
          column: 10
        }
      },
      "118": {
        start: {
          line: 342,
          column: 12
        },
        end: {
          line: 344,
          column: 13
        }
      },
      "119": {
        start: {
          line: 343,
          column: 16
        },
        end: {
          line: 343,
          column: 38
        }
      },
      "120": {
        start: {
          line: 345,
          column: 12
        },
        end: {
          line: 350,
          column: 21
        }
      },
      "121": {
        start: {
          line: 346,
          column: 16
        },
        end: {
          line: 346,
          column: 43
        }
      },
      "122": {
        start: {
          line: 347,
          column: 16
        },
        end: {
          line: 349,
          column: 19
        }
      },
      "123": {
        start: {
          line: 348,
          column: 20
        },
        end: {
          line: 348,
          column: 53
        }
      },
      "124": {
        start: {
          line: 361,
          column: 24
        },
        end: {
          line: 368,
          column: 9
        }
      },
      "125": {
        start: {
          line: 369,
          column: 8
        },
        end: {
          line: 369,
          column: 41
        }
      },
      "126": {
        start: {
          line: 376,
          column: 8
        },
        end: {
          line: 378,
          column: 9
        }
      },
      "127": {
        start: {
          line: 377,
          column: 12
        },
        end: {
          line: 377,
          column: 28
        }
      },
      "128": {
        start: {
          line: 379,
          column: 8
        },
        end: {
          line: 379,
          column: 30
        }
      },
      "129": {
        start: {
          line: 381,
          column: 8
        },
        end: {
          line: 381,
          column: 27
        }
      },
      "130": {
        start: {
          line: 382,
          column: 8
        },
        end: {
          line: 382,
          column: 37
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 57,
            column: 4
          },
          end: {
            line: 57,
            column: 5
          }
        },
        loc: {
          start: {
            line: 57,
            column: 29
          },
          end: {
            line: 66,
            column: 5
          }
        },
        line: 57
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 72,
            column: 5
          }
        },
        loc: {
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 85,
            column: 5
          }
        },
        line: 72
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 92,
            column: 4
          },
          end: {
            line: 92,
            column: 5
          }
        },
        loc: {
          start: {
            line: 92,
            column: 25
          },
          end: {
            line: 126,
            column: 5
          }
        },
        line: 92
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 133,
            column: 5
          }
        },
        loc: {
          start: {
            line: 133,
            column: 27
          },
          end: {
            line: 156,
            column: 5
          }
        },
        line: 133
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 164,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        loc: {
          start: {
            line: 164,
            column: 34
          },
          end: {
            line: 214,
            column: 5
          }
        },
        line: 164
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 195,
            column: 28
          },
          end: {
            line: 195,
            column: 29
          }
        },
        loc: {
          start: {
            line: 195,
            column: 36
          },
          end: {
            line: 195,
            column: 47
          }
        },
        line: 195
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 196,
            column: 25
          },
          end: {
            line: 196,
            column: 26
          }
        },
        loc: {
          start: {
            line: 196,
            column: 33
          },
          end: {
            line: 196,
            column: 49
          }
        },
        line: 196
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 222,
            column: 5
          }
        },
        loc: {
          start: {
            line: 222,
            column: 35
          },
          end: {
            line: 270,
            column: 5
          }
        },
        line: 222
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 279,
            column: 5
          }
        },
        loc: {
          start: {
            line: 279,
            column: 41
          },
          end: {
            line: 301,
            column: 5
          }
        },
        line: 279
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 308,
            column: 5
          }
        },
        loc: {
          start: {
            line: 308,
            column: 37
          },
          end: {
            line: 333,
            column: 5
          }
        },
        line: 308
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 317,
            column: 48
          },
          end: {
            line: 317,
            column: 49
          }
        },
        loc: {
          start: {
            line: 317,
            column: 63
          },
          end: {
            line: 325,
            column: 17
          }
        },
        line: 317
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 339,
            column: 23
          },
          end: {
            line: 339,
            column: 24
          }
        },
        loc: {
          start: {
            line: 339,
            column: 29
          },
          end: {
            line: 352,
            column: 5
          }
        },
        line: 339
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 341,
            column: 15
          },
          end: {
            line: 341,
            column: 16
          }
        },
        loc: {
          start: {
            line: 341,
            column: 21
          },
          end: {
            line: 351,
            column: 9
          }
        },
        line: 341
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 345,
            column: 33
          },
          end: {
            line: 345,
            column: 34
          }
        },
        loc: {
          start: {
            line: 345,
            column: 39
          },
          end: {
            line: 350,
            column: 13
          }
        },
        line: 345
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 347,
            column: 37
          },
          end: {
            line: 347,
            column: 38
          }
        },
        loc: {
          start: {
            line: 347,
            column: 46
          },
          end: {
            line: 349,
            column: 17
          }
        },
        line: 347
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        loc: {
          start: {
            line: 360,
            column: 38
          },
          end: {
            line: 370,
            column: 5
          }
        },
        line: 360
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 374,
            column: 5
          }
        },
        loc: {
          start: {
            line: 374,
            column: 14
          },
          end: {
            line: 383,
            column: 5
          }
        },
        line: 374
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 57,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 57,
            column: 25
          },
          end: {
            line: 57,
            column: 27
          }
        }],
        line: 57
      },
      "1": {
        loc: {
          start: {
            line: 59,
            column: 21
          },
          end: {
            line: 59,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 21
          },
          end: {
            line: 59,
            column: 35
          }
        }, {
          start: {
            line: 59,
            column: 39
          },
          end: {
            line: 59,
            column: 47
          }
        }],
        line: 59
      },
      "2": {
        loc: {
          start: {
            line: 60,
            column: 29
          },
          end: {
            line: 60,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 29
          },
          end: {
            line: 60,
            column: 51
          }
        }, {
          start: {
            line: 60,
            column: 55
          },
          end: {
            line: 60,
            column: 59
          }
        }],
        line: 60
      },
      "3": {
        loc: {
          start: {
            line: 61,
            column: 30
          },
          end: {
            line: 61,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 30
          },
          end: {
            line: 61,
            column: 53
          }
        }, {
          start: {
            line: 61,
            column: 57
          },
          end: {
            line: 61,
            column: 61
          }
        }],
        line: 61
      },
      "4": {
        loc: {
          start: {
            line: 62,
            column: 25
          },
          end: {
            line: 62,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 25
          },
          end: {
            line: 62,
            column: 43
          }
        }, {
          start: {
            line: 62,
            column: 47
          },
          end: {
            line: 62,
            column: 51
          }
        }],
        line: 62
      },
      "5": {
        loc: {
          start: {
            line: 63,
            column: 22
          },
          end: {
            line: 63,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 22
          },
          end: {
            line: 63,
            column: 37
          }
        }, {
          start: {
            line: 63,
            column: 41
          },
          end: {
            line: 63,
            column: 45
          }
        }],
        line: 63
      },
      "6": {
        loc: {
          start: {
            line: 64,
            column: 24
          },
          end: {
            line: 64,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 24
          },
          end: {
            line: 64,
            column: 41
          }
        }, {
          start: {
            line: 64,
            column: 45
          },
          end: {
            line: 64,
            column: 46
          }
        }],
        line: 64
      },
      "7": {
        loc: {
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "8": {
        loc: {
          start: {
            line: 104,
            column: 31
          },
          end: {
            line: 106,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 18
          },
          end: {
            line: 105,
            column: 56
          }
        }, {
          start: {
            line: 106,
            column: 18
          },
          end: {
            line: 106,
            column: 118
          }
        }],
        line: 104
      },
      "9": {
        loc: {
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 112,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 112,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "10": {
        loc: {
          start: {
            line: 124,
            column: 39
          },
          end: {
            line: 124,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 124,
            column: 64
          },
          end: {
            line: 124,
            column: 77
          }
        }, {
          start: {
            line: 124,
            column: 80
          },
          end: {
            line: 124,
            column: 93
          }
        }],
        line: 124
      },
      "11": {
        loc: {
          start: {
            line: 141,
            column: 16
          },
          end: {
            line: 145,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 141,
            column: 16
          },
          end: {
            line: 145,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 141
      },
      "12": {
        loc: {
          start: {
            line: 141,
            column: 20
          },
          end: {
            line: 143,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 141,
            column: 20
          },
          end: {
            line: 141,
            column: 37
          }
        }, {
          start: {
            line: 142,
            column: 20
          },
          end: {
            line: 142,
            column: 61
          }
        }, {
          start: {
            line: 143,
            column: 21
          },
          end: {
            line: 143,
            column: 46
          }
        }, {
          start: {
            line: 143,
            column: 50
          },
          end: {
            line: 143,
            column: 69
          }
        }],
        line: 141
      },
      "13": {
        loc: {
          start: {
            line: 147,
            column: 12
          },
          end: {
            line: 149,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 12
          },
          end: {
            line: 149,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "14": {
        loc: {
          start: {
            line: 154,
            column: 41
          },
          end: {
            line: 154,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 66
          },
          end: {
            line: 154,
            column: 79
          }
        }, {
          start: {
            line: 154,
            column: 82
          },
          end: {
            line: 154,
            column: 95
          }
        }],
        line: 154
      },
      "15": {
        loc: {
          start: {
            line: 167,
            column: 12
          },
          end: {
            line: 173,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 12
          },
          end: {
            line: 173,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "16": {
        loc: {
          start: {
            line: 169,
            column: 16
          },
          end: {
            line: 172,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 16
          },
          end: {
            line: 172,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "17": {
        loc: {
          start: {
            line: 169,
            column: 20
          },
          end: {
            line: 169,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 20
          },
          end: {
            line: 169,
            column: 26
          }
        }, {
          start: {
            line: 169,
            column: 30
          },
          end: {
            line: 169,
            column: 89
          }
        }],
        line: 169
      },
      "18": {
        loc: {
          start: {
            line: 178,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 12
          },
          end: {
            line: 200,
            column: 13
          }
        }, {
          start: {
            line: 192,
            column: 17
          },
          end: {
            line: 200,
            column: 13
          }
        }],
        line: 178
      },
      "19": {
        loc: {
          start: {
            line: 181,
            column: 16
          },
          end: {
            line: 190,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 16
          },
          end: {
            line: 190,
            column: 17
          }
        }, {
          start: {
            line: 184,
            column: 21
          },
          end: {
            line: 190,
            column: 17
          }
        }],
        line: 181
      },
      "20": {
        loc: {
          start: {
            line: 184,
            column: 21
          },
          end: {
            line: 190,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 21
          },
          end: {
            line: 190,
            column: 17
          }
        }, {
          start: {
            line: 188,
            column: 21
          },
          end: {
            line: 190,
            column: 17
          }
        }],
        line: 184
      },
      "21": {
        loc: {
          start: {
            line: 184,
            column: 25
          },
          end: {
            line: 184,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 25
          },
          end: {
            line: 184,
            column: 41
          }
        }, {
          start: {
            line: 184,
            column: 45
          },
          end: {
            line: 184,
            column: 76
          }
        }],
        line: 184
      },
      "22": {
        loc: {
          start: {
            line: 192,
            column: 17
          },
          end: {
            line: 200,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 17
          },
          end: {
            line: 200,
            column: 13
          }
        }, {
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 200,
            column: 13
          }
        }],
        line: 192
      },
      "23": {
        loc: {
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 207,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 207,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "24": {
        loc: {
          start: {
            line: 212,
            column: 51
          },
          end: {
            line: 212,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 212,
            column: 76
          },
          end: {
            line: 212,
            column: 89
          }
        }, {
          start: {
            line: 212,
            column: 92
          },
          end: {
            line: 212,
            column: 105
          }
        }],
        line: 212
      },
      "25": {
        loc: {
          start: {
            line: 228,
            column: 39
          },
          end: {
            line: 228,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 39
          },
          end: {
            line: 228,
            column: 50
          }
        }, {
          start: {
            line: 228,
            column: 54
          },
          end: {
            line: 228,
            column: 63
          }
        }],
        line: 228
      },
      "26": {
        loc: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 233,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 233,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "27": {
        loc: {
          start: {
            line: 231,
            column: 20
          },
          end: {
            line: 231,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 20
          },
          end: {
            line: 231,
            column: 40
          }
        }, {
          start: {
            line: 231,
            column: 44
          },
          end: {
            line: 231,
            column: 73
          }
        }],
        line: 231
      },
      "28": {
        loc: {
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 239,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 239,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 236
      },
      "29": {
        loc: {
          start: {
            line: 236,
            column: 16
          },
          end: {
            line: 237,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 16
          },
          end: {
            line: 236,
            column: 60
          }
        }, {
          start: {
            line: 237,
            column: 16
          },
          end: {
            line: 237,
            column: 44
          }
        }, {
          start: {
            line: 237,
            column: 48
          },
          end: {
            line: 237,
            column: 76
          }
        }],
        line: 236
      },
      "30": {
        loc: {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 244,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 244,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "31": {
        loc: {
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 242,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 240,
            column: 58
          }
        }, {
          start: {
            line: 241,
            column: 16
          },
          end: {
            line: 241,
            column: 76
          }
        }, {
          start: {
            line: 242,
            column: 16
          },
          end: {
            line: 242,
            column: 76
          }
        }],
        line: 240
      },
      "32": {
        loc: {
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 249,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 249,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "33": {
        loc: {
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 247,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 246,
            column: 56
          }
        }, {
          start: {
            line: 247,
            column: 16
          },
          end: {
            line: 247,
            column: 95
          }
        }],
        line: 246
      },
      "34": {
        loc: {
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 253,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 253,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "35": {
        loc: {
          start: {
            line: 251,
            column: 16
          },
          end: {
            line: 251,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 16
          },
          end: {
            line: 251,
            column: 32
          }
        }, {
          start: {
            line: 251,
            column: 36
          },
          end: {
            line: 251,
            column: 72
          }
        }],
        line: 251
      },
      "36": {
        loc: {
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 262,
            column: 9
          }
        }, {
          start: {
            line: 260,
            column: 13
          },
          end: {
            line: 262,
            column: 9
          }
        }],
        line: 257
      },
      "37": {
        loc: {
          start: {
            line: 285,
            column: 46
          },
          end: {
            line: 285,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 46
          },
          end: {
            line: 285,
            column: 76
          }
        }, {
          start: {
            line: 285,
            column: 80
          },
          end: {
            line: 285,
            column: 81
          }
        }],
        line: 285
      },
      "38": {
        loc: {
          start: {
            line: 287,
            column: 31
          },
          end: {
            line: 289,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 288,
            column: 18
          },
          end: {
            line: 288,
            column: 43
          }
        }, {
          start: {
            line: 289,
            column: 18
          },
          end: {
            line: 289,
            column: 102
          }
        }],
        line: 287
      },
      "39": {
        loc: {
          start: {
            line: 290,
            column: 38
          },
          end: {
            line: 290,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 38
          },
          end: {
            line: 290,
            column: 60
          }
        }, {
          start: {
            line: 290,
            column: 64
          },
          end: {
            line: 290,
            column: 65
          }
        }],
        line: 290
      },
      "40": {
        loc: {
          start: {
            line: 298,
            column: 25
          },
          end: {
            line: 298,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 298,
            column: 48
          },
          end: {
            line: 298,
            column: 80
          }
        }, {
          start: {
            line: 298,
            column: 83
          },
          end: {
            line: 298,
            column: 84
          }
        }],
        line: 298
      },
      "41": {
        loc: {
          start: {
            line: 318,
            column: 20
          },
          end: {
            line: 324,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 318,
            column: 20
          },
          end: {
            line: 324,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 318
      },
      "42": {
        loc: {
          start: {
            line: 342,
            column: 12
          },
          end: {
            line: 344,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 12
          },
          end: {
            line: 344,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "43": {
        loc: {
          start: {
            line: 369,
            column: 15
          },
          end: {
            line: 369,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 369,
            column: 15
          },
          end: {
            line: 369,
            column: 32
          }
        }, {
          start: {
            line: 369,
            column: 36
          },
          end: {
            line: 369,
            column: 40
          }
        }],
        line: 369
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0, 0],
      "30": [0, 0],
      "31": [0, 0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts",
      mappings: "AAAA;;;;;;;;;;GAUG;AAEH,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAA;AACrD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AACpC,OAAO,EAAE,KAAK,EAAE,MAAM,IAAI,CAAA;AAM1B,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;AAE/E,eAAe;AACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAA;AAE5C,gBAAgB;AAChB,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAU,CAAA;AAEtD,aAAa;AACb,MAAM,eAAe,GAAG,OAAgB,CAAA;AAExC,aAAa;AACb,MAAM,iBAAiB,GAAG;IACxB,aAAa;IACb,yBAAyB,EAAE,EAAE;IAC7B,aAAa;IACb,iBAAiB,EAAE,GAAG;IACtB,aAAa;IACb,iBAAiB,EAAE,GAAG;IACtB,WAAW;IACX,eAAe,EAAE;QACf,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB;QAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM;QAC7D,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS;KACnE;CACF,CAAA;AAgEV,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,UAAU;IACb,MAAM,CAA4B;IAClC,KAAK,GAAyD,IAAI,GAAG,EAAE,CAAA;IACvE,QAAQ,GAAqB,IAAI,GAAG,EAAE,CAAA;IACtC,WAAW,GAAmC,IAAI,CAAA;IAE1D;;;;OAIG;IACH,YAAY,SAA2B,EAAE;QACvC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,QAAQ;YACnC,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,IAAI;YAC/C,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,IAAI;YACjD,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI;YACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE,MAAM;YACzC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;SACnC,CAAA;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACX,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,WAAW,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QAEtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAA;YACrC,OAAO,MAAM,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACzB,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,YAAY;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC;YACH,YAAY;YACZ,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;YAE7C,YAAY;YACZ,MAAM,YAAY,GAAe,EAAE,CAAA;YAEnC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBACpD,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;YACjC,CAAC;YAED,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBAC7C,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;gBACxC,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,eAAe,EAAE,YAAY,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,EAAE,CAAA;YAExG,YAAY;YACZ,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAA;YAExE,WAAW;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;YACvC,CAAC;YAED,MAAM,MAAM,GAAmB;gBAC7B,SAAS,EAAE,YAAY;gBACvB,KAAK;gBACL,UAAU;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,CAAC,MAAM,UAAU,KAAK,CAAC,SAAS,IAAI,CAAC,CAAA;YAE1E,OAAO,MAAM,CAAA;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;YACjC,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACtF,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAChD,MAAM,SAAS,GAAa,EAAE,CAAA;YAE9B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;gBAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAErC,mBAAmB;gBACnB,IAAI,QAAQ,CAAC,MAAM,EAAE;oBACjB,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAQ,CAAC;oBAChD,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACvD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC1B,CAAC;YACH,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAA;YACvD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,QAAQ,SAAS,CAAC,MAAM,OAAO,CAAC,CAAA;YAC5C,OAAO,SAAS,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,aAAa,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACxF,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,aAAa,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,OAAO;YACP,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBACvC,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;oBAC1E,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAA;oBACrC,OAAO,MAAM,CAAC,IAAI,CAAA;gBACpB,CAAC;YACH,CAAC;YAED,SAAS;YACT,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACjD,IAAI,SAAqB,CAAA;YAEzB,WAAW;YACX,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;gBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;gBAElC,+BAA+B;gBAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,SAAS,GAAG,MAAM,CAAA;gBACpB,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC/D,SAAS,GAAG,MAAM,CAAC,SAAS,CAAA;oBAC5B,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,CAAC,MAAM,KAAK,CAAC,CAAA;gBACpD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;gBACrD,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC1C,SAAS,GAAG,OAAO;qBAChB,KAAK,CAAC,IAAI,CAAC;qBACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;qBAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAClC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;YACnD,CAAC;YAED,OAAO;YACP,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACvB,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAA;YACJ,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,CAAA;YAC5D,OAAO,SAAS,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QAClG,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,aAAa,CAAC,SAAqB;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,MAAM,QAAQ,GAAa,EAAE,CAAA;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,QAAQ,CAAC,EAAE,IAAI,SAAS,GAAG,CAAA;YAEtD,SAAS;YACT,KAAK,MAAM,KAAK,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACtD,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC5E,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA;gBAC7C,CAAC;YACH,CAAC;YAED,YAAY;YACZ,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ;gBAC5C,QAAQ,CAAC,eAAe,GAAG,CAAC,IAAI,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,oCAAoC,CAAC,CAAA;YAC5D,CAAC;YAED,IAAI,OAAO,QAAQ,CAAC,aAAa,KAAK,QAAQ;gBAC1C,QAAQ,CAAC,aAAa,GAAG,iBAAiB,CAAC,iBAAiB;gBAC5D,QAAQ,CAAC,aAAa,GAAG,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,wBAAwB,iBAAiB,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB,UAAU,CAAC,CAAA;YACpI,CAAC;YAED,SAAS;YACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;gBACxC,QAAQ,CAAC,eAAe,CAAC,MAAM,KAAK,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;gBACpF,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,4BAA4B,iBAAiB,CAAC,yBAAyB,QAAQ,CAAC,CAAA;YACvG,CAAC;YAED,OAAO;YACP,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,KAAK,eAAe,EAAE,CAAC;gBAC7D,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,SAAS,QAAQ,CAAC,OAAO,YAAY,eAAe,SAAS,CAAC,CAAA;YACvF,CAAC;QACH,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;QAElC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,SAAS,QAAQ,CAAC,MAAM,MAAM,CAAC,CAAA;QACxE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,MAAM,UAAU,eAAe,IAAI,CAAC,CAAA;QACzE,CAAC;QAED,OAAO;YACL,MAAM;YACN,MAAM;YACN,QAAQ;YACR,eAAe,EAAE,SAAS,CAAC,MAAM;YACjC,eAAe;SAChB,CAAA;IACH,CAAC;IAED;;;;;;;OAOG;IACK,eAAe,CAAC,SAAqB,EAAE,QAAgB;QAC7D,MAAM,WAAW,GAA2B,EAAE,CAAA;QAC9C,MAAM,UAAU,GAA2B,EAAE,CAAA;QAC7C,IAAI,aAAa,GAAG,CAAC,CAAA;QAErB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,QAAQ;YACR,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAE1E,0BAA0B;YAC1B,MAAM,UAAU,GAAG,OAAO,QAAQ,CAAC,gBAAgB,KAAK,QAAQ;gBAC9D,CAAC,CAAC,QAAQ,CAAC,gBAAgB;gBAC3B,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAC,cAAc,IAAI,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAA;YACxF,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAE1D,SAAS;YACT,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAA;QACzC,CAAC;QAED,OAAO;YACL,WAAW,EAAE,SAAS,CAAC,MAAM;YAC7B,WAAW;YACX,UAAU;YACV,WAAW,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxE,SAAS,EAAE,QAAQ;SACpB,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,eAAe,CAAC,SAAmB;QAC/C,UAAU;QACV,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,OAAO,CAAC,KAAK,EAAE,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QAErB,UAAU;QACV,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,EAAE;oBAC5C,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;wBAC3B,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,EAAE,CAAC,CAAA;wBACtC,OAAO;wBACP,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;wBAC3B,oBAAoB;wBACpB,IAAI,CAAC,eAAe,EAAE,CAAA;oBACxB,CAAC;gBACH,CAAC,CAAC,CAAA;gBAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,gBAAgB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAA;YACjD,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAA;IACxD,CAAC;IAED;;;;OAIG;IACK,eAAe,GAAG,CAAC,GAAG,EAAE;QAC9B,IAAI,OAAO,GAA0B,IAAI,CAAA;QACzC,OAAO,GAAG,EAAE;YACV,IAAI,OAAO,EAAE,CAAC;gBACZ,YAAY,CAAC,OAAO,CAAC,CAAA;YACvB,CAAC;YACD,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;gBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBAC3B,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;gBAClC,CAAC,CAAC,CAAA;YACJ,CAAC,EAAE,IAAI,CAAC,CAAA,CAAC,OAAO;QAClB,CAAC,CAAA;IACH,CAAC,CAAC,EAAE,CAAA;IAIJ;;;;;;OAMG;IACK,uBAAuB,CAAC,QAAgB;QAC9C,MAAM,OAAO,GAA2B;YACtC,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,IAAI;YACnB,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;SACjB,CAAA;QAED,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,UAAU;QACV,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,OAAO,CAAC,KAAK,EAAE,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QAErB,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QAElB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAC9B,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts"],
      sourcesContent: ["/**\n * \u6570\u636E\u52A0\u8F7D\u5668\n *\n * \u8D1F\u8D23\u4ECE\u6587\u4EF6\u7CFB\u7EDF\u52A0\u8F7D\u8BED\u7D20\u6570\u636E\uFF0C\u652F\u6301v3.0\u591A\u8BED\u79CD\u67B6\u6784\u3001\u6570\u636E\u9A8C\u8BC1\u548C\u7BA1\u7406\u3002\n * \u5B9E\u73B0\u4E86\u70ED\u91CD\u8F7D\u3001\u7F13\u5B58\u4F18\u5316\u548C\u9519\u8BEF\u6062\u590D\u673A\u5236\u3002\n *\n * @fileoverview \u6570\u636E\u52A0\u8F7D\u548C\u7BA1\u7406\u6838\u5FC3\u6A21\u5757 - v3.0\u591A\u8BED\u79CD\u67B6\u6784\n * @version 3.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport { readFile, readdir, stat } from 'fs/promises'\nimport { join, extname } from 'path'\nimport { watch } from 'fs'\n\n// \u7C7B\u578B\u5BFC\u5165\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\nimport { SUPPORTED_VERSIONS } from '../../types/common'\n\n// ============================================================================\n// \u914D\u7F6E\u5E38\u91CF\n// ============================================================================\n\n/** \u6570\u636E\u6587\u4EF6\u76EE\u5F55\u8DEF\u5F84 */\nconst DATA_DIR = join(process.cwd(), 'data')\n\n/** \u652F\u6301\u7684\u6570\u636E\u6587\u4EF6\u683C\u5F0F */\nconst SUPPORTED_FORMATS = ['.json', '.jsonl'] as const\n\n/** \u5F53\u524D\u6570\u636E\u7248\u672C */\nconst CURRENT_VERSION = '3.0.0' as const\n\n/** \u6570\u636E\u9A8C\u8BC1\u914D\u7F6E */\nconst VALIDATION_CONFIG = {\n  /** \u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 */\n  semantic_vector_dimension: 20,\n  /** \u6700\u5C0F\u8D28\u91CF\u8BC4\u5206 */\n  min_quality_score: 0.0,\n  /** \u6700\u5927\u8D28\u91CF\u8BC4\u5206 */\n  max_quality_score: 1.0,\n  /** \u5FC5\u9700\u5B57\u6BB5 */\n  required_fields: [\n    'id', 'text', 'category', 'subcategory', 'cultural_context',\n    'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n    'language_properties', 'quality_metrics', 'created_at', 'source', 'version'\n  ] as const\n} as const\n\n// ============================================================================\n// \u63A5\u53E3\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u6570\u636E\u52A0\u8F7D\u914D\u7F6E\u63A5\u53E3\n */\nexport interface DataLoaderConfig {\n  /** \u6570\u636E\u76EE\u5F55\u8DEF\u5F84 */\n  dataDir?: string\n  /** \u662F\u5426\u542F\u7528\u70ED\u91CD\u8F7D */\n  enableHotReload?: boolean\n  /** \u662F\u5426\u542F\u7528\u6570\u636E\u9A8C\u8BC1 */\n  enableValidation?: boolean\n  /** \u662F\u5426\u542F\u7528\u7F13\u5B58 */\n  enableCache?: boolean\n  /** \u7F13\u5B58TTL (\u79D2) */\n  cacheTTL?: number\n  /** \u6700\u5927\u91CD\u8BD5\u6B21\u6570 */\n  maxRetries?: number\n}\n\n/**\n * \u6570\u636E\u52A0\u8F7D\u7ED3\u679C\u63A5\u53E3\n */\nexport interface DataLoadResult {\n  /** \u52A0\u8F7D\u7684\u8BED\u7D20\u6570\u636E */\n  morphemes: Morpheme[]\n  /** \u52A0\u8F7D\u7EDF\u8BA1\u4FE1\u606F */\n  stats: {\n    total_count: number\n    by_category: Record<string, number>\n    by_context: Record<string, number>\n    avg_quality: number\n    load_time: number\n  }\n  /** \u9A8C\u8BC1\u7ED3\u679C */\n  validation: {\n    passed: boolean\n    errors: string[]\n    warnings: string[]\n  }\n  /** \u52A0\u8F7D\u65F6\u95F4\u6233 */\n  timestamp: number\n}\n\n/**\n * \u6570\u636E\u9A8C\u8BC1\u7ED3\u679C\u63A5\u53E3\n */\nexport interface ValidationResult {\n  /** \u9A8C\u8BC1\u662F\u5426\u901A\u8FC7 */\n  passed: boolean\n  /** \u9519\u8BEF\u4FE1\u606F */\n  errors: string[]\n  /** \u8B66\u544A\u4FE1\u606F */\n  warnings: string[]\n  /** \u9A8C\u8BC1\u7684\u6570\u636E\u9879\u6570\u91CF */\n  validated_count: number\n  /** \u9A8C\u8BC1\u8017\u65F6 (\u6BEB\u79D2) */\n  validation_time: number\n}\n\n// ============================================================================\n// \u6570\u636E\u52A0\u8F7D\u5668\u7C7B\n// ============================================================================\n\n/**\n * \u6570\u636E\u52A0\u8F7D\u5668\u7C7B\n * \n * \u63D0\u4F9B\u8BED\u7D20\u6570\u636E\u7684\u52A0\u8F7D\u3001\u9A8C\u8BC1\u3001\u7F13\u5B58\u548C\u70ED\u91CD\u8F7D\u529F\u80FD\n */\nexport class DataLoader {\n  private config: Required<DataLoaderConfig>\n  private cache: Map<string, { data: Morpheme[], timestamp: number }> = new Map()\n  private watchers: Map<string, any> = new Map()\n  private loadPromise: Promise<DataLoadResult> | null = null\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   * \n   * @param config \u6570\u636E\u52A0\u8F7D\u914D\u7F6E\n   */\n  constructor(config: DataLoaderConfig = {}) {\n    this.config = {\n      dataDir: config.dataDir || DATA_DIR,\n      enableHotReload: config.enableHotReload ?? true,\n      enableValidation: config.enableValidation ?? true,\n      enableCache: config.enableCache ?? true,\n      cacheTTL: config.cacheTTL ?? 3600, // 1\u5C0F\u65F6\n      maxRetries: config.maxRetries ?? 3\n    }\n  }\n\n  /**\n   * \u52A0\u8F7D\u6240\u6709\u8BED\u7D20\u6570\u636E\n   * \n   * @returns \u6570\u636E\u52A0\u8F7D\u7ED3\u679C\n   */\n  async loadAll(): Promise<DataLoadResult> {\n    // \u9632\u6B62\u5E76\u53D1\u52A0\u8F7D\n    if (this.loadPromise) {\n      return this.loadPromise\n    }\n\n    this.loadPromise = this._performLoad()\n    \n    try {\n      const result = await this.loadPromise\n      return result\n    } finally {\n      this.loadPromise = null\n    }\n  }\n\n  /**\n   * \u6267\u884C\u6570\u636E\u52A0\u8F7D\n   * \n   * @private\n   * @returns \u6570\u636E\u52A0\u8F7D\u7ED3\u679C\n   */\n  private async _performLoad(): Promise<DataLoadResult> {\n    const startTime = Date.now()\n    \n    try {\n      // 1. \u626B\u63CF\u6570\u636E\u6587\u4EF6\n      const dataFiles = await this._scanDataFiles()\n      \n      // 2. \u52A0\u8F7D\u6570\u636E\u6587\u4EF6\n      const allMorphemes: Morpheme[] = []\n\n      for (const filePath of dataFiles) {\n        const morphemes = await this._loadDataFile(filePath)\n        allMorphemes.push(...morphemes)\n      }\n\n      // 3. \u6570\u636E\u9A8C\u8BC1\n      const validation = this.config.enableValidation \n        ? await this._validateData(allMorphemes)\n        : { passed: true, errors: [], warnings: [], validated_count: allMorphemes.length, validation_time: 0 }\n\n      // 4. \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F\n      const stats = this._calculateStats(allMorphemes, Date.now() - startTime)\n\n      // 5. \u8BBE\u7F6E\u70ED\u91CD\u8F7D\n      if (this.config.enableHotReload) {\n        await this._setupHotReload(dataFiles)\n      }\n\n      const result: DataLoadResult = {\n        morphemes: allMorphemes,\n        stats,\n        validation,\n        timestamp: Date.now()\n      }\n\n      console.log(`\u2705 \u6570\u636E\u52A0\u8F7D\u5B8C\u6210: ${allMorphemes.length}\u4E2A\u8BED\u7D20, \u8017\u65F6${stats.load_time}ms`)\n      \n      return result\n\n    } catch (error) {\n      console.error('\u274C \u6570\u636E\u52A0\u8F7D\u5931\u8D25:', error)\n      throw new Error(`\u6570\u636E\u52A0\u8F7D\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u626B\u63CF\u6570\u636E\u6587\u4EF6\n   * \n   * @private\n   * @returns \u6570\u636E\u6587\u4EF6\u8DEF\u5F84\u5217\u8868\n   */\n  private async _scanDataFiles(): Promise<string[]> {\n    try {\n      const files = await readdir(this.config.dataDir)\n      const dataFiles: string[] = []\n\n      for (const file of files) {\n        const filePath = join(this.config.dataDir, file)\n        const fileStat = await stat(filePath)\n\n        // \u53EA\u52A0\u8F7D\u8BED\u7D20\u6570\u636E\u6587\u4EF6\uFF0C\u8DF3\u8FC7\u5176\u4ED6\u6587\u4EF6\n        if (fileStat.isFile() &&\n            SUPPORTED_FORMATS.includes(extname(file) as any) &&\n            (file.includes('morpheme') || file.includes('\u8BED\u7D20'))) {\n          dataFiles.push(filePath)\n        }\n      }\n\n      if (dataFiles.length === 0) {\n        throw new Error(`\u672A\u627E\u5230\u6570\u636E\u6587\u4EF6\uFF0C\u76EE\u5F55: ${this.config.dataDir}`)\n      }\n\n      console.log(`\uD83D\uDCC1 \u53D1\u73B0${dataFiles.length}\u4E2A\u6570\u636E\u6587\u4EF6`)\n      return dataFiles\n\n    } catch (error) {\n      throw new Error(`\u626B\u63CF\u6570\u636E\u6587\u4EF6\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u52A0\u8F7D\u5355\u4E2A\u6570\u636E\u6587\u4EF6\n   * \n   * @private\n   * @param filePath \u6587\u4EF6\u8DEF\u5F84\n   * @returns \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   */\n  private async _loadDataFile(filePath: string): Promise<Morpheme[]> {\n    try {\n      // \u68C0\u67E5\u7F13\u5B58\n      if (this.config.enableCache) {\n        const cached = this.cache.get(filePath)\n        if (cached && Date.now() - cached.timestamp < this.config.cacheTTL * 1000) {\n          console.log(`\uD83D\uDCCB \u4F7F\u7528\u7F13\u5B58\u6570\u636E: ${filePath}`)\n          return cached.data\n        }\n      }\n\n      // \u8BFB\u53D6\u6587\u4EF6\u5185\u5BB9\n      const content = await readFile(filePath, 'utf-8')\n      let morphemes: Morpheme[]\n\n      // \u6839\u636E\u6587\u4EF6\u683C\u5F0F\u89E3\u6790\n      if (extname(filePath) === '.json') {\n        const parsed = JSON.parse(content)\n\n        // \u652F\u6301\u4E24\u79CD\u683C\u5F0F\uFF1A\u76F4\u63A5\u6570\u7EC4\u6216\u5305\u542Bmorphemes\u5B57\u6BB5\u7684\u5BF9\u8C61\n        if (Array.isArray(parsed)) {\n          morphemes = parsed\n        } else if (parsed.morphemes && Array.isArray(parsed.morphemes)) {\n          morphemes = parsed.morphemes\n          console.log(`\uD83D\uDCCB \u68C0\u6D4B\u5230\u5D4C\u5957\u683C\u5F0F\uFF0C\u63D0\u53D6${morphemes.length}\u4E2A\u8BED\u7D20`)\n        } else {\n          throw new Error(`\u65E0\u6548\u7684JSON\u683C\u5F0F: \u671F\u671B\u6570\u7EC4\u6216\u5305\u542Bmorphemes\u5B57\u6BB5\u7684\u5BF9\u8C61`)\n        }\n      } else if (extname(filePath) === '.jsonl') {\n        morphemes = content\n          .split('\\n')\n          .filter(line => line.trim())\n          .map(line => JSON.parse(line))\n      } else {\n        throw new Error(`\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u683C\u5F0F: ${extname(filePath)}`)\n      }\n\n      // \u66F4\u65B0\u7F13\u5B58\n      if (this.config.enableCache) {\n        this.cache.set(filePath, {\n          data: morphemes,\n          timestamp: Date.now()\n        })\n      }\n\n      console.log(`\uD83D\uDCC4 \u52A0\u8F7D\u6587\u4EF6: ${filePath} (${morphemes.length}\u4E2A\u8BED\u7D20)`)\n      return morphemes\n\n    } catch (error) {\n      throw new Error(`\u52A0\u8F7D\u6587\u4EF6\u5931\u8D25 ${filePath}: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u9A8C\u8BC1\u6570\u636E\u5B8C\u6574\u6027\u548C\u6B63\u786E\u6027\n   * \n   * @private\n   * @param morphemes \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * @returns \u9A8C\u8BC1\u7ED3\u679C\n   */\n  private async _validateData(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: string[] = []\n    const warnings: string[] = []\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      const prefix = `\u8BED\u7D20[${i}](${morpheme.id || 'unknown'})`\n\n      // \u9A8C\u8BC1\u5FC5\u9700\u5B57\u6BB5\n      for (const field of VALIDATION_CONFIG.required_fields) {\n        if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined) {\n          errors.push(`${prefix}: \u7F3A\u5C11\u5FC5\u9700\u5B57\u6BB5 '${field}'`)\n        }\n      }\n\n      // \u9A8C\u8BC1\u6570\u636E\u7C7B\u578B\u548C\u8303\u56F4\n      if (typeof morpheme.usage_frequency !== 'number' || \n          morpheme.usage_frequency < 0 || morpheme.usage_frequency > 1) {\n        errors.push(`${prefix}: usage_frequency \u5FC5\u987B\u662F [0-1] \u8303\u56F4\u5185\u7684\u6570\u5B57`)\n      }\n\n      if (typeof morpheme.quality_score !== 'number' || \n          morpheme.quality_score < VALIDATION_CONFIG.min_quality_score || \n          morpheme.quality_score > VALIDATION_CONFIG.max_quality_score) {\n        errors.push(`${prefix}: quality_score \u5FC5\u987B\u662F [${VALIDATION_CONFIG.min_quality_score}-${VALIDATION_CONFIG.max_quality_score}] \u8303\u56F4\u5185\u7684\u6570\u5B57`)\n      }\n\n      // \u9A8C\u8BC1\u8BED\u4E49\u5411\u91CF\n      if (!Array.isArray(morpheme.semantic_vector) || \n          morpheme.semantic_vector.length !== VALIDATION_CONFIG.semantic_vector_dimension) {\n        errors.push(`${prefix}: semantic_vector \u5FC5\u987B\u662F\u957F\u5EA6\u4E3A ${VALIDATION_CONFIG.semantic_vector_dimension} \u7684\u6570\u5B57\u6570\u7EC4`)\n      }\n\n      // \u9A8C\u8BC1\u7248\u672C\n      if (morpheme.version && morpheme.version !== CURRENT_VERSION) {\n        warnings.push(`${prefix}: \u7248\u672C '${morpheme.version}' \u4E0D\u662F\u5F53\u524D\u7248\u672C ${CURRENT_VERSION}\uFF0C\u5EFA\u8BAE\u5347\u7EA7\u6570\u636E`)\n      }\n    }\n\n    const validation_time = Date.now() - startTime\n    const passed = errors.length === 0\n\n    if (!passed) {\n      console.warn(`\u26A0\uFE0F \u6570\u636E\u9A8C\u8BC1\u53D1\u73B0 ${errors.length} \u4E2A\u9519\u8BEF, ${warnings.length} \u4E2A\u8B66\u544A`)\n    } else {\n      console.log(`\u2705 \u6570\u636E\u9A8C\u8BC1\u901A\u8FC7: ${morphemes.length}\u4E2A\u8BED\u7D20, \u8017\u65F6${validation_time}ms`)\n    }\n\n    return {\n      passed,\n      errors,\n      warnings,\n      validated_count: morphemes.length,\n      validation_time\n    }\n  }\n\n  /**\n   * \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F\n   * \n   * @private\n   * @param morphemes \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * @param loadTime \u52A0\u8F7D\u8017\u65F6\n   * @returns \u7EDF\u8BA1\u4FE1\u606F\n   */\n  private _calculateStats(morphemes: Morpheme[], loadTime: number) {\n    const by_category: Record<string, number> = {}\n    const by_context: Record<string, number> = {}\n    let total_quality = 0\n\n    for (const morpheme of morphemes) {\n      // \u6309\u7C7B\u522B\u7EDF\u8BA1\n      by_category[morpheme.category] = (by_category[morpheme.category] || 0) + 1\n      \n      // \u6309\u6587\u5316\u8BED\u5883\u7EDF\u8BA1 (\u652F\u6301v3.0\u591A\u7EF4\u5EA6\u6587\u5316\u8BED\u5883)\n      const contextKey = typeof morpheme.cultural_context === 'string'\n        ? morpheme.cultural_context\n        : `${morpheme.cultural_context.traditionality}_${morpheme.cultural_context.formality}`\n      by_context[contextKey] = (by_context[contextKey] || 0) + 1\n      \n      // \u7D2F\u8BA1\u8D28\u91CF\u8BC4\u5206\n      total_quality += morpheme.quality_score\n    }\n\n    return {\n      total_count: morphemes.length,\n      by_category,\n      by_context,\n      avg_quality: morphemes.length > 0 ? total_quality / morphemes.length : 0,\n      load_time: loadTime\n    }\n  }\n\n  /**\n   * \u8BBE\u7F6E\u70ED\u91CD\u8F7D\u76D1\u542C\n   * \n   * @private\n   * @param dataFiles \u6570\u636E\u6587\u4EF6\u8DEF\u5F84\u5217\u8868\n   */\n  private async _setupHotReload(dataFiles: string[]): Promise<void> {\n    // \u6E05\u7406\u73B0\u6709\u76D1\u542C\u5668\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // \u8BBE\u7F6E\u65B0\u7684\u76D1\u542C\u5668\n    for (const filePath of dataFiles) {\n      try {\n        const watcher = watch(filePath, (eventType) => {\n          if (eventType === 'change') {\n            console.log(`\uD83D\uDD04 \u68C0\u6D4B\u5230\u6587\u4EF6\u53D8\u5316: ${filePath}`)\n            // \u6E05\u9664\u7F13\u5B58\n            this.cache.delete(filePath)\n            // \u89E6\u53D1\u91CD\u65B0\u52A0\u8F7D (\u53EF\u4EE5\u6DFB\u52A0\u9632\u6296\u903B\u8F91)\n            this._debounceReload()\n          }\n        })\n\n        this.watchers.set(filePath, watcher)\n      } catch (error) {\n        console.warn(`\u26A0\uFE0F \u65E0\u6CD5\u76D1\u542C\u6587\u4EF6\u53D8\u5316: ${filePath}`, error)\n      }\n    }\n\n    console.log(`\uD83D\uDC41\uFE0F \u70ED\u91CD\u8F7D\u5DF2\u542F\u7528\uFF0C\u76D1\u542C ${this.watchers.size} \u4E2A\u6587\u4EF6`)\n  }\n\n  /**\n   * \u9632\u6296\u91CD\u65B0\u52A0\u8F7D\n   * \n   * @private\n   */\n  private _debounceReload = (() => {\n    let timeout: NodeJS.Timeout | null = null\n    return () => {\n      if (timeout) {\n        clearTimeout(timeout)\n      }\n      timeout = setTimeout(() => {\n        console.log('\uD83D\uDD04 \u6267\u884C\u70ED\u91CD\u8F7D...')\n        this.loadAll().catch(error => {\n          console.error('\u274C \u70ED\u91CD\u8F7D\u5931\u8D25:', error)\n        })\n      }, 1000) // 1\u79D2\u9632\u6296\n    }\n  })()\n\n\n\n  /**\n   * \u63A8\u65AD\u8BCD\u6CD5\u7C7B\u578B\n   *\n   * @private\n   * @param category \u8BED\u7D20\u7C7B\u522B\n   * @returns \u8BCD\u6CD5\u7C7B\u578B\n   */\n  private _inferMorphologicalType(category: string): string {\n    const typeMap: Record<string, string> = {\n      'emotions': '\u5F62\u5BB9\u8BCD',\n      'professions': '\u540D\u8BCD',\n      'characteristics': '\u5F62\u5BB9\u8BCD',\n      'objects': '\u540D\u8BCD',\n      'actions': '\u52A8\u8BCD',\n      'concepts': '\u540D\u8BCD'\n    }\n\n    return typeMap[category] || '\u672A\u77E5'\n  }\n\n  /**\n   * \u6E05\u7406\u8D44\u6E90\n   */\n  destroy(): void {\n    // \u5173\u95ED\u6587\u4EF6\u76D1\u542C\u5668\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // \u6E05\u7A7A\u7F13\u5B58\n    this.cache.clear()\n\n    console.log('\uD83E\uDDF9 \u6570\u636E\u52A0\u8F7D\u5668\u8D44\u6E90\u5DF2\u6E05\u7406')\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5414a9a1ad7b424f9da77b616772ce954d41ad5c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2i3pzto8wf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2i3pzto8wf();
/**
 * 数据加载器
 *
 * 负责从文件系统加载语素数据，支持v3.0多语种架构、数据验证和管理。
 * 实现了热重载、缓存优化和错误恢复机制。
 *
 * @fileoverview 数据加载和管理核心模块 - v3.0多语种架构
 * @version 3.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */
import { readFile, readdir, stat } from 'fs/promises';
import { join, extname } from 'path';
import { watch } from 'fs';
// ============================================================================
// 配置常量
// ============================================================================
/** 数据文件目录路径 */
const DATA_DIR =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[0]++, join(process.cwd(), 'data'));
/** 支持的数据文件格式 */
const SUPPORTED_FORMATS =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[1]++, ['.json', '.jsonl']);
/** 当前数据版本 */
const CURRENT_VERSION =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[2]++, '3.0.0');
/** 数据验证配置 */
const VALIDATION_CONFIG =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[3]++, {
  /** 语义向量维度 */
  semantic_vector_dimension: 20,
  /** 最小质量评分 */
  min_quality_score: 0.0,
  /** 最大质量评分 */
  max_quality_score: 1.0,
  /** 必需字段 */
  required_fields: ['id', 'text', 'category', 'subcategory', 'cultural_context', 'usage_frequency', 'quality_score', 'semantic_vector', 'tags', 'language_properties', 'quality_metrics', 'created_at', 'source', 'version']
});
// ============================================================================
// 数据加载器类
// ============================================================================
/**
 * 数据加载器类
 *
 * 提供语素数据的加载、验证、缓存和热重载功能
 */
export class DataLoader {
  config;
  cache =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[4]++, new Map());
  watchers =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[5]++, new Map());
  loadPromise =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[6]++, null);
  /**
   * 构造函数
   *
   * @param config 数据加载配置
   */
  constructor(config =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[0]++;
    cov_2i3pzto8wf().s[7]++;
    this.config = {
      dataDir:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[1][0]++, config.dataDir) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[1][1]++, DATA_DIR),
      enableHotReload:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[2][0]++, config.enableHotReload) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[2][1]++, true),
      enableValidation:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[3][0]++, config.enableValidation) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[3][1]++, true),
      enableCache:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[4][0]++, config.enableCache) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[4][1]++, true),
      cacheTTL:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[5][0]++, config.cacheTTL) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[5][1]++, 3600),
      // 1小时
      maxRetries:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[6][0]++, config.maxRetries) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[6][1]++, 3)
    };
  }
  /**
   * 加载所有语素数据
   *
   * @returns 数据加载结果
   */
  async loadAll() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[1]++;
    cov_2i3pzto8wf().s[8]++;
    // 防止并发加载
    if (this.loadPromise) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[7][0]++;
      cov_2i3pzto8wf().s[9]++;
      return this.loadPromise;
    } else
    /* istanbul ignore next */
    {
      cov_2i3pzto8wf().b[7][1]++;
    }
    cov_2i3pzto8wf().s[10]++;
    this.loadPromise = this._performLoad();
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[11]++;
    try {
      const result =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[12]++, await this.loadPromise);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[13]++;
      return result;
    } finally {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[14]++;
      this.loadPromise = null;
    }
  }
  /**
   * 执行数据加载
   *
   * @private
   * @returns 数据加载结果
   */
  async _performLoad() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[2]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[15]++, Date.now());
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[16]++;
    try {
      // 1. 扫描数据文件
      const dataFiles =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[17]++, await this._scanDataFiles());
      // 2. 加载数据文件
      const allMorphemes =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[18]++, []);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[19]++;
      for (const filePath of dataFiles) {
        const morphemes =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[20]++, await this._loadDataFile(filePath));
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[21]++;
        allMorphemes.push(...morphemes);
      }
      // 3. 数据验证
      const validation =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[22]++, this.config.enableValidation ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[8][0]++, await this._validateData(allMorphemes)) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[8][1]++, {
        passed: true,
        errors: [],
        warnings: [],
        validated_count: allMorphemes.length,
        validation_time: 0
      }));
      // 4. 计算统计信息
      const stats =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[23]++, this._calculateStats(allMorphemes, Date.now() - startTime));
      // 5. 设置热重载
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[24]++;
      if (this.config.enableHotReload) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[9][0]++;
        cov_2i3pzto8wf().s[25]++;
        await this._setupHotReload(dataFiles);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[9][1]++;
      }
      const result =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[26]++, {
        morphemes: allMorphemes,
        stats,
        validation,
        timestamp: Date.now()
      });
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[27]++;
      console.log(`✅ 数据加载完成: ${allMorphemes.length}个语素, 耗时${stats.load_time}ms`);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[28]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[29]++;
      console.error('❌ 数据加载失败:', error);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[30]++;
      throw new Error(`数据加载失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[10][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[10][1]++, String(error))}`);
    }
  }
  /**
   * 扫描数据文件
   *
   * @private
   * @returns 数据文件路径列表
   */
  async _scanDataFiles() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[3]++;
    cov_2i3pzto8wf().s[31]++;
    try {
      const files =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[32]++, await readdir(this.config.dataDir));
      const dataFiles =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[33]++, []);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[34]++;
      for (const file of files) {
        const filePath =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[35]++, join(this.config.dataDir, file));
        const fileStat =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[36]++, await stat(filePath));
        // 只加载语素数据文件，跳过其他文件
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[37]++;
        if (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][0]++, fileStat.isFile()) &&
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][1]++, SUPPORTED_FORMATS.includes(extname(file))) && (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][2]++, file.includes('morpheme')) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][3]++, file.includes('语素')))) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[11][0]++;
          cov_2i3pzto8wf().s[38]++;
          dataFiles.push(filePath);
        } else
        /* istanbul ignore next */
        {
          cov_2i3pzto8wf().b[11][1]++;
        }
      }
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[39]++;
      if (dataFiles.length === 0) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[13][0]++;
        cov_2i3pzto8wf().s[40]++;
        throw new Error(`未找到数据文件，目录: ${this.config.dataDir}`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[13][1]++;
      }
      cov_2i3pzto8wf().s[41]++;
      console.log(`📁 发现${dataFiles.length}个数据文件`);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[42]++;
      return dataFiles;
    } catch (error) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[43]++;
      throw new Error(`扫描数据文件失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[14][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[14][1]++, String(error))}`);
    }
  }
  /**
   * 加载单个数据文件
   *
   * @private
   * @param filePath 文件路径
   * @returns 语素数据数组
   */
  async _loadDataFile(filePath) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[4]++;
    cov_2i3pzto8wf().s[44]++;
    try {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[45]++;
      // 检查缓存
      if (this.config.enableCache) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[15][0]++;
        const cached =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[46]++, this.cache.get(filePath));
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[47]++;
        if (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[17][0]++, cached) &&
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[17][1]++, Date.now() - cached.timestamp < this.config.cacheTTL * 1000)) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[16][0]++;
          cov_2i3pzto8wf().s[48]++;
          console.log(`📋 使用缓存数据: ${filePath}`);
          /* istanbul ignore next */
          cov_2i3pzto8wf().s[49]++;
          return cached.data;
        } else
        /* istanbul ignore next */
        {
          cov_2i3pzto8wf().b[16][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[15][1]++;
      }
      // 读取文件内容
      const content =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[50]++, await readFile(filePath, 'utf-8'));
      let morphemes;
      // 根据文件格式解析
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[51]++;
      if (extname(filePath) === '.json') {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[18][0]++;
        const parsed =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[52]++, JSON.parse(content));
        // 支持两种格式：直接数组或包含morphemes字段的对象
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[53]++;
        if (Array.isArray(parsed)) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[19][0]++;
          cov_2i3pzto8wf().s[54]++;
          morphemes = parsed;
        } else {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[19][1]++;
          cov_2i3pzto8wf().s[55]++;
          if (
          /* istanbul ignore next */
          (cov_2i3pzto8wf().b[21][0]++, parsed.morphemes) &&
          /* istanbul ignore next */
          (cov_2i3pzto8wf().b[21][1]++, Array.isArray(parsed.morphemes))) {
            /* istanbul ignore next */
            cov_2i3pzto8wf().b[20][0]++;
            cov_2i3pzto8wf().s[56]++;
            morphemes = parsed.morphemes;
            /* istanbul ignore next */
            cov_2i3pzto8wf().s[57]++;
            console.log(`📋 检测到嵌套格式，提取${morphemes.length}个语素`);
          } else {
            /* istanbul ignore next */
            cov_2i3pzto8wf().b[20][1]++;
            cov_2i3pzto8wf().s[58]++;
            throw new Error(`无效的JSON格式: 期望数组或包含morphemes字段的对象`);
          }
        }
      } else {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[18][1]++;
        cov_2i3pzto8wf().s[59]++;
        if (extname(filePath) === '.jsonl') {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[22][0]++;
          cov_2i3pzto8wf().s[60]++;
          morphemes = content.split('\n').filter(line => {
            /* istanbul ignore next */
            cov_2i3pzto8wf().f[5]++;
            cov_2i3pzto8wf().s[61]++;
            return line.trim();
          }).map(line => {
            /* istanbul ignore next */
            cov_2i3pzto8wf().f[6]++;
            cov_2i3pzto8wf().s[62]++;
            return JSON.parse(line);
          });
        } else {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[22][1]++;
          cov_2i3pzto8wf().s[63]++;
          throw new Error(`不支持的文件格式: ${extname(filePath)}`);
        }
      }
      // 更新缓存
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[64]++;
      if (this.config.enableCache) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[23][0]++;
        cov_2i3pzto8wf().s[65]++;
        this.cache.set(filePath, {
          data: morphemes,
          timestamp: Date.now()
        });
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[23][1]++;
      }
      cov_2i3pzto8wf().s[66]++;
      console.log(`📄 加载文件: ${filePath} (${morphemes.length}个语素)`);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[67]++;
      return morphemes;
    } catch (error) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[68]++;
      throw new Error(`加载文件失败 ${filePath}: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[24][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[24][1]++, String(error))}`);
    }
  }
  /**
   * 验证数据完整性和正确性
   *
   * @private
   * @param morphemes 语素数据数组
   * @returns 验证结果
   */
  async _validateData(morphemes) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[7]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[69]++, Date.now());
    const errors =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[70]++, []);
    const warnings =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[71]++, []);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[72]++;
    for (let i =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[73]++, 0); i < morphemes.length; i++) {
      const morpheme =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[74]++, morphemes[i]);
      const prefix =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[75]++, `语素[${i}](${
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[25][0]++, morpheme.id) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[25][1]++, 'unknown')})`);
      // 验证必需字段
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[76]++;
      for (const field of VALIDATION_CONFIG.required_fields) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[77]++;
        if (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[27][0]++, !(field in morpheme)) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[27][1]++, morpheme[field] === undefined)) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[26][0]++;
          cov_2i3pzto8wf().s[78]++;
          errors.push(`${prefix}: 缺少必需字段 '${field}'`);
        } else
        /* istanbul ignore next */
        {
          cov_2i3pzto8wf().b[26][1]++;
        }
      }
      // 验证数据类型和范围
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[79]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[29][0]++, typeof morpheme.usage_frequency !== 'number') ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[29][1]++, morpheme.usage_frequency < 0) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[29][2]++, morpheme.usage_frequency > 1)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[28][0]++;
        cov_2i3pzto8wf().s[80]++;
        errors.push(`${prefix}: usage_frequency 必须是 [0-1] 范围内的数字`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[28][1]++;
      }
      cov_2i3pzto8wf().s[81]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[31][0]++, typeof morpheme.quality_score !== 'number') ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[31][1]++, morpheme.quality_score < VALIDATION_CONFIG.min_quality_score) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[31][2]++, morpheme.quality_score > VALIDATION_CONFIG.max_quality_score)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[30][0]++;
        cov_2i3pzto8wf().s[82]++;
        errors.push(`${prefix}: quality_score 必须是 [${VALIDATION_CONFIG.min_quality_score}-${VALIDATION_CONFIG.max_quality_score}] 范围内的数字`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[30][1]++;
      }
      // 验证语义向量
      cov_2i3pzto8wf().s[83]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[33][0]++, !Array.isArray(morpheme.semantic_vector)) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[33][1]++, morpheme.semantic_vector.length !== VALIDATION_CONFIG.semantic_vector_dimension)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[32][0]++;
        cov_2i3pzto8wf().s[84]++;
        errors.push(`${prefix}: semantic_vector 必须是长度为 ${VALIDATION_CONFIG.semantic_vector_dimension} 的数字数组`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[32][1]++;
      }
      // 验证版本
      cov_2i3pzto8wf().s[85]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[35][0]++, morpheme.version) &&
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[35][1]++, morpheme.version !== CURRENT_VERSION)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[34][0]++;
        cov_2i3pzto8wf().s[86]++;
        warnings.push(`${prefix}: 版本 '${morpheme.version}' 不是当前版本 ${CURRENT_VERSION}，建议升级数据`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[34][1]++;
      }
    }
    const validation_time =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[87]++, Date.now() - startTime);
    const passed =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[88]++, errors.length === 0);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[89]++;
    if (!passed) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[36][0]++;
      cov_2i3pzto8wf().s[90]++;
      console.warn(`⚠️ 数据验证发现 ${errors.length} 个错误, ${warnings.length} 个警告`);
    } else {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[36][1]++;
      cov_2i3pzto8wf().s[91]++;
      console.log(`✅ 数据验证通过: ${morphemes.length}个语素, 耗时${validation_time}ms`);
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[92]++;
    return {
      passed,
      errors,
      warnings,
      validated_count: morphemes.length,
      validation_time
    };
  }
  /**
   * 计算统计信息
   *
   * @private
   * @param morphemes 语素数据数组
   * @param loadTime 加载耗时
   * @returns 统计信息
   */
  _calculateStats(morphemes, loadTime) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[8]++;
    const by_category =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[93]++, {});
    const by_context =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[94]++, {});
    let total_quality =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[95]++, 0);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[96]++;
    for (const morpheme of morphemes) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[97]++;
      // 按类别统计
      by_category[morpheme.category] = (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[37][0]++, by_category[morpheme.category]) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[37][1]++, 0)) + 1;
      // 按文化语境统计 (支持v3.0多维度文化语境)
      const contextKey =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[98]++, typeof morpheme.cultural_context === 'string' ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[38][0]++, morpheme.cultural_context) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[38][1]++, `${morpheme.cultural_context.traditionality}_${morpheme.cultural_context.formality}`));
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[99]++;
      by_context[contextKey] = (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[39][0]++, by_context[contextKey]) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[39][1]++, 0)) + 1;
      // 累计质量评分
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[100]++;
      total_quality += morpheme.quality_score;
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[101]++;
    return {
      total_count: morphemes.length,
      by_category,
      by_context,
      avg_quality: morphemes.length > 0 ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[40][0]++, total_quality / morphemes.length) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[40][1]++, 0),
      load_time: loadTime
    };
  }
  /**
   * 设置热重载监听
   *
   * @private
   * @param dataFiles 数据文件路径列表
   */
  async _setupHotReload(dataFiles) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[9]++;
    cov_2i3pzto8wf().s[102]++;
    // 清理现有监听器
    for (const [path, watcher] of this.watchers) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[103]++;
      watcher.close();
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[104]++;
    this.watchers.clear();
    // 设置新的监听器
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[105]++;
    for (const filePath of dataFiles) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[106]++;
      try {
        const watcher =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[107]++, watch(filePath, eventType => {
          /* istanbul ignore next */
          cov_2i3pzto8wf().f[10]++;
          cov_2i3pzto8wf().s[108]++;
          if (eventType === 'change') {
            /* istanbul ignore next */
            cov_2i3pzto8wf().b[41][0]++;
            cov_2i3pzto8wf().s[109]++;
            console.log(`🔄 检测到文件变化: ${filePath}`);
            // 清除缓存
            /* istanbul ignore next */
            cov_2i3pzto8wf().s[110]++;
            this.cache.delete(filePath);
            // 触发重新加载 (可以添加防抖逻辑)
            /* istanbul ignore next */
            cov_2i3pzto8wf().s[111]++;
            this._debounceReload();
          } else
          /* istanbul ignore next */
          {
            cov_2i3pzto8wf().b[41][1]++;
          }
        }));
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[112]++;
        this.watchers.set(filePath, watcher);
      } catch (error) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[113]++;
        console.warn(`⚠️ 无法监听文件变化: ${filePath}`, error);
      }
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[114]++;
    console.log(`👁️ 热重载已启用，监听 ${this.watchers.size} 个文件`);
  }
  /**
   * 防抖重新加载
   *
   * @private
   */
  _debounceReload =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[115]++, (() => {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[11]++;
    let timeout =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[116]++, null);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[117]++;
    return () => {
      /* istanbul ignore next */
      cov_2i3pzto8wf().f[12]++;
      cov_2i3pzto8wf().s[118]++;
      if (timeout) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[42][0]++;
        cov_2i3pzto8wf().s[119]++;
        clearTimeout(timeout);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[42][1]++;
      }
      cov_2i3pzto8wf().s[120]++;
      timeout = setTimeout(() => {
        /* istanbul ignore next */
        cov_2i3pzto8wf().f[13]++;
        cov_2i3pzto8wf().s[121]++;
        console.log('🔄 执行热重载...');
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[122]++;
        this.loadAll().catch(error => {
          /* istanbul ignore next */
          cov_2i3pzto8wf().f[14]++;
          cov_2i3pzto8wf().s[123]++;
          console.error('❌ 热重载失败:', error);
        });
      }, 1000); // 1秒防抖
    };
  })());
  /**
   * 推断词法类型
   *
   * @private
   * @param category 语素类别
   * @returns 词法类型
   */
  _inferMorphologicalType(category) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[15]++;
    const typeMap =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[124]++, {
      'emotions': '形容词',
      'professions': '名词',
      'characteristics': '形容词',
      'objects': '名词',
      'actions': '动词',
      'concepts': '名词'
    });
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[125]++;
    return /* istanbul ignore next */(cov_2i3pzto8wf().b[43][0]++, typeMap[category]) ||
    /* istanbul ignore next */
    (cov_2i3pzto8wf().b[43][1]++, '未知');
  }
  /**
   * 清理资源
   */
  destroy() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[16]++;
    cov_2i3pzto8wf().s[126]++;
    // 关闭文件监听器
    for (const [path, watcher] of this.watchers) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[127]++;
      watcher.close();
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[128]++;
    this.watchers.clear();
    // 清空缓存
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[129]++;
    this.cache.clear();
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[130]++;
    console.log('🧹 数据加载器资源已清理');
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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