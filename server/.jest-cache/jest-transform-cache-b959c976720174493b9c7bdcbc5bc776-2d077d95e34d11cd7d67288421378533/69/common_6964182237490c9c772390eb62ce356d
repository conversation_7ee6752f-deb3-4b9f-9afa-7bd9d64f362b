f0f43fb6c68aebf1afe6e61d4642b602
/* istanbul ignore next */
function cov_1ym1naerw() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts";
  var hash = "0e0989cb8f0b25f69486fe64802eaad3f4fff651";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 34
        },
        end: {
          line: 2,
          column: 61
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts",
      mappings: "AAAA,UAAU;AACV,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAU,CAAA",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/common.ts"],
      sourcesContent: ["// \u652F\u6301\u7684\u7248\u672C\u5E38\u91CF\nexport const SUPPORTED_VERSIONS = ['1.0.0', '1.1.0', '2.0.0'] as const\n\n// \u4ECE\u5E38\u91CF\u63A8\u5BFC\u51FA\u7248\u672C\u7C7B\u578B\nexport type MorphemeVersion = typeof SUPPORTED_VERSIONS[number]\n\n\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0e0989cb8f0b25f69486fe64802eaad3f4fff651"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ym1naerw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ym1naerw();
// 支持的版本常量
export const SUPPORTED_VERSIONS =
/* istanbul ignore next */
(cov_1ym1naerw().s[0]++, ['1.0.0', '1.1.0', '2.0.0']);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTVVBQT1JURURfVkVSU0lPTlMiLCJjb3ZfMXltMW5hZXJ3IiwicyJdLCJzb3VyY2VzIjpbIi9ob21lL3AvZGV2ZWxvcC93b3Jrc3BhY2UvbmFtZXItdjYvc2VydmVyL3R5cGVzL2NvbW1vbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDmlK/mjIHnmoTniYjmnKzluLjph49cbmV4cG9ydCBjb25zdCBTVVBQT1JURURfVkVSU0lPTlMgPSBbJzEuMC4wJywgJzEuMS4wJywgJzIuMC4wJ10gYXMgY29uc3RcblxuLy8g5LuO5bi46YeP5o6o5a+85Ye654mI5pys57G75Z6LXG5leHBvcnQgdHlwZSBNb3JwaGVtZVZlcnNpb24gPSB0eXBlb2YgU1VQUE9SVEVEX1ZFUlNJT05TW251bWJlcl1cblxuXG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBLE9BQU8sTUFBTUEsa0JBQWtCO0FBQUE7QUFBQSxDQUFBQyxhQUFBLEdBQUFDLENBQUEsT0FBRyxDQUFDLE9BQU8sRUFBRSxPQUFPLEVBQUUsT0FBTyxDQUFVIiwiaWdub3JlTGlzdCI6W119