{"version": 3, "names": ["SUPPORTED_VERSIONS", "cov_1ym1naerw", "s"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/types/common.ts"], "sourcesContent": ["// 支持的版本常量\nexport const SUPPORTED_VERSIONS = ['1.0.0', '1.1.0', '2.0.0'] as const\n\n// 从常量推导出版本类型\nexport type MorphemeVersion = typeof SUPPORTED_VERSIONS[number]\n\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,OAAO,MAAMA,kBAAkB;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAU", "ignoreList": []}