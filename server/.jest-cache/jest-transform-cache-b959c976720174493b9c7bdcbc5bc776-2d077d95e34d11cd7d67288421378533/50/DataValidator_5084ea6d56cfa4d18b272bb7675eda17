f53756e3cf7c83eaf130d54c0d4ca3cb
/* istanbul ignore next */
function cov_bk4eiopux() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts";
  var hash = "b9cf5f417b8b0ade053cb5971795a19007a1e6f1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 25
        },
        end: {
          line: 35,
          column: 1
        }
      },
      "1": {
        start: {
          line: 37,
          column: 25
        },
        end: {
          line: 44,
          column: 2
        }
      },
      "2": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 50,
          column: 2
        }
      },
      "3": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 73,
          column: 10
        }
      },
      "4": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 74,
          column: 57
        }
      },
      "5": {
        start: {
          line: 83,
          column: 26
        },
        end: {
          line: 83,
          column: 36
        }
      },
      "6": {
        start: {
          line: 84,
          column: 23
        },
        end: {
          line: 84,
          column: 25
        }
      },
      "7": {
        start: {
          line: 85,
          column: 25
        },
        end: {
          line: 85,
          column: 27
        }
      },
      "8": {
        start: {
          line: 86,
          column: 26
        },
        end: {
          line: 86,
          column: 27
        }
      },
      "9": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 87,
          column: 58
        }
      },
      "10": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 132,
          column: 9
        }
      },
      "11": {
        start: {
          line: 88,
          column: 21
        },
        end: {
          line: 88,
          column: 22
        }
      },
      "12": {
        start: {
          line: 89,
          column: 29
        },
        end: {
          line: 89,
          column: 41
        }
      },
      "13": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 131,
          column: 13
        }
      },
      "14": {
        start: {
          line: 92,
          column: 36
        },
        end: {
          line: 92,
          column: 77
        }
      },
      "15": {
        start: {
          line: 94,
          column: 35
        },
        end: {
          line: 94,
          column: 71
        }
      },
      "16": {
        start: {
          line: 96,
          column: 36
        },
        end: {
          line: 96,
          column: 76
        }
      },
      "17": {
        start: {
          line: 98,
          column: 42
        },
        end: {
          line: 98,
          column: 80
        }
      },
      "18": {
        start: {
          line: 100,
          column: 37
        },
        end: {
          line: 100,
          column: 75
        }
      },
      "19": {
        start: {
          line: 102,
          column: 34
        },
        end: {
          line: 108,
          column: 17
        }
      },
      "20": {
        start: {
          line: 110,
          column: 39
        },
        end: {
          line: 110,
          column: 80
        }
      },
      "21": {
        start: {
          line: 110,
          column: 61
        },
        end: {
          line: 110,
          column: 79
        }
      },
      "22": {
        start: {
          line: 111,
          column: 41
        },
        end: {
          line: 111,
          column: 84
        }
      },
      "23": {
        start: {
          line: 111,
          column: 63
        },
        end: {
          line: 111,
          column: 83
        }
      },
      "24": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 47
        }
      },
      "25": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 51
        }
      },
      "26": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 117,
          column: 17
        }
      },
      "27": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 116,
          column: 34
        }
      },
      "28": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 122,
          column: 17
        }
      },
      "29": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 81
        }
      },
      "30": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 121,
          column: 26
        }
      },
      "31": {
        start: {
          line: 125,
          column: 16
        },
        end: {
          line: 130,
          column: 19
        }
      },
      "32": {
        start: {
          line: 133,
          column: 31
        },
        end: {
          line: 133,
          column: 53
        }
      },
      "33": {
        start: {
          line: 134,
          column: 23
        },
        end: {
          line: 134,
          column: 42
        }
      },
      "34": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 141,
          column: 9
        }
      },
      "35": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 137,
          column: 98
        }
      },
      "36": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 140,
          column: 147
        }
      },
      "37": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 151,
          column: 10
        }
      },
      "38": {
        start: {
          line: 162,
          column: 23
        },
        end: {
          line: 162,
          column: 25
        }
      },
      "39": {
        start: {
          line: 163,
          column: 27
        },
        end: {
          line: 163,
          column: 58
        }
      },
      "40": {
        start: {
          line: 165,
          column: 31
        },
        end: {
          line: 169,
          column: 9
        }
      },
      "41": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 180,
          column: 9
        }
      },
      "42": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "43": {
        start: {
          line: 172,
          column: 16
        },
        end: {
          line: 178,
          column: 19
        }
      },
      "44": {
        start: {
          line: 182,
          column: 31
        },
        end: {
          line: 182,
          column: 84
        }
      },
      "45": {
        start: {
          line: 183,
          column: 8
        },
        end: {
          line: 193,
          column: 9
        }
      },
      "46": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 192,
          column: 13
        }
      },
      "47": {
        start: {
          line: 185,
          column: 16
        },
        end: {
          line: 191,
          column: 19
        }
      },
      "48": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 22
        }
      },
      "49": {
        start: {
          line: 205,
          column: 23
        },
        end: {
          line: 205,
          column: 25
        }
      },
      "50": {
        start: {
          line: 206,
          column: 27
        },
        end: {
          line: 206,
          column: 58
        }
      },
      "51": {
        start: {
          line: 208,
          column: 29
        },
        end: {
          line: 208,
          column: 79
        }
      },
      "52": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 222,
          column: 9
        }
      },
      "53": {
        start: {
          line: 210,
          column: 26
        },
        end: {
          line: 210,
          column: 41
        }
      },
      "54": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 221,
          column: 13
        }
      },
      "55": {
        start: {
          line: 212,
          column: 16
        },
        end: {
          line: 220,
          column: 19
        }
      },
      "56": {
        start: {
          line: 224,
          column: 29
        },
        end: {
          line: 224,
          column: 79
        }
      },
      "57": {
        start: {
          line: 225,
          column: 8
        },
        end: {
          line: 238,
          column: 9
        }
      },
      "58": {
        start: {
          line: 226,
          column: 26
        },
        end: {
          line: 226,
          column: 41
        }
      },
      "59": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 237,
          column: 13
        }
      },
      "60": {
        start: {
          line: 228,
          column: 16
        },
        end: {
          line: 236,
          column: 19
        }
      },
      "61": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "62": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 249,
          column: 15
        }
      },
      "63": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 261,
          column: 9
        }
      },
      "64": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 260,
          column: 15
        }
      },
      "65": {
        start: {
          line: 262,
          column: 8
        },
        end: {
          line: 262,
          column: 22
        }
      },
      "66": {
        start: {
          line: 273,
          column: 23
        },
        end: {
          line: 273,
          column: 25
        }
      },
      "67": {
        start: {
          line: 274,
          column: 27
        },
        end: {
          line: 274,
          column: 58
        }
      },
      "68": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "69": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 285,
          column: 15
        }
      },
      "70": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 301,
          column: 9
        }
      },
      "71": {
        start: {
          line: 289,
          column: 31
        },
        end: {
          line: 289,
          column: 51
        }
      },
      "72": {
        start: {
          line: 290,
          column: 12
        },
        end: {
          line: 300,
          column: 13
        }
      },
      "73": {
        start: {
          line: 291,
          column: 16
        },
        end: {
          line: 299,
          column: 19
        }
      },
      "74": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 313,
          column: 9
        }
      },
      "75": {
        start: {
          line: 304,
          column: 12
        },
        end: {
          line: 312,
          column: 15
        }
      },
      "76": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 325,
          column: 9
        }
      },
      "77": {
        start: {
          line: 316,
          column: 12
        },
        end: {
          line: 324,
          column: 15
        }
      },
      "78": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 340,
          column: 9
        }
      },
      "79": {
        start: {
          line: 328,
          column: 12
        },
        end: {
          line: 339,
          column: 13
        }
      },
      "80": {
        start: {
          line: 330,
          column: 16
        },
        end: {
          line: 338,
          column: 19
        }
      },
      "81": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 355,
          column: 9
        }
      },
      "82": {
        start: {
          line: 343,
          column: 12
        },
        end: {
          line: 354,
          column: 13
        }
      },
      "83": {
        start: {
          line: 345,
          column: 16
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "84": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 383,
          column: 9
        }
      },
      "85": {
        start: {
          line: 358,
          column: 12
        },
        end: {
          line: 368,
          column: 13
        }
      },
      "86": {
        start: {
          line: 359,
          column: 16
        },
        end: {
          line: 367,
          column: 19
        }
      },
      "87": {
        start: {
          line: 370,
          column: 12
        },
        end: {
          line: 382,
          column: 13
        }
      },
      "88": {
        start: {
          line: 370,
          column: 25
        },
        end: {
          line: 370,
          column: 26
        }
      },
      "89": {
        start: {
          line: 371,
          column: 16
        },
        end: {
          line: 381,
          column: 17
        }
      },
      "90": {
        start: {
          line: 372,
          column: 20
        },
        end: {
          line: 380,
          column: 23
        }
      },
      "91": {
        start: {
          line: 384,
          column: 8
        },
        end: {
          line: 384,
          column: 22
        }
      },
      "92": {
        start: {
          line: 395,
          column: 23
        },
        end: {
          line: 395,
          column: 25
        }
      },
      "93": {
        start: {
          line: 396,
          column: 27
        },
        end: {
          line: 396,
          column: 58
        }
      },
      "94": {
        start: {
          line: 398,
          column: 8
        },
        end: {
          line: 411,
          column: 9
        }
      },
      "95": {
        start: {
          line: 399,
          column: 35
        },
        end: {
          line: 399,
          column: 52
        }
      },
      "96": {
        start: {
          line: 400,
          column: 12
        },
        end: {
          line: 410,
          column: 13
        }
      },
      "97": {
        start: {
          line: 401,
          column: 16
        },
        end: {
          line: 409,
          column: 19
        }
      },
      "98": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 427,
          column: 9
        }
      },
      "99": {
        start: {
          line: 414,
          column: 36
        },
        end: {
          line: 414,
          column: 56
        }
      },
      "100": {
        start: {
          line: 415,
          column: 38
        },
        end: {
          line: 415,
          column: 82
        }
      },
      "101": {
        start: {
          line: 416,
          column: 12
        },
        end: {
          line: 426,
          column: 13
        }
      },
      "102": {
        start: {
          line: 417,
          column: 16
        },
        end: {
          line: 425,
          column: 19
        }
      },
      "103": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 428,
          column: 22
        }
      },
      "104": {
        start: {
          line: 439,
          column: 23
        },
        end: {
          line: 439,
          column: 25
        }
      },
      "105": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 455,
          column: 9
        }
      },
      "106": {
        start: {
          line: 441,
          column: 12
        },
        end: {
          line: 442,
          column: 25
        }
      },
      "107": {
        start: {
          line: 442,
          column: 16
        },
        end: {
          line: 442,
          column: 25
        }
      },
      "108": {
        start: {
          line: 443,
          column: 12
        },
        end: {
          line: 454,
          column: 13
        }
      },
      "109": {
        start: {
          line: 444,
          column: 35
        },
        end: {
          line: 444,
          column: 59
        }
      },
      "110": {
        start: {
          line: 445,
          column: 16
        },
        end: {
          line: 445,
          column: 43
        }
      },
      "111": {
        start: {
          line: 448,
          column: 16
        },
        end: {
          line: 453,
          column: 19
        }
      },
      "112": {
        start: {
          line: 456,
          column: 8
        },
        end: {
          line: 456,
          column: 22
        }
      },
      "113": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 464,
          column: 36
        }
      },
      "114": {
        start: {
          line: 465,
          column: 8
        },
        end: {
          line: 465,
          column: 50
        }
      },
      "115": {
        start: {
          line: 473,
          column: 22
        },
        end: {
          line: 473,
          column: 80
        }
      },
      "116": {
        start: {
          line: 473,
          column: 57
        },
        end: {
          line: 473,
          column: 79
        }
      },
      "117": {
        start: {
          line: 474,
          column: 8
        },
        end: {
          line: 477,
          column: 9
        }
      },
      "118": {
        start: {
          line: 475,
          column: 12
        },
        end: {
          line: 475,
          column: 46
        }
      },
      "119": {
        start: {
          line: 476,
          column: 12
        },
        end: {
          line: 476,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        loc: {
          start: {
            line: 67,
            column: 29
          },
          end: {
            line: 75,
            column: 5
          }
        },
        line: 67
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        loc: {
          start: {
            line: 82,
            column: 30
          },
          end: {
            line: 152,
            column: 5
          }
        },
        line: 82
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 110,
            column: 56
          },
          end: {
            line: 110,
            column: 57
          }
        },
        loc: {
          start: {
            line: 110,
            column: 61
          },
          end: {
            line: 110,
            column: 79
          }
        },
        line: 110
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 111,
            column: 58
          },
          end: {
            line: 111,
            column: 59
          }
        },
        loc: {
          start: {
            line: 111,
            column: 63
          },
          end: {
            line: 111,
            column: 83
          }
        },
        line: 111
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 161,
            column: 5
          }
        },
        loc: {
          start: {
            line: 161,
            column: 45
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 161
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 204,
            column: 5
          }
        },
        loc: {
          start: {
            line: 204,
            column: 40
          },
          end: {
            line: 263,
            column: 5
          }
        },
        line: 204
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 272,
            column: 4
          },
          end: {
            line: 272,
            column: 5
          }
        },
        loc: {
          start: {
            line: 272,
            column: 44
          },
          end: {
            line: 385,
            column: 5
          }
        },
        line: 272
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 394,
            column: 4
          },
          end: {
            line: 394,
            column: 5
          }
        },
        loc: {
          start: {
            line: 394,
            column: 42
          },
          end: {
            line: 429,
            column: 5
          }
        },
        line: 394
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 438,
            column: 4
          },
          end: {
            line: 438,
            column: 5
          }
        },
        loc: {
          start: {
            line: 438,
            column: 42
          },
          end: {
            line: 457,
            column: 5
          }
        },
        line: 438
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 5
          }
        },
        loc: {
          start: {
            line: 463,
            column: 24
          },
          end: {
            line: 466,
            column: 5
          }
        },
        line: 463
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 472,
            column: 4
          },
          end: {
            line: 472,
            column: 5
          }
        },
        loc: {
          start: {
            line: 472,
            column: 31
          },
          end: {
            line: 478,
            column: 5
          }
        },
        line: 472
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 473,
            column: 49
          },
          end: {
            line: 473,
            column: 50
          }
        },
        loc: {
          start: {
            line: 473,
            column: 57
          },
          end: {
            line: 473,
            column: 79
          }
        },
        line: 473
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 67,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 27
          }
        }],
        line: 67
      },
      "1": {
        loc: {
          start: {
            line: 69,
            column: 25
          },
          end: {
            line: 69,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 25
          },
          end: {
            line: 69,
            column: 43
          }
        }, {
          start: {
            line: 69,
            column: 47
          },
          end: {
            line: 69,
            column: 51
          }
        }],
        line: 69
      },
      "2": {
        loc: {
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 70,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 70,
            column: 47
          }
        }, {
          start: {
            line: 70,
            column: 51
          },
          end: {
            line: 70,
            column: 56
          }
        }],
        line: 70
      },
      "3": {
        loc: {
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 45
          }
        }, {
          start: {
            line: 71,
            column: 49
          },
          end: {
            line: 71,
            column: 51
          }
        }],
        line: 71
      },
      "4": {
        loc: {
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 41
          }
        }, {
          start: {
            line: 72,
            column: 45
          },
          end: {
            line: 72,
            column: 49
          }
        }],
        line: 72
      },
      "5": {
        loc: {
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 117,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 117,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "6": {
        loc: {
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "7": {
        loc: {
          start: {
            line: 128,
            column: 43
          },
          end: {
            line: 128,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 128,
            column: 68
          },
          end: {
            line: 128,
            column: 81
          }
        }, {
          start: {
            line: 128,
            column: 84
          },
          end: {
            line: 128,
            column: 97
          }
        }],
        line: 128
      },
      "8": {
        loc: {
          start: {
            line: 129,
            column: 33
          },
          end: {
            line: 129,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 129,
            column: 33
          },
          end: {
            line: 129,
            column: 44
          }
        }, {
          start: {
            line: 129,
            column: 48
          },
          end: {
            line: 129,
            column: 60
          }
        }],
        line: 129
      },
      "9": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        }, {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 141,
            column: 9
          }
        }],
        line: 136
      },
      "10": {
        loc: {
          start: {
            line: 163,
            column: 27
          },
          end: {
            line: 163,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 27
          },
          end: {
            line: 163,
            column: 38
          }
        }, {
          start: {
            line: 163,
            column: 42
          },
          end: {
            line: 163,
            column: 58
          }
        }],
        line: 163
      },
      "11": {
        loc: {
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "12": {
        loc: {
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 171,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 171,
            column: 36
          }
        }, {
          start: {
            line: 171,
            column: 40
          },
          end: {
            line: 171,
            column: 69
          }
        }, {
          start: {
            line: 171,
            column: 73
          },
          end: {
            line: 171,
            column: 97
          }
        }],
        line: 171
      },
      "13": {
        loc: {
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "14": {
        loc: {
          start: {
            line: 184,
            column: 16
          },
          end: {
            line: 184,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 16
          },
          end: {
            line: 184,
            column: 33
          }
        }, {
          start: {
            line: 184,
            column: 37
          },
          end: {
            line: 184,
            column: 66
          }
        }],
        line: 184
      },
      "15": {
        loc: {
          start: {
            line: 206,
            column: 27
          },
          end: {
            line: 206,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 206,
            column: 27
          },
          end: {
            line: 206,
            column: 38
          }
        }, {
          start: {
            line: 206,
            column: 42
          },
          end: {
            line: 206,
            column: 58
          }
        }],
        line: 206
      },
      "16": {
        loc: {
          start: {
            line: 211,
            column: 12
          },
          end: {
            line: 221,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 12
          },
          end: {
            line: 221,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "17": {
        loc: {
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 211,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 211,
            column: 35
          }
        }, {
          start: {
            line: 211,
            column: 39
          },
          end: {
            line: 211,
            column: 64
          }
        }],
        line: 211
      },
      "18": {
        loc: {
          start: {
            line: 227,
            column: 12
          },
          end: {
            line: 237,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 12
          },
          end: {
            line: 237,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "19": {
        loc: {
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 227,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 227,
            column: 16
          },
          end: {
            line: 227,
            column: 35
          }
        }, {
          start: {
            line: 227,
            column: 39
          },
          end: {
            line: 227,
            column: 64
          }
        }],
        line: 227
      },
      "20": {
        loc: {
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "21": {
        loc: {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 240,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 240,
            column: 50
          }
        }, {
          start: {
            line: 240,
            column: 54
          },
          end: {
            line: 240,
            column: 94
          }
        }],
        line: 240
      },
      "22": {
        loc: {
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 261,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 261,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "23": {
        loc: {
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 251,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 12
          },
          end: {
            line: 251,
            column: 39
          }
        }, {
          start: {
            line: 251,
            column: 43
          },
          end: {
            line: 251,
            column: 72
          }
        }],
        line: 251
      },
      "24": {
        loc: {
          start: {
            line: 274,
            column: 27
          },
          end: {
            line: 274,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 274,
            column: 27
          },
          end: {
            line: 274,
            column: 38
          }
        }, {
          start: {
            line: 274,
            column: 42
          },
          end: {
            line: 274,
            column: 58
          }
        }],
        line: 274
      },
      "25": {
        loc: {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "26": {
        loc: {
          start: {
            line: 276,
            column: 12
          },
          end: {
            line: 276,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 12
          },
          end: {
            line: 276,
            column: 23
          }
        }, {
          start: {
            line: 276,
            column: 27
          },
          end: {
            line: 276,
            column: 73
          }
        }],
        line: 276
      },
      "27": {
        loc: {
          start: {
            line: 288,
            column: 8
          },
          end: {
            line: 301,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 288,
            column: 8
          },
          end: {
            line: 301,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 288
      },
      "28": {
        loc: {
          start: {
            line: 290,
            column: 12
          },
          end: {
            line: 300,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 12
          },
          end: {
            line: 300,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "29": {
        loc: {
          start: {
            line: 290,
            column: 16
          },
          end: {
            line: 290,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 16
          },
          end: {
            line: 290,
            column: 61
          }
        }, {
          start: {
            line: 290,
            column: 65
          },
          end: {
            line: 290,
            column: 110
          }
        }],
        line: 290
      },
      "30": {
        loc: {
          start: {
            line: 303,
            column: 8
          },
          end: {
            line: 313,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 303,
            column: 8
          },
          end: {
            line: 313,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 303
      },
      "31": {
        loc: {
          start: {
            line: 303,
            column: 12
          },
          end: {
            line: 303,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 303,
            column: 12
          },
          end: {
            line: 303,
            column: 29
          }
        }, {
          start: {
            line: 303,
            column: 33
          },
          end: {
            line: 303,
            column: 78
          }
        }],
        line: 303
      },
      "32": {
        loc: {
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 325,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 325,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "33": {
        loc: {
          start: {
            line: 315,
            column: 12
          },
          end: {
            line: 315,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 315,
            column: 12
          },
          end: {
            line: 315,
            column: 37
          }
        }, {
          start: {
            line: 315,
            column: 41
          },
          end: {
            line: 315,
            column: 92
          }
        }],
        line: 315
      },
      "34": {
        loc: {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "35": {
        loc: {
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 339,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 12
          },
          end: {
            line: 339,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "36": {
        loc: {
          start: {
            line: 328,
            column: 16
          },
          end: {
            line: 329,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 328,
            column: 16
          },
          end: {
            line: 328,
            column: 75
          }
        }, {
          start: {
            line: 329,
            column: 16
          },
          end: {
            line: 329,
            column: 75
          }
        }],
        line: 328
      },
      "37": {
        loc: {
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 355,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 8
          },
          end: {
            line: 355,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "38": {
        loc: {
          start: {
            line: 343,
            column: 12
          },
          end: {
            line: 354,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 343,
            column: 12
          },
          end: {
            line: 354,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 343
      },
      "39": {
        loc: {
          start: {
            line: 343,
            column: 16
          },
          end: {
            line: 344,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 343,
            column: 16
          },
          end: {
            line: 343,
            column: 79
          }
        }, {
          start: {
            line: 344,
            column: 16
          },
          end: {
            line: 344,
            column: 79
          }
        }],
        line: 343
      },
      "40": {
        loc: {
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 383,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 8
          },
          end: {
            line: 383,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "41": {
        loc: {
          start: {
            line: 358,
            column: 12
          },
          end: {
            line: 368,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 12
          },
          end: {
            line: 368,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "42": {
        loc: {
          start: {
            line: 371,
            column: 16
          },
          end: {
            line: 381,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 371,
            column: 16
          },
          end: {
            line: 381,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 371
      },
      "43": {
        loc: {
          start: {
            line: 396,
            column: 27
          },
          end: {
            line: 396,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 396,
            column: 27
          },
          end: {
            line: 396,
            column: 38
          }
        }, {
          start: {
            line: 396,
            column: 42
          },
          end: {
            line: 396,
            column: 58
          }
        }],
        line: 396
      },
      "44": {
        loc: {
          start: {
            line: 398,
            column: 8
          },
          end: {
            line: 411,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 398,
            column: 8
          },
          end: {
            line: 411,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 398
      },
      "45": {
        loc: {
          start: {
            line: 398,
            column: 12
          },
          end: {
            line: 398,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 398,
            column: 12
          },
          end: {
            line: 398,
            column: 23
          }
        }, {
          start: {
            line: 398,
            column: 27
          },
          end: {
            line: 398,
            column: 44
          }
        }],
        line: 398
      },
      "46": {
        loc: {
          start: {
            line: 400,
            column: 12
          },
          end: {
            line: 410,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 400,
            column: 12
          },
          end: {
            line: 410,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 400
      },
      "47": {
        loc: {
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 427,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 427,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "48": {
        loc: {
          start: {
            line: 413,
            column: 12
          },
          end: {
            line: 413,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 413,
            column: 12
          },
          end: {
            line: 413,
            column: 40
          }
        }, {
          start: {
            line: 413,
            column: 44
          },
          end: {
            line: 413,
            column: 57
          }
        }],
        line: 413
      },
      "49": {
        loc: {
          start: {
            line: 416,
            column: 12
          },
          end: {
            line: 426,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 416,
            column: 12
          },
          end: {
            line: 426,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 416
      },
      "50": {
        loc: {
          start: {
            line: 441,
            column: 12
          },
          end: {
            line: 442,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 441,
            column: 12
          },
          end: {
            line: 442,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 441
      },
      "51": {
        loc: {
          start: {
            line: 451,
            column: 59
          },
          end: {
            line: 451,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 451,
            column: 84
          },
          end: {
            line: 451,
            column: 97
          }
        }, {
          start: {
            line: 451,
            column: 100
          },
          end: {
            line: 451,
            column: 113
          }
        }],
        line: 451
      },
      "52": {
        loc: {
          start: {
            line: 452,
            column: 33
          },
          end: {
            line: 452,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 452,
            column: 33
          },
          end: {
            line: 452,
            column: 44
          }
        }, {
          start: {
            line: 452,
            column: 48
          },
          end: {
            line: 452,
            column: 64
          }
        }],
        line: 452
      },
      "53": {
        loc: {
          start: {
            line: 474,
            column: 8
          },
          end: {
            line: 477,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 474,
            column: 8
          },
          end: {
            line: 477,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 474
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts",
      mappings: "AAAA;;;;;;;;;;GAUG;AAIH,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,aAAa;AACb,MAAM,gBAAgB,GAAG;IACvB,gBAAgB;IAChB,UAAU,EAAE,iBAAiB;IAC7B,aAAa;IACb,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;IAChC,cAAc;IACd,mBAAmB,EAAE,WAAW;IAChC,aAAa;IACb,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;IAC/B,aAAa;IACb,yBAAyB,EAAE,EAAE;IAC7B,aAAa;IACb,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IACrC,aAAa;IACb,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IACvC,aAAa;IACb,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IAClC,aAAa;IACb,eAAe,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;CAC5B,CAAA;AAEV,cAAc;AACd,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE,UAAU;IACpB,WAAW,EAAE,aAAa;IAC1B,eAAe,EAAE,iBAAiB;IAClC,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;CACrB,CAAC,CAAA;AAEF,cAAc;AACd,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;IACnC,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;CACnB,CAAC,CAAA;AA8EF,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,aAAa;IAChB,MAAM,CAA4B;IAClC,WAAW,CAAkB;IAErC;;;;OAIG;IACH,YAAY,SAA2B,EAAE;QACvC,IAAI,CAAC,MAAM,GAAG;YACZ,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI;YACvC,aAAa,EAAE,MAAM,CAAC,aAAa,IAAI,KAAK;YAC5C,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;YACvC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI;SACtC,CAAA;QAED,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;IAClD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,CAAC,SAAqB;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,QAAQ,GAAsB,EAAE,CAAA;QACtC,IAAI,WAAW,GAAG,CAAC,CAAA;QAEnB,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,CAAC,MAAM,SAAS,CAAC,CAAA;QAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;YAE7B,IAAI,CAAC;gBACH,OAAO;gBACP,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAE7D,SAAS;gBACT,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAEvD,SAAS;gBACT,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAE5D,QAAQ;gBACR,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAEhE,UAAU;gBACV,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAE3D,SAAS;gBACT,MAAM,SAAS,GAAG;oBAChB,GAAG,WAAW;oBACd,GAAG,UAAU;oBACb,GAAG,WAAW;oBACd,GAAG,iBAAiB;oBACpB,GAAG,YAAY;iBAChB,CAAA;gBAED,UAAU;gBACV,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAA;gBAChE,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;gBAEpE,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;gBAC9B,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAA;gBAElC,eAAe;gBACf,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,WAAW,EAAE,CAAA;gBACf,CAAC;gBAED,eAAe;gBACf,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBAC5C,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,UAAU,QAAQ,CAAC,CAAA;oBAC5D,MAAK;gBACP,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,sBAAsB;oBAC5B,OAAO,EAAE,cAAc,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC/E,WAAW,EAAE,QAAQ,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;iBACzC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;QAElC,WAAW;QACX,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,WAAW,WAAW,IAAI,SAAS,CAAC,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA;QACvF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,WAAW,WAAW,IAAI,SAAS,CAAC,MAAM,WAAW,MAAM,CAAC,MAAM,SAAS,QAAQ,CAAC,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA;QACxI,CAAC;QAED,OAAO;YACL,MAAM;YACN,WAAW,EAAE,SAAS,CAAC,MAAM;YAC7B,YAAY,EAAE,WAAW;YACzB,YAAY,EAAE,SAAS,CAAC,MAAM,GAAG,WAAW;YAC5C,MAAM;YACN,QAAQ;YACR,eAAe,EAAE,cAAc;YAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;IACH,CAAC;IAED;;;;;;;OAOG;IACK,uBAAuB,CAAC,QAAkB,EAAE,KAAa;QAC/D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,kBAAkB;QAClB,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB;YAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM;YAC7D,YAAY,EAAE,QAAQ;SACvB,CAAA;QAED,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC1H,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,wBAAwB;oBAC9B,OAAO,EAAE,WAAW,KAAK,EAAE;oBAC3B,KAAK;oBACL,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,MAAM,cAAc,GAAG,CAAC,qBAAqB,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAA;QAC5E,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,KAAK,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,SAAS,EAAE,CAAC;gBACzE,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,wBAAwB;oBAC9B,OAAO,EAAE,QAAQ,KAAK,iBAAiB;oBACvC,KAAK;oBACL,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,kBAAkB,CAAC,QAAkB,EAAE,KAAa;QAC1D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,UAAU;QACV,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;QACvE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAuB,CAAC,CAAA;YAC/C,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,MAAM,KAAK,WAAW;oBAC/B,KAAK;oBACL,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,OAAO,KAAK;oBACpB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,SAAS;QACT,MAAM,YAAY,GAAG,CAAC,iBAAiB,EAAE,eAAe,EAAE,YAAY,CAAC,CAAA;QACvE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAuB,CAAC,CAAA;YAC/C,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,MAAM,KAAK,UAAU;oBAC9B,KAAK;oBACL,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,OAAO,KAAK;oBACpB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACvF,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,yBAAyB;gBAClC,KAAK,EAAE,iBAAiB;gBACxB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE,OAAO,QAAQ,CAAC,eAAe;gBACvC,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,cAAc;gBACvB,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE,OAAO,QAAQ,CAAC,IAAI;gBAC5B,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,sBAAsB,CAAC,QAAkB,EAAE,KAAa;QAC9D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,SAAS;QACT,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,iCAAiC;gBAC1C,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,gBAAgB,CAAC,UAAU,CAAC,QAAQ,EAAE;gBAChD,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAA;YACvC,IAAI,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;gBACnG,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,qBAAqB;oBAC3B,OAAO,EAAE,WAAW,gBAAgB,CAAC,WAAW,CAAC,GAAG,IAAI,gBAAgB,CAAC,WAAW,CAAC,GAAG,KAAK;oBAC7F,KAAK,EAAE,MAAM;oBACb,QAAQ,EAAE,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,IAAI,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE;oBACnF,MAAM,EAAE,UAAU;oBAClB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO;QACP,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAe,CAAC,EAAE,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE,gBAAgB;gBAC1B,MAAM,EAAE,QAAQ,CAAC,QAAQ;gBACzB,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,gBAAgB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAuB,CAAC,EAAE,CAAC;YAC5F,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,0BAA0B;gBAChC,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,QAAQ,CAAC,gBAAgB;gBACjC,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,WAAW;QACX,IAAI,OAAO,QAAQ,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YAC/C,IAAI,QAAQ,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG;gBAC3D,QAAQ,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;gBAChE,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,uBAAuB;oBAC7B,OAAO,EAAE,WAAW,gBAAgB,CAAC,aAAa,CAAC,GAAG,IAAI,gBAAgB,CAAC,aAAa,CAAC,GAAG,KAAK;oBACjG,KAAK,EAAE,eAAe;oBACtB,QAAQ,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,IAAI,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAE;oBACvF,MAAM,EAAE,QAAQ,CAAC,aAAa;oBAC9B,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,WAAW;QACX,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;YACjD,IAAI,QAAQ,CAAC,eAAe,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG;gBAC/D,QAAQ,CAAC,eAAe,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;gBACpE,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,yBAAyB;oBAC/B,OAAO,EAAE,WAAW,gBAAgB,CAAC,eAAe,CAAC,GAAG,IAAI,gBAAgB,CAAC,eAAe,CAAC,GAAG,KAAK;oBACrG,KAAK,EAAE,iBAAiB;oBACxB,QAAQ,EAAE,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,IAAI,gBAAgB,CAAC,eAAe,CAAC,GAAG,EAAE;oBAC3F,MAAM,EAAE,QAAQ,CAAC,eAAe;oBAChC,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5C,IAAI,QAAQ,CAAC,eAAe,CAAC,MAAM,KAAK,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;gBACnF,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mCAAmC;oBACzC,OAAO,EAAE,aAAa,gBAAgB,CAAC,yBAAyB,EAAE;oBAClE,KAAK,EAAE,iBAAiB;oBACxB,QAAQ,EAAE,gBAAgB,CAAC,yBAAyB;oBACpD,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC,MAAM;oBACvC,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;YAED,cAAc;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzD,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACpD,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,iCAAiC;wBACvC,OAAO,EAAE,eAAe;wBACxB,KAAK,EAAE,mBAAmB,CAAC,GAAG;wBAC9B,QAAQ,EAAE,QAAQ;wBAClB,MAAM,EAAE,OAAO,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;wBAC1C,WAAW,EAAE,UAAU;qBACxB,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,oBAAoB,CAAC,QAAkB,EAAE,KAAa;QAC5D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,cAAc;QACd,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAA;YACxC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,YAAY;oBACrB,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,GAAG,cAAc,MAAM;oBACjC,MAAM,EAAE,QAAQ,CAAC,EAAE;oBACnB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,YAAY;QACZ,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAA;YAC5C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,mBAAmB,CAAC,eAAe,CAAA;YAEtE,IAAI,iBAAiB,KAAK,eAAe,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,8BAA8B;oBACpC,OAAO,EAAE,aAAa;oBACtB,KAAK,EAAE,qCAAqC;oBAC5C,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,iBAAiB;oBACzB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,oBAAoB,CAAC,QAAkB,EAAE,KAAa;QAC5D,MAAM,MAAM,GAAsB,EAAE,CAAA;QAEpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,OAAO;gBAAE,SAAQ;YAE3B,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBAC3C,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAA;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,UAAU,IAAI,CAAC,IAAI,WAAW,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC/F,WAAW,EAAE,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE;iBAC7C,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,IAAoB;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3B,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IAC3C,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,QAAgB;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QACxE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YACjC,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAA;QAC3C,CAAC;IACH,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts"],
      sourcesContent: ["/**\n * \u6570\u636E\u9A8C\u8BC1\u5668\n * \n * \u63D0\u4F9B\u8BED\u7D20\u6570\u636E\u7684\u5B8C\u6574\u6027\u9A8C\u8BC1\u3001\u8D28\u91CF\u68C0\u67E5\u548C\u4E00\u81F4\u6027\u6821\u9A8C\u529F\u80FD\u3002\n * \u652F\u6301\u591A\u5C42\u6B21\u9A8C\u8BC1\u89C4\u5219\u548C\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u7B56\u7565\u3002\n * \n * @fileoverview \u6570\u636E\u9A8C\u8BC1\u6838\u5FC3\u6A21\u5757\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\n\n// ============================================================================\n// \u9A8C\u8BC1\u89C4\u5219\u914D\u7F6E\n// ============================================================================\n\n/** \u9A8C\u8BC1\u89C4\u5219\u914D\u7F6E */\nconst VALIDATION_RULES = {\n  /** ID\u683C\u5F0F\u6B63\u5219\u8868\u8FBE\u5F0F */\n  id_pattern: /^[a-z_]+_\\d{3}$/,\n  /** \u6587\u672C\u957F\u5EA6\u9650\u5236 */\n  text_length: { min: 1, max: 10 },\n  /** \u5B50\u5206\u7C7B\u547D\u540D\u89C4\u5219 */\n  subcategory_pattern: /^[a-z_]+$/,\n  /** \u6807\u7B7E\u6570\u91CF\u9650\u5236 */\n  tags_count: { min: 1, max: 10 },\n  /** \u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 */\n  semantic_vector_dimension: 20,\n  /** \u8D28\u91CF\u8BC4\u5206\u8303\u56F4 */\n  quality_range: { min: 0.0, max: 1.0 },\n  /** \u4F7F\u7528\u9891\u7387\u8303\u56F4 */\n  frequency_range: { min: 0.0, max: 1.0 },\n  /** \u97F3\u8282\u6570\u91CF\u9650\u5236 */\n  syllable_count: { min: 1, max: 5 },\n  /** \u5B57\u7B26\u6570\u91CF\u9650\u5236 */\n  character_count: { min: 1, max: 10 }\n} as const\n\n/** \u6709\u6548\u7684\u8BED\u7D20\u7C7B\u522B */\nconst VALID_CATEGORIES = Object.values({\n  EMOTIONS: 'emotions',\n  PROFESSIONS: 'professions',\n  CHARACTERISTICS: 'characteristics',\n  OBJECTS: 'objects',\n  ACTIONS: 'actions',\n  CONCEPTS: 'concepts'\n})\n\n/** \u6709\u6548\u7684\u6587\u5316\u8BED\u5883 */\nconst VALID_CONTEXTS = Object.values({\n  ANCIENT: 'ancient',\n  MODERN: 'modern',\n  NEUTRAL: 'neutral'\n})\n\n// ============================================================================\n// \u63A5\u53E3\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u9A8C\u8BC1\u9519\u8BEF\u63A5\u53E3\n */\nexport interface ValidationError {\n  /** \u9519\u8BEF\u7C7B\u578B */\n  type: 'error' | 'warning' | 'info'\n  /** \u9519\u8BEF\u4EE3\u7801 */\n  code: string\n  /** \u9519\u8BEF\u6D88\u606F */\n  message: string\n  /** \u5B57\u6BB5\u8DEF\u5F84 */\n  field?: string\n  /** \u671F\u671B\u503C */\n  expected?: any\n  /** \u5B9E\u9645\u503C */\n  actual?: any\n  /** \u8BED\u7D20ID */\n  morpheme_id?: string\n}\n\n/**\n * \u9A8C\u8BC1\u7ED3\u679C\u63A5\u53E3\n */\nexport interface ValidationResult {\n  /** \u9A8C\u8BC1\u662F\u5426\u901A\u8FC7 */\n  passed: boolean\n  /** \u9A8C\u8BC1\u7684\u8BED\u7D20\u6570\u91CF */\n  total_count: number\n  /** \u901A\u8FC7\u9A8C\u8BC1\u7684\u8BED\u7D20\u6570\u91CF */\n  passed_count: number\n  /** \u5931\u8D25\u7684\u8BED\u7D20\u6570\u91CF */\n  failed_count: number\n  /** \u9519\u8BEF\u5217\u8868 */\n  errors: ValidationError[]\n  /** \u8B66\u544A\u5217\u8868 */\n  warnings: ValidationError[]\n  /** \u9A8C\u8BC1\u8017\u65F6 (\u6BEB\u79D2) */\n  validation_time: number\n  /** \u9A8C\u8BC1\u65F6\u95F4\u6233 */\n  timestamp: number\n}\n\n/**\n * \u9A8C\u8BC1\u914D\u7F6E\u63A5\u53E3\n */\nexport interface ValidationConfig {\n  /** \u662F\u5426\u542F\u7528\u4E25\u683C\u6A21\u5F0F */\n  strict_mode?: boolean\n  /** \u662F\u5426\u8DF3\u8FC7\u8B66\u544A */\n  skip_warnings?: boolean\n  /** \u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219 */\n  custom_rules?: ValidationRule[]\n  /** \u6700\u5927\u9519\u8BEF\u6570\u91CF (\u8D85\u8FC7\u5219\u505C\u6B62\u9A8C\u8BC1) */\n  max_errors?: number\n}\n\n/**\n * \u9A8C\u8BC1\u89C4\u5219\u63A5\u53E3\n */\nexport interface ValidationRule {\n  /** \u89C4\u5219\u540D\u79F0 */\n  name: string\n  /** \u89C4\u5219\u63CF\u8FF0 */\n  description: string\n  /** \u9A8C\u8BC1\u51FD\u6570 */\n  validator: (morpheme: Morpheme) => ValidationError[]\n  /** \u89C4\u5219\u4F18\u5148\u7EA7 */\n  priority: number\n  /** \u662F\u5426\u542F\u7528 */\n  enabled: boolean\n}\n\n// ============================================================================\n// \u6570\u636E\u9A8C\u8BC1\u5668\u7C7B\n// ============================================================================\n\n/**\n * \u6570\u636E\u9A8C\u8BC1\u5668\u7C7B\n * \n * \u63D0\u4F9B\u5168\u9762\u7684\u8BED\u7D20\u6570\u636E\u9A8C\u8BC1\u529F\u80FD\n */\nexport class DataValidator {\n  private config: Required<ValidationConfig>\n  private customRules: ValidationRule[]\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   * \n   * @param config \u9A8C\u8BC1\u914D\u7F6E\n   */\n  constructor(config: ValidationConfig = {}) {\n    this.config = {\n      strict_mode: config.strict_mode ?? true,\n      skip_warnings: config.skip_warnings ?? false,\n      custom_rules: config.custom_rules ?? [],\n      max_errors: config.max_errors ?? 1000\n    }\n    \n    this.customRules = [...this.config.custom_rules]\n  }\n\n  /**\n   * \u9A8C\u8BC1\u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * \n   * @param morphemes \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * @returns \u9A8C\u8BC1\u7ED3\u679C\n   */\n  async validate(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: ValidationError[] = []\n    const warnings: ValidationError[] = []\n    let passedCount = 0\n\n    console.log(`\uD83D\uDD0D \u5F00\u59CB\u9A8C\u8BC1 ${morphemes.length} \u4E2A\u8BED\u7D20...`)\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      \n      try {\n        // \u57FA\u7840\u9A8C\u8BC1\n        const basicErrors = this._validateBasicStructure(morpheme, i)\n        \n        // \u6570\u636E\u7C7B\u578B\u9A8C\u8BC1\n        const typeErrors = this._validateDataTypes(morpheme, i)\n        \n        // \u4E1A\u52A1\u903B\u8F91\u9A8C\u8BC1\n        const logicErrors = this._validateBusinessLogic(morpheme, i)\n        \n        // \u4E00\u81F4\u6027\u9A8C\u8BC1\n        const consistencyErrors = this._validateConsistency(morpheme, i)\n        \n        // \u81EA\u5B9A\u4E49\u89C4\u5219\u9A8C\u8BC1\n        const customErrors = this._validateCustomRules(morpheme, i)\n        \n        // \u6536\u96C6\u6240\u6709\u9519\u8BEF\n        const allErrors = [\n          ...basicErrors,\n          ...typeErrors,\n          ...logicErrors,\n          ...consistencyErrors,\n          ...customErrors\n        ]\n        \n        // \u5206\u7C7B\u9519\u8BEF\u548C\u8B66\u544A\n        const morphemeErrors = allErrors.filter(e => e.type === 'error')\n        const morphemeWarnings = allErrors.filter(e => e.type === 'warning')\n        \n        errors.push(...morphemeErrors)\n        warnings.push(...morphemeWarnings)\n        \n        // \u5982\u679C\u6CA1\u6709\u9519\u8BEF\uFF0C\u5219\u901A\u8FC7\u9A8C\u8BC1\n        if (morphemeErrors.length === 0) {\n          passedCount++\n        }\n        \n        // \u68C0\u67E5\u662F\u5426\u8D85\u8FC7\u6700\u5927\u9519\u8BEF\u6570\u91CF\n        if (errors.length >= this.config.max_errors) {\n          console.warn(`\u26A0\uFE0F \u9519\u8BEF\u6570\u91CF\u8D85\u8FC7\u9650\u5236 (${this.config.max_errors})\uFF0C\u505C\u6B62\u9A8C\u8BC1`)\n          break\n        }\n        \n      } catch (error) {\n        errors.push({\n          type: 'error',\n          code: 'VALIDATION_EXCEPTION',\n          message: `\u9A8C\u8BC1\u8FC7\u7A0B\u4E2D\u53D1\u751F\u5F02\u5E38: ${error instanceof Error ? error.message : String(error)}`,\n          morpheme_id: morpheme.id || `index_${i}`\n        })\n      }\n    }\n\n    const validationTime = Date.now() - startTime\n    const passed = errors.length === 0\n\n    // \u8F93\u51FA\u9A8C\u8BC1\u7ED3\u679C\u6458\u8981\n    if (passed) {\n      console.log(`\u2705 \u9A8C\u8BC1\u901A\u8FC7: ${passedCount}/${morphemes.length} \u4E2A\u8BED\u7D20, \u8017\u65F6 ${validationTime}ms`)\n    } else {\n      console.warn(`\u274C \u9A8C\u8BC1\u5931\u8D25: ${passedCount}/${morphemes.length} \u4E2A\u8BED\u7D20\u901A\u8FC7, ${errors.length} \u4E2A\u9519\u8BEF, ${warnings.length} \u4E2A\u8B66\u544A, \u8017\u65F6 ${validationTime}ms`)\n    }\n\n    return {\n      passed,\n      total_count: morphemes.length,\n      passed_count: passedCount,\n      failed_count: morphemes.length - passedCount,\n      errors,\n      warnings,\n      validation_time: validationTime,\n      timestamp: Date.now()\n    }\n  }\n\n  /**\n   * \u9A8C\u8BC1\u57FA\u7840\u7ED3\u6784\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateBasicStructure(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1\u5FC5\u9700\u5B57\u6BB5 (v1.0\u517C\u5BB9)\n    const requiredFields = [\n      'id', 'text', 'category', 'subcategory', 'cultural_context',\n      'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n      'created_at', 'source'\n    ]\n\n    for (const field of requiredFields) {\n      if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined || morpheme[field as keyof Morpheme] === null) {\n        errors.push({\n          type: 'error',\n          code: 'MISSING_REQUIRED_FIELD',\n          message: `\u7F3A\u5C11\u5FC5\u9700\u5B57\u6BB5: ${field}`,\n          field,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u53EF\u9009\u5B57\u6BB5 (v2.0\u65B0\u589E) - \u5982\u679C\u5B58\u5728\u5219\u9A8C\u8BC1\u683C\u5F0F\n    const optionalFields = ['language_properties', 'quality_metrics', 'version']\n    for (const field of optionalFields) {\n      if (field in morpheme && morpheme[field as keyof Morpheme] === undefined) {\n        errors.push({\n          type: 'warning',\n          code: 'INVALID_OPTIONAL_FIELD',\n          message: `\u53EF\u9009\u5B57\u6BB5 ${field} \u5B58\u5728\u4F46\u503C\u4E3Aundefined`,\n          field,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u6570\u636E\u7C7B\u578B\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateDataTypes(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1\u5B57\u7B26\u4E32\u5B57\u6BB5\n    const stringFields = ['id', 'text', 'subcategory', 'source', 'version']\n    for (const field of stringFields) {\n      const value = morpheme[field as keyof Morpheme]\n      if (value !== undefined && typeof value !== 'string') {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_DATA_TYPE',\n          message: `\u5B57\u6BB5 ${field} \u5FC5\u987B\u662F\u5B57\u7B26\u4E32\u7C7B\u578B`,\n          field,\n          expected: 'string',\n          actual: typeof value,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u6570\u5B57\u5B57\u6BB5\n    const numberFields = ['usage_frequency', 'quality_score', 'created_at']\n    for (const field of numberFields) {\n      const value = morpheme[field as keyof Morpheme]\n      if (value !== undefined && typeof value !== 'number') {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_DATA_TYPE',\n          message: `\u5B57\u6BB5 ${field} \u5FC5\u987B\u662F\u6570\u5B57\u7C7B\u578B`,\n          field,\n          expected: 'number',\n          actual: typeof value,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u6570\u7EC4\u5B57\u6BB5\n    if (morpheme.semantic_vector !== undefined && !Array.isArray(morpheme.semantic_vector)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_DATA_TYPE',\n        message: 'semantic_vector \u5FC5\u987B\u662F\u6570\u7EC4\u7C7B\u578B',\n        field: 'semantic_vector',\n        expected: 'array',\n        actual: typeof morpheme.semantic_vector,\n        morpheme_id: morphemeId\n      })\n    }\n\n    if (morpheme.tags !== undefined && !Array.isArray(morpheme.tags)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_DATA_TYPE',\n        message: 'tags \u5FC5\u987B\u662F\u6570\u7EC4\u7C7B\u578B',\n        field: 'tags',\n        expected: 'array',\n        actual: typeof morpheme.tags,\n        morpheme_id: morphemeId\n      })\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u4E1A\u52A1\u903B\u8F91\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateBusinessLogic(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1ID\u683C\u5F0F\n    if (morpheme.id && !VALIDATION_RULES.id_pattern.test(morpheme.id)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_ID_FORMAT',\n        message: `ID\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u5E94\u4E3A: {category}_{number}`,\n        field: 'id',\n        expected: VALIDATION_RULES.id_pattern.toString(),\n        actual: morpheme.id,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // \u9A8C\u8BC1\u6587\u672C\u957F\u5EA6\n    if (morpheme.text) {\n      const textLength = morpheme.text.length\n      if (textLength < VALIDATION_RULES.text_length.min || textLength > VALIDATION_RULES.text_length.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_TEXT_LENGTH',\n          message: `\u6587\u672C\u957F\u5EA6\u5FC5\u987B\u5728 ${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max} \u4E4B\u95F4`,\n          field: 'text',\n          expected: `${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max}`,\n          actual: textLength,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u7C7B\u522B\n    if (morpheme.category && !VALID_CATEGORIES.includes(morpheme.category as any)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_CATEGORY',\n        message: `\u65E0\u6548\u7684\u8BED\u7D20\u7C7B\u522B`,\n        field: 'category',\n        expected: VALID_CATEGORIES,\n        actual: morpheme.category,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // \u9A8C\u8BC1\u6587\u5316\u8BED\u5883\n    if (morpheme.cultural_context && !VALID_CONTEXTS.includes(morpheme.cultural_context as any)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_CULTURAL_CONTEXT',\n        message: `\u65E0\u6548\u7684\u6587\u5316\u8BED\u5883`,\n        field: 'cultural_context',\n        expected: VALID_CONTEXTS,\n        actual: morpheme.cultural_context,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // \u9A8C\u8BC1\u8D28\u91CF\u8BC4\u5206\u8303\u56F4\n    if (typeof morpheme.quality_score === 'number') {\n      if (morpheme.quality_score < VALIDATION_RULES.quality_range.min || \n          morpheme.quality_score > VALIDATION_RULES.quality_range.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_QUALITY_SCORE',\n          message: `\u8D28\u91CF\u8BC4\u5206\u5FC5\u987B\u5728 ${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max} \u4E4B\u95F4`,\n          field: 'quality_score',\n          expected: `${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max}`,\n          actual: morpheme.quality_score,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u4F7F\u7528\u9891\u7387\u8303\u56F4\n    if (typeof morpheme.usage_frequency === 'number') {\n      if (morpheme.usage_frequency < VALIDATION_RULES.frequency_range.min || \n          morpheme.usage_frequency > VALIDATION_RULES.frequency_range.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_USAGE_FREQUENCY',\n          message: `\u4F7F\u7528\u9891\u7387\u5FC5\u987B\u5728 ${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max} \u4E4B\u95F4`,\n          field: 'usage_frequency',\n          expected: `${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max}`,\n          actual: morpheme.usage_frequency,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u8BED\u4E49\u5411\u91CF\n    if (Array.isArray(morpheme.semantic_vector)) {\n      if (morpheme.semantic_vector.length !== VALIDATION_RULES.semantic_vector_dimension) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_SEMANTIC_VECTOR_DIMENSION',\n          message: `\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6\u5FC5\u987B\u4E3A ${VALIDATION_RULES.semantic_vector_dimension}`,\n          field: 'semantic_vector',\n          expected: VALIDATION_RULES.semantic_vector_dimension,\n          actual: morpheme.semantic_vector.length,\n          morpheme_id: morphemeId\n        })\n      }\n\n      // \u9A8C\u8BC1\u5411\u91CF\u5143\u7D20\u662F\u5426\u4E3A\u6570\u5B57\n      for (let i = 0; i < morpheme.semantic_vector.length; i++) {\n        if (typeof morpheme.semantic_vector[i] !== 'number') {\n          errors.push({\n            type: 'error',\n            code: 'INVALID_SEMANTIC_VECTOR_ELEMENT',\n            message: `\u8BED\u4E49\u5411\u91CF\u5143\u7D20\u5FC5\u987B\u662F\u6570\u5B57\u7C7B\u578B`,\n            field: `semantic_vector[${i}]`,\n            expected: 'number',\n            actual: typeof morpheme.semantic_vector[i],\n            morpheme_id: morphemeId\n          })\n        }\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u4E00\u81F4\u6027\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateConsistency(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1ID\u4E0E\u7C7B\u522B\u7684\u4E00\u81F4\u6027\n    if (morpheme.id && morpheme.category) {\n      const expectedPrefix = morpheme.category\n      if (!morpheme.id.startsWith(expectedPrefix)) {\n        errors.push({\n          type: 'warning',\n          code: 'INCONSISTENT_ID_CATEGORY',\n          message: `ID\u524D\u7F00\u4E0E\u7C7B\u522B\u4E0D\u4E00\u81F4`,\n          field: 'id',\n          expected: `${expectedPrefix}_xxx`,\n          actual: morpheme.id,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u8BED\u8A00\u5C5E\u6027\u4E00\u81F4\u6027\n    if (morpheme.language_properties && morpheme.text) {\n      const actualCharCount = morpheme.text.length\n      const declaredCharCount = morpheme.language_properties.character_count\n\n      if (declaredCharCount !== actualCharCount) {\n        errors.push({\n          type: 'warning',\n          code: 'INCONSISTENT_CHARACTER_COUNT',\n          message: `\u58F0\u660E\u7684\u5B57\u7B26\u6570\u4E0E\u5B9E\u9645\u4E0D\u7B26`,\n          field: 'language_properties.character_count',\n          expected: actualCharCount,\n          actual: declaredCharCount,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u81EA\u5B9A\u4E49\u89C4\u5219\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateCustomRules(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n\n    for (const rule of this.customRules) {\n      if (!rule.enabled) continue\n\n      try {\n        const ruleErrors = rule.validator(morpheme)\n        errors.push(...ruleErrors)\n      } catch (error) {\n        errors.push({\n          type: 'error',\n          code: 'CUSTOM_RULE_ERROR',\n          message: `\u81EA\u5B9A\u4E49\u89C4\u5219 '${rule.name}' \u6267\u884C\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`,\n          morpheme_id: morpheme.id || `index_${index}`\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u6DFB\u52A0\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219\n   * \n   * @param rule \u9A8C\u8BC1\u89C4\u5219\n   */\n  addCustomRule(rule: ValidationRule): void {\n    this.customRules.push(rule)\n    console.log(`\uD83D\uDCCB \u6DFB\u52A0\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219: ${rule.name}`)\n  }\n\n  /**\n   * \u79FB\u9664\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219\n   * \n   * @param ruleName \u89C4\u5219\u540D\u79F0\n   */\n  removeCustomRule(ruleName: string): void {\n    const index = this.customRules.findIndex(rule => rule.name === ruleName)\n    if (index !== -1) {\n      this.customRules.splice(index, 1)\n      console.log(`\uD83D\uDDD1\uFE0F \u79FB\u9664\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219: ${ruleName}`)\n    }\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b9cf5f417b8b0ade053cb5971795a19007a1e6f1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bk4eiopux = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bk4eiopux();
/**
 * 数据验证器
 *
 * 提供语素数据的完整性验证、质量检查和一致性校验功能。
 * 支持多层次验证规则和自定义验证策略。
 *
 * @fileoverview 数据验证核心模块
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */
// ============================================================================
// 验证规则配置
// ============================================================================
/** 验证规则配置 */
const VALIDATION_RULES =
/* istanbul ignore next */
(cov_bk4eiopux().s[0]++, {
  /** ID格式正则表达式 */
  id_pattern: /^[a-z_]+_\d{3}$/,
  /** 文本长度限制 */
  text_length: {
    min: 1,
    max: 10
  },
  /** 子分类命名规则 */
  subcategory_pattern: /^[a-z_]+$/,
  /** 标签数量限制 */
  tags_count: {
    min: 1,
    max: 10
  },
  /** 语义向量维度 */
  semantic_vector_dimension: 20,
  /** 质量评分范围 */
  quality_range: {
    min: 0.0,
    max: 1.0
  },
  /** 使用频率范围 */
  frequency_range: {
    min: 0.0,
    max: 1.0
  },
  /** 音节数量限制 */
  syllable_count: {
    min: 1,
    max: 5
  },
  /** 字符数量限制 */
  character_count: {
    min: 1,
    max: 10
  }
});
/** 有效的语素类别 */
const VALID_CATEGORIES =
/* istanbul ignore next */
(cov_bk4eiopux().s[1]++, Object.values({
  EMOTIONS: 'emotions',
  PROFESSIONS: 'professions',
  CHARACTERISTICS: 'characteristics',
  OBJECTS: 'objects',
  ACTIONS: 'actions',
  CONCEPTS: 'concepts'
}));
/** 有效的文化语境 */
const VALID_CONTEXTS =
/* istanbul ignore next */
(cov_bk4eiopux().s[2]++, Object.values({
  ANCIENT: 'ancient',
  MODERN: 'modern',
  NEUTRAL: 'neutral'
}));
// ============================================================================
// 数据验证器类
// ============================================================================
/**
 * 数据验证器类
 *
 * 提供全面的语素数据验证功能
 */
export class DataValidator {
  config;
  customRules;
  /**
   * 构造函数
   *
   * @param config 验证配置
   */
  constructor(config =
  /* istanbul ignore next */
  (cov_bk4eiopux().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[0]++;
    cov_bk4eiopux().s[3]++;
    this.config = {
      strict_mode:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[1][0]++, config.strict_mode) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[1][1]++, true),
      skip_warnings:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[2][0]++, config.skip_warnings) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[2][1]++, false),
      custom_rules:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[3][0]++, config.custom_rules) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[3][1]++, []),
      max_errors:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[4][0]++, config.max_errors) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[4][1]++, 1000)
    };
    /* istanbul ignore next */
    cov_bk4eiopux().s[4]++;
    this.customRules = [...this.config.custom_rules];
  }
  /**
   * 验证语素数据数组
   *
   * @param morphemes 语素数据数组
   * @returns 验证结果
   */
  async validate(morphemes) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[5]++, Date.now());
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[6]++, []);
    const warnings =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[7]++, []);
    let passedCount =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[8]++, 0);
    /* istanbul ignore next */
    cov_bk4eiopux().s[9]++;
    console.log(`🔍 开始验证 ${morphemes.length} 个语素...`);
    /* istanbul ignore next */
    cov_bk4eiopux().s[10]++;
    for (let i =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[11]++, 0); i < morphemes.length; i++) {
      const morpheme =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[12]++, morphemes[i]);
      /* istanbul ignore next */
      cov_bk4eiopux().s[13]++;
      try {
        // 基础验证
        const basicErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[14]++, this._validateBasicStructure(morpheme, i));
        // 数据类型验证
        const typeErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[15]++, this._validateDataTypes(morpheme, i));
        // 业务逻辑验证
        const logicErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[16]++, this._validateBusinessLogic(morpheme, i));
        // 一致性验证
        const consistencyErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[17]++, this._validateConsistency(morpheme, i));
        // 自定义规则验证
        const customErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[18]++, this._validateCustomRules(morpheme, i));
        // 收集所有错误
        const allErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[19]++, [...basicErrors, ...typeErrors, ...logicErrors, ...consistencyErrors, ...customErrors]);
        // 分类错误和警告
        const morphemeErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[20]++, allErrors.filter(e => {
          /* istanbul ignore next */
          cov_bk4eiopux().f[2]++;
          cov_bk4eiopux().s[21]++;
          return e.type === 'error';
        }));
        const morphemeWarnings =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[22]++, allErrors.filter(e => {
          /* istanbul ignore next */
          cov_bk4eiopux().f[3]++;
          cov_bk4eiopux().s[23]++;
          return e.type === 'warning';
        }));
        /* istanbul ignore next */
        cov_bk4eiopux().s[24]++;
        errors.push(...morphemeErrors);
        /* istanbul ignore next */
        cov_bk4eiopux().s[25]++;
        warnings.push(...morphemeWarnings);
        // 如果没有错误，则通过验证
        /* istanbul ignore next */
        cov_bk4eiopux().s[26]++;
        if (morphemeErrors.length === 0) {
          /* istanbul ignore next */
          cov_bk4eiopux().b[5][0]++;
          cov_bk4eiopux().s[27]++;
          passedCount++;
        } else
        /* istanbul ignore next */
        {
          cov_bk4eiopux().b[5][1]++;
        }
        // 检查是否超过最大错误数量
        cov_bk4eiopux().s[28]++;
        if (errors.length >= this.config.max_errors) {
          /* istanbul ignore next */
          cov_bk4eiopux().b[6][0]++;
          cov_bk4eiopux().s[29]++;
          console.warn(`⚠️ 错误数量超过限制 (${this.config.max_errors})，停止验证`);
          /* istanbul ignore next */
          cov_bk4eiopux().s[30]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_bk4eiopux().b[6][1]++;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_bk4eiopux().s[31]++;
        errors.push({
          type: 'error',
          code: 'VALIDATION_EXCEPTION',
          message: `验证过程中发生异常: ${error instanceof Error ?
          /* istanbul ignore next */
          (cov_bk4eiopux().b[7][0]++, error.message) :
          /* istanbul ignore next */
          (cov_bk4eiopux().b[7][1]++, String(error))}`,
          morpheme_id:
          /* istanbul ignore next */
          (cov_bk4eiopux().b[8][0]++, morpheme.id) ||
          /* istanbul ignore next */
          (cov_bk4eiopux().b[8][1]++, `index_${i}`)
        });
      }
    }
    const validationTime =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[32]++, Date.now() - startTime);
    const passed =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[33]++, errors.length === 0);
    // 输出验证结果摘要
    /* istanbul ignore next */
    cov_bk4eiopux().s[34]++;
    if (passed) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[9][0]++;
      cov_bk4eiopux().s[35]++;
      console.log(`✅ 验证通过: ${passedCount}/${morphemes.length} 个语素, 耗时 ${validationTime}ms`);
    } else {
      /* istanbul ignore next */
      cov_bk4eiopux().b[9][1]++;
      cov_bk4eiopux().s[36]++;
      console.warn(`❌ 验证失败: ${passedCount}/${morphemes.length} 个语素通过, ${errors.length} 个错误, ${warnings.length} 个警告, 耗时 ${validationTime}ms`);
    }
    /* istanbul ignore next */
    cov_bk4eiopux().s[37]++;
    return {
      passed,
      total_count: morphemes.length,
      passed_count: passedCount,
      failed_count: morphemes.length - passedCount,
      errors,
      warnings,
      validation_time: validationTime,
      timestamp: Date.now()
    };
  }
  /**
   * 验证基础结构
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateBasicStructure(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[4]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[38]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[39]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[10][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[10][1]++, `index_${index}`));
    // 验证必需字段 (v1.0兼容)
    const requiredFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[40]++, ['id', 'text', 'category', 'subcategory', 'cultural_context', 'usage_frequency', 'quality_score', 'semantic_vector', 'tags', 'created_at', 'source']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[41]++;
    for (const field of requiredFields) {
      /* istanbul ignore next */
      cov_bk4eiopux().s[42]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[12][0]++, !(field in morpheme)) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[12][1]++, morpheme[field] === undefined) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[12][2]++, morpheme[field] === null)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[11][0]++;
        cov_bk4eiopux().s[43]++;
        errors.push({
          type: 'error',
          code: 'MISSING_REQUIRED_FIELD',
          message: `缺少必需字段: ${field}`,
          field,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[11][1]++;
      }
    }
    // 验证可选字段 (v2.0新增) - 如果存在则验证格式
    const optionalFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[44]++, ['language_properties', 'quality_metrics', 'version']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[45]++;
    for (const field of optionalFields) {
      /* istanbul ignore next */
      cov_bk4eiopux().s[46]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[14][0]++, field in morpheme) &&
      /* istanbul ignore next */
      (cov_bk4eiopux().b[14][1]++, morpheme[field] === undefined)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[13][0]++;
        cov_bk4eiopux().s[47]++;
        errors.push({
          type: 'warning',
          code: 'INVALID_OPTIONAL_FIELD',
          message: `可选字段 ${field} 存在但值为undefined`,
          field,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[13][1]++;
      }
    }
    /* istanbul ignore next */
    cov_bk4eiopux().s[48]++;
    return errors;
  }
  /**
   * 验证数据类型
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateDataTypes(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[5]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[49]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[50]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[15][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[15][1]++, `index_${index}`));
    // 验证字符串字段
    const stringFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[51]++, ['id', 'text', 'subcategory', 'source', 'version']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[52]++;
    for (const field of stringFields) {
      const value =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[53]++, morpheme[field]);
      /* istanbul ignore next */
      cov_bk4eiopux().s[54]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[17][0]++, value !== undefined) &&
      /* istanbul ignore next */
      (cov_bk4eiopux().b[17][1]++, typeof value !== 'string')) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[16][0]++;
        cov_bk4eiopux().s[55]++;
        errors.push({
          type: 'error',
          code: 'INVALID_DATA_TYPE',
          message: `字段 ${field} 必须是字符串类型`,
          field,
          expected: 'string',
          actual: typeof value,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[16][1]++;
      }
    }
    // 验证数字字段
    const numberFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[56]++, ['usage_frequency', 'quality_score', 'created_at']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[57]++;
    for (const field of numberFields) {
      const value =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[58]++, morpheme[field]);
      /* istanbul ignore next */
      cov_bk4eiopux().s[59]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[19][0]++, value !== undefined) &&
      /* istanbul ignore next */
      (cov_bk4eiopux().b[19][1]++, typeof value !== 'number')) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[18][0]++;
        cov_bk4eiopux().s[60]++;
        errors.push({
          type: 'error',
          code: 'INVALID_DATA_TYPE',
          message: `字段 ${field} 必须是数字类型`,
          field,
          expected: 'number',
          actual: typeof value,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[18][1]++;
      }
    }
    // 验证数组字段
    /* istanbul ignore next */
    cov_bk4eiopux().s[61]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[21][0]++, morpheme.semantic_vector !== undefined) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[21][1]++, !Array.isArray(morpheme.semantic_vector))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[20][0]++;
      cov_bk4eiopux().s[62]++;
      errors.push({
        type: 'error',
        code: 'INVALID_DATA_TYPE',
        message: 'semantic_vector 必须是数组类型',
        field: 'semantic_vector',
        expected: 'array',
        actual: typeof morpheme.semantic_vector,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[20][1]++;
    }
    cov_bk4eiopux().s[63]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[23][0]++, morpheme.tags !== undefined) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[23][1]++, !Array.isArray(morpheme.tags))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[22][0]++;
      cov_bk4eiopux().s[64]++;
      errors.push({
        type: 'error',
        code: 'INVALID_DATA_TYPE',
        message: 'tags 必须是数组类型',
        field: 'tags',
        expected: 'array',
        actual: typeof morpheme.tags,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[22][1]++;
    }
    cov_bk4eiopux().s[65]++;
    return errors;
  }
  /**
   * 验证业务逻辑
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateBusinessLogic(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[6]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[66]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[67]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[24][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[24][1]++, `index_${index}`));
    // 验证ID格式
    /* istanbul ignore next */
    cov_bk4eiopux().s[68]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[26][0]++, morpheme.id) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[26][1]++, !VALIDATION_RULES.id_pattern.test(morpheme.id))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[25][0]++;
      cov_bk4eiopux().s[69]++;
      errors.push({
        type: 'error',
        code: 'INVALID_ID_FORMAT',
        message: `ID格式不正确，应为: {category}_{number}`,
        field: 'id',
        expected: VALIDATION_RULES.id_pattern.toString(),
        actual: morpheme.id,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[25][1]++;
    }
    // 验证文本长度
    cov_bk4eiopux().s[70]++;
    if (morpheme.text) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[27][0]++;
      const textLength =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[71]++, morpheme.text.length);
      /* istanbul ignore next */
      cov_bk4eiopux().s[72]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[29][0]++, textLength < VALIDATION_RULES.text_length.min) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[29][1]++, textLength > VALIDATION_RULES.text_length.max)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[28][0]++;
        cov_bk4eiopux().s[73]++;
        errors.push({
          type: 'error',
          code: 'INVALID_TEXT_LENGTH',
          message: `文本长度必须在 ${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max} 之间`,
          field: 'text',
          expected: `${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max}`,
          actual: textLength,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[28][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[27][1]++;
    }
    // 验证类别
    cov_bk4eiopux().s[74]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[31][0]++, morpheme.category) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[31][1]++, !VALID_CATEGORIES.includes(morpheme.category))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[30][0]++;
      cov_bk4eiopux().s[75]++;
      errors.push({
        type: 'error',
        code: 'INVALID_CATEGORY',
        message: `无效的语素类别`,
        field: 'category',
        expected: VALID_CATEGORIES,
        actual: morpheme.category,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[30][1]++;
    }
    // 验证文化语境
    cov_bk4eiopux().s[76]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[33][0]++, morpheme.cultural_context) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[33][1]++, !VALID_CONTEXTS.includes(morpheme.cultural_context))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[32][0]++;
      cov_bk4eiopux().s[77]++;
      errors.push({
        type: 'error',
        code: 'INVALID_CULTURAL_CONTEXT',
        message: `无效的文化语境`,
        field: 'cultural_context',
        expected: VALID_CONTEXTS,
        actual: morpheme.cultural_context,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[32][1]++;
    }
    // 验证质量评分范围
    cov_bk4eiopux().s[78]++;
    if (typeof morpheme.quality_score === 'number') {
      /* istanbul ignore next */
      cov_bk4eiopux().b[34][0]++;
      cov_bk4eiopux().s[79]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[36][0]++, morpheme.quality_score < VALIDATION_RULES.quality_range.min) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[36][1]++, morpheme.quality_score > VALIDATION_RULES.quality_range.max)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[35][0]++;
        cov_bk4eiopux().s[80]++;
        errors.push({
          type: 'error',
          code: 'INVALID_QUALITY_SCORE',
          message: `质量评分必须在 ${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max} 之间`,
          field: 'quality_score',
          expected: `${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max}`,
          actual: morpheme.quality_score,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[35][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[34][1]++;
    }
    // 验证使用频率范围
    cov_bk4eiopux().s[81]++;
    if (typeof morpheme.usage_frequency === 'number') {
      /* istanbul ignore next */
      cov_bk4eiopux().b[37][0]++;
      cov_bk4eiopux().s[82]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[39][0]++, morpheme.usage_frequency < VALIDATION_RULES.frequency_range.min) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[39][1]++, morpheme.usage_frequency > VALIDATION_RULES.frequency_range.max)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[38][0]++;
        cov_bk4eiopux().s[83]++;
        errors.push({
          type: 'error',
          code: 'INVALID_USAGE_FREQUENCY',
          message: `使用频率必须在 ${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max} 之间`,
          field: 'usage_frequency',
          expected: `${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max}`,
          actual: morpheme.usage_frequency,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[38][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[37][1]++;
    }
    // 验证语义向量
    cov_bk4eiopux().s[84]++;
    if (Array.isArray(morpheme.semantic_vector)) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[40][0]++;
      cov_bk4eiopux().s[85]++;
      if (morpheme.semantic_vector.length !== VALIDATION_RULES.semantic_vector_dimension) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[41][0]++;
        cov_bk4eiopux().s[86]++;
        errors.push({
          type: 'error',
          code: 'INVALID_SEMANTIC_VECTOR_DIMENSION',
          message: `语义向量维度必须为 ${VALIDATION_RULES.semantic_vector_dimension}`,
          field: 'semantic_vector',
          expected: VALIDATION_RULES.semantic_vector_dimension,
          actual: morpheme.semantic_vector.length,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[41][1]++;
      }
      // 验证向量元素是否为数字
      cov_bk4eiopux().s[87]++;
      for (let i =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[88]++, 0); i < morpheme.semantic_vector.length; i++) {
        /* istanbul ignore next */
        cov_bk4eiopux().s[89]++;
        if (typeof morpheme.semantic_vector[i] !== 'number') {
          /* istanbul ignore next */
          cov_bk4eiopux().b[42][0]++;
          cov_bk4eiopux().s[90]++;
          errors.push({
            type: 'error',
            code: 'INVALID_SEMANTIC_VECTOR_ELEMENT',
            message: `语义向量元素必须是数字类型`,
            field: `semantic_vector[${i}]`,
            expected: 'number',
            actual: typeof morpheme.semantic_vector[i],
            morpheme_id: morphemeId
          });
        } else
        /* istanbul ignore next */
        {
          cov_bk4eiopux().b[42][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[40][1]++;
    }
    cov_bk4eiopux().s[91]++;
    return errors;
  }
  /**
   * 验证一致性
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateConsistency(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[7]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[92]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[93]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[43][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[43][1]++, `index_${index}`));
    // 验证ID与类别的一致性
    /* istanbul ignore next */
    cov_bk4eiopux().s[94]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[45][0]++, morpheme.id) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[45][1]++, morpheme.category)) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[44][0]++;
      const expectedPrefix =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[95]++, morpheme.category);
      /* istanbul ignore next */
      cov_bk4eiopux().s[96]++;
      if (!morpheme.id.startsWith(expectedPrefix)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[46][0]++;
        cov_bk4eiopux().s[97]++;
        errors.push({
          type: 'warning',
          code: 'INCONSISTENT_ID_CATEGORY',
          message: `ID前缀与类别不一致`,
          field: 'id',
          expected: `${expectedPrefix}_xxx`,
          actual: morpheme.id,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[46][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[44][1]++;
    }
    // 验证语言属性一致性
    cov_bk4eiopux().s[98]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[48][0]++, morpheme.language_properties) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[48][1]++, morpheme.text)) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[47][0]++;
      const actualCharCount =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[99]++, morpheme.text.length);
      const declaredCharCount =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[100]++, morpheme.language_properties.character_count);
      /* istanbul ignore next */
      cov_bk4eiopux().s[101]++;
      if (declaredCharCount !== actualCharCount) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[49][0]++;
        cov_bk4eiopux().s[102]++;
        errors.push({
          type: 'warning',
          code: 'INCONSISTENT_CHARACTER_COUNT',
          message: `声明的字符数与实际不符`,
          field: 'language_properties.character_count',
          expected: actualCharCount,
          actual: declaredCharCount,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[49][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[47][1]++;
    }
    cov_bk4eiopux().s[103]++;
    return errors;
  }
  /**
   * 验证自定义规则
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateCustomRules(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[8]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[104]++, []);
    /* istanbul ignore next */
    cov_bk4eiopux().s[105]++;
    for (const rule of this.customRules) {
      /* istanbul ignore next */
      cov_bk4eiopux().s[106]++;
      if (!rule.enabled) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[50][0]++;
        cov_bk4eiopux().s[107]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[50][1]++;
      }
      cov_bk4eiopux().s[108]++;
      try {
        const ruleErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[109]++, rule.validator(morpheme));
        /* istanbul ignore next */
        cov_bk4eiopux().s[110]++;
        errors.push(...ruleErrors);
      } catch (error) {
        /* istanbul ignore next */
        cov_bk4eiopux().s[111]++;
        errors.push({
          type: 'error',
          code: 'CUSTOM_RULE_ERROR',
          message: `自定义规则 '${rule.name}' 执行失败: ${error instanceof Error ?
          /* istanbul ignore next */
          (cov_bk4eiopux().b[51][0]++, error.message) :
          /* istanbul ignore next */
          (cov_bk4eiopux().b[51][1]++, String(error))}`,
          morpheme_id:
          /* istanbul ignore next */
          (cov_bk4eiopux().b[52][0]++, morpheme.id) ||
          /* istanbul ignore next */
          (cov_bk4eiopux().b[52][1]++, `index_${index}`)
        });
      }
    }
    /* istanbul ignore next */
    cov_bk4eiopux().s[112]++;
    return errors;
  }
  /**
   * 添加自定义验证规则
   *
   * @param rule 验证规则
   */
  addCustomRule(rule) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[9]++;
    cov_bk4eiopux().s[113]++;
    this.customRules.push(rule);
    /* istanbul ignore next */
    cov_bk4eiopux().s[114]++;
    console.log(`📋 添加自定义验证规则: ${rule.name}`);
  }
  /**
   * 移除自定义验证规则
   *
   * @param ruleName 规则名称
   */
  removeCustomRule(ruleName) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[10]++;
    const index =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[115]++, this.customRules.findIndex(rule => {
      /* istanbul ignore next */
      cov_bk4eiopux().f[11]++;
      cov_bk4eiopux().s[116]++;
      return rule.name === ruleName;
    }));
    /* istanbul ignore next */
    cov_bk4eiopux().s[117]++;
    if (index !== -1) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[53][0]++;
      cov_bk4eiopux().s[118]++;
      this.customRules.splice(index, 1);
      /* istanbul ignore next */
      cov_bk4eiopux().s[119]++;
      console.log(`🗑️ 移除自定义验证规则: ${ruleName}`);
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[53][1]++;
    }
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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