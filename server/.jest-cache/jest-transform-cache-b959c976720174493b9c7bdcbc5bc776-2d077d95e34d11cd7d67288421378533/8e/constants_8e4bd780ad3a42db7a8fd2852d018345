6dff8bbdec0b5396d93205f817d5263c
/* istanbul ignore next */
function cov_gpvvqkfzd() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts";
  var hash = "3294e38527b696fd39ce7f2f763f3bc1fb85b73d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 33
        },
        end: {
          line: 13,
          column: 111
        }
      },
      "1": {
        start: {
          line: 15,
          column: 19
        },
        end: {
          line: 33,
          column: 1
        }
      },
      "2": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 58,
          column: 1
        }
      },
      "3": {
        start: {
          line: 60,
          column: 26
        },
        end: {
          line: 104,
          column: 1
        }
      },
      "4": {
        start: {
          line: 106,
          column: 21
        },
        end: {
          line: 113,
          column: 1
        }
      },
      "5": {
        start: {
          line: 115,
          column: 27
        },
        end: {
          line: 120,
          column: 1
        }
      },
      "6": {
        start: {
          line: 122,
          column: 30
        },
        end: {
          line: 147,
          column: 1
        }
      },
      "7": {
        start: {
          line: 124,
          column: 37
        },
        end: {
          line: 124,
          column: 87
        }
      },
      "8": {
        start: {
          line: 125,
          column: 48
        },
        end: {
          line: 125,
          column: 109
        }
      },
      "9": {
        start: {
          line: 126,
          column: 49
        },
        end: {
          line: 126,
          column: 111
        }
      },
      "10": {
        start: {
          line: 127,
          column: 54
        },
        end: {
          line: 127,
          column: 117
        }
      },
      "11": {
        start: {
          line: 128,
          column: 51
        },
        end: {
          line: 128,
          column: 111
        }
      },
      "12": {
        start: {
          line: 131,
          column: 46
        },
        end: {
          line: 131,
          column: 101
        }
      },
      "13": {
        start: {
          line: 132,
          column: 52
        },
        end: {
          line: 132,
          column: 117
        }
      },
      "14": {
        start: {
          line: 133,
          column: 52
        },
        end: {
          line: 133,
          column: 117
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 124,
            column: 23
          },
          end: {
            line: 124,
            column: 24
          }
        },
        loc: {
          start: {
            line: 124,
            column: 37
          },
          end: {
            line: 124,
            column: 87
          }
        },
        line: 124
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 125,
            column: 34
          },
          end: {
            line: 125,
            column: 35
          }
        },
        loc: {
          start: {
            line: 125,
            column: 48
          },
          end: {
            line: 125,
            column: 109
          }
        },
        line: 125
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 126,
            column: 35
          },
          end: {
            line: 126,
            column: 36
          }
        },
        loc: {
          start: {
            line: 126,
            column: 49
          },
          end: {
            line: 126,
            column: 111
          }
        },
        line: 126
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 127,
            column: 37
          },
          end: {
            line: 127,
            column: 38
          }
        },
        loc: {
          start: {
            line: 127,
            column: 54
          },
          end: {
            line: 127,
            column: 117
          }
        },
        line: 127
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 128,
            column: 34
          },
          end: {
            line: 128,
            column: 35
          }
        },
        loc: {
          start: {
            line: 128,
            column: 51
          },
          end: {
            line: 128,
            column: 111
          }
        },
        line: 128
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 131,
            column: 26
          },
          end: {
            line: 131,
            column: 27
          }
        },
        loc: {
          start: {
            line: 131,
            column: 46
          },
          end: {
            line: 131,
            column: 101
          }
        },
        line: 131
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 132,
            column: 38
          },
          end: {
            line: 132,
            column: 39
          }
        },
        loc: {
          start: {
            line: 132,
            column: 52
          },
          end: {
            line: 132,
            column: 117
          }
        },
        line: 132
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 133,
            column: 38
          },
          end: {
            line: 133,
            column: 39
          }
        },
        loc: {
          start: {
            line: 133,
            column: 52
          },
          end: {
            line: 133,
            column: 117
          }
        },
        line: 133
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {},
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts",
      mappings: "AAAA;;;;;;;;;GASG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAA;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAA;AAEpD,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAU,CAAA;AAExH,WAAW;AACX,MAAM,CAAC,MAAM,GAAG,GAAG;IACjB,qBAAqB;IACrB,OAAO,EAAE,oBAAoB;IAC7B,SAAS;IACT,iBAAiB,EAAE,MAAM;IACzB,OAAO;IACP,gBAAgB,EAAE,YAAY,CAAC,KAAK;IACpC,UAAU;IACV,mBAAmB,EAAE;QACnB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,KAAK;KACV;CACF,CAAA;AAEV,qBAAqB;AACrB,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,yBAAyB;IACzB,kBAAkB,EAAE,EAAE;IACtB,SAAS;IACT,sBAAsB,EAAE,CAAC;IACzB,SAAS;IACT,wBAAwB,EAAE,GAAG;IAC7B,SAAS;IACT,yBAAyB,EAAE,GAAG;IAC9B,SAAS;IACT,yBAAyB,EAAE,QAAiB;IAC5C,SAAS;IACT,2BAA2B,EAAE,eAAe,CAAC,OAAO;IACpD,SAAS;IACT,wBAAwB,EAAE,UAAmB;IAC7C,WAAW;IACX,kBAAkB,EAAE,CAAC;IACrB,aAAa;IACb,eAAe,EAAE,KAAK;IACtB,cAAc;IACd,gBAAgB,EAAE,YAAY,CAAC,KAAK;IACpC,oBAAoB,EAAE,KAAK;IAC3B,8BAA8B,EAAE,GAAG;CAC3B,CAAA;AAEV,qBAAqB;AACrB,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,uBAAuB;IACvB,KAAK,EAAE;QACL,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,EAAE;KACR;IACD,SAAS;IACT,gBAAgB,EAAE;QAChB,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;KACT;IACD,SAAS;IACT,iBAAiB,EAAE;QACjB,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;KACT;IACD,mBAAmB;IACnB,oBAAoB,EAAE;QACpB,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;KACT;IACD,mBAAmB;IACnB,oBAAoB,EAAE;QACpB,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;KACT;IACD,SAAS;IACT,WAAW,EAAE;QACX,kBAAkB,EAAE,OAAO;QAC3B,sBAAsB,EAAE,OAAO;QAC/B,iBAAiB,EAAE,OAAO;QAC1B,iBAAiB,EAAE,OAAO;QAC1B,cAAc,EAAE,OAAO;QACvB,oBAAoB,EAAE,OAAO,EAAG,SAAS;QACzC,4BAA4B,EAAE,OAAO,CAAE,SAAS;KACjD;IACD,iBAAiB;IACjB,mBAAmB,EAAE,GAAG,CAAC,mBAAmB;IAC5C,SAAS;IACT,oBAAoB,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAU;IACxE,SAAS;IACT,iBAAiB,EAAE,iBAAiB;IACpC,SAAS;IACT,kBAAkB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAU;CAChD,CAAA;AAEV,SAAS;AACT,MAAM,CAAC,MAAM,KAAK,GAAG;IACnB,YAAY;IACZ,WAAW,EAAE,IAAI;IACjB,UAAU;IACV,SAAS,EAAE,IAAI;IACf,QAAQ;IACR,UAAU,EAAE,QAAQ;CACZ,CAAA;AAEV,SAAS;AACT,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,YAAY;IACZ,oBAAoB,EAAE,GAAG;IACzB,aAAa;IACb,sBAAsB,EAAE,GAAG;CACnB,CAAA;AAEV,mBAAmB;AACnB,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,UAAU,EAAE;QACV,aAAa,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,kCAAkC,GAAG,QAAQ,GAAG,EAAE;QAC/F,wBAAwB,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,6CAA6C,GAAG,QAAQ,GAAG,EAAE;QACrH,yBAAyB,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,8CAA8C,GAAG,QAAQ,GAAG,EAAE;QACvH,2BAA2B,EAAE,CAAC,WAA8B,EAAE,EAAE,CAAC,uCAAuC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAChI,wBAAwB,EAAE,CAAC,WAA8B,EAAE,EAAE,CAAC,oCAAoC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC1H,sBAAsB,EAAE,2BAA2B;QACnD,gBAAgB;QAChB,gBAAgB,EAAE,CAAC,cAAiC,EAAE,EAAE,CAAC,4BAA4B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAChH,4BAA4B,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,iDAAiD,GAAG,QAAQ,GAAG,EAAE;QAC7H,4BAA4B,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,iDAAiD,GAAG,QAAQ,GAAG,EAAE;QAC7H,gCAAgC,EAAE,uEAAuE;QACzG,4BAA4B,EAAE,4CAA4C;QAC1E,2BAA2B,EAAE,sEAAsE;KACpG;IACD,QAAQ,EAAE;QACR,iBAAiB,EAAE,8BAA8B;QACjD,iBAAiB,EAAE,2BAA2B;QAC9C,cAAc,EAAE,uBAAuB;QACvC,gBAAgB;QAChB,yBAAyB,EAAE,sCAAsC;QACjE,4BAA4B,EAAE,sCAAsC;QACpE,wBAAwB,EAAE,sCAAsC;KACjE;CACO,CAAA",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts"],
      sourcesContent: ["/**\n * \u5E94\u7528\u5E38\u91CF\u914D\u7F6E\n *\n * \u5305\u542BAPI\u914D\u7F6E\u3001\u751F\u6210\u53C2\u6570\u3001\u9A8C\u8BC1\u89C4\u5219\u3001\u7F13\u5B58\u8BBE\u7F6E\u7B49\u5168\u5C40\u5E38\u91CF\n *\n * @fileoverview \u5E94\u7528\u5E38\u91CF\u914D\u7F6E\u6587\u4EF6\n * @version 1.0.0\n * @since 2025-06-24\n * <AUTHOR> team\n */\n\nimport { CulturalContext } from \"../types/core\"\nimport { LanguageCode } from \"../types/multilingual\"\n\nexport const STYLE_PREFERENCES = ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional'] as const\n\n// API \u76F8\u5173\u5E38\u91CF\nexport const API = {\n  // API \u7248\u672C (v3.0\u591A\u8BED\u79CD\u652F\u6301)\n  VERSION: '3.0.0-multilingual',\n  // \u8BF7\u6C42ID\u524D\u7F00\n  REQUEST_ID_PREFIX: 'req_',\n  // \u9ED8\u8BA4\u8BED\u8A00\n  DEFAULT_LANGUAGE: LanguageCode.ZH_CN,\n  // \u652F\u6301\u7684\u8BED\u8A00\u5217\u8868\n  SUPPORTED_LANGUAGES: [\n    LanguageCode.ZH_CN,\n    LanguageCode.EN_US,\n    LanguageCode.JA_JP,\n    LanguageCode.KO_KR,\n    LanguageCode.ES_ES,\n    LanguageCode.FR_FR,\n    LanguageCode.DE_DE,\n    LanguageCode.AR_SA\n  ] as const\n} as const\n\n// \u751F\u6210\u76F8\u5173\u5E38\u91CF (v3.0\u591A\u8BED\u79CD\u4F18\u5316)\nexport const GENERATION = {\n  // \u6BCF\u6B21\u8BF7\u6C42\u6700\u5927\u751F\u6210\u6570\u91CF (v3.0\u7B80\u5316\u4E3A10)\n  MAX_GENERATE_COUNT: 10,\n  // \u9ED8\u8BA4\u751F\u6210\u6570\u91CF\n  DEFAULT_GENERATE_COUNT: 5,\n  // \u9ED8\u8BA4\u521B\u610F\u7A0B\u5EA6\n  DEFAULT_CREATIVITY_LEVEL: 0.7,\n  // \u9ED8\u8BA4\u8D28\u91CF\u9608\u503C\n  DEFAULT_QUALITY_THRESHOLD: 0.6,\n  // \u9ED8\u8BA4\u957F\u5EA6\u504F\u597D\n  DEFAULT_LENGTH_PREFERENCE: 'medium' as const,\n  // \u9ED8\u8BA4\u6587\u5316\u504F\u597D\n  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,\n  // \u9ED8\u8BA4\u98CE\u683C\u504F\u597D\n  DEFAULT_STYLE_PREFERENCE: 'humorous' as const,\n  // \u9ED8\u8BA4\u6700\u5927\u91CD\u8BD5\u6B21\u6570\n  MAX_RETRY_ATTEMPTS: 3,\n  // \u8BF7\u6C42\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09\n  REQUEST_TIMEOUT: 15000,\n  // v3.0\u591A\u8BED\u79CD\u65B0\u589E\u914D\u7F6E\n  DEFAULT_LANGUAGE: LanguageCode.ZH_CN,\n  ENABLE_CROSS_LINGUAL: false,\n  SEMANTIC_CONSISTENCY_THRESHOLD: 0.8\n} as const\n\n// \u9A8C\u8BC1\u76F8\u5173\u5E38\u91CF (v3.0\u591A\u8BED\u79CD\u6269\u5C55)\nexport const VALIDATION = {\n  // \u751F\u6210\u6570\u91CF\u8303\u56F4 (v3.0\u8C03\u6574\u4E3A1-10)\n  COUNT: {\n    MIN: 1,\n    MAX: 10\n  },\n  // \u521B\u610F\u7A0B\u5EA6\u8303\u56F4\n  CREATIVITY_LEVEL: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // \u8D28\u91CF\u9608\u503C\u8303\u56F4\n  QUALITY_THRESHOLD: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // \u8BED\u4E49\u4E00\u81F4\u6027\u8303\u56F4 (v3.0\u65B0\u589E)\n  SEMANTIC_CONSISTENCY: {\n    MIN: 0.0,\n    MAX: 1.0\n  },\n  // \u6587\u5316\u654F\u611F\u5EA6\u8303\u56F4 (v3.0\u65B0\u589E)\n  CULTURAL_SENSITIVITY: {\n    MIN: 0.0,\n    MAX: 1.0\n  },\n  // \u9ED8\u8BA4\u9519\u8BEF\u4EE3\u7801\n  ERROR_CODES: {\n    INVALID_PARAMETERS: 'E1001',\n    MISSING_REQUIRED_FIELD: 'E1002',\n    VALIDATION_FAILED: 'E1003',\n    GENERATION_FAILED: 'E3001',\n    INTERNAL_ERROR: 'E5001',\n    UNSUPPORTED_LANGUAGE: 'E1004',  // v3.0\u65B0\u589E\n    INVALID_MULTILINGUAL_OPTIONS: 'E1005'  // v3.0\u65B0\u589E\n  },\n  // \u652F\u6301\u7684\u8BED\u8A00 (v3.0\u65B0\u589E)\n  SUPPORTED_LANGUAGES: API.SUPPORTED_LANGUAGES,\n  // \u6587\u5316\u504F\u597D\u9009\u9879\n  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'] as const,\n  // \u98CE\u683C\u504F\u597D\u9009\u9879\n  STYLE_PREFERENCES: STYLE_PREFERENCES,\n  // \u957F\u5EA6\u504F\u597D\u9009\u9879\n  LENGTH_PREFERENCES: ['short', 'medium', 'long'] as const\n} as const\n\n// \u7F13\u5B58\u76F8\u5173\u5E38\u91CF\nexport const CACHE = {\n  // \u9ED8\u8BA4\u7F13\u5B58\u65F6\u95F4\uFF08\u79D2\uFF09\n  DEFAULT_TTL: 3600,\n  // \u6700\u5927\u7F13\u5B58\u6761\u76EE\u6570\n  MAX_ITEMS: 1000,\n  // \u7F13\u5B58\u952E\u524D\u7F00\n  KEY_PREFIX: 'namer:'\n} as const\n\n// \u6027\u80FD\u76F8\u5173\u5E38\u91CF\nexport const PERFORMANCE = {\n  // \u6162\u67E5\u8BE2\u9608\u503C\uFF08\u6BEB\u79D2\uFF09\n  SLOW_QUERY_THRESHOLD: 500,\n  // \u76D1\u63A7\u91C7\u6837\u7387\uFF080-1\uFF09\n  MONITORING_SAMPLE_RATE: 0.1\n} as const\n\n// \u9519\u8BEF\u6D88\u606F (v3.0\u591A\u8BED\u79CD\u6269\u5C55)\nexport const ERROR_MESSAGES = {\n  VALIDATION: {\n    INVALID_COUNT: (min: number, max: number) => `count must be a number between ${min} and ${max}`,\n    INVALID_CREATIVITY_LEVEL: (min: number, max: number) => `creativity_level must be a number between ${min} and ${max}`,\n    INVALID_QUALITY_THRESHOLD: (min: number, max: number) => `quality_threshold must be a number between ${min} and ${max}`,\n    INVALID_CULTURAL_PREFERENCE: (validValues: readonly string[]) => `cultural_preference must be one of: ${validValues.join(', ')}`,\n    INVALID_STYLE_PREFERENCE: (validValues: readonly string[]) => `style_preference must be one of: ${validValues.join(', ')}`,\n    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array',\n    // v3.0\u591A\u8BED\u79CD\u65B0\u589E\u9519\u8BEF\u6D88\u606F\n    INVALID_LANGUAGE: (validLanguages: readonly string[]) => `language must be one of: ${validLanguages.join(', ')}`,\n    INVALID_SEMANTIC_CONSISTENCY: (min: number, max: number) => `semantic_consistency must be a number between ${min} and ${max}`,\n    INVALID_CULTURAL_SENSITIVITY: (min: number, max: number) => `cultural_sensitivity must be a number between ${min} and ${max}`,\n    UNSUPPORTED_LANGUAGE_COMBINATION: 'The specified source and target language combination is not supported',\n    INVALID_MULTILINGUAL_OPTIONS: 'Invalid multilingual options configuration',\n    CONCEPT_PREFERENCES_INVALID: 'concept_preferences must be a valid object with supported categories'\n  },\n  INTERNAL: {\n    GENERATION_FAILED: 'Failed to generate usernames',\n    VALIDATION_FAILED: 'Request validation failed',\n    INTERNAL_ERROR: 'Internal server error',\n    // v3.0\u591A\u8BED\u79CD\u65B0\u589E\u5185\u90E8\u9519\u8BEF\n    MULTILINGUAL_ENGINE_ERROR: 'Multilingual generation engine error',\n    CROSS_LINGUAL_MAPPING_FAILED: 'Cross-lingual concept mapping failed',\n    SEMANTIC_ALIGNMENT_ERROR: 'Semantic alignment calculation error'\n  }\n} as const\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3294e38527b696fd39ce7f2f763f3bc1fb85b73d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_gpvvqkfzd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gpvvqkfzd();
/**
 * 应用常量配置
 *
 * 包含API配置、生成参数、验证规则、缓存设置等全局常量
 *
 * @fileoverview 应用常量配置文件
 * @version 1.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */
import { CulturalContext } from "../types/core";
import { LanguageCode } from "../types/multilingual";
export const STYLE_PREFERENCES =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[0]++, ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional']);
// API 相关常量
export const API =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[1]++, {
  // API 版本 (v3.0多语种支持)
  VERSION: '3.0.0-multilingual',
  // 请求ID前缀
  REQUEST_ID_PREFIX: 'req_',
  // 默认语言
  DEFAULT_LANGUAGE: LanguageCode.ZH_CN,
  // 支持的语言列表
  SUPPORTED_LANGUAGES: [LanguageCode.ZH_CN, LanguageCode.EN_US, LanguageCode.JA_JP, LanguageCode.KO_KR, LanguageCode.ES_ES, LanguageCode.FR_FR, LanguageCode.DE_DE, LanguageCode.AR_SA]
});
// 生成相关常量 (v3.0多语种优化)
export const GENERATION =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[2]++, {
  // 每次请求最大生成数量 (v3.0简化为10)
  MAX_GENERATE_COUNT: 10,
  // 默认生成数量
  DEFAULT_GENERATE_COUNT: 5,
  // 默认创意程度
  DEFAULT_CREATIVITY_LEVEL: 0.7,
  // 默认质量阈值
  DEFAULT_QUALITY_THRESHOLD: 0.6,
  // 默认长度偏好
  DEFAULT_LENGTH_PREFERENCE: 'medium',
  // 默认文化偏好
  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,
  // 默认风格偏好
  DEFAULT_STYLE_PREFERENCE: 'humorous',
  // 默认最大重试次数
  MAX_RETRY_ATTEMPTS: 3,
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 15000,
  // v3.0多语种新增配置
  DEFAULT_LANGUAGE: LanguageCode.ZH_CN,
  ENABLE_CROSS_LINGUAL: false,
  SEMANTIC_CONSISTENCY_THRESHOLD: 0.8
});
// 验证相关常量 (v3.0多语种扩展)
export const VALIDATION =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[3]++, {
  // 生成数量范围 (v3.0调整为1-10)
  COUNT: {
    MIN: 1,
    MAX: 10
  },
  // 创意程度范围
  CREATIVITY_LEVEL: {
    MIN: 0.1,
    MAX: 1.0
  },
  // 质量阈值范围
  QUALITY_THRESHOLD: {
    MIN: 0.1,
    MAX: 1.0
  },
  // 语义一致性范围 (v3.0新增)
  SEMANTIC_CONSISTENCY: {
    MIN: 0.0,
    MAX: 1.0
  },
  // 文化敏感度范围 (v3.0新增)
  CULTURAL_SENSITIVITY: {
    MIN: 0.0,
    MAX: 1.0
  },
  // 默认错误代码
  ERROR_CODES: {
    INVALID_PARAMETERS: 'E1001',
    MISSING_REQUIRED_FIELD: 'E1002',
    VALIDATION_FAILED: 'E1003',
    GENERATION_FAILED: 'E3001',
    INTERNAL_ERROR: 'E5001',
    UNSUPPORTED_LANGUAGE: 'E1004',
    // v3.0新增
    INVALID_MULTILINGUAL_OPTIONS: 'E1005' // v3.0新增
  },
  // 支持的语言 (v3.0新增)
  SUPPORTED_LANGUAGES: API.SUPPORTED_LANGUAGES,
  // 文化偏好选项
  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'],
  // 风格偏好选项
  STYLE_PREFERENCES: STYLE_PREFERENCES,
  // 长度偏好选项
  LENGTH_PREFERENCES: ['short', 'medium', 'long']
});
// 缓存相关常量
export const CACHE =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[4]++, {
  // 默认缓存时间（秒）
  DEFAULT_TTL: 3600,
  // 最大缓存条目数
  MAX_ITEMS: 1000,
  // 缓存键前缀
  KEY_PREFIX: 'namer:'
});
// 性能相关常量
export const PERFORMANCE =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[5]++, {
  // 慢查询阈值（毫秒）
  SLOW_QUERY_THRESHOLD: 500,
  // 监控采样率（0-1）
  MONITORING_SAMPLE_RATE: 0.1
});
// 错误消息 (v3.0多语种扩展)
export const ERROR_MESSAGES =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[6]++, {
  VALIDATION: {
    INVALID_COUNT: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[0]++;
      cov_gpvvqkfzd().s[7]++;
      return `count must be a number between ${min} and ${max}`;
    },
    INVALID_CREATIVITY_LEVEL: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[1]++;
      cov_gpvvqkfzd().s[8]++;
      return `creativity_level must be a number between ${min} and ${max}`;
    },
    INVALID_QUALITY_THRESHOLD: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[2]++;
      cov_gpvvqkfzd().s[9]++;
      return `quality_threshold must be a number between ${min} and ${max}`;
    },
    INVALID_CULTURAL_PREFERENCE: validValues => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[3]++;
      cov_gpvvqkfzd().s[10]++;
      return `cultural_preference must be one of: ${validValues.join(', ')}`;
    },
    INVALID_STYLE_PREFERENCE: validValues => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[4]++;
      cov_gpvvqkfzd().s[11]++;
      return `style_preference must be one of: ${validValues.join(', ')}`;
    },
    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array',
    // v3.0多语种新增错误消息
    INVALID_LANGUAGE: validLanguages => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[5]++;
      cov_gpvvqkfzd().s[12]++;
      return `language must be one of: ${validLanguages.join(', ')}`;
    },
    INVALID_SEMANTIC_CONSISTENCY: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[6]++;
      cov_gpvvqkfzd().s[13]++;
      return `semantic_consistency must be a number between ${min} and ${max}`;
    },
    INVALID_CULTURAL_SENSITIVITY: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[7]++;
      cov_gpvvqkfzd().s[14]++;
      return `cultural_sensitivity must be a number between ${min} and ${max}`;
    },
    UNSUPPORTED_LANGUAGE_COMBINATION: 'The specified source and target language combination is not supported',
    INVALID_MULTILINGUAL_OPTIONS: 'Invalid multilingual options configuration',
    CONCEPT_PREFERENCES_INVALID: 'concept_preferences must be a valid object with supported categories'
  },
  INTERNAL: {
    GENERATION_FAILED: 'Failed to generate usernames',
    VALIDATION_FAILED: 'Request validation failed',
    INTERNAL_ERROR: 'Internal server error',
    // v3.0多语种新增内部错误
    MULTILINGUAL_ENGINE_ERROR: 'Multilingual generation engine error',
    CROSS_LINGUAL_MAPPING_FAILED: 'Cross-lingual concept mapping failed',
    SEMANTIC_ALIGNMENT_ERROR: 'Semantic alignment calculation error'
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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