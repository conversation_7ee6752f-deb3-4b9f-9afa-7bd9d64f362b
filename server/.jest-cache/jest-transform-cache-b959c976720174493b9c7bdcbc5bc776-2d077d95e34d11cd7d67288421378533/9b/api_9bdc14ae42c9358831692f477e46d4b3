5754e4d94201e2d6e49560ffb09e7ce7
/* istanbul ignore next */
function cov_qeof9c1nw() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/api.ts";
  var hash = "2f9b8a9709f10459dfa70cd4a2150c35ce9a1c68";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/api.ts",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 25,
          column: 34
        }
      },
      "1": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 46
        }
      },
      "2": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 50
        }
      },
      "3": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 42
        }
      },
      "4": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 45
        }
      },
      "5": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 45
        }
      },
      "6": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 43
        }
      },
      "7": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 48
        }
      },
      "8": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 50
        }
      },
      "9": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 42
        }
      },
      "10": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 47
        }
      },
      "11": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 35
        }
      },
      "12": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 49
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 9,
            column: 1
          },
          end: {
            line: 9,
            column: 2
          }
        },
        loc: {
          start: {
            line: 9,
            column: 22
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 9
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 12
          }
        }, {
          start: {
            line: 25,
            column: 17
          },
          end: {
            line: 25,
            column: 31
          }
        }],
        line: 25
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/api.ts",
      mappings: "AAAA;;;GAGG;AA6TH;;GAEG;AACH,MAAM,CAAN,IAAY,SAkBX;AAlBD,WAAY,SAAS;IACnB,mBAAmB;IACnB,yCAA4B,CAAA;IAC5B,6CAAgC,CAAA;IAChC,qCAAwB,CAAA;IACxB,wCAA2B,CAAA;IAE3B,mBAAmB;IACnB,wCAA2B,CAAA;IAC3B,sCAAyB,CAAA;IACzB,2CAA8B,CAAA;IAC9B,6CAAgC,CAAA;IAEhC,mBAAmB;IACnB,qCAAwB,CAAA;IACxB,0CAA6B,CAAA;IAC7B,8BAAiB,CAAA;IACjB,4CAA+B,CAAA;AACjC,CAAC,EAlBW,SAAS,KAAT,SAAS,QAkBpB",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/api.ts"],
      sourcesContent: ["/**\n * API\u63A5\u53E3\u7C7B\u578B\u5B9A\u4E49\n * \u5B9A\u4E49namer-v6\u9879\u76EE\u7684API\u8BF7\u6C42\u548C\u54CD\u5E94\u7ED3\u6784\n */\n\nimport type {\n  GeneratedUsername,\n  QualityScore,\n  CulturalContext,\n  StylePreference,\n  PerformanceMetrics,\n  CreativePattern,\n  LengthPreference\n} from './core'\nimport type {\n  LanguageCode,\n  ConceptCategory,\n  MultiDimensionalCulturalContext\n} from './multilingual'\n\n/**\n * \u7EDF\u4E00API\u54CD\u5E94\u683C\u5F0F\n */\nexport interface APIResponse<T = unknown> {\n  success: boolean\n  data?: T\n  error?: {\n    code: string\n    message: string\n    details?: Record<string, unknown>\n  }\n  meta: {\n    timestamp: number\n    request_id: string\n    execution_time: number\n    version: string\n  }\n}\n\n/**\n * \u7528\u6237\u540D\u751F\u6210\u8BF7\u6C42 (v3.0\u591A\u8BED\u79CD\u652F\u6301)\n */\nexport interface GenerateRequest {\n  // \u57FA\u7840\u53C2\u6570\n  count?: number                    // \u751F\u6210\u6570\u91CF\uFF0C\u9ED8\u8BA45\uFF0C\u6700\u592710 (v3.0\u7B80\u5316)\n  language?: LanguageCode           // \u76EE\u6807\u8BED\u8A00\uFF0C\u9ED8\u8BA4zh-CN\n\n  // \u6982\u5FF5\u504F\u597D (v3.0\u65B0\u589E)\n  concept_preferences?: {\n    categories?: ConceptCategory[]   // \u6982\u5FF5\u7C7B\u522B\u504F\u597D\n    cultural_context?: CulturalContext | MultiDimensionalCulturalContext\n    abstraction_level?: number      // [0-1] \u62BD\u8C61\u7A0B\u5EA6\n  }\n\n  // \u4F20\u7EDF\u504F\u597D\u8BBE\u7F6E (\u5411\u540E\u517C\u5BB9)\n  cultural_preference?: CulturalContext\n  style_preference?: StylePreference\n  length_preference?: LengthPreference\n  creativity_level?: number         // \u521B\u610F\u7A0B\u5EA6: 0.1-1.0\n  quality_threshold?: number        // \u8D28\u91CF\u9608\u503C: 0.1-1.0\n\n  // \u591A\u8BED\u79CD\u9009\u9879 (v3.0\u65B0\u589E)\n  multilingual_options?: {\n    enable_cross_lingual?: boolean  // \u542F\u7528\u8DE8\u8BED\u8A00\u751F\u6210\n    fallback_languages?: LanguageCode[]\n    maintain_semantic_consistency?: boolean\n    translation_preference?: 'literal' | 'semantic' | 'cultural'\n  }\n\n  // \u4E2A\u6027\u5316\u53C2\u6570\n  user_id?: string                  // \u7528\u6237ID\uFF0C\u7528\u4E8E\u4E2A\u6027\u5316\n  context?: {\n    occasion?: string               // \u4F7F\u7528\u573A\u5408\n    target_audience?: string        // \u76EE\u6807\u53D7\u4F17\n    keywords?: string[]             // \u5173\u952E\u8BCD\n    cultural_sensitivity?: number   // \u6587\u5316\u654F\u611F\u5EA6 [0-1]\n  }\n\n  // \u6A21\u5F0F\u63A7\u5236\n  patterns?: string[]               // \u6307\u5B9A\u521B\u610F\u6A21\u5F0F\n  exclude_patterns?: string[]       // \u6392\u9664\u7684\u6A21\u5F0F\n}\n\n/**\n * \u7528\u6237\u540D\u751F\u6210\u54CD\u5E94 (v3.0\u591A\u8BED\u79CD\u652F\u6301)\n */\nexport interface GenerateResponse {\n  usernames: GeneratedUsername[]\n  generation_time: number           // \u751F\u6210\u8017\u65F6(ms)\n  cache_hit: boolean               // \u662F\u5426\u547D\u4E2D\u7F13\u5B58\n\n  // v3.0\u591A\u8BED\u79CD\u4FE1\u606F\n  language: LanguageCode           // \u5B9E\u9645\u4F7F\u7528\u7684\u8BED\u8A00\n  semantic_consistency_score?: number // \u8BED\u4E49\u4E00\u81F4\u6027\u8BC4\u5206 [0-1]\n  cross_lingual_alternatives?: {   // \u8DE8\u8BED\u8A00\u66FF\u4EE3\u65B9\u6848\n    [key in LanguageCode]?: string[]\n  }\n\n  // \u4F20\u7EDF\u5B57\u6BB5 (\u5411\u540E\u517C\u5BB9)\n  recommendations?: string[]        // \u63A8\u8350\u7684\u76F8\u5173\u7528\u6237\u540D\n  patterns_used: string[]          // \u4F7F\u7528\u7684\u6A21\u5F0F\u5217\u8868\n  total_attempts: number           // \u603B\u5C1D\u8BD5\u6B21\u6570\n\n  // \u8D28\u91CF\u7EDF\u8BA1 (v3.0\u589E\u5F3A)\n  quality_distribution?: {\n    excellent: number              // \u4F18\u79C0\u8D28\u91CF\u6570\u91CF\n    good: number                   // \u826F\u597D\u8D28\u91CF\u6570\u91CF\n    acceptable: number             // \u53EF\u63A5\u53D7\u8D28\u91CF\u6570\u91CF\n  }\n}\n\n/**\n * \u6279\u91CF\u751F\u6210\u8BF7\u6C42\n */\nexport interface BatchGenerateRequest {\n  requests: GenerateRequest[]       // \u6279\u91CF\u8BF7\u6C42\n  parallel?: boolean               // \u662F\u5426\u5E76\u884C\u5904\u7406\uFF0C\u9ED8\u8BA4true\n  timeout?: number                 // \u8D85\u65F6\u65F6\u95F4(ms)\uFF0C\u9ED8\u8BA430000\n}\n\n/**\n * \u6279\u91CF\u751F\u6210\u54CD\u5E94\n */\nexport interface BatchGenerateResponse {\n  results: (GenerateResponse | null)[]  // \u7ED3\u679C\u6570\u7EC4\uFF0C\u5931\u8D25\u4E3Anull\n  success_count: number            // \u6210\u529F\u6570\u91CF\n  total_time: number              // \u603B\u8017\u65F6\n  errors?: BatchError[]           // \u9519\u8BEF\u4FE1\u606F\n}\n\n// ============================================================================\n// v3.0\u591A\u8BED\u79CD\u4E13\u7528API\u63A5\u53E3\n// ============================================================================\n\n/**\n * \u8BED\u8A00\u68C0\u6D4B\u8BF7\u6C42\n */\nexport interface LanguageDetectionRequest {\n  text: string                     // \u5F85\u68C0\u6D4B\u6587\u672C\n  confidence_threshold?: number    // \u7F6E\u4FE1\u5EA6\u9608\u503C [0-1]\n}\n\n/**\n * \u8BED\u8A00\u68C0\u6D4B\u54CD\u5E94\n */\nexport interface LanguageDetectionResponse {\n  detected_language: LanguageCode  // \u68C0\u6D4B\u5230\u7684\u8BED\u8A00\n  confidence: number               // \u68C0\u6D4B\u7F6E\u4FE1\u5EA6 [0-1]\n  alternatives?: {                 // \u5907\u9009\u8BED\u8A00\n    language: LanguageCode\n    confidence: number\n  }[]\n}\n\n/**\n * \u8DE8\u8BED\u8A00\u7FFB\u8BD1\u8BF7\u6C42\n */\nexport interface TranslationRequest {\n  username: string                 // \u6E90\u7528\u6237\u540D\n  source_language: LanguageCode    // \u6E90\u8BED\u8A00\n  target_language: LanguageCode    // \u76EE\u6807\u8BED\u8A00\n  preserve_meaning?: boolean       // \u4FDD\u6301\u8BED\u4E49\uFF0C\u9ED8\u8BA4true\n  preserve_style?: boolean         // \u4FDD\u6301\u98CE\u683C\uFF0C\u9ED8\u8BA4false\n  cultural_adaptation?: boolean    // \u6587\u5316\u9002\u914D\uFF0C\u9ED8\u8BA4true\n}\n\n/**\n * \u8DE8\u8BED\u8A00\u7FFB\u8BD1\u54CD\u5E94\n */\nexport interface TranslationResponse {\n  translated_username: string     // \u7FFB\u8BD1\u7ED3\u679C\n  confidence: number              // \u7FFB\u8BD1\u7F6E\u4FE1\u5EA6 [0-1]\n  alternatives: string[]          // \u5907\u9009\u7FFB\u8BD1\n  semantic_similarity: number     // \u8BED\u4E49\u76F8\u4F3C\u5EA6 [0-1]\n  cultural_adaptation_score: number // \u6587\u5316\u9002\u914D\u8BC4\u5206 [0-1]\n  explanation?: string            // \u7FFB\u8BD1\u8BF4\u660E\n}\n\n/**\n * \u6982\u5FF5\u67E5\u8BE2\u8BF7\u6C42\n */\nexport interface ConceptQueryRequest {\n  query: string                   // \u67E5\u8BE2\u5173\u952E\u8BCD\n  concept_id?: string             // \u6982\u5FF5ID\n  category?: ConceptCategory      // \u6982\u5FF5\u7C7B\u522B\n  language?: LanguageCode         // \u67E5\u8BE2\u8BED\u8A00\n  limit?: number                  // \u7ED3\u679C\u6570\u91CF\u9650\u5236\n  include_related?: boolean       // \u5305\u542B\u76F8\u5173\u6982\u5FF5\n}\n\n/**\n * \u6982\u5FF5\u67E5\u8BE2\u54CD\u5E94\n */\nexport interface ConceptQueryResponse {\n  concepts: {\n    concept_id: string\n    category: ConceptCategory\n    abstract_meaning: string\n    language_implementations: {\n      [key in LanguageCode]?: {\n        text: string\n        alternatives: string[]\n        cultural_context: MultiDimensionalCulturalContext\n      }\n    }\n  }[]\n  total_count: number\n}\n\n/**\n * \u6279\u91CF\u9519\u8BEF\u4FE1\u606F\n */\nexport interface BatchError {\n  index: number\n  error: {\n    code: string\n    message: string\n    details?: Record<string, unknown>\n  }\n}\n\n/**\n * \u8D28\u91CF\u8BC4\u4F30\u8BF7\u6C42\n */\nexport interface EvaluateRequest {\n  usernames: string[]              // \u5F85\u8BC4\u4F30\u7684\u7528\u6237\u540D\n  language?: string                // \u8BED\u8A00\u4EE3\u7801\n  cultural_context?: CulturalContext\n  detailed?: boolean               // \u662F\u5426\u8FD4\u56DE\u8BE6\u7EC6\u8BC4\u4F30\n}\n\n/**\n * \u8D28\u91CF\u8BC4\u4F30\u54CD\u5E94\n */\nexport interface EvaluateResponse {\n  evaluations: UsernameEvaluation[]\n  average_score: number\n  evaluation_time: number\n  summary: {\n    excellent_count: number        // \u4F18\u79C0\u6570\u91CF (>0.8)\n    good_count: number            // \u826F\u597D\u6570\u91CF (0.6-0.8)\n    average_count: number         // \u4E00\u822C\u6570\u91CF (0.4-0.6)\n    poor_count: number            // \u8F83\u5DEE\u6570\u91CF (<0.4)\n  }\n}\n\n/**\n * \u7528\u6237\u540D\u8BC4\u4F30\u7ED3\u679C\n */\nexport interface UsernameEvaluation {\n  username: string\n  overall_score: number            // \u603B\u4F53\u8BC4\u5206 0-1\n  quality_score: QualityScore\n  suggestions?: string[]           // \u6539\u8FDB\u5EFA\u8BAE\n  similar_usernames?: string[]     // \u76F8\u4F3C\u7528\u6237\u540D\u63A8\u8350\n}\n\n/**\n * \u5065\u5EB7\u68C0\u67E5\u54CD\u5E94\n */\nexport interface HealthResponse {\n  status: 'healthy' | 'degraded' | 'unhealthy'\n  timestamp: number\n  version: string\n  uptime: number\n  checks: {\n    memory: HealthCheck\n    cache: HealthCheck\n    data_integrity: HealthCheck\n    performance: HealthCheck\n  }\n}\n\n/**\n * \u5065\u5EB7\u68C0\u67E5\u8BE6\u60C5\n */\nexport interface HealthCheckDetails {\n  [key: string]: string | number | boolean | null | undefined | HealthCheckDetails | HealthCheckDetails[]\n}\n\n/**\n * \u5065\u5EB7\u68C0\u67E5\u9879\n */\nexport interface HealthCheck {\n  status: 'pass' | 'fail' | 'warn'\n  response_time?: number\n  details?: HealthCheckDetails\n  message?: string\n}\n\n/**\n * \u6027\u80FD\u6307\u6807\u54CD\u5E94\n */\nexport interface MetricsResponse {\n  performance: PerformanceMetrics\n  system_info: {\n    node_version: string\n    memory_usage: NodeJS.MemoryUsage\n    uptime: number\n    platform: string\n  }\n  api_stats: {\n    total_requests: number\n    successful_requests: number\n    failed_requests: number\n    avg_response_time: number\n  }\n}\n\n/**\n * \u521B\u610F\u6A21\u5F0F\u5217\u8868\u54CD\u5E94\n */\nexport interface PatternsResponse {\n  patterns: CreativePattern[]\n  total_count: number\n  categories: {\n    [category: string]: number\n  }\n}\n\n/**\n * \u9519\u8BEF\u4EE3\u7801\u679A\u4E3E\n */\nexport enum ErrorCode {\n  // \u901A\u7528\u9519\u8BEF (1000-1999)\n  INVALID_PARAMETERS = 'E1001',\n  MISSING_REQUIRED_FIELD = 'E1002',\n  INVALID_FORMAT = 'E1003',\n  VALIDATION_FAILED = 'E1004',\n\n  // \u4E1A\u52A1\u9519\u8BEF (3000-3999)\n  GENERATION_FAILED = 'E3001',\n  QUALITY_TOO_LOW = 'E3002',\n  NO_SUITABLE_PATTERNS = 'E3003',\n  INSUFFICIENT_MORPHEMES = 'E3004',\n\n  // \u7CFB\u7EDF\u9519\u8BEF (5000-5999)\n  INTERNAL_ERROR = 'E5001',\n  SERVICE_UNAVAILABLE = 'E5002',\n  TIMEOUT = 'E5003',\n  MEMORY_LIMIT_EXCEEDED = 'E5004'\n}\n\n/**\n * \u8BF7\u6C42\u9A8C\u8BC1\u89C4\u5219\n */\nexport type ValidationValue = string | number | boolean | null | undefined\n\nexport interface ValidationRule {\n  field: string\n  type: 'string' | 'number' | 'boolean' | 'array' | 'object'\n  required?: boolean\n  min?: number\n  max?: number\n  pattern?: RegExp\n  enum?: ValidationValue[]\n  custom?: (value: unknown) => boolean | string\n}\n\n/**\n * \u5206\u9875\u53C2\u6570\n */\nexport interface PaginationParams {\n  page?: number\n  limit?: number\n  sort?: string\n  order?: 'asc' | 'desc'\n}\n\n/**\n * \u5206\u9875\u54CD\u5E94\n */\nexport interface PaginatedResponse<T> {\n  data: T[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n    has_next: boolean\n    has_prev: boolean\n  }\n}"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2f9b8a9709f10459dfa70cd4a2150c35ce9a1c68"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_qeof9c1nw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_qeof9c1nw();
/**
 * API接口类型定义
 * 定义namer-v6项目的API请求和响应结构
 */
/**
 * 错误代码枚举
 */
export var ErrorCode;
/* istanbul ignore next */
cov_qeof9c1nw().s[0]++;
(function (ErrorCode) {
  /* istanbul ignore next */
  cov_qeof9c1nw().f[0]++;
  cov_qeof9c1nw().s[1]++;
  // 通用错误 (1000-1999)
  ErrorCode["INVALID_PARAMETERS"] = "E1001";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[2]++;
  ErrorCode["MISSING_REQUIRED_FIELD"] = "E1002";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[3]++;
  ErrorCode["INVALID_FORMAT"] = "E1003";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[4]++;
  ErrorCode["VALIDATION_FAILED"] = "E1004";
  // 业务错误 (3000-3999)
  /* istanbul ignore next */
  cov_qeof9c1nw().s[5]++;
  ErrorCode["GENERATION_FAILED"] = "E3001";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[6]++;
  ErrorCode["QUALITY_TOO_LOW"] = "E3002";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[7]++;
  ErrorCode["NO_SUITABLE_PATTERNS"] = "E3003";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[8]++;
  ErrorCode["INSUFFICIENT_MORPHEMES"] = "E3004";
  // 系统错误 (5000-5999)
  /* istanbul ignore next */
  cov_qeof9c1nw().s[9]++;
  ErrorCode["INTERNAL_ERROR"] = "E5001";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[10]++;
  ErrorCode["SERVICE_UNAVAILABLE"] = "E5002";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[11]++;
  ErrorCode["TIMEOUT"] = "E5003";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[12]++;
  ErrorCode["MEMORY_LIMIT_EXCEEDED"] = "E5004";
})(
/* istanbul ignore next */
(cov_qeof9c1nw().b[0][0]++, ErrorCode) ||
/* istanbul ignore next */
(cov_qeof9c1nw().b[0][1]++, ErrorCode = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfcWVvZjljMW53IiwiYWN0dWFsQ292ZXJhZ2UiLCJFcnJvckNvZGUiLCJzIiwiZiIsImIiXSwic291cmNlcyI6WyIvaG9tZS9wL2RldmVsb3Avd29ya3NwYWNlL25hbWVyLXY2L3NlcnZlci90eXBlcy9hcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBUEnmjqXlj6PnsbvlnovlrprkuYlcbiAqIOWumuS5iW5hbWVyLXY26aG555uu55qEQVBJ6K+35rGC5ZKM5ZON5bqU57uT5p6EXG4gKi9cblxuaW1wb3J0IHR5cGUge1xuICBHZW5lcmF0ZWRVc2VybmFtZSxcbiAgUXVhbGl0eVNjb3JlLFxuICBDdWx0dXJhbENvbnRleHQsXG4gIFN0eWxlUHJlZmVyZW5jZSxcbiAgUGVyZm9ybWFuY2VNZXRyaWNzLFxuICBDcmVhdGl2ZVBhdHRlcm4sXG4gIExlbmd0aFByZWZlcmVuY2Vcbn0gZnJvbSAnLi9jb3JlJ1xuaW1wb3J0IHR5cGUge1xuICBMYW5ndWFnZUNvZGUsXG4gIENvbmNlcHRDYXRlZ29yeSxcbiAgTXVsdGlEaW1lbnNpb25hbEN1bHR1cmFsQ29udGV4dFxufSBmcm9tICcuL211bHRpbGluZ3VhbCdcblxuLyoqXG4gKiDnu5/kuIBBUEnlk43lupTmoLzlvI9cbiAqL1xuZXhwb3J0IGludGVyZmFjZSBBUElSZXNwb25zZTxUID0gdW5rbm93bj4ge1xuICBzdWNjZXNzOiBib29sZWFuXG4gIGRhdGE/OiBUXG4gIGVycm9yPzoge1xuICAgIGNvZGU6IHN0cmluZ1xuICAgIG1lc3NhZ2U6IHN0cmluZ1xuICAgIGRldGFpbHM/OiBSZWNvcmQ8c3RyaW5nLCB1bmtub3duPlxuICB9XG4gIG1ldGE6IHtcbiAgICB0aW1lc3RhbXA6IG51bWJlclxuICAgIHJlcXVlc3RfaWQ6IHN0cmluZ1xuICAgIGV4ZWN1dGlvbl90aW1lOiBudW1iZXJcbiAgICB2ZXJzaW9uOiBzdHJpbmdcbiAgfVxufVxuXG4vKipcbiAqIOeUqOaIt+WQjeeUn+aIkOivt+axgiAodjMuMOWkmuivreenjeaUr+aMgSlcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBHZW5lcmF0ZVJlcXVlc3Qge1xuICAvLyDln7rnoYDlj4LmlbBcbiAgY291bnQ/OiBudW1iZXIgICAgICAgICAgICAgICAgICAgIC8vIOeUn+aIkOaVsOmHj++8jOm7mOiupDXvvIzmnIDlpKcxMCAodjMuMOeugOWMlilcbiAgbGFuZ3VhZ2U/OiBMYW5ndWFnZUNvZGUgICAgICAgICAgIC8vIOebruagh+ivreiogO+8jOm7mOiupHpoLUNOXG5cbiAgLy8g5qaC5b+15YGP5aW9ICh2My4w5paw5aKeKVxuICBjb25jZXB0X3ByZWZlcmVuY2VzPzoge1xuICAgIGNhdGVnb3JpZXM/OiBDb25jZXB0Q2F0ZWdvcnlbXSAgIC8vIOamguW/teexu+WIq+WBj+WlvVxuICAgIGN1bHR1cmFsX2NvbnRleHQ/OiBDdWx0dXJhbENvbnRleHQgfCBNdWx0aURpbWVuc2lvbmFsQ3VsdHVyYWxDb250ZXh0XG4gICAgYWJzdHJhY3Rpb25fbGV2ZWw/OiBudW1iZXIgICAgICAvLyBbMC0xXSDmir3osaHnqIvluqZcbiAgfVxuXG4gIC8vIOS8oOe7n+WBj+Wlveiuvue9riAo5ZCR5ZCO5YW85a65KVxuICBjdWx0dXJhbF9wcmVmZXJlbmNlPzogQ3VsdHVyYWxDb250ZXh0XG4gIHN0eWxlX3ByZWZlcmVuY2U/OiBTdHlsZVByZWZlcmVuY2VcbiAgbGVuZ3RoX3ByZWZlcmVuY2U/OiBMZW5ndGhQcmVmZXJlbmNlXG4gIGNyZWF0aXZpdHlfbGV2ZWw/OiBudW1iZXIgICAgICAgICAvLyDliJvmhI/nqIvluqY6IDAuMS0xLjBcbiAgcXVhbGl0eV90aHJlc2hvbGQ/OiBudW1iZXIgICAgICAgIC8vIOi0qOmHj+mYiOWAvDogMC4xLTEuMFxuXG4gIC8vIOWkmuivreenjemAiemhuSAodjMuMOaWsOWinilcbiAgbXVsdGlsaW5ndWFsX29wdGlvbnM/OiB7XG4gICAgZW5hYmxlX2Nyb3NzX2xpbmd1YWw/OiBib29sZWFuICAvLyDlkK/nlKjot6jor63oqIDnlJ/miJBcbiAgICBmYWxsYmFja19sYW5ndWFnZXM/OiBMYW5ndWFnZUNvZGVbXVxuICAgIG1haW50YWluX3NlbWFudGljX2NvbnNpc3RlbmN5PzogYm9vbGVhblxuICAgIHRyYW5zbGF0aW9uX3ByZWZlcmVuY2U/OiAnbGl0ZXJhbCcgfCAnc2VtYW50aWMnIHwgJ2N1bHR1cmFsJ1xuICB9XG5cbiAgLy8g5Liq5oCn5YyW5Y+C5pWwXG4gIHVzZXJfaWQ/OiBzdHJpbmcgICAgICAgICAgICAgICAgICAvLyDnlKjmiLdJRO+8jOeUqOS6juS4quaAp+WMllxuICBjb250ZXh0Pzoge1xuICAgIG9jY2FzaW9uPzogc3RyaW5nICAgICAgICAgICAgICAgLy8g5L2/55So5Zy65ZCIXG4gICAgdGFyZ2V0X2F1ZGllbmNlPzogc3RyaW5nICAgICAgICAvLyDnm67moIflj5fkvJdcbiAgICBrZXl3b3Jkcz86IHN0cmluZ1tdICAgICAgICAgICAgIC8vIOWFs+mUruivjVxuICAgIGN1bHR1cmFsX3NlbnNpdGl2aXR5PzogbnVtYmVyICAgLy8g5paH5YyW5pWP5oSf5bqmIFswLTFdXG4gIH1cblxuICAvLyDmqKHlvI/mjqfliLZcbiAgcGF0dGVybnM/OiBzdHJpbmdbXSAgICAgICAgICAgICAgIC8vIOaMh+WumuWIm+aEj+aooeW8j1xuICBleGNsdWRlX3BhdHRlcm5zPzogc3RyaW5nW10gICAgICAgLy8g5o6S6Zmk55qE5qih5byPXG59XG5cbi8qKlxuICog55So5oi35ZCN55Sf5oiQ5ZON5bqUICh2My4w5aSa6K+t56eN5pSv5oyBKVxuICovXG5leHBvcnQgaW50ZXJmYWNlIEdlbmVyYXRlUmVzcG9uc2Uge1xuICB1c2VybmFtZXM6IEdlbmVyYXRlZFVzZXJuYW1lW11cbiAgZ2VuZXJhdGlvbl90aW1lOiBudW1iZXIgICAgICAgICAgIC8vIOeUn+aIkOiAl+aXtihtcylcbiAgY2FjaGVfaGl0OiBib29sZWFuICAgICAgICAgICAgICAgLy8g5piv5ZCm5ZG95Lit57yT5a2YXG5cbiAgLy8gdjMuMOWkmuivreenjeS/oeaBr1xuICBsYW5ndWFnZTogTGFuZ3VhZ2VDb2RlICAgICAgICAgICAvLyDlrp7pmYXkvb/nlKjnmoTor63oqIBcbiAgc2VtYW50aWNfY29uc2lzdGVuY3lfc2NvcmU/OiBudW1iZXIgLy8g6K+t5LmJ5LiA6Ie05oCn6K+E5YiGIFswLTFdXG4gIGNyb3NzX2xpbmd1YWxfYWx0ZXJuYXRpdmVzPzogeyAgIC8vIOi3qOivreiogOabv+S7o+aWueahiFxuICAgIFtrZXkgaW4gTGFuZ3VhZ2VDb2RlXT86IHN0cmluZ1tdXG4gIH1cblxuICAvLyDkvKDnu5/lrZfmrrUgKOWQkeWQjuWFvOWuuSlcbiAgcmVjb21tZW5kYXRpb25zPzogc3RyaW5nW10gICAgICAgIC8vIOaOqOiNkOeahOebuOWFs+eUqOaIt+WQjVxuICBwYXR0ZXJuc191c2VkOiBzdHJpbmdbXSAgICAgICAgICAvLyDkvb/nlKjnmoTmqKHlvI/liJfooahcbiAgdG90YWxfYXR0ZW1wdHM6IG51bWJlciAgICAgICAgICAgLy8g5oC75bCd6K+V5qyh5pWwXG5cbiAgLy8g6LSo6YeP57uf6K6hICh2My4w5aKe5by6KVxuICBxdWFsaXR5X2Rpc3RyaWJ1dGlvbj86IHtcbiAgICBleGNlbGxlbnQ6IG51bWJlciAgICAgICAgICAgICAgLy8g5LyY56eA6LSo6YeP5pWw6YePXG4gICAgZ29vZDogbnVtYmVyICAgICAgICAgICAgICAgICAgIC8vIOiJr+Wlvei0qOmHj+aVsOmHj1xuICAgIGFjY2VwdGFibGU6IG51bWJlciAgICAgICAgICAgICAvLyDlj6/mjqXlj5fotKjph4/mlbDph49cbiAgfVxufVxuXG4vKipcbiAqIOaJuemHj+eUn+aIkOivt+axglxuICovXG5leHBvcnQgaW50ZXJmYWNlIEJhdGNoR2VuZXJhdGVSZXF1ZXN0IHtcbiAgcmVxdWVzdHM6IEdlbmVyYXRlUmVxdWVzdFtdICAgICAgIC8vIOaJuemHj+ivt+axglxuICBwYXJhbGxlbD86IGJvb2xlYW4gICAgICAgICAgICAgICAvLyDmmK/lkKblubbooYzlpITnkIbvvIzpu5jorqR0cnVlXG4gIHRpbWVvdXQ/OiBudW1iZXIgICAgICAgICAgICAgICAgIC8vIOi2heaXtuaXtumXtChtcynvvIzpu5jorqQzMDAwMFxufVxuXG4vKipcbiAqIOaJuemHj+eUn+aIkOWTjeW6lFxuICovXG5leHBvcnQgaW50ZXJmYWNlIEJhdGNoR2VuZXJhdGVSZXNwb25zZSB7XG4gIHJlc3VsdHM6IChHZW5lcmF0ZVJlc3BvbnNlIHwgbnVsbClbXSAgLy8g57uT5p6c5pWw57uE77yM5aSx6LSl5Li6bnVsbFxuICBzdWNjZXNzX2NvdW50OiBudW1iZXIgICAgICAgICAgICAvLyDmiJDlip/mlbDph49cbiAgdG90YWxfdGltZTogbnVtYmVyICAgICAgICAgICAgICAvLyDmgLvogJfml7ZcbiAgZXJyb3JzPzogQmF0Y2hFcnJvcltdICAgICAgICAgICAvLyDplJnor6/kv6Hmga9cbn1cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gdjMuMOWkmuivreenjeS4k+eUqEFQSeaOpeWPo1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4vKipcbiAqIOivreiogOajgOa1i+ivt+axglxuICovXG5leHBvcnQgaW50ZXJmYWNlIExhbmd1YWdlRGV0ZWN0aW9uUmVxdWVzdCB7XG4gIHRleHQ6IHN0cmluZyAgICAgICAgICAgICAgICAgICAgIC8vIOW+heajgOa1i+aWh+acrFxuICBjb25maWRlbmNlX3RocmVzaG9sZD86IG51bWJlciAgICAvLyDnva7kv6HluqbpmIjlgLwgWzAtMV1cbn1cblxuLyoqXG4gKiDor63oqIDmo4DmtYvlk43lupRcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBMYW5ndWFnZURldGVjdGlvblJlc3BvbnNlIHtcbiAgZGV0ZWN0ZWRfbGFuZ3VhZ2U6IExhbmd1YWdlQ29kZSAgLy8g5qOA5rWL5Yiw55qE6K+t6KiAXG4gIGNvbmZpZGVuY2U6IG51bWJlciAgICAgICAgICAgICAgIC8vIOajgOa1i+e9ruS/oeW6piBbMC0xXVxuICBhbHRlcm5hdGl2ZXM/OiB7ICAgICAgICAgICAgICAgICAvLyDlpIfpgInor63oqIBcbiAgICBsYW5ndWFnZTogTGFuZ3VhZ2VDb2RlXG4gICAgY29uZmlkZW5jZTogbnVtYmVyXG4gIH1bXVxufVxuXG4vKipcbiAqIOi3qOivreiogOe/u+ivkeivt+axglxuICovXG5leHBvcnQgaW50ZXJmYWNlIFRyYW5zbGF0aW9uUmVxdWVzdCB7XG4gIHVzZXJuYW1lOiBzdHJpbmcgICAgICAgICAgICAgICAgIC8vIOa6kOeUqOaIt+WQjVxuICBzb3VyY2VfbGFuZ3VhZ2U6IExhbmd1YWdlQ29kZSAgICAvLyDmupDor63oqIBcbiAgdGFyZ2V0X2xhbmd1YWdlOiBMYW5ndWFnZUNvZGUgICAgLy8g55uu5qCH6K+t6KiAXG4gIHByZXNlcnZlX21lYW5pbmc/OiBib29sZWFuICAgICAgIC8vIOS/neaMgeivreS5ie+8jOm7mOiupHRydWVcbiAgcHJlc2VydmVfc3R5bGU/OiBib29sZWFuICAgICAgICAgLy8g5L+d5oyB6aOO5qC877yM6buY6K6kZmFsc2VcbiAgY3VsdHVyYWxfYWRhcHRhdGlvbj86IGJvb2xlYW4gICAgLy8g5paH5YyW6YCC6YWN77yM6buY6K6kdHJ1ZVxufVxuXG4vKipcbiAqIOi3qOivreiogOe/u+ivkeWTjeW6lFxuICovXG5leHBvcnQgaW50ZXJmYWNlIFRyYW5zbGF0aW9uUmVzcG9uc2Uge1xuICB0cmFuc2xhdGVkX3VzZXJuYW1lOiBzdHJpbmcgICAgIC8vIOe/u+ivkee7k+aenFxuICBjb25maWRlbmNlOiBudW1iZXIgICAgICAgICAgICAgIC8vIOe/u+ivkee9ruS/oeW6piBbMC0xXVxuICBhbHRlcm5hdGl2ZXM6IHN0cmluZ1tdICAgICAgICAgIC8vIOWkh+mAiee/u+ivkVxuICBzZW1hbnRpY19zaW1pbGFyaXR5OiBudW1iZXIgICAgIC8vIOivreS5ieebuOS8vOW6piBbMC0xXVxuICBjdWx0dXJhbF9hZGFwdGF0aW9uX3Njb3JlOiBudW1iZXIgLy8g5paH5YyW6YCC6YWN6K+E5YiGIFswLTFdXG4gIGV4cGxhbmF0aW9uPzogc3RyaW5nICAgICAgICAgICAgLy8g57+76K+R6K+05piOXG59XG5cbi8qKlxuICog5qaC5b+15p+l6K+i6K+35rGCXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgQ29uY2VwdFF1ZXJ5UmVxdWVzdCB7XG4gIHF1ZXJ5OiBzdHJpbmcgICAgICAgICAgICAgICAgICAgLy8g5p+l6K+i5YWz6ZSu6K+NXG4gIGNvbmNlcHRfaWQ/OiBzdHJpbmcgICAgICAgICAgICAgLy8g5qaC5b+1SURcbiAgY2F0ZWdvcnk/OiBDb25jZXB0Q2F0ZWdvcnkgICAgICAvLyDmpoLlv7XnsbvliKtcbiAgbGFuZ3VhZ2U/OiBMYW5ndWFnZUNvZGUgICAgICAgICAvLyDmn6Xor6Lor63oqIBcbiAgbGltaXQ/OiBudW1iZXIgICAgICAgICAgICAgICAgICAvLyDnu5PmnpzmlbDph4/pmZDliLZcbiAgaW5jbHVkZV9yZWxhdGVkPzogYm9vbGVhbiAgICAgICAvLyDljIXlkKvnm7jlhbPmpoLlv7Vcbn1cblxuLyoqXG4gKiDmpoLlv7Xmn6Xor6Llk43lupRcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBDb25jZXB0UXVlcnlSZXNwb25zZSB7XG4gIGNvbmNlcHRzOiB7XG4gICAgY29uY2VwdF9pZDogc3RyaW5nXG4gICAgY2F0ZWdvcnk6IENvbmNlcHRDYXRlZ29yeVxuICAgIGFic3RyYWN0X21lYW5pbmc6IHN0cmluZ1xuICAgIGxhbmd1YWdlX2ltcGxlbWVudGF0aW9uczoge1xuICAgICAgW2tleSBpbiBMYW5ndWFnZUNvZGVdPzoge1xuICAgICAgICB0ZXh0OiBzdHJpbmdcbiAgICAgICAgYWx0ZXJuYXRpdmVzOiBzdHJpbmdbXVxuICAgICAgICBjdWx0dXJhbF9jb250ZXh0OiBNdWx0aURpbWVuc2lvbmFsQ3VsdHVyYWxDb250ZXh0XG4gICAgICB9XG4gICAgfVxuICB9W11cbiAgdG90YWxfY291bnQ6IG51bWJlclxufVxuXG4vKipcbiAqIOaJuemHj+mUmeivr+S/oeaBr1xuICovXG5leHBvcnQgaW50ZXJmYWNlIEJhdGNoRXJyb3Ige1xuICBpbmRleDogbnVtYmVyXG4gIGVycm9yOiB7XG4gICAgY29kZTogc3RyaW5nXG4gICAgbWVzc2FnZTogc3RyaW5nXG4gICAgZGV0YWlscz86IFJlY29yZDxzdHJpbmcsIHVua25vd24+XG4gIH1cbn1cblxuLyoqXG4gKiDotKjph4/or4TkvLDor7fmsYJcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBFdmFsdWF0ZVJlcXVlc3Qge1xuICB1c2VybmFtZXM6IHN0cmluZ1tdICAgICAgICAgICAgICAvLyDlvoXor4TkvLDnmoTnlKjmiLflkI1cbiAgbGFuZ3VhZ2U/OiBzdHJpbmcgICAgICAgICAgICAgICAgLy8g6K+t6KiA5Luj56CBXG4gIGN1bHR1cmFsX2NvbnRleHQ/OiBDdWx0dXJhbENvbnRleHRcbiAgZGV0YWlsZWQ/OiBib29sZWFuICAgICAgICAgICAgICAgLy8g5piv5ZCm6L+U5Zue6K+m57uG6K+E5LywXG59XG5cbi8qKlxuICog6LSo6YeP6K+E5Lyw5ZON5bqUXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgRXZhbHVhdGVSZXNwb25zZSB7XG4gIGV2YWx1YXRpb25zOiBVc2VybmFtZUV2YWx1YXRpb25bXVxuICBhdmVyYWdlX3Njb3JlOiBudW1iZXJcbiAgZXZhbHVhdGlvbl90aW1lOiBudW1iZXJcbiAgc3VtbWFyeToge1xuICAgIGV4Y2VsbGVudF9jb3VudDogbnVtYmVyICAgICAgICAvLyDkvJjnp4DmlbDph48gKD4wLjgpXG4gICAgZ29vZF9jb3VudDogbnVtYmVyICAgICAgICAgICAgLy8g6Imv5aW95pWw6YePICgwLjYtMC44KVxuICAgIGF2ZXJhZ2VfY291bnQ6IG51bWJlciAgICAgICAgIC8vIOS4gOiIrOaVsOmHjyAoMC40LTAuNilcbiAgICBwb29yX2NvdW50OiBudW1iZXIgICAgICAgICAgICAvLyDovoPlt67mlbDph48gKDwwLjQpXG4gIH1cbn1cblxuLyoqXG4gKiDnlKjmiLflkI3or4TkvLDnu5PmnpxcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBVc2VybmFtZUV2YWx1YXRpb24ge1xuICB1c2VybmFtZTogc3RyaW5nXG4gIG92ZXJhbGxfc2NvcmU6IG51bWJlciAgICAgICAgICAgIC8vIOaAu+S9k+ivhOWIhiAwLTFcbiAgcXVhbGl0eV9zY29yZTogUXVhbGl0eVNjb3JlXG4gIHN1Z2dlc3Rpb25zPzogc3RyaW5nW10gICAgICAgICAgIC8vIOaUuei/m+W7uuiurlxuICBzaW1pbGFyX3VzZXJuYW1lcz86IHN0cmluZ1tdICAgICAvLyDnm7jkvLznlKjmiLflkI3mjqjojZBcbn1cblxuLyoqXG4gKiDlgaXlurfmo4Dmn6Xlk43lupRcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBIZWFsdGhSZXNwb25zZSB7XG4gIHN0YXR1czogJ2hlYWx0aHknIHwgJ2RlZ3JhZGVkJyB8ICd1bmhlYWx0aHknXG4gIHRpbWVzdGFtcDogbnVtYmVyXG4gIHZlcnNpb246IHN0cmluZ1xuICB1cHRpbWU6IG51bWJlclxuICBjaGVja3M6IHtcbiAgICBtZW1vcnk6IEhlYWx0aENoZWNrXG4gICAgY2FjaGU6IEhlYWx0aENoZWNrXG4gICAgZGF0YV9pbnRlZ3JpdHk6IEhlYWx0aENoZWNrXG4gICAgcGVyZm9ybWFuY2U6IEhlYWx0aENoZWNrXG4gIH1cbn1cblxuLyoqXG4gKiDlgaXlurfmo4Dmn6Xor6bmg4VcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBIZWFsdGhDaGVja0RldGFpbHMge1xuICBba2V5OiBzdHJpbmddOiBzdHJpbmcgfCBudW1iZXIgfCBib29sZWFuIHwgbnVsbCB8IHVuZGVmaW5lZCB8IEhlYWx0aENoZWNrRGV0YWlscyB8IEhlYWx0aENoZWNrRGV0YWlsc1tdXG59XG5cbi8qKlxuICog5YGl5bq35qOA5p+l6aG5XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgSGVhbHRoQ2hlY2sge1xuICBzdGF0dXM6ICdwYXNzJyB8ICdmYWlsJyB8ICd3YXJuJ1xuICByZXNwb25zZV90aW1lPzogbnVtYmVyXG4gIGRldGFpbHM/OiBIZWFsdGhDaGVja0RldGFpbHNcbiAgbWVzc2FnZT86IHN0cmluZ1xufVxuXG4vKipcbiAqIOaAp+iDveaMh+agh+WTjeW6lFxuICovXG5leHBvcnQgaW50ZXJmYWNlIE1ldHJpY3NSZXNwb25zZSB7XG4gIHBlcmZvcm1hbmNlOiBQZXJmb3JtYW5jZU1ldHJpY3NcbiAgc3lzdGVtX2luZm86IHtcbiAgICBub2RlX3ZlcnNpb246IHN0cmluZ1xuICAgIG1lbW9yeV91c2FnZTogTm9kZUpTLk1lbW9yeVVzYWdlXG4gICAgdXB0aW1lOiBudW1iZXJcbiAgICBwbGF0Zm9ybTogc3RyaW5nXG4gIH1cbiAgYXBpX3N0YXRzOiB7XG4gICAgdG90YWxfcmVxdWVzdHM6IG51bWJlclxuICAgIHN1Y2Nlc3NmdWxfcmVxdWVzdHM6IG51bWJlclxuICAgIGZhaWxlZF9yZXF1ZXN0czogbnVtYmVyXG4gICAgYXZnX3Jlc3BvbnNlX3RpbWU6IG51bWJlclxuICB9XG59XG5cbi8qKlxuICog5Yib5oSP5qih5byP5YiX6KGo5ZON5bqUXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgUGF0dGVybnNSZXNwb25zZSB7XG4gIHBhdHRlcm5zOiBDcmVhdGl2ZVBhdHRlcm5bXVxuICB0b3RhbF9jb3VudDogbnVtYmVyXG4gIGNhdGVnb3JpZXM6IHtcbiAgICBbY2F0ZWdvcnk6IHN0cmluZ106IG51bWJlclxuICB9XG59XG5cbi8qKlxuICog6ZSZ6K+v5Luj56CB5p6a5Li+XG4gKi9cbmV4cG9ydCBlbnVtIEVycm9yQ29kZSB7XG4gIC8vIOmAmueUqOmUmeivryAoMTAwMC0xOTk5KVxuICBJTlZBTElEX1BBUkFNRVRFUlMgPSAnRTEwMDEnLFxuICBNSVNTSU5HX1JFUVVJUkVEX0ZJRUxEID0gJ0UxMDAyJyxcbiAgSU5WQUxJRF9GT1JNQVQgPSAnRTEwMDMnLFxuICBWQUxJREFUSU9OX0ZBSUxFRCA9ICdFMTAwNCcsXG5cbiAgLy8g5Lia5Yqh6ZSZ6K+vICgzMDAwLTM5OTkpXG4gIEdFTkVSQVRJT05fRkFJTEVEID0gJ0UzMDAxJyxcbiAgUVVBTElUWV9UT09fTE9XID0gJ0UzMDAyJyxcbiAgTk9fU1VJVEFCTEVfUEFUVEVSTlMgPSAnRTMwMDMnLFxuICBJTlNVRkZJQ0lFTlRfTU9SUEhFTUVTID0gJ0UzMDA0JyxcblxuICAvLyDns7vnu5/plJnor68gKDUwMDAtNTk5OSlcbiAgSU5URVJOQUxfRVJST1IgPSAnRTUwMDEnLFxuICBTRVJWSUNFX1VOQVZBSUxBQkxFID0gJ0U1MDAyJyxcbiAgVElNRU9VVCA9ICdFNTAwMycsXG4gIE1FTU9SWV9MSU1JVF9FWENFRURFRCA9ICdFNTAwNCdcbn1cblxuLyoqXG4gKiDor7fmsYLpqozor4Hop4TliJlcbiAqL1xuZXhwb3J0IHR5cGUgVmFsaWRhdGlvblZhbHVlID0gc3RyaW5nIHwgbnVtYmVyIHwgYm9vbGVhbiB8IG51bGwgfCB1bmRlZmluZWRcblxuZXhwb3J0IGludGVyZmFjZSBWYWxpZGF0aW9uUnVsZSB7XG4gIGZpZWxkOiBzdHJpbmdcbiAgdHlwZTogJ3N0cmluZycgfCAnbnVtYmVyJyB8ICdib29sZWFuJyB8ICdhcnJheScgfCAnb2JqZWN0J1xuICByZXF1aXJlZD86IGJvb2xlYW5cbiAgbWluPzogbnVtYmVyXG4gIG1heD86IG51bWJlclxuICBwYXR0ZXJuPzogUmVnRXhwXG4gIGVudW0/OiBWYWxpZGF0aW9uVmFsdWVbXVxuICBjdXN0b20/OiAodmFsdWU6IHVua25vd24pID0+IGJvb2xlYW4gfCBzdHJpbmdcbn1cblxuLyoqXG4gKiDliIbpobXlj4LmlbBcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBQYWdpbmF0aW9uUGFyYW1zIHtcbiAgcGFnZT86IG51bWJlclxuICBsaW1pdD86IG51bWJlclxuICBzb3J0Pzogc3RyaW5nXG4gIG9yZGVyPzogJ2FzYycgfCAnZGVzYydcbn1cblxuLyoqXG4gKiDliIbpobXlk43lupRcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBQYWdpbmF0ZWRSZXNwb25zZTxUPiB7XG4gIGRhdGE6IFRbXVxuICBwYWdpbmF0aW9uOiB7XG4gICAgcGFnZTogbnVtYmVyXG4gICAgbGltaXQ6IG51bWJlclxuICAgIHRvdGFsOiBudW1iZXJcbiAgICBwYWdlczogbnVtYmVyXG4gICAgaGFzX25leHQ6IGJvb2xlYW5cbiAgICBoYXNfcHJldjogYm9vbGVhblxuICB9XG59Il0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQTJVRTtJQUFBQSxhQUFBLFlBQUFBLENBQUE7TUFBQSxPQUFBQyxjQUFBO0lBQUE7RUFBQTtFQUFBLE9BQUFBLGNBQUE7QUFBQTtBQUFBRCxhQUFBO0FBM1VGOzs7O0FBZ1VBOzs7QUFHQSxXQUFZRSxTQWtCWDtBQUFBO0FBQUFGLGFBQUEsR0FBQUcsQ0FBQTtBQWxCRCxXQUFZRCxTQUFTO0VBQUE7RUFBQUYsYUFBQSxHQUFBSSxDQUFBO0VBQUFKLGFBQUEsR0FBQUcsQ0FBQTtFQUNuQjtFQUNBRCxTQUFBLGdDQUE0QjtFQUFBO0VBQUFGLGFBQUEsR0FBQUcsQ0FBQTtFQUM1QkQsU0FBQSxvQ0FBZ0M7RUFBQTtFQUFBRixhQUFBLEdBQUFHLENBQUE7RUFDaENELFNBQUEsNEJBQXdCO0VBQUE7RUFBQUYsYUFBQSxHQUFBRyxDQUFBO0VBQ3hCRCxTQUFBLCtCQUEyQjtFQUUzQjtFQUFBO0VBQUFGLGFBQUEsR0FBQUcsQ0FBQTtFQUNBRCxTQUFBLCtCQUEyQjtFQUFBO0VBQUFGLGFBQUEsR0FBQUcsQ0FBQTtFQUMzQkQsU0FBQSw2QkFBeUI7RUFBQTtFQUFBRixhQUFBLEdBQUFHLENBQUE7RUFDekJELFNBQUEsa0NBQThCO0VBQUE7RUFBQUYsYUFBQSxHQUFBRyxDQUFBO0VBQzlCRCxTQUFBLG9DQUFnQztFQUVoQztFQUFBO0VBQUFGLGFBQUEsR0FBQUcsQ0FBQTtFQUNBRCxTQUFBLDRCQUF3QjtFQUFBO0VBQUFGLGFBQUEsR0FBQUcsQ0FBQTtFQUN4QkQsU0FBQSxpQ0FBNkI7RUFBQTtFQUFBRixhQUFBLEdBQUFHLENBQUE7RUFDN0JELFNBQUEscUJBQWlCO0VBQUE7RUFBQUYsYUFBQSxHQUFBRyxDQUFBO0VBQ2pCRCxTQUFBLG1DQUErQjtBQUNqQyxDQUFDO0FBbEJXO0FBQUEsQ0FBQUYsYUFBQSxHQUFBSyxDQUFBLFVBQUFILFNBQVM7QUFBQTtBQUFBLENBQUFGLGFBQUEsR0FBQUssQ0FBQSxVQUFUSCxTQUFTIiwiaWdub3JlTGlzdCI6W119