8a970c47eb6d5f767f105899c14144aa
/* istanbul ignore next */
function cov_s721x8a1() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/SemanticAligner.ts";
  var hash = "9da2722a2b8535716240c22a6de646d541c51e70";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/SemanticAligner.ts",
    statementMap: {
      "0": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 31,
          column: 10
        }
      },
      "1": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 102
        }
      },
      "2": {
        start: {
          line: 40,
          column: 28
        },
        end: {
          line: 40,
          column: 72
        }
      },
      "3": {
        start: {
          line: 42,
          column: 35
        },
        end: {
          line: 42,
          column: 86
        }
      },
      "4": {
        start: {
          line: 44,
          column: 31
        },
        end: {
          line: 46,
          column: 58
        }
      },
      "5": {
        start: {
          line: 48,
          column: 27
        },
        end: {
          line: 48,
          column: 113
        }
      },
      "6": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 57,
          column: 10
        }
      },
      "7": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "8": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 80
        }
      },
      "9": {
        start: {
          line: 68,
          column: 37
        },
        end: {
          line: 68,
          column: 75
        }
      },
      "10": {
        start: {
          line: 69,
          column: 31
        },
        end: {
          line: 69,
          column: 75
        }
      },
      "11": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 84
        }
      },
      "12": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 80,
          column: 9
        }
      },
      "13": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 47
        }
      },
      "14": {
        start: {
          line: 82,
          column: 26
        },
        end: {
          line: 82,
          column: 59
        }
      },
      "15": {
        start: {
          line: 83,
          column: 26
        },
        end: {
          line: 83,
          column: 53
        }
      },
      "16": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 86,
          column: 9
        }
      },
      "17": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 40
        }
      },
      "18": {
        start: {
          line: 88,
          column: 24
        },
        end: {
          line: 88,
          column: 52
        }
      },
      "19": {
        start: {
          line: 89,
          column: 25
        },
        end: {
          line: 89,
          column: 58
        }
      },
      "20": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 98,
          column: 9
        }
      },
      "21": {
        start: {
          line: 90,
          column: 21
        },
        end: {
          line: 90,
          column: 22
        }
      },
      "22": {
        start: {
          line: 91,
          column: 22
        },
        end: {
          line: 91,
          column: 23
        }
      },
      "23": {
        start: {
          line: 92,
          column: 26
        },
        end: {
          line: 92,
          column: 38
        }
      },
      "24": {
        start: {
          line: 93,
          column: 24
        },
        end: {
          line: 93,
          column: 61
        }
      },
      "25": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 96,
          column: 13
        }
      },
      "26": {
        start: {
          line: 94,
          column: 25
        },
        end: {
          line: 94,
          column: 30
        }
      },
      "27": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 95,
          column: 47
        }
      },
      "28": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 97,
          column: 45
        }
      },
      "29": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 23
        }
      },
      "30": {
        start: {
          line: 106,
          column: 26
        },
        end: {
          line: 106,
          column: 59
        }
      },
      "31": {
        start: {
          line: 107,
          column: 23
        },
        end: {
          line: 107,
          column: 51
        }
      },
      "32": {
        start: {
          line: 109,
          column: 30
        },
        end: {
          line: 109,
          column: 62
        }
      },
      "33": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 110,
          column: 46
        }
      },
      "34": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 42
        }
      },
      "35": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 112,
          column: 47
        }
      },
      "36": {
        start: {
          line: 113,
          column: 8
        },
        end: {
          line: 113,
          column: 51
        }
      },
      "37": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 53
        }
      },
      "38": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 115,
          column: 47
        }
      },
      "39": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 116,
          column: 45
        }
      },
      "40": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 47
        }
      },
      "41": {
        start: {
          line: 119,
          column: 25
        },
        end: {
          line: 119,
          column: 50
        }
      },
      "42": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 44
        }
      },
      "43": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 39
        }
      },
      "44": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 122,
          column: 40
        }
      },
      "45": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 42
        }
      },
      "46": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 124,
          column: 52
        }
      },
      "47": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 65
        }
      },
      "48": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 67
        }
      },
      "49": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 129,
          column: 46
        }
      },
      "50": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 130,
          column: 52
        }
      },
      "51": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 55
        }
      },
      "52": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 132,
          column: 47
        }
      },
      "53": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 134,
          column: 73
        }
      },
      "54": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 135,
          column: 22
        }
      },
      "55": {
        start: {
          line: 141,
          column: 23
        },
        end: {
          line: 150,
          column: 9
        }
      },
      "56": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 39
        }
      },
      "57": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 159,
          column: 9
        }
      },
      "58": {
        start: {
          line: 158,
          column: 12
        },
        end: {
          line: 158,
          column: 21
        }
      },
      "59": {
        start: {
          line: 160,
          column: 25
        },
        end: {
          line: 160,
          column: 26
        }
      },
      "60": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 161,
          column: 21
        }
      },
      "61": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 162,
          column: 21
        }
      },
      "62": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 167,
          column: 9
        }
      },
      "63": {
        start: {
          line: 163,
          column: 21
        },
        end: {
          line: 163,
          column: 22
        }
      },
      "64": {
        start: {
          line: 164,
          column: 12
        },
        end: {
          line: 164,
          column: 50
        }
      },
      "65": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 45
        }
      },
      "66": {
        start: {
          line: 166,
          column: 12
        },
        end: {
          line: 166,
          column: 45
        }
      },
      "67": {
        start: {
          line: 168,
          column: 26
        },
        end: {
          line: 168,
          column: 61
        }
      },
      "68": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 58
        }
      },
      "69": {
        start: {
          line: 176,
          column: 27
        },
        end: {
          line: 183,
          column: 9
        }
      },
      "70": {
        start: {
          line: 185,
          column: 33
        },
        end: {
          line: 185,
          column: 83
        }
      },
      "71": {
        start: {
          line: 186,
          column: 28
        },
        end: {
          line: 186,
          column: 68
        }
      },
      "72": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 187,
          column: 85
        }
      },
      "73": {
        start: {
          line: 194,
          column: 25
        },
        end: {
          line: 194,
          column: 60
        }
      },
      "74": {
        start: {
          line: 195,
          column: 27
        },
        end: {
          line: 195,
          column: 107
        }
      },
      "75": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 70
        }
      },
      "76": {
        start: {
          line: 203,
          column: 30
        },
        end: {
          line: 203,
          column: 81
        }
      },
      "77": {
        start: {
          line: 205,
          column: 31
        },
        end: {
          line: 205,
          column: 68
        }
      },
      "78": {
        start: {
          line: 207,
          column: 32
        },
        end: {
          line: 207,
          column: 65
        }
      },
      "79": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 209,
          column: 78
        }
      },
      "80": {
        start: {
          line: 216,
          column: 31
        },
        end: {
          line: 216,
          column: 72
        }
      },
      "81": {
        start: {
          line: 217,
          column: 32
        },
        end: {
          line: 217,
          column: 62
        }
      },
      "82": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 75
        }
      },
      "83": {
        start: {
          line: 226,
          column: 23
        },
        end: {
          line: 226,
          column: 76
        }
      },
      "84": {
        start: {
          line: 227,
          column: 21
        },
        end: {
          line: 227,
          column: 70
        }
      },
      "85": {
        start: {
          line: 227,
          column: 45
        },
        end: {
          line: 227,
          column: 50
        }
      },
      "86": {
        start: {
          line: 228,
          column: 25
        },
        end: {
          line: 228,
          column: 106
        }
      },
      "87": {
        start: {
          line: 228,
          column: 55
        },
        end: {
          line: 228,
          column: 86
        }
      },
      "88": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 41
        }
      },
      "89": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 238,
          column: 9
        }
      },
      "90": {
        start: {
          line: 237,
          column: 12
        },
        end: {
          line: 237,
          column: 24
        }
      },
      "91": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 239,
          column: 32
        }
      },
      "92": {
        start: {
          line: 240,
          column: 24
        },
        end: {
          line: 240,
          column: 25
        }
      },
      "93": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "94": {
        start: {
          line: 242,
          column: 30
        },
        end: {
          line: 242,
          column: 87
        }
      },
      "95": {
        start: {
          line: 243,
          column: 12
        },
        end: {
          line: 247,
          column: 13
        }
      },
      "96": {
        start: {
          line: 245,
          column: 16
        },
        end: {
          line: 245,
          column: 53
        }
      },
      "97": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 42
        }
      },
      "98": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 249,
          column: 29
        }
      },
      "99": {
        start: {
          line: 255,
          column: 27
        },
        end: {
          line: 255,
          column: 36
        }
      },
      "100": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "101": {
        start: {
          line: 258,
          column: 34
        },
        end: {
          line: 258,
          column: 76
        }
      },
      "102": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 261,
          column: 13
        }
      },
      "103": {
        start: {
          line: 260,
          column: 16
        },
        end: {
          line: 260,
          column: 56
        }
      },
      "104": {
        start: {
          line: 264,
          column: 33
        },
        end: {
          line: 264,
          column: 82
        }
      },
      "105": {
        start: {
          line: 266,
          column: 31
        },
        end: {
          line: 266,
          column: 69
        }
      },
      "106": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 272,
          column: 10
        }
      },
      "107": {
        start: {
          line: 278,
          column: 23
        },
        end: {
          line: 278,
          column: 81
        }
      },
      "108": {
        start: {
          line: 278,
          column: 64
        },
        end: {
          line: 278,
          column: 80
        }
      },
      "109": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 280,
          column: 23
        }
      },
      "110": {
        start: {
          line: 280,
          column: 12
        },
        end: {
          line: 280,
          column: 23
        }
      },
      "111": {
        start: {
          line: 281,
          column: 21
        },
        end: {
          line: 281,
          column: 70
        }
      },
      "112": {
        start: {
          line: 281,
          column: 45
        },
        end: {
          line: 281,
          column: 50
        }
      },
      "113": {
        start: {
          line: 282,
          column: 25
        },
        end: {
          line: 282,
          column: 106
        }
      },
      "114": {
        start: {
          line: 282,
          column: 55
        },
        end: {
          line: 282,
          column: 86
        }
      },
      "115": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 284,
          column: 50
        }
      },
      "116": {
        start: {
          line: 290,
          column: 26
        },
        end: {
          line: 290,
          column: 55
        }
      },
      "117": {
        start: {
          line: 291,
          column: 22
        },
        end: {
          line: 291,
          column: 24
        }
      },
      "118": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 302,
          column: 9
        }
      },
      "119": {
        start: {
          line: 292,
          column: 21
        },
        end: {
          line: 292,
          column: 22
        }
      },
      "120": {
        start: {
          line: 293,
          column: 12
        },
        end: {
          line: 301,
          column: 13
        }
      },
      "121": {
        start: {
          line: 293,
          column: 25
        },
        end: {
          line: 293,
          column: 30
        }
      },
      "122": {
        start: {
          line: 294,
          column: 30
        },
        end: {
          line: 294,
          column: 42
        }
      },
      "123": {
        start: {
          line: 295,
          column: 30
        },
        end: {
          line: 295,
          column: 42
        }
      },
      "124": {
        start: {
          line: 296,
          column: 35
        },
        end: {
          line: 296,
          column: 56
        }
      },
      "125": {
        start: {
          line: 297,
          column: 35
        },
        end: {
          line: 297,
          column: 56
        }
      },
      "126": {
        start: {
          line: 299,
          column: 34
        },
        end: {
          line: 299,
          column: 93
        }
      },
      "127": {
        start: {
          line: 300,
          column: 16
        },
        end: {
          line: 300,
          column: 85
        }
      },
      "128": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 304,
          column: 55
        }
      },
      "129": {
        start: {
          line: 304,
          column: 36
        },
        end: {
          line: 304,
          column: 53
        }
      },
      "130": {
        start: {
          line: 310,
          column: 8
        },
        end: {
          line: 310,
          column: 55
        }
      },
      "131": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        loc: {
          start: {
            line: 25,
            column: 29
          },
          end: {
            line: 32,
            column: 5
          }
        },
        line: 25
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 36,
            column: 4
          },
          end: {
            line: 36,
            column: 5
          }
        },
        loc: {
          start: {
            line: 36,
            column: 57
          },
          end: {
            line: 58,
            column: 5
          }
        },
        line: 36
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 62,
            column: 4
          },
          end: {
            line: 62,
            column: 5
          }
        },
        loc: {
          start: {
            line: 62,
            column: 57
          },
          end: {
            line: 72,
            column: 5
          }
        },
        line: 62
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 76,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        },
        loc: {
          start: {
            line: 76,
            column: 38
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 76
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        loc: {
          start: {
            line: 104,
            column: 44
          },
          end: {
            line: 136,
            column: 5
          }
        },
        line: 104
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 140,
            column: 4
          },
          end: {
            line: 140,
            column: 5
          }
        },
        loc: {
          start: {
            line: 140,
            column: 42
          },
          end: {
            line: 152,
            column: 5
          }
        },
        line: 140
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 156,
            column: 5
          }
        },
        loc: {
          start: {
            line: 156,
            column: 48
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 156
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 174,
            column: 5
          }
        },
        loc: {
          start: {
            line: 174,
            column: 60
          },
          end: {
            line: 188,
            column: 5
          }
        },
        line: 174
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 192,
            column: 4
          },
          end: {
            line: 192,
            column: 5
          }
        },
        loc: {
          start: {
            line: 192,
            column: 36
          },
          end: {
            line: 197,
            column: 5
          }
        },
        line: 192
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        loc: {
          start: {
            line: 201,
            column: 44
          },
          end: {
            line: 210,
            column: 5
          }
        },
        line: 201
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 214,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        },
        loc: {
          start: {
            line: 214,
            column: 51
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 214
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        },
        loc: {
          start: {
            line: 224,
            column: 86
          },
          end: {
            line: 231,
            column: 5
          }
        },
        line: 224
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 227,
            column: 35
          },
          end: {
            line: 227,
            column: 36
          }
        },
        loc: {
          start: {
            line: 227,
            column: 45
          },
          end: {
            line: 227,
            column: 50
          }
        },
        line: 227
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 228,
            column: 39
          },
          end: {
            line: 228,
            column: 40
          }
        },
        loc: {
          start: {
            line: 228,
            column: 55
          },
          end: {
            line: 228,
            column: 86
          }
        },
        line: 228
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 235,
            column: 4
          },
          end: {
            line: 235,
            column: 5
          }
        },
        loc: {
          start: {
            line: 235,
            column: 51
          },
          end: {
            line: 250,
            column: 5
          }
        },
        line: 235
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        loc: {
          start: {
            line: 254,
            column: 57
          },
          end: {
            line: 273,
            column: 5
          }
        },
        line: 254
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 277,
            column: 4
          },
          end: {
            line: 277,
            column: 5
          }
        },
        loc: {
          start: {
            line: 277,
            column: 49
          },
          end: {
            line: 285,
            column: 5
          }
        },
        line: 277
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 278,
            column: 59
          },
          end: {
            line: 278,
            column: 60
          }
        },
        loc: {
          start: {
            line: 278,
            column: 64
          },
          end: {
            line: 278,
            column: 80
          }
        },
        line: 278
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 281,
            column: 35
          },
          end: {
            line: 281,
            column: 36
          }
        },
        loc: {
          start: {
            line: 281,
            column: 45
          },
          end: {
            line: 281,
            column: 50
          }
        },
        line: 281
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 282,
            column: 39
          },
          end: {
            line: 282,
            column: 40
          }
        },
        loc: {
          start: {
            line: 282,
            column: 55
          },
          end: {
            line: 282,
            column: 86
          }
        },
        line: 282
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 289,
            column: 4
          },
          end: {
            line: 289,
            column: 5
          }
        },
        loc: {
          start: {
            line: 289,
            column: 38
          },
          end: {
            line: 305,
            column: 5
          }
        },
        line: 289
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 304,
            column: 26
          },
          end: {
            line: 304,
            column: 27
          }
        },
        loc: {
          start: {
            line: 304,
            column: 36
          },
          end: {
            line: 304,
            column: 53
          }
        },
        line: 304
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 309,
            column: 4
          },
          end: {
            line: 309,
            column: 5
          }
        },
        loc: {
          start: {
            line: 309,
            column: 28
          },
          end: {
            line: 311,
            column: 5
          }
        },
        line: 309
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 315,
            column: 5
          }
        },
        loc: {
          start: {
            line: 315,
            column: 16
          },
          end: {
            line: 317,
            column: 5
          }
        },
        line: 315
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 25,
            column: 25
          },
          end: {
            line: 25,
            column: 27
          }
        }],
        line: 25
      },
      "1": {
        loc: {
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 75
          }
        }, {
          start: {
            line: 27,
            column: 79
          },
          end: {
            line: 27,
            column: 83
          }
        }],
        line: 27
      },
      "2": {
        loc: {
          start: {
            line: 28,
            column: 31
          },
          end: {
            line: 28,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 31
          },
          end: {
            line: 28,
            column: 55
          }
        }, {
          start: {
            line: 28,
            column: 59
          },
          end: {
            line: 28,
            column: 62
          }
        }],
        line: 28
      },
      "3": {
        loc: {
          start: {
            line: 29,
            column: 28
          },
          end: {
            line: 29,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 28
          },
          end: {
            line: 29,
            column: 49
          }
        }, {
          start: {
            line: 29,
            column: 53
          },
          end: {
            line: 29,
            column: 56
          }
        }],
        line: 29
      },
      "4": {
        loc: {
          start: {
            line: 30,
            column: 27
          },
          end: {
            line: 30,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 27
          },
          end: {
            line: 30,
            column: 47
          }
        }, {
          start: {
            line: 30,
            column: 51
          },
          end: {
            line: 30,
            column: 54
          }
        }],
        line: 30
      },
      "5": {
        loc: {
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 66,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 66,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "6": {
        loc: {
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "7": {
        loc: {
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 78,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 78,
            column: 12
          },
          end: {
            line: 78,
            column: 39
          }
        }, {
          start: {
            line: 78,
            column: 43
          },
          end: {
            line: 78,
            column: 115
          }
        }],
        line: 78
      },
      "8": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 86,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 86,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "9": {
        loc: {
          start: {
            line: 151,
            column: 15
          },
          end: {
            line: 151,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 151,
            column: 15
          },
          end: {
            line: 151,
            column: 31
          }
        }, {
          start: {
            line: 151,
            column: 35
          },
          end: {
            line: 151,
            column: 38
          }
        }],
        line: 151
      },
      "10": {
        loc: {
          start: {
            line: 157,
            column: 8
          },
          end: {
            line: 159,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 8
          },
          end: {
            line: 159,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "11": {
        loc: {
          start: {
            line: 169,
            column: 15
          },
          end: {
            line: 169,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 169,
            column: 31
          },
          end: {
            line: 169,
            column: 53
          }
        }, {
          start: {
            line: 169,
            column: 56
          },
          end: {
            line: 169,
            column: 57
          }
        }],
        line: 169
      },
      "12": {
        loc: {
          start: {
            line: 186,
            column: 28
          },
          end: {
            line: 186,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 28
          },
          end: {
            line: 186,
            column: 56
          }
        }, {
          start: {
            line: 186,
            column: 60
          },
          end: {
            line: 186,
            column: 68
          }
        }],
        line: 186
      },
      "13": {
        loc: {
          start: {
            line: 187,
            column: 15
          },
          end: {
            line: 187,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 187,
            column: 75
          },
          end: {
            line: 187,
            column: 78
          }
        }, {
          start: {
            line: 187,
            column: 81
          },
          end: {
            line: 187,
            column: 84
          }
        }],
        line: 187
      },
      "14": {
        loc: {
          start: {
            line: 196,
            column: 15
          },
          end: {
            line: 196,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 196,
            column: 15
          },
          end: {
            line: 196,
            column: 55
          }
        }, {
          start: {
            line: 196,
            column: 59
          },
          end: {
            line: 196,
            column: 69
          }
        }],
        line: 196
      },
      "15": {
        loc: {
          start: {
            line: 236,
            column: 8
          },
          end: {
            line: 238,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 236,
            column: 8
          },
          end: {
            line: 238,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 236
      },
      "16": {
        loc: {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 247,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 247,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "17": {
        loc: {
          start: {
            line: 243,
            column: 16
          },
          end: {
            line: 244,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 16
          },
          end: {
            line: 243,
            column: 52
          }
        }, {
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 87
          }
        }],
        line: 243
      },
      "18": {
        loc: {
          start: {
            line: 259,
            column: 12
          },
          end: {
            line: 261,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 12
          },
          end: {
            line: 261,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "19": {
        loc: {
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 280,
            column: 23
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 280,
            column: 23
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/SemanticAligner.ts",
      mappings: "AAAA;;;;;;;;;GASG;AAEH,OAAO,EAAE,YAAY,EAAE,0BAA0B,EAAE,MAAM,6BAA6B,CAAA;AAsDtF,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,eAAe;IAClB,MAAM,CAAuB;IAErC;;OAEG;IACH,YAAY,SAAyC,EAAE;QACrD,IAAI,CAAC,MAAM,GAAG;YACZ,2BAA2B,EAAE,MAAM,CAAC,2BAA2B,IAAI,IAAI;YACvE,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,IAAI,GAAG;YAClD,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,GAAG;YAC5C,aAAa,EAAE,MAAM,CAAC,aAAa,IAAI,GAAG;SAC3C,CAAA;IACH,CAAC;IAED;;OAEG;IACH,iCAAiC,CAC/B,OAAyB,EACzB,QAAkC;QAElC,aAAa;QACb,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CACzD,OAAO,CAAC,eAAe,EACvB,QAAQ,CACT,CAAA;QAED,aAAa;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAEhE,aAAa;QACb,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAE9E,YAAY;QACZ,MAAM,cAAc,GAClB,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,kBAAkB;YAC/C,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,WAAW;YAC3C,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,kBAAkB,CAAA;QAEhD,WAAW;QACX,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAClD,kBAAkB,EAClB,WAAW,EACX,kBAAkB,CACnB,CAAA;QAED,OAAO;YACL,eAAe,EAAE,OAAO,CAAC,UAAU;YACnC,cAAc,EAAE,QAAQ,CAAC,QAAQ;YACjC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;YAC5B,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,UAAU;SACX,CAAA;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CACjC,aAAsC,EACtC,QAAkC;QAElC,wBAAwB;QACxB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,8BAA8B,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;QACrE,CAAC;QAED,YAAY;QACZ,MAAM,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAA;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAA;QAEnE,mBAAmB;QACnB,OAAO,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAA;IAC7E,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,aAAsC;QAC/D,gBAAgB;QAChB,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa,CAAC,MAAM,KAAK,0BAA0B,CAAC,MAAM,EAAE,CAAC;YAC5G,OAAO,aAAa,CAAC,aAAa,CAAA;QACpC,CAAC;QAED,iBAAiB;QACjB,MAAM,SAAS,GAAG,0BAA0B,CAAC,MAAM,CAAA;QACnD,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAA;QAE7C,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,OAAO,aAAa,CAAC,MAAM,CAAA;QAC7B,CAAC;QAED,eAAe;QACf,MAAM,OAAO,GAAa,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,CAAA;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,GAAG,GAAG,CAAC,CAAA;YACX,MAAM,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAA;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,EAAE,SAAS,CAAC,CAAA;YAEjD,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjC,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAChC,CAAC;YAED,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;QAClC,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,QAAkC;QACtE,kBAAkB;QAClB,MAAM,SAAS,GAAG,0BAA0B,CAAC,MAAM,CAAA,CAAC,YAAY;QAChE,MAAM,MAAM,GAAa,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAErD,aAAa;QACb,MAAM,aAAa,GAAG,QAAQ,CAAC,uBAAuB,CAAA;QACtD,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,WAAW,CAAA;QACrC,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,OAAO,CAAA;QACjC,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,YAAY,CAAA;QACtC,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,gBAAgB,CAAA;QAC1C,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,kBAAkB,CAAA;QAC5C,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,YAAY,CAAA;QACtC,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,UAAU,CAAA;QACpC,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,YAAY,CAAA;QAEtC,aAAa;QACb,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAA;QAC1C,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAA;QACnC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAA;QAC9B,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAA;QAC/B,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAA;QACjC,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,qBAAqB,CAAA;QAE3C,aAAa;QACb,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,iBAAiB,CAAC,gBAAgB,CAAA;QACxD,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,iBAAiB,CAAC,cAAc,GAAG,CAAC,CAAA,CAAC,MAAM;QAEjE,gBAAgB;QAChB,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAA;QACrC,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,qBAAqB,CAAA;QAC3C,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,wBAAwB,CAAA;QAC9C,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAA;QAEtC,gBAAgB;QAChB,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QAEhE,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,QAAsB;QACxD,MAAM,MAAM,GAAiC;YAC3C,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG;YACzB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG;YACzB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI;YAC1B,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI;YAC1B,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG;YACzB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG;YACzB,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI;YAC1B,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG;SAC1B,CAAA;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAA;IAChC,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,OAAiB,EAAE,OAAiB;QACpE,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO,CAAC,CAAA;QACV,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,CAAA;QAClB,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,KAAK,GAAG,CAAC,CAAA;QAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,UAAU,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YACrC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YAChC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QAClC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACrD,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;IACnD,CAAC;IAED;;OAEG;IACK,8BAA8B,CACpC,aAAsC,EACtC,QAAkC;QAElC,sBAAsB;QACtB,MAAM,UAAU,GAA6B;YAC3C,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YAC3B,aAAa,EAAE,CAAC,MAAM,CAAC;YACvB,iBAAiB,EAAE,CAAC,KAAK,CAAC;YAC1B,SAAS,EAAE,CAAC,MAAM,CAAC;YACnB,SAAS,EAAE,CAAC,MAAM,CAAC;YACnB,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;SAC5B,CAAA;QAED,kBAAkB;QAClB,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAC3E,MAAM,WAAW,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAE5D,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;IAC9E,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAAgB;QAC9C,YAAY;QACZ,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAA;QACpD,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAA;QACnG,OAAO,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,UAAU,CAAA;IAC/D,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,OAAyB,EACzB,QAAkC;QAElC,aAAa;QACb,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;QAEzE,YAAY;QACZ,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,GAAG,GAAG,CAAA;QAE5D,cAAc;QACd,MAAM,eAAe,GAAG,QAAQ,CAAC,wBAAwB,CAAA;QAEzD,aAAa;QACb,OAAO,CAAC,aAAa,GAAG,GAAG,GAAG,cAAc,GAAG,eAAe,GAAG,GAAG,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACK,2BAA2B,CACjC,OAAyB,EACzB,QAAkC;QAElC,yBAAyB;QACzB,MAAM,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,YAAY,CAAA;QAChE,MAAM,eAAe,GAAG,QAAQ,CAAC,qBAAqB,CAAA;QAEtD,uBAAuB;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,eAAe,CAAC,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACK,4BAA4B,CAClC,kBAA0B,EAC1B,WAAmB,EACnB,kBAA0B;QAE1B,iBAAiB;QACjB,MAAM,MAAM,GAAG,CAAC,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAA;QACpE,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAA;QAC9D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAA;QAElG,aAAa;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,iBAAiB,CACf,OAAyB,EACzB,kBAA8C;QAE9C,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,aAAa,GAA6B,IAAI,CAAA;QAClD,IAAI,SAAS,GAAG,CAAC,CAAA;QAEjB,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;YAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,iCAAiC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YAE3E,IAAI,SAAS,CAAC,cAAc,GAAG,SAAS;gBACpC,SAAS,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,CAAC;gBAC5E,SAAS,GAAG,SAAS,CAAC,cAAc,CAAA;gBACpC,aAAa,GAAG,SAAS,CAAA;YAC3B,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,wBAAwB,CACtB,OAAyB,EACzB,iBAAgE;QAEhE,MAAM,UAAU,GAAG,IAAI,GAAG,EAAmC,CAAA;QAE7D,cAAc;QACd,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACtD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;YAChE,IAAI,aAAa,EAAE,CAAC;gBAClB,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;YACzC,CAAC;QACH,CAAC;QAED,WAAW;QACX,MAAM,gBAAgB,GAAG,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAA;QAE1E,YAAY;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAA;QAE7D,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,UAAU;YAC7B,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;SACf,CAAA;IACH,CAAC;IAED;;OAEG;IACK,gCAAgC,CACtC,UAAgD;QAEhD,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAA;QACzE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,GAAG,CAAA;QAEjC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAA;QAC9D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAA;QAElG,2BAA2B;QAC3B,OAAO,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAA;IAC3C,CAAC;IAED;;OAEG;IACK,qBAAqB,CAC3B,UAAgD;QAEhD,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAA;QAC/C,MAAM,KAAK,GAA+E,EAAE,CAAA;QAE5F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBAC1B,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBAC1B,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAE,CAAA;gBACzC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAE,CAAA;gBAEzC,aAAa;gBACb,MAAM,SAAS,GAAG,CAAC,UAAU,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;gBAC7E,KAAK,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAA;YACtE,CAAC;QACH,CAAC;QAED,UAAU;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAyC;QACpD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;IAC3B,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/SemanticAligner.ts"],
      sourcesContent: ["/**\n * \u8BED\u4E49\u5BF9\u9F50\u5668\n * \n * \u8D1F\u8D23\u8DE8\u8BED\u8A00\u8BED\u4E49\u5BF9\u9F50\u3001\u6982\u5FF5\u6620\u5C04\u3001\u8BED\u4E49\u76F8\u4F3C\u5EA6\u8BA1\u7B97\u7B49\u529F\u80FD\n * \u786E\u4FDD\u591A\u8BED\u79CD\u751F\u6210\u7684\u8BED\u4E49\u4E00\u81F4\u6027\u548C\u6587\u5316\u9002\u914D\u6027\n * \n * <AUTHOR> team\n * @version 3.0.0\n * @created 2025-06-24\n */\n\nimport { LanguageCode, SEMANTIC_VECTOR_DIMENSIONS } from '../../types/multilingual.js'\nimport type {\n  UniversalConcept,\n  LanguageSpecificMorpheme,\n  UniversalSemanticVector\n} from '../../types/multilingual.js'\n\n// ============================================================================\n// \u8BED\u4E49\u5BF9\u9F50\u5668\u63A5\u53E3\n// ============================================================================\n\nexport interface SemanticAlignment {\n  /** \u6E90\u6982\u5FF5ID */\n  sourceConceptId: string\n  /** \u76EE\u6807\u8BED\u8A00 */\n  targetLanguage: LanguageCode\n  /** \u5BF9\u9F50\u7684\u8BED\u7D20\u5217\u8868 */\n  alignedMorphemes: LanguageSpecificMorpheme[]\n  /** \u5BF9\u9F50\u5206\u6570 [0-1] */\n  alignmentScore: number\n  /** \u8BED\u4E49\u76F8\u4F3C\u5EA6 [0-1] */\n  semanticSimilarity: number\n  /** \u6587\u5316\u9002\u914D\u5EA6 [0-1] */\n  culturalFit: number\n  /** \u5BF9\u9F50\u7F6E\u4FE1\u5EA6 [0-1] */\n  confidence: number\n}\n\nexport interface CrossLingualMapping {\n  /** \u6982\u5FF5ID */\n  conceptId: string\n  /** \u5404\u8BED\u8A00\u7684\u8BED\u7D20\u6620\u5C04 */\n  languageMorphemes: Map<LanguageCode, LanguageSpecificMorpheme[]>\n  /** \u8DE8\u8BED\u8A00\u4E00\u81F4\u6027\u8BC4\u5206 [0-1] */\n  consistencyScore: number\n  /** \u6700\u4F73\u5BF9\u9F50\u8BED\u8A00\u5BF9 */\n  bestAlignments: Array<{\n    language1: LanguageCode\n    language2: LanguageCode\n    score: number\n  }>\n}\n\nexport interface SemanticAlignerConfig {\n  /** \u8BED\u4E49\u76F8\u4F3C\u5EA6\u9608\u503C */\n  semanticSimilarityThreshold: number\n  /** \u6587\u5316\u9002\u914D\u6743\u91CD */\n  culturalFitWeight: number\n  /** \u8BED\u4E49\u6743\u91CD */\n  semanticWeight: number\n  /** \u8D28\u91CF\u6743\u91CD */\n  qualityWeight: number\n}\n\n// ============================================================================\n// \u8BED\u4E49\u5BF9\u9F50\u5668\u7C7B\n// ============================================================================\n\n/**\n * \u8BED\u4E49\u5BF9\u9F50\u5668\u7C7B\n * \n * \u63D0\u4F9B\u8DE8\u8BED\u8A00\u8BED\u4E49\u5BF9\u9F50\u548C\u6982\u5FF5\u6620\u5C04\u529F\u80FD\n */\nexport class SemanticAligner {\n  private config: SemanticAlignerConfig\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   */\n  constructor(config: Partial<SemanticAlignerConfig> = {}) {\n    this.config = {\n      semanticSimilarityThreshold: config.semanticSimilarityThreshold || 0.75,\n      culturalFitWeight: config.culturalFitWeight || 0.3,\n      semanticWeight: config.semanticWeight || 0.5,\n      qualityWeight: config.qualityWeight || 0.2\n    }\n  }\n\n  /**\n   * \u8BA1\u7B97\u6982\u5FF5\u4E0E\u8BED\u7D20\u7684\u8BED\u4E49\u5BF9\u9F50\u5EA6\n   */\n  calculateConceptMorphemeAlignment(\n    concept: UniversalConcept,\n    morpheme: LanguageSpecificMorpheme\n  ): SemanticAlignment {\n    // 1. \u8BED\u4E49\u76F8\u4F3C\u5EA6\u8BA1\u7B97\n    const semanticSimilarity = this.calculateSemanticSimilarity(\n      concept.semantic_vector,\n      morpheme\n    )\n\n    // 2. \u6587\u5316\u9002\u914D\u5EA6\u8BA1\u7B97\n    const culturalFit = this.calculateCulturalFit(concept, morpheme)\n\n    // 3. \u8D28\u91CF\u4E00\u81F4\u6027\u8BA1\u7B97\n    const qualityConsistency = this.calculateQualityConsistency(concept, morpheme)\n\n    // 4. \u7EFC\u5408\u5BF9\u9F50\u5206\u6570\n    const alignmentScore = \n      this.config.semanticWeight * semanticSimilarity +\n      this.config.culturalFitWeight * culturalFit +\n      this.config.qualityWeight * qualityConsistency\n\n    // 5. \u8BA1\u7B97\u7F6E\u4FE1\u5EA6\n    const confidence = this.calculateAlignmentConfidence(\n      semanticSimilarity,\n      culturalFit,\n      qualityConsistency\n    )\n\n    return {\n      sourceConceptId: concept.concept_id,\n      targetLanguage: morpheme.language,\n      alignedMorphemes: [morpheme],\n      alignmentScore,\n      semanticSimilarity,\n      culturalFit,\n      confidence\n    }\n  }\n\n  /**\n   * \u8BA1\u7B97\u8BED\u4E49\u76F8\u4F3C\u5EA6\n   */\n  private calculateSemanticSimilarity(\n    conceptVector: UniversalSemanticVector,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // \u5982\u679C\u8BED\u7D20\u6CA1\u6709\u8BED\u4E49\u5411\u91CF\uFF0C\u4F7F\u7528\u57FA\u4E8E\u7C7B\u522B\u7684\u76F8\u4F3C\u5EA6\n    if (!morpheme.phonetic_features) {\n      return this.calculateCategoricalSimilarity(conceptVector, morpheme)\n    }\n\n    // \u83B7\u53D6\u9002\u914D\u7684\u6982\u5FF5\u5411\u91CF\n    const adaptedConceptVector = this.adaptConceptVector(conceptVector)\n    const morphemeVector = this.extractMorphemeSemanticVector(morpheme)\n\n    // \u4F7F\u7528\u4F59\u5F26\u76F8\u4F3C\u5EA6\u8BA1\u7B97\u8BED\u4E49\u5411\u91CF\u76F8\u4F3C\u5EA6\n    return this.calculateCosineSimilarity(adaptedConceptVector, morphemeVector)\n  }\n\n  /**\n   * \u9002\u914D\u6982\u5FF5\u5411\u91CF\u5230\u517C\u5BB9\u7EF4\u5EA6\n   */\n  private adaptConceptVector(conceptVector: UniversalSemanticVector): number[] {\n    // \u5982\u679C\u6709\u517C\u5BB9\u6027\u5411\u91CF\uFF0C\u76F4\u63A5\u4F7F\u7528\n    if (conceptVector.legacy_vector && conceptVector.legacy_vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY) {\n      return conceptVector.legacy_vector\n    }\n\n    // \u5426\u5219\u4ECE\u9AD8\u7EF4\u5411\u91CF\u964D\u7EF4\u5230\u517C\u5BB9\u7EF4\u5EA6\n    const targetDim = SEMANTIC_VECTOR_DIMENSIONS.LEGACY\n    const sourceDim = conceptVector.vector.length\n\n    if (sourceDim === targetDim) {\n      return conceptVector.vector\n    }\n\n    // \u7B80\u5355\u7684\u964D\u7EF4\u7B56\u7565\uFF1A\u5E73\u5747\u6C60\u5316\n    const adapted: number[] = new Array(targetDim).fill(0)\n    const poolSize = Math.floor(sourceDim / targetDim)\n\n    for (let i = 0; i < targetDim; i++) {\n      let sum = 0\n      const start = i * poolSize\n      const end = Math.min(start + poolSize, sourceDim)\n\n      for (let j = start; j < end; j++) {\n        sum += conceptVector.vector[j]\n      }\n\n      adapted[i] = sum / (end - start)\n    }\n\n    return adapted\n  }\n\n  /**\n   * \u63D0\u53D6\u8BED\u7D20\u7684\u8BED\u4E49\u5411\u91CF\n   */\n  private extractMorphemeSemanticVector(morpheme: LanguageSpecificMorpheme): number[] {\n    // \u57FA\u4E8E\u8BED\u7D20\u7684\u5404\u79CD\u7279\u5F81\u6784\u5EFA\u8BED\u4E49\u5411\u91CF\n    const vectorDim = SEMANTIC_VECTOR_DIMENSIONS.LEGACY // \u4F7F\u7528\u517C\u5BB9\u6027\u5411\u91CF\u7EF4\u5EA6\n    const vector: number[] = new Array(vectorDim).fill(0)\n    \n    // \u57FA\u4E8E\u8D28\u91CF\u8BC4\u5206\u586B\u5145\u5411\u91CF\n    const qualityScores = morpheme.language_quality_scores\n    vector[0] = qualityScores.naturalness\n    vector[1] = qualityScores.fluency\n    vector[2] = qualityScores.authenticity\n    vector[3] = qualityScores.aesthetic_appeal\n    vector[4] = qualityScores.pronunciation_ease\n    vector[5] = qualityScores.memorability\n    vector[6] = qualityScores.uniqueness\n    vector[7] = qualityScores.practicality\n    \n    // \u57FA\u4E8E\u6587\u5316\u8BED\u5883\u586B\u5145\u5411\u91CF\n    const cultural = morpheme.cultural_context\n    vector[8] = cultural.traditionality\n    vector[9] = cultural.modernity\n    vector[10] = cultural.formality\n    vector[11] = cultural.regionality\n    vector[12] = cultural.religious_sensitivity\n    \n    // \u57FA\u4E8E\u8BED\u97F3\u7279\u5F81\u586B\u5145\u5411\u91CF\n    vector[13] = morpheme.phonetic_features.phonetic_harmony\n    vector[14] = morpheme.phonetic_features.syllable_count / 5 // \u5F52\u4E00\u5316\n    \n    // \u57FA\u4E8E\u4F7F\u7528\u9891\u7387\u548C\u8BC4\u5206\u586B\u5145\u5411\u91CF\n    vector[15] = morpheme.usage_frequency\n    vector[16] = morpheme.native_speaker_rating\n    vector[17] = morpheme.cultural_appropriateness\n    vector[18] = morpheme.popularity_trend\n    \n    // \u6700\u540E\u4E00\u4E2A\u7EF4\u5EA6\u7528\u4E8E\u8BED\u8A00\u7279\u5F02\u6027\n    vector[19] = this.getLanguageSpecificityScore(morpheme.language)\n    \n    return vector\n  }\n\n  /**\n   * \u83B7\u53D6\u8BED\u8A00\u7279\u5F02\u6027\u8BC4\u5206\n   */\n  private getLanguageSpecificityScore(language: LanguageCode): number {\n    const scores: Record<LanguageCode, number> = {\n      [LanguageCode.ZH_CN]: 0.9,\n      [LanguageCode.EN_US]: 0.8,\n      [LanguageCode.JA_JP]: 0.85,\n      [LanguageCode.KO_KR]: 0.85,\n      [LanguageCode.ES_ES]: 0.7,\n      [LanguageCode.FR_FR]: 0.7,\n      [LanguageCode.DE_DE]: 0.75,\n      [LanguageCode.AR_SA]: 0.9\n    }\n    return scores[language] || 0.5\n  }\n\n  /**\n   * \u8BA1\u7B97\u4F59\u5F26\u76F8\u4F3C\u5EA6\n   */\n  private calculateCosineSimilarity(vector1: number[], vector2: number[]): number {\n    if (vector1.length !== vector2.length) {\n      return 0\n    }\n\n    let dotProduct = 0\n    let norm1 = 0\n    let norm2 = 0\n\n    for (let i = 0; i < vector1.length; i++) {\n      dotProduct += vector1[i] * vector2[i]\n      norm1 += vector1[i] * vector1[i]\n      norm2 += vector2[i] * vector2[i]\n    }\n\n    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)\n    return magnitude > 0 ? dotProduct / magnitude : 0\n  }\n\n  /**\n   * \u57FA\u4E8E\u7C7B\u522B\u7684\u76F8\u4F3C\u5EA6\u8BA1\u7B97\n   */\n  private calculateCategoricalSimilarity(\n    conceptVector: UniversalSemanticVector,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // \u57FA\u4E8E\u6982\u5FF5\u7C7B\u522B\u548C\u8BED\u7D20\u7684\u8BCD\u6027\u6807\u6CE8\u8BA1\u7B97\u76F8\u4F3C\u5EA6\n    const posMapping: Record<string, string[]> = {\n      'emotions': ['ADJ', 'NOUN'],\n      'professions': ['NOUN'],\n      'characteristics': ['ADJ'],\n      'objects': ['NOUN'],\n      'actions': ['VERB'],\n      'concepts': ['NOUN', 'ADJ']\n    }\n\n    // \u4ECE\u6982\u5FF5\u5411\u91CF\u63A8\u65AD\u7C7B\u522B\uFF08\u7B80\u5316\u5B9E\u73B0\uFF09\n    const inferredCategory = this.inferCategoryFromVector(conceptVector.vector)\n    const expectedPOS = posMapping[inferredCategory] || ['NOUN']\n    \n    return expectedPOS.includes(morpheme.morphological_info.pos_tag) ? 0.8 : 0.4\n  }\n\n  /**\n   * \u4ECE\u8BED\u4E49\u5411\u91CF\u63A8\u65AD\u7C7B\u522B\n   */\n  private inferCategoryFromVector(vector: number[]): string {\n    // \u7B80\u5316\u7684\u7C7B\u522B\u63A8\u65AD\u903B\u8F91\n    const maxIndex = vector.indexOf(Math.max(...vector))\n    const categories = ['emotions', 'professions', 'characteristics', 'objects', 'actions', 'concepts']\n    return categories[maxIndex % categories.length] || 'concepts'\n  }\n\n  /**\n   * \u8BA1\u7B97\u6587\u5316\u9002\u914D\u5EA6\n   */\n  private calculateCulturalFit(\n    concept: UniversalConcept,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // 1. \u6587\u5316\u4E2D\u6027\u5EA6\u5339\u914D\n    const neutralityFit = 1 - Math.abs(concept.cultural_neutrality - 0.5) * 2\n\n    // 2. \u8DE8\u8BED\u8A00\u7A33\u5B9A\u6027\n    const stabilityBonus = concept.cross_lingual_stability * 0.3\n\n    // 3. \u8BED\u7D20\u7684\u6587\u5316\u9002\u5B9C\u6027\n    const appropriateness = morpheme.cultural_appropriateness\n\n    // 4. \u7EFC\u5408\u6587\u5316\u9002\u914D\u5EA6\n    return (neutralityFit * 0.4 + stabilityBonus + appropriateness * 0.3)\n  }\n\n  /**\n   * \u8BA1\u7B97\u8D28\u91CF\u4E00\u81F4\u6027\n   */\n  private calculateQualityConsistency(\n    concept: UniversalConcept,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // \u57FA\u4E8E\u6982\u5FF5\u7684\u8BA4\u77E5\u5C5E\u6027\u548C\u8BED\u7D20\u7684\u8D28\u91CF\u8BC4\u5206\u8BA1\u7B97\u4E00\u81F4\u6027\n    const conceptQuality = concept.cognitive_attributes.memorability\n    const morphemeQuality = morpheme.native_speaker_rating\n    \n    // \u8D28\u91CF\u5206\u6570\u7684\u4E00\u81F4\u6027\uFF081 - \u5DEE\u503C\u7684\u7EDD\u5BF9\u503C\uFF09\n    return Math.max(0, 1 - Math.abs(conceptQuality - morphemeQuality))\n  }\n\n  /**\n   * \u8BA1\u7B97\u5BF9\u9F50\u7F6E\u4FE1\u5EA6\n   */\n  private calculateAlignmentConfidence(\n    semanticSimilarity: number,\n    culturalFit: number,\n    qualityConsistency: number\n  ): number {\n    // \u57FA\u4E8E\u5404\u9879\u6307\u6807\u7684\u65B9\u5DEE\u8BA1\u7B97\u7F6E\u4FE1\u5EA6\n    const scores = [semanticSimilarity, culturalFit, qualityConsistency]\n    const mean = scores.reduce((a, b) => a + b, 0) / scores.length\n    const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length\n    \n    // \u65B9\u5DEE\u8D8A\u5C0F\uFF0C\u7F6E\u4FE1\u5EA6\u8D8A\u9AD8\n    return Math.max(0, 1 - variance)\n  }\n\n  /**\n   * \u4E3A\u6982\u5FF5\u627E\u5230\u6700\u4F73\u8BED\u8A00\u5BF9\u9F50\n   */\n  findBestAlignment(\n    concept: UniversalConcept,\n    candidateMorphemes: LanguageSpecificMorpheme[]\n  ): SemanticAlignment | null {\n    if (candidateMorphemes.length === 0) {\n      return null\n    }\n\n    let bestAlignment: SemanticAlignment | null = null\n    let bestScore = 0\n\n    for (const morpheme of candidateMorphemes) {\n      const alignment = this.calculateConceptMorphemeAlignment(concept, morpheme)\n      \n      if (alignment.alignmentScore > bestScore && \n          alignment.semanticSimilarity >= this.config.semanticSimilarityThreshold) {\n        bestScore = alignment.alignmentScore\n        bestAlignment = alignment\n      }\n    }\n\n    return bestAlignment\n  }\n\n  /**\n   * \u6784\u5EFA\u8DE8\u8BED\u8A00\u6620\u5C04\n   */\n  buildCrossLingualMapping(\n    concept: UniversalConcept,\n    languageMorphemes: Map<LanguageCode, LanguageSpecificMorpheme[]>\n  ): CrossLingualMapping {\n    const alignments = new Map<LanguageCode, SemanticAlignment>()\n    \n    // \u4E3A\u6BCF\u79CD\u8BED\u8A00\u627E\u5230\u6700\u4F73\u5BF9\u9F50\n    for (const [language, morphemes] of languageMorphemes) {\n      const bestAlignment = this.findBestAlignment(concept, morphemes)\n      if (bestAlignment) {\n        alignments.set(language, bestAlignment)\n      }\n    }\n\n    // \u8BA1\u7B97\u8DE8\u8BED\u8A00\u4E00\u81F4\u6027\n    const consistencyScore = this.calculateCrossLingualConsistency(alignments)\n\n    // \u627E\u5230\u6700\u4F73\u5BF9\u9F50\u8BED\u8A00\u5BF9\n    const bestAlignments = this.findBestLanguagePairs(alignments)\n\n    return {\n      conceptId: concept.concept_id,\n      languageMorphemes,\n      consistencyScore,\n      bestAlignments\n    }\n  }\n\n  /**\n   * \u8BA1\u7B97\u8DE8\u8BED\u8A00\u4E00\u81F4\u6027\n   */\n  private calculateCrossLingualConsistency(\n    alignments: Map<LanguageCode, SemanticAlignment>\n  ): number {\n    const scores = Array.from(alignments.values()).map(a => a.alignmentScore)\n    if (scores.length < 2) return 1.0\n\n    const mean = scores.reduce((a, b) => a + b, 0) / scores.length\n    const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length\n    \n    // \u4E00\u81F4\u6027 = \u5E73\u5747\u5206\u6570 * (1 - \u6807\u51C6\u5316\u65B9\u5DEE)\n    return mean * (1 - Math.min(variance, 1))\n  }\n\n  /**\n   * \u627E\u5230\u6700\u4F73\u8BED\u8A00\u5BF9\u9F50\u5BF9\n   */\n  private findBestLanguagePairs(\n    alignments: Map<LanguageCode, SemanticAlignment>\n  ): Array<{ language1: LanguageCode; language2: LanguageCode; score: number }> {\n    const languages = Array.from(alignments.keys())\n    const pairs: Array<{ language1: LanguageCode; language2: LanguageCode; score: number }> = []\n\n    for (let i = 0; i < languages.length; i++) {\n      for (let j = i + 1; j < languages.length; j++) {\n        const lang1 = languages[i]\n        const lang2 = languages[j]\n        const alignment1 = alignments.get(lang1)!\n        const alignment2 = alignments.get(lang2)!\n        \n        // \u8BA1\u7B97\u8BED\u8A00\u5BF9\u7684\u5BF9\u9F50\u5206\u6570\n        const pairScore = (alignment1.alignmentScore + alignment2.alignmentScore) / 2\n        pairs.push({ language1: lang1, language2: lang2, score: pairScore })\n      }\n    }\n\n    // \u6309\u5206\u6570\u964D\u5E8F\u6392\u5E8F\n    return pairs.sort((a, b) => b.score - a.score)\n  }\n\n  /**\n   * \u66F4\u65B0\u914D\u7F6E\n   */\n  updateConfig(newConfig: Partial<SemanticAlignerConfig>): void {\n    this.config = { ...this.config, ...newConfig }\n  }\n\n  /**\n   * \u83B7\u53D6\u5F53\u524D\u914D\u7F6E\n   */\n  getConfig(): SemanticAlignerConfig {\n    return { ...this.config }\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9da2722a2b8535716240c22a6de646d541c51e70"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_s721x8a1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_s721x8a1();
/**
 * 语义对齐器
 *
 * 负责跨语言语义对齐、概念映射、语义相似度计算等功能
 * 确保多语种生成的语义一致性和文化适配性
 *
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */
import { LanguageCode, SEMANTIC_VECTOR_DIMENSIONS } from '../../types/multilingual.js';
// ============================================================================
// 语义对齐器类
// ============================================================================
/**
 * 语义对齐器类
 *
 * 提供跨语言语义对齐和概念映射功能
 */
export class SemanticAligner {
  config;
  /**
   * 构造函数
   */
  constructor(config =
  /* istanbul ignore next */
  (cov_s721x8a1().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_s721x8a1().f[0]++;
    cov_s721x8a1().s[0]++;
    this.config = {
      semanticSimilarityThreshold:
      /* istanbul ignore next */
      (cov_s721x8a1().b[1][0]++, config.semanticSimilarityThreshold) ||
      /* istanbul ignore next */
      (cov_s721x8a1().b[1][1]++, 0.75),
      culturalFitWeight:
      /* istanbul ignore next */
      (cov_s721x8a1().b[2][0]++, config.culturalFitWeight) ||
      /* istanbul ignore next */
      (cov_s721x8a1().b[2][1]++, 0.3),
      semanticWeight:
      /* istanbul ignore next */
      (cov_s721x8a1().b[3][0]++, config.semanticWeight) ||
      /* istanbul ignore next */
      (cov_s721x8a1().b[3][1]++, 0.5),
      qualityWeight:
      /* istanbul ignore next */
      (cov_s721x8a1().b[4][0]++, config.qualityWeight) ||
      /* istanbul ignore next */
      (cov_s721x8a1().b[4][1]++, 0.2)
    };
  }
  /**
   * 计算概念与语素的语义对齐度
   */
  calculateConceptMorphemeAlignment(concept, morpheme) {
    /* istanbul ignore next */
    cov_s721x8a1().f[1]++;
    // 1. 语义相似度计算
    const semanticSimilarity =
    /* istanbul ignore next */
    (cov_s721x8a1().s[1]++, this.calculateSemanticSimilarity(concept.semantic_vector, morpheme));
    // 2. 文化适配度计算
    const culturalFit =
    /* istanbul ignore next */
    (cov_s721x8a1().s[2]++, this.calculateCulturalFit(concept, morpheme));
    // 3. 质量一致性计算
    const qualityConsistency =
    /* istanbul ignore next */
    (cov_s721x8a1().s[3]++, this.calculateQualityConsistency(concept, morpheme));
    // 4. 综合对齐分数
    const alignmentScore =
    /* istanbul ignore next */
    (cov_s721x8a1().s[4]++, this.config.semanticWeight * semanticSimilarity + this.config.culturalFitWeight * culturalFit + this.config.qualityWeight * qualityConsistency);
    // 5. 计算置信度
    const confidence =
    /* istanbul ignore next */
    (cov_s721x8a1().s[5]++, this.calculateAlignmentConfidence(semanticSimilarity, culturalFit, qualityConsistency));
    /* istanbul ignore next */
    cov_s721x8a1().s[6]++;
    return {
      sourceConceptId: concept.concept_id,
      targetLanguage: morpheme.language,
      alignedMorphemes: [morpheme],
      alignmentScore,
      semanticSimilarity,
      culturalFit,
      confidence
    };
  }
  /**
   * 计算语义相似度
   */
  calculateSemanticSimilarity(conceptVector, morpheme) {
    /* istanbul ignore next */
    cov_s721x8a1().f[2]++;
    cov_s721x8a1().s[7]++;
    // 如果语素没有语义向量，使用基于类别的相似度
    if (!morpheme.phonetic_features) {
      /* istanbul ignore next */
      cov_s721x8a1().b[5][0]++;
      cov_s721x8a1().s[8]++;
      return this.calculateCategoricalSimilarity(conceptVector, morpheme);
    } else
    /* istanbul ignore next */
    {
      cov_s721x8a1().b[5][1]++;
    }
    // 获取适配的概念向量
    const adaptedConceptVector =
    /* istanbul ignore next */
    (cov_s721x8a1().s[9]++, this.adaptConceptVector(conceptVector));
    const morphemeVector =
    /* istanbul ignore next */
    (cov_s721x8a1().s[10]++, this.extractMorphemeSemanticVector(morpheme));
    // 使用余弦相似度计算语义向量相似度
    /* istanbul ignore next */
    cov_s721x8a1().s[11]++;
    return this.calculateCosineSimilarity(adaptedConceptVector, morphemeVector);
  }
  /**
   * 适配概念向量到兼容维度
   */
  adaptConceptVector(conceptVector) {
    /* istanbul ignore next */
    cov_s721x8a1().f[3]++;
    cov_s721x8a1().s[12]++;
    // 如果有兼容性向量，直接使用
    if (
    /* istanbul ignore next */
    (cov_s721x8a1().b[7][0]++, conceptVector.legacy_vector) &&
    /* istanbul ignore next */
    (cov_s721x8a1().b[7][1]++, conceptVector.legacy_vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY)) {
      /* istanbul ignore next */
      cov_s721x8a1().b[6][0]++;
      cov_s721x8a1().s[13]++;
      return conceptVector.legacy_vector;
    } else
    /* istanbul ignore next */
    {
      cov_s721x8a1().b[6][1]++;
    }
    // 否则从高维向量降维到兼容维度
    const targetDim =
    /* istanbul ignore next */
    (cov_s721x8a1().s[14]++, SEMANTIC_VECTOR_DIMENSIONS.LEGACY);
    const sourceDim =
    /* istanbul ignore next */
    (cov_s721x8a1().s[15]++, conceptVector.vector.length);
    /* istanbul ignore next */
    cov_s721x8a1().s[16]++;
    if (sourceDim === targetDim) {
      /* istanbul ignore next */
      cov_s721x8a1().b[8][0]++;
      cov_s721x8a1().s[17]++;
      return conceptVector.vector;
    } else
    /* istanbul ignore next */
    {
      cov_s721x8a1().b[8][1]++;
    }
    // 简单的降维策略：平均池化
    const adapted =
    /* istanbul ignore next */
    (cov_s721x8a1().s[18]++, new Array(targetDim).fill(0));
    const poolSize =
    /* istanbul ignore next */
    (cov_s721x8a1().s[19]++, Math.floor(sourceDim / targetDim));
    /* istanbul ignore next */
    cov_s721x8a1().s[20]++;
    for (let i =
    /* istanbul ignore next */
    (cov_s721x8a1().s[21]++, 0); i < targetDim; i++) {
      let sum =
      /* istanbul ignore next */
      (cov_s721x8a1().s[22]++, 0);
      const start =
      /* istanbul ignore next */
      (cov_s721x8a1().s[23]++, i * poolSize);
      const end =
      /* istanbul ignore next */
      (cov_s721x8a1().s[24]++, Math.min(start + poolSize, sourceDim));
      /* istanbul ignore next */
      cov_s721x8a1().s[25]++;
      for (let j =
      /* istanbul ignore next */
      (cov_s721x8a1().s[26]++, start); j < end; j++) {
        /* istanbul ignore next */
        cov_s721x8a1().s[27]++;
        sum += conceptVector.vector[j];
      }
      /* istanbul ignore next */
      cov_s721x8a1().s[28]++;
      adapted[i] = sum / (end - start);
    }
    /* istanbul ignore next */
    cov_s721x8a1().s[29]++;
    return adapted;
  }
  /**
   * 提取语素的语义向量
   */
  extractMorphemeSemanticVector(morpheme) {
    /* istanbul ignore next */
    cov_s721x8a1().f[4]++;
    // 基于语素的各种特征构建语义向量
    const vectorDim =
    /* istanbul ignore next */
    (cov_s721x8a1().s[30]++, SEMANTIC_VECTOR_DIMENSIONS.LEGACY); // 使用兼容性向量维度
    const vector =
    /* istanbul ignore next */
    (cov_s721x8a1().s[31]++, new Array(vectorDim).fill(0));
    // 基于质量评分填充向量
    const qualityScores =
    /* istanbul ignore next */
    (cov_s721x8a1().s[32]++, morpheme.language_quality_scores);
    /* istanbul ignore next */
    cov_s721x8a1().s[33]++;
    vector[0] = qualityScores.naturalness;
    /* istanbul ignore next */
    cov_s721x8a1().s[34]++;
    vector[1] = qualityScores.fluency;
    /* istanbul ignore next */
    cov_s721x8a1().s[35]++;
    vector[2] = qualityScores.authenticity;
    /* istanbul ignore next */
    cov_s721x8a1().s[36]++;
    vector[3] = qualityScores.aesthetic_appeal;
    /* istanbul ignore next */
    cov_s721x8a1().s[37]++;
    vector[4] = qualityScores.pronunciation_ease;
    /* istanbul ignore next */
    cov_s721x8a1().s[38]++;
    vector[5] = qualityScores.memorability;
    /* istanbul ignore next */
    cov_s721x8a1().s[39]++;
    vector[6] = qualityScores.uniqueness;
    /* istanbul ignore next */
    cov_s721x8a1().s[40]++;
    vector[7] = qualityScores.practicality;
    // 基于文化语境填充向量
    const cultural =
    /* istanbul ignore next */
    (cov_s721x8a1().s[41]++, morpheme.cultural_context);
    /* istanbul ignore next */
    cov_s721x8a1().s[42]++;
    vector[8] = cultural.traditionality;
    /* istanbul ignore next */
    cov_s721x8a1().s[43]++;
    vector[9] = cultural.modernity;
    /* istanbul ignore next */
    cov_s721x8a1().s[44]++;
    vector[10] = cultural.formality;
    /* istanbul ignore next */
    cov_s721x8a1().s[45]++;
    vector[11] = cultural.regionality;
    /* istanbul ignore next */
    cov_s721x8a1().s[46]++;
    vector[12] = cultural.religious_sensitivity;
    // 基于语音特征填充向量
    /* istanbul ignore next */
    cov_s721x8a1().s[47]++;
    vector[13] = morpheme.phonetic_features.phonetic_harmony;
    /* istanbul ignore next */
    cov_s721x8a1().s[48]++;
    vector[14] = morpheme.phonetic_features.syllable_count / 5; // 归一化
    // 基于使用频率和评分填充向量
    /* istanbul ignore next */
    cov_s721x8a1().s[49]++;
    vector[15] = morpheme.usage_frequency;
    /* istanbul ignore next */
    cov_s721x8a1().s[50]++;
    vector[16] = morpheme.native_speaker_rating;
    /* istanbul ignore next */
    cov_s721x8a1().s[51]++;
    vector[17] = morpheme.cultural_appropriateness;
    /* istanbul ignore next */
    cov_s721x8a1().s[52]++;
    vector[18] = morpheme.popularity_trend;
    // 最后一个维度用于语言特异性
    /* istanbul ignore next */
    cov_s721x8a1().s[53]++;
    vector[19] = this.getLanguageSpecificityScore(morpheme.language);
    /* istanbul ignore next */
    cov_s721x8a1().s[54]++;
    return vector;
  }
  /**
   * 获取语言特异性评分
   */
  getLanguageSpecificityScore(language) {
    /* istanbul ignore next */
    cov_s721x8a1().f[5]++;
    const scores =
    /* istanbul ignore next */
    (cov_s721x8a1().s[55]++, {
      [LanguageCode.ZH_CN]: 0.9,
      [LanguageCode.EN_US]: 0.8,
      [LanguageCode.JA_JP]: 0.85,
      [LanguageCode.KO_KR]: 0.85,
      [LanguageCode.ES_ES]: 0.7,
      [LanguageCode.FR_FR]: 0.7,
      [LanguageCode.DE_DE]: 0.75,
      [LanguageCode.AR_SA]: 0.9
    });
    /* istanbul ignore next */
    cov_s721x8a1().s[56]++;
    return /* istanbul ignore next */(cov_s721x8a1().b[9][0]++, scores[language]) ||
    /* istanbul ignore next */
    (cov_s721x8a1().b[9][1]++, 0.5);
  }
  /**
   * 计算余弦相似度
   */
  calculateCosineSimilarity(vector1, vector2) {
    /* istanbul ignore next */
    cov_s721x8a1().f[6]++;
    cov_s721x8a1().s[57]++;
    if (vector1.length !== vector2.length) {
      /* istanbul ignore next */
      cov_s721x8a1().b[10][0]++;
      cov_s721x8a1().s[58]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_s721x8a1().b[10][1]++;
    }
    let dotProduct =
    /* istanbul ignore next */
    (cov_s721x8a1().s[59]++, 0);
    let norm1 =
    /* istanbul ignore next */
    (cov_s721x8a1().s[60]++, 0);
    let norm2 =
    /* istanbul ignore next */
    (cov_s721x8a1().s[61]++, 0);
    /* istanbul ignore next */
    cov_s721x8a1().s[62]++;
    for (let i =
    /* istanbul ignore next */
    (cov_s721x8a1().s[63]++, 0); i < vector1.length; i++) {
      /* istanbul ignore next */
      cov_s721x8a1().s[64]++;
      dotProduct += vector1[i] * vector2[i];
      /* istanbul ignore next */
      cov_s721x8a1().s[65]++;
      norm1 += vector1[i] * vector1[i];
      /* istanbul ignore next */
      cov_s721x8a1().s[66]++;
      norm2 += vector2[i] * vector2[i];
    }
    const magnitude =
    /* istanbul ignore next */
    (cov_s721x8a1().s[67]++, Math.sqrt(norm1) * Math.sqrt(norm2));
    /* istanbul ignore next */
    cov_s721x8a1().s[68]++;
    return magnitude > 0 ?
    /* istanbul ignore next */
    (cov_s721x8a1().b[11][0]++, dotProduct / magnitude) :
    /* istanbul ignore next */
    (cov_s721x8a1().b[11][1]++, 0);
  }
  /**
   * 基于类别的相似度计算
   */
  calculateCategoricalSimilarity(conceptVector, morpheme) {
    /* istanbul ignore next */
    cov_s721x8a1().f[7]++;
    // 基于概念类别和语素的词性标注计算相似度
    const posMapping =
    /* istanbul ignore next */
    (cov_s721x8a1().s[69]++, {
      'emotions': ['ADJ', 'NOUN'],
      'professions': ['NOUN'],
      'characteristics': ['ADJ'],
      'objects': ['NOUN'],
      'actions': ['VERB'],
      'concepts': ['NOUN', 'ADJ']
    });
    // 从概念向量推断类别（简化实现）
    const inferredCategory =
    /* istanbul ignore next */
    (cov_s721x8a1().s[70]++, this.inferCategoryFromVector(conceptVector.vector));
    const expectedPOS =
    /* istanbul ignore next */
    (cov_s721x8a1().s[71]++,
    /* istanbul ignore next */
    (cov_s721x8a1().b[12][0]++, posMapping[inferredCategory]) ||
    /* istanbul ignore next */
    (cov_s721x8a1().b[12][1]++, ['NOUN']));
    /* istanbul ignore next */
    cov_s721x8a1().s[72]++;
    return expectedPOS.includes(morpheme.morphological_info.pos_tag) ?
    /* istanbul ignore next */
    (cov_s721x8a1().b[13][0]++, 0.8) :
    /* istanbul ignore next */
    (cov_s721x8a1().b[13][1]++, 0.4);
  }
  /**
   * 从语义向量推断类别
   */
  inferCategoryFromVector(vector) {
    /* istanbul ignore next */
    cov_s721x8a1().f[8]++;
    // 简化的类别推断逻辑
    const maxIndex =
    /* istanbul ignore next */
    (cov_s721x8a1().s[73]++, vector.indexOf(Math.max(...vector)));
    const categories =
    /* istanbul ignore next */
    (cov_s721x8a1().s[74]++, ['emotions', 'professions', 'characteristics', 'objects', 'actions', 'concepts']);
    /* istanbul ignore next */
    cov_s721x8a1().s[75]++;
    return /* istanbul ignore next */(cov_s721x8a1().b[14][0]++, categories[maxIndex % categories.length]) ||
    /* istanbul ignore next */
    (cov_s721x8a1().b[14][1]++, 'concepts');
  }
  /**
   * 计算文化适配度
   */
  calculateCulturalFit(concept, morpheme) {
    /* istanbul ignore next */
    cov_s721x8a1().f[9]++;
    // 1. 文化中性度匹配
    const neutralityFit =
    /* istanbul ignore next */
    (cov_s721x8a1().s[76]++, 1 - Math.abs(concept.cultural_neutrality - 0.5) * 2);
    // 2. 跨语言稳定性
    const stabilityBonus =
    /* istanbul ignore next */
    (cov_s721x8a1().s[77]++, concept.cross_lingual_stability * 0.3);
    // 3. 语素的文化适宜性
    const appropriateness =
    /* istanbul ignore next */
    (cov_s721x8a1().s[78]++, morpheme.cultural_appropriateness);
    // 4. 综合文化适配度
    /* istanbul ignore next */
    cov_s721x8a1().s[79]++;
    return neutralityFit * 0.4 + stabilityBonus + appropriateness * 0.3;
  }
  /**
   * 计算质量一致性
   */
  calculateQualityConsistency(concept, morpheme) {
    /* istanbul ignore next */
    cov_s721x8a1().f[10]++;
    // 基于概念的认知属性和语素的质量评分计算一致性
    const conceptQuality =
    /* istanbul ignore next */
    (cov_s721x8a1().s[80]++, concept.cognitive_attributes.memorability);
    const morphemeQuality =
    /* istanbul ignore next */
    (cov_s721x8a1().s[81]++, morpheme.native_speaker_rating);
    // 质量分数的一致性（1 - 差值的绝对值）
    /* istanbul ignore next */
    cov_s721x8a1().s[82]++;
    return Math.max(0, 1 - Math.abs(conceptQuality - morphemeQuality));
  }
  /**
   * 计算对齐置信度
   */
  calculateAlignmentConfidence(semanticSimilarity, culturalFit, qualityConsistency) {
    /* istanbul ignore next */
    cov_s721x8a1().f[11]++;
    // 基于各项指标的方差计算置信度
    const scores =
    /* istanbul ignore next */
    (cov_s721x8a1().s[83]++, [semanticSimilarity, culturalFit, qualityConsistency]);
    const mean =
    /* istanbul ignore next */
    (cov_s721x8a1().s[84]++, scores.reduce((a, b) => {
      /* istanbul ignore next */
      cov_s721x8a1().f[12]++;
      cov_s721x8a1().s[85]++;
      return a + b;
    }, 0) / scores.length);
    const variance =
    /* istanbul ignore next */
    (cov_s721x8a1().s[86]++, scores.reduce((acc, score) => {
      /* istanbul ignore next */
      cov_s721x8a1().f[13]++;
      cov_s721x8a1().s[87]++;
      return acc + Math.pow(score - mean, 2);
    }, 0) / scores.length);
    // 方差越小，置信度越高
    /* istanbul ignore next */
    cov_s721x8a1().s[88]++;
    return Math.max(0, 1 - variance);
  }
  /**
   * 为概念找到最佳语言对齐
   */
  findBestAlignment(concept, candidateMorphemes) {
    /* istanbul ignore next */
    cov_s721x8a1().f[14]++;
    cov_s721x8a1().s[89]++;
    if (candidateMorphemes.length === 0) {
      /* istanbul ignore next */
      cov_s721x8a1().b[15][0]++;
      cov_s721x8a1().s[90]++;
      return null;
    } else
    /* istanbul ignore next */
    {
      cov_s721x8a1().b[15][1]++;
    }
    let bestAlignment =
    /* istanbul ignore next */
    (cov_s721x8a1().s[91]++, null);
    let bestScore =
    /* istanbul ignore next */
    (cov_s721x8a1().s[92]++, 0);
    /* istanbul ignore next */
    cov_s721x8a1().s[93]++;
    for (const morpheme of candidateMorphemes) {
      const alignment =
      /* istanbul ignore next */
      (cov_s721x8a1().s[94]++, this.calculateConceptMorphemeAlignment(concept, morpheme));
      /* istanbul ignore next */
      cov_s721x8a1().s[95]++;
      if (
      /* istanbul ignore next */
      (cov_s721x8a1().b[17][0]++, alignment.alignmentScore > bestScore) &&
      /* istanbul ignore next */
      (cov_s721x8a1().b[17][1]++, alignment.semanticSimilarity >= this.config.semanticSimilarityThreshold)) {
        /* istanbul ignore next */
        cov_s721x8a1().b[16][0]++;
        cov_s721x8a1().s[96]++;
        bestScore = alignment.alignmentScore;
        /* istanbul ignore next */
        cov_s721x8a1().s[97]++;
        bestAlignment = alignment;
      } else
      /* istanbul ignore next */
      {
        cov_s721x8a1().b[16][1]++;
      }
    }
    /* istanbul ignore next */
    cov_s721x8a1().s[98]++;
    return bestAlignment;
  }
  /**
   * 构建跨语言映射
   */
  buildCrossLingualMapping(concept, languageMorphemes) {
    /* istanbul ignore next */
    cov_s721x8a1().f[15]++;
    const alignments =
    /* istanbul ignore next */
    (cov_s721x8a1().s[99]++, new Map());
    // 为每种语言找到最佳对齐
    /* istanbul ignore next */
    cov_s721x8a1().s[100]++;
    for (const [language, morphemes] of languageMorphemes) {
      const bestAlignment =
      /* istanbul ignore next */
      (cov_s721x8a1().s[101]++, this.findBestAlignment(concept, morphemes));
      /* istanbul ignore next */
      cov_s721x8a1().s[102]++;
      if (bestAlignment) {
        /* istanbul ignore next */
        cov_s721x8a1().b[18][0]++;
        cov_s721x8a1().s[103]++;
        alignments.set(language, bestAlignment);
      } else
      /* istanbul ignore next */
      {
        cov_s721x8a1().b[18][1]++;
      }
    }
    // 计算跨语言一致性
    const consistencyScore =
    /* istanbul ignore next */
    (cov_s721x8a1().s[104]++, this.calculateCrossLingualConsistency(alignments));
    // 找到最佳对齐语言对
    const bestAlignments =
    /* istanbul ignore next */
    (cov_s721x8a1().s[105]++, this.findBestLanguagePairs(alignments));
    /* istanbul ignore next */
    cov_s721x8a1().s[106]++;
    return {
      conceptId: concept.concept_id,
      languageMorphemes,
      consistencyScore,
      bestAlignments
    };
  }
  /**
   * 计算跨语言一致性
   */
  calculateCrossLingualConsistency(alignments) {
    /* istanbul ignore next */
    cov_s721x8a1().f[16]++;
    const scores =
    /* istanbul ignore next */
    (cov_s721x8a1().s[107]++, Array.from(alignments.values()).map(a => {
      /* istanbul ignore next */
      cov_s721x8a1().f[17]++;
      cov_s721x8a1().s[108]++;
      return a.alignmentScore;
    }));
    /* istanbul ignore next */
    cov_s721x8a1().s[109]++;
    if (scores.length < 2) {
      /* istanbul ignore next */
      cov_s721x8a1().b[19][0]++;
      cov_s721x8a1().s[110]++;
      return 1.0;
    } else
    /* istanbul ignore next */
    {
      cov_s721x8a1().b[19][1]++;
    }
    const mean =
    /* istanbul ignore next */
    (cov_s721x8a1().s[111]++, scores.reduce((a, b) => {
      /* istanbul ignore next */
      cov_s721x8a1().f[18]++;
      cov_s721x8a1().s[112]++;
      return a + b;
    }, 0) / scores.length);
    const variance =
    /* istanbul ignore next */
    (cov_s721x8a1().s[113]++, scores.reduce((acc, score) => {
      /* istanbul ignore next */
      cov_s721x8a1().f[19]++;
      cov_s721x8a1().s[114]++;
      return acc + Math.pow(score - mean, 2);
    }, 0) / scores.length);
    // 一致性 = 平均分数 * (1 - 标准化方差)
    /* istanbul ignore next */
    cov_s721x8a1().s[115]++;
    return mean * (1 - Math.min(variance, 1));
  }
  /**
   * 找到最佳语言对齐对
   */
  findBestLanguagePairs(alignments) {
    /* istanbul ignore next */
    cov_s721x8a1().f[20]++;
    const languages =
    /* istanbul ignore next */
    (cov_s721x8a1().s[116]++, Array.from(alignments.keys()));
    const pairs =
    /* istanbul ignore next */
    (cov_s721x8a1().s[117]++, []);
    /* istanbul ignore next */
    cov_s721x8a1().s[118]++;
    for (let i =
    /* istanbul ignore next */
    (cov_s721x8a1().s[119]++, 0); i < languages.length; i++) {
      /* istanbul ignore next */
      cov_s721x8a1().s[120]++;
      for (let j =
      /* istanbul ignore next */
      (cov_s721x8a1().s[121]++, i + 1); j < languages.length; j++) {
        const lang1 =
        /* istanbul ignore next */
        (cov_s721x8a1().s[122]++, languages[i]);
        const lang2 =
        /* istanbul ignore next */
        (cov_s721x8a1().s[123]++, languages[j]);
        const alignment1 =
        /* istanbul ignore next */
        (cov_s721x8a1().s[124]++, alignments.get(lang1));
        const alignment2 =
        /* istanbul ignore next */
        (cov_s721x8a1().s[125]++, alignments.get(lang2));
        // 计算语言对的对齐分数
        const pairScore =
        /* istanbul ignore next */
        (cov_s721x8a1().s[126]++, (alignment1.alignmentScore + alignment2.alignmentScore) / 2);
        /* istanbul ignore next */
        cov_s721x8a1().s[127]++;
        pairs.push({
          language1: lang1,
          language2: lang2,
          score: pairScore
        });
      }
    }
    // 按分数降序排序
    /* istanbul ignore next */
    cov_s721x8a1().s[128]++;
    return pairs.sort((a, b) => {
      /* istanbul ignore next */
      cov_s721x8a1().f[21]++;
      cov_s721x8a1().s[129]++;
      return b.score - a.score;
    });
  }
  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    /* istanbul ignore next */
    cov_s721x8a1().f[22]++;
    cov_s721x8a1().s[130]++;
    this.config = {
      ...this.config,
      ...newConfig
    };
  }
  /**
   * 获取当前配置
   */
  getConfig() {
    /* istanbul ignore next */
    cov_s721x8a1().f[23]++;
    cov_s721x8a1().s[131]++;
    return {
      ...this.config
    };
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJMYW5ndWFnZUNvZGUiLCJTRU1BTlRJQ19WRUNUT1JfRElNRU5TSU9OUyIsIlNlbWFudGljQWxpZ25lciIsImNvbmZpZyIsImNvbnN0cnVjdG9yIiwiY292X3M3MjF4OGExIiwiYiIsImYiLCJzIiwic2VtYW50aWNTaW1pbGFyaXR5VGhyZXNob2xkIiwiY3VsdHVyYWxGaXRXZWlnaHQiLCJzZW1hbnRpY1dlaWdodCIsInF1YWxpdHlXZWlnaHQiLCJjYWxjdWxhdGVDb25jZXB0TW9ycGhlbWVBbGlnbm1lbnQiLCJjb25jZXB0IiwibW9ycGhlbWUiLCJzZW1hbnRpY1NpbWlsYXJpdHkiLCJjYWxjdWxhdGVTZW1hbnRpY1NpbWlsYXJpdHkiLCJzZW1hbnRpY192ZWN0b3IiLCJjdWx0dXJhbEZpdCIsImNhbGN1bGF0ZUN1bHR1cmFsRml0IiwicXVhbGl0eUNvbnNpc3RlbmN5IiwiY2FsY3VsYXRlUXVhbGl0eUNvbnNpc3RlbmN5IiwiYWxpZ25tZW50U2NvcmUiLCJjb25maWRlbmNlIiwiY2FsY3VsYXRlQWxpZ25tZW50Q29uZmlkZW5jZSIsInNvdXJjZUNvbmNlcHRJZCIsImNvbmNlcHRfaWQiLCJ0YXJnZXRMYW5ndWFnZSIsImxhbmd1YWdlIiwiYWxpZ25lZE1vcnBoZW1lcyIsImNvbmNlcHRWZWN0b3IiLCJwaG9uZXRpY19mZWF0dXJlcyIsImNhbGN1bGF0ZUNhdGVnb3JpY2FsU2ltaWxhcml0eSIsImFkYXB0ZWRDb25jZXB0VmVjdG9yIiwiYWRhcHRDb25jZXB0VmVjdG9yIiwibW9ycGhlbWVWZWN0b3IiLCJleHRyYWN0TW9ycGhlbWVTZW1hbnRpY1ZlY3RvciIsImNhbGN1bGF0ZUNvc2luZVNpbWlsYXJpdHkiLCJsZWdhY3lfdmVjdG9yIiwibGVuZ3RoIiwiTEVHQUNZIiwidGFyZ2V0RGltIiwic291cmNlRGltIiwidmVjdG9yIiwiYWRhcHRlZCIsIkFycmF5IiwiZmlsbCIsInBvb2xTaXplIiwiTWF0aCIsImZsb29yIiwiaSIsInN1bSIsInN0YXJ0IiwiZW5kIiwibWluIiwiaiIsInZlY3RvckRpbSIsInF1YWxpdHlTY29yZXMiLCJsYW5ndWFnZV9xdWFsaXR5X3Njb3JlcyIsIm5hdHVyYWxuZXNzIiwiZmx1ZW5jeSIsImF1dGhlbnRpY2l0eSIsImFlc3RoZXRpY19hcHBlYWwiLCJwcm9udW5jaWF0aW9uX2Vhc2UiLCJtZW1vcmFiaWxpdHkiLCJ1bmlxdWVuZXNzIiwicHJhY3RpY2FsaXR5IiwiY3VsdHVyYWwiLCJjdWx0dXJhbF9jb250ZXh0IiwidHJhZGl0aW9uYWxpdHkiLCJtb2Rlcm5pdHkiLCJmb3JtYWxpdHkiLCJyZWdpb25hbGl0eSIsInJlbGlnaW91c19zZW5zaXRpdml0eSIsInBob25ldGljX2hhcm1vbnkiLCJzeWxsYWJsZV9jb3VudCIsInVzYWdlX2ZyZXF1ZW5jeSIsIm5hdGl2ZV9zcGVha2VyX3JhdGluZyIsImN1bHR1cmFsX2FwcHJvcHJpYXRlbmVzcyIsInBvcHVsYXJpdHlfdHJlbmQiLCJnZXRMYW5ndWFnZVNwZWNpZmljaXR5U2NvcmUiLCJzY29yZXMiLCJaSF9DTiIsIkVOX1VTIiwiSkFfSlAiLCJLT19LUiIsIkVTX0VTIiwiRlJfRlIiLCJERV9ERSIsIkFSX1NBIiwidmVjdG9yMSIsInZlY3RvcjIiLCJkb3RQcm9kdWN0Iiwibm9ybTEiLCJub3JtMiIsIm1hZ25pdHVkZSIsInNxcnQiLCJwb3NNYXBwaW5nIiwiaW5mZXJyZWRDYXRlZ29yeSIsImluZmVyQ2F0ZWdvcnlGcm9tVmVjdG9yIiwiZXhwZWN0ZWRQT1MiLCJpbmNsdWRlcyIsIm1vcnBob2xvZ2ljYWxfaW5mbyIsInBvc190YWciLCJtYXhJbmRleCIsImluZGV4T2YiLCJtYXgiLCJjYXRlZ29yaWVzIiwibmV1dHJhbGl0eUZpdCIsImFicyIsImN1bHR1cmFsX25ldXRyYWxpdHkiLCJzdGFiaWxpdHlCb251cyIsImNyb3NzX2xpbmd1YWxfc3RhYmlsaXR5IiwiYXBwcm9wcmlhdGVuZXNzIiwiY29uY2VwdFF1YWxpdHkiLCJjb2duaXRpdmVfYXR0cmlidXRlcyIsIm1vcnBoZW1lUXVhbGl0eSIsIm1lYW4iLCJyZWR1Y2UiLCJhIiwidmFyaWFuY2UiLCJhY2MiLCJzY29yZSIsInBvdyIsImZpbmRCZXN0QWxpZ25tZW50IiwiY2FuZGlkYXRlTW9ycGhlbWVzIiwiYmVzdEFsaWdubWVudCIsImJlc3RTY29yZSIsImFsaWdubWVudCIsImJ1aWxkQ3Jvc3NMaW5ndWFsTWFwcGluZyIsImxhbmd1YWdlTW9ycGhlbWVzIiwiYWxpZ25tZW50cyIsIk1hcCIsIm1vcnBoZW1lcyIsInNldCIsImNvbnNpc3RlbmN5U2NvcmUiLCJjYWxjdWxhdGVDcm9zc0xpbmd1YWxDb25zaXN0ZW5jeSIsImJlc3RBbGlnbm1lbnRzIiwiZmluZEJlc3RMYW5ndWFnZVBhaXJzIiwiY29uY2VwdElkIiwiZnJvbSIsInZhbHVlcyIsIm1hcCIsImxhbmd1YWdlcyIsImtleXMiLCJwYWlycyIsImxhbmcxIiwibGFuZzIiLCJhbGlnbm1lbnQxIiwiZ2V0IiwiYWxpZ25tZW50MiIsInBhaXJTY29yZSIsInB1c2giLCJsYW5ndWFnZTEiLCJsYW5ndWFnZTIiLCJzb3J0IiwidXBkYXRlQ29uZmlnIiwibmV3Q29uZmlnIiwiZ2V0Q29uZmlnIl0sInNvdXJjZXMiOlsiL2hvbWUvcC9kZXZlbG9wL3dvcmtzcGFjZS9uYW1lci12Ni9zZXJ2ZXIvY29yZS9tdWx0aWxpbmd1YWwvU2VtYW50aWNBbGlnbmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog6K+t5LmJ5a+56b2Q5ZmoXG4gKiBcbiAqIOi0n+i0o+i3qOivreiogOivreS5ieWvuem9kOOAgeamguW/teaYoOWwhOOAgeivreS5ieebuOS8vOW6puiuoeeul+etieWKn+iDvVxuICog56Gu5L+d5aSa6K+t56eN55Sf5oiQ55qE6K+t5LmJ5LiA6Ie05oCn5ZKM5paH5YyW6YCC6YWN5oCnXG4gKiBcbiAqIEBhdXRob3IgbmFtZXItdjYgdGVhbVxuICogQHZlcnNpb24gMy4wLjBcbiAqIEBjcmVhdGVkIDIwMjUtMDYtMjRcbiAqL1xuXG5pbXBvcnQgeyBMYW5ndWFnZUNvZGUsIFNFTUFOVElDX1ZFQ1RPUl9ESU1FTlNJT05TIH0gZnJvbSAnLi4vLi4vdHlwZXMvbXVsdGlsaW5ndWFsLmpzJ1xuaW1wb3J0IHR5cGUge1xuICBVbml2ZXJzYWxDb25jZXB0LFxuICBMYW5ndWFnZVNwZWNpZmljTW9ycGhlbWUsXG4gIFVuaXZlcnNhbFNlbWFudGljVmVjdG9yXG59IGZyb20gJy4uLy4uL3R5cGVzL211bHRpbGluZ3VhbC5qcydcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8g6K+t5LmJ5a+56b2Q5Zmo5o6l5Y+jXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VtYW50aWNBbGlnbm1lbnQge1xuICAvKiog5rqQ5qaC5b+1SUQgKi9cbiAgc291cmNlQ29uY2VwdElkOiBzdHJpbmdcbiAgLyoqIOebruagh+ivreiogCAqL1xuICB0YXJnZXRMYW5ndWFnZTogTGFuZ3VhZ2VDb2RlXG4gIC8qKiDlr7npvZDnmoTor63ntKDliJfooaggKi9cbiAgYWxpZ25lZE1vcnBoZW1lczogTGFuZ3VhZ2VTcGVjaWZpY01vcnBoZW1lW11cbiAgLyoqIOWvuem9kOWIhuaVsCBbMC0xXSAqL1xuICBhbGlnbm1lbnRTY29yZTogbnVtYmVyXG4gIC8qKiDor63kuYnnm7jkvLzluqYgWzAtMV0gKi9cbiAgc2VtYW50aWNTaW1pbGFyaXR5OiBudW1iZXJcbiAgLyoqIOaWh+WMlumAgumFjeW6piBbMC0xXSAqL1xuICBjdWx0dXJhbEZpdDogbnVtYmVyXG4gIC8qKiDlr7npvZDnva7kv6HluqYgWzAtMV0gKi9cbiAgY29uZmlkZW5jZTogbnVtYmVyXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3Jvc3NMaW5ndWFsTWFwcGluZyB7XG4gIC8qKiDmpoLlv7VJRCAqL1xuICBjb25jZXB0SWQ6IHN0cmluZ1xuICAvKiog5ZCE6K+t6KiA55qE6K+t57Sg5pig5bCEICovXG4gIGxhbmd1YWdlTW9ycGhlbWVzOiBNYXA8TGFuZ3VhZ2VDb2RlLCBMYW5ndWFnZVNwZWNpZmljTW9ycGhlbWVbXT5cbiAgLyoqIOi3qOivreiogOS4gOiHtOaAp+ivhOWIhiBbMC0xXSAqL1xuICBjb25zaXN0ZW5jeVNjb3JlOiBudW1iZXJcbiAgLyoqIOacgOS9s+Wvuem9kOivreiogOWvuSAqL1xuICBiZXN0QWxpZ25tZW50czogQXJyYXk8e1xuICAgIGxhbmd1YWdlMTogTGFuZ3VhZ2VDb2RlXG4gICAgbGFuZ3VhZ2UyOiBMYW5ndWFnZUNvZGVcbiAgICBzY29yZTogbnVtYmVyXG4gIH0+XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VtYW50aWNBbGlnbmVyQ29uZmlnIHtcbiAgLyoqIOivreS5ieebuOS8vOW6pumYiOWAvCAqL1xuICBzZW1hbnRpY1NpbWlsYXJpdHlUaHJlc2hvbGQ6IG51bWJlclxuICAvKiog5paH5YyW6YCC6YWN5p2D6YeNICovXG4gIGN1bHR1cmFsRml0V2VpZ2h0OiBudW1iZXJcbiAgLyoqIOivreS5ieadg+mHjSAqL1xuICBzZW1hbnRpY1dlaWdodDogbnVtYmVyXG4gIC8qKiDotKjph4/mnYPph40gKi9cbiAgcXVhbGl0eVdlaWdodDogbnVtYmVyXG59XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIOivreS5ieWvuem9kOWZqOexu1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4vKipcbiAqIOivreS5ieWvuem9kOWZqOexu1xuICogXG4gKiDmj5Dkvpvot6jor63oqIDor63kuYnlr7npvZDlkozmpoLlv7XmmKDlsITlip/og71cbiAqL1xuZXhwb3J0IGNsYXNzIFNlbWFudGljQWxpZ25lciB7XG4gIHByaXZhdGUgY29uZmlnOiBTZW1hbnRpY0FsaWduZXJDb25maWdcblxuICAvKipcbiAgICog5p6E6YCg5Ye95pWwXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihjb25maWc6IFBhcnRpYWw8U2VtYW50aWNBbGlnbmVyQ29uZmlnPiA9IHt9KSB7XG4gICAgdGhpcy5jb25maWcgPSB7XG4gICAgICBzZW1hbnRpY1NpbWlsYXJpdHlUaHJlc2hvbGQ6IGNvbmZpZy5zZW1hbnRpY1NpbWlsYXJpdHlUaHJlc2hvbGQgfHwgMC43NSxcbiAgICAgIGN1bHR1cmFsRml0V2VpZ2h0OiBjb25maWcuY3VsdHVyYWxGaXRXZWlnaHQgfHwgMC4zLFxuICAgICAgc2VtYW50aWNXZWlnaHQ6IGNvbmZpZy5zZW1hbnRpY1dlaWdodCB8fCAwLjUsXG4gICAgICBxdWFsaXR5V2VpZ2h0OiBjb25maWcucXVhbGl0eVdlaWdodCB8fCAwLjJcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog6K6h566X5qaC5b+15LiO6K+t57Sg55qE6K+t5LmJ5a+56b2Q5bqmXG4gICAqL1xuICBjYWxjdWxhdGVDb25jZXB0TW9ycGhlbWVBbGlnbm1lbnQoXG4gICAgY29uY2VwdDogVW5pdmVyc2FsQ29uY2VwdCxcbiAgICBtb3JwaGVtZTogTGFuZ3VhZ2VTcGVjaWZpY01vcnBoZW1lXG4gICk6IFNlbWFudGljQWxpZ25tZW50IHtcbiAgICAvLyAxLiDor63kuYnnm7jkvLzluqborqHnrpdcbiAgICBjb25zdCBzZW1hbnRpY1NpbWlsYXJpdHkgPSB0aGlzLmNhbGN1bGF0ZVNlbWFudGljU2ltaWxhcml0eShcbiAgICAgIGNvbmNlcHQuc2VtYW50aWNfdmVjdG9yLFxuICAgICAgbW9ycGhlbWVcbiAgICApXG5cbiAgICAvLyAyLiDmlofljJbpgILphY3luqborqHnrpdcbiAgICBjb25zdCBjdWx0dXJhbEZpdCA9IHRoaXMuY2FsY3VsYXRlQ3VsdHVyYWxGaXQoY29uY2VwdCwgbW9ycGhlbWUpXG5cbiAgICAvLyAzLiDotKjph4/kuIDoh7TmgKforqHnrpdcbiAgICBjb25zdCBxdWFsaXR5Q29uc2lzdGVuY3kgPSB0aGlzLmNhbGN1bGF0ZVF1YWxpdHlDb25zaXN0ZW5jeShjb25jZXB0LCBtb3JwaGVtZSlcblxuICAgIC8vIDQuIOe7vOWQiOWvuem9kOWIhuaVsFxuICAgIGNvbnN0IGFsaWdubWVudFNjb3JlID0gXG4gICAgICB0aGlzLmNvbmZpZy5zZW1hbnRpY1dlaWdodCAqIHNlbWFudGljU2ltaWxhcml0eSArXG4gICAgICB0aGlzLmNvbmZpZy5jdWx0dXJhbEZpdFdlaWdodCAqIGN1bHR1cmFsRml0ICtcbiAgICAgIHRoaXMuY29uZmlnLnF1YWxpdHlXZWlnaHQgKiBxdWFsaXR5Q29uc2lzdGVuY3lcblxuICAgIC8vIDUuIOiuoeeul+e9ruS/oeW6plxuICAgIGNvbnN0IGNvbmZpZGVuY2UgPSB0aGlzLmNhbGN1bGF0ZUFsaWdubWVudENvbmZpZGVuY2UoXG4gICAgICBzZW1hbnRpY1NpbWlsYXJpdHksXG4gICAgICBjdWx0dXJhbEZpdCxcbiAgICAgIHF1YWxpdHlDb25zaXN0ZW5jeVxuICAgIClcblxuICAgIHJldHVybiB7XG4gICAgICBzb3VyY2VDb25jZXB0SWQ6IGNvbmNlcHQuY29uY2VwdF9pZCxcbiAgICAgIHRhcmdldExhbmd1YWdlOiBtb3JwaGVtZS5sYW5ndWFnZSxcbiAgICAgIGFsaWduZWRNb3JwaGVtZXM6IFttb3JwaGVtZV0sXG4gICAgICBhbGlnbm1lbnRTY29yZSxcbiAgICAgIHNlbWFudGljU2ltaWxhcml0eSxcbiAgICAgIGN1bHR1cmFsRml0LFxuICAgICAgY29uZmlkZW5jZVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDorqHnrpfor63kuYnnm7jkvLzluqZcbiAgICovXG4gIHByaXZhdGUgY2FsY3VsYXRlU2VtYW50aWNTaW1pbGFyaXR5KFxuICAgIGNvbmNlcHRWZWN0b3I6IFVuaXZlcnNhbFNlbWFudGljVmVjdG9yLFxuICAgIG1vcnBoZW1lOiBMYW5ndWFnZVNwZWNpZmljTW9ycGhlbWVcbiAgKTogbnVtYmVyIHtcbiAgICAvLyDlpoLmnpzor63ntKDmsqHmnInor63kuYnlkJHph4/vvIzkvb/nlKjln7rkuo7nsbvliKvnmoTnm7jkvLzluqZcbiAgICBpZiAoIW1vcnBoZW1lLnBob25ldGljX2ZlYXR1cmVzKSB7XG4gICAgICByZXR1cm4gdGhpcy5jYWxjdWxhdGVDYXRlZ29yaWNhbFNpbWlsYXJpdHkoY29uY2VwdFZlY3RvciwgbW9ycGhlbWUpXG4gICAgfVxuXG4gICAgLy8g6I635Y+W6YCC6YWN55qE5qaC5b+15ZCR6YePXG4gICAgY29uc3QgYWRhcHRlZENvbmNlcHRWZWN0b3IgPSB0aGlzLmFkYXB0Q29uY2VwdFZlY3Rvcihjb25jZXB0VmVjdG9yKVxuICAgIGNvbnN0IG1vcnBoZW1lVmVjdG9yID0gdGhpcy5leHRyYWN0TW9ycGhlbWVTZW1hbnRpY1ZlY3Rvcihtb3JwaGVtZSlcblxuICAgIC8vIOS9v+eUqOS9meW8puebuOS8vOW6puiuoeeul+ivreS5ieWQkemHj+ebuOS8vOW6plxuICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZUNvc2luZVNpbWlsYXJpdHkoYWRhcHRlZENvbmNlcHRWZWN0b3IsIG1vcnBoZW1lVmVjdG9yKVxuICB9XG5cbiAgLyoqXG4gICAqIOmAgumFjeamguW/teWQkemHj+WIsOWFvOWuuee7tOW6plxuICAgKi9cbiAgcHJpdmF0ZSBhZGFwdENvbmNlcHRWZWN0b3IoY29uY2VwdFZlY3RvcjogVW5pdmVyc2FsU2VtYW50aWNWZWN0b3IpOiBudW1iZXJbXSB7XG4gICAgLy8g5aaC5p6c5pyJ5YW85a655oCn5ZCR6YeP77yM55u05o6l5L2/55SoXG4gICAgaWYgKGNvbmNlcHRWZWN0b3IubGVnYWN5X3ZlY3RvciAmJiBjb25jZXB0VmVjdG9yLmxlZ2FjeV92ZWN0b3IubGVuZ3RoID09PSBTRU1BTlRJQ19WRUNUT1JfRElNRU5TSU9OUy5MRUdBQ1kpIHtcbiAgICAgIHJldHVybiBjb25jZXB0VmVjdG9yLmxlZ2FjeV92ZWN0b3JcbiAgICB9XG5cbiAgICAvLyDlkKbliJnku47pq5jnu7TlkJHph4/pmY3nu7TliLDlhbzlrrnnu7TluqZcbiAgICBjb25zdCB0YXJnZXREaW0gPSBTRU1BTlRJQ19WRUNUT1JfRElNRU5TSU9OUy5MRUdBQ1lcbiAgICBjb25zdCBzb3VyY2VEaW0gPSBjb25jZXB0VmVjdG9yLnZlY3Rvci5sZW5ndGhcblxuICAgIGlmIChzb3VyY2VEaW0gPT09IHRhcmdldERpbSkge1xuICAgICAgcmV0dXJuIGNvbmNlcHRWZWN0b3IudmVjdG9yXG4gICAgfVxuXG4gICAgLy8g566A5Y2V55qE6ZmN57u0562W55Wl77ya5bmz5Z2H5rGg5YyWXG4gICAgY29uc3QgYWRhcHRlZDogbnVtYmVyW10gPSBuZXcgQXJyYXkodGFyZ2V0RGltKS5maWxsKDApXG4gICAgY29uc3QgcG9vbFNpemUgPSBNYXRoLmZsb29yKHNvdXJjZURpbSAvIHRhcmdldERpbSlcblxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGFyZ2V0RGltOyBpKyspIHtcbiAgICAgIGxldCBzdW0gPSAwXG4gICAgICBjb25zdCBzdGFydCA9IGkgKiBwb29sU2l6ZVxuICAgICAgY29uc3QgZW5kID0gTWF0aC5taW4oc3RhcnQgKyBwb29sU2l6ZSwgc291cmNlRGltKVxuXG4gICAgICBmb3IgKGxldCBqID0gc3RhcnQ7IGogPCBlbmQ7IGorKykge1xuICAgICAgICBzdW0gKz0gY29uY2VwdFZlY3Rvci52ZWN0b3Jbal1cbiAgICAgIH1cblxuICAgICAgYWRhcHRlZFtpXSA9IHN1bSAvIChlbmQgLSBzdGFydClcbiAgICB9XG5cbiAgICByZXR1cm4gYWRhcHRlZFxuICB9XG5cbiAgLyoqXG4gICAqIOaPkOWPluivree0oOeahOivreS5ieWQkemHj1xuICAgKi9cbiAgcHJpdmF0ZSBleHRyYWN0TW9ycGhlbWVTZW1hbnRpY1ZlY3Rvcihtb3JwaGVtZTogTGFuZ3VhZ2VTcGVjaWZpY01vcnBoZW1lKTogbnVtYmVyW10ge1xuICAgIC8vIOWfuuS6juivree0oOeahOWQhOenjeeJueW+geaehOW7uuivreS5ieWQkemHj1xuICAgIGNvbnN0IHZlY3RvckRpbSA9IFNFTUFOVElDX1ZFQ1RPUl9ESU1FTlNJT05TLkxFR0FDWSAvLyDkvb/nlKjlhbzlrrnmgKflkJHph4/nu7TluqZcbiAgICBjb25zdCB2ZWN0b3I6IG51bWJlcltdID0gbmV3IEFycmF5KHZlY3RvckRpbSkuZmlsbCgwKVxuICAgIFxuICAgIC8vIOWfuuS6jui0qOmHj+ivhOWIhuWhq+WFheWQkemHj1xuICAgIGNvbnN0IHF1YWxpdHlTY29yZXMgPSBtb3JwaGVtZS5sYW5ndWFnZV9xdWFsaXR5X3Njb3Jlc1xuICAgIHZlY3RvclswXSA9IHF1YWxpdHlTY29yZXMubmF0dXJhbG5lc3NcbiAgICB2ZWN0b3JbMV0gPSBxdWFsaXR5U2NvcmVzLmZsdWVuY3lcbiAgICB2ZWN0b3JbMl0gPSBxdWFsaXR5U2NvcmVzLmF1dGhlbnRpY2l0eVxuICAgIHZlY3RvclszXSA9IHF1YWxpdHlTY29yZXMuYWVzdGhldGljX2FwcGVhbFxuICAgIHZlY3Rvcls0XSA9IHF1YWxpdHlTY29yZXMucHJvbnVuY2lhdGlvbl9lYXNlXG4gICAgdmVjdG9yWzVdID0gcXVhbGl0eVNjb3Jlcy5tZW1vcmFiaWxpdHlcbiAgICB2ZWN0b3JbNl0gPSBxdWFsaXR5U2NvcmVzLnVuaXF1ZW5lc3NcbiAgICB2ZWN0b3JbN10gPSBxdWFsaXR5U2NvcmVzLnByYWN0aWNhbGl0eVxuICAgIFxuICAgIC8vIOWfuuS6juaWh+WMluivreWig+Whq+WFheWQkemHj1xuICAgIGNvbnN0IGN1bHR1cmFsID0gbW9ycGhlbWUuY3VsdHVyYWxfY29udGV4dFxuICAgIHZlY3Rvcls4XSA9IGN1bHR1cmFsLnRyYWRpdGlvbmFsaXR5XG4gICAgdmVjdG9yWzldID0gY3VsdHVyYWwubW9kZXJuaXR5XG4gICAgdmVjdG9yWzEwXSA9IGN1bHR1cmFsLmZvcm1hbGl0eVxuICAgIHZlY3RvclsxMV0gPSBjdWx0dXJhbC5yZWdpb25hbGl0eVxuICAgIHZlY3RvclsxMl0gPSBjdWx0dXJhbC5yZWxpZ2lvdXNfc2Vuc2l0aXZpdHlcbiAgICBcbiAgICAvLyDln7rkuo7or63pn7PnibnlvoHloavlhYXlkJHph49cbiAgICB2ZWN0b3JbMTNdID0gbW9ycGhlbWUucGhvbmV0aWNfZmVhdHVyZXMucGhvbmV0aWNfaGFybW9ueVxuICAgIHZlY3RvclsxNF0gPSBtb3JwaGVtZS5waG9uZXRpY19mZWF0dXJlcy5zeWxsYWJsZV9jb3VudCAvIDUgLy8g5b2S5LiA5YyWXG4gICAgXG4gICAgLy8g5Z+65LqO5L2/55So6aKR546H5ZKM6K+E5YiG5aGr5YWF5ZCR6YePXG4gICAgdmVjdG9yWzE1XSA9IG1vcnBoZW1lLnVzYWdlX2ZyZXF1ZW5jeVxuICAgIHZlY3RvclsxNl0gPSBtb3JwaGVtZS5uYXRpdmVfc3BlYWtlcl9yYXRpbmdcbiAgICB2ZWN0b3JbMTddID0gbW9ycGhlbWUuY3VsdHVyYWxfYXBwcm9wcmlhdGVuZXNzXG4gICAgdmVjdG9yWzE4XSA9IG1vcnBoZW1lLnBvcHVsYXJpdHlfdHJlbmRcbiAgICBcbiAgICAvLyDmnIDlkI7kuIDkuKrnu7TluqbnlKjkuo7or63oqIDnibnlvILmgKdcbiAgICB2ZWN0b3JbMTldID0gdGhpcy5nZXRMYW5ndWFnZVNwZWNpZmljaXR5U2NvcmUobW9ycGhlbWUubGFuZ3VhZ2UpXG4gICAgXG4gICAgcmV0dXJuIHZlY3RvclxuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluivreiogOeJueW8guaAp+ivhOWIhlxuICAgKi9cbiAgcHJpdmF0ZSBnZXRMYW5ndWFnZVNwZWNpZmljaXR5U2NvcmUobGFuZ3VhZ2U6IExhbmd1YWdlQ29kZSk6IG51bWJlciB7XG4gICAgY29uc3Qgc2NvcmVzOiBSZWNvcmQ8TGFuZ3VhZ2VDb2RlLCBudW1iZXI+ID0ge1xuICAgICAgW0xhbmd1YWdlQ29kZS5aSF9DTl06IDAuOSxcbiAgICAgIFtMYW5ndWFnZUNvZGUuRU5fVVNdOiAwLjgsXG4gICAgICBbTGFuZ3VhZ2VDb2RlLkpBX0pQXTogMC44NSxcbiAgICAgIFtMYW5ndWFnZUNvZGUuS09fS1JdOiAwLjg1LFxuICAgICAgW0xhbmd1YWdlQ29kZS5FU19FU106IDAuNyxcbiAgICAgIFtMYW5ndWFnZUNvZGUuRlJfRlJdOiAwLjcsXG4gICAgICBbTGFuZ3VhZ2VDb2RlLkRFX0RFXTogMC43NSxcbiAgICAgIFtMYW5ndWFnZUNvZGUuQVJfU0FdOiAwLjlcbiAgICB9XG4gICAgcmV0dXJuIHNjb3Jlc1tsYW5ndWFnZV0gfHwgMC41XG4gIH1cblxuICAvKipcbiAgICog6K6h566X5L2Z5bym55u45Ly85bqmXG4gICAqL1xuICBwcml2YXRlIGNhbGN1bGF0ZUNvc2luZVNpbWlsYXJpdHkodmVjdG9yMTogbnVtYmVyW10sIHZlY3RvcjI6IG51bWJlcltdKTogbnVtYmVyIHtcbiAgICBpZiAodmVjdG9yMS5sZW5ndGggIT09IHZlY3RvcjIubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gMFxuICAgIH1cblxuICAgIGxldCBkb3RQcm9kdWN0ID0gMFxuICAgIGxldCBub3JtMSA9IDBcbiAgICBsZXQgbm9ybTIgPSAwXG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZlY3RvcjEubGVuZ3RoOyBpKyspIHtcbiAgICAgIGRvdFByb2R1Y3QgKz0gdmVjdG9yMVtpXSAqIHZlY3RvcjJbaV1cbiAgICAgIG5vcm0xICs9IHZlY3RvcjFbaV0gKiB2ZWN0b3IxW2ldXG4gICAgICBub3JtMiArPSB2ZWN0b3IyW2ldICogdmVjdG9yMltpXVxuICAgIH1cblxuICAgIGNvbnN0IG1hZ25pdHVkZSA9IE1hdGguc3FydChub3JtMSkgKiBNYXRoLnNxcnQobm9ybTIpXG4gICAgcmV0dXJuIG1hZ25pdHVkZSA+IDAgPyBkb3RQcm9kdWN0IC8gbWFnbml0dWRlIDogMFxuICB9XG5cbiAgLyoqXG4gICAqIOWfuuS6juexu+WIq+eahOebuOS8vOW6puiuoeeul1xuICAgKi9cbiAgcHJpdmF0ZSBjYWxjdWxhdGVDYXRlZ29yaWNhbFNpbWlsYXJpdHkoXG4gICAgY29uY2VwdFZlY3RvcjogVW5pdmVyc2FsU2VtYW50aWNWZWN0b3IsXG4gICAgbW9ycGhlbWU6IExhbmd1YWdlU3BlY2lmaWNNb3JwaGVtZVxuICApOiBudW1iZXIge1xuICAgIC8vIOWfuuS6juamguW/teexu+WIq+WSjOivree0oOeahOivjeaAp+agh+azqOiuoeeul+ebuOS8vOW6plxuICAgIGNvbnN0IHBvc01hcHBpbmc6IFJlY29yZDxzdHJpbmcsIHN0cmluZ1tdPiA9IHtcbiAgICAgICdlbW90aW9ucyc6IFsnQURKJywgJ05PVU4nXSxcbiAgICAgICdwcm9mZXNzaW9ucyc6IFsnTk9VTiddLFxuICAgICAgJ2NoYXJhY3RlcmlzdGljcyc6IFsnQURKJ10sXG4gICAgICAnb2JqZWN0cyc6IFsnTk9VTiddLFxuICAgICAgJ2FjdGlvbnMnOiBbJ1ZFUkInXSxcbiAgICAgICdjb25jZXB0cyc6IFsnTk9VTicsICdBREonXVxuICAgIH1cblxuICAgIC8vIOS7juamguW/teWQkemHj+aOqOaWreexu+WIq++8iOeugOWMluWunueOsO+8iVxuICAgIGNvbnN0IGluZmVycmVkQ2F0ZWdvcnkgPSB0aGlzLmluZmVyQ2F0ZWdvcnlGcm9tVmVjdG9yKGNvbmNlcHRWZWN0b3IudmVjdG9yKVxuICAgIGNvbnN0IGV4cGVjdGVkUE9TID0gcG9zTWFwcGluZ1tpbmZlcnJlZENhdGVnb3J5XSB8fCBbJ05PVU4nXVxuICAgIFxuICAgIHJldHVybiBleHBlY3RlZFBPUy5pbmNsdWRlcyhtb3JwaGVtZS5tb3JwaG9sb2dpY2FsX2luZm8ucG9zX3RhZykgPyAwLjggOiAwLjRcbiAgfVxuXG4gIC8qKlxuICAgKiDku47or63kuYnlkJHph4/mjqjmlq3nsbvliKtcbiAgICovXG4gIHByaXZhdGUgaW5mZXJDYXRlZ29yeUZyb21WZWN0b3IodmVjdG9yOiBudW1iZXJbXSk6IHN0cmluZyB7XG4gICAgLy8g566A5YyW55qE57G75Yir5o6o5pat6YC76L6RXG4gICAgY29uc3QgbWF4SW5kZXggPSB2ZWN0b3IuaW5kZXhPZihNYXRoLm1heCguLi52ZWN0b3IpKVxuICAgIGNvbnN0IGNhdGVnb3JpZXMgPSBbJ2Vtb3Rpb25zJywgJ3Byb2Zlc3Npb25zJywgJ2NoYXJhY3RlcmlzdGljcycsICdvYmplY3RzJywgJ2FjdGlvbnMnLCAnY29uY2VwdHMnXVxuICAgIHJldHVybiBjYXRlZ29yaWVzW21heEluZGV4ICUgY2F0ZWdvcmllcy5sZW5ndGhdIHx8ICdjb25jZXB0cydcbiAgfVxuXG4gIC8qKlxuICAgKiDorqHnrpfmlofljJbpgILphY3luqZcbiAgICovXG4gIHByaXZhdGUgY2FsY3VsYXRlQ3VsdHVyYWxGaXQoXG4gICAgY29uY2VwdDogVW5pdmVyc2FsQ29uY2VwdCxcbiAgICBtb3JwaGVtZTogTGFuZ3VhZ2VTcGVjaWZpY01vcnBoZW1lXG4gICk6IG51bWJlciB7XG4gICAgLy8gMS4g5paH5YyW5Lit5oCn5bqm5Yy56YWNXG4gICAgY29uc3QgbmV1dHJhbGl0eUZpdCA9IDEgLSBNYXRoLmFicyhjb25jZXB0LmN1bHR1cmFsX25ldXRyYWxpdHkgLSAwLjUpICogMlxuXG4gICAgLy8gMi4g6Leo6K+t6KiA56iz5a6a5oCnXG4gICAgY29uc3Qgc3RhYmlsaXR5Qm9udXMgPSBjb25jZXB0LmNyb3NzX2xpbmd1YWxfc3RhYmlsaXR5ICogMC4zXG5cbiAgICAvLyAzLiDor63ntKDnmoTmlofljJbpgILlrpzmgKdcbiAgICBjb25zdCBhcHByb3ByaWF0ZW5lc3MgPSBtb3JwaGVtZS5jdWx0dXJhbF9hcHByb3ByaWF0ZW5lc3NcblxuICAgIC8vIDQuIOe7vOWQiOaWh+WMlumAgumFjeW6plxuICAgIHJldHVybiAobmV1dHJhbGl0eUZpdCAqIDAuNCArIHN0YWJpbGl0eUJvbnVzICsgYXBwcm9wcmlhdGVuZXNzICogMC4zKVxuICB9XG5cbiAgLyoqXG4gICAqIOiuoeeul+i0qOmHj+S4gOiHtOaAp1xuICAgKi9cbiAgcHJpdmF0ZSBjYWxjdWxhdGVRdWFsaXR5Q29uc2lzdGVuY3koXG4gICAgY29uY2VwdDogVW5pdmVyc2FsQ29uY2VwdCxcbiAgICBtb3JwaGVtZTogTGFuZ3VhZ2VTcGVjaWZpY01vcnBoZW1lXG4gICk6IG51bWJlciB7XG4gICAgLy8g5Z+65LqO5qaC5b+155qE6K6k55+l5bGe5oCn5ZKM6K+t57Sg55qE6LSo6YeP6K+E5YiG6K6h566X5LiA6Ie05oCnXG4gICAgY29uc3QgY29uY2VwdFF1YWxpdHkgPSBjb25jZXB0LmNvZ25pdGl2ZV9hdHRyaWJ1dGVzLm1lbW9yYWJpbGl0eVxuICAgIGNvbnN0IG1vcnBoZW1lUXVhbGl0eSA9IG1vcnBoZW1lLm5hdGl2ZV9zcGVha2VyX3JhdGluZ1xuICAgIFxuICAgIC8vIOi0qOmHj+WIhuaVsOeahOS4gOiHtOaAp++8iDEgLSDlt67lgLznmoTnu53lr7nlgLzvvIlcbiAgICByZXR1cm4gTWF0aC5tYXgoMCwgMSAtIE1hdGguYWJzKGNvbmNlcHRRdWFsaXR5IC0gbW9ycGhlbWVRdWFsaXR5KSlcbiAgfVxuXG4gIC8qKlxuICAgKiDorqHnrpflr7npvZDnva7kv6HluqZcbiAgICovXG4gIHByaXZhdGUgY2FsY3VsYXRlQWxpZ25tZW50Q29uZmlkZW5jZShcbiAgICBzZW1hbnRpY1NpbWlsYXJpdHk6IG51bWJlcixcbiAgICBjdWx0dXJhbEZpdDogbnVtYmVyLFxuICAgIHF1YWxpdHlDb25zaXN0ZW5jeTogbnVtYmVyXG4gICk6IG51bWJlciB7XG4gICAgLy8g5Z+65LqO5ZCE6aG55oyH5qCH55qE5pa55beu6K6h566X572u5L+h5bqmXG4gICAgY29uc3Qgc2NvcmVzID0gW3NlbWFudGljU2ltaWxhcml0eSwgY3VsdHVyYWxGaXQsIHF1YWxpdHlDb25zaXN0ZW5jeV1cbiAgICBjb25zdCBtZWFuID0gc2NvcmVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gc2NvcmVzLmxlbmd0aFxuICAgIGNvbnN0IHZhcmlhbmNlID0gc2NvcmVzLnJlZHVjZSgoYWNjLCBzY29yZSkgPT4gYWNjICsgTWF0aC5wb3coc2NvcmUgLSBtZWFuLCAyKSwgMCkgLyBzY29yZXMubGVuZ3RoXG4gICAgXG4gICAgLy8g5pa55beu6LaK5bCP77yM572u5L+h5bqm6LaK6auYXG4gICAgcmV0dXJuIE1hdGgubWF4KDAsIDEgLSB2YXJpYW5jZSlcbiAgfVxuXG4gIC8qKlxuICAgKiDkuLrmpoLlv7Xmib7liLDmnIDkvbPor63oqIDlr7npvZBcbiAgICovXG4gIGZpbmRCZXN0QWxpZ25tZW50KFxuICAgIGNvbmNlcHQ6IFVuaXZlcnNhbENvbmNlcHQsXG4gICAgY2FuZGlkYXRlTW9ycGhlbWVzOiBMYW5ndWFnZVNwZWNpZmljTW9ycGhlbWVbXVxuICApOiBTZW1hbnRpY0FsaWdubWVudCB8IG51bGwge1xuICAgIGlmIChjYW5kaWRhdGVNb3JwaGVtZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cblxuICAgIGxldCBiZXN0QWxpZ25tZW50OiBTZW1hbnRpY0FsaWdubWVudCB8IG51bGwgPSBudWxsXG4gICAgbGV0IGJlc3RTY29yZSA9IDBcblxuICAgIGZvciAoY29uc3QgbW9ycGhlbWUgb2YgY2FuZGlkYXRlTW9ycGhlbWVzKSB7XG4gICAgICBjb25zdCBhbGlnbm1lbnQgPSB0aGlzLmNhbGN1bGF0ZUNvbmNlcHRNb3JwaGVtZUFsaWdubWVudChjb25jZXB0LCBtb3JwaGVtZSlcbiAgICAgIFxuICAgICAgaWYgKGFsaWdubWVudC5hbGlnbm1lbnRTY29yZSA+IGJlc3RTY29yZSAmJiBcbiAgICAgICAgICBhbGlnbm1lbnQuc2VtYW50aWNTaW1pbGFyaXR5ID49IHRoaXMuY29uZmlnLnNlbWFudGljU2ltaWxhcml0eVRocmVzaG9sZCkge1xuICAgICAgICBiZXN0U2NvcmUgPSBhbGlnbm1lbnQuYWxpZ25tZW50U2NvcmVcbiAgICAgICAgYmVzdEFsaWdubWVudCA9IGFsaWdubWVudFxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBiZXN0QWxpZ25tZW50XG4gIH1cblxuICAvKipcbiAgICog5p6E5bu66Leo6K+t6KiA5pig5bCEXG4gICAqL1xuICBidWlsZENyb3NzTGluZ3VhbE1hcHBpbmcoXG4gICAgY29uY2VwdDogVW5pdmVyc2FsQ29uY2VwdCxcbiAgICBsYW5ndWFnZU1vcnBoZW1lczogTWFwPExhbmd1YWdlQ29kZSwgTGFuZ3VhZ2VTcGVjaWZpY01vcnBoZW1lW10+XG4gICk6IENyb3NzTGluZ3VhbE1hcHBpbmcge1xuICAgIGNvbnN0IGFsaWdubWVudHMgPSBuZXcgTWFwPExhbmd1YWdlQ29kZSwgU2VtYW50aWNBbGlnbm1lbnQ+KClcbiAgICBcbiAgICAvLyDkuLrmr4/np43or63oqIDmib7liLDmnIDkvbPlr7npvZBcbiAgICBmb3IgKGNvbnN0IFtsYW5ndWFnZSwgbW9ycGhlbWVzXSBvZiBsYW5ndWFnZU1vcnBoZW1lcykge1xuICAgICAgY29uc3QgYmVzdEFsaWdubWVudCA9IHRoaXMuZmluZEJlc3RBbGlnbm1lbnQoY29uY2VwdCwgbW9ycGhlbWVzKVxuICAgICAgaWYgKGJlc3RBbGlnbm1lbnQpIHtcbiAgICAgICAgYWxpZ25tZW50cy5zZXQobGFuZ3VhZ2UsIGJlc3RBbGlnbm1lbnQpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g6K6h566X6Leo6K+t6KiA5LiA6Ie05oCnXG4gICAgY29uc3QgY29uc2lzdGVuY3lTY29yZSA9IHRoaXMuY2FsY3VsYXRlQ3Jvc3NMaW5ndWFsQ29uc2lzdGVuY3koYWxpZ25tZW50cylcblxuICAgIC8vIOaJvuWIsOacgOS9s+Wvuem9kOivreiogOWvuVxuICAgIGNvbnN0IGJlc3RBbGlnbm1lbnRzID0gdGhpcy5maW5kQmVzdExhbmd1YWdlUGFpcnMoYWxpZ25tZW50cylcblxuICAgIHJldHVybiB7XG4gICAgICBjb25jZXB0SWQ6IGNvbmNlcHQuY29uY2VwdF9pZCxcbiAgICAgIGxhbmd1YWdlTW9ycGhlbWVzLFxuICAgICAgY29uc2lzdGVuY3lTY29yZSxcbiAgICAgIGJlc3RBbGlnbm1lbnRzXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiuoeeul+i3qOivreiogOS4gOiHtOaAp1xuICAgKi9cbiAgcHJpdmF0ZSBjYWxjdWxhdGVDcm9zc0xpbmd1YWxDb25zaXN0ZW5jeShcbiAgICBhbGlnbm1lbnRzOiBNYXA8TGFuZ3VhZ2VDb2RlLCBTZW1hbnRpY0FsaWdubWVudD5cbiAgKTogbnVtYmVyIHtcbiAgICBjb25zdCBzY29yZXMgPSBBcnJheS5mcm9tKGFsaWdubWVudHMudmFsdWVzKCkpLm1hcChhID0+IGEuYWxpZ25tZW50U2NvcmUpXG4gICAgaWYgKHNjb3Jlcy5sZW5ndGggPCAyKSByZXR1cm4gMS4wXG5cbiAgICBjb25zdCBtZWFuID0gc2NvcmVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gc2NvcmVzLmxlbmd0aFxuICAgIGNvbnN0IHZhcmlhbmNlID0gc2NvcmVzLnJlZHVjZSgoYWNjLCBzY29yZSkgPT4gYWNjICsgTWF0aC5wb3coc2NvcmUgLSBtZWFuLCAyKSwgMCkgLyBzY29yZXMubGVuZ3RoXG4gICAgXG4gICAgLy8g5LiA6Ie05oCnID0g5bmz5Z2H5YiG5pWwICogKDEgLSDmoIflh4bljJbmlrnlt64pXG4gICAgcmV0dXJuIG1lYW4gKiAoMSAtIE1hdGgubWluKHZhcmlhbmNlLCAxKSlcbiAgfVxuXG4gIC8qKlxuICAgKiDmib7liLDmnIDkvbPor63oqIDlr7npvZDlr7lcbiAgICovXG4gIHByaXZhdGUgZmluZEJlc3RMYW5ndWFnZVBhaXJzKFxuICAgIGFsaWdubWVudHM6IE1hcDxMYW5ndWFnZUNvZGUsIFNlbWFudGljQWxpZ25tZW50PlxuICApOiBBcnJheTx7IGxhbmd1YWdlMTogTGFuZ3VhZ2VDb2RlOyBsYW5ndWFnZTI6IExhbmd1YWdlQ29kZTsgc2NvcmU6IG51bWJlciB9PiB7XG4gICAgY29uc3QgbGFuZ3VhZ2VzID0gQXJyYXkuZnJvbShhbGlnbm1lbnRzLmtleXMoKSlcbiAgICBjb25zdCBwYWlyczogQXJyYXk8eyBsYW5ndWFnZTE6IExhbmd1YWdlQ29kZTsgbGFuZ3VhZ2UyOiBMYW5ndWFnZUNvZGU7IHNjb3JlOiBudW1iZXIgfT4gPSBbXVxuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsYW5ndWFnZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGZvciAobGV0IGogPSBpICsgMTsgaiA8IGxhbmd1YWdlcy5sZW5ndGg7IGorKykge1xuICAgICAgICBjb25zdCBsYW5nMSA9IGxhbmd1YWdlc1tpXVxuICAgICAgICBjb25zdCBsYW5nMiA9IGxhbmd1YWdlc1tqXVxuICAgICAgICBjb25zdCBhbGlnbm1lbnQxID0gYWxpZ25tZW50cy5nZXQobGFuZzEpIVxuICAgICAgICBjb25zdCBhbGlnbm1lbnQyID0gYWxpZ25tZW50cy5nZXQobGFuZzIpIVxuICAgICAgICBcbiAgICAgICAgLy8g6K6h566X6K+t6KiA5a+555qE5a+56b2Q5YiG5pWwXG4gICAgICAgIGNvbnN0IHBhaXJTY29yZSA9IChhbGlnbm1lbnQxLmFsaWdubWVudFNjb3JlICsgYWxpZ25tZW50Mi5hbGlnbm1lbnRTY29yZSkgLyAyXG4gICAgICAgIHBhaXJzLnB1c2goeyBsYW5ndWFnZTE6IGxhbmcxLCBsYW5ndWFnZTI6IGxhbmcyLCBzY29yZTogcGFpclNjb3JlIH0pXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g5oyJ5YiG5pWw6ZmN5bqP5o6S5bqPXG4gICAgcmV0dXJuIHBhaXJzLnNvcnQoKGEsIGIpID0+IGIuc2NvcmUgLSBhLnNjb3JlKVxuICB9XG5cbiAgLyoqXG4gICAqIOabtOaWsOmFjee9rlxuICAgKi9cbiAgdXBkYXRlQ29uZmlnKG5ld0NvbmZpZzogUGFydGlhbDxTZW1hbnRpY0FsaWduZXJDb25maWc+KTogdm9pZCB7XG4gICAgdGhpcy5jb25maWcgPSB7IC4uLnRoaXMuY29uZmlnLCAuLi5uZXdDb25maWcgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluW9k+WJjemFjee9rlxuICAgKi9cbiAgZ2V0Q29uZmlnKCk6IFNlbWFudGljQWxpZ25lckNvbmZpZyB7XG4gICAgcmV0dXJuIHsgLi4udGhpcy5jb25maWcgfVxuICB9XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7Ozs7Ozs7QUFXQSxTQUFTQSxZQUFZLEVBQUVDLDBCQUEwQixRQUFRLDZCQUE2QjtBQXNEdEY7QUFDQTtBQUNBO0FBRUE7Ozs7O0FBS0EsT0FBTSxNQUFPQyxlQUFlO0VBQ2xCQyxNQUFNO0VBRWQ7OztFQUdBQyxZQUFZRCxNQUFBO0VBQUE7RUFBQSxDQUFBRSxZQUFBLEdBQUFDLENBQUEsVUFBeUMsRUFBRTtJQUFBO0lBQUFELFlBQUEsR0FBQUUsQ0FBQTtJQUFBRixZQUFBLEdBQUFHLENBQUE7SUFDckQsSUFBSSxDQUFDTCxNQUFNLEdBQUc7TUFDWk0sMkJBQTJCO01BQUU7TUFBQSxDQUFBSixZQUFBLEdBQUFDLENBQUEsVUFBQUgsTUFBTSxDQUFDTSwyQkFBMkI7TUFBQTtNQUFBLENBQUFKLFlBQUEsR0FBQUMsQ0FBQSxVQUFJLElBQUk7TUFDdkVJLGlCQUFpQjtNQUFFO01BQUEsQ0FBQUwsWUFBQSxHQUFBQyxDQUFBLFVBQUFILE1BQU0sQ0FBQ08saUJBQWlCO01BQUE7TUFBQSxDQUFBTCxZQUFBLEdBQUFDLENBQUEsVUFBSSxHQUFHO01BQ2xESyxjQUFjO01BQUU7TUFBQSxDQUFBTixZQUFBLEdBQUFDLENBQUEsVUFBQUgsTUFBTSxDQUFDUSxjQUFjO01BQUE7TUFBQSxDQUFBTixZQUFBLEdBQUFDLENBQUEsVUFBSSxHQUFHO01BQzVDTSxhQUFhO01BQUU7TUFBQSxDQUFBUCxZQUFBLEdBQUFDLENBQUEsVUFBQUgsTUFBTSxDQUFDUyxhQUFhO01BQUE7TUFBQSxDQUFBUCxZQUFBLEdBQUFDLENBQUEsVUFBSSxHQUFHO0tBQzNDO0VBQ0g7RUFFQTs7O0VBR0FPLGlDQUFpQ0EsQ0FDL0JDLE9BQXlCLEVBQ3pCQyxRQUFrQztJQUFBO0lBQUFWLFlBQUEsR0FBQUUsQ0FBQTtJQUVsQztJQUNBLE1BQU1TLGtCQUFrQjtJQUFBO0lBQUEsQ0FBQVgsWUFBQSxHQUFBRyxDQUFBLE9BQUcsSUFBSSxDQUFDUywyQkFBMkIsQ0FDekRILE9BQU8sQ0FBQ0ksZUFBZSxFQUN2QkgsUUFBUSxDQUNUO0lBRUQ7SUFDQSxNQUFNSSxXQUFXO0lBQUE7SUFBQSxDQUFBZCxZQUFBLEdBQUFHLENBQUEsT0FBRyxJQUFJLENBQUNZLG9CQUFvQixDQUFDTixPQUFPLEVBQUVDLFFBQVEsQ0FBQztJQUVoRTtJQUNBLE1BQU1NLGtCQUFrQjtJQUFBO0lBQUEsQ0FBQWhCLFlBQUEsR0FBQUcsQ0FBQSxPQUFHLElBQUksQ0FBQ2MsMkJBQTJCLENBQUNSLE9BQU8sRUFBRUMsUUFBUSxDQUFDO0lBRTlFO0lBQ0EsTUFBTVEsY0FBYztJQUFBO0lBQUEsQ0FBQWxCLFlBQUEsR0FBQUcsQ0FBQSxPQUNsQixJQUFJLENBQUNMLE1BQU0sQ0FBQ1EsY0FBYyxHQUFHSyxrQkFBa0IsR0FDL0MsSUFBSSxDQUFDYixNQUFNLENBQUNPLGlCQUFpQixHQUFHUyxXQUFXLEdBQzNDLElBQUksQ0FBQ2hCLE1BQU0sQ0FBQ1MsYUFBYSxHQUFHUyxrQkFBa0I7SUFFaEQ7SUFDQSxNQUFNRyxVQUFVO0lBQUE7SUFBQSxDQUFBbkIsWUFBQSxHQUFBRyxDQUFBLE9BQUcsSUFBSSxDQUFDaUIsNEJBQTRCLENBQ2xEVCxrQkFBa0IsRUFDbEJHLFdBQVcsRUFDWEUsa0JBQWtCLENBQ25CO0lBQUE7SUFBQWhCLFlBQUEsR0FBQUcsQ0FBQTtJQUVELE9BQU87TUFDTGtCLGVBQWUsRUFBRVosT0FBTyxDQUFDYSxVQUFVO01BQ25DQyxjQUFjLEVBQUViLFFBQVEsQ0FBQ2MsUUFBUTtNQUNqQ0MsZ0JBQWdCLEVBQUUsQ0FBQ2YsUUFBUSxDQUFDO01BQzVCUSxjQUFjO01BQ2RQLGtCQUFrQjtNQUNsQkcsV0FBVztNQUNYSztLQUNEO0VBQ0g7RUFFQTs7O0VBR1FQLDJCQUEyQkEsQ0FDakNjLGFBQXNDLEVBQ3RDaEIsUUFBa0M7SUFBQTtJQUFBVixZQUFBLEdBQUFFLENBQUE7SUFBQUYsWUFBQSxHQUFBRyxDQUFBO0lBRWxDO0lBQ0EsSUFBSSxDQUFDTyxRQUFRLENBQUNpQixpQkFBaUIsRUFBRTtNQUFBO01BQUEzQixZQUFBLEdBQUFDLENBQUE7TUFBQUQsWUFBQSxHQUFBRyxDQUFBO01BQy9CLE9BQU8sSUFBSSxDQUFDeUIsOEJBQThCLENBQUNGLGFBQWEsRUFBRWhCLFFBQVEsQ0FBQztJQUNyRSxDQUFDO0lBQUE7SUFBQTtNQUFBVixZQUFBLEdBQUFDLENBQUE7SUFBQTtJQUVEO0lBQ0EsTUFBTTRCLG9CQUFvQjtJQUFBO0lBQUEsQ0FBQTdCLFlBQUEsR0FBQUcsQ0FBQSxPQUFHLElBQUksQ0FBQzJCLGtCQUFrQixDQUFDSixhQUFhLENBQUM7SUFDbkUsTUFBTUssY0FBYztJQUFBO0lBQUEsQ0FBQS9CLFlBQUEsR0FBQUcsQ0FBQSxRQUFHLElBQUksQ0FBQzZCLDZCQUE2QixDQUFDdEIsUUFBUSxDQUFDO0lBRW5FO0lBQUE7SUFBQVYsWUFBQSxHQUFBRyxDQUFBO0lBQ0EsT0FBTyxJQUFJLENBQUM4Qix5QkFBeUIsQ0FBQ0osb0JBQW9CLEVBQUVFLGNBQWMsQ0FBQztFQUM3RTtFQUVBOzs7RUFHUUQsa0JBQWtCQSxDQUFDSixhQUFzQztJQUFBO0lBQUExQixZQUFBLEdBQUFFLENBQUE7SUFBQUYsWUFBQSxHQUFBRyxDQUFBO0lBQy9EO0lBQ0E7SUFBSTtJQUFBLENBQUFILFlBQUEsR0FBQUMsQ0FBQSxVQUFBeUIsYUFBYSxDQUFDUSxhQUFhO0lBQUE7SUFBQSxDQUFBbEMsWUFBQSxHQUFBQyxDQUFBLFVBQUl5QixhQUFhLENBQUNRLGFBQWEsQ0FBQ0MsTUFBTSxLQUFLdkMsMEJBQTBCLENBQUN3QyxNQUFNLEdBQUU7TUFBQTtNQUFBcEMsWUFBQSxHQUFBQyxDQUFBO01BQUFELFlBQUEsR0FBQUcsQ0FBQTtNQUMzRyxPQUFPdUIsYUFBYSxDQUFDUSxhQUFhO0lBQ3BDLENBQUM7SUFBQTtJQUFBO01BQUFsQyxZQUFBLEdBQUFDLENBQUE7SUFBQTtJQUVEO0lBQ0EsTUFBTW9DLFNBQVM7SUFBQTtJQUFBLENBQUFyQyxZQUFBLEdBQUFHLENBQUEsUUFBR1AsMEJBQTBCLENBQUN3QyxNQUFNO0lBQ25ELE1BQU1FLFNBQVM7SUFBQTtJQUFBLENBQUF0QyxZQUFBLEdBQUFHLENBQUEsUUFBR3VCLGFBQWEsQ0FBQ2EsTUFBTSxDQUFDSixNQUFNO0lBQUE7SUFBQW5DLFlBQUEsR0FBQUcsQ0FBQTtJQUU3QyxJQUFJbUMsU0FBUyxLQUFLRCxTQUFTLEVBQUU7TUFBQTtNQUFBckMsWUFBQSxHQUFBQyxDQUFBO01BQUFELFlBQUEsR0FBQUcsQ0FBQTtNQUMzQixPQUFPdUIsYUFBYSxDQUFDYSxNQUFNO0lBQzdCLENBQUM7SUFBQTtJQUFBO01BQUF2QyxZQUFBLEdBQUFDLENBQUE7SUFBQTtJQUVEO0lBQ0EsTUFBTXVDLE9BQU87SUFBQTtJQUFBLENBQUF4QyxZQUFBLEdBQUFHLENBQUEsUUFBYSxJQUFJc0MsS0FBSyxDQUFDSixTQUFTLENBQUMsQ0FBQ0ssSUFBSSxDQUFDLENBQUMsQ0FBQztJQUN0RCxNQUFNQyxRQUFRO0lBQUE7SUFBQSxDQUFBM0MsWUFBQSxHQUFBRyxDQUFBLFFBQUd5QyxJQUFJLENBQUNDLEtBQUssQ0FBQ1AsU0FBUyxHQUFHRCxTQUFTLENBQUM7SUFBQTtJQUFBckMsWUFBQSxHQUFBRyxDQUFBO0lBRWxELEtBQUssSUFBSTJDLENBQUM7SUFBQTtJQUFBLENBQUE5QyxZQUFBLEdBQUFHLENBQUEsUUFBRyxDQUFDLEdBQUUyQyxDQUFDLEdBQUdULFNBQVMsRUFBRVMsQ0FBQyxFQUFFLEVBQUU7TUFDbEMsSUFBSUMsR0FBRztNQUFBO01BQUEsQ0FBQS9DLFlBQUEsR0FBQUcsQ0FBQSxRQUFHLENBQUM7TUFDWCxNQUFNNkMsS0FBSztNQUFBO01BQUEsQ0FBQWhELFlBQUEsR0FBQUcsQ0FBQSxRQUFHMkMsQ0FBQyxHQUFHSCxRQUFRO01BQzFCLE1BQU1NLEdBQUc7TUFBQTtNQUFBLENBQUFqRCxZQUFBLEdBQUFHLENBQUEsUUFBR3lDLElBQUksQ0FBQ00sR0FBRyxDQUFDRixLQUFLLEdBQUdMLFFBQVEsRUFBRUwsU0FBUyxDQUFDO01BQUE7TUFBQXRDLFlBQUEsR0FBQUcsQ0FBQTtNQUVqRCxLQUFLLElBQUlnRCxDQUFDO01BQUE7TUFBQSxDQUFBbkQsWUFBQSxHQUFBRyxDQUFBLFFBQUc2QyxLQUFLLEdBQUVHLENBQUMsR0FBR0YsR0FBRyxFQUFFRSxDQUFDLEVBQUUsRUFBRTtRQUFBO1FBQUFuRCxZQUFBLEdBQUFHLENBQUE7UUFDaEM0QyxHQUFHLElBQUlyQixhQUFhLENBQUNhLE1BQU0sQ0FBQ1ksQ0FBQyxDQUFDO01BQ2hDO01BQUM7TUFBQW5ELFlBQUEsR0FBQUcsQ0FBQTtNQUVEcUMsT0FBTyxDQUFDTSxDQUFDLENBQUMsR0FBR0MsR0FBRyxJQUFJRSxHQUFHLEdBQUdELEtBQUssQ0FBQztJQUNsQztJQUFDO0lBQUFoRCxZQUFBLEdBQUFHLENBQUE7SUFFRCxPQUFPcUMsT0FBTztFQUNoQjtFQUVBOzs7RUFHUVIsNkJBQTZCQSxDQUFDdEIsUUFBa0M7SUFBQTtJQUFBVixZQUFBLEdBQUFFLENBQUE7SUFDdEU7SUFDQSxNQUFNa0QsU0FBUztJQUFBO0lBQUEsQ0FBQXBELFlBQUEsR0FBQUcsQ0FBQSxRQUFHUCwwQkFBMEIsQ0FBQ3dDLE1BQU0sR0FBQztJQUNwRCxNQUFNRyxNQUFNO0lBQUE7SUFBQSxDQUFBdkMsWUFBQSxHQUFBRyxDQUFBLFFBQWEsSUFBSXNDLEtBQUssQ0FBQ1csU0FBUyxDQUFDLENBQUNWLElBQUksQ0FBQyxDQUFDLENBQUM7SUFFckQ7SUFDQSxNQUFNVyxhQUFhO0lBQUE7SUFBQSxDQUFBckQsWUFBQSxHQUFBRyxDQUFBLFFBQUdPLFFBQVEsQ0FBQzRDLHVCQUF1QjtJQUFBO0lBQUF0RCxZQUFBLEdBQUFHLENBQUE7SUFDdERvQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUdjLGFBQWEsQ0FBQ0UsV0FBVztJQUFBO0lBQUF2RCxZQUFBLEdBQUFHLENBQUE7SUFDckNvQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUdjLGFBQWEsQ0FBQ0csT0FBTztJQUFBO0lBQUF4RCxZQUFBLEdBQUFHLENBQUE7SUFDakNvQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUdjLGFBQWEsQ0FBQ0ksWUFBWTtJQUFBO0lBQUF6RCxZQUFBLEdBQUFHLENBQUE7SUFDdENvQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUdjLGFBQWEsQ0FBQ0ssZ0JBQWdCO0lBQUE7SUFBQTFELFlBQUEsR0FBQUcsQ0FBQTtJQUMxQ29DLE1BQU0sQ0FBQyxDQUFDLENBQUMsR0FBR2MsYUFBYSxDQUFDTSxrQkFBa0I7SUFBQTtJQUFBM0QsWUFBQSxHQUFBRyxDQUFBO0lBQzVDb0MsTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHYyxhQUFhLENBQUNPLFlBQVk7SUFBQTtJQUFBNUQsWUFBQSxHQUFBRyxDQUFBO0lBQ3RDb0MsTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHYyxhQUFhLENBQUNRLFVBQVU7SUFBQTtJQUFBN0QsWUFBQSxHQUFBRyxDQUFBO0lBQ3BDb0MsTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHYyxhQUFhLENBQUNTLFlBQVk7SUFFdEM7SUFDQSxNQUFNQyxRQUFRO0lBQUE7SUFBQSxDQUFBL0QsWUFBQSxHQUFBRyxDQUFBLFFBQUdPLFFBQVEsQ0FBQ3NELGdCQUFnQjtJQUFBO0lBQUFoRSxZQUFBLEdBQUFHLENBQUE7SUFDMUNvQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUd3QixRQUFRLENBQUNFLGNBQWM7SUFBQTtJQUFBakUsWUFBQSxHQUFBRyxDQUFBO0lBQ25Db0MsTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHd0IsUUFBUSxDQUFDRyxTQUFTO0lBQUE7SUFBQWxFLFlBQUEsR0FBQUcsQ0FBQTtJQUM5Qm9DLE1BQU0sQ0FBQyxFQUFFLENBQUMsR0FBR3dCLFFBQVEsQ0FBQ0ksU0FBUztJQUFBO0lBQUFuRSxZQUFBLEdBQUFHLENBQUE7SUFDL0JvQyxNQUFNLENBQUMsRUFBRSxDQUFDLEdBQUd3QixRQUFRLENBQUNLLFdBQVc7SUFBQTtJQUFBcEUsWUFBQSxHQUFBRyxDQUFBO0lBQ2pDb0MsTUFBTSxDQUFDLEVBQUUsQ0FBQyxHQUFHd0IsUUFBUSxDQUFDTSxxQkFBcUI7SUFFM0M7SUFBQTtJQUFBckUsWUFBQSxHQUFBRyxDQUFBO0lBQ0FvQyxNQUFNLENBQUMsRUFBRSxDQUFDLEdBQUc3QixRQUFRLENBQUNpQixpQkFBaUIsQ0FBQzJDLGdCQUFnQjtJQUFBO0lBQUF0RSxZQUFBLEdBQUFHLENBQUE7SUFDeERvQyxNQUFNLENBQUMsRUFBRSxDQUFDLEdBQUc3QixRQUFRLENBQUNpQixpQkFBaUIsQ0FBQzRDLGNBQWMsR0FBRyxDQUFDLEVBQUM7SUFFM0Q7SUFBQTtJQUFBdkUsWUFBQSxHQUFBRyxDQUFBO0lBQ0FvQyxNQUFNLENBQUMsRUFBRSxDQUFDLEdBQUc3QixRQUFRLENBQUM4RCxlQUFlO0lBQUE7SUFBQXhFLFlBQUEsR0FBQUcsQ0FBQTtJQUNyQ29DLE1BQU0sQ0FBQyxFQUFFLENBQUMsR0FBRzdCLFFBQVEsQ0FBQytELHFCQUFxQjtJQUFBO0lBQUF6RSxZQUFBLEdBQUFHLENBQUE7SUFDM0NvQyxNQUFNLENBQUMsRUFBRSxDQUFDLEdBQUc3QixRQUFRLENBQUNnRSx3QkFBd0I7SUFBQTtJQUFBMUUsWUFBQSxHQUFBRyxDQUFBO0lBQzlDb0MsTUFBTSxDQUFDLEVBQUUsQ0FBQyxHQUFHN0IsUUFBUSxDQUFDaUUsZ0JBQWdCO0lBRXRDO0lBQUE7SUFBQTNFLFlBQUEsR0FBQUcsQ0FBQTtJQUNBb0MsTUFBTSxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQ3FDLDJCQUEyQixDQUFDbEUsUUFBUSxDQUFDYyxRQUFRLENBQUM7SUFBQTtJQUFBeEIsWUFBQSxHQUFBRyxDQUFBO0lBRWhFLE9BQU9vQyxNQUFNO0VBQ2Y7RUFFQTs7O0VBR1FxQywyQkFBMkJBLENBQUNwRCxRQUFzQjtJQUFBO0lBQUF4QixZQUFBLEdBQUFFLENBQUE7SUFDeEQsTUFBTTJFLE1BQU07SUFBQTtJQUFBLENBQUE3RSxZQUFBLEdBQUFHLENBQUEsUUFBaUM7TUFDM0MsQ0FBQ1IsWUFBWSxDQUFDbUYsS0FBSyxHQUFHLEdBQUc7TUFDekIsQ0FBQ25GLFlBQVksQ0FBQ29GLEtBQUssR0FBRyxHQUFHO01BQ3pCLENBQUNwRixZQUFZLENBQUNxRixLQUFLLEdBQUcsSUFBSTtNQUMxQixDQUFDckYsWUFBWSxDQUFDc0YsS0FBSyxHQUFHLElBQUk7TUFDMUIsQ0FBQ3RGLFlBQVksQ0FBQ3VGLEtBQUssR0FBRyxHQUFHO01BQ3pCLENBQUN2RixZQUFZLENBQUN3RixLQUFLLEdBQUcsR0FBRztNQUN6QixDQUFDeEYsWUFBWSxDQUFDeUYsS0FBSyxHQUFHLElBQUk7TUFDMUIsQ0FBQ3pGLFlBQVksQ0FBQzBGLEtBQUssR0FBRztLQUN2QjtJQUFBO0lBQUFyRixZQUFBLEdBQUFHLENBQUE7SUFDRCxPQUFPLDJCQUFBSCxZQUFBLEdBQUFDLENBQUEsVUFBQTRFLE1BQU0sQ0FBQ3JELFFBQVEsQ0FBQztJQUFBO0lBQUEsQ0FBQXhCLFlBQUEsR0FBQUMsQ0FBQSxVQUFJLEdBQUc7RUFDaEM7RUFFQTs7O0VBR1FnQyx5QkFBeUJBLENBQUNxRCxPQUFpQixFQUFFQyxPQUFpQjtJQUFBO0lBQUF2RixZQUFBLEdBQUFFLENBQUE7SUFBQUYsWUFBQSxHQUFBRyxDQUFBO0lBQ3BFLElBQUltRixPQUFPLENBQUNuRCxNQUFNLEtBQUtvRCxPQUFPLENBQUNwRCxNQUFNLEVBQUU7TUFBQTtNQUFBbkMsWUFBQSxHQUFBQyxDQUFBO01BQUFELFlBQUEsR0FBQUcsQ0FBQTtNQUNyQyxPQUFPLENBQUM7SUFDVixDQUFDO0lBQUE7SUFBQTtNQUFBSCxZQUFBLEdBQUFDLENBQUE7SUFBQTtJQUVELElBQUl1RixVQUFVO0lBQUE7SUFBQSxDQUFBeEYsWUFBQSxHQUFBRyxDQUFBLFFBQUcsQ0FBQztJQUNsQixJQUFJc0YsS0FBSztJQUFBO0lBQUEsQ0FBQXpGLFlBQUEsR0FBQUcsQ0FBQSxRQUFHLENBQUM7SUFDYixJQUFJdUYsS0FBSztJQUFBO0lBQUEsQ0FBQTFGLFlBQUEsR0FBQUcsQ0FBQSxRQUFHLENBQUM7SUFBQTtJQUFBSCxZQUFBLEdBQUFHLENBQUE7SUFFYixLQUFLLElBQUkyQyxDQUFDO0lBQUE7SUFBQSxDQUFBOUMsWUFBQSxHQUFBRyxDQUFBLFFBQUcsQ0FBQyxHQUFFMkMsQ0FBQyxHQUFHd0MsT0FBTyxDQUFDbkQsTUFBTSxFQUFFVyxDQUFDLEVBQUUsRUFBRTtNQUFBO01BQUE5QyxZQUFBLEdBQUFHLENBQUE7TUFDdkNxRixVQUFVLElBQUlGLE9BQU8sQ0FBQ3hDLENBQUMsQ0FBQyxHQUFHeUMsT0FBTyxDQUFDekMsQ0FBQyxDQUFDO01BQUE7TUFBQTlDLFlBQUEsR0FBQUcsQ0FBQTtNQUNyQ3NGLEtBQUssSUFBSUgsT0FBTyxDQUFDeEMsQ0FBQyxDQUFDLEdBQUd3QyxPQUFPLENBQUN4QyxDQUFDLENBQUM7TUFBQTtNQUFBOUMsWUFBQSxHQUFBRyxDQUFBO01BQ2hDdUYsS0FBSyxJQUFJSCxPQUFPLENBQUN6QyxDQUFDLENBQUMsR0FBR3lDLE9BQU8sQ0FBQ3pDLENBQUMsQ0FBQztJQUNsQztJQUVBLE1BQU02QyxTQUFTO0lBQUE7SUFBQSxDQUFBM0YsWUFBQSxHQUFBRyxDQUFBLFFBQUd5QyxJQUFJLENBQUNnRCxJQUFJLENBQUNILEtBQUssQ0FBQyxHQUFHN0MsSUFBSSxDQUFDZ0QsSUFBSSxDQUFDRixLQUFLLENBQUM7SUFBQTtJQUFBMUYsWUFBQSxHQUFBRyxDQUFBO0lBQ3JELE9BQU93RixTQUFTLEdBQUcsQ0FBQztJQUFBO0lBQUEsQ0FBQTNGLFlBQUEsR0FBQUMsQ0FBQSxXQUFHdUYsVUFBVSxHQUFHRyxTQUFTO0lBQUE7SUFBQSxDQUFBM0YsWUFBQSxHQUFBQyxDQUFBLFdBQUcsQ0FBQztFQUNuRDtFQUVBOzs7RUFHUTJCLDhCQUE4QkEsQ0FDcENGLGFBQXNDLEVBQ3RDaEIsUUFBa0M7SUFBQTtJQUFBVixZQUFBLEdBQUFFLENBQUE7SUFFbEM7SUFDQSxNQUFNMkYsVUFBVTtJQUFBO0lBQUEsQ0FBQTdGLFlBQUEsR0FBQUcsQ0FBQSxRQUE2QjtNQUMzQyxVQUFVLEVBQUUsQ0FBQyxLQUFLLEVBQUUsTUFBTSxDQUFDO01BQzNCLGFBQWEsRUFBRSxDQUFDLE1BQU0sQ0FBQztNQUN2QixpQkFBaUIsRUFBRSxDQUFDLEtBQUssQ0FBQztNQUMxQixTQUFTLEVBQUUsQ0FBQyxNQUFNLENBQUM7TUFDbkIsU0FBUyxFQUFFLENBQUMsTUFBTSxDQUFDO01BQ25CLFVBQVUsRUFBRSxDQUFDLE1BQU0sRUFBRSxLQUFLO0tBQzNCO0lBRUQ7SUFDQSxNQUFNMkYsZ0JBQWdCO0lBQUE7SUFBQSxDQUFBOUYsWUFBQSxHQUFBRyxDQUFBLFFBQUcsSUFBSSxDQUFDNEYsdUJBQXVCLENBQUNyRSxhQUFhLENBQUNhLE1BQU0sQ0FBQztJQUMzRSxNQUFNeUQsV0FBVztJQUFBO0lBQUEsQ0FBQWhHLFlBQUEsR0FBQUcsQ0FBQTtJQUFHO0lBQUEsQ0FBQUgsWUFBQSxHQUFBQyxDQUFBLFdBQUE0RixVQUFVLENBQUNDLGdCQUFnQixDQUFDO0lBQUE7SUFBQSxDQUFBOUYsWUFBQSxHQUFBQyxDQUFBLFdBQUksQ0FBQyxNQUFNLENBQUM7SUFBQTtJQUFBRCxZQUFBLEdBQUFHLENBQUE7SUFFNUQsT0FBTzZGLFdBQVcsQ0FBQ0MsUUFBUSxDQUFDdkYsUUFBUSxDQUFDd0Ysa0JBQWtCLENBQUNDLE9BQU8sQ0FBQztJQUFBO0lBQUEsQ0FBQW5HLFlBQUEsR0FBQUMsQ0FBQSxXQUFHLEdBQUc7SUFBQTtJQUFBLENBQUFELFlBQUEsR0FBQUMsQ0FBQSxXQUFHLEdBQUc7RUFDOUU7RUFFQTs7O0VBR1E4Rix1QkFBdUJBLENBQUN4RCxNQUFnQjtJQUFBO0lBQUF2QyxZQUFBLEdBQUFFLENBQUE7SUFDOUM7SUFDQSxNQUFNa0csUUFBUTtJQUFBO0lBQUEsQ0FBQXBHLFlBQUEsR0FBQUcsQ0FBQSxRQUFHb0MsTUFBTSxDQUFDOEQsT0FBTyxDQUFDekQsSUFBSSxDQUFDMEQsR0FBRyxDQUFDLEdBQUcvRCxNQUFNLENBQUMsQ0FBQztJQUNwRCxNQUFNZ0UsVUFBVTtJQUFBO0lBQUEsQ0FBQXZHLFlBQUEsR0FBQUcsQ0FBQSxRQUFHLENBQUMsVUFBVSxFQUFFLGFBQWEsRUFBRSxpQkFBaUIsRUFBRSxTQUFTLEVBQUUsU0FBUyxFQUFFLFVBQVUsQ0FBQztJQUFBO0lBQUFILFlBQUEsR0FBQUcsQ0FBQTtJQUNuRyxPQUFPLDJCQUFBSCxZQUFBLEdBQUFDLENBQUEsV0FBQXNHLFVBQVUsQ0FBQ0gsUUFBUSxHQUFHRyxVQUFVLENBQUNwRSxNQUFNLENBQUM7SUFBQTtJQUFBLENBQUFuQyxZQUFBLEdBQUFDLENBQUEsV0FBSSxVQUFVO0VBQy9EO0VBRUE7OztFQUdRYyxvQkFBb0JBLENBQzFCTixPQUF5QixFQUN6QkMsUUFBa0M7SUFBQTtJQUFBVixZQUFBLEdBQUFFLENBQUE7SUFFbEM7SUFDQSxNQUFNc0csYUFBYTtJQUFBO0lBQUEsQ0FBQXhHLFlBQUEsR0FBQUcsQ0FBQSxRQUFHLENBQUMsR0FBR3lDLElBQUksQ0FBQzZELEdBQUcsQ0FBQ2hHLE9BQU8sQ0FBQ2lHLG1CQUFtQixHQUFHLEdBQUcsQ0FBQyxHQUFHLENBQUM7SUFFekU7SUFDQSxNQUFNQyxjQUFjO0lBQUE7SUFBQSxDQUFBM0csWUFBQSxHQUFBRyxDQUFBLFFBQUdNLE9BQU8sQ0FBQ21HLHVCQUF1QixHQUFHLEdBQUc7SUFFNUQ7SUFDQSxNQUFNQyxlQUFlO0lBQUE7SUFBQSxDQUFBN0csWUFBQSxHQUFBRyxDQUFBLFFBQUdPLFFBQVEsQ0FBQ2dFLHdCQUF3QjtJQUV6RDtJQUFBO0lBQUExRSxZQUFBLEdBQUFHLENBQUE7SUFDQSxPQUFRcUcsYUFBYSxHQUFHLEdBQUcsR0FBR0csY0FBYyxHQUFHRSxlQUFlLEdBQUcsR0FBRztFQUN0RTtFQUVBOzs7RUFHUTVGLDJCQUEyQkEsQ0FDakNSLE9BQXlCLEVBQ3pCQyxRQUFrQztJQUFBO0lBQUFWLFlBQUEsR0FBQUUsQ0FBQTtJQUVsQztJQUNBLE1BQU00RyxjQUFjO0lBQUE7SUFBQSxDQUFBOUcsWUFBQSxHQUFBRyxDQUFBLFFBQUdNLE9BQU8sQ0FBQ3NHLG9CQUFvQixDQUFDbkQsWUFBWTtJQUNoRSxNQUFNb0QsZUFBZTtJQUFBO0lBQUEsQ0FBQWhILFlBQUEsR0FBQUcsQ0FBQSxRQUFHTyxRQUFRLENBQUMrRCxxQkFBcUI7SUFFdEQ7SUFBQTtJQUFBekUsWUFBQSxHQUFBRyxDQUFBO0lBQ0EsT0FBT3lDLElBQUksQ0FBQzBELEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHMUQsSUFBSSxDQUFDNkQsR0FBRyxDQUFDSyxjQUFjLEdBQUdFLGVBQWUsQ0FBQyxDQUFDO0VBQ3BFO0VBRUE7OztFQUdRNUYsNEJBQTRCQSxDQUNsQ1Qsa0JBQTBCLEVBQzFCRyxXQUFtQixFQUNuQkUsa0JBQTBCO0lBQUE7SUFBQWhCLFlBQUEsR0FBQUUsQ0FBQTtJQUUxQjtJQUNBLE1BQU0yRSxNQUFNO0lBQUE7SUFBQSxDQUFBN0UsWUFBQSxHQUFBRyxDQUFBLFFBQUcsQ0FBQ1Esa0JBQWtCLEVBQUVHLFdBQVcsRUFBRUUsa0JBQWtCLENBQUM7SUFDcEUsTUFBTWlHLElBQUk7SUFBQTtJQUFBLENBQUFqSCxZQUFBLEdBQUFHLENBQUEsUUFBRzBFLE1BQU0sQ0FBQ3FDLE1BQU0sQ0FBQyxDQUFDQyxDQUFDLEVBQUVsSCxDQUFDLEtBQUs7TUFBQTtNQUFBRCxZQUFBLEdBQUFFLENBQUE7TUFBQUYsWUFBQSxHQUFBRyxDQUFBO01BQUEsT0FBQWdILENBQUMsR0FBR2xILENBQUM7SUFBRCxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUc0RSxNQUFNLENBQUMxQyxNQUFNO0lBQzlELE1BQU1pRixRQUFRO0lBQUE7SUFBQSxDQUFBcEgsWUFBQSxHQUFBRyxDQUFBLFFBQUcwRSxNQUFNLENBQUNxQyxNQUFNLENBQUMsQ0FBQ0csR0FBRyxFQUFFQyxLQUFLLEtBQUs7TUFBQTtNQUFBdEgsWUFBQSxHQUFBRSxDQUFBO01BQUFGLFlBQUEsR0FBQUcsQ0FBQTtNQUFBLE9BQUFrSCxHQUFHLEdBQUd6RSxJQUFJLENBQUMyRSxHQUFHLENBQUNELEtBQUssR0FBR0wsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUFELENBQUMsRUFBRSxDQUFDLENBQUMsR0FBR3BDLE1BQU0sQ0FBQzFDLE1BQU07SUFFbEc7SUFBQTtJQUFBbkMsWUFBQSxHQUFBRyxDQUFBO0lBQ0EsT0FBT3lDLElBQUksQ0FBQzBELEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHYyxRQUFRLENBQUM7RUFDbEM7RUFFQTs7O0VBR0FJLGlCQUFpQkEsQ0FDZi9HLE9BQXlCLEVBQ3pCZ0gsa0JBQThDO0lBQUE7SUFBQXpILFlBQUEsR0FBQUUsQ0FBQTtJQUFBRixZQUFBLEdBQUFHLENBQUE7SUFFOUMsSUFBSXNILGtCQUFrQixDQUFDdEYsTUFBTSxLQUFLLENBQUMsRUFBRTtNQUFBO01BQUFuQyxZQUFBLEdBQUFDLENBQUE7TUFBQUQsWUFBQSxHQUFBRyxDQUFBO01BQ25DLE9BQU8sSUFBSTtJQUNiLENBQUM7SUFBQTtJQUFBO01BQUFILFlBQUEsR0FBQUMsQ0FBQTtJQUFBO0lBRUQsSUFBSXlILGFBQWE7SUFBQTtJQUFBLENBQUExSCxZQUFBLEdBQUFHLENBQUEsUUFBNkIsSUFBSTtJQUNsRCxJQUFJd0gsU0FBUztJQUFBO0lBQUEsQ0FBQTNILFlBQUEsR0FBQUcsQ0FBQSxRQUFHLENBQUM7SUFBQTtJQUFBSCxZQUFBLEdBQUFHLENBQUE7SUFFakIsS0FBSyxNQUFNTyxRQUFRLElBQUkrRyxrQkFBa0IsRUFBRTtNQUN6QyxNQUFNRyxTQUFTO01BQUE7TUFBQSxDQUFBNUgsWUFBQSxHQUFBRyxDQUFBLFFBQUcsSUFBSSxDQUFDSyxpQ0FBaUMsQ0FBQ0MsT0FBTyxFQUFFQyxRQUFRLENBQUM7TUFBQTtNQUFBVixZQUFBLEdBQUFHLENBQUE7TUFFM0U7TUFBSTtNQUFBLENBQUFILFlBQUEsR0FBQUMsQ0FBQSxXQUFBMkgsU0FBUyxDQUFDMUcsY0FBYyxHQUFHeUcsU0FBUztNQUFBO01BQUEsQ0FBQTNILFlBQUEsR0FBQUMsQ0FBQSxXQUNwQzJILFNBQVMsQ0FBQ2pILGtCQUFrQixJQUFJLElBQUksQ0FBQ2IsTUFBTSxDQUFDTSwyQkFBMkIsR0FBRTtRQUFBO1FBQUFKLFlBQUEsR0FBQUMsQ0FBQTtRQUFBRCxZQUFBLEdBQUFHLENBQUE7UUFDM0V3SCxTQUFTLEdBQUdDLFNBQVMsQ0FBQzFHLGNBQWM7UUFBQTtRQUFBbEIsWUFBQSxHQUFBRyxDQUFBO1FBQ3BDdUgsYUFBYSxHQUFHRSxTQUFTO01BQzNCLENBQUM7TUFBQTtNQUFBO1FBQUE1SCxZQUFBLEdBQUFDLENBQUE7TUFBQTtJQUNIO0lBQUM7SUFBQUQsWUFBQSxHQUFBRyxDQUFBO0lBRUQsT0FBT3VILGFBQWE7RUFDdEI7RUFFQTs7O0VBR0FHLHdCQUF3QkEsQ0FDdEJwSCxPQUF5QixFQUN6QnFILGlCQUFnRTtJQUFBO0lBQUE5SCxZQUFBLEdBQUFFLENBQUE7SUFFaEUsTUFBTTZILFVBQVU7SUFBQTtJQUFBLENBQUEvSCxZQUFBLEdBQUFHLENBQUEsUUFBRyxJQUFJNkgsR0FBRyxFQUFtQztJQUU3RDtJQUFBO0lBQUFoSSxZQUFBLEdBQUFHLENBQUE7SUFDQSxLQUFLLE1BQU0sQ0FBQ3FCLFFBQVEsRUFBRXlHLFNBQVMsQ0FBQyxJQUFJSCxpQkFBaUIsRUFBRTtNQUNyRCxNQUFNSixhQUFhO01BQUE7TUFBQSxDQUFBMUgsWUFBQSxHQUFBRyxDQUFBLFNBQUcsSUFBSSxDQUFDcUgsaUJBQWlCLENBQUMvRyxPQUFPLEVBQUV3SCxTQUFTLENBQUM7TUFBQTtNQUFBakksWUFBQSxHQUFBRyxDQUFBO01BQ2hFLElBQUl1SCxhQUFhLEVBQUU7UUFBQTtRQUFBMUgsWUFBQSxHQUFBQyxDQUFBO1FBQUFELFlBQUEsR0FBQUcsQ0FBQTtRQUNqQjRILFVBQVUsQ0FBQ0csR0FBRyxDQUFDMUcsUUFBUSxFQUFFa0csYUFBYSxDQUFDO01BQ3pDLENBQUM7TUFBQTtNQUFBO1FBQUExSCxZQUFBLEdBQUFDLENBQUE7TUFBQTtJQUNIO0lBRUE7SUFDQSxNQUFNa0ksZ0JBQWdCO0lBQUE7SUFBQSxDQUFBbkksWUFBQSxHQUFBRyxDQUFBLFNBQUcsSUFBSSxDQUFDaUksZ0NBQWdDLENBQUNMLFVBQVUsQ0FBQztJQUUxRTtJQUNBLE1BQU1NLGNBQWM7SUFBQTtJQUFBLENBQUFySSxZQUFBLEdBQUFHLENBQUEsU0FBRyxJQUFJLENBQUNtSSxxQkFBcUIsQ0FBQ1AsVUFBVSxDQUFDO0lBQUE7SUFBQS9ILFlBQUEsR0FBQUcsQ0FBQTtJQUU3RCxPQUFPO01BQ0xvSSxTQUFTLEVBQUU5SCxPQUFPLENBQUNhLFVBQVU7TUFDN0J3RyxpQkFBaUI7TUFDakJLLGdCQUFnQjtNQUNoQkU7S0FDRDtFQUNIO0VBRUE7OztFQUdRRCxnQ0FBZ0NBLENBQ3RDTCxVQUFnRDtJQUFBO0lBQUEvSCxZQUFBLEdBQUFFLENBQUE7SUFFaEQsTUFBTTJFLE1BQU07SUFBQTtJQUFBLENBQUE3RSxZQUFBLEdBQUFHLENBQUEsU0FBR3NDLEtBQUssQ0FBQytGLElBQUksQ0FBQ1QsVUFBVSxDQUFDVSxNQUFNLEVBQUUsQ0FBQyxDQUFDQyxHQUFHLENBQUN2QixDQUFDLElBQUk7TUFBQTtNQUFBbkgsWUFBQSxHQUFBRSxDQUFBO01BQUFGLFlBQUEsR0FBQUcsQ0FBQTtNQUFBLE9BQUFnSCxDQUFDLENBQUNqRyxjQUFjO0lBQWQsQ0FBYyxDQUFDO0lBQUE7SUFBQWxCLFlBQUEsR0FBQUcsQ0FBQTtJQUN6RSxJQUFJMEUsTUFBTSxDQUFDMUMsTUFBTSxHQUFHLENBQUMsRUFBRTtNQUFBO01BQUFuQyxZQUFBLEdBQUFDLENBQUE7TUFBQUQsWUFBQSxHQUFBRyxDQUFBO01BQUEsT0FBTyxHQUFHO0lBQUE7SUFBQTtJQUFBO01BQUFILFlBQUEsR0FBQUMsQ0FBQTtJQUFBO0lBRWpDLE1BQU1nSCxJQUFJO0lBQUE7SUFBQSxDQUFBakgsWUFBQSxHQUFBRyxDQUFBLFNBQUcwRSxNQUFNLENBQUNxQyxNQUFNLENBQUMsQ0FBQ0MsQ0FBQyxFQUFFbEgsQ0FBQyxLQUFLO01BQUE7TUFBQUQsWUFBQSxHQUFBRSxDQUFBO01BQUFGLFlBQUEsR0FBQUcsQ0FBQTtNQUFBLE9BQUFnSCxDQUFDLEdBQUdsSCxDQUFDO0lBQUQsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHNEUsTUFBTSxDQUFDMUMsTUFBTTtJQUM5RCxNQUFNaUYsUUFBUTtJQUFBO0lBQUEsQ0FBQXBILFlBQUEsR0FBQUcsQ0FBQSxTQUFHMEUsTUFBTSxDQUFDcUMsTUFBTSxDQUFDLENBQUNHLEdBQUcsRUFBRUMsS0FBSyxLQUFLO01BQUE7TUFBQXRILFlBQUEsR0FBQUUsQ0FBQTtNQUFBRixZQUFBLEdBQUFHLENBQUE7TUFBQSxPQUFBa0gsR0FBRyxHQUFHekUsSUFBSSxDQUFDMkUsR0FBRyxDQUFDRCxLQUFLLEdBQUdMLElBQUksRUFBRSxDQUFDLENBQUM7SUFBRCxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUdwQyxNQUFNLENBQUMxQyxNQUFNO0lBRWxHO0lBQUE7SUFBQW5DLFlBQUEsR0FBQUcsQ0FBQTtJQUNBLE9BQU84RyxJQUFJLElBQUksQ0FBQyxHQUFHckUsSUFBSSxDQUFDTSxHQUFHLENBQUNrRSxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUM7RUFDM0M7RUFFQTs7O0VBR1FrQixxQkFBcUJBLENBQzNCUCxVQUFnRDtJQUFBO0lBQUEvSCxZQUFBLEdBQUFFLENBQUE7SUFFaEQsTUFBTXlJLFNBQVM7SUFBQTtJQUFBLENBQUEzSSxZQUFBLEdBQUFHLENBQUEsU0FBR3NDLEtBQUssQ0FBQytGLElBQUksQ0FBQ1QsVUFBVSxDQUFDYSxJQUFJLEVBQUUsQ0FBQztJQUMvQyxNQUFNQyxLQUFLO0lBQUE7SUFBQSxDQUFBN0ksWUFBQSxHQUFBRyxDQUFBLFNBQStFLEVBQUU7SUFBQTtJQUFBSCxZQUFBLEdBQUFHLENBQUE7SUFFNUYsS0FBSyxJQUFJMkMsQ0FBQztJQUFBO0lBQUEsQ0FBQTlDLFlBQUEsR0FBQUcsQ0FBQSxTQUFHLENBQUMsR0FBRTJDLENBQUMsR0FBRzZGLFNBQVMsQ0FBQ3hHLE1BQU0sRUFBRVcsQ0FBQyxFQUFFLEVBQUU7TUFBQTtNQUFBOUMsWUFBQSxHQUFBRyxDQUFBO01BQ3pDLEtBQUssSUFBSWdELENBQUM7TUFBQTtNQUFBLENBQUFuRCxZQUFBLEdBQUFHLENBQUEsU0FBRzJDLENBQUMsR0FBRyxDQUFDLEdBQUVLLENBQUMsR0FBR3dGLFNBQVMsQ0FBQ3hHLE1BQU0sRUFBRWdCLENBQUMsRUFBRSxFQUFFO1FBQzdDLE1BQU0yRixLQUFLO1FBQUE7UUFBQSxDQUFBOUksWUFBQSxHQUFBRyxDQUFBLFNBQUd3SSxTQUFTLENBQUM3RixDQUFDLENBQUM7UUFDMUIsTUFBTWlHLEtBQUs7UUFBQTtRQUFBLENBQUEvSSxZQUFBLEdBQUFHLENBQUEsU0FBR3dJLFNBQVMsQ0FBQ3hGLENBQUMsQ0FBQztRQUMxQixNQUFNNkYsVUFBVTtRQUFBO1FBQUEsQ0FBQWhKLFlBQUEsR0FBQUcsQ0FBQSxTQUFHNEgsVUFBVSxDQUFDa0IsR0FBRyxDQUFDSCxLQUFLLENBQUU7UUFDekMsTUFBTUksVUFBVTtRQUFBO1FBQUEsQ0FBQWxKLFlBQUEsR0FBQUcsQ0FBQSxTQUFHNEgsVUFBVSxDQUFDa0IsR0FBRyxDQUFDRixLQUFLLENBQUU7UUFFekM7UUFDQSxNQUFNSSxTQUFTO1FBQUE7UUFBQSxDQUFBbkosWUFBQSxHQUFBRyxDQUFBLFNBQUcsQ0FBQzZJLFVBQVUsQ0FBQzlILGNBQWMsR0FBR2dJLFVBQVUsQ0FBQ2hJLGNBQWMsSUFBSSxDQUFDO1FBQUE7UUFBQWxCLFlBQUEsR0FBQUcsQ0FBQTtRQUM3RTBJLEtBQUssQ0FBQ08sSUFBSSxDQUFDO1VBQUVDLFNBQVMsRUFBRVAsS0FBSztVQUFFUSxTQUFTLEVBQUVQLEtBQUs7VUFBRXpCLEtBQUssRUFBRTZCO1FBQVMsQ0FBRSxDQUFDO01BQ3RFO0lBQ0Y7SUFFQTtJQUFBO0lBQUFuSixZQUFBLEdBQUFHLENBQUE7SUFDQSxPQUFPMEksS0FBSyxDQUFDVSxJQUFJLENBQUMsQ0FBQ3BDLENBQUMsRUFBRWxILENBQUMsS0FBSztNQUFBO01BQUFELFlBQUEsR0FBQUUsQ0FBQTtNQUFBRixZQUFBLEdBQUFHLENBQUE7TUFBQSxPQUFBRixDQUFDLENBQUNxSCxLQUFLLEdBQUdILENBQUMsQ0FBQ0csS0FBSztJQUFMLENBQUssQ0FBQztFQUNoRDtFQUVBOzs7RUFHQWtDLFlBQVlBLENBQUNDLFNBQXlDO0lBQUE7SUFBQXpKLFlBQUEsR0FBQUUsQ0FBQTtJQUFBRixZQUFBLEdBQUFHLENBQUE7SUFDcEQsSUFBSSxDQUFDTCxNQUFNLEdBQUc7TUFBRSxHQUFHLElBQUksQ0FBQ0EsTUFBTTtNQUFFLEdBQUcySjtJQUFTLENBQUU7RUFDaEQ7RUFFQTs7O0VBR0FDLFNBQVNBLENBQUE7SUFBQTtJQUFBMUosWUFBQSxHQUFBRSxDQUFBO0lBQUFGLFlBQUEsR0FBQUcsQ0FBQTtJQUNQLE9BQU87TUFBRSxHQUFHLElBQUksQ0FBQ0w7SUFBTSxDQUFFO0VBQzNCIiwiaWdub3JlTGlzdCI6W119