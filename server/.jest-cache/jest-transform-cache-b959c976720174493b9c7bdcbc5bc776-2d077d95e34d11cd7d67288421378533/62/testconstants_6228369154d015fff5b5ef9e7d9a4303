f027aa55f1af3daf23a0fc4983aa5c46
"use strict";

/**
 * 测试相关常量配置
 *
 * 定义测试中使用的常量值，便于维护和修改
 *
 * @fileoverview 测试常量配置
 * @version 1.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */
/* istanbul ignore next */
function cov_1y7000xrj6() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/config/test-constants.ts";
  var hash = "d99b3e487bbaa2820d5dfb1db5bff651d76e4b75";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/config/test-constants.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 404
        }
      },
      "2": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 27,
          column: 2
        }
      },
      "3": {
        start: {
          line: 29,
          column: 0
        },
        end: {
          line: 36,
          column: 2
        }
      },
      "4": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 47,
          column: 2
        }
      },
      "5": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 58,
          column: 2
        }
      },
      "6": {
        start: {
          line: 63,
          column: 0
        },
        end: {
          line: 67,
          column: 2
        }
      },
      "7": {
        start: {
          line: 69,
          column: 0
        },
        end: {
          line: 77,
          column: 2
        }
      },
      "8": {
        start: {
          line: 79,
          column: 0
        },
        end: {
          line: 84,
          column: 2
        }
      },
      "9": {
        start: {
          line: 86,
          column: 0
        },
        end: {
          line: 95,
          column: 2
        }
      },
      "10": {
        start: {
          line: 100,
          column: 0
        },
        end: {
          line: 100,
          column: 38
        }
      },
      "11": {
        start: {
          line: 102,
          column: 0
        },
        end: {
          line: 103,
          column: 90
        }
      },
      "12": {
        start: {
          line: 103,
          column: 28
        },
        end: {
          line: 103,
          column: 39
        }
      },
      "13": {
        start: {
          line: 105,
          column: 0
        },
        end: {
          line: 110,
          column: 2
        }
      },
      "14": {
        start: {
          line: 115,
          column: 0
        },
        end: {
          line: 124,
          column: 2
        }
      },
      "15": {
        start: {
          line: 126,
          column: 0
        },
        end: {
          line: 133,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 13
          }
        },
        loc: {
          start: {
            line: 103,
            column: 28
          },
          end: {
            line: 103,
            column: 39
          }
        },
        line: 103
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/config/test-constants.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,iBAAiB;AACJ,QAAA,uBAAuB,GAAG;IACrC,gBAAgB;IAChB,cAAc,EAAE,GAAG;IACnB,cAAc;IACd,YAAY,EAAE,GAAG;IACjB,eAAe;IACf,SAAS,EAAE,GAAG;IACd,eAAe;IACf,OAAO,EAAE,GAAG;CACJ,CAAA;AAEV,iBAAiB;AACJ,QAAA,sBAAsB,GAAG;IACpC,eAAe;IACf,eAAe,EAAE,EAAE;IACnB,gBAAgB;IAChB,qBAAqB,EAAE,GAAG;IAC1B,cAAc;IACd,eAAe,EAAE,GAAG;CACZ,CAAA;AAEV,iBAAiB;AACJ,QAAA,gBAAgB,GAAG;IAC9B,eAAe;IACf,gBAAgB,EAAE,CAAC;IACnB,eAAe;IACf,aAAa,EAAE,CAAC;IAChB,gBAAgB;IAChB,cAAc,EAAE,CAAC;IACjB,aAAa;IACb,kBAAkB,EAAE,CAAC;CACb,CAAA;AAEV,eAAe;AACF,QAAA,iBAAiB,GAAG;IAC/B,aAAa;IACb,aAAa,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACjC,cAAc;IACd,mBAAmB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACvC,cAAc;IACd,gBAAgB,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACpC,aAAa;IACb,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;CAC1B,CAAA;AAEV,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,gBAAgB;AACH,QAAA,wBAAwB,GAAG;IACtC,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,KAAK;CACJ,CAAA;AAEV,kBAAkB;AACL,QAAA,wBAAwB,GAAG;IACtC,cAAc,EAAE,GAAG;IACnB,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,WAAW,EAAE,GAAG;IAChB,qBAAqB,EAAE,GAAG;IAC1B,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;IACtC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;CAC7B,CAAA;AAEV,iBAAiB;AACJ,QAAA,sBAAsB,GAAG;IACpC,iBAAiB,EAAE,QAAQ;IAC3B,cAAc,EAAE,CAAC;IACjB,YAAY,EAAE,CAAC,GAAG,CAAC;IACnB,gBAAgB,EAAE,GAAG;CACb,CAAA;AAEV,iBAAiB;AACJ,QAAA,uBAAuB,GAAG;IACrC,UAAU,EAAE,GAAG;IACf,YAAY,EAAE,GAAG;IACjB,YAAY,EAAE,GAAG;IACjB,gBAAgB,EAAE,GAAG;IACrB,kBAAkB,EAAE,GAAG;IACvB,UAAU,EAAE,GAAG;IACf,YAAY,EAAE,GAAG;IACjB,iBAAiB,EAAE,GAAG;CACd,CAAA;AAEV,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,qBAAqB;AACR,QAAA,gBAAgB,GAAG,UAAU,CAAA;AAE1C,mBAAmB;AACN,QAAA,oBAAoB,GAAG,MAAM,CAAC,MAAM,CAAC,+BAAuB,CAAC;KACvE,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,wBAAgB,CAAC,kBAAkB,CAAA;AAE/E,eAAe;AACF,QAAA,cAAc,GAAG;IAC5B,cAAc;IACd,eAAe,EAAE,CAAC;IAClB,aAAa;IACb,iBAAiB,EAAE,CAAC;CACZ,CAAA;AAEV,+EAA+E;AAC/E,QAAQ;AACR,+EAA+E;AAE/E,YAAY;AACC,QAAA,oBAAoB,GAAG;IAClC,aAAa;IACb,SAAS,EAAE,CAAC;IACZ,aAAa;IACb,SAAS,EAAE,CAAC;IACZ,YAAY;IACZ,kBAAkB,EAAE,CAAC;IACrB,aAAa;IACb,iBAAiB,EAAE,CAAC;CACZ,CAAA;AAEV,aAAa;AACA,QAAA,aAAa,GAAG;IAC3B,oBAAoB;IACpB,OAAO,EAAE,IAAI;IACb,sBAAsB;IACtB,YAAY,EAAE,KAAK;IACnB,oBAAoB;IACpB,UAAU,EAAE,KAAK;CACT,CAAA",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/config/test-constants.ts"],
      sourcesContent: ["/**\n * \u6D4B\u8BD5\u76F8\u5173\u5E38\u91CF\u914D\u7F6E\n * \n * \u5B9A\u4E49\u6D4B\u8BD5\u4E2D\u4F7F\u7528\u7684\u5E38\u91CF\u503C\uFF0C\u4FBF\u4E8E\u7EF4\u62A4\u548C\u4FEE\u6539\n * \n * @fileoverview \u6D4B\u8BD5\u5E38\u91CF\u914D\u7F6E\n * @version 1.0.0\n * @since 2025-06-24\n * <AUTHOR> team\n */\n\n// ============================================================================\n// \u6D4B\u8BD5\u6570\u636E\u5E38\u91CF\n// ============================================================================\n\n/** \u6D4B\u8BD5\u7528\u7684\u8D28\u91CF\u8BC4\u5206\u9608\u503C */\nexport const TEST_QUALITY_THRESHOLDS = {\n  /** \u6700\u5C0F\u53EF\u63A5\u53D7\u8D28\u91CF\u8BC4\u5206 */\n  MIN_ACCEPTABLE: 0.6,\n  /** \u9AD8\u8D28\u91CF\u8BC4\u5206\u9608\u503C */\n  HIGH_QUALITY: 0.7,\n  /** \u4F18\u79C0\u8D28\u91CF\u8BC4\u5206\u9608\u503C */\n  EXCELLENT: 0.8,\n  /** \u5B8C\u7F8E\u8D28\u91CF\u8BC4\u5206\u9608\u503C */\n  PERFECT: 0.9\n} as const\n\n/** \u6D4B\u8BD5\u7528\u7684\u5411\u91CF\u7EF4\u5EA6\u9A8C\u8BC1 */\nexport const TEST_VECTOR_DIMENSIONS = {\n  /** \u4F20\u7EDF\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 */\n  LEGACY_EXPECTED: 20,\n  /** \u591A\u8BED\u8A00\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 */\n  MULTILINGUAL_EXPECTED: 512,\n  /** \u6D4B\u8BD5\u5411\u91CF\u586B\u5145\u503C */\n  TEST_FILL_VALUE: 0.1\n} as const\n\n/** \u6D4B\u8BD5\u7528\u7684\u679A\u4E3E\u6570\u91CF\u9A8C\u8BC1 */\nexport const TEST_ENUM_COUNTS = {\n  /** \u6587\u5316\u8BED\u5883\u679A\u4E3E\u6570\u91CF */\n  CULTURAL_CONTEXT: 3,\n  /** \u8BED\u8A00\u4EE3\u7801\u679A\u4E3E\u6570\u91CF */\n  LANGUAGE_CODE: 8,\n  /** \u5BC4\u5B58\u5668\u7EA7\u522B\u679A\u4E3E\u6570\u91CF */\n  REGISTER_LEVEL: 4,\n  /** \u8D28\u91CF\u7EF4\u5EA6\u6570\u91CF */\n  QUALITY_DIMENSIONS: 8\n} as const\n\n/** \u6D4B\u8BD5\u7528\u7684\u6570\u503C\u8303\u56F4 */\nexport const TEST_VALUE_RANGES = {\n  /** \u8D28\u91CF\u8BC4\u5206\u8303\u56F4 */\n  QUALITY_SCORE: { min: 0, max: 1 },\n  /** \u6587\u5316\u9002\u5E94\u6027\u8303\u56F4 */\n  CULTURAL_ADAPTATION: { min: 0, max: 1 },\n  /** \u8BED\u97F3\u548C\u8C10\u6027\u8303\u56F4 */\n  PHONETIC_HARMONY: { min: 0, max: 1 },\n  /** \u97F3\u8282\u6570\u91CF\u8303\u56F4 */\n  SYLLABLE_COUNT: { min: 1, max: 5 }\n} as const\n\n// ============================================================================\n// \u6D4B\u8BD5\u7528\u4F8B\u6570\u636E\n// ============================================================================\n\n/** \u6D4B\u8BD5\u7528\u7684\u591A\u8BED\u8A00\u6807\u7B7E */\nexport const TEST_MULTILINGUAL_LABELS = {\n  ZH_CN: '\u6D4B\u8BD5',\n  EN_US: 'test',\n  JA_JP: '\u30C6\u30B9\u30C8'\n} as const\n\n/** \u6D4B\u8BD5\u7528\u7684\u6587\u5316\u9002\u5E94\u6027\u914D\u7F6E */\nexport const TEST_CULTURAL_ADAPTATION = {\n  traditionality: 0.6,\n  modernity: 0.8,\n  formality: 0.5,\n  regionality: 0.7,\n  religious_sensitivity: 0.3,\n  age_appropriateness: ['adult', 'teen'],\n  cultural_tags: ['positive', 'modern']\n} as const\n\n/** \u6D4B\u8BD5\u7528\u7684\u8BED\u97F3\u7279\u5F81\u914D\u7F6E */\nexport const TEST_PHONETIC_FEATURES = {\n  ipa_transcription: '/test/',\n  syllable_count: 1,\n  tone_pattern: ['1'],\n  phonetic_harmony: 0.8\n} as const\n\n/** \u6D4B\u8BD5\u7528\u7684\u8D28\u91CF\u7EF4\u5EA6\u8BC4\u5206 */\nexport const TEST_QUALITY_DIMENSIONS = {\n  creativity: 0.9,\n  memorability: 0.8,\n  cultural_fit: 0.9,\n  aesthetic_appeal: 0.8,\n  pronunciation_ease: 0.8,\n  uniqueness: 0.7,\n  practicality: 0.8,\n  semantic_richness: 0.8\n} as const\n\n// ============================================================================\n// \u6D4B\u8BD5\u9A8C\u8BC1\u89C4\u5219\n// ============================================================================\n\n/** IPA\u97F3\u6807\u683C\u5F0F\u9A8C\u8BC1\u6B63\u5219\u8868\u8FBE\u5F0F */\nexport const TEST_IPA_PATTERN = /^\\/.*\\/$/\n\n/** \u6D4B\u8BD5\u7528\u7684\u5E73\u5747\u8D28\u91CF\u8BC4\u5206\u8BA1\u7B97 */\nexport const TEST_AVERAGE_QUALITY = Object.values(TEST_QUALITY_DIMENSIONS)\n  .reduce((sum, score) => sum + score, 0) / TEST_ENUM_COUNTS.QUALITY_DIMENSIONS\n\n/** \u6D4B\u8BD5\u7528\u7684\u7CBE\u5EA6\u8981\u6C42 */\nexport const TEST_PRECISION = {\n  /** \u6D6E\u70B9\u6570\u6BD4\u8F83\u7CBE\u5EA6 */\n  FLOAT_PRECISION: 4,\n  /** \u8D28\u91CF\u8BC4\u5206\u7CBE\u5EA6 */\n  QUALITY_PRECISION: 3\n} as const\n\n// ============================================================================\n// \u6D4B\u8BD5\u8FB9\u754C\u503C\n// ============================================================================\n\n/** \u6D4B\u8BD5\u8FB9\u754C\u503C */\nexport const TEST_BOUNDARY_VALUES = {\n  /** \u6700\u5C0F\u8D28\u91CF\u8BC4\u5206 */\n  MIN_SCORE: 0,\n  /** \u6700\u5927\u8D28\u91CF\u8BC4\u5206 */\n  MAX_SCORE: 1,\n  /** \u7A7A\u6570\u7EC4\u957F\u5EA6 */\n  EMPTY_ARRAY_LENGTH: 0,\n  /** \u7A7A\u5BF9\u8C61\u952E\u6570\u91CF */\n  EMPTY_OBJECT_KEYS: 0\n} as const\n\n/** \u6D4B\u8BD5\u8D85\u65F6\u914D\u7F6E */\nexport const TEST_TIMEOUTS = {\n  /** \u9ED8\u8BA4\u6D4B\u8BD5\u8D85\u65F6\u65F6\u95F4 (\u6BEB\u79D2) */\n  DEFAULT: 5000,\n  /** \u6570\u636E\u52A0\u8F7D\u6D4B\u8BD5\u8D85\u65F6\u65F6\u95F4 (\u6BEB\u79D2) */\n  DATA_LOADING: 10000,\n  /** \u751F\u6210\u6D4B\u8BD5\u8D85\u65F6\u65F6\u95F4 (\u6BEB\u79D2) */\n  GENERATION: 15000\n} as const\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d99b3e487bbaa2820d5dfb1db5bff651d76e4b75"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1y7000xrj6 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1y7000xrj6();
cov_1y7000xrj6().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1y7000xrj6().s[1]++;
exports.TEST_TIMEOUTS = exports.TEST_BOUNDARY_VALUES = exports.TEST_PRECISION = exports.TEST_AVERAGE_QUALITY = exports.TEST_IPA_PATTERN = exports.TEST_QUALITY_DIMENSIONS = exports.TEST_PHONETIC_FEATURES = exports.TEST_CULTURAL_ADAPTATION = exports.TEST_MULTILINGUAL_LABELS = exports.TEST_VALUE_RANGES = exports.TEST_ENUM_COUNTS = exports.TEST_VECTOR_DIMENSIONS = exports.TEST_QUALITY_THRESHOLDS = void 0;
// ============================================================================
// 测试数据常量
// ============================================================================
/** 测试用的质量评分阈值 */
/* istanbul ignore next */
cov_1y7000xrj6().s[2]++;
exports.TEST_QUALITY_THRESHOLDS = {
  /** 最小可接受质量评分 */
  MIN_ACCEPTABLE: 0.6,
  /** 高质量评分阈值 */
  HIGH_QUALITY: 0.7,
  /** 优秀质量评分阈值 */
  EXCELLENT: 0.8,
  /** 完美质量评分阈值 */
  PERFECT: 0.9
};
/** 测试用的向量维度验证 */
/* istanbul ignore next */
cov_1y7000xrj6().s[3]++;
exports.TEST_VECTOR_DIMENSIONS = {
  /** 传统语义向量维度 */
  LEGACY_EXPECTED: 20,
  /** 多语言语义向量维度 */
  MULTILINGUAL_EXPECTED: 512,
  /** 测试向量填充值 */
  TEST_FILL_VALUE: 0.1
};
/** 测试用的枚举数量验证 */
/* istanbul ignore next */
cov_1y7000xrj6().s[4]++;
exports.TEST_ENUM_COUNTS = {
  /** 文化语境枚举数量 */
  CULTURAL_CONTEXT: 3,
  /** 语言代码枚举数量 */
  LANGUAGE_CODE: 8,
  /** 寄存器级别枚举数量 */
  REGISTER_LEVEL: 4,
  /** 质量维度数量 */
  QUALITY_DIMENSIONS: 8
};
/** 测试用的数值范围 */
/* istanbul ignore next */
cov_1y7000xrj6().s[5]++;
exports.TEST_VALUE_RANGES = {
  /** 质量评分范围 */
  QUALITY_SCORE: {
    min: 0,
    max: 1
  },
  /** 文化适应性范围 */
  CULTURAL_ADAPTATION: {
    min: 0,
    max: 1
  },
  /** 语音和谐性范围 */
  PHONETIC_HARMONY: {
    min: 0,
    max: 1
  },
  /** 音节数量范围 */
  SYLLABLE_COUNT: {
    min: 1,
    max: 5
  }
};
// ============================================================================
// 测试用例数据
// ============================================================================
/** 测试用的多语言标签 */
/* istanbul ignore next */
cov_1y7000xrj6().s[6]++;
exports.TEST_MULTILINGUAL_LABELS = {
  ZH_CN: '测试',
  EN_US: 'test',
  JA_JP: 'テスト'
};
/** 测试用的文化适应性配置 */
/* istanbul ignore next */
cov_1y7000xrj6().s[7]++;
exports.TEST_CULTURAL_ADAPTATION = {
  traditionality: 0.6,
  modernity: 0.8,
  formality: 0.5,
  regionality: 0.7,
  religious_sensitivity: 0.3,
  age_appropriateness: ['adult', 'teen'],
  cultural_tags: ['positive', 'modern']
};
/** 测试用的语音特征配置 */
/* istanbul ignore next */
cov_1y7000xrj6().s[8]++;
exports.TEST_PHONETIC_FEATURES = {
  ipa_transcription: '/test/',
  syllable_count: 1,
  tone_pattern: ['1'],
  phonetic_harmony: 0.8
};
/** 测试用的质量维度评分 */
/* istanbul ignore next */
cov_1y7000xrj6().s[9]++;
exports.TEST_QUALITY_DIMENSIONS = {
  creativity: 0.9,
  memorability: 0.8,
  cultural_fit: 0.9,
  aesthetic_appeal: 0.8,
  pronunciation_ease: 0.8,
  uniqueness: 0.7,
  practicality: 0.8,
  semantic_richness: 0.8
};
// ============================================================================
// 测试验证规则
// ============================================================================
/** IPA音标格式验证正则表达式 */
/* istanbul ignore next */
cov_1y7000xrj6().s[10]++;
exports.TEST_IPA_PATTERN = /^\/.*\/$/;
/** 测试用的平均质量评分计算 */
/* istanbul ignore next */
cov_1y7000xrj6().s[11]++;
exports.TEST_AVERAGE_QUALITY = Object.values(exports.TEST_QUALITY_DIMENSIONS).reduce((sum, score) => {
  /* istanbul ignore next */
  cov_1y7000xrj6().f[0]++;
  cov_1y7000xrj6().s[12]++;
  return sum + score;
}, 0) / exports.TEST_ENUM_COUNTS.QUALITY_DIMENSIONS;
/** 测试用的精度要求 */
/* istanbul ignore next */
cov_1y7000xrj6().s[13]++;
exports.TEST_PRECISION = {
  /** 浮点数比较精度 */
  FLOAT_PRECISION: 4,
  /** 质量评分精度 */
  QUALITY_PRECISION: 3
};
// ============================================================================
// 测试边界值
// ============================================================================
/** 测试边界值 */
/* istanbul ignore next */
cov_1y7000xrj6().s[14]++;
exports.TEST_BOUNDARY_VALUES = {
  /** 最小质量评分 */
  MIN_SCORE: 0,
  /** 最大质量评分 */
  MAX_SCORE: 1,
  /** 空数组长度 */
  EMPTY_ARRAY_LENGTH: 0,
  /** 空对象键数量 */
  EMPTY_OBJECT_KEYS: 0
};
/** 测试超时配置 */
/* istanbul ignore next */
cov_1y7000xrj6().s[15]++;
exports.TEST_TIMEOUTS = {
  /** 默认测试超时时间 (毫秒) */
  DEFAULT: 5000,
  /** 数据加载测试超时时间 (毫秒) */
  DATA_LOADING: 10000,
  /** 生成测试超时时间 (毫秒) */
  GENERATION: 15000
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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