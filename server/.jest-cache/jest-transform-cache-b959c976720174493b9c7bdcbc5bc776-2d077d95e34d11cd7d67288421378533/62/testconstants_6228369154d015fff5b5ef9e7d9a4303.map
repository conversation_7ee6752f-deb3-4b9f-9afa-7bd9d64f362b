{"version": 3, "names": ["cov_1y7000xrj6", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "TEST_QUALITY_THRESHOLDS", "MIN_ACCEPTABLE", "HIGH_QUALITY", "EXCELLENT", "PERFECT", "TEST_VECTOR_DIMENSIONS", "LEGACY_EXPECTED", "MULTILINGUAL_EXPECTED", "TEST_FILL_VALUE", "TEST_ENUM_COUNTS", "CULTURAL_CONTEXT", "LANGUAGE_CODE", "REGISTER_LEVEL", "QUALITY_DIMENSIONS", "TEST_VALUE_RANGES", "QUALITY_SCORE", "min", "max", "CULTURAL_ADAPTATION", "PHONETIC_HARMONY", "SYLLABLE_COUNT", "TEST_MULTILINGUAL_LABELS", "ZH_CN", "EN_US", "JA_JP", "TEST_CULTURAL_ADAPTATION", "traditionality", "modernity", "formality", "regionality", "religious_sensitivity", "age_appropriateness", "cultural_tags", "TEST_PHONETIC_FEATURES", "ipa_transcription", "syllable_count", "tone_pattern", "phonetic_harmony", "TEST_QUALITY_DIMENSIONS", "creativity", "memorability", "cultural_fit", "aesthetic_appeal", "pronunciation_ease", "uniqueness", "practicality", "semantic_richness", "TEST_IPA_PATTERN", "TEST_AVERAGE_QUALITY", "Object", "values", "reduce", "sum", "score", "TEST_PRECISION", "FLOAT_PRECISION", "QUALITY_PRECISION", "TEST_BOUNDARY_VALUES", "MIN_SCORE", "MAX_SCORE", "EMPTY_ARRAY_LENGTH", "EMPTY_OBJECT_KEYS", "TEST_TIMEOUTS", "DEFAULT", "DATA_LOADING", "GENERATION"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/config/test-constants.ts"], "sourcesContent": ["/**\n * 测试相关常量配置\n * \n * 定义测试中使用的常量值，便于维护和修改\n * \n * @fileoverview 测试常量配置\n * @version 1.0.0\n * @since 2025-06-24\n * <AUTHOR> team\n */\n\n// ============================================================================\n// 测试数据常量\n// ============================================================================\n\n/** 测试用的质量评分阈值 */\nexport const TEST_QUALITY_THRESHOLDS = {\n  /** 最小可接受质量评分 */\n  MIN_ACCEPTABLE: 0.6,\n  /** 高质量评分阈值 */\n  HIGH_QUALITY: 0.7,\n  /** 优秀质量评分阈值 */\n  EXCELLENT: 0.8,\n  /** 完美质量评分阈值 */\n  PERFECT: 0.9\n} as const\n\n/** 测试用的向量维度验证 */\nexport const TEST_VECTOR_DIMENSIONS = {\n  /** 传统语义向量维度 */\n  LEGACY_EXPECTED: 20,\n  /** 多语言语义向量维度 */\n  MULTILINGUAL_EXPECTED: 512,\n  /** 测试向量填充值 */\n  TEST_FILL_VALUE: 0.1\n} as const\n\n/** 测试用的枚举数量验证 */\nexport const TEST_ENUM_COUNTS = {\n  /** 文化语境枚举数量 */\n  CULTURAL_CONTEXT: 3,\n  /** 语言代码枚举数量 */\n  LANGUAGE_CODE: 8,\n  /** 寄存器级别枚举数量 */\n  REGISTER_LEVEL: 4,\n  /** 质量维度数量 */\n  QUALITY_DIMENSIONS: 8\n} as const\n\n/** 测试用的数值范围 */\nexport const TEST_VALUE_RANGES = {\n  /** 质量评分范围 */\n  QUALITY_SCORE: { min: 0, max: 1 },\n  /** 文化适应性范围 */\n  CULTURAL_ADAPTATION: { min: 0, max: 1 },\n  /** 语音和谐性范围 */\n  PHONETIC_HARMONY: { min: 0, max: 1 },\n  /** 音节数量范围 */\n  SYLLABLE_COUNT: { min: 1, max: 5 }\n} as const\n\n// ============================================================================\n// 测试用例数据\n// ============================================================================\n\n/** 测试用的多语言标签 */\nexport const TEST_MULTILINGUAL_LABELS = {\n  ZH_CN: '测试',\n  EN_US: 'test',\n  JA_JP: 'テスト'\n} as const\n\n/** 测试用的文化适应性配置 */\nexport const TEST_CULTURAL_ADAPTATION = {\n  traditionality: 0.6,\n  modernity: 0.8,\n  formality: 0.5,\n  regionality: 0.7,\n  religious_sensitivity: 0.3,\n  age_appropriateness: ['adult', 'teen'],\n  cultural_tags: ['positive', 'modern']\n} as const\n\n/** 测试用的语音特征配置 */\nexport const TEST_PHONETIC_FEATURES = {\n  ipa_transcription: '/test/',\n  syllable_count: 1,\n  tone_pattern: ['1'],\n  phonetic_harmony: 0.8\n} as const\n\n/** 测试用的质量维度评分 */\nexport const TEST_QUALITY_DIMENSIONS = {\n  creativity: 0.9,\n  memorability: 0.8,\n  cultural_fit: 0.9,\n  aesthetic_appeal: 0.8,\n  pronunciation_ease: 0.8,\n  uniqueness: 0.7,\n  practicality: 0.8,\n  semantic_richness: 0.8\n} as const\n\n// ============================================================================\n// 测试验证规则\n// ============================================================================\n\n/** IPA音标格式验证正则表达式 */\nexport const TEST_IPA_PATTERN = /^\\/.*\\/$/\n\n/** 测试用的平均质量评分计算 */\nexport const TEST_AVERAGE_QUALITY = Object.values(TEST_QUALITY_DIMENSIONS)\n  .reduce((sum, score) => sum + score, 0) / TEST_ENUM_COUNTS.QUALITY_DIMENSIONS\n\n/** 测试用的精度要求 */\nexport const TEST_PRECISION = {\n  /** 浮点数比较精度 */\n  FLOAT_PRECISION: 4,\n  /** 质量评分精度 */\n  QUALITY_PRECISION: 3\n} as const\n\n// ============================================================================\n// 测试边界值\n// ============================================================================\n\n/** 测试边界值 */\nexport const TEST_BOUNDARY_VALUES = {\n  /** 最小质量评分 */\n  MIN_SCORE: 0,\n  /** 最大质量评分 */\n  MAX_SCORE: 1,\n  /** 空数组长度 */\n  EMPTY_ARRAY_LENGTH: 0,\n  /** 空对象键数量 */\n  EMPTY_OBJECT_KEYS: 0\n} as const\n\n/** 测试超时配置 */\nexport const TEST_TIMEOUTS = {\n  /** 默认测试超时时间 (毫秒) */\n  DEFAULT: 5000,\n  /** 数据加载测试超时时间 (毫秒) */\n  DATA_LOADING: 10000,\n  /** 生成测试超时时间 (毫秒) */\n  GENERATION: 15000\n} as const\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;IAAA;IAAAC,CAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAAzB,IAAA;EAAA;EAAA,IAAA0B,QAAA,GAAAzB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAuB,QAAA,CAAA3B,IAAA,KAAA2B,QAAA,CAAA3B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA0B,QAAA,CAAA3B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAuB,cAAA,GAAAD,QAAA,CAAA3B,IAAA;EAAA;IAaA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAA6B,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAA7B,cAAA;AAAAA,cAAA,GAAAiB,CAAA;;;;;;;AAFA;AACA;AACA;AAEA;AAAA;AAAAjB,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAC,uBAAuB,GAAG;EACrC;EACAC,cAAc,EAAE,GAAG;EACnB;EACAC,YAAY,EAAE,GAAG;EACjB;EACAC,SAAS,EAAE,GAAG;EACd;EACAC,OAAO,EAAE;CACD;AAEV;AAAA;AAAAnC,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAM,sBAAsB,GAAG;EACpC;EACAC,eAAe,EAAE,EAAE;EACnB;EACAC,qBAAqB,EAAE,GAAG;EAC1B;EACAC,eAAe,EAAE;CACT;AAEV;AAAA;AAAAvC,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAU,gBAAgB,GAAG;EAC9B;EACAC,gBAAgB,EAAE,CAAC;EACnB;EACAC,aAAa,EAAE,CAAC;EAChB;EACAC,cAAc,EAAE,CAAC;EACjB;EACAC,kBAAkB,EAAE;CACZ;AAEV;AAAA;AAAA5C,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAe,iBAAiB,GAAG;EAC/B;EACAC,aAAa,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAC,CAAE;EACjC;EACAC,mBAAmB,EAAE;IAAEF,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAC,CAAE;EACvC;EACAE,gBAAgB,EAAE;IAAEH,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAC,CAAE;EACpC;EACAG,cAAc,EAAE;IAAEJ,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAC;CACxB;AAEV;AACA;AACA;AAEA;AAAA;AAAAhD,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAsB,wBAAwB,GAAG;EACtCC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;CACC;AAEV;AAAA;AAAAvD,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAA0B,wBAAwB,GAAG;EACtCC,cAAc,EAAE,GAAG;EACnBC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,GAAG;EACdC,WAAW,EAAE,GAAG;EAChBC,qBAAqB,EAAE,GAAG;EAC1BC,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EACtCC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ;CAC5B;AAEV;AAAA;AAAA/D,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAkC,sBAAsB,GAAG;EACpCC,iBAAiB,EAAE,QAAQ;EAC3BC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC,GAAG,CAAC;EACnBC,gBAAgB,EAAE;CACV;AAEV;AAAA;AAAApE,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAuC,uBAAuB,GAAG;EACrCC,UAAU,EAAE,GAAG;EACfC,YAAY,EAAE,GAAG;EACjBC,YAAY,EAAE,GAAG;EACjBC,gBAAgB,EAAE,GAAG;EACrBC,kBAAkB,EAAE,GAAG;EACvBC,UAAU,EAAE,GAAG;EACfC,YAAY,EAAE,GAAG;EACjBC,iBAAiB,EAAE;CACX;AAEV;AACA;AACA;AAEA;AAAA;AAAA7E,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAgD,gBAAgB,GAAG,UAAU;AAE1C;AAAA;AAAA9E,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAiD,oBAAoB,GAAGC,MAAM,CAACC,MAAM,CAACnD,OAAA,CAAAuC,uBAAuB,CAAC,CACvEa,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;EAAA;EAAApF,cAAA,GAAAkB,CAAA;EAAAlB,cAAA,GAAAiB,CAAA;EAAA,OAAAkE,GAAG,GAAGC,KAAK;AAAL,CAAK,EAAE,CAAC,CAAC,GAAGtD,OAAA,CAAAU,gBAAgB,CAACI,kBAAkB;AAE/E;AAAA;AAAA5C,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAAuD,cAAc,GAAG;EAC5B;EACAC,eAAe,EAAE,CAAC;EAClB;EACAC,iBAAiB,EAAE;CACX;AAEV;AACA;AACA;AAEA;AAAA;AAAAvF,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAA0D,oBAAoB,GAAG;EAClC;EACAC,SAAS,EAAE,CAAC;EACZ;EACAC,SAAS,EAAE,CAAC;EACZ;EACAC,kBAAkB,EAAE,CAAC;EACrB;EACAC,iBAAiB,EAAE;CACX;AAEV;AAAA;AAAA5F,cAAA,GAAAiB,CAAA;AACaa,OAAA,CAAA+D,aAAa,GAAG;EAC3B;EACAC,OAAO,EAAE,IAAI;EACb;EACAC,YAAY,EAAE,KAAK;EACnB;EACAC,UAAU,EAAE;CACJ", "ignoreList": []}