8b61b99b66b7061ee2531c57e349fcaa
"use strict";

/**
 * namer-v6 核心数据类型定义
 *
 * 本文件定义了namer-v6项目的核心数据结构和接口，
 * 支持多语言扩展、智能语义分析和基于第一性原理的创意模式。
 *
 * @fileoverview 核心数据类型定义
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */
/* istanbul ignore next */
function cov_2lhtm2izxv() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/core.ts";
  var hash = "bcae2c0108963fcbef00d80f2b1bde51e8502d8c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/core.ts",
    statementMap: {
      "0": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 62
        }
      },
      "1": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 87
        }
      },
      "2": {
        start: {
          line: 25,
          column: 0
        },
        end: {
          line: 38,
          column: 75
        }
      },
      "3": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 46
        }
      },
      "4": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 52
        }
      },
      "5": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 60
        }
      },
      "6": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 33,
          column: 44
        }
      },
      "7": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 44
        }
      },
      "8": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 46
        }
      },
      "9": {
        start: {
          line: 46,
          column: 0
        },
        end: {
          line: 53,
          column: 72
        }
      },
      "10": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 43
        }
      },
      "11": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 41
        }
      },
      "12": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 43
        }
      },
      "13": {
        start: {
          line: 63,
          column: 0
        },
        end: {
          line: 80,
          column: 75
        }
      },
      "14": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 54
        }
      },
      "15": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 67,
          column: 46
        }
      },
      "16": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 46
        }
      },
      "17": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 71,
          column: 44
        }
      },
      "18": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 50
        }
      },
      "19": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 75,
          column: 52
        }
      },
      "20": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 77,
          column: 50
        }
      },
      "21": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 79,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 25,
            column: 1
          },
          end: {
            line: 25,
            column: 2
          }
        },
        loc: {
          start: {
            line: 25,
            column: 29
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 25
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 46,
            column: 1
          },
          end: {
            line: 46,
            column: 2
          }
        },
        loc: {
          start: {
            line: 46,
            column: 28
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 46
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 63,
            column: 1
          },
          end: {
            line: 63,
            column: 2
          }
        },
        loc: {
          start: {
            line: 63,
            column: 29
          },
          end: {
            line: 80,
            column: 1
          }
        },
        line: 63
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 38,
            column: 3
          },
          end: {
            line: 38,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 3
          },
          end: {
            line: 38,
            column: 19
          }
        }, {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 72
          }
        }],
        line: 38
      },
      "1": {
        loc: {
          start: {
            line: 53,
            column: 3
          },
          end: {
            line: 53,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 3
          },
          end: {
            line: 53,
            column: 18
          }
        }, {
          start: {
            line: 53,
            column: 23
          },
          end: {
            line: 53,
            column: 69
          }
        }],
        line: 53
      },
      "2": {
        loc: {
          start: {
            line: 80,
            column: 3
          },
          end: {
            line: 80,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 3
          },
          end: {
            line: 80,
            column: 19
          }
        }, {
          start: {
            line: 80,
            column: 24
          },
          end: {
            line: 80,
            column: 72
          }
        }],
        line: 80
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/core.ts",
      mappings: ";AAAA;;;;;;;;;;GAUG;;;AAIH,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;;GAKG;AACH,IAAY,gBAaX;AAbD,WAAY,gBAAgB;IAC1B,2BAA2B;IAC3B,yCAAqB,CAAA;IACrB,+BAA+B;IAC/B,+CAA2B,CAAA;IAC3B,+BAA+B;IAC/B,uDAAmC,CAAA;IACnC,4BAA4B;IAC5B,uCAAmB,CAAA;IACnB,2BAA2B;IAC3B,uCAAmB,CAAA;IACnB,2BAA2B;IAC3B,yCAAqB,CAAA;AACvB,CAAC,EAbW,gBAAgB,gCAAhB,gBAAgB,QAa3B;AAED;;;;;GAKG;AACH,IAAY,eAOX;AAPD,WAAY,eAAe;IACzB,6BAA6B;IAC7B,sCAAmB,CAAA;IACnB,8BAA8B;IAC9B,oCAAiB,CAAA;IACjB,6BAA6B;IAC7B,sCAAmB,CAAA;AACrB,CAAC,EAPW,eAAe,+BAAf,eAAe,QAO1B;AA+ED,+EAA+E;AAC/E,yBAAyB;AACzB,+EAA+E;AAE/E;;;;GAIG;AACH,IAAY,gBAiBX;AAjBD,WAAY,gBAAgB;IAC1B,8BAA8B;IAC9B,iDAA6B,CAAA;IAC7B,6BAA6B;IAC7B,yCAAqB,CAAA;IACrB,8BAA8B;IAC9B,yCAAqB,CAAA;IACrB,6BAA6B;IAC7B,uCAAmB,CAAA;IACnB,6BAA6B;IAC7B,6CAAyB,CAAA;IACzB,6BAA6B;IAC7B,+CAA2B,CAAA;IAC3B,6BAA6B;IAC7B,6CAAyB,CAAA;IACzB,6BAA6B;IAC7B,6CAAyB,CAAA;AAC3B,CAAC,EAjBW,gBAAgB,gCAAhB,gBAAgB,QAiB3B",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/core.ts"],
      sourcesContent: ["/**\n * namer-v6 \u6838\u5FC3\u6570\u636E\u7C7B\u578B\u5B9A\u4E49\n *\n * \u672C\u6587\u4EF6\u5B9A\u4E49\u4E86namer-v6\u9879\u76EE\u7684\u6838\u5FC3\u6570\u636E\u7ED3\u6784\u548C\u63A5\u53E3\uFF0C\n * \u652F\u6301\u591A\u8BED\u8A00\u6269\u5C55\u3001\u667A\u80FD\u8BED\u4E49\u5206\u6790\u548C\u57FA\u4E8E\u7B2C\u4E00\u6027\u539F\u7406\u7684\u521B\u610F\u6A21\u5F0F\u3002\n *\n * @fileoverview \u6838\u5FC3\u6570\u636E\u7C7B\u578B\u5B9A\u4E49\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type { MorphemeVersion } from \"./common\"\n\n// ============================================================================\n// \u57FA\u7840\u679A\u4E3E\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u8BED\u7D20\u7C7B\u522B\u679A\u4E3E\n *\n * \u5B9A\u4E49\u4E86\u8BED\u7D20\u7684\u4E3B\u8981\u5206\u7C7B\uFF0C\u7528\u4E8E\u8BED\u4E49\u7EC4\u7EC7\u548C\u68C0\u7D22\u4F18\u5316\u3002\n * \u57FA\u4E8E\u8BED\u8A00\u5B66\u548C\u8BA4\u77E5\u5FC3\u7406\u5B66\u539F\u7406\u8FDB\u884C\u5206\u7C7B\u3002\n */\nexport enum MorphemeCategory {\n  /** \u60C5\u611F\u7C7B\u8BED\u7D20 - \u8868\u8FBE\u60C5\u611F\u72B6\u6001\u3001\u611F\u53D7\u548C\u60C5\u7EEA */\n  EMOTIONS = 'emotions',\n  /** \u804C\u4E1A\u7C7B\u8BED\u7D20 - \u8868\u8FBE\u804C\u4E1A\u8EAB\u4EFD\u3001\u4E13\u4E1A\u9886\u57DF\u548C\u5DE5\u4F5C\u89D2\u8272 */\n  PROFESSIONS = 'professions',\n  /** \u7279\u5F81\u7C7B\u8BED\u7D20 - \u8868\u8FBE\u4E2A\u6027\u7279\u5F81\u3001\u80FD\u529B\u5C5E\u6027\u548C\u54C1\u8D28\u7279\u70B9 */\n  CHARACTERISTICS = 'characteristics',\n  /** \u7269\u4F53\u7C7B\u8BED\u7D20 - \u8868\u8FBE\u5177\u4F53\u6216\u62BD\u8C61\u7684\u4E8B\u7269\u3001\u5BF9\u8C61 */\n  OBJECTS = 'objects',\n  /** \u52A8\u4F5C\u7C7B\u8BED\u7D20 - \u8868\u8FBE\u884C\u4E3A\u3001\u52A8\u4F5C\u72B6\u6001\u548C\u6D3B\u52A8 */\n  ACTIONS = 'actions',\n  /** \u6982\u5FF5\u7C7B\u8BED\u7D20 - \u8868\u8FBE\u62BD\u8C61\u6982\u5FF5\u3001\u7406\u5FF5\u548C\u601D\u60F3 */\n  CONCEPTS = 'concepts'\n}\n\n/**\n * \u6587\u5316\u8BED\u5883\u679A\u4E3E\n *\n * \u5B9A\u4E49\u4E86\u8BED\u7D20\u7684\u6587\u5316\u80CC\u666F\u5C5E\u6027\uFF0C\u7528\u4E8E\u6587\u5316\u9002\u914D\u548C\u98CE\u683C\u5339\u914D\u3002\n * \u57FA\u4E8E\u6587\u5316\u4EBA\u7C7B\u5B66\u548C\u793E\u4F1A\u5FC3\u7406\u5B66\u7406\u8BBA\u8FDB\u884C\u5206\u7C7B\u3002\n */\nexport enum CulturalContext {\n  /** \u53E4\u5178\u6587\u5316\u8BED\u5883 - \u4F20\u7EDF\u3001\u5178\u96C5\u3001\u6DF1\u539A\u7684\u6587\u5316\u7279\u8272 */\n  ANCIENT = 'ancient',\n  /** \u73B0\u4EE3\u6587\u5316\u8BED\u5883 - \u65F6\u5C1A\u3001\u521B\u65B0\u3001\u56FD\u9645\u5316\u7684\u6587\u5316\u7279\u8272 */\n  MODERN = 'modern',\n  /** \u4E2D\u6027\u6587\u5316\u8BED\u5883 - \u901A\u7528\u3001\u5E73\u8861\u3001\u5305\u5BB9\u7684\u6587\u5316\u7279\u8272 */\n  NEUTRAL = 'neutral'\n}\n\n// ============================================================================\n// \u8BED\u7D20\u76F8\u5173\u63A5\u53E3\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u8BED\u7D20\u8D28\u91CF\u6307\u6807\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u8BED\u7D20\u8D28\u91CF\u8BC4\u4F30\u7684\u5404\u9879\u6307\u6807\uFF0C\u7528\u4E8E\u667A\u80FD\u7B5B\u9009\u548C\u6392\u5E8F\n */\nexport interface MorphemeQualityMetrics {\n  /** \u81EA\u7136\u5EA6\u8BC4\u5206 [0-1] - \u8BED\u7D20\u5728\u8BED\u8A00\u4E2D\u7684\u81EA\u7136\u7A0B\u5EA6 */\n  naturalness: number\n  /** \u4F7F\u7528\u9891\u7387 [0-1] - \u8BED\u7D20\u5728\u5B9E\u9645\u4F7F\u7528\u4E2D\u7684\u9891\u7387 */\n  frequency: number\n  /** \u53EF\u63A5\u53D7\u5EA6 [0-1] - \u7528\u6237\u5BF9\u8BED\u7D20\u7684\u63A5\u53D7\u7A0B\u5EA6 */\n  acceptability: number\n  /** \u7F8E\u5B66\u5438\u5F15\u529B [0-1] - \u8BED\u7D20\u7684\u7F8E\u5B66\u4EF7\u503C\u548C\u5438\u5F15\u529B */\n  aesthetic_appeal: number\n}\n\n/**\n * \u8BED\u7D20\u8BED\u8A00\u5C5E\u6027\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u8BED\u7D20\u7684\u8BED\u8A00\u5B66\u7279\u5F81\uFF0C\u7528\u4E8E\u8BED\u97F3\u5206\u6790\u548C\u7EC4\u5408\u4F18\u5316\n */\nexport interface MorphemeLanguageProperties {\n  /** \u97F3\u8282\u6570\u91CF - \u7528\u4E8E\u97F3\u97F5\u5206\u6790 */\n  syllable_count: number\n  /** \u5B57\u7B26\u6570\u91CF - \u7528\u4E8E\u957F\u5EA6\u63A7\u5236 */\n  character_count: number\n  /** \u8BED\u97F3\u7279\u5F81\u6807\u7B7E - \u7528\u4E8E\u97F3\u97F5\u548C\u8C10\u6027\u68C0\u67E5 */\n  phonetic_features: string[]\n  /** \u8BCD\u6CD5\u7C7B\u578B - \u8BCD\u6027\u548C\u8BED\u6CD5\u529F\u80FD */\n  morphological_type: string\n  /** \u53D1\u97F3\u6807\u8BB0 - \u62FC\u97F3\u6216\u97F3\u6807 */\n  pronunciation?: string\n}\n\n/**\n * \u8BED\u7D20\u6570\u636E\u7ED3\u6784\n *\n * \u6838\u5FC3\u8BED\u7D20\u6570\u636E\u7ED3\u6784\uFF0C\u5305\u542B\u8BED\u4E49\u3001\u6587\u5316\u3001\u8D28\u91CF\u7B49\u591A\u7EF4\u5EA6\u5C5E\u6027\u3002\n * \u652F\u6301\u591A\u8BED\u8A00\u6269\u5C55\u548C\u667A\u80FD\u5206\u6790\u3002\n */\nexport interface Morpheme {\n  /** \u8BED\u7D20\u552F\u4E00\u6807\u8BC6\u7B26\uFF0C\u683C\u5F0F\uFF1A{category}_{\u5E8F\u53F7} */\n  id: string\n  /** \u8BED\u7D20\u6587\u672C\u5185\u5BB9 */\n  text: string\n  /** \u8BED\u7D20\u4E3B\u5206\u7C7B */\n  category: MorphemeCategory\n  /** \u8BED\u7D20\u5B50\u5206\u7C7B\uFF0C\u7528\u4E8E\u66F4\u7CBE\u7EC6\u7684\u5206\u7C7B\u7BA1\u7406 */\n  subcategory: string\n  /** \u6587\u5316\u8BED\u5883\u5C5E\u6027 */\n  cultural_context: CulturalContext\n  /** \u4F7F\u7528\u9891\u7387 [0-1]\uFF0C\u5F71\u54CD\u91C7\u6837\u6743\u91CD */\n  usage_frequency: number\n  /** \u7EFC\u5408\u8D28\u91CF\u8BC4\u5206 [0-1] */\n  quality_score: number\n  /** \u8BED\u4E49\u5411\u91CF (20\u7EF4)\uFF0C\u7528\u4E8E\u76F8\u4F3C\u5EA6\u8BA1\u7B97\u548C\u8BED\u4E49\u5206\u6790 */\n  semantic_vector: number[]\n  /** \u8BED\u4E49\u6807\u7B7E\uFF0C\u652F\u6301\u591A\u7EF4\u5EA6\u68C0\u7D22\u548C\u5206\u7C7B */\n  tags: string[]\n  /** \u8BED\u8A00\u5C5E\u6027 */\n  language_properties: MorphemeLanguageProperties\n  /** \u8D28\u91CF\u6307\u6807 */\n  quality_metrics: MorphemeQualityMetrics\n  /** \u521B\u5EFA\u65F6\u95F4\u6233 */\n  created_at: number\n  /** \u6570\u636E\u6765\u6E90\u6807\u8BC6 */\n  source: string\n  /** \u6570\u636E\u7248\u672C\uFF0C\u7528\u4E8E\u7248\u672C\u7BA1\u7406\u548C\u517C\u5BB9\u6027\u68C0\u67E5 */\n  version: MorphemeVersion\n  /** \u6E90\u6587\u4EF6 (\u7528\u4E8E\u6570\u636E\u91CD\u7EC4\u811A\u672C) */\n  _source_file?: string\n}\n\n// ============================================================================\n// \u521B\u610F\u6A21\u5F0F\u76F8\u5173\u63A5\u53E3\u5B9A\u4E49 (\u57FA\u4E8E\u7B2C\u4E00\u6027\u539F\u7406\u8BBE\u8BA1)\n// ============================================================================\n\n/**\n * \u6A21\u5F0F\u539F\u578B\u679A\u4E3E\n *\n * \u57FA\u4E8E\u7B2C\u4E00\u6027\u539F\u7406\u548C\u7528\u6237\u5FC3\u7406\u9700\u6C42\u5206\u6790\u5B9A\u4E49\u76848\u79CD\u521B\u610F\u6A21\u5F0F\u539F\u578B\n */\nexport enum PatternArchetype {\n  /** \u4E13\u4E1A\u578B - \u5F3A\u8C03\u80FD\u529B\u548C\u4E13\u4E1A\u6027\uFF0C\u6EE1\u8DB3\u8EAB\u4EFD\u8868\u8FBE\u9700\u6C42 */\n  PROFESSIONAL = 'professional',\n  /** \u827A\u672F\u578B - \u5F3A\u8C03\u521B\u610F\u548C\u7F8E\u611F\uFF0C\u6EE1\u8DB3\u7F8E\u5B66\u8FFD\u6C42\u9700\u6C42 */\n  ARTISTIC = 'artistic',\n  /** \u5E7D\u9ED8\u578B - \u5F3A\u8C03\u8DA3\u5473\u548C\u4EB2\u548C\u529B\uFF0C\u6EE1\u8DB3\u793E\u4EA4\u6DA6\u6ED1\u9700\u6C42 */\n  HUMOROUS = 'humorous',\n  /** \u4F18\u96C5\u578B - \u5F3A\u8C03\u54C1\u5473\u548C\u6C14\u8D28\uFF0C\u6EE1\u8DB3\u54C1\u5473\u5C55\u793A\u9700\u6C42 */\n  ELEGANT = 'elegant',\n  /** \u521B\u65B0\u578B - \u5F3A\u8C03\u524D\u77BB\u548C\u72EC\u7279\u6027\uFF0C\u6EE1\u8DB3\u72EC\u7279\u6027\u9700\u6C42 */\n  INNOVATIVE = 'innovative',\n  /** \u4F20\u7EDF\u578B - \u5F3A\u8C03\u6587\u5316\u548C\u5E95\u8574\uFF0C\u6EE1\u8DB3\u6587\u5316\u8BA4\u540C\u9700\u6C42 */\n  TRADITIONAL = 'traditional',\n  /** \u7B80\u7EA6\u578B - \u5F3A\u8C03\u7B80\u6D01\u548C\u7EAF\u7CB9\uFF0C\u6EE1\u8DB3\u8BA4\u77E5\u4FBF\u5229\u9700\u6C42 */\n  MINIMALIST = 'minimalist',\n  /** \u8868\u8FBE\u578B - \u5F3A\u8C03\u4E2A\u6027\u548C\u60C5\u611F\uFF0C\u6EE1\u8DB3\u60C5\u611F\u5171\u9E23\u9700\u6C42 */\n  EXPRESSIVE = 'expressive'\n}\n\n/**\n * \u5FC3\u7406\u9700\u6C42\u6620\u5C04\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u521B\u610F\u6A21\u5F0F\u4E0E\u7528\u6237\u5FC3\u7406\u9700\u6C42\u7684\u6620\u5C04\u5173\u7CFB\n */\nexport interface PsychologicalNeeds {\n  /** \u8EAB\u4EFD\u8868\u8FBE\u9700\u6C42 [0-1] - \u901A\u8FC7\u7528\u6237\u540D\u8868\u8FBE\u4E2A\u4EBA\u8EAB\u4EFD\u548C\u89D2\u8272 */\n  identity_expression: number\n  /** \u793E\u4EA4\u5F52\u5C5E\u9700\u6C42 [0-1] - \u901A\u8FC7\u7528\u6237\u540D\u83B7\u5F97\u7FA4\u4F53\u8BA4\u540C\u548C\u5F52\u5C5E\u611F */\n  social_belonging: number\n  /** \u7F8E\u5B66\u6109\u60A6\u9700\u6C42 [0-1] - \u901A\u8FC7\u7528\u6237\u540D\u83B7\u5F97\u7F8E\u5B66\u4EAB\u53D7\u548C\u6109\u60A6\u611F */\n  aesthetic_pleasure: number\n  /** \u8BA4\u77E5\u4FBF\u5229\u9700\u6C42 [0-1] - \u7528\u6237\u540D\u6613\u4E8E\u7406\u89E3\u3001\u8BB0\u5FC6\u548C\u4F20\u64AD */\n  cognitive_ease: number\n  /** \u60C5\u611F\u5171\u9E23\u9700\u6C42 [0-1] - \u901A\u8FC7\u7528\u6237\u540D\u4EA7\u751F\u60C5\u611F\u8FDE\u63A5\u548C\u5171\u9E23 */\n  emotional_resonance: number\n}\n\n/**\n * \u4F7F\u7528\u573A\u666F\u9002\u914D\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u521B\u610F\u6A21\u5F0F\u5728\u4E0D\u540C\u4F7F\u7528\u573A\u666F\u4E0B\u7684\u9002\u7528\u5EA6\n */\nexport interface UsageScenarios {\n  /** \u4E13\u4E1A\u573A\u666F\u9002\u7528\u5EA6 [0-1] - \u5DE5\u4F5C\u3001\u5546\u52A1\u7B49\u6B63\u5F0F\u573A\u5408 */\n  professional: number\n  /** \u793E\u4EA4\u573A\u666F\u9002\u7528\u5EA6 [0-1] - \u793E\u4EA4\u5A92\u4F53\u3001\u670B\u53CB\u5708\u7B49\u793E\u4EA4\u573A\u5408 */\n  social: number\n  /** \u521B\u610F\u573A\u666F\u9002\u7528\u5EA6 [0-1] - \u827A\u672F\u521B\u4F5C\u3001\u8BBE\u8BA1\u7B49\u521B\u610F\u573A\u5408 */\n  creative: number\n  /** \u6B63\u5F0F\u573A\u666F\u9002\u7528\u5EA6 [0-1] - \u5B98\u65B9\u3001\u5B66\u672F\u7B49\u6B63\u5F0F\u573A\u5408 */\n  formal: number\n  /** \u4F11\u95F2\u573A\u666F\u9002\u7528\u5EA6 [0-1] - \u6E38\u620F\u3001\u5A31\u4E50\u7B49\u4F11\u95F2\u573A\u5408 */\n  casual: number\n}\n\n/**\n * \u6587\u5316\u5171\u9E23\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u521B\u610F\u6A21\u5F0F\u4E0E\u4E0D\u540C\u6587\u5316\u80CC\u666F\u7684\u5171\u9E23\u7A0B\u5EA6\n */\nexport interface CulturalResonance {\n  /** \u4F20\u7EDF\u6587\u5316\u5171\u9E23 [0-1] - \u4E0E\u4F20\u7EDF\u6587\u5316\u7684\u5951\u5408\u5EA6 */\n  traditional: number\n  /** \u73B0\u4EE3\u6587\u5316\u5171\u9E23 [0-1] - \u4E0E\u73B0\u4EE3\u6587\u5316\u7684\u5951\u5408\u5EA6 */\n  modern: number\n  /** \u56FD\u9645\u5316\u9002\u5E94 [0-1] - \u8DE8\u6587\u5316\u7406\u89E3\u548C\u63A5\u53D7\u5EA6 */\n  international: number\n  /** \u672C\u571F\u5316\u7279\u8272 [0-1] - \u672C\u571F\u6587\u5316\u7279\u8272\u548C\u8BA4\u540C\u5EA6 */\n  local: number\n}\n\n/**\n * \u521B\u610F\u6A21\u5F0F\u5FC3\u7406\u5B66\u5C5E\u6027\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u521B\u610F\u6A21\u5F0F\u7684\u5FC3\u7406\u5B66\u7279\u5F81\u548C\u9002\u7528\u6027\n */\nexport interface CreativePatternPsychology {\n  /** \u5FC3\u7406\u9700\u6C42\u6620\u5C04 */\n  psychological_needs: PsychologicalNeeds\n  /** \u9002\u7528\u573A\u666F */\n  usage_scenarios: UsageScenarios\n  /** \u6587\u5316\u5171\u9E23 */\n  cultural_resonance: CulturalResonance\n}\n\n/**\n * \u6A21\u5F0F\u89C4\u5219\u63A5\u53E3\n *\n * \u5B9A\u4E49\u4E86\u521B\u610F\u6A21\u5F0F\u7684\u751F\u6210\u89C4\u5219\u548C\u7EA6\u675F\u6761\u4EF6\n */\nexport interface PatternRule {\n  /** \u6267\u884C\u6B65\u9AA4\u5E8F\u53F7 */\n  step: number\n  /** \u89C4\u5219\u52A8\u4F5C\u7C7B\u578B */\n  action: 'select' | 'combine' | 'transform' | 'validate'\n  /** \u89C4\u5219\u53C2\u6570 */\n  parameters: Record<string, any>\n  /** \u5931\u8D25\u56DE\u9000\u89C4\u5219 */\n  fallback?: PatternRule\n  /** \u89C4\u5219\u6743\u91CD */\n  weight: number\n  /** \u89C4\u5219\u63CF\u8FF0 */\n  description?: string\n}\n\n/**\n * \u521B\u610F\u6A21\u5F0F\u6570\u636E\u7ED3\u6784\n *\n * \u57FA\u4E8E\u7B2C\u4E00\u6027\u539F\u7406\u8BBE\u8BA1\u7684\u521B\u610F\u6A21\u5F0F\u5B8C\u6574\u5B9A\u4E49\uFF0C\n * \u5305\u542B\u5FC3\u7406\u5B66\u57FA\u7840\u3001\u751F\u6210\u89C4\u5219\u548C\u6548\u679C\u8BC4\u4F30\n */\nexport interface CreativePattern {\n  /** \u6A21\u5F0F\u552F\u4E00\u6807\u8BC6\u7B26 */\n  id: string\n  /** \u6A21\u5F0F\u540D\u79F0 */\n  name: string\n  /** \u6A21\u5F0F\u63CF\u8FF0 */\n  description: string\n  /** \u6A21\u5F0F\u539F\u578B */\n  archetype: PatternArchetype\n  /** \u5168\u5C40\u6743\u91CD [0-1] */\n  weight: number\n  /** \u5FC3\u7406\u5B66\u5C5E\u6027 */\n  psychology: CreativePatternPsychology\n  /** \u751F\u6210\u89C4\u5219\u96C6\u5408 */\n  rules: PatternRule[]\n  /** \u7EA6\u675F\u6761\u4EF6 */\n  constraints: CompatibilityRule[]\n  /** \u6548\u679C\u8BC4\u5206 [0-1] */\n  effectiveness_score: number\n  /** \u7528\u6237\u504F\u597D\u8BC4\u5206 [0-1] */\n  user_preference_score: number\n  /** \u751F\u6210\u793A\u4F8B */\n  examples: string[]\n  /** \u53CD\u4F8B\uFF08\u907F\u514D\u751F\u6210\u7684\u7C7B\u578B\uFF09 */\n  anti_examples: string[]\n  /** \u521B\u5EFA\u65F6\u95F4\u6233 */\n  created_at: number\n  /** \u6A21\u5F0F\u7248\u672C */\n  version: string\n  /** \u6A21\u5F0F\u4EE3\u6570\uFF08\u8FDB\u5316\u4EE3\u6570\uFF09 */\n  generation?: number\n  /** \u7236\u6A21\u5F0FID\uFF08\u7528\u4E8E\u8FDB\u5316\u8FFD\u8E2A\uFF09 */\n  parents?: string[]\n}\n\n/**\n * \u8BED\u7D20\u6570\u91CF\u7EA6\u675F\n */\ninterface MorphemeCountConstraint {\n  min?: number\n  max?: number\n  exact?: number\n}\n\n/**\n * \u7C7B\u522B\u8981\u6C42\u7EA6\u675F\n */\ninterface CategoryRequiredConstraint {\n  category: MorphemeCategory\n  min_count?: number\n  max_count?: number\n  exact_count?: number\n}\n\n/**\n * \u6587\u5316\u5339\u914D\u7EA6\u675F\n */\ninterface CulturalMatchConstraint {\n  context: CulturalContext | CulturalContext[]\n  required: boolean\n}\n\n/**\n * \u8D28\u91CF\u9608\u503C\u7EA6\u675F\n */\ninterface QualityThresholdConstraint {\n  min_score: number\n  max_score?: number\n}\n\n/**\n * \u6A21\u5F0F\u89C4\u5219\n */\nexport interface PatternRule {\n  type: 'morpheme_count' | 'category_required' | 'cultural_match' | 'quality_threshold'\n  constraint: MorphemeCountConstraint | CategoryRequiredConstraint | CulturalMatchConstraint | QualityThresholdConstraint\n  weight: number\n}\n\n// ============================================================================\n// \u8D28\u91CF\u8BC4\u4F30\u76F8\u5173\u63A5\u53E3\u5B9A\u4E49 (8\u7EF4\u5EA6\u8BC4\u4F30\u4F53\u7CFB)\n// ============================================================================\n\n/**\n * \u8D28\u91CF\u8BC4\u4F30\u7EF4\u5EA6\u63A5\u53E3\n *\n * \u57FA\u4E8E\u8BA4\u77E5\u5FC3\u7406\u5B66\u548C\u8BED\u8A00\u5B66\u7406\u8BBA\u5B9A\u4E49\u76848\u7EF4\u5EA6\u8D28\u91CF\u8BC4\u4F30\u4F53\u7CFB\n */\nexport interface QualityDimensions {\n  /** \u521B\u610F\u6027 [0-1] - \u7528\u6237\u540D\u7684\u521B\u65B0\u6027\u548C\u72EC\u7279\u6027 */\n  creativity: number\n  /** \u8BB0\u5FC6\u6027 [0-1] - \u7528\u6237\u540D\u7684\u6613\u8BB0\u6027\u548C\u4F20\u64AD\u6027 */\n  memorability: number\n  /** \u6587\u5316\u9002\u914D\u5EA6 [0-1] - \u4E0E\u76EE\u6807\u6587\u5316\u7684\u5951\u5408\u7A0B\u5EA6 */\n  cultural_fit: number\n  /** \u72EC\u7279\u6027 [0-1] - \u7528\u6237\u540D\u7684\u7A00\u6709\u6027\u548C\u533A\u5206\u5EA6 */\n  uniqueness: number\n  /** \u53D1\u97F3\u53CB\u597D\u5EA6 [0-1] - \u7528\u6237\u540D\u7684\u53D1\u97F3\u96BE\u6613\u7A0B\u5EA6 */\n  pronunciation: number\n  /** \u8BED\u4E49\u8FDE\u8D2F\u6027 [0-1] - \u8BED\u7D20\u7EC4\u5408\u7684\u8BED\u4E49\u903B\u8F91\u6027 */\n  semantic_coherence: number\n  /** \u7F8E\u5B66\u5438\u5F15\u529B [0-1] - \u7528\u6237\u540D\u7684\u7F8E\u5B66\u4EF7\u503C\u548C\u5438\u5F15\u529B */\n  aesthetic_appeal: number\n  /** \u5B9E\u7528\u6027 [0-1] - \u7528\u6237\u540D\u5728\u5B9E\u9645\u4F7F\u7528\u4E2D\u7684\u4FBF\u5229\u6027 */\n  practical_usability: number\n}\n\n/**\n * \u8D28\u91CF\u8BC4\u5206\u7ED3\u6784\n *\n * \u5B8C\u6574\u7684\u8D28\u91CF\u8BC4\u4F30\u7ED3\u679C\uFF0C\u5305\u542B\u7EFC\u5408\u8BC4\u5206\u3001\u5206\u7EF4\u5EA6\u8BC4\u5206\u548C\u6539\u8FDB\u5EFA\u8BAE\n */\nexport interface QualityScore {\n  /** \u7EFC\u5408\u8D28\u91CF\u8BC4\u5206 [0-1] */\n  overall: number\n  /** \u5206\u7EF4\u5EA6\u8BC4\u5206 */\n  dimensions: QualityDimensions\n  /** \u8BC4\u4F30\u7F6E\u4FE1\u5EA6 [0-1] */\n  confidence: number\n  /** \u8BC4\u4F30\u8017\u65F6 (\u6BEB\u79D2) */\n  evaluation_time: number\n  /** \u7B97\u6CD5\u7248\u672C */\n  algorithm_version: string\n  /** \u53D1\u73B0\u7684\u95EE\u9898 */\n  issues: string[]\n  /** \u6539\u8FDB\u5EFA\u8BAE */\n  suggestions: string[]\n  /** \u8BC4\u4F30\u65F6\u95F4\u6233 */\n  timestamp: number\n}\n\n/**\n * \u751F\u6210\u7684\u7528\u6237\u540D\u7ED3\u6784\n */\nexport interface GeneratedUsername {\n  text: string\n  pattern: string\n  quality_score: QualityScore\n  explanation: string\n  components: MorphemeComponent[]\n  metadata: {\n    cultural_fit: number\n    creativity: number\n    memorability: number\n    uniqueness: number\n    generation_time: number\n  }\n}\n\n/**\n * \u8BED\u7D20\u7EC4\u4EF6\n */\nexport interface MorphemeComponent {\n  morpheme: Morpheme\n  position: number\n  role: 'prefix' | 'root' | 'suffix' | 'modifier' | 'complement'\n  contribution_score: number\n}\n\n/**\n * \u517C\u5BB9\u6027\u89C4\u5219\n */\nexport interface CompatibilityRule {\n  id: string\n  name: string\n  description: string\n  type: 'category_combination' | 'cultural_harmony' | 'semantic_coherence' | 'phonetic_beauty'\n  conditions: CompatibilityCondition[]\n  score_modifier: number\n  is_blocking: boolean\n}\n\n/**\n * \u517C\u5BB9\u6027\u6761\u4EF6\u503C\u7C7B\u578B\n */\ntype CompatibilityConditionValue = \n  | string \n  | number \n  | boolean \n  | string[] \n  | number[]\n  | MorphemeCategory \n  | CulturalContext\n  | null\n  | undefined\n\n/**\n * \u517C\u5BB9\u6027\u6761\u4EF6\n */\nexport interface CompatibilityCondition {\n  field: string\n  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than'\n  value: CompatibilityConditionValue\n  weight: number\n}\n\n/**\n * \u751F\u6210\u4E0A\u4E0B\u6587\n */\nexport interface GenerationContext {\n  user_preferences?: UserPreferences\n  cultural_preference: CulturalContext\n  style_preference: StylePreference\n  creativity_level: number\n  quality_threshold: number\n  patterns?: string[]\n  exclude_patterns?: string[]\n  context_keywords?: string[]\n}\n\n/**\n * \u7528\u6237\u504F\u597D\n */\nexport interface UserPreferences {\n  favorite_patterns: string[]\n  cultural_preference: CulturalContext\n  style_preference: StylePreference\n  creativity_level: number\n  quality_threshold: number\n  excluded_categories: MorphemeCategory[]\n  preferred_length: 'short' | 'medium' | 'long'\n}\n\n/**\n * \u98CE\u683C\u504F\u597D\u679A\u4E3E\n */\nexport type StylePreference =\n  | 'humorous'      // \u5E7D\u9ED8\u98CE\u8DA3\n  | 'artistic'      // \u6587\u827A\u96C5\u81F4\n  | 'cute'          // \u53EF\u7231\u840C\u7CFB\n  | 'cool'          // \u9177\u70AB\u5E05\u6C14\n  | 'elegant'       // \u4F18\u96C5\u9AD8\u8D35\n  | 'playful'       // \u6D3B\u6CFC\u4FCF\u76AE\n  | 'professional'  // \u4E13\u4E1A\u578B\n\n/**\n * \u957F\u5EA6\u504F\u597D\u679A\u4E3E\n */\nexport type LengthPreference = 'short' | 'medium' | 'long'\n\n/**\n * \u91C7\u6837\u6761\u4EF6\n */\nexport interface SampleCriteria {\n  category?: MorphemeCategory\n  cultural_context?: CulturalContext\n  min_quality_score?: number\n  max_quality_score?: number\n  tags?: string[]\n  exclude_ids?: string[]\n  limit?: number\n  random_seed?: number\n}\n\n/**\n * \u6027\u80FD\u6307\u6807\n */\nexport interface PerformanceMetrics {\n  response_time: {\n    avg: number\n    p95: number\n    p99: number\n    max: number\n  }\n  throughput: {\n    requests_per_second: number\n    concurrent_users: number\n  }\n  resource_usage: {\n    memory_mb: number\n    cpu_percent: number\n  }\n  cache_metrics: {\n    hit_rate: number\n    miss_rate: number\n    eviction_rate: number\n  }\n  quality_metrics: {\n    avg_quality_score: number\n    generation_success_rate: number\n  }\n  timestamp: number\n}\n\n/**\n * \u7F13\u5B58\u952E\u751F\u6210\u53C2\u6570\n */\nexport interface CacheKeyParams {\n  cultural_preference: CulturalContext\n  style_preference: StylePreference\n  creativity_level: number\n  quality_threshold: number\n  patterns?: string[]\n  count: number\n}\n\n/**\n * \u8BED\u4E49\u76F8\u4F3C\u5EA6\u8BA1\u7B97\u7ED3\u679C\n */\nexport interface SemanticSimilarity {\n  score: number\n  dimensions: number[]\n  explanation: string\n}\n\n/**\n * \u7B97\u6CD5\u914D\u7F6E\n */\nexport interface AlgorithmConfig {\n  max_generation_attempts: number\n  quality_threshold: number\n  cache_ttl_seconds: number\n  max_cache_size: number\n  semantic_similarity_threshold: number\n  cultural_weight: number\n  creativity_weight: number\n  quality_weight: number\n}\n\n/**\n * \u5F15\u64CE\u7EDF\u8BA1\u4FE1\u606F morpheme \u8BED\u7D20\n */\nexport interface EngineStats {\n  morpheme_count: number\n  morpheme_stats: {\n    total: number\n    byCategory: Record<string, number>\n    byContext: Record<string, number>\n    avgQuality: number\n  }\n  engine_status: 'ready' | 'not_initialized'\n  total_generations: number\n  avg_generation_time: number\n  success_rate: number\n}"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "bcae2c0108963fcbef00d80f2b1bde51e8502d8c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2lhtm2izxv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2lhtm2izxv();
cov_2lhtm2izxv().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2lhtm2izxv().s[1]++;
exports.PatternArchetype = exports.CulturalContext = exports.MorphemeCategory = void 0;
// ============================================================================
// 基础枚举定义
// ============================================================================
/**
 * 语素类别枚举
 *
 * 定义了语素的主要分类，用于语义组织和检索优化。
 * 基于语言学和认知心理学原理进行分类。
 */
var MorphemeCategory;
/* istanbul ignore next */
cov_2lhtm2izxv().s[2]++;
(function (MorphemeCategory) {
  /* istanbul ignore next */
  cov_2lhtm2izxv().f[0]++;
  cov_2lhtm2izxv().s[3]++;
  /** 情感类语素 - 表达情感状态、感受和情绪 */
  MorphemeCategory["EMOTIONS"] = "emotions";
  /** 职业类语素 - 表达职业身份、专业领域和工作角色 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[4]++;
  MorphemeCategory["PROFESSIONS"] = "professions";
  /** 特征类语素 - 表达个性特征、能力属性和品质特点 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[5]++;
  MorphemeCategory["CHARACTERISTICS"] = "characteristics";
  /** 物体类语素 - 表达具体或抽象的事物、对象 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[6]++;
  MorphemeCategory["OBJECTS"] = "objects";
  /** 动作类语素 - 表达行为、动作状态和活动 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[7]++;
  MorphemeCategory["ACTIONS"] = "actions";
  /** 概念类语素 - 表达抽象概念、理念和思想 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[8]++;
  MorphemeCategory["CONCEPTS"] = "concepts";
})(
/* istanbul ignore next */
(cov_2lhtm2izxv().b[0][0]++, MorphemeCategory) ||
/* istanbul ignore next */
(cov_2lhtm2izxv().b[0][1]++, exports.MorphemeCategory = MorphemeCategory = {}));
/**
 * 文化语境枚举
 *
 * 定义了语素的文化背景属性，用于文化适配和风格匹配。
 * 基于文化人类学和社会心理学理论进行分类。
 */
var CulturalContext;
/* istanbul ignore next */
cov_2lhtm2izxv().s[9]++;
(function (CulturalContext) {
  /* istanbul ignore next */
  cov_2lhtm2izxv().f[1]++;
  cov_2lhtm2izxv().s[10]++;
  /** 古典文化语境 - 传统、典雅、深厚的文化特色 */
  CulturalContext["ANCIENT"] = "ancient";
  /** 现代文化语境 - 时尚、创新、国际化的文化特色 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[11]++;
  CulturalContext["MODERN"] = "modern";
  /** 中性文化语境 - 通用、平衡、包容的文化特色 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[12]++;
  CulturalContext["NEUTRAL"] = "neutral";
})(
/* istanbul ignore next */
(cov_2lhtm2izxv().b[1][0]++, CulturalContext) ||
/* istanbul ignore next */
(cov_2lhtm2izxv().b[1][1]++, exports.CulturalContext = CulturalContext = {}));
// ============================================================================
// 创意模式相关接口定义 (基于第一性原理设计)
// ============================================================================
/**
 * 模式原型枚举
 *
 * 基于第一性原理和用户心理需求分析定义的8种创意模式原型
 */
var PatternArchetype;
/* istanbul ignore next */
cov_2lhtm2izxv().s[13]++;
(function (PatternArchetype) {
  /* istanbul ignore next */
  cov_2lhtm2izxv().f[2]++;
  cov_2lhtm2izxv().s[14]++;
  /** 专业型 - 强调能力和专业性，满足身份表达需求 */
  PatternArchetype["PROFESSIONAL"] = "professional";
  /** 艺术型 - 强调创意和美感，满足美学追求需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[15]++;
  PatternArchetype["ARTISTIC"] = "artistic";
  /** 幽默型 - 强调趣味和亲和力，满足社交润滑需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[16]++;
  PatternArchetype["HUMOROUS"] = "humorous";
  /** 优雅型 - 强调品味和气质，满足品味展示需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[17]++;
  PatternArchetype["ELEGANT"] = "elegant";
  /** 创新型 - 强调前瞻和独特性，满足独特性需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[18]++;
  PatternArchetype["INNOVATIVE"] = "innovative";
  /** 传统型 - 强调文化和底蕴，满足文化认同需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[19]++;
  PatternArchetype["TRADITIONAL"] = "traditional";
  /** 简约型 - 强调简洁和纯粹，满足认知便利需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[20]++;
  PatternArchetype["MINIMALIST"] = "minimalist";
  /** 表达型 - 强调个性和情感，满足情感共鸣需求 */
  /* istanbul ignore next */
  cov_2lhtm2izxv().s[21]++;
  PatternArchetype["EXPRESSIVE"] = "expressive";
})(
/* istanbul ignore next */
(cov_2lhtm2izxv().b[2][0]++, PatternArchetype) ||
/* istanbul ignore next */
(cov_2lhtm2izxv().b[2][1]++, exports.PatternArchetype = PatternArchetype = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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