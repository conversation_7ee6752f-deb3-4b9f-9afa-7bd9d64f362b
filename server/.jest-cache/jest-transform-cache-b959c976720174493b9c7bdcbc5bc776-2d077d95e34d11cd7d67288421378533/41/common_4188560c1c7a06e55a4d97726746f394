2e772b4991f998125bf09971a87fa04a
/* istanbul ignore next */
function cov_1ym1naerw() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts";
  var hash = "7f83545f1f0b62a7f6a0e02d5d97f2e9d98192e8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 34
        },
        end: {
          line: 2,
          column: 43
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/common.ts",
      mappings: "AAAA,uBAAuB;AACvB,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,OAAO,CAAU,CAAA",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/common.ts"],
      sourcesContent: ["// \u652F\u6301\u7684\u7248\u672C\u5E38\u91CF (v3.0\u5355\u4E00\u7248\u672C\u652F\u6301)\nexport const SUPPORTED_VERSIONS = ['3.0.0'] as const\n\n// \u4ECE\u5E38\u91CF\u63A8\u5BFC\u51FA\u7248\u672C\u7C7B\u578B\nexport type MorphemeVersion = typeof SUPPORTED_VERSIONS[number]\n\n\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7f83545f1f0b62a7f6a0e02d5d97f2e9d98192e8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ym1naerw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ym1naerw();
// 支持的版本常量 (v3.0单一版本支持)
export const SUPPORTED_VERSIONS =
/* istanbul ignore next */
(cov_1ym1naerw().s[0]++, ['3.0.0']);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTVVBQT1JURURfVkVSU0lPTlMiLCJjb3ZfMXltMW5hZXJ3IiwicyJdLCJzb3VyY2VzIjpbIi9ob21lL3AvZGV2ZWxvcC93b3Jrc3BhY2UvbmFtZXItdjYvc2VydmVyL3R5cGVzL2NvbW1vbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDmlK/mjIHnmoTniYjmnKzluLjph48gKHYzLjDljZXkuIDniYjmnKzmlK/mjIEpXG5leHBvcnQgY29uc3QgU1VQUE9SVEVEX1ZFUlNJT05TID0gWyczLjAuMCddIGFzIGNvbnN0XG5cbi8vIOS7juW4uOmHj+aOqOWvvOWHuueJiOacrOexu+Wei1xuZXhwb3J0IHR5cGUgTW9ycGhlbWVWZXJzaW9uID0gdHlwZW9mIFNVUFBPUlRFRF9WRVJTSU9OU1tudW1iZXJdXG5cblxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQSxPQUFPLE1BQU1BLGtCQUFrQjtBQUFBO0FBQUEsQ0FBQUMsYUFBQSxHQUFBQyxDQUFBLE9BQUcsQ0FBQyxPQUFPLENBQVUiLCJpZ25vcmVMaXN0IjpbXX0=