c4a1f14b250328336a31e2f1f3a293f6
/* istanbul ignore next */
function cov_qeof9c1nw() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/api.ts";
  var hash = "5f06a644bf72750a38472bf55ef594e4f3ac477e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/api.ts",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 25,
          column: 34
        }
      },
      "1": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 46
        }
      },
      "2": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 50
        }
      },
      "3": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 42
        }
      },
      "4": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 45
        }
      },
      "5": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 45
        }
      },
      "6": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 43
        }
      },
      "7": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 48
        }
      },
      "8": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 19,
          column: 50
        }
      },
      "9": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 42
        }
      },
      "10": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 47
        }
      },
      "11": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 35
        }
      },
      "12": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 49
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 9,
            column: 1
          },
          end: {
            line: 9,
            column: 2
          }
        },
        loc: {
          start: {
            line: 9,
            column: 22
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 9
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 3
          },
          end: {
            line: 25,
            column: 12
          }
        }, {
          start: {
            line: 25,
            column: 17
          },
          end: {
            line: 25,
            column: 31
          }
        }],
        line: 25
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/api.ts",
      mappings: "AAAA;;;GAGG;AAkMH;;GAEG;AACH,MAAM,CAAN,IAAY,SAkBX;AAlBD,WAAY,SAAS;IACnB,mBAAmB;IACnB,yCAA4B,CAAA;IAC5B,6CAAgC,CAAA;IAChC,qCAAwB,CAAA;IACxB,wCAA2B,CAAA;IAE3B,mBAAmB;IACnB,wCAA2B,CAAA;IAC3B,sCAAyB,CAAA;IACzB,2CAA8B,CAAA;IAC9B,6CAAgC,CAAA;IAEhC,mBAAmB;IACnB,qCAAwB,CAAA;IACxB,0CAA6B,CAAA;IAC7B,8BAAiB,CAAA;IACjB,4CAA+B,CAAA;AACjC,CAAC,EAlBW,SAAS,KAAT,SAAS,QAkBpB",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/api.ts"],
      sourcesContent: ["/**\n * API\u63A5\u53E3\u7C7B\u578B\u5B9A\u4E49\n * \u5B9A\u4E49namer-v6\u9879\u76EE\u7684API\u8BF7\u6C42\u548C\u54CD\u5E94\u7ED3\u6784\n */\n\nimport type {\n  GeneratedUsername,\n  QualityScore,\n  CulturalContext,\n  StylePreference,\n  PerformanceMetrics,\n  CreativePattern,\n  LengthPreference\n} from './core'\n\n/**\n * \u7EDF\u4E00API\u54CD\u5E94\u683C\u5F0F\n */\nexport interface APIResponse<T = unknown> {\n  success: boolean\n  data?: T\n  error?: {\n    code: string\n    message: string\n    details?: Record<string, unknown>\n  }\n  meta: {\n    timestamp: number\n    request_id: string\n    execution_time: number\n    version: string\n  }\n}\n\n/**\n * \u7528\u6237\u540D\u751F\u6210\u8BF7\u6C42\n */\nexport interface GenerateRequest {\n  count?: number                    // \u751F\u6210\u6570\u91CF\uFF0C\u9ED8\u8BA45\uFF0C\u6700\u592720\n  language?: string                 // \u8BED\u8A00\u4EE3\u7801\uFF0C\u9ED8\u8BA4zh-CN\n  cultural_preference?: CulturalContext\n  style_preference?: StylePreference\n  length_preference?: LengthPreference\n  creativity_level?: number         // \u521B\u610F\u7A0B\u5EA6: 0.1-1.0\n  quality_threshold?: number        // \u8D28\u91CF\u9608\u503C: 0.1-1.0\n  user_id?: string                  // \u7528\u6237ID\uFF0C\u7528\u4E8E\u4E2A\u6027\u5316\n  context?: {\n    occasion?: string               // \u4F7F\u7528\u573A\u5408\n    target_audience?: string        // \u76EE\u6807\u53D7\u4F17\n    keywords?: string[]             // \u5173\u952E\u8BCD\n  }\n  patterns?: string[]               // \u6307\u5B9A\u521B\u610F\u6A21\u5F0F\n  exclude_patterns?: string[]       // \u6392\u9664\u7684\u6A21\u5F0F\n}\n\n/**\n * \u7528\u6237\u540D\u751F\u6210\u54CD\u5E94\n */\nexport interface GenerateResponse {\n  usernames: GeneratedUsername[]\n  generation_time: number           // \u751F\u6210\u8017\u65F6(ms)\n  cache_hit: boolean               // \u662F\u5426\u547D\u4E2D\u7F13\u5B58\n  recommendations?: string[]        // \u63A8\u8350\u7684\u76F8\u5173\u7528\u6237\u540D\n  patterns_used: string[]          // \u4F7F\u7528\u7684\u6A21\u5F0F\u5217\u8868\n  total_attempts: number           // \u603B\u5C1D\u8BD5\u6B21\u6570\n}\n\n/**\n * \u6279\u91CF\u751F\u6210\u8BF7\u6C42\n */\nexport interface BatchGenerateRequest {\n  requests: GenerateRequest[]       // \u6279\u91CF\u8BF7\u6C42\n  parallel?: boolean               // \u662F\u5426\u5E76\u884C\u5904\u7406\uFF0C\u9ED8\u8BA4true\n  timeout?: number                 // \u8D85\u65F6\u65F6\u95F4(ms)\uFF0C\u9ED8\u8BA430000\n}\n\n/**\n * \u6279\u91CF\u751F\u6210\u54CD\u5E94\n */\nexport interface BatchGenerateResponse {\n  results: (GenerateResponse | null)[]  // \u7ED3\u679C\u6570\u7EC4\uFF0C\u5931\u8D25\u4E3Anull\n  success_count: number            // \u6210\u529F\u6570\u91CF\n  total_time: number              // \u603B\u8017\u65F6\n  errors?: BatchError[]           // \u9519\u8BEF\u4FE1\u606F\n}\n\n/**\n * \u6279\u91CF\u9519\u8BEF\u4FE1\u606F\n */\nexport interface BatchError {\n  index: number\n  error: {\n    code: string\n    message: string\n    details?: Record<string, unknown>\n  }\n}\n\n/**\n * \u8D28\u91CF\u8BC4\u4F30\u8BF7\u6C42\n */\nexport interface EvaluateRequest {\n  usernames: string[]              // \u5F85\u8BC4\u4F30\u7684\u7528\u6237\u540D\n  language?: string                // \u8BED\u8A00\u4EE3\u7801\n  cultural_context?: CulturalContext\n  detailed?: boolean               // \u662F\u5426\u8FD4\u56DE\u8BE6\u7EC6\u8BC4\u4F30\n}\n\n/**\n * \u8D28\u91CF\u8BC4\u4F30\u54CD\u5E94\n */\nexport interface EvaluateResponse {\n  evaluations: UsernameEvaluation[]\n  average_score: number\n  evaluation_time: number\n  summary: {\n    excellent_count: number        // \u4F18\u79C0\u6570\u91CF (>0.8)\n    good_count: number            // \u826F\u597D\u6570\u91CF (0.6-0.8)\n    average_count: number         // \u4E00\u822C\u6570\u91CF (0.4-0.6)\n    poor_count: number            // \u8F83\u5DEE\u6570\u91CF (<0.4)\n  }\n}\n\n/**\n * \u7528\u6237\u540D\u8BC4\u4F30\u7ED3\u679C\n */\nexport interface UsernameEvaluation {\n  username: string\n  overall_score: number            // \u603B\u4F53\u8BC4\u5206 0-1\n  quality_score: QualityScore\n  suggestions?: string[]           // \u6539\u8FDB\u5EFA\u8BAE\n  similar_usernames?: string[]     // \u76F8\u4F3C\u7528\u6237\u540D\u63A8\u8350\n}\n\n/**\n * \u5065\u5EB7\u68C0\u67E5\u54CD\u5E94\n */\nexport interface HealthResponse {\n  status: 'healthy' | 'degraded' | 'unhealthy'\n  timestamp: number\n  version: string\n  uptime: number\n  checks: {\n    memory: HealthCheck\n    cache: HealthCheck\n    data_integrity: HealthCheck\n    performance: HealthCheck\n  }\n}\n\n/**\n * \u5065\u5EB7\u68C0\u67E5\u8BE6\u60C5\n */\nexport interface HealthCheckDetails {\n  [key: string]: string | number | boolean | null | undefined | HealthCheckDetails | HealthCheckDetails[]\n}\n\n/**\n * \u5065\u5EB7\u68C0\u67E5\u9879\n */\nexport interface HealthCheck {\n  status: 'pass' | 'fail' | 'warn'\n  response_time?: number\n  details?: HealthCheckDetails\n  message?: string\n}\n\n/**\n * \u6027\u80FD\u6307\u6807\u54CD\u5E94\n */\nexport interface MetricsResponse {\n  performance: PerformanceMetrics\n  system_info: {\n    node_version: string\n    memory_usage: NodeJS.MemoryUsage\n    uptime: number\n    platform: string\n  }\n  api_stats: {\n    total_requests: number\n    successful_requests: number\n    failed_requests: number\n    avg_response_time: number\n  }\n}\n\n/**\n * \u521B\u610F\u6A21\u5F0F\u5217\u8868\u54CD\u5E94\n */\nexport interface PatternsResponse {\n  patterns: CreativePattern[]\n  total_count: number\n  categories: {\n    [category: string]: number\n  }\n}\n\n/**\n * \u9519\u8BEF\u4EE3\u7801\u679A\u4E3E\n */\nexport enum ErrorCode {\n  // \u901A\u7528\u9519\u8BEF (1000-1999)\n  INVALID_PARAMETERS = 'E1001',\n  MISSING_REQUIRED_FIELD = 'E1002',\n  INVALID_FORMAT = 'E1003',\n  VALIDATION_FAILED = 'E1004',\n\n  // \u4E1A\u52A1\u9519\u8BEF (3000-3999)\n  GENERATION_FAILED = 'E3001',\n  QUALITY_TOO_LOW = 'E3002',\n  NO_SUITABLE_PATTERNS = 'E3003',\n  INSUFFICIENT_MORPHEMES = 'E3004',\n\n  // \u7CFB\u7EDF\u9519\u8BEF (5000-5999)\n  INTERNAL_ERROR = 'E5001',\n  SERVICE_UNAVAILABLE = 'E5002',\n  TIMEOUT = 'E5003',\n  MEMORY_LIMIT_EXCEEDED = 'E5004'\n}\n\n/**\n * \u8BF7\u6C42\u9A8C\u8BC1\u89C4\u5219\n */\nexport type ValidationValue = string | number | boolean | null | undefined\n\nexport interface ValidationRule {\n  field: string\n  type: 'string' | 'number' | 'boolean' | 'array' | 'object'\n  required?: boolean\n  min?: number\n  max?: number\n  pattern?: RegExp\n  enum?: ValidationValue[]\n  custom?: (value: unknown) => boolean | string\n}\n\n/**\n * \u5206\u9875\u53C2\u6570\n */\nexport interface PaginationParams {\n  page?: number\n  limit?: number\n  sort?: string\n  order?: 'asc' | 'desc'\n}\n\n/**\n * \u5206\u9875\u54CD\u5E94\n */\nexport interface PaginatedResponse<T> {\n  data: T[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n    has_next: boolean\n    has_prev: boolean\n  }\n}"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5f06a644bf72750a38472bf55ef594e4f3ac477e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_qeof9c1nw = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_qeof9c1nw();
/**
 * API接口类型定义
 * 定义namer-v6项目的API请求和响应结构
 */
/**
 * 错误代码枚举
 */
export var ErrorCode;
/* istanbul ignore next */
cov_qeof9c1nw().s[0]++;
(function (ErrorCode) {
  /* istanbul ignore next */
  cov_qeof9c1nw().f[0]++;
  cov_qeof9c1nw().s[1]++;
  // 通用错误 (1000-1999)
  ErrorCode["INVALID_PARAMETERS"] = "E1001";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[2]++;
  ErrorCode["MISSING_REQUIRED_FIELD"] = "E1002";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[3]++;
  ErrorCode["INVALID_FORMAT"] = "E1003";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[4]++;
  ErrorCode["VALIDATION_FAILED"] = "E1004";
  // 业务错误 (3000-3999)
  /* istanbul ignore next */
  cov_qeof9c1nw().s[5]++;
  ErrorCode["GENERATION_FAILED"] = "E3001";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[6]++;
  ErrorCode["QUALITY_TOO_LOW"] = "E3002";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[7]++;
  ErrorCode["NO_SUITABLE_PATTERNS"] = "E3003";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[8]++;
  ErrorCode["INSUFFICIENT_MORPHEMES"] = "E3004";
  // 系统错误 (5000-5999)
  /* istanbul ignore next */
  cov_qeof9c1nw().s[9]++;
  ErrorCode["INTERNAL_ERROR"] = "E5001";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[10]++;
  ErrorCode["SERVICE_UNAVAILABLE"] = "E5002";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[11]++;
  ErrorCode["TIMEOUT"] = "E5003";
  /* istanbul ignore next */
  cov_qeof9c1nw().s[12]++;
  ErrorCode["MEMORY_LIMIT_EXCEEDED"] = "E5004";
})(
/* istanbul ignore next */
(cov_qeof9c1nw().b[0][0]++, ErrorCode) ||
/* istanbul ignore next */
(cov_qeof9c1nw().b[0][1]++, ErrorCode = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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