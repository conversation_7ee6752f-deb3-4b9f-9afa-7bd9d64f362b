{"file": "/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,2CAAoD;AAQpD,iDAAqD;AACrD,iEAKoC;AAWpC,IAAA,kBAAQ,EAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,QAAQ,GAAa;gBACzB,EAAE,EAAE,UAAU;gBACd,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,UAA8B;gBACxC,WAAW,EAAE,UAAU;gBACvB,gBAAgB,EAAE,yBAAe,CAAC,MAAM;gBACxC,eAAe,EAAE,GAAG;gBACpB,aAAa,EAAE,GAAG;gBAClB,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAChC,IAAI,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;gBACzB,mBAAmB,EAAE;oBACnB,cAAc,EAAE,CAAC;oBACjB,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;oBACrC,kBAAkB,EAAE,UAAU;oBAC9B,aAAa,EAAE,QAAQ;iBACxB;gBACD,eAAe,EAAE;oBACf,WAAW,EAAE,GAAG;oBAChB,SAAS,EAAE,GAAG;oBACd,aAAa,EAAE,GAAG;oBAClB,gBAAgB,EAAE,GAAG;iBACtB;gBACD,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;gBACtB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,OAAO;aACjB,CAAA;YAED,IAAA,gBAAM,EAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACpC,IAAA,gBAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChC,IAAA,gBAAM,EAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,OAAO,GAAsB;gBACjC,mBAAmB,EAAE,yBAAe,CAAC,MAAM;gBAC3C,gBAAgB,EAAE,SAAS;gBAC3B,gBAAgB,EAAE,GAAG;gBACrB,iBAAiB,EAAE,GAAG;gBACtB,QAAQ,EAAE,CAAC,QAAQ,CAAC;gBACpB,gBAAgB,EAAE,CAAC,cAAc,CAAC;aACnC,CAAA;YAED,IAAA,gBAAM,EAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAChD,IAAA,gBAAM,EAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAA,gBAAM,EAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC5C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,QAAQ,GAAsB;gBAClC,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,QAAQ;gBACjB,aAAa,EAAE;oBACb,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE;wBACV,UAAU,EAAE,GAAG;wBACf,YAAY,EAAE,GAAG;wBACjB,YAAY,EAAE,GAAG;wBACjB,UAAU,EAAE,GAAG;wBACf,aAAa,EAAE,GAAG;wBAClB,kBAAkB,EAAE,GAAG;wBACvB,gBAAgB,EAAE,GAAG;wBACrB,mBAAmB,EAAE,GAAG;qBACzB;oBACD,UAAU,EAAE,GAAG;oBACf,eAAe,EAAE,GAAG;oBACpB,iBAAiB,EAAE,MAAM;oBACzB,MAAM,EAAE,EAAE;oBACV,WAAW,EAAE,EAAE;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB;gBACD,WAAW,EAAE,mBAAmB;gBAChC,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE;oBACR,YAAY,EAAE,GAAG;oBACjB,UAAU,EAAE,GAAG;oBACf,YAAY,EAAE,GAAG;oBACjB,UAAU,EAAE,GAAG;oBACf,eAAe,EAAE,GAAG;iBACrB;aACF,CAAA;YAED,IAAA,gBAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACnC,IAAA,gBAAM,EAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACjD,IAAA,gBAAM,EAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,YAAE,EAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,SAAS,GAAmB;gBAChC,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;gBAClB,8BAAY,CAAC,KAAK;aACnB,CAAA;YAED,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YACjC,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,SAAS,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,SAAS,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,OAAO,GAAqB;gBAChC,UAAU,EAAE,aAAa;gBACzB,eAAe,EAAE;oBACf,MAAM,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;oBAChC,aAAa,EAAE,YAAY;oBAC3B,UAAU,EAAE,IAAI;oBAChB,UAAU,EAAE,sBAAsB;iBACnC;gBACD,gBAAgB,EAAE,iCAAe,CAAC,QAAQ;gBAC1C,iBAAiB,EAAE,GAAG;gBACtB,mBAAmB,EAAE,GAAG;gBACxB,uBAAuB,EAAE,GAAG;gBAC5B,cAAc,EAAE,GAAG;gBACnB,kBAAkB,EAAE,GAAG;gBACvB,iBAAiB,EAAE,GAAG;gBACtB,oBAAoB,EAAE;oBACpB,YAAY,EAAE,GAAG;oBACjB,cAAc,EAAE,GAAG;oBACnB,iBAAiB,EAAE,GAAG;iBACvB;gBACD,gBAAgB,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;gBACjD,qBAAqB,EAAE,EAAE;gBACzB,UAAU,EAAE,sBAAsB;gBAClC,UAAU,EAAE,sBAAsB;gBAClC,OAAO,EAAE,OAAO;aACjB,CAAA;YAED,IAAA,gBAAM,EAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAC9C,IAAA,gBAAM,EAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;YACxD,IAAA,gBAAM,EAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACnD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAA6B;gBACzC,WAAW,EAAE,iBAAiB;gBAC9B,UAAU,EAAE,aAAa;gBACzB,QAAQ,EAAE,8BAAY,CAAC,KAAK;gBAC5B,IAAI,EAAE,IAAI;gBACV,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC/B,iBAAiB,EAAE;oBACjB,iBAAiB,EAAE,WAAW;oBAC9B,cAAc,EAAE,CAAC;oBACjB,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;oBACxB,gBAAgB,EAAE,GAAG;iBACtB;gBACD,kBAAkB,EAAE;oBAClB,OAAO,EAAE,KAAK;oBACd,kBAAkB,EAAE,UAAU;oBAC9B,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,EAAE;iBACb;gBACD,oBAAoB,EAAE;oBACpB,kBAAkB,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;oBAClD,uBAAuB,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;oBACvC,oBAAoB,EAAE,EAAE;iBACzB;gBACD,gBAAgB,EAAE;oBAChB,cAAc,EAAE,GAAG;oBACnB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,GAAG;oBACd,WAAW,EAAE,GAAG;oBAChB,qBAAqB,EAAE,GAAG;oBAC1B,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;oBAC/C,aAAa,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;iBACpD;gBACD,iBAAiB,EAAE,EAAE;gBACrB,cAAc,EAAE,+BAAa,CAAC,OAAO;gBACrC,uBAAuB,EAAE;oBACvB,WAAW,EAAE,GAAG;oBAChB,OAAO,EAAE,GAAG;oBACZ,YAAY,EAAE,IAAI;oBAClB,gBAAgB,EAAE,GAAG;oBACrB,kBAAkB,EAAE,GAAG;oBACvB,YAAY,EAAE,GAAG;oBACjB,UAAU,EAAE,GAAG;oBACf,YAAY,EAAE,GAAG;iBAClB;gBACD,wBAAwB,EAAE,GAAG;gBAC7B,qBAAqB,EAAE,IAAI;gBAC3B,eAAe,EAAE,GAAG;gBACpB,gBAAgB,EAAE,GAAG;gBACrB,UAAU,EAAE,sBAAsB;gBAClC,UAAU,EAAE,sBAAsB;gBAClC,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,eAAe;gBACvB,iBAAiB,EAAE,WAAW;aAC/B,CAAA;YAED,IAAA,gBAAM,EAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YACpD,IAAA,gBAAM,EAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAA;YAClD,IAAA,gBAAM,EAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACzD,IAAA,gBAAM,EAAC,QAAQ,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAChE,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,SAAS;YACT,MAAM,SAAS,GAAW,4CAA0B,CAAC,MAAM,CAAA;YAC3D,MAAM,eAAe,GAAW,4CAA0B,CAAC,YAAY,CAAA;YACvE,MAAM,UAAU,GAAW,4CAA0B,CAAC,OAAO,CAAA;YAE7D,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC1B,IAAA,gBAAM,EAAC,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACjC,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,YAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,QAAQ,GAAsB,CAAC,yBAAe,CAAC,OAAO,EAAE,yBAAe,CAAC,MAAM,EAAE,yBAAe,CAAC,OAAO,CAAC,CAAA;YAE9G,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzB,IAAA,gBAAM,EAAC,CAAC,yBAAe,CAAC,OAAO,EAAE,yBAAe,CAAC,MAAM,EAAE,yBAAe,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACvG,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,YAAY,GAAoC;gBACpD,cAAc,EAAE,GAAG;gBACnB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,qBAAqB,EAAE,GAAG;gBAC1B,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;gBACtC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;aACtC,CAAA;YAED,IAAA,gBAAM,EAAC,YAAY,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,YAAY,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;QAC1D,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,eAAe,EAAE,GAAG,EAAE;YACvB,MAAM,eAAe,GAA4B;gBAC/C,MAAM,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;gBAChC,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,sBAAsB;gBAClC,aAAa,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO;aAC/C,CAAA;YAED,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;YAChD,IAAA,gBAAM,EAAC,eAAe,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,cAAc,EAAE,GAAG,EAAE;YACtB,WAAW;YACX,SAAS,cAAc,CAAC,IAAY;gBAClC,OAAO;oBACL,EAAE,EAAE,MAAM;oBACV,IAAI;oBACJ,QAAQ,EAAE,UAA8B;oBACxC,WAAW,EAAE,UAAU;oBACvB,gBAAgB,EAAE,yBAAe,CAAC,MAAM;oBACxC,eAAe,EAAE,GAAG;oBACpB,aAAa,EAAE,GAAG;oBAClB,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;oBAC3B,IAAI,EAAE,CAAC,MAAM,CAAC;oBACd,mBAAmB,EAAE;wBACnB,cAAc,EAAE,CAAC;wBACjB,eAAe,EAAE,CAAC;wBAClB,iBAAiB,EAAE,CAAC,OAAO,CAAC;wBAC5B,kBAAkB,EAAE,QAAQ;qBAC7B;oBACD,eAAe,EAAE;wBACf,WAAW,EAAE,GAAG;wBAChB,SAAS,EAAE,GAAG;wBACd,aAAa,EAAE,GAAG;wBAClB,gBAAgB,EAAE,GAAG;qBACtB;oBACD,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;oBACtB,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,OAAO;iBACjB,CAAA;YACH,CAAC;YAED,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA;YACrC,IAAA,gBAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,SAAS;YACT,SAAS,mBAAmB,CAAuC,IAAS;gBAC1E,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAA;YACvD,CAAC;YAED,MAAM,QAAQ,GAAG;gBACf,EAAE,QAAQ,EAAE,8BAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;gBAC7C,EAAE,QAAQ,EAAE,8BAAY,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;aAChD,CAAA;YAED,MAAM,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAA;YAC9C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YAChC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAA;QACvD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,8BAA8B;YAE9B,UAAU;YACV,MAAM,aAAa,GAAiB,8BAAY,CAAC,KAAK,CAAA;YACtD,MAAM,aAAa,GAAkB,+BAAa,CAAC,MAAM,CAAA;YAEzD,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,IAAI,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAA;YAC9C,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,IAAI,CAAC,+BAAa,CAAC,MAAM,CAAC,CAAA;YAEhD,0BAA0B;YAC1B,+DAA+D;YAC/D,2DAA2D;QAC7D,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,0BAA0B;YAC1B,MAAM,eAAe,GAAsB;gBACzC,EAAE,EAAE,MAAM;gBACV,IAAI,EAAE,IAAI;gBACV,WAAW;aACZ,CAAA;YAED,wBAAwB;YACxB,IAAA,gBAAM,EAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACvC,IAAA,gBAAM,EAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,IAAA,YAAE,EAAC,eAAe,EAAE,GAAG,EAAE;YACvB,MAAM,YAAY,GAAiB;gBACjC,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE;oBACV,UAAU,EAAE,GAAG;oBACf,YAAY,EAAE,GAAG;oBACjB,YAAY,EAAE,GAAG;oBACjB,UAAU,EAAE,GAAG;oBACf,aAAa,EAAE,GAAG;oBAClB,kBAAkB,EAAE,GAAG;oBACvB,gBAAgB,EAAE,GAAG;oBACrB,mBAAmB,EAAE,GAAG;iBACzB;gBACD,UAAU,EAAE,GAAG;gBACf,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,MAAM;gBACzB,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAA;YAED,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrD,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;gBACvC,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACtC,CAAC,CAAC,CAAA;YAEF,iBAAiB;YACjB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;YAElG,iBAAiB;YACjB,IAAA,gBAAM,EAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QACrE,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,iBAAiB,EAAE,GAAG,EAAE;YACzB,MAAM,iBAAiB,GAA0B;gBAC/C,WAAW,EAAE,GAAG;gBAChB,OAAO,EAAE,IAAI;gBACb,YAAY,EAAE,IAAI;gBAClB,gBAAgB,EAAE,GAAG;gBACrB,kBAAkB,EAAE,IAAI;gBACxB,YAAY,EAAE,GAAG;gBACjB,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,IAAI;aACnB,CAAA;YAED,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC/C,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;gBACvC,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACtC,CAAC,CAAC,CAAA;YAEF,WAAW;YACX,IAAA,gBAAM,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA,CAAC,UAAU;YACtE,IAAA,gBAAM,EAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA,CAAC,UAAU;QACvE,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA", "names": [], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/test/types/type-safety.test.ts"], "sourcesContent": ["/**\n * TypeScript 类型安全测试\n * \n * 确保TypeScript编译无错误，验证类型定义的正确性\n */\n\nimport { describe, it, expect } from '@jest/globals'\nimport type {\n  Morpheme,\n  GenerationContext,\n  GeneratedUsername,\n  QualityScore,\n  MorphemeCategory\n} from '../../types/core.js'\nimport { CulturalContext } from '../../types/core.js'\nimport {\n  LanguageCode,\n  RegisterLevel,\n  ConceptCategory,\n  SEMANTIC_VECTOR_DIMENSIONS\n} from '../../types/multilingual.js'\nimport type {\n  UniversalConcept,\n  LanguageSpecificMorpheme,\n  UniversalSemanticVector,\n  MultiDimensionalCulturalContext,\n  PhoneticFeatures,\n  MorphologicalInfo,\n  LanguageQualityScores\n} from '../../types/multilingual.js'\n\ndescribe('TypeScript 类型安全测试', () => {\n  describe('核心类型定义', () => {\n    it('应该正确定义 Morpheme 类型', () => {\n      const morpheme: Morpheme = {\n        id: 'test_001',\n        text: '测试',\n        category: 'emotions' as MorphemeCategory,\n        subcategory: 'positive',\n        cultural_context: CulturalContext.MODERN,\n        usage_frequency: 0.8,\n        quality_score: 0.9,\n        semantic_vector: [0.1, 0.2, 0.3],\n        tags: ['test', 'example'],\n        language_properties: {\n          syllable_count: 2,\n          character_count: 2,\n          phonetic_features: ['tone2', 'tone4'],\n          morphological_type: 'compound',\n          pronunciation: 'cè shì'\n        },\n        quality_metrics: {\n          naturalness: 0.9,\n          frequency: 0.8,\n          acceptability: 0.9,\n          aesthetic_appeal: 0.7\n        },\n        created_at: Date.now(),\n        source: 'test',\n        version: '2.0.0'\n      }\n\n      expect(morpheme.id).toBe('test_001')\n      expect(morpheme.text).toBe('测试')\n      expect(morpheme.quality_score).toBe(0.9)\n    })\n\n    it('应该正确定义 GenerationContext 类型', () => {\n      const context: GenerationContext = {\n        cultural_preference: CulturalContext.MODERN,\n        style_preference: 'elegant',\n        creativity_level: 0.8,\n        quality_threshold: 0.7,\n        patterns: ['形容词+名词'],\n        exclude_patterns: ['小{adjective}']\n      }\n\n      expect(context.style_preference).toBe('elegant')\n      expect(context.quality_threshold).toBe(0.7)\n      expect(context.creativity_level).toBe(0.8)\n    })\n\n    it('应该正确定义 GeneratedUsername 类型', () => {\n      const username: GeneratedUsername = {\n        text: '优雅设计师',\n        pattern: '形容词+名词',\n        quality_score: {\n          overall: 0.85,\n          dimensions: {\n            creativity: 0.9,\n            memorability: 0.8,\n            cultural_fit: 0.9,\n            uniqueness: 0.7,\n            pronunciation: 0.8,\n            semantic_coherence: 0.8,\n            aesthetic_appeal: 0.8,\n            practical_usability: 0.8\n          },\n          confidence: 0.9,\n          evaluation_time: 150,\n          algorithm_version: 'v2.0',\n          issues: [],\n          suggestions: [],\n          timestamp: Date.now()\n        },\n        explanation: '结合了优雅的特质和设计师的职业特色',\n        components: [],\n        metadata: {\n          cultural_fit: 0.8,\n          creativity: 0.7,\n          memorability: 0.9,\n          uniqueness: 0.7,\n          generation_time: 150\n        }\n      }\n\n      expect(username.text).toBe('优雅设计师')\n      expect(username.quality_score.overall).toBe(0.85)\n      expect(username.metadata.generation_time).toBe(150)\n    })\n  })\n\n  describe('多语种类型定义', () => {\n    it('应该正确定义 LanguageCode 枚举', () => {\n      const languages: LanguageCode[] = [\n        LanguageCode.ZH_CN,\n        LanguageCode.EN_US,\n        LanguageCode.JA_JP,\n        LanguageCode.KO_KR,\n        LanguageCode.ES_ES,\n        LanguageCode.FR_FR,\n        LanguageCode.DE_DE,\n        LanguageCode.AR_SA\n      ]\n\n      expect(languages).toHaveLength(8)\n      expect(languages).toContain(LanguageCode.ZH_CN)\n      expect(languages).toContain(LanguageCode.EN_US)\n    })\n\n    it('应该正确定义 UniversalConcept 类型', () => {\n      const concept: UniversalConcept = {\n        concept_id: 'concept_001',\n        semantic_vector: {\n          vector: new Array(512).fill(0.1),\n          model_version: 'mBERT-v1.0',\n          confidence: 0.95,\n          updated_at: '2025-06-24T00:00:00Z'\n        },\n        concept_category: ConceptCategory.EMOTIONS,\n        abstraction_level: 0.6,\n        cultural_neutrality: 0.8,\n        cross_lingual_stability: 0.9,\n        cognitive_load: 0.3,\n        memorability_score: 0.8,\n        emotional_valence: 0.7,\n        cognitive_attributes: {\n          memorability: 0.8,\n          cognitive_load: 0.3,\n          emotional_valence: 0.7\n        },\n        related_concepts: ['joy_001', 'satisfaction_001'],\n        hierarchical_children: [],\n        created_at: '2025-06-24T00:00:00Z',\n        updated_at: '2025-06-24T00:00:00Z',\n        version: '3.0.0'\n      }\n\n      expect(concept.concept_id).toBe('concept_001')\n      expect(concept.semantic_vector.vector).toHaveLength(512)\n      expect(concept.concept_category).toBe('emotions')\n    })\n\n    it('应该正确定义 LanguageSpecificMorpheme 类型', () => {\n      const morpheme: LanguageSpecificMorpheme = {\n        morpheme_id: 'zh_morpheme_001',\n        concept_id: 'concept_001',\n        language: LanguageCode.ZH_CN,\n        text: '快乐',\n        alternative_forms: ['愉快', '开心'],\n        phonetic_features: {\n          ipa_transcription: '/kuài.lè/',\n          syllable_count: 2,\n          tone_pattern: ['4', '4'],\n          phonetic_harmony: 0.8\n        },\n        morphological_info: {\n          pos_tag: 'ADJ',\n          morphological_type: 'compound',\n          root: '快乐',\n          prefixes: [],\n          suffixes: []\n        },\n        syntactic_properties: {\n          syntactic_function: ['attributive', 'predicative'],\n          collocation_constraints: ['快乐的', '很快乐'],\n          grammatical_features: {}\n        },\n        cultural_context: {\n          traditionality: 0.6,\n          modernity: 0.8,\n          formality: 0.5,\n          regionality: 0.7,\n          religious_sensitivity: 0.3,\n          age_appropriateness: ['adult', 'teen', 'child'],\n          cultural_tags: ['positive', 'emotion', 'wellbeing']\n        },\n        regional_variants: [],\n        register_level: RegisterLevel.NEUTRAL,\n        language_quality_scores: {\n          naturalness: 0.9,\n          fluency: 0.9,\n          authenticity: 0.95,\n          aesthetic_appeal: 0.8,\n          pronunciation_ease: 0.8,\n          memorability: 0.9,\n          uniqueness: 0.6,\n          practicality: 0.9\n        },\n        cultural_appropriateness: 0.9,\n        native_speaker_rating: 0.95,\n        usage_frequency: 0.8,\n        popularity_trend: 0.1,\n        created_at: '2025-06-24T00:00:00Z',\n        updated_at: '2025-06-24T00:00:00Z',\n        version: '3.0.0',\n        source: 'native_corpus',\n        validation_status: 'validated'\n      }\n\n      expect(morpheme.morpheme_id).toBe('zh_morpheme_001')\n      expect(morpheme.language).toBe(LanguageCode.ZH_CN)\n      expect(morpheme.phonetic_features.syllable_count).toBe(2)\n      expect(morpheme.language_quality_scores.naturalness).toBe(0.9)\n    })\n\n    it('应该正确定义语义向量维度常量', () => {\n      // 测试常量类型\n      const legacyDim: number = SEMANTIC_VECTOR_DIMENSIONS.LEGACY\n      const multilingualDim: number = SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL\n      const defaultDim: number = SEMANTIC_VECTOR_DIMENSIONS.DEFAULT\n\n      expect(legacyDim).toBe(20)\n      expect(multilingualDim).toBe(512)\n      expect(defaultDim).toBe(512)\n    })\n  })\n\n  describe('类型兼容性测试', () => {\n    it('应该支持传统 CulturalContext 枚举', () => {\n      const contexts: CulturalContext[] = [CulturalContext.ANCIENT, CulturalContext.MODERN, CulturalContext.NEUTRAL]\n\n      contexts.forEach(context => {\n        expect([CulturalContext.ANCIENT, CulturalContext.MODERN, CulturalContext.NEUTRAL]).toContain(context)\n      })\n    })\n\n    it('应该支持多维度 CulturalContext 接口', () => {\n      const multiContext: MultiDimensionalCulturalContext = {\n        traditionality: 0.6,\n        modernity: 0.8,\n        formality: 0.5,\n        regionality: 0.7,\n        religious_sensitivity: 0.3,\n        age_appropriateness: ['adult', 'teen'],\n        cultural_tags: ['positive', 'modern']\n      }\n\n      expect(multiContext.traditionality).toBe(0.6)\n      expect(multiContext.cultural_tags).toContain('positive')\n    })\n\n    it('应该支持语义向量的向后兼容', () => {\n      const universalVector: UniversalSemanticVector = {\n        vector: new Array(512).fill(0.1),\n        model_version: 'mBERT-v1.0',\n        confidence: 0.95,\n        updated_at: '2025-06-24T00:00:00Z',\n        legacy_vector: new Array(20).fill(0.1) // 向后兼容\n      }\n\n      expect(universalVector.vector).toHaveLength(512)\n      expect(universalVector.legacy_vector).toHaveLength(20)\n    })\n  })\n\n  describe('类型推断测试', () => {\n    it('应该正确推断函数返回类型', () => {\n      // 模拟函数类型推断\n      function createMorpheme(text: string): Morpheme {\n        return {\n          id: 'test',\n          text,\n          category: 'emotions' as MorphemeCategory,\n          subcategory: 'positive',\n          cultural_context: CulturalContext.MODERN,\n          usage_frequency: 0.8,\n          quality_score: 0.9,\n          semantic_vector: [0.1, 0.2],\n          tags: ['test'],\n          language_properties: {\n            syllable_count: 1,\n            character_count: 2,\n            phonetic_features: ['tone4'],\n            morphological_type: 'simple'\n          },\n          quality_metrics: {\n            naturalness: 0.9,\n            frequency: 0.8,\n            acceptability: 0.9,\n            aesthetic_appeal: 0.7\n          },\n          created_at: Date.now(),\n          source: 'test',\n          version: '2.0.0'\n        }\n      }\n\n      const morpheme = createMorpheme('测试')\n      expect(morpheme.text).toBe('测试')\n    })\n\n    it('应该支持泛型类型约束', () => {\n      // 测试泛型约束\n      function processLanguageData<T extends { language: LanguageCode }>(data: T[]): T[] {\n        return data.filter(item => item.language === 'zh-CN')\n      }\n\n      const mockData = [\n        { language: LanguageCode.ZH_CN, text: '测试1' },\n        { language: LanguageCode.EN_US, text: 'test2' }\n      ]\n\n      const filtered = processLanguageData(mockData)\n      expect(filtered).toHaveLength(1)\n      expect(filtered[0].language).toBe(LanguageCode.ZH_CN)\n    })\n  })\n\n  describe('编译时类型检查', () => {\n    it('应该防止类型错误的赋值', () => {\n      // 这些测试确保TypeScript编译器能够捕获类型错误\n      \n      // 正确的类型赋值\n      const validLanguage: LanguageCode = LanguageCode.ZH_CN\n      const validRegister: RegisterLevel = RegisterLevel.FORMAL\n\n      expect(validLanguage).toBe(LanguageCode.ZH_CN)\n      expect(validRegister).toBe(RegisterLevel.FORMAL)\n      \n      // TypeScript应该在编译时捕获以下错误：\n      // const invalidLanguage: LanguageCode = 'invalid-lang' // 编译错误\n      // const invalidRegister: RegisterLevel = 'INVALID' // 编译错误\n    })\n\n    it('应该确保必需字段的存在', () => {\n      // TypeScript应该确保所有必需字段都存在\n      const partialMorpheme: Partial<Morpheme> = {\n        id: 'test',\n        text: '测试'\n        // 缺少其他必需字段\n      }\n\n      // 完整的morpheme应该包含所有必需字段\n      expect(partialMorpheme.id).toBe('test')\n      expect(partialMorpheme.text).toBe('测试')\n    })\n  })\n\n  describe('质量评估系统准确性测试', () => {\n    it('应该正确计算8维度质量评分', () => {\n      const qualityScore: QualityScore = {\n        overall: 0.85,\n        dimensions: {\n          creativity: 0.9,\n          memorability: 0.8,\n          cultural_fit: 0.9,\n          uniqueness: 0.7,\n          pronunciation: 0.8,\n          semantic_coherence: 0.8,\n          aesthetic_appeal: 0.8,\n          practical_usability: 0.8\n        },\n        confidence: 0.9,\n        evaluation_time: 150,\n        algorithm_version: 'v2.0',\n        issues: [],\n        suggestions: [],\n        timestamp: Date.now()\n      }\n\n      // 验证所有维度都在有效范围内\n      Object.values(qualityScore.dimensions).forEach(score => {\n        expect(score).toBeGreaterThanOrEqual(0)\n        expect(score).toBeLessThanOrEqual(1)\n      })\n\n      // 验证整体评分与各维度的一致性\n      const avgScore = Object.values(qualityScore.dimensions).reduce((sum, score) => sum + score, 0) / 8\n\n      // 整体评分应该接近各维度平均值\n      expect(Math.abs(qualityScore.overall - avgScore)).toBeLessThan(0.1)\n    })\n\n    it('应该验证多语种质量评分的一致性', () => {\n      const langQualityScores: LanguageQualityScores = {\n        naturalness: 0.9,\n        fluency: 0.85,\n        authenticity: 0.95,\n        aesthetic_appeal: 0.8,\n        pronunciation_ease: 0.75,\n        memorability: 0.9,\n        uniqueness: 0.6,\n        practicality: 0.85\n      }\n\n      // 验证所有评分都在有效范围内\n      Object.values(langQualityScores).forEach(score => {\n        expect(score).toBeGreaterThanOrEqual(0)\n        expect(score).toBeLessThanOrEqual(1)\n      })\n\n      // 验证评分的合理性\n      expect(langQualityScores.authenticity).toBeGreaterThan(0.8) // 真实性应该较高\n      expect(langQualityScores.naturalness).toBeGreaterThan(0.7) // 自然度应该较高\n    })\n  })\n})\n"], "version": 3}