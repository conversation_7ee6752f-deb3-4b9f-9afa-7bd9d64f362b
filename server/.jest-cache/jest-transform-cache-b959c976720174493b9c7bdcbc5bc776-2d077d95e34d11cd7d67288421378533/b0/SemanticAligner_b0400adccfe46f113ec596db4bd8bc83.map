{"version": 3, "names": ["LanguageCode", "SEMANTIC_VECTOR_DIMENSIONS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "constructor", "cov_s721x8a1", "b", "f", "s", "semanticSimilarityThreshold", "culturalFitWeight", "semanticWeight", "qualityWeight", "calculateConceptMorphemeAlignment", "concept", "morpheme", "semanticSimilarity", "calculateSemanticSimilarity", "semantic_vector", "culturalFit", "calculateCulturalFit", "qualityConsistency", "calculateQualityConsistency", "alignmentScore", "confidence", "calculateAlignmentConfidence", "sourceConceptId", "concept_id", "targetLanguage", "language", "alignedMorphemes", "conceptVector", "phonetic_features", "calculateCategoricalSimilarity", "adaptedConceptVector", "adaptConceptVector", "morphemeVector", "extractMorphemeSemanticVector", "calculateCosineSimilarity", "legacy_vector", "length", "LEGACY", "targetDim", "sourceDim", "vector", "adapted", "Array", "fill", "poolSize", "Math", "floor", "i", "sum", "start", "end", "min", "j", "vectorDim", "qualityScores", "language_quality_scores", "naturalness", "fluency", "authenticity", "aesthetic_appeal", "pronunciation_ease", "memorability", "uniqueness", "practicality", "cultural", "cultural_context", "traditionality", "modernity", "formality", "regionality", "religious_sensitivity", "phonetic_harmony", "syllable_count", "usage_frequency", "native_speaker_rating", "cultural_appropriateness", "popularity_trend", "getLanguageSpecificityScore", "scores", "ZH_CN", "EN_US", "JA_JP", "KO_KR", "ES_ES", "FR_FR", "DE_DE", "AR_SA", "vector1", "vector2", "dotProduct", "norm1", "norm2", "magnitude", "sqrt", "posMapping", "inferredCategory", "inferCategoryFromVector", "expectedPOS", "includes", "morphological_info", "pos_tag", "maxIndex", "indexOf", "max", "categories", "neutralityFit", "abs", "cultural_neutrality", "stabilityBonus", "cross_lingual_stability", "appropriateness", "conceptQuality", "cognitive_attributes", "morphemeQuality", "mean", "reduce", "a", "variance", "acc", "score", "pow", "findBestAlignment", "candidate<PERSON>or<PERSON><PERSON>", "bestAlignment", "bestScore", "alignment", "buildCrossLingualMapping", "languageMorphemes", "alignments", "Map", "morphemes", "set", "consistencyScore", "calculateCrossLingualConsistency", "bestAlignments", "findBestLanguagePairs", "conceptId", "from", "values", "map", "languages", "keys", "pairs", "lang1", "lang2", "alignment1", "get", "alignment2", "pairScore", "push", "language1", "language2", "sort", "updateConfig", "newConfig", "getConfig"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/SemanticAligner.ts"], "sourcesContent": ["/**\n * 语义对齐器\n * \n * 负责跨语言语义对齐、概念映射、语义相似度计算等功能\n * 确保多语种生成的语义一致性和文化适配性\n * \n * <AUTHOR> team\n * @version 3.0.0\n * @created 2025-06-24\n */\n\nimport { LanguageCode, SEMANTIC_VECTOR_DIMENSIONS } from '../../types/multilingual.js'\nimport type {\n  UniversalConcept,\n  LanguageSpecificMorpheme,\n  UniversalSemanticVector\n} from '../../types/multilingual.js'\n\n// ============================================================================\n// 语义对齐器接口\n// ============================================================================\n\nexport interface SemanticAlignment {\n  /** 源概念ID */\n  sourceConceptId: string\n  /** 目标语言 */\n  targetLanguage: LanguageCode\n  /** 对齐的语素列表 */\n  alignedMorphemes: LanguageSpecificMorpheme[]\n  /** 对齐分数 [0-1] */\n  alignmentScore: number\n  /** 语义相似度 [0-1] */\n  semanticSimilarity: number\n  /** 文化适配度 [0-1] */\n  culturalFit: number\n  /** 对齐置信度 [0-1] */\n  confidence: number\n}\n\nexport interface CrossLingualMapping {\n  /** 概念ID */\n  conceptId: string\n  /** 各语言的语素映射 */\n  languageMorphemes: Map<LanguageCode, LanguageSpecificMorpheme[]>\n  /** 跨语言一致性评分 [0-1] */\n  consistencyScore: number\n  /** 最佳对齐语言对 */\n  bestAlignments: Array<{\n    language1: LanguageCode\n    language2: LanguageCode\n    score: number\n  }>\n}\n\nexport interface SemanticAlignerConfig {\n  /** 语义相似度阈值 */\n  semanticSimilarityThreshold: number\n  /** 文化适配权重 */\n  culturalFitWeight: number\n  /** 语义权重 */\n  semanticWeight: number\n  /** 质量权重 */\n  qualityWeight: number\n}\n\n// ============================================================================\n// 语义对齐器类\n// ============================================================================\n\n/**\n * 语义对齐器类\n * \n * 提供跨语言语义对齐和概念映射功能\n */\nexport class SemanticAligner {\n  private config: SemanticAlignerConfig\n\n  /**\n   * 构造函数\n   */\n  constructor(config: Partial<SemanticAlignerConfig> = {}) {\n    this.config = {\n      semanticSimilarityThreshold: config.semanticSimilarityThreshold || 0.75,\n      culturalFitWeight: config.culturalFitWeight || 0.3,\n      semanticWeight: config.semanticWeight || 0.5,\n      qualityWeight: config.qualityWeight || 0.2\n    }\n  }\n\n  /**\n   * 计算概念与语素的语义对齐度\n   */\n  calculateConceptMorphemeAlignment(\n    concept: UniversalConcept,\n    morpheme: LanguageSpecificMorpheme\n  ): SemanticAlignment {\n    // 1. 语义相似度计算\n    const semanticSimilarity = this.calculateSemanticSimilarity(\n      concept.semantic_vector,\n      morpheme\n    )\n\n    // 2. 文化适配度计算\n    const culturalFit = this.calculateCulturalFit(concept, morpheme)\n\n    // 3. 质量一致性计算\n    const qualityConsistency = this.calculateQualityConsistency(concept, morpheme)\n\n    // 4. 综合对齐分数\n    const alignmentScore = \n      this.config.semanticWeight * semanticSimilarity +\n      this.config.culturalFitWeight * culturalFit +\n      this.config.qualityWeight * qualityConsistency\n\n    // 5. 计算置信度\n    const confidence = this.calculateAlignmentConfidence(\n      semanticSimilarity,\n      culturalFit,\n      qualityConsistency\n    )\n\n    return {\n      sourceConceptId: concept.concept_id,\n      targetLanguage: morpheme.language,\n      alignedMorphemes: [morpheme],\n      alignmentScore,\n      semanticSimilarity,\n      culturalFit,\n      confidence\n    }\n  }\n\n  /**\n   * 计算语义相似度\n   */\n  private calculateSemanticSimilarity(\n    conceptVector: UniversalSemanticVector,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // 如果语素没有语义向量，使用基于类别的相似度\n    if (!morpheme.phonetic_features) {\n      return this.calculateCategoricalSimilarity(conceptVector, morpheme)\n    }\n\n    // 获取适配的概念向量\n    const adaptedConceptVector = this.adaptConceptVector(conceptVector)\n    const morphemeVector = this.extractMorphemeSemanticVector(morpheme)\n\n    // 使用余弦相似度计算语义向量相似度\n    return this.calculateCosineSimilarity(adaptedConceptVector, morphemeVector)\n  }\n\n  /**\n   * 适配概念向量到兼容维度\n   */\n  private adaptConceptVector(conceptVector: UniversalSemanticVector): number[] {\n    // 如果有兼容性向量，直接使用\n    if (conceptVector.legacy_vector && conceptVector.legacy_vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY) {\n      return conceptVector.legacy_vector\n    }\n\n    // 否则从高维向量降维到兼容维度\n    const targetDim = SEMANTIC_VECTOR_DIMENSIONS.LEGACY\n    const sourceDim = conceptVector.vector.length\n\n    if (sourceDim === targetDim) {\n      return conceptVector.vector\n    }\n\n    // 简单的降维策略：平均池化\n    const adapted: number[] = new Array(targetDim).fill(0)\n    const poolSize = Math.floor(sourceDim / targetDim)\n\n    for (let i = 0; i < targetDim; i++) {\n      let sum = 0\n      const start = i * poolSize\n      const end = Math.min(start + poolSize, sourceDim)\n\n      for (let j = start; j < end; j++) {\n        sum += conceptVector.vector[j]\n      }\n\n      adapted[i] = sum / (end - start)\n    }\n\n    return adapted\n  }\n\n  /**\n   * 提取语素的语义向量\n   */\n  private extractMorphemeSemanticVector(morpheme: LanguageSpecificMorpheme): number[] {\n    // 基于语素的各种特征构建语义向量\n    const vectorDim = SEMANTIC_VECTOR_DIMENSIONS.LEGACY // 使用兼容性向量维度\n    const vector: number[] = new Array(vectorDim).fill(0)\n    \n    // 基于质量评分填充向量\n    const qualityScores = morpheme.language_quality_scores\n    vector[0] = qualityScores.naturalness\n    vector[1] = qualityScores.fluency\n    vector[2] = qualityScores.authenticity\n    vector[3] = qualityScores.aesthetic_appeal\n    vector[4] = qualityScores.pronunciation_ease\n    vector[5] = qualityScores.memorability\n    vector[6] = qualityScores.uniqueness\n    vector[7] = qualityScores.practicality\n    \n    // 基于文化语境填充向量\n    const cultural = morpheme.cultural_context\n    vector[8] = cultural.traditionality\n    vector[9] = cultural.modernity\n    vector[10] = cultural.formality\n    vector[11] = cultural.regionality\n    vector[12] = cultural.religious_sensitivity\n    \n    // 基于语音特征填充向量\n    vector[13] = morpheme.phonetic_features.phonetic_harmony\n    vector[14] = morpheme.phonetic_features.syllable_count / 5 // 归一化\n    \n    // 基于使用频率和评分填充向量\n    vector[15] = morpheme.usage_frequency\n    vector[16] = morpheme.native_speaker_rating\n    vector[17] = morpheme.cultural_appropriateness\n    vector[18] = morpheme.popularity_trend\n    \n    // 最后一个维度用于语言特异性\n    vector[19] = this.getLanguageSpecificityScore(morpheme.language)\n    \n    return vector\n  }\n\n  /**\n   * 获取语言特异性评分\n   */\n  private getLanguageSpecificityScore(language: LanguageCode): number {\n    const scores: Record<LanguageCode, number> = {\n      [LanguageCode.ZH_CN]: 0.9,\n      [LanguageCode.EN_US]: 0.8,\n      [LanguageCode.JA_JP]: 0.85,\n      [LanguageCode.KO_KR]: 0.85,\n      [LanguageCode.ES_ES]: 0.7,\n      [LanguageCode.FR_FR]: 0.7,\n      [LanguageCode.DE_DE]: 0.75,\n      [LanguageCode.AR_SA]: 0.9\n    }\n    return scores[language] || 0.5\n  }\n\n  /**\n   * 计算余弦相似度\n   */\n  private calculateCosineSimilarity(vector1: number[], vector2: number[]): number {\n    if (vector1.length !== vector2.length) {\n      return 0\n    }\n\n    let dotProduct = 0\n    let norm1 = 0\n    let norm2 = 0\n\n    for (let i = 0; i < vector1.length; i++) {\n      dotProduct += vector1[i] * vector2[i]\n      norm1 += vector1[i] * vector1[i]\n      norm2 += vector2[i] * vector2[i]\n    }\n\n    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)\n    return magnitude > 0 ? dotProduct / magnitude : 0\n  }\n\n  /**\n   * 基于类别的相似度计算\n   */\n  private calculateCategoricalSimilarity(\n    conceptVector: UniversalSemanticVector,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // 基于概念类别和语素的词性标注计算相似度\n    const posMapping: Record<string, string[]> = {\n      'emotions': ['ADJ', 'NOUN'],\n      'professions': ['NOUN'],\n      'characteristics': ['ADJ'],\n      'objects': ['NOUN'],\n      'actions': ['VERB'],\n      'concepts': ['NOUN', 'ADJ']\n    }\n\n    // 从概念向量推断类别（简化实现）\n    const inferredCategory = this.inferCategoryFromVector(conceptVector.vector)\n    const expectedPOS = posMapping[inferredCategory] || ['NOUN']\n    \n    return expectedPOS.includes(morpheme.morphological_info.pos_tag) ? 0.8 : 0.4\n  }\n\n  /**\n   * 从语义向量推断类别\n   */\n  private inferCategoryFromVector(vector: number[]): string {\n    // 简化的类别推断逻辑\n    const maxIndex = vector.indexOf(Math.max(...vector))\n    const categories = ['emotions', 'professions', 'characteristics', 'objects', 'actions', 'concepts']\n    return categories[maxIndex % categories.length] || 'concepts'\n  }\n\n  /**\n   * 计算文化适配度\n   */\n  private calculateCulturalFit(\n    concept: UniversalConcept,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // 1. 文化中性度匹配\n    const neutralityFit = 1 - Math.abs(concept.cultural_neutrality - 0.5) * 2\n\n    // 2. 跨语言稳定性\n    const stabilityBonus = concept.cross_lingual_stability * 0.3\n\n    // 3. 语素的文化适宜性\n    const appropriateness = morpheme.cultural_appropriateness\n\n    // 4. 综合文化适配度\n    return (neutralityFit * 0.4 + stabilityBonus + appropriateness * 0.3)\n  }\n\n  /**\n   * 计算质量一致性\n   */\n  private calculateQualityConsistency(\n    concept: UniversalConcept,\n    morpheme: LanguageSpecificMorpheme\n  ): number {\n    // 基于概念的认知属性和语素的质量评分计算一致性\n    const conceptQuality = concept.cognitive_attributes.memorability\n    const morphemeQuality = morpheme.native_speaker_rating\n    \n    // 质量分数的一致性（1 - 差值的绝对值）\n    return Math.max(0, 1 - Math.abs(conceptQuality - morphemeQuality))\n  }\n\n  /**\n   * 计算对齐置信度\n   */\n  private calculateAlignmentConfidence(\n    semanticSimilarity: number,\n    culturalFit: number,\n    qualityConsistency: number\n  ): number {\n    // 基于各项指标的方差计算置信度\n    const scores = [semanticSimilarity, culturalFit, qualityConsistency]\n    const mean = scores.reduce((a, b) => a + b, 0) / scores.length\n    const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length\n    \n    // 方差越小，置信度越高\n    return Math.max(0, 1 - variance)\n  }\n\n  /**\n   * 为概念找到最佳语言对齐\n   */\n  findBestAlignment(\n    concept: UniversalConcept,\n    candidateMorphemes: LanguageSpecificMorpheme[]\n  ): SemanticAlignment | null {\n    if (candidateMorphemes.length === 0) {\n      return null\n    }\n\n    let bestAlignment: SemanticAlignment | null = null\n    let bestScore = 0\n\n    for (const morpheme of candidateMorphemes) {\n      const alignment = this.calculateConceptMorphemeAlignment(concept, morpheme)\n      \n      if (alignment.alignmentScore > bestScore && \n          alignment.semanticSimilarity >= this.config.semanticSimilarityThreshold) {\n        bestScore = alignment.alignmentScore\n        bestAlignment = alignment\n      }\n    }\n\n    return bestAlignment\n  }\n\n  /**\n   * 构建跨语言映射\n   */\n  buildCrossLingualMapping(\n    concept: UniversalConcept,\n    languageMorphemes: Map<LanguageCode, LanguageSpecificMorpheme[]>\n  ): CrossLingualMapping {\n    const alignments = new Map<LanguageCode, SemanticAlignment>()\n    \n    // 为每种语言找到最佳对齐\n    for (const [language, morphemes] of languageMorphemes) {\n      const bestAlignment = this.findBestAlignment(concept, morphemes)\n      if (bestAlignment) {\n        alignments.set(language, bestAlignment)\n      }\n    }\n\n    // 计算跨语言一致性\n    const consistencyScore = this.calculateCrossLingualConsistency(alignments)\n\n    // 找到最佳对齐语言对\n    const bestAlignments = this.findBestLanguagePairs(alignments)\n\n    return {\n      conceptId: concept.concept_id,\n      languageMorphemes,\n      consistencyScore,\n      bestAlignments\n    }\n  }\n\n  /**\n   * 计算跨语言一致性\n   */\n  private calculateCrossLingualConsistency(\n    alignments: Map<LanguageCode, SemanticAlignment>\n  ): number {\n    const scores = Array.from(alignments.values()).map(a => a.alignmentScore)\n    if (scores.length < 2) return 1.0\n\n    const mean = scores.reduce((a, b) => a + b, 0) / scores.length\n    const variance = scores.reduce((acc, score) => acc + Math.pow(score - mean, 2), 0) / scores.length\n    \n    // 一致性 = 平均分数 * (1 - 标准化方差)\n    return mean * (1 - Math.min(variance, 1))\n  }\n\n  /**\n   * 找到最佳语言对齐对\n   */\n  private findBestLanguagePairs(\n    alignments: Map<LanguageCode, SemanticAlignment>\n  ): Array<{ language1: LanguageCode; language2: LanguageCode; score: number }> {\n    const languages = Array.from(alignments.keys())\n    const pairs: Array<{ language1: LanguageCode; language2: LanguageCode; score: number }> = []\n\n    for (let i = 0; i < languages.length; i++) {\n      for (let j = i + 1; j < languages.length; j++) {\n        const lang1 = languages[i]\n        const lang2 = languages[j]\n        const alignment1 = alignments.get(lang1)!\n        const alignment2 = alignments.get(lang2)!\n        \n        // 计算语言对的对齐分数\n        const pairScore = (alignment1.alignmentScore + alignment2.alignmentScore) / 2\n        pairs.push({ language1: lang1, language2: lang2, score: pairScore })\n      }\n    }\n\n    // 按分数降序排序\n    return pairs.sort((a, b) => b.score - a.score)\n  }\n\n  /**\n   * 更新配置\n   */\n  updateConfig(newConfig: Partial<SemanticAlignerConfig>): void {\n    this.config = { ...this.config, ...newConfig }\n  }\n\n  /**\n   * 获取当前配置\n   */\n  getConfig(): SemanticAlignerConfig {\n    return { ...this.config }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;AAWA,SAASA,YAAY,EAAEC,0BAA0B,QAAQ,6BAA6B;AAsDtF;AACA;AACA;AAEA;;;;;AAKA,OAAM,MAAOC,eAAe;EAClBC,MAAM;EAEd;;;EAGAC,YAAYD,MAAA;EAAA;EAAA,CAAAE,YAAA,GAAAC,CAAA,UAAyC,EAAE;IAAA;IAAAD,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IACrD,IAAI,CAACL,MAAM,GAAG;MACZM,2BAA2B;MAAE;MAAA,CAAAJ,YAAA,GAAAC,CAAA,UAAAH,MAAM,CAACM,2BAA2B;MAAA;MAAA,CAAAJ,YAAA,GAAAC,CAAA,UAAI,IAAI;MACvEI,iBAAiB;MAAE;MAAA,CAAAL,YAAA,GAAAC,CAAA,UAAAH,MAAM,CAACO,iBAAiB;MAAA;MAAA,CAAAL,YAAA,GAAAC,CAAA,UAAI,GAAG;MAClDK,cAAc;MAAE;MAAA,CAAAN,YAAA,GAAAC,CAAA,UAAAH,MAAM,CAACQ,cAAc;MAAA;MAAA,CAAAN,YAAA,GAAAC,CAAA,UAAI,GAAG;MAC5CM,aAAa;MAAE;MAAA,CAAAP,YAAA,GAAAC,CAAA,UAAAH,MAAM,CAACS,aAAa;MAAA;MAAA,CAAAP,YAAA,GAAAC,CAAA,UAAI,GAAG;KAC3C;EACH;EAEA;;;EAGAO,iCAAiCA,CAC/BC,OAAyB,EACzBC,QAAkC;IAAA;IAAAV,YAAA,GAAAE,CAAA;IAElC;IACA,MAAMS,kBAAkB;IAAA;IAAA,CAAAX,YAAA,GAAAG,CAAA,OAAG,IAAI,CAACS,2BAA2B,CACzDH,OAAO,CAACI,eAAe,EACvBH,QAAQ,CACT;IAED;IACA,MAAMI,WAAW;IAAA;IAAA,CAAAd,YAAA,GAAAG,CAAA,OAAG,IAAI,CAACY,oBAAoB,CAACN,OAAO,EAAEC,QAAQ,CAAC;IAEhE;IACA,MAAMM,kBAAkB;IAAA;IAAA,CAAAhB,YAAA,GAAAG,CAAA,OAAG,IAAI,CAACc,2BAA2B,CAACR,OAAO,EAAEC,QAAQ,CAAC;IAE9E;IACA,MAAMQ,cAAc;IAAA;IAAA,CAAAlB,YAAA,GAAAG,CAAA,OAClB,IAAI,CAACL,MAAM,CAACQ,cAAc,GAAGK,kBAAkB,GAC/C,IAAI,CAACb,MAAM,CAACO,iBAAiB,GAAGS,WAAW,GAC3C,IAAI,CAAChB,MAAM,CAACS,aAAa,GAAGS,kBAAkB;IAEhD;IACA,MAAMG,UAAU;IAAA;IAAA,CAAAnB,YAAA,GAAAG,CAAA,OAAG,IAAI,CAACiB,4BAA4B,CAClDT,kBAAkB,EAClBG,WAAW,EACXE,kBAAkB,CACnB;IAAA;IAAAhB,YAAA,GAAAG,CAAA;IAED,OAAO;MACLkB,eAAe,EAAEZ,OAAO,CAACa,UAAU;MACnCC,cAAc,EAAEb,QAAQ,CAACc,QAAQ;MACjCC,gBAAgB,EAAE,CAACf,QAAQ,CAAC;MAC5BQ,cAAc;MACdP,kBAAkB;MAClBG,WAAW;MACXK;KACD;EACH;EAEA;;;EAGQP,2BAA2BA,CACjCc,aAAsC,EACtChB,QAAkC;IAAA;IAAAV,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IAElC;IACA,IAAI,CAACO,QAAQ,CAACiB,iBAAiB,EAAE;MAAA;MAAA3B,YAAA,GAAAC,CAAA;MAAAD,YAAA,GAAAG,CAAA;MAC/B,OAAO,IAAI,CAACyB,8BAA8B,CAACF,aAAa,EAAEhB,QAAQ,CAAC;IACrE,CAAC;IAAA;IAAA;MAAAV,YAAA,GAAAC,CAAA;IAAA;IAED;IACA,MAAM4B,oBAAoB;IAAA;IAAA,CAAA7B,YAAA,GAAAG,CAAA,OAAG,IAAI,CAAC2B,kBAAkB,CAACJ,aAAa,CAAC;IACnE,MAAMK,cAAc;IAAA;IAAA,CAAA/B,YAAA,GAAAG,CAAA,QAAG,IAAI,CAAC6B,6BAA6B,CAACtB,QAAQ,CAAC;IAEnE;IAAA;IAAAV,YAAA,GAAAG,CAAA;IACA,OAAO,IAAI,CAAC8B,yBAAyB,CAACJ,oBAAoB,EAAEE,cAAc,CAAC;EAC7E;EAEA;;;EAGQD,kBAAkBA,CAACJ,aAAsC;IAAA;IAAA1B,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IAC/D;IACA;IAAI;IAAA,CAAAH,YAAA,GAAAC,CAAA,UAAAyB,aAAa,CAACQ,aAAa;IAAA;IAAA,CAAAlC,YAAA,GAAAC,CAAA,UAAIyB,aAAa,CAACQ,aAAa,CAACC,MAAM,KAAKvC,0BAA0B,CAACwC,MAAM,GAAE;MAAA;MAAApC,YAAA,GAAAC,CAAA;MAAAD,YAAA,GAAAG,CAAA;MAC3G,OAAOuB,aAAa,CAACQ,aAAa;IACpC,CAAC;IAAA;IAAA;MAAAlC,YAAA,GAAAC,CAAA;IAAA;IAED;IACA,MAAMoC,SAAS;IAAA;IAAA,CAAArC,YAAA,GAAAG,CAAA,QAAGP,0BAA0B,CAACwC,MAAM;IACnD,MAAME,SAAS;IAAA;IAAA,CAAAtC,YAAA,GAAAG,CAAA,QAAGuB,aAAa,CAACa,MAAM,CAACJ,MAAM;IAAA;IAAAnC,YAAA,GAAAG,CAAA;IAE7C,IAAImC,SAAS,KAAKD,SAAS,EAAE;MAAA;MAAArC,YAAA,GAAAC,CAAA;MAAAD,YAAA,GAAAG,CAAA;MAC3B,OAAOuB,aAAa,CAACa,MAAM;IAC7B,CAAC;IAAA;IAAA;MAAAvC,YAAA,GAAAC,CAAA;IAAA;IAED;IACA,MAAMuC,OAAO;IAAA;IAAA,CAAAxC,YAAA,GAAAG,CAAA,QAAa,IAAIsC,KAAK,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,CAAC,CAAC;IACtD,MAAMC,QAAQ;IAAA;IAAA,CAAA3C,YAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACC,KAAK,CAACP,SAAS,GAAGD,SAAS,CAAC;IAAA;IAAArC,YAAA,GAAAG,CAAA;IAElD,KAAK,IAAI2C,CAAC;IAAA;IAAA,CAAA9C,YAAA,GAAAG,CAAA,QAAG,CAAC,GAAE2C,CAAC,GAAGT,SAAS,EAAES,CAAC,EAAE,EAAE;MAClC,IAAIC,GAAG;MAAA;MAAA,CAAA/C,YAAA,GAAAG,CAAA,QAAG,CAAC;MACX,MAAM6C,KAAK;MAAA;MAAA,CAAAhD,YAAA,GAAAG,CAAA,QAAG2C,CAAC,GAAGH,QAAQ;MAC1B,MAAMM,GAAG;MAAA;MAAA,CAAAjD,YAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACM,GAAG,CAACF,KAAK,GAAGL,QAAQ,EAAEL,SAAS,CAAC;MAAA;MAAAtC,YAAA,GAAAG,CAAA;MAEjD,KAAK,IAAIgD,CAAC;MAAA;MAAA,CAAAnD,YAAA,GAAAG,CAAA,QAAG6C,KAAK,GAAEG,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAAA;QAAAnD,YAAA,GAAAG,CAAA;QAChC4C,GAAG,IAAIrB,aAAa,CAACa,MAAM,CAACY,CAAC,CAAC;MAChC;MAAC;MAAAnD,YAAA,GAAAG,CAAA;MAEDqC,OAAO,CAACM,CAAC,CAAC,GAAGC,GAAG,IAAIE,GAAG,GAAGD,KAAK,CAAC;IAClC;IAAC;IAAAhD,YAAA,GAAAG,CAAA;IAED,OAAOqC,OAAO;EAChB;EAEA;;;EAGQR,6BAA6BA,CAACtB,QAAkC;IAAA;IAAAV,YAAA,GAAAE,CAAA;IACtE;IACA,MAAMkD,SAAS;IAAA;IAAA,CAAApD,YAAA,GAAAG,CAAA,QAAGP,0BAA0B,CAACwC,MAAM,GAAC;IACpD,MAAMG,MAAM;IAAA;IAAA,CAAAvC,YAAA,GAAAG,CAAA,QAAa,IAAIsC,KAAK,CAACW,SAAS,CAAC,CAACV,IAAI,CAAC,CAAC,CAAC;IAErD;IACA,MAAMW,aAAa;IAAA;IAAA,CAAArD,YAAA,GAAAG,CAAA,QAAGO,QAAQ,CAAC4C,uBAAuB;IAAA;IAAAtD,YAAA,GAAAG,CAAA;IACtDoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACE,WAAW;IAAA;IAAAvD,YAAA,GAAAG,CAAA;IACrCoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACG,OAAO;IAAA;IAAAxD,YAAA,GAAAG,CAAA;IACjCoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACI,YAAY;IAAA;IAAAzD,YAAA,GAAAG,CAAA;IACtCoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACK,gBAAgB;IAAA;IAAA1D,YAAA,GAAAG,CAAA;IAC1CoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACM,kBAAkB;IAAA;IAAA3D,YAAA,GAAAG,CAAA;IAC5CoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACO,YAAY;IAAA;IAAA5D,YAAA,GAAAG,CAAA;IACtCoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACQ,UAAU;IAAA;IAAA7D,YAAA,GAAAG,CAAA;IACpCoC,MAAM,CAAC,CAAC,CAAC,GAAGc,aAAa,CAACS,YAAY;IAEtC;IACA,MAAMC,QAAQ;IAAA;IAAA,CAAA/D,YAAA,GAAAG,CAAA,QAAGO,QAAQ,CAACsD,gBAAgB;IAAA;IAAAhE,YAAA,GAAAG,CAAA;IAC1CoC,MAAM,CAAC,CAAC,CAAC,GAAGwB,QAAQ,CAACE,cAAc;IAAA;IAAAjE,YAAA,GAAAG,CAAA;IACnCoC,MAAM,CAAC,CAAC,CAAC,GAAGwB,QAAQ,CAACG,SAAS;IAAA;IAAAlE,YAAA,GAAAG,CAAA;IAC9BoC,MAAM,CAAC,EAAE,CAAC,GAAGwB,QAAQ,CAACI,SAAS;IAAA;IAAAnE,YAAA,GAAAG,CAAA;IAC/BoC,MAAM,CAAC,EAAE,CAAC,GAAGwB,QAAQ,CAACK,WAAW;IAAA;IAAApE,YAAA,GAAAG,CAAA;IACjCoC,MAAM,CAAC,EAAE,CAAC,GAAGwB,QAAQ,CAACM,qBAAqB;IAE3C;IAAA;IAAArE,YAAA,GAAAG,CAAA;IACAoC,MAAM,CAAC,EAAE,CAAC,GAAG7B,QAAQ,CAACiB,iBAAiB,CAAC2C,gBAAgB;IAAA;IAAAtE,YAAA,GAAAG,CAAA;IACxDoC,MAAM,CAAC,EAAE,CAAC,GAAG7B,QAAQ,CAACiB,iBAAiB,CAAC4C,cAAc,GAAG,CAAC,EAAC;IAE3D;IAAA;IAAAvE,YAAA,GAAAG,CAAA;IACAoC,MAAM,CAAC,EAAE,CAAC,GAAG7B,QAAQ,CAAC8D,eAAe;IAAA;IAAAxE,YAAA,GAAAG,CAAA;IACrCoC,MAAM,CAAC,EAAE,CAAC,GAAG7B,QAAQ,CAAC+D,qBAAqB;IAAA;IAAAzE,YAAA,GAAAG,CAAA;IAC3CoC,MAAM,CAAC,EAAE,CAAC,GAAG7B,QAAQ,CAACgE,wBAAwB;IAAA;IAAA1E,YAAA,GAAAG,CAAA;IAC9CoC,MAAM,CAAC,EAAE,CAAC,GAAG7B,QAAQ,CAACiE,gBAAgB;IAEtC;IAAA;IAAA3E,YAAA,GAAAG,CAAA;IACAoC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAACqC,2BAA2B,CAAClE,QAAQ,CAACc,QAAQ,CAAC;IAAA;IAAAxB,YAAA,GAAAG,CAAA;IAEhE,OAAOoC,MAAM;EACf;EAEA;;;EAGQqC,2BAA2BA,CAACpD,QAAsB;IAAA;IAAAxB,YAAA,GAAAE,CAAA;IACxD,MAAM2E,MAAM;IAAA;IAAA,CAAA7E,YAAA,GAAAG,CAAA,QAAiC;MAC3C,CAACR,YAAY,CAACmF,KAAK,GAAG,GAAG;MACzB,CAACnF,YAAY,CAACoF,KAAK,GAAG,GAAG;MACzB,CAACpF,YAAY,CAACqF,KAAK,GAAG,IAAI;MAC1B,CAACrF,YAAY,CAACsF,KAAK,GAAG,IAAI;MAC1B,CAACtF,YAAY,CAACuF,KAAK,GAAG,GAAG;MACzB,CAACvF,YAAY,CAACwF,KAAK,GAAG,GAAG;MACzB,CAACxF,YAAY,CAACyF,KAAK,GAAG,IAAI;MAC1B,CAACzF,YAAY,CAAC0F,KAAK,GAAG;KACvB;IAAA;IAAArF,YAAA,GAAAG,CAAA;IACD,OAAO,2BAAAH,YAAA,GAAAC,CAAA,UAAA4E,MAAM,CAACrD,QAAQ,CAAC;IAAA;IAAA,CAAAxB,YAAA,GAAAC,CAAA,UAAI,GAAG;EAChC;EAEA;;;EAGQgC,yBAAyBA,CAACqD,OAAiB,EAAEC,OAAiB;IAAA;IAAAvF,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IACpE,IAAImF,OAAO,CAACnD,MAAM,KAAKoD,OAAO,CAACpD,MAAM,EAAE;MAAA;MAAAnC,YAAA,GAAAC,CAAA;MAAAD,YAAA,GAAAG,CAAA;MACrC,OAAO,CAAC;IACV,CAAC;IAAA;IAAA;MAAAH,YAAA,GAAAC,CAAA;IAAA;IAED,IAAIuF,UAAU;IAAA;IAAA,CAAAxF,YAAA,GAAAG,CAAA,QAAG,CAAC;IAClB,IAAIsF,KAAK;IAAA;IAAA,CAAAzF,YAAA,GAAAG,CAAA,QAAG,CAAC;IACb,IAAIuF,KAAK;IAAA;IAAA,CAAA1F,YAAA,GAAAG,CAAA,QAAG,CAAC;IAAA;IAAAH,YAAA,GAAAG,CAAA;IAEb,KAAK,IAAI2C,CAAC;IAAA;IAAA,CAAA9C,YAAA,GAAAG,CAAA,QAAG,CAAC,GAAE2C,CAAC,GAAGwC,OAAO,CAACnD,MAAM,EAAEW,CAAC,EAAE,EAAE;MAAA;MAAA9C,YAAA,GAAAG,CAAA;MACvCqF,UAAU,IAAIF,OAAO,CAACxC,CAAC,CAAC,GAAGyC,OAAO,CAACzC,CAAC,CAAC;MAAA;MAAA9C,YAAA,GAAAG,CAAA;MACrCsF,KAAK,IAAIH,OAAO,CAACxC,CAAC,CAAC,GAAGwC,OAAO,CAACxC,CAAC,CAAC;MAAA;MAAA9C,YAAA,GAAAG,CAAA;MAChCuF,KAAK,IAAIH,OAAO,CAACzC,CAAC,CAAC,GAAGyC,OAAO,CAACzC,CAAC,CAAC;IAClC;IAEA,MAAM6C,SAAS;IAAA;IAAA,CAAA3F,YAAA,GAAAG,CAAA,QAAGyC,IAAI,CAACgD,IAAI,CAACH,KAAK,CAAC,GAAG7C,IAAI,CAACgD,IAAI,CAACF,KAAK,CAAC;IAAA;IAAA1F,YAAA,GAAAG,CAAA;IACrD,OAAOwF,SAAS,GAAG,CAAC;IAAA;IAAA,CAAA3F,YAAA,GAAAC,CAAA,WAAGuF,UAAU,GAAGG,SAAS;IAAA;IAAA,CAAA3F,YAAA,GAAAC,CAAA,WAAG,CAAC;EACnD;EAEA;;;EAGQ2B,8BAA8BA,CACpCF,aAAsC,EACtChB,QAAkC;IAAA;IAAAV,YAAA,GAAAE,CAAA;IAElC;IACA,MAAM2F,UAAU;IAAA;IAAA,CAAA7F,YAAA,GAAAG,CAAA,QAA6B;MAC3C,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MAC3B,aAAa,EAAE,CAAC,MAAM,CAAC;MACvB,iBAAiB,EAAE,CAAC,KAAK,CAAC;MAC1B,SAAS,EAAE,CAAC,MAAM,CAAC;MACnB,SAAS,EAAE,CAAC,MAAM,CAAC;MACnB,UAAU,EAAE,CAAC,MAAM,EAAE,KAAK;KAC3B;IAED;IACA,MAAM2F,gBAAgB;IAAA;IAAA,CAAA9F,YAAA,GAAAG,CAAA,QAAG,IAAI,CAAC4F,uBAAuB,CAACrE,aAAa,CAACa,MAAM,CAAC;IAC3E,MAAMyD,WAAW;IAAA;IAAA,CAAAhG,YAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,YAAA,GAAAC,CAAA,WAAA4F,UAAU,CAACC,gBAAgB,CAAC;IAAA;IAAA,CAAA9F,YAAA,GAAAC,CAAA,WAAI,CAAC,MAAM,CAAC;IAAA;IAAAD,YAAA,GAAAG,CAAA;IAE5D,OAAO6F,WAAW,CAACC,QAAQ,CAACvF,QAAQ,CAACwF,kBAAkB,CAACC,OAAO,CAAC;IAAA;IAAA,CAAAnG,YAAA,GAAAC,CAAA,WAAG,GAAG;IAAA;IAAA,CAAAD,YAAA,GAAAC,CAAA,WAAG,GAAG;EAC9E;EAEA;;;EAGQ8F,uBAAuBA,CAACxD,MAAgB;IAAA;IAAAvC,YAAA,GAAAE,CAAA;IAC9C;IACA,MAAMkG,QAAQ;IAAA;IAAA,CAAApG,YAAA,GAAAG,CAAA,QAAGoC,MAAM,CAAC8D,OAAO,CAACzD,IAAI,CAAC0D,GAAG,CAAC,GAAG/D,MAAM,CAAC,CAAC;IACpD,MAAMgE,UAAU;IAAA;IAAA,CAAAvG,YAAA,GAAAG,CAAA,QAAG,CAAC,UAAU,EAAE,aAAa,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAAA;IAAAH,YAAA,GAAAG,CAAA;IACnG,OAAO,2BAAAH,YAAA,GAAAC,CAAA,WAAAsG,UAAU,CAACH,QAAQ,GAAGG,UAAU,CAACpE,MAAM,CAAC;IAAA;IAAA,CAAAnC,YAAA,GAAAC,CAAA,WAAI,UAAU;EAC/D;EAEA;;;EAGQc,oBAAoBA,CAC1BN,OAAyB,EACzBC,QAAkC;IAAA;IAAAV,YAAA,GAAAE,CAAA;IAElC;IACA,MAAMsG,aAAa;IAAA;IAAA,CAAAxG,YAAA,GAAAG,CAAA,QAAG,CAAC,GAAGyC,IAAI,CAAC6D,GAAG,CAAChG,OAAO,CAACiG,mBAAmB,GAAG,GAAG,CAAC,GAAG,CAAC;IAEzE;IACA,MAAMC,cAAc;IAAA;IAAA,CAAA3G,YAAA,GAAAG,CAAA,QAAGM,OAAO,CAACmG,uBAAuB,GAAG,GAAG;IAE5D;IACA,MAAMC,eAAe;IAAA;IAAA,CAAA7G,YAAA,GAAAG,CAAA,QAAGO,QAAQ,CAACgE,wBAAwB;IAEzD;IAAA;IAAA1E,YAAA,GAAAG,CAAA;IACA,OAAQqG,aAAa,GAAG,GAAG,GAAGG,cAAc,GAAGE,eAAe,GAAG,GAAG;EACtE;EAEA;;;EAGQ5F,2BAA2BA,CACjCR,OAAyB,EACzBC,QAAkC;IAAA;IAAAV,YAAA,GAAAE,CAAA;IAElC;IACA,MAAM4G,cAAc;IAAA;IAAA,CAAA9G,YAAA,GAAAG,CAAA,QAAGM,OAAO,CAACsG,oBAAoB,CAACnD,YAAY;IAChE,MAAMoD,eAAe;IAAA;IAAA,CAAAhH,YAAA,GAAAG,CAAA,QAAGO,QAAQ,CAAC+D,qBAAqB;IAEtD;IAAA;IAAAzE,YAAA,GAAAG,CAAA;IACA,OAAOyC,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG1D,IAAI,CAAC6D,GAAG,CAACK,cAAc,GAAGE,eAAe,CAAC,CAAC;EACpE;EAEA;;;EAGQ5F,4BAA4BA,CAClCT,kBAA0B,EAC1BG,WAAmB,EACnBE,kBAA0B;IAAA;IAAAhB,YAAA,GAAAE,CAAA;IAE1B;IACA,MAAM2E,MAAM;IAAA;IAAA,CAAA7E,YAAA,GAAAG,CAAA,QAAG,CAACQ,kBAAkB,EAAEG,WAAW,EAAEE,kBAAkB,CAAC;IACpE,MAAMiG,IAAI;IAAA;IAAA,CAAAjH,YAAA,GAAAG,CAAA,QAAG0E,MAAM,CAACqC,MAAM,CAAC,CAACC,CAAC,EAAElH,CAAC,KAAK;MAAA;MAAAD,YAAA,GAAAE,CAAA;MAAAF,YAAA,GAAAG,CAAA;MAAA,OAAAgH,CAAC,GAAGlH,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAG4E,MAAM,CAAC1C,MAAM;IAC9D,MAAMiF,QAAQ;IAAA;IAAA,CAAApH,YAAA,GAAAG,CAAA,QAAG0E,MAAM,CAACqC,MAAM,CAAC,CAACG,GAAG,EAAEC,KAAK,KAAK;MAAA;MAAAtH,YAAA,GAAAE,CAAA;MAAAF,YAAA,GAAAG,CAAA;MAAA,OAAAkH,GAAG,GAAGzE,IAAI,CAAC2E,GAAG,CAACD,KAAK,GAAGL,IAAI,EAAE,CAAC,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGpC,MAAM,CAAC1C,MAAM;IAElG;IAAA;IAAAnC,YAAA,GAAAG,CAAA;IACA,OAAOyC,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGc,QAAQ,CAAC;EAClC;EAEA;;;EAGAI,iBAAiBA,CACf/G,OAAyB,EACzBgH,kBAA8C;IAAA;IAAAzH,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IAE9C,IAAIsH,kBAAkB,CAACtF,MAAM,KAAK,CAAC,EAAE;MAAA;MAAAnC,YAAA,GAAAC,CAAA;MAAAD,YAAA,GAAAG,CAAA;MACnC,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAH,YAAA,GAAAC,CAAA;IAAA;IAED,IAAIyH,aAAa;IAAA;IAAA,CAAA1H,YAAA,GAAAG,CAAA,QAA6B,IAAI;IAClD,IAAIwH,SAAS;IAAA;IAAA,CAAA3H,YAAA,GAAAG,CAAA,QAAG,CAAC;IAAA;IAAAH,YAAA,GAAAG,CAAA;IAEjB,KAAK,MAAMO,QAAQ,IAAI+G,kBAAkB,EAAE;MACzC,MAAMG,SAAS;MAAA;MAAA,CAAA5H,YAAA,GAAAG,CAAA,QAAG,IAAI,CAACK,iCAAiC,CAACC,OAAO,EAAEC,QAAQ,CAAC;MAAA;MAAAV,YAAA,GAAAG,CAAA;MAE3E;MAAI;MAAA,CAAAH,YAAA,GAAAC,CAAA,WAAA2H,SAAS,CAAC1G,cAAc,GAAGyG,SAAS;MAAA;MAAA,CAAA3H,YAAA,GAAAC,CAAA,WACpC2H,SAAS,CAACjH,kBAAkB,IAAI,IAAI,CAACb,MAAM,CAACM,2BAA2B,GAAE;QAAA;QAAAJ,YAAA,GAAAC,CAAA;QAAAD,YAAA,GAAAG,CAAA;QAC3EwH,SAAS,GAAGC,SAAS,CAAC1G,cAAc;QAAA;QAAAlB,YAAA,GAAAG,CAAA;QACpCuH,aAAa,GAAGE,SAAS;MAC3B,CAAC;MAAA;MAAA;QAAA5H,YAAA,GAAAC,CAAA;MAAA;IACH;IAAC;IAAAD,YAAA,GAAAG,CAAA;IAED,OAAOuH,aAAa;EACtB;EAEA;;;EAGAG,wBAAwBA,CACtBpH,OAAyB,EACzBqH,iBAAgE;IAAA;IAAA9H,YAAA,GAAAE,CAAA;IAEhE,MAAM6H,UAAU;IAAA;IAAA,CAAA/H,YAAA,GAAAG,CAAA,QAAG,IAAI6H,GAAG,EAAmC;IAE7D;IAAA;IAAAhI,YAAA,GAAAG,CAAA;IACA,KAAK,MAAM,CAACqB,QAAQ,EAAEyG,SAAS,CAAC,IAAIH,iBAAiB,EAAE;MACrD,MAAMJ,aAAa;MAAA;MAAA,CAAA1H,YAAA,GAAAG,CAAA,SAAG,IAAI,CAACqH,iBAAiB,CAAC/G,OAAO,EAAEwH,SAAS,CAAC;MAAA;MAAAjI,YAAA,GAAAG,CAAA;MAChE,IAAIuH,aAAa,EAAE;QAAA;QAAA1H,YAAA,GAAAC,CAAA;QAAAD,YAAA,GAAAG,CAAA;QACjB4H,UAAU,CAACG,GAAG,CAAC1G,QAAQ,EAAEkG,aAAa,CAAC;MACzC,CAAC;MAAA;MAAA;QAAA1H,YAAA,GAAAC,CAAA;MAAA;IACH;IAEA;IACA,MAAMkI,gBAAgB;IAAA;IAAA,CAAAnI,YAAA,GAAAG,CAAA,SAAG,IAAI,CAACiI,gCAAgC,CAACL,UAAU,CAAC;IAE1E;IACA,MAAMM,cAAc;IAAA;IAAA,CAAArI,YAAA,GAAAG,CAAA,SAAG,IAAI,CAACmI,qBAAqB,CAACP,UAAU,CAAC;IAAA;IAAA/H,YAAA,GAAAG,CAAA;IAE7D,OAAO;MACLoI,SAAS,EAAE9H,OAAO,CAACa,UAAU;MAC7BwG,iBAAiB;MACjBK,gBAAgB;MAChBE;KACD;EACH;EAEA;;;EAGQD,gCAAgCA,CACtCL,UAAgD;IAAA;IAAA/H,YAAA,GAAAE,CAAA;IAEhD,MAAM2E,MAAM;IAAA;IAAA,CAAA7E,YAAA,GAAAG,CAAA,SAAGsC,KAAK,CAAC+F,IAAI,CAACT,UAAU,CAACU,MAAM,EAAE,CAAC,CAACC,GAAG,CAACvB,CAAC,IAAI;MAAA;MAAAnH,YAAA,GAAAE,CAAA;MAAAF,YAAA,GAAAG,CAAA;MAAA,OAAAgH,CAAC,CAACjG,cAAc;IAAd,CAAc,CAAC;IAAA;IAAAlB,YAAA,GAAAG,CAAA;IACzE,IAAI0E,MAAM,CAAC1C,MAAM,GAAG,CAAC,EAAE;MAAA;MAAAnC,YAAA,GAAAC,CAAA;MAAAD,YAAA,GAAAG,CAAA;MAAA,OAAO,GAAG;IAAA;IAAA;IAAA;MAAAH,YAAA,GAAAC,CAAA;IAAA;IAEjC,MAAMgH,IAAI;IAAA;IAAA,CAAAjH,YAAA,GAAAG,CAAA,SAAG0E,MAAM,CAACqC,MAAM,CAAC,CAACC,CAAC,EAAElH,CAAC,KAAK;MAAA;MAAAD,YAAA,GAAAE,CAAA;MAAAF,YAAA,GAAAG,CAAA;MAAA,OAAAgH,CAAC,GAAGlH,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAG4E,MAAM,CAAC1C,MAAM;IAC9D,MAAMiF,QAAQ;IAAA;IAAA,CAAApH,YAAA,GAAAG,CAAA,SAAG0E,MAAM,CAACqC,MAAM,CAAC,CAACG,GAAG,EAAEC,KAAK,KAAK;MAAA;MAAAtH,YAAA,GAAAE,CAAA;MAAAF,YAAA,GAAAG,CAAA;MAAA,OAAAkH,GAAG,GAAGzE,IAAI,CAAC2E,GAAG,CAACD,KAAK,GAAGL,IAAI,EAAE,CAAC,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGpC,MAAM,CAAC1C,MAAM;IAElG;IAAA;IAAAnC,YAAA,GAAAG,CAAA;IACA,OAAO8G,IAAI,IAAI,CAAC,GAAGrE,IAAI,CAACM,GAAG,CAACkE,QAAQ,EAAE,CAAC,CAAC,CAAC;EAC3C;EAEA;;;EAGQkB,qBAAqBA,CAC3BP,UAAgD;IAAA;IAAA/H,YAAA,GAAAE,CAAA;IAEhD,MAAMyI,SAAS;IAAA;IAAA,CAAA3I,YAAA,GAAAG,CAAA,SAAGsC,KAAK,CAAC+F,IAAI,CAACT,UAAU,CAACa,IAAI,EAAE,CAAC;IAC/C,MAAMC,KAAK;IAAA;IAAA,CAAA7I,YAAA,GAAAG,CAAA,SAA+E,EAAE;IAAA;IAAAH,YAAA,GAAAG,CAAA;IAE5F,KAAK,IAAI2C,CAAC;IAAA;IAAA,CAAA9C,YAAA,GAAAG,CAAA,SAAG,CAAC,GAAE2C,CAAC,GAAG6F,SAAS,CAACxG,MAAM,EAAEW,CAAC,EAAE,EAAE;MAAA;MAAA9C,YAAA,GAAAG,CAAA;MACzC,KAAK,IAAIgD,CAAC;MAAA;MAAA,CAAAnD,YAAA,GAAAG,CAAA,SAAG2C,CAAC,GAAG,CAAC,GAAEK,CAAC,GAAGwF,SAAS,CAACxG,MAAM,EAAEgB,CAAC,EAAE,EAAE;QAC7C,MAAM2F,KAAK;QAAA;QAAA,CAAA9I,YAAA,GAAAG,CAAA,SAAGwI,SAAS,CAAC7F,CAAC,CAAC;QAC1B,MAAMiG,KAAK;QAAA;QAAA,CAAA/I,YAAA,GAAAG,CAAA,SAAGwI,SAAS,CAACxF,CAAC,CAAC;QAC1B,MAAM6F,UAAU;QAAA;QAAA,CAAAhJ,YAAA,GAAAG,CAAA,SAAG4H,UAAU,CAACkB,GAAG,CAACH,KAAK,CAAE;QACzC,MAAMI,UAAU;QAAA;QAAA,CAAAlJ,YAAA,GAAAG,CAAA,SAAG4H,UAAU,CAACkB,GAAG,CAACF,KAAK,CAAE;QAEzC;QACA,MAAMI,SAAS;QAAA;QAAA,CAAAnJ,YAAA,GAAAG,CAAA,SAAG,CAAC6I,UAAU,CAAC9H,cAAc,GAAGgI,UAAU,CAAChI,cAAc,IAAI,CAAC;QAAA;QAAAlB,YAAA,GAAAG,CAAA;QAC7E0I,KAAK,CAACO,IAAI,CAAC;UAAEC,SAAS,EAAEP,KAAK;UAAEQ,SAAS,EAAEP,KAAK;UAAEzB,KAAK,EAAE6B;QAAS,CAAE,CAAC;MACtE;IACF;IAEA;IAAA;IAAAnJ,YAAA,GAAAG,CAAA;IACA,OAAO0I,KAAK,CAACU,IAAI,CAAC,CAACpC,CAAC,EAAElH,CAAC,KAAK;MAAA;MAAAD,YAAA,GAAAE,CAAA;MAAAF,YAAA,GAAAG,CAAA;MAAA,OAAAF,CAAC,CAACqH,KAAK,GAAGH,CAAC,CAACG,KAAK;IAAL,CAAK,CAAC;EAChD;EAEA;;;EAGAkC,YAAYA,CAACC,SAAyC;IAAA;IAAAzJ,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IACpD,IAAI,CAACL,MAAM,GAAG;MAAE,GAAG,IAAI,CAACA,MAAM;MAAE,GAAG2J;IAAS,CAAE;EAChD;EAEA;;;EAGAC,SAASA,CAAA;IAAA;IAAA1J,YAAA,GAAAE,CAAA;IAAAF,YAAA,GAAAG,CAAA;IACP,OAAO;MAAE,GAAG,IAAI,CAACL;IAAM,CAAE;EAC3B", "ignoreList": []}