{"version": 3, "names": ["cov_2lhtm2izxv", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "isValidSemanticVector", "getSemanticVectorVersion", "multilingual_1", "require", "vector", "length", "SEMANTIC_VECTOR_DIMENSIONS", "LEGACY", "MULTILINGUAL", "MorphemeCategory", "CulturalContext", "PatternArchetype"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/types/core.ts"], "sourcesContent": ["/**\n * namer-v6 核心数据类型定义\n *\n * 本文件定义了namer-v6项目的核心数据结构和接口，\n * 支持多语言扩展、智能语义分析和基于第一性原理的创意模式。\n * v3.0版本：支持多语种架构，概念-语言分离设计\n *\n * @fileoverview 核心数据类型定义 (v3.0兼容)\n * @version 3.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type { MorphemeVersion } from \"./common\"\nimport type {\n  LanguageCode,\n  ConceptCategory,\n  MultiDimensionalCulturalContext\n} from \"./multilingual\"\nimport { SEMANTIC_VECTOR_DIMENSIONS } from \"./multilingual\"\n\n// ============================================================================\n// v3.0兼容性工具函数\n// ============================================================================\n\n/**\n * 验证语义向量维度是否符合v3.0标准\n * @param vector 语义向量\n * @returns 是否为有效的v3.0向量\n */\nexport function isValidSemanticVector(vector: number[]): boolean {\n  return vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY ||\n         vector.length === SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL\n}\n\n/**\n * 获取语义向量的版本类型\n * @param vector 语义向量\n * @returns 向量版本类型\n */\nexport function getSemanticVectorVersion(vector: number[]): 'v2.0' | 'v3.0' | 'unknown' {\n  if (vector.length === SEMANTIC_VECTOR_DIMENSIONS.LEGACY) return 'v2.0'\n  if (vector.length === SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL) return 'v3.0'\n  return 'unknown'\n}\n\n// ============================================================================\n// 基础枚举定义\n// ============================================================================\n\n/**\n * 语素类别枚举\n *\n * 定义了语素的主要分类，用于语义组织和检索优化。\n * 基于语言学和认知心理学原理进行分类。\n */\nexport enum MorphemeCategory {\n  /** 情感类语素 - 表达情感状态、感受和情绪 */\n  EMOTIONS = 'emotions',\n  /** 职业类语素 - 表达职业身份、专业领域和工作角色 */\n  PROFESSIONS = 'professions',\n  /** 特征类语素 - 表达个性特征、能力属性和品质特点 */\n  CHARACTERISTICS = 'characteristics',\n  /** 物体类语素 - 表达具体或抽象的事物、对象 */\n  OBJECTS = 'objects',\n  /** 动作类语素 - 表达行为、动作状态和活动 */\n  ACTIONS = 'actions',\n  /** 概念类语素 - 表达抽象概念、理念和思想 */\n  CONCEPTS = 'concepts'\n}\n\n/**\n * 文化语境枚举\n *\n * 定义了语素的文化背景属性，用于文化适配和风格匹配。\n * 基于文化人类学和社会心理学理论进行分类。\n */\nexport enum CulturalContext {\n  /** 古典文化语境 - 传统、典雅、深厚的文化特色 */\n  ANCIENT = 'ancient',\n  /** 现代文化语境 - 时尚、创新、国际化的文化特色 */\n  MODERN = 'modern',\n  /** 中性文化语境 - 通用、平衡、包容的文化特色 */\n  NEUTRAL = 'neutral'\n}\n\n// ============================================================================\n// 语素相关接口定义\n// ============================================================================\n\n/**\n * 语素质量指标接口\n *\n * 定义了语素质量评估的各项指标，用于智能筛选和排序\n */\nexport interface MorphemeQualityMetrics {\n  /** 自然度评分 [0-1] - 语素在语言中的自然程度 */\n  naturalness: number\n  /** 使用频率 [0-1] - 语素在实际使用中的频率 */\n  frequency: number\n  /** 可接受度 [0-1] - 用户对语素的接受程度 */\n  acceptability: number\n  /** 美学吸引力 [0-1] - 语素的美学价值和吸引力 */\n  aesthetic_appeal: number\n}\n\n/**\n * 语素语言属性接口 (v3.0扩展)\n *\n * 定义了语素的语言学特征，用于语音分析和组合优化。\n * v3.0版本支持国际音标和多语种语言学特征。\n */\nexport interface MorphemeLanguageProperties {\n  /** 音节数量 - 用于音韵分析 */\n  syllable_count: number\n  /** 字符数量 - 用于长度控制 */\n  character_count: number\n  /** 语音特征标签 - 用于音韵和谐性检查 */\n  phonetic_features: string[]\n  /** 词法类型 - 词性和语法功能 */\n  morphological_type: string\n  /** 发音标记 - 拼音或音标 */\n  pronunciation?: string\n  /** IPA国际音标 (v3.0新增) */\n  ipa_transcription?: string\n  /** 重音模式 (v3.0新增，用于英文等语言) */\n  stress_pattern?: string\n  /** 声调信息 (v3.0新增，用于中文等声调语言) */\n  tone_pattern?: string[]\n  /** 语音和谐性评分 [0-1] (v3.0新增) */\n  phonetic_harmony?: number\n}\n\n/**\n * 语素数据结构 (v3.0兼容)\n *\n * 核心语素数据结构，包含语义、文化、质量等多维度属性。\n * 支持多语言扩展和智能分析。v3.0版本支持概念-语言分离架构。\n */\nexport interface Morpheme {\n  /** 语素唯一标识符，格式：{category}_{序号} */\n  id: string\n  /** 对应的通用概念ID (v3.0新增) */\n  concept_id?: string\n  /** 语言代码 (v3.0新增，用于多语种支持) */\n  language?: LanguageCode\n  /** 语素文本内容 */\n  text: string\n  /** 语素主分类 */\n  category: MorphemeCategory\n  /** 语素子分类，用于更精细的分类管理 */\n  subcategory: string\n  /** 文化语境属性 (支持传统枚举和多维度对象) */\n  cultural_context: CulturalContext | MultiDimensionalCulturalContext\n  /** 使用频率 [0-1]，影响采样权重 */\n  usage_frequency: number\n  /** 综合质量评分 [0-1] */\n  quality_score: number\n  /** 语义向量，支持20维(v2.0)和512维(v3.0) */\n  semantic_vector: number[]\n  /** 语义标签，支持多维度检索和分类 */\n  tags: string[]\n  /** 语言属性 */\n  language_properties: MorphemeLanguageProperties\n  /** 质量指标 */\n  quality_metrics: MorphemeQualityMetrics\n  /** 创建时间戳 */\n  created_at: number\n  /** 数据来源标识 */\n  source: string\n  /** 数据版本，用于版本管理和兼容性检查 */\n  version: MorphemeVersion\n  /** 源文件 (用于数据重组脚本) */\n  _source_file?: string\n}\n\n// ============================================================================\n// 创意模式相关接口定义 (基于第一性原理设计)\n// ============================================================================\n\n/**\n * 模式原型枚举\n *\n * 基于第一性原理和用户心理需求分析定义的8种创意模式原型\n */\nexport enum PatternArchetype {\n  /** 专业型 - 强调能力和专业性，满足身份表达需求 */\n  PROFESSIONAL = 'professional',\n  /** 艺术型 - 强调创意和美感，满足美学追求需求 */\n  ARTISTIC = 'artistic',\n  /** 幽默型 - 强调趣味和亲和力，满足社交润滑需求 */\n  HUMOROUS = 'humorous',\n  /** 优雅型 - 强调品味和气质，满足品味展示需求 */\n  ELEGANT = 'elegant',\n  /** 创新型 - 强调前瞻和独特性，满足独特性需求 */\n  INNOVATIVE = 'innovative',\n  /** 传统型 - 强调文化和底蕴，满足文化认同需求 */\n  TRADITIONAL = 'traditional',\n  /** 简约型 - 强调简洁和纯粹，满足认知便利需求 */\n  MINIMALIST = 'minimalist',\n  /** 表达型 - 强调个性和情感，满足情感共鸣需求 */\n  EXPRESSIVE = 'expressive'\n}\n\n/**\n * 心理需求映射接口\n *\n * 定义了创意模式与用户心理需求的映射关系\n */\nexport interface PsychologicalNeeds {\n  /** 身份表达需求 [0-1] - 通过用户名表达个人身份和角色 */\n  identity_expression: number\n  /** 社交归属需求 [0-1] - 通过用户名获得群体认同和归属感 */\n  social_belonging: number\n  /** 美学愉悦需求 [0-1] - 通过用户名获得美学享受和愉悦感 */\n  aesthetic_pleasure: number\n  /** 认知便利需求 [0-1] - 用户名易于理解、记忆和传播 */\n  cognitive_ease: number\n  /** 情感共鸣需求 [0-1] - 通过用户名产生情感连接和共鸣 */\n  emotional_resonance: number\n}\n\n/**\n * 使用场景适配接口\n *\n * 定义了创意模式在不同使用场景下的适用度\n */\nexport interface UsageScenarios {\n  /** 专业场景适用度 [0-1] - 工作、商务等正式场合 */\n  professional: number\n  /** 社交场景适用度 [0-1] - 社交媒体、朋友圈等社交场合 */\n  social: number\n  /** 创意场景适用度 [0-1] - 艺术创作、设计等创意场合 */\n  creative: number\n  /** 正式场景适用度 [0-1] - 官方、学术等正式场合 */\n  formal: number\n  /** 休闲场景适用度 [0-1] - 游戏、娱乐等休闲场合 */\n  casual: number\n}\n\n/**\n * 文化共鸣接口\n *\n * 定义了创意模式与不同文化背景的共鸣程度\n */\nexport interface CulturalResonance {\n  /** 传统文化共鸣 [0-1] - 与传统文化的契合度 */\n  traditional: number\n  /** 现代文化共鸣 [0-1] - 与现代文化的契合度 */\n  modern: number\n  /** 国际化适应 [0-1] - 跨文化理解和接受度 */\n  international: number\n  /** 本土化特色 [0-1] - 本土文化特色和认同度 */\n  local: number\n}\n\n/**\n * 创意模式心理学属性接口\n *\n * 定义了创意模式的心理学特征和适用性\n */\nexport interface CreativePatternPsychology {\n  /** 心理需求映射 */\n  psychological_needs: PsychologicalNeeds\n  /** 适用场景 */\n  usage_scenarios: UsageScenarios\n  /** 文化共鸣 */\n  cultural_resonance: CulturalResonance\n}\n\n/**\n * 模式规则接口\n *\n * 定义了创意模式的生成规则和约束条件\n */\nexport interface PatternRule {\n  /** 执行步骤序号 */\n  step: number\n  /** 规则动作类型 */\n  action: 'select' | 'combine' | 'transform' | 'validate'\n  /** 规则参数 */\n  parameters: Record<string, any>\n  /** 失败回退规则 */\n  fallback?: PatternRule\n  /** 规则权重 */\n  weight: number\n  /** 规则描述 */\n  description?: string\n}\n\n/**\n * 创意模式数据结构\n *\n * 基于第一性原理设计的创意模式完整定义，\n * 包含心理学基础、生成规则和效果评估\n */\nexport interface CreativePattern {\n  /** 模式唯一标识符 */\n  id: string\n  /** 模式名称 */\n  name: string\n  /** 模式描述 */\n  description: string\n  /** 模式原型 */\n  archetype: PatternArchetype\n  /** 全局权重 [0-1] */\n  weight: number\n  /** 心理学属性 */\n  psychology: CreativePatternPsychology\n  /** 生成规则集合 */\n  rules: PatternRule[]\n  /** 约束条件 */\n  constraints: CompatibilityRule[]\n  /** 效果评分 [0-1] */\n  effectiveness_score: number\n  /** 用户偏好评分 [0-1] */\n  user_preference_score: number\n  /** 生成示例 */\n  examples: string[]\n  /** 反例（避免生成的类型） */\n  anti_examples: string[]\n  /** 创建时间戳 */\n  created_at: number\n  /** 模式版本 */\n  version: string\n  /** 模式代数（进化代数） */\n  generation?: number\n  /** 父模式ID（用于进化追踪） */\n  parents?: string[]\n}\n\n/**\n * 语素数量约束\n */\ninterface MorphemeCountConstraint {\n  min?: number\n  max?: number\n  exact?: number\n}\n\n/**\n * 类别要求约束\n */\ninterface CategoryRequiredConstraint {\n  category: MorphemeCategory\n  min_count?: number\n  max_count?: number\n  exact_count?: number\n}\n\n/**\n * 文化匹配约束\n */\ninterface CulturalMatchConstraint {\n  context: CulturalContext | CulturalContext[]\n  required: boolean\n}\n\n/**\n * 质量阈值约束\n */\ninterface QualityThresholdConstraint {\n  min_score: number\n  max_score?: number\n}\n\n/**\n * 模式规则\n */\nexport interface PatternRule {\n  type: 'morpheme_count' | 'category_required' | 'cultural_match' | 'quality_threshold'\n  constraint: MorphemeCountConstraint | CategoryRequiredConstraint | CulturalMatchConstraint | QualityThresholdConstraint\n  weight: number\n}\n\n// ============================================================================\n// 质量评估相关接口定义 (8维度评估体系)\n// ============================================================================\n\n/**\n * 质量评估维度接口\n *\n * 基于认知心理学和语言学理论定义的8维度质量评估体系\n */\nexport interface QualityDimensions {\n  /** 创意性 [0-1] - 用户名的创新性和独特性 */\n  creativity: number\n  /** 记忆性 [0-1] - 用户名的易记性和传播性 */\n  memorability: number\n  /** 文化适配度 [0-1] - 与目标文化的契合程度 */\n  cultural_fit: number\n  /** 独特性 [0-1] - 用户名的稀有性和区分度 */\n  uniqueness: number\n  /** 发音友好度 [0-1] - 用户名的发音难易程度 */\n  pronunciation: number\n  /** 语义连贯性 [0-1] - 语素组合的语义逻辑性 */\n  semantic_coherence: number\n  /** 美学吸引力 [0-1] - 用户名的美学价值和吸引力 */\n  aesthetic_appeal: number\n  /** 实用性 [0-1] - 用户名在实际使用中的便利性 */\n  practical_usability: number\n}\n\n/**\n * 质量评分结构\n *\n * 完整的质量评估结果，包含综合评分、分维度评分和改进建议\n */\nexport interface QualityScore {\n  /** 综合质量评分 [0-1] */\n  overall: number\n  /** 分维度评分 */\n  dimensions: QualityDimensions\n  /** 评估置信度 [0-1] */\n  confidence: number\n  /** 评估耗时 (毫秒) */\n  evaluation_time: number\n  /** 算法版本 */\n  algorithm_version: string\n  /** 发现的问题 */\n  issues: string[]\n  /** 改进建议 */\n  suggestions: string[]\n  /** 评估时间戳 */\n  timestamp: number\n}\n\n/**\n * 生成的用户名结构\n */\nexport interface GeneratedUsername {\n  text: string\n  pattern: string\n  quality_score: QualityScore\n  explanation: string\n  components: MorphemeComponent[]\n  metadata: {\n    cultural_fit: number\n    creativity: number\n    memorability: number\n    uniqueness: number\n    generation_time: number\n  }\n}\n\n/**\n * 语素组件\n */\nexport interface MorphemeComponent {\n  morpheme: Morpheme\n  position: number\n  role: 'prefix' | 'root' | 'suffix' | 'modifier' | 'complement'\n  contribution_score: number\n}\n\n/**\n * 兼容性规则\n */\nexport interface CompatibilityRule {\n  id: string\n  name: string\n  description: string\n  type: 'category_combination' | 'cultural_harmony' | 'semantic_coherence' | 'phonetic_beauty'\n  conditions: CompatibilityCondition[]\n  score_modifier: number\n  is_blocking: boolean\n}\n\n/**\n * 兼容性条件值类型\n */\ntype CompatibilityConditionValue = \n  | string \n  | number \n  | boolean \n  | string[] \n  | number[]\n  | MorphemeCategory \n  | CulturalContext\n  | null\n  | undefined\n\n/**\n * 兼容性条件\n */\nexport interface CompatibilityCondition {\n  field: string\n  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than'\n  value: CompatibilityConditionValue\n  weight: number\n}\n\n/**\n * 生成上下文 (v3.0多语种支持)\n */\nexport interface GenerationContext {\n  user_preferences?: UserPreferences\n  cultural_preference: CulturalContext\n  style_preference: StylePreference\n  creativity_level: number\n  quality_threshold: number\n  patterns?: string[]\n  exclude_patterns?: string[]\n  context_keywords?: string[]\n\n  // v3.0多语种新增字段\n  target_language?: LanguageCode\n  concept_preferences?: {\n    categories?: ConceptCategory[]\n    cultural_context?: CulturalContext | MultiDimensionalCulturalContext\n    abstraction_level?: number\n  }\n  multilingual_options?: {\n    enable_cross_lingual?: boolean\n    fallback_languages?: LanguageCode[]\n    maintain_semantic_consistency?: boolean\n    translation_preference?: 'literal' | 'semantic' | 'cultural'\n  }\n  cultural_sensitivity?: number\n}\n\n/**\n * 用户偏好\n */\nexport interface UserPreferences {\n  favorite_patterns: string[]\n  cultural_preference: CulturalContext\n  style_preference: StylePreference\n  creativity_level: number\n  quality_threshold: number\n  excluded_categories: MorphemeCategory[]\n  preferred_length: 'short' | 'medium' | 'long'\n}\n\n/**\n * 风格偏好枚举\n */\nexport type StylePreference =\n  | 'humorous'      // 幽默风趣\n  | 'artistic'      // 文艺雅致\n  | 'cute'          // 可爱萌系\n  | 'cool'          // 酷炫帅气\n  | 'elegant'       // 优雅高贵\n  | 'playful'       // 活泼俏皮\n  | 'professional'  // 专业型\n\n/**\n * 长度偏好枚举\n */\nexport type LengthPreference = 'short' | 'medium' | 'long'\n\n/**\n * 采样条件\n */\nexport interface SampleCriteria {\n  category?: MorphemeCategory\n  cultural_context?: CulturalContext\n  min_quality_score?: number\n  max_quality_score?: number\n  tags?: string[]\n  exclude_ids?: string[]\n  limit?: number\n  random_seed?: number\n}\n\n/**\n * 性能指标\n */\nexport interface PerformanceMetrics {\n  response_time: {\n    avg: number\n    p95: number\n    p99: number\n    max: number\n  }\n  throughput: {\n    requests_per_second: number\n    concurrent_users: number\n  }\n  resource_usage: {\n    memory_mb: number\n    cpu_percent: number\n  }\n  cache_metrics: {\n    hit_rate: number\n    miss_rate: number\n    eviction_rate: number\n  }\n  quality_metrics: {\n    avg_quality_score: number\n    generation_success_rate: number\n  }\n  timestamp: number\n}\n\n/**\n * 缓存键生成参数\n */\nexport interface CacheKeyParams {\n  cultural_preference: CulturalContext\n  style_preference: StylePreference\n  creativity_level: number\n  quality_threshold: number\n  patterns?: string[]\n  count: number\n}\n\n/**\n * 语义相似度计算结果\n */\nexport interface SemanticSimilarity {\n  score: number\n  dimensions: number[]\n  explanation: string\n}\n\n/**\n * 算法配置\n */\nexport interface AlgorithmConfig {\n  max_generation_attempts: number\n  quality_threshold: number\n  cache_ttl_seconds: number\n  max_cache_size: number\n  semantic_similarity_threshold: number\n  cultural_weight: number\n  creativity_weight: number\n  quality_weight: number\n}\n\n/**\n * 引擎统计信息 morpheme 语素\n */\nexport interface EngineStats {\n  morpheme_count: number\n  morpheme_stats: {\n    total: number\n    byCategory: Record<string, number>\n    byContext: Record<string, number>\n    avgQuality: number\n  }\n  engine_status: 'ready' | 'not_initialized'\n  total_generations: number\n  avg_generation_time: number\n  success_rate: number\n}"], "mappings": ";;AAAA;;;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA5B,IAAA;EAAA;EAAA,IAAA6B,QAAA,GAAA5B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAA0B,QAAA,CAAA9B,IAAA,KAAA8B,QAAA,CAAA9B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA6B,QAAA,CAAA9B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAA0B,cAAA,GAAAD,QAAA,CAAA9B,IAAA;EAAA;IA8BA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAgC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAhC,cAAA;AAAAA,cAAA,GAAAoB,CAAA;;;;;;;;;AAAAa,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AAGC;AAAAlC,cAAA,GAAAoB,CAAA;AAODa,OAAA,CAAAE,wBAAA,GAAAA,wBAAA;AArBA,MAAAC,cAAA;AAAA;AAAA,CAAApC,cAAA,GAAAoB,CAAA,OAAAiB,OAAA;AAEA;AACA;AACA;AAEA;;;;;AAKA,SAAgBH,qBAAqBA,CAACI,MAAgB;EAAA;EAAAtC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACpD,OAAO,2BAAApB,cAAA,GAAAsB,CAAA,UAAAgB,MAAM,CAACC,MAAM,KAAKH,cAAA,CAAAI,0BAA0B,CAACC,MAAM;EAAA;EAAA,CAAAzC,cAAA,GAAAsB,CAAA,UACnDgB,MAAM,CAACC,MAAM,KAAKH,cAAA,CAAAI,0BAA0B,CAACE,YAAY;AAClE;AAEA;;;;;AAKA,SAAgBP,wBAAwBA,CAACG,MAAgB;EAAA;EAAAtC,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACvD,IAAIkB,MAAM,CAACC,MAAM,KAAKH,cAAA,CAAAI,0BAA0B,CAACC,MAAM,EAAE;IAAA;IAAAzC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAAA,OAAO,MAAM;EAAA;EAAA;EAAA;IAAApB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EACtE,IAAIkB,MAAM,CAACC,MAAM,KAAKH,cAAA,CAAAI,0BAA0B,CAACE,YAAY,EAAE;IAAA;IAAA1C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAoB,CAAA;IAAA,OAAO,MAAM;EAAA;EAAA;EAAA;IAAApB,cAAA,GAAAsB,CAAA;EAAA;EAAAtB,cAAA,GAAAoB,CAAA;EAC5E,OAAO,SAAS;AAClB;AAEA;AACA;AACA;AAEA;;;;;;AAMA,IAAYuB,gBAaX;AAAA;AAAA3C,cAAA,GAAAoB,CAAA;AAbD,WAAYuB,gBAAgB;EAAA;EAAA3C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1B;EACAuB,gBAAA,yBAAqB;EACrB;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACAuB,gBAAA,+BAA2B;EAC3B;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACAuB,gBAAA,uCAAmC;EACnC;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACAuB,gBAAA,uBAAmB;EACnB;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACAuB,gBAAA,uBAAmB;EACnB;EAAA;EAAA3C,cAAA,GAAAoB,CAAA;EACAuB,gBAAA,yBAAqB;AACvB,CAAC;AAbW;AAAA,CAAA3C,cAAA,GAAAsB,CAAA,UAAAqB,gBAAgB;AAAA;AAAA,CAAA3C,cAAA,GAAAsB,CAAA,UAAAW,OAAA,CAAAU,gBAAA,GAAhBA,gBAAgB;AAe5B;;;;;;AAMA,IAAYC,eAOX;AAAA;AAAA5C,cAAA,GAAAoB,CAAA;AAPD,WAAYwB,eAAe;EAAA;EAAA5C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EACzB;EACAwB,eAAA,uBAAmB;EACnB;EAAA;EAAA5C,cAAA,GAAAoB,CAAA;EACAwB,eAAA,qBAAiB;EACjB;EAAA;EAAA5C,cAAA,GAAAoB,CAAA;EACAwB,eAAA,uBAAmB;AACrB,CAAC;AAPW;AAAA,CAAA5C,cAAA,GAAAsB,CAAA,UAAAsB,eAAe;AAAA;AAAA,CAAA5C,cAAA,GAAAsB,CAAA,UAAAW,OAAA,CAAAW,eAAA,GAAfA,eAAe;AAmG3B;AACA;AACA;AAEA;;;;;AAKA,IAAYC,gBAiBX;AAAA;AAAA7C,cAAA,GAAAoB,CAAA;AAjBD,WAAYyB,gBAAgB;EAAA;EAAA7C,cAAA,GAAAqB,CAAA;EAAArB,cAAA,GAAAoB,CAAA;EAC1B;EACAyB,gBAAA,iCAA6B;EAC7B;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,yBAAqB;EACrB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,yBAAqB;EACrB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,uBAAmB;EACnB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,6BAAyB;EACzB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,+BAA2B;EAC3B;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,6BAAyB;EACzB;EAAA;EAAA7C,cAAA,GAAAoB,CAAA;EACAyB,gBAAA,6BAAyB;AAC3B,CAAC;AAjBW;AAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAAuB,gBAAgB;AAAA;AAAA,CAAA7C,cAAA,GAAAsB,CAAA,UAAAW,OAAA,CAAAY,gBAAA,GAAhBA,gBAAgB", "ignoreList": []}