{"version": 3, "names": ["cov_qeof9c1nw", "actualCoverage", "ErrorCode", "s", "f", "b"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/types/api.ts"], "sourcesContent": ["/**\n * API接口类型定义\n * 定义namer-v6项目的API请求和响应结构\n */\n\nimport type {\n  GeneratedUsername,\n  QualityScore,\n  CulturalContext,\n  StylePreference,\n  PerformanceMetrics,\n  CreativePattern,\n  LengthPreference\n} from './core'\nimport type {\n  LanguageCode,\n  ConceptCategory,\n  MultiDimensionalCulturalContext\n} from './multilingual'\n\n/**\n * 统一API响应格式\n */\nexport interface APIResponse<T = unknown> {\n  success: boolean\n  data?: T\n  error?: {\n    code: string\n    message: string\n    details?: Record<string, unknown>\n  }\n  meta: {\n    timestamp: number\n    request_id: string\n    execution_time: number\n    version: string\n  }\n}\n\n/**\n * 用户名生成请求 (v3.0多语种支持)\n */\nexport interface GenerateRequest {\n  // 基础参数\n  count?: number                    // 生成数量，默认5，最大10 (v3.0简化)\n  language?: LanguageCode           // 目标语言，默认zh-CN\n\n  // 概念偏好 (v3.0新增)\n  concept_preferences?: {\n    categories?: ConceptCategory[]   // 概念类别偏好\n    cultural_context?: CulturalContext | MultiDimensionalCulturalContext\n    abstraction_level?: number      // [0-1] 抽象程度\n  }\n\n  // 传统偏好设置 (向后兼容)\n  cultural_preference?: CulturalContext\n  style_preference?: StylePreference\n  length_preference?: LengthPreference\n  creativity_level?: number         // 创意程度: 0.1-1.0\n  quality_threshold?: number        // 质量阈值: 0.1-1.0\n\n  // 多语种选项 (v3.0新增)\n  multilingual_options?: {\n    enable_cross_lingual?: boolean  // 启用跨语言生成\n    fallback_languages?: LanguageCode[]\n    maintain_semantic_consistency?: boolean\n    translation_preference?: 'literal' | 'semantic' | 'cultural'\n  }\n\n  // 个性化参数\n  user_id?: string                  // 用户ID，用于个性化\n  context?: {\n    occasion?: string               // 使用场合\n    target_audience?: string        // 目标受众\n    keywords?: string[]             // 关键词\n    cultural_sensitivity?: number   // 文化敏感度 [0-1]\n  }\n\n  // 模式控制\n  patterns?: string[]               // 指定创意模式\n  exclude_patterns?: string[]       // 排除的模式\n}\n\n/**\n * 用户名生成响应 (v3.0多语种支持)\n */\nexport interface GenerateResponse {\n  usernames: GeneratedUsername[]\n  generation_time: number           // 生成耗时(ms)\n  cache_hit: boolean               // 是否命中缓存\n\n  // v3.0多语种信息\n  language: LanguageCode           // 实际使用的语言\n  semantic_consistency_score?: number // 语义一致性评分 [0-1]\n  cross_lingual_alternatives?: {   // 跨语言替代方案\n    [key in LanguageCode]?: string[]\n  }\n\n  // 传统字段 (向后兼容)\n  recommendations?: string[]        // 推荐的相关用户名\n  patterns_used: string[]          // 使用的模式列表\n  total_attempts: number           // 总尝试次数\n\n  // 质量统计 (v3.0增强)\n  quality_distribution?: {\n    excellent: number              // 优秀质量数量\n    good: number                   // 良好质量数量\n    acceptable: number             // 可接受质量数量\n  }\n}\n\n/**\n * 批量生成请求\n */\nexport interface BatchGenerateRequest {\n  requests: GenerateRequest[]       // 批量请求\n  parallel?: boolean               // 是否并行处理，默认true\n  timeout?: number                 // 超时时间(ms)，默认30000\n}\n\n/**\n * 批量生成响应\n */\nexport interface BatchGenerateResponse {\n  results: (GenerateResponse | null)[]  // 结果数组，失败为null\n  success_count: number            // 成功数量\n  total_time: number              // 总耗时\n  errors?: BatchError[]           // 错误信息\n}\n\n// ============================================================================\n// v3.0多语种专用API接口\n// ============================================================================\n\n/**\n * 语言检测请求\n */\nexport interface LanguageDetectionRequest {\n  text: string                     // 待检测文本\n  confidence_threshold?: number    // 置信度阈值 [0-1]\n}\n\n/**\n * 语言检测响应\n */\nexport interface LanguageDetectionResponse {\n  detected_language: LanguageCode  // 检测到的语言\n  confidence: number               // 检测置信度 [0-1]\n  alternatives?: {                 // 备选语言\n    language: LanguageCode\n    confidence: number\n  }[]\n}\n\n/**\n * 跨语言翻译请求\n */\nexport interface TranslationRequest {\n  username: string                 // 源用户名\n  source_language: LanguageCode    // 源语言\n  target_language: LanguageCode    // 目标语言\n  preserve_meaning?: boolean       // 保持语义，默认true\n  preserve_style?: boolean         // 保持风格，默认false\n  cultural_adaptation?: boolean    // 文化适配，默认true\n}\n\n/**\n * 跨语言翻译响应\n */\nexport interface TranslationResponse {\n  translated_username: string     // 翻译结果\n  confidence: number              // 翻译置信度 [0-1]\n  alternatives: string[]          // 备选翻译\n  semantic_similarity: number     // 语义相似度 [0-1]\n  cultural_adaptation_score: number // 文化适配评分 [0-1]\n  explanation?: string            // 翻译说明\n}\n\n/**\n * 概念查询请求\n */\nexport interface ConceptQueryRequest {\n  concept_id?: string             // 概念ID\n  category?: ConceptCategory      // 概念类别\n  language?: LanguageCode         // 查询语言\n  include_related?: boolean       // 包含相关概念\n}\n\n/**\n * 概念查询响应\n */\nexport interface ConceptQueryResponse {\n  concepts: {\n    concept_id: string\n    category: ConceptCategory\n    abstract_meaning: string\n    language_implementations: {\n      [key in LanguageCode]?: {\n        text: string\n        alternatives: string[]\n        cultural_context: MultiDimensionalCulturalContext\n      }\n    }\n  }[]\n  total_count: number\n}\n\n/**\n * 批量错误信息\n */\nexport interface BatchError {\n  index: number\n  error: {\n    code: string\n    message: string\n    details?: Record<string, unknown>\n  }\n}\n\n/**\n * 质量评估请求\n */\nexport interface EvaluateRequest {\n  usernames: string[]              // 待评估的用户名\n  language?: string                // 语言代码\n  cultural_context?: CulturalContext\n  detailed?: boolean               // 是否返回详细评估\n}\n\n/**\n * 质量评估响应\n */\nexport interface EvaluateResponse {\n  evaluations: UsernameEvaluation[]\n  average_score: number\n  evaluation_time: number\n  summary: {\n    excellent_count: number        // 优秀数量 (>0.8)\n    good_count: number            // 良好数量 (0.6-0.8)\n    average_count: number         // 一般数量 (0.4-0.6)\n    poor_count: number            // 较差数量 (<0.4)\n  }\n}\n\n/**\n * 用户名评估结果\n */\nexport interface UsernameEvaluation {\n  username: string\n  overall_score: number            // 总体评分 0-1\n  quality_score: QualityScore\n  suggestions?: string[]           // 改进建议\n  similar_usernames?: string[]     // 相似用户名推荐\n}\n\n/**\n * 健康检查响应\n */\nexport interface HealthResponse {\n  status: 'healthy' | 'degraded' | 'unhealthy'\n  timestamp: number\n  version: string\n  uptime: number\n  checks: {\n    memory: HealthCheck\n    cache: HealthCheck\n    data_integrity: HealthCheck\n    performance: HealthCheck\n  }\n}\n\n/**\n * 健康检查详情\n */\nexport interface HealthCheckDetails {\n  [key: string]: string | number | boolean | null | undefined | HealthCheckDetails | HealthCheckDetails[]\n}\n\n/**\n * 健康检查项\n */\nexport interface HealthCheck {\n  status: 'pass' | 'fail' | 'warn'\n  response_time?: number\n  details?: HealthCheckDetails\n  message?: string\n}\n\n/**\n * 性能指标响应\n */\nexport interface MetricsResponse {\n  performance: PerformanceMetrics\n  system_info: {\n    node_version: string\n    memory_usage: NodeJS.MemoryUsage\n    uptime: number\n    platform: string\n  }\n  api_stats: {\n    total_requests: number\n    successful_requests: number\n    failed_requests: number\n    avg_response_time: number\n  }\n}\n\n/**\n * 创意模式列表响应\n */\nexport interface PatternsResponse {\n  patterns: CreativePattern[]\n  total_count: number\n  categories: {\n    [category: string]: number\n  }\n}\n\n/**\n * 错误代码枚举\n */\nexport enum ErrorCode {\n  // 通用错误 (1000-1999)\n  INVALID_PARAMETERS = 'E1001',\n  MISSING_REQUIRED_FIELD = 'E1002',\n  INVALID_FORMAT = 'E1003',\n  VALIDATION_FAILED = 'E1004',\n\n  // 业务错误 (3000-3999)\n  GENERATION_FAILED = 'E3001',\n  QUALITY_TOO_LOW = 'E3002',\n  NO_SUITABLE_PATTERNS = 'E3003',\n  INSUFFICIENT_MORPHEMES = 'E3004',\n\n  // 系统错误 (5000-5999)\n  INTERNAL_ERROR = 'E5001',\n  SERVICE_UNAVAILABLE = 'E5002',\n  TIMEOUT = 'E5003',\n  MEMORY_LIMIT_EXCEEDED = 'E5004'\n}\n\n/**\n * 请求验证规则\n */\nexport type ValidationValue = string | number | boolean | null | undefined\n\nexport interface ValidationRule {\n  field: string\n  type: 'string' | 'number' | 'boolean' | 'array' | 'object'\n  required?: boolean\n  min?: number\n  max?: number\n  pattern?: RegExp\n  enum?: ValidationValue[]\n  custom?: (value: unknown) => boolean | string\n}\n\n/**\n * 分页参数\n */\nexport interface PaginationParams {\n  page?: number\n  limit?: number\n  sort?: string\n  order?: 'asc' | 'desc'\n}\n\n/**\n * 分页响应\n */\nexport interface PaginatedResponse<T> {\n  data: T[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n    has_next: boolean\n    has_prev: boolean\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyUE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAzUF;;;;AA8TA;;;AAGA,WAAYE,SAkBX;AAAA;AAAAF,aAAA,GAAAG,CAAA;AAlBD,WAAYD,SAAS;EAAA;EAAAF,aAAA,GAAAI,CAAA;EAAAJ,aAAA,GAAAG,CAAA;EACnB;EACAD,SAAA,gCAA4B;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAC5BD,SAAA,oCAAgC;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAChCD,SAAA,4BAAwB;EAAA;EAAAF,aAAA,GAAAG,CAAA;EACxBD,SAAA,+BAA2B;EAE3B;EAAA;EAAAF,aAAA,GAAAG,CAAA;EACAD,SAAA,+BAA2B;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAC3BD,SAAA,6BAAyB;EAAA;EAAAF,aAAA,GAAAG,CAAA;EACzBD,SAAA,kCAA8B;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAC9BD,SAAA,oCAAgC;EAEhC;EAAA;EAAAF,aAAA,GAAAG,CAAA;EACAD,SAAA,4BAAwB;EAAA;EAAAF,aAAA,GAAAG,CAAA;EACxBD,SAAA,iCAA6B;EAAA;EAAAF,aAAA,GAAAG,CAAA;EAC7BD,SAAA,qBAAiB;EAAA;EAAAF,aAAA,GAAAG,CAAA;EACjBD,SAAA,mCAA+B;AACjC,CAAC;AAlBW;AAAA,CAAAF,aAAA,GAAAK,CAAA,UAAAH,SAAS;AAAA;AAAA,CAAAF,aAAA,GAAAK,CAAA,UAATH,SAAS", "ignoreList": []}