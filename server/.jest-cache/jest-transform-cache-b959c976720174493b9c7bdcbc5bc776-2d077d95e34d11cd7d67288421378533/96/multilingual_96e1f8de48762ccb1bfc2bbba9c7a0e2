14ecbf58575421ecdc6876541cfdbfdc
"use strict";

/**
 * 多语种数据模型定义 (v3.0)
 *
 * 基于概念-语言分离架构的多语种数据类型定义
 * 支持8种主流语言的语素生成和跨语言语义对齐
 *
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */
/* istanbul ignore next */
function cov_2rdfck1oki() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/types/multilingual.ts";
  var hash = "9018bda46c61361aabefff4a67723f0009b1649a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/types/multilingual.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 62
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 117
        }
      },
      "2": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 27,
          column: 2
        }
      },
      "3": {
        start: {
          line: 32,
          column: 0
        },
        end: {
          line: 49,
          column: 63
        }
      },
      "4": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 34,
          column: 36
        }
      },
      "5": {
        start: {
          line: 36,
          column: 4
        },
        end: {
          line: 36,
          column: 36
        }
      },
      "6": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 36
        }
      },
      "7": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 36
        }
      },
      "8": {
        start: {
          line: 42,
          column: 4
        },
        end: {
          line: 42,
          column: 36
        }
      },
      "9": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 44,
          column: 36
        }
      },
      "10": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 36
        }
      },
      "11": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 48,
          column: 36
        }
      },
      "12": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 67,
          column: 72
        }
      },
      "13": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 45
        }
      },
      "14": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 51
        }
      },
      "15": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 59
        }
      },
      "16": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 43
        }
      },
      "17": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 43
        }
      },
      "18": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 45
        }
      },
      "19": {
        start: {
          line: 72,
          column: 0
        },
        end: {
          line: 81,
          column: 66
        }
      },
      "20": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 74,
          column: 39
        }
      },
      "21": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 41
        }
      },
      "22": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 43
        }
      },
      "23": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 80,
          column: 47
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 32,
            column: 1
          },
          end: {
            line: 32,
            column: 2
          }
        },
        loc: {
          start: {
            line: 32,
            column: 25
          },
          end: {
            line: 49,
            column: 1
          }
        },
        line: 32
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 54,
            column: 1
          },
          end: {
            line: 54,
            column: 2
          }
        },
        loc: {
          start: {
            line: 54,
            column: 28
          },
          end: {
            line: 67,
            column: 1
          }
        },
        line: 54
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 72,
            column: 1
          },
          end: {
            line: 72,
            column: 2
          }
        },
        loc: {
          start: {
            line: 72,
            column: 26
          },
          end: {
            line: 81,
            column: 1
          }
        },
        line: 72
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 49,
            column: 3
          },
          end: {
            line: 49,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 3
          },
          end: {
            line: 49,
            column: 15
          }
        }, {
          start: {
            line: 49,
            column: 20
          },
          end: {
            line: 49,
            column: 60
          }
        }],
        line: 49
      },
      "1": {
        loc: {
          start: {
            line: 67,
            column: 3
          },
          end: {
            line: 67,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 3
          },
          end: {
            line: 67,
            column: 18
          }
        }, {
          start: {
            line: 67,
            column: 23
          },
          end: {
            line: 67,
            column: 69
          }
        }],
        line: 67
      },
      "2": {
        loc: {
          start: {
            line: 81,
            column: 3
          },
          end: {
            line: 81,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 3
          },
          end: {
            line: 81,
            column: 16
          }
        }, {
          start: {
            line: 81,
            column: 21
          },
          end: {
            line: 81,
            column: 63
          }
        }],
        line: 81
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/types/multilingual.ts",
      mappings: ";AAAA;;;;;;;;;GASG;;;AAEH,+EAA+E;AAC/E,YAAY;AACZ,+EAA+E;AAE/E;;GAEG;AACU,QAAA,0BAA0B,GAAG;IACxC,wBAAwB;IACxB,MAAM,EAAE,EAAE;IACV,gCAAgC;IAChC,YAAY,EAAE,GAAG;IACjB,cAAc;IACd,OAAO,EAAE,GAAG;CACJ,CAAA;AAEV;;GAEG;AACH,IAAY,YAiBX;AAjBD,WAAY,YAAY;IACtB,cAAc;IACd,+BAAe,CAAA;IACf,cAAc;IACd,+BAAe,CAAA;IACf,SAAS;IACT,+BAAe,CAAA;IACf,SAAS;IACT,+BAAe,CAAA;IACf,WAAW;IACX,+BAAe,CAAA;IACf,SAAS;IACT,+BAAe,CAAA;IACf,SAAS;IACT,+BAAe,CAAA;IACf,WAAW;IACX,+BAAe,CAAA;AACjB,CAAC,EAjBW,YAAY,4BAAZ,YAAY,QAiBvB;AAED;;GAEG;AACH,IAAY,eAaX;AAbD,WAAY,eAAe;IACzB,WAAW;IACX,wCAAqB,CAAA;IACrB,WAAW;IACX,8CAA2B,CAAA;IAC3B,WAAW;IACX,sDAAmC,CAAA;IACnC,WAAW;IACX,sCAAmB,CAAA;IACnB,WAAW;IACX,sCAAmB,CAAA;IACnB,WAAW;IACX,wCAAqB,CAAA;AACvB,CAAC,EAbW,eAAe,+BAAf,eAAe,QAa1B;AAED;;GAEG;AACH,IAAY,aASX;AATD,WAAY,aAAa;IACvB,WAAW;IACX,kCAAiB,CAAA;IACjB,WAAW;IACX,oCAAmB,CAAA;IACnB,YAAY;IACZ,sCAAqB,CAAA;IACrB,YAAY;IACZ,0CAAyB,CAAA;AAC3B,CAAC,EATW,aAAa,6BAAb,aAAa,QASxB",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/types/multilingual.ts"],
      sourcesContent: ["/**\n * \u591A\u8BED\u79CD\u6570\u636E\u6A21\u578B\u5B9A\u4E49 (v3.0)\n * \n * \u57FA\u4E8E\u6982\u5FF5-\u8BED\u8A00\u5206\u79BB\u67B6\u6784\u7684\u591A\u8BED\u79CD\u6570\u636E\u7C7B\u578B\u5B9A\u4E49\n * \u652F\u63018\u79CD\u4E3B\u6D41\u8BED\u8A00\u7684\u8BED\u7D20\u751F\u6210\u548C\u8DE8\u8BED\u8A00\u8BED\u4E49\u5BF9\u9F50\n * \n * <AUTHOR> team\n * @version 3.0.0\n * @created 2025-06-24\n */\n\n// ============================================================================\n// \u8BED\u8A00\u4EE3\u7801\u548C\u57FA\u7840\u7C7B\u578B\n// ============================================================================\n\n/**\n * \u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6\u914D\u7F6E\n */\nexport const SEMANTIC_VECTOR_DIMENSIONS = {\n  /** \u4F20\u7EDF\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 (\u517C\u5BB9v2.0) */\n  LEGACY: 20,\n  /** \u591A\u8BED\u8A00\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 (\u57FA\u4E8EmBERT/XLM-R) */\n  MULTILINGUAL: 512,\n  /** \u9ED8\u8BA4\u4F7F\u7528\u7684\u7EF4\u5EA6 */\n  DEFAULT: 512\n} as const\n\n/**\n * \u652F\u6301\u7684\u8BED\u8A00\u4EE3\u7801\u679A\u4E3E\n */\nexport enum LanguageCode {\n  /** \u4E2D\u6587 (\u7B80\u4F53) */\n  ZH_CN = 'zh-CN',\n  /** \u82F1\u6587 (\u7F8E\u5F0F) */\n  EN_US = 'en-US',\n  /** \u65E5\u6587 */\n  JA_JP = 'ja-JP',\n  /** \u97E9\u6587 */\n  KO_KR = 'ko-KR',\n  /** \u897F\u73ED\u7259\u6587 */\n  ES_ES = 'es-ES',\n  /** \u6CD5\u6587 */\n  FR_FR = 'fr-FR',\n  /** \u5FB7\u6587 */\n  DE_DE = 'de-DE',\n  /** \u963F\u62C9\u4F2F\u6587 */\n  AR_SA = 'ar-SA'\n}\n\n/**\n * \u6982\u5FF5\u7C7B\u522B\u679A\u4E3E (\u8BED\u8A00\u65E0\u5173)\n */\nexport enum ConceptCategory {\n  /** \u60C5\u611F\u6982\u5FF5 */\n  EMOTIONS = 'emotions',\n  /** \u804C\u4E1A\u6982\u5FF5 */\n  PROFESSIONS = 'professions',\n  /** \u7279\u5F81\u6982\u5FF5 */\n  CHARACTERISTICS = 'characteristics',\n  /** \u7269\u4F53\u6982\u5FF5 */\n  OBJECTS = 'objects',\n  /** \u52A8\u4F5C\u6982\u5FF5 */\n  ACTIONS = 'actions',\n  /** \u62BD\u8C61\u6982\u5FF5 */\n  CONCEPTS = 'concepts'\n}\n\n/**\n * \u8BED\u57DF\u5C42\u6B21\u679A\u4E3E\n */\nexport enum RegisterLevel {\n  /** \u6B63\u5F0F\u8BED\u57DF */\n  FORMAL = 'formal',\n  /** \u4E2D\u6027\u8BED\u57DF */\n  NEUTRAL = 'neutral',\n  /** \u975E\u6B63\u5F0F\u8BED\u57DF */\n  INFORMAL = 'informal',\n  /** \u53E3\u8BED\u5316\u8BED\u57DF */\n  COLLOQUIAL = 'colloquial'\n}\n\n// ============================================================================\n// \u901A\u7528\u6982\u5FF5\u6570\u636E\u6A21\u578B\n// ============================================================================\n\n/**\n * \u901A\u7528\u8BED\u4E49\u5411\u91CF\u63A5\u53E3\n */\nexport interface UniversalSemanticVector {\n  /** \u591A\u8BED\u8A00\u8BED\u4E49\u5411\u91CF (\u57FA\u4E8EmBERT/XLM-R) */\n  vector: number[] // \u957F\u5EA6\u4E3A SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL\n  /** \u5411\u91CF\u6A21\u578B\u7248\u672C */\n  model_version: string\n  /** \u5411\u91CF\u7F6E\u4FE1\u5EA6 [0-1] */\n  confidence: number\n  /** \u6700\u540E\u66F4\u65B0\u65F6\u95F4 */\n  updated_at: string\n  /** \u517C\u5BB9\u6027\u5411\u91CF (\u7528\u4E8E\u4E0E\u4F20\u7EDF20\u7EF4\u5411\u91CF\u5BF9\u9F50) */\n  legacy_vector?: number[] // \u957F\u5EA6\u4E3A SEMANTIC_VECTOR_DIMENSIONS.LEGACY\n}\n\n/**\n * \u591A\u7EF4\u5EA6\u6587\u5316\u8BED\u5883\u63A5\u53E3\n */\nexport interface MultiDimensionalCulturalContext {\n  /** \u4F20\u7EDF\u6027\u7A0B\u5EA6 [0-1] */\n  traditionality: number\n  /** \u73B0\u4EE3\u6027\u7A0B\u5EA6 [0-1] */\n  modernity: number\n  /** \u6B63\u5F0F\u6027\u7A0B\u5EA6 [0-1] */\n  formality: number\n  /** \u5730\u57DF\u7279\u8272\u7A0B\u5EA6 [0-1] */\n  regionality: number\n  /** \u5B97\u6559\u654F\u611F\u6027 [0-1] */\n  religious_sensitivity: number\n  /** \u5E74\u9F84\u9002\u5B9C\u6027\u6807\u7B7E */\n  age_appropriateness: string[]\n  /** \u6587\u5316\u6807\u7B7E */\n  cultural_tags: string[]\n}\n\n/**\n * \u901A\u7528\u6982\u5FF5\u63A5\u53E3 - \u8BED\u8A00\u65E0\u5173\u7684\u62BD\u8C61\u6982\u5FF5\n */\nexport interface UniversalConcept {\n  /** \u5168\u5C40\u552F\u4E00\u6982\u5FF5ID */\n  concept_id: string\n  /** \u8BED\u4E49\u5411\u91CF */\n  semantic_vector: UniversalSemanticVector\n  /** \u6982\u5FF5\u7C7B\u522B */\n  concept_category: ConceptCategory\n  /** \u62BD\u8C61\u7A0B\u5EA6 [0-1] */\n  abstraction_level: number\n  /** \u6587\u5316\u4E2D\u6027\u5EA6 [0-1] */\n  cultural_neutrality: number\n  /** \u8DE8\u8BED\u8A00\u7A33\u5B9A\u6027 [0-1] */\n  cross_lingual_stability: number\n  \n  // \u8BA4\u77E5\u5C5E\u6027\n  /** \u8BA4\u77E5\u8D1F\u8377 [0-1] */\n  cognitive_load: number\n  /** \u8BB0\u5FC6\u6027\u8BC4\u5206 [0-1] */\n  memorability_score: number\n  /** \u60C5\u611F\u4EF7\u503C [-1,1] */\n  emotional_valence: number\n  /** \u8BA4\u77E5\u5C5E\u6027\u96C6\u5408 */\n  cognitive_attributes: {\n    memorability: number\n    cognitive_load: number\n    emotional_valence: number\n  }\n  \n  // \u5173\u7CFB\u6620\u5C04\n  /** \u76F8\u5173\u6982\u5FF5ID\u5217\u8868 */\n  related_concepts: string[]\n  /** \u5C42\u6B21\u7236\u6982\u5FF5ID */\n  hierarchical_parent?: string\n  /** \u5C42\u6B21\u5B50\u6982\u5FF5ID\u5217\u8868 */\n  hierarchical_children: string[]\n  \n  // \u5143\u6570\u636E\n  created_at: string\n  updated_at: string\n  version: string\n}\n\n// ============================================================================\n// \u8BED\u8A00\u7279\u5B9A\u5B9E\u73B0\u6570\u636E\u6A21\u578B\n// ============================================================================\n\n/**\n * \u8BED\u97F3\u7279\u5F81\u63A5\u53E3\n */\nexport interface PhoneticFeatures {\n  /** IPA\u97F3\u6807 */\n  ipa_transcription: string\n  /** \u97F3\u8282\u6570\u91CF */\n  syllable_count: number\n  /** \u91CD\u97F3\u6A21\u5F0F (\u82F1\u6587\u7B49) */\n  stress_pattern?: string\n  /** \u58F0\u8C03\u4FE1\u606F (\u4E2D\u6587\u7B49) */\n  tone_pattern?: string[]\n  /** \u8BED\u97F3\u548C\u8C10\u6027\u8BC4\u5206 [0-1] */\n  phonetic_harmony: number\n}\n\n/**\n * \u8BCD\u6CD5\u4FE1\u606F\u63A5\u53E3\n */\nexport interface MorphologicalInfo {\n  /** \u8BCD\u6027\u6807\u6CE8 (Universal Dependencies) */\n  pos_tag: string\n  /** \u8BCD\u6CD5\u7C7B\u578B */\n  morphological_type: string\n  /** \u8BCD\u6839\u4FE1\u606F */\n  root?: string\n  /** \u524D\u7F00\u5217\u8868 */\n  prefixes: string[]\n  /** \u540E\u7F00\u5217\u8868 */\n  suffixes: string[]\n  /** \u5C48\u6298\u53D8\u5316\u4FE1\u606F */\n  inflection_info?: Record<string, string>\n}\n\n/**\n * \u53E5\u6CD5\u5C5E\u6027\u63A5\u53E3\n */\nexport interface SyntacticProperties {\n  /** \u53E5\u6CD5\u529F\u80FD */\n  syntactic_function: string[]\n  /** \u642D\u914D\u9650\u5236 */\n  collocation_constraints: string[]\n  /** \u8BED\u6CD5\u7279\u5F81 */\n  grammatical_features: Record<string, string>\n}\n\n/**\n * \u5730\u533A\u53D8\u4F53\u63A5\u53E3\n */\nexport interface RegionalVariant {\n  /** \u5730\u533A\u4EE3\u7801 */\n  region_code: string\n  /** \u53D8\u4F53\u6587\u672C */\n  variant_text: string\n  /** \u4F7F\u7528\u9891\u7387 [0-1] */\n  usage_frequency: number\n  /** \u5730\u533A\u7279\u8272\u7A0B\u5EA6 [0-1] */\n  regional_specificity: number\n}\n\n/**\n * \u8BED\u8A00\u7279\u5B9A\u8D28\u91CF\u8BC4\u5206\u63A5\u53E3\n */\nexport interface LanguageQualityScores {\n  /** \u81EA\u7136\u5EA6 [0-1] */\n  naturalness: number\n  /** \u6D41\u7545\u5EA6 [0-1] */\n  fluency: number\n  /** \u5730\u9053\u6027 [0-1] */\n  authenticity: number\n  /** \u7F8E\u5B66\u5438\u5F15\u529B [0-1] */\n  aesthetic_appeal: number\n  /** \u53D1\u97F3\u53CB\u597D\u5EA6 [0-1] */\n  pronunciation_ease: number\n  /** \u8BB0\u5FC6\u6027 [0-1] */\n  memorability: number\n  /** \u72EC\u7279\u6027 [0-1] */\n  uniqueness: number\n  /** \u5B9E\u7528\u6027 [0-1] */\n  practicality: number\n}\n\n/**\n * \u8BED\u8A00\u7279\u5B9A\u8BED\u7D20\u63A5\u53E3 - \u6982\u5FF5\u5728\u7279\u5B9A\u8BED\u8A00\u4E2D\u7684\u5B9E\u73B0\n */\nexport interface LanguageSpecificMorpheme {\n  /** \u8BED\u7D20\u552F\u4E00ID */\n  morpheme_id: string\n  /** \u5BF9\u5E94\u7684\u901A\u7528\u6982\u5FF5ID */\n  concept_id: string\n  /** \u8BED\u8A00\u4EE3\u7801 */\n  language: LanguageCode\n  \n  // \u8BED\u8A00\u5F62\u5F0F\n  /** \u6587\u672C\u5F62\u5F0F */\n  text: string\n  /** \u66FF\u4EE3\u5F62\u5F0F\u5217\u8868 */\n  alternative_forms: string[]\n  \n  // \u8BED\u8A00\u5B66\u5C5E\u6027\n  /** \u8BED\u97F3\u7279\u5F81 */\n  phonetic_features: PhoneticFeatures\n  /** \u8BCD\u6CD5\u4FE1\u606F */\n  morphological_info: MorphologicalInfo\n  /** \u53E5\u6CD5\u5C5E\u6027 */\n  syntactic_properties: SyntacticProperties\n  \n  // \u6587\u5316\u9002\u914D\n  /** \u6587\u5316\u8BED\u5883 */\n  cultural_context: MultiDimensionalCulturalContext\n  /** \u5730\u533A\u53D8\u4F53 */\n  regional_variants: RegionalVariant[]\n  /** \u8BED\u57DF\u5C42\u6B21 */\n  register_level: RegisterLevel\n  \n  // \u8D28\u91CF\u6307\u6807\n  /** \u8BED\u8A00\u7279\u5B9A\u8D28\u91CF\u8BC4\u5206 */\n  language_quality_scores: LanguageQualityScores\n  /** \u6587\u5316\u9002\u5B9C\u6027 [0-1] */\n  cultural_appropriateness: number\n  /** \u6BCD\u8BED\u8005\u8BC4\u5206 [0-1] */\n  native_speaker_rating: number\n  \n  // \u4F7F\u7528\u7EDF\u8BA1\n  /** \u4F7F\u7528\u9891\u7387 [0-1] */\n  usage_frequency: number\n  /** \u6D41\u884C\u5EA6\u8D8B\u52BF */\n  popularity_trend: number\n  \n  // \u5143\u6570\u636E\n  created_at: string\n  updated_at: string\n  version: string\n  /** \u6570\u636E\u6765\u6E90 */\n  source: string\n  /** \u9A8C\u8BC1\u72B6\u6001 */\n  validation_status: 'pending' | 'validated' | 'rejected'\n}\n\n// ============================================================================\n// \u8DE8\u8BED\u8A00\u5BF9\u9F50\u548C\u8D28\u91CF\u8BC4\u4F30\n// ============================================================================\n\n/**\n * \u8BED\u4E49\u5BF9\u9F50\u7ED3\u679C\u63A5\u53E3\n */\nexport interface AlignmentResult {\n  /** \u6E90\u6982\u5FF5 */\n  source_concept: UniversalConcept\n  /** \u76EE\u6807\u8BED\u8A00 */\n  target_language: LanguageCode\n  /** \u5BF9\u9F50\u7684\u8BED\u7D20\u5217\u8868 */\n  aligned_morphemes: LanguageSpecificMorpheme[]\n  /** \u5BF9\u9F50\u7F6E\u4FE1\u5EA6 [0-1] */\n  confidence_score: number\n  /** \u8BED\u4E49\u8DDD\u79BB [0-1] */\n  semantic_distance: number\n  /** \u6587\u5316\u9002\u914D\u8BF4\u660E */\n  cultural_adaptation_notes: string[]\n  /** \u5BF9\u9F50\u65F6\u95F4\u6233 */\n  aligned_at: string\n}\n\n/**\n * \u6279\u91CF\u5BF9\u9F50\u7ED3\u679C\u63A5\u53E3\n */\nexport interface BatchAlignmentResult {\n  /** \u5BF9\u9F50\u7ED3\u679C\u5217\u8868 */\n  alignments: AlignmentResult[]\n  /** \u603B\u4F53\u6210\u529F\u7387 [0-1] */\n  overall_success_rate: number\n  /** \u5E73\u5747\u7F6E\u4FE1\u5EA6 [0-1] */\n  average_confidence: number\n  /** \u5904\u7406\u65F6\u95F4 (\u6BEB\u79D2) */\n  processing_time: number\n  /** \u9519\u8BEF\u4FE1\u606F */\n  errors: string[]\n}\n\n/**\n * \u8DE8\u8BED\u8A00\u4E00\u81F4\u6027\u62A5\u544A\u63A5\u53E3\n */\nexport interface ConsistencyReport {\n  /** \u6982\u5FF5ID */\n  concept_id: string\n  /** \u68C0\u67E5\u7684\u8BED\u8A00\u5217\u8868 */\n  languages: LanguageCode[]\n  /** \u8BED\u4E49\u4E00\u81F4\u6027\u8BC4\u5206 [0-1] */\n  semantic_consistency: number\n  /** \u6587\u5316\u4E00\u81F4\u6027\u8BC4\u5206 [0-1] */\n  cultural_consistency: number\n  /** \u8D28\u91CF\u4E00\u81F4\u6027\u8BC4\u5206 [0-1] */\n  quality_consistency: number\n  /** \u4E0D\u4E00\u81F4\u9879\u8BE6\u60C5 */\n  inconsistencies: {\n    type: 'semantic' | 'cultural' | 'quality'\n    description: string\n    affected_languages: LanguageCode[]\n    severity: 'low' | 'medium' | 'high'\n  }[]\n  /** \u5EFA\u8BAE\u6539\u8FDB\u63AA\u65BD */\n  improvement_suggestions: string[]\n  /** \u68C0\u67E5\u65F6\u95F4\u6233 */\n  checked_at: string\n}\n\n/**\n * \u901A\u7528\u8D28\u91CF\u8BC4\u4F30\u63A5\u53E3\n */\nexport interface UniversalQualityAssessment {\n  /** \u6982\u5FF5ID */\n  concept_id: string\n  /** \u521B\u610F\u6027\u8BC4\u5206 [0-1] */\n  creativity: number\n  /** \u8BB0\u5FC6\u6027\u8BC4\u5206 [0-1] */\n  memorability: number\n  /** \u72EC\u7279\u6027\u8BC4\u5206 [0-1] */\n  uniqueness: number\n  /** \u8BED\u4E49\u8FDE\u8D2F\u6027\u8BC4\u5206 [0-1] */\n  semantic_coherence: number\n  /** \u8DE8\u8BED\u8A00\u9002\u5E94\u6027\u8BC4\u5206 [0-1] */\n  cross_lingual_adaptability: number\n  /** \u7EFC\u5408\u8D28\u91CF\u8BC4\u5206 [0-1] */\n  overall_quality: number\n  /** \u8BC4\u4F30\u65F6\u95F4\u6233 */\n  assessed_at: string\n}\n\n/**\n * \u591A\u8BED\u79CD\u7528\u6237\u540D\u751F\u6210\u8BF7\u6C42\u63A5\u53E3\n */\nexport interface MultilingualGenerationRequest {\n  /** \u76EE\u6807\u8BED\u8A00 */\n  target_language: LanguageCode\n  /** \u6982\u5FF5\u504F\u597D */\n  concept_preferences?: {\n    categories: ConceptCategory[]\n    cultural_context?: Partial<MultiDimensionalCulturalContext>\n    abstraction_level?: [number, number] // \u8303\u56F4\n    emotional_valence?: [number, number] // \u8303\u56F4\n  }\n  /** \u8DE8\u8BED\u8A00\u9009\u9879 */\n  cross_lingual_options?: {\n    /** \u662F\u5426\u542F\u7528\u8DE8\u8BED\u8A00\u5BF9\u6BD4 */\n    enable_comparison: boolean\n    /** \u5BF9\u6BD4\u8BED\u8A00\u5217\u8868 */\n    comparison_languages: LanguageCode[]\n    /** \u8BED\u4E49\u4E00\u81F4\u6027\u8981\u6C42 [0-1] */\n    semantic_consistency_threshold: number\n  }\n  /** \u751F\u6210\u6570\u91CF */\n  count: number\n  /** \u8D28\u91CF\u9608\u503C [0-1] */\n  quality_threshold: number\n}\n\n/**\n * \u591A\u8BED\u79CD\u7528\u6237\u540D\u751F\u6210\u7ED3\u679C\u63A5\u53E3\n */\nexport interface MultilingualGenerationResult {\n  /** \u76EE\u6807\u8BED\u8A00 */\n  target_language: LanguageCode\n  /** \u751F\u6210\u7684\u7528\u6237\u540D\u5217\u8868 */\n  usernames: {\n    /** \u7528\u6237\u540D\u6587\u672C */\n    text: string\n    /** \u4F7F\u7528\u7684\u8BED\u7D20\u7EC4\u5408 */\n    morpheme_combination: LanguageSpecificMorpheme[]\n    /** \u5BF9\u5E94\u7684\u6982\u5FF5\u7EC4\u5408 */\n    concept_combination: UniversalConcept[]\n    /** \u8D28\u91CF\u8BC4\u4F30 */\n    quality_assessment: UniversalQualityAssessment\n    /** \u8DE8\u8BED\u8A00\u5BF9\u6BD4 (\u5982\u679C\u542F\u7528) */\n    cross_lingual_comparison?: {\n      language: LanguageCode\n      equivalent_text: string\n      semantic_similarity: number\n    }[]\n  }[]\n  /** \u751F\u6210\u7EDF\u8BA1 */\n  generation_stats: {\n    /** \u8BF7\u6C42\u5904\u7406\u65F6\u95F4 (\u6BEB\u79D2) */\n    processing_time: number\n    /** \u6210\u529F\u751F\u6210\u6570\u91CF */\n    successful_count: number\n    /** \u5E73\u5747\u8D28\u91CF\u8BC4\u5206 */\n    average_quality: number\n    /** \u8BED\u4E49\u591A\u6837\u6027\u8BC4\u5206 [0-1] */\n    semantic_diversity: number\n  }\n  /** \u751F\u6210\u65F6\u95F4\u6233 */\n  generated_at: string\n}\n\n// ============================================================================\n// \u6570\u636E\u8FC1\u79FB\u548C\u517C\u5BB9\u6027\n// ============================================================================\n\n/**\n * v2.0\u5230v3.0\u6570\u636E\u8FC1\u79FB\u6620\u5C04\u63A5\u53E3\n */\nexport interface MigrationMapping {\n  /** v2.0\u8BED\u7D20ID */\n  v2_morpheme_id: string\n  /** \u5BF9\u5E94\u7684v3.0\u6982\u5FF5ID */\n  v3_concept_id: string\n  /** \u5BF9\u5E94\u7684v3.0\u8BED\u7D20ID */\n  v3_morpheme_id: string\n  /** \u8FC1\u79FB\u7F6E\u4FE1\u5EA6 [0-1] */\n  migration_confidence: number\n  /** \u9700\u8981\u4EBA\u5DE5\u5BA1\u6838 */\n  requires_manual_review: boolean\n  /** \u8FC1\u79FB\u8BF4\u660E */\n  migration_notes: string[]\n}\n\n/**\n * \u5411\u540E\u517C\u5BB9\u6027\u914D\u7F6E\u63A5\u53E3\n */\nexport interface BackwardCompatibilityConfig {\n  /** \u662F\u5426\u542F\u7528v2.0\u517C\u5BB9\u6A21\u5F0F */\n  enable_v2_compatibility: boolean\n  /** v2.0 API\u7AEF\u70B9\u6620\u5C04 */\n  v2_api_mappings: Record<string, string>\n  /** \u6570\u636E\u683C\u5F0F\u8F6C\u6362\u89C4\u5219 */\n  format_conversion_rules: Record<string, any>\n  /** \u517C\u5BB9\u6027\u8B66\u544A\u9608\u503C */\n  compatibility_warning_threshold: number\n}\n\n/**\n * \u6982\u5FF5-\u8BED\u7D20\u6620\u5C04\u63A5\u53E3\n */\nexport interface ConceptMorphemeMapping {\n  /** \u6982\u5FF5ID */\n  conceptId: string\n  /** \u5BF9\u5E94\u7684\u8BED\u7D20\u5217\u8868 */\n  morphemes: LanguageSpecificMorpheme[]\n  /** \u5BF9\u9F50\u5206\u6570 */\n  alignmentScore: number\n  /** \u6700\u540E\u66F4\u65B0\u65F6\u95F4 */\n  lastUpdated: string\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9018bda46c61361aabefff4a67723f0009b1649a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2rdfck1oki = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2rdfck1oki();
cov_2rdfck1oki().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2rdfck1oki().s[1]++;
exports.RegisterLevel = exports.ConceptCategory = exports.LanguageCode = exports.SEMANTIC_VECTOR_DIMENSIONS = void 0;
// ============================================================================
// 语言代码和基础类型
// ============================================================================
/**
 * 语义向量维度配置
 */
/* istanbul ignore next */
cov_2rdfck1oki().s[2]++;
exports.SEMANTIC_VECTOR_DIMENSIONS = {
  /** 传统语义向量维度 (兼容v2.0) */
  LEGACY: 20,
  /** 多语言语义向量维度 (基于mBERT/XLM-R) */
  MULTILINGUAL: 512,
  /** 默认使用的维度 */
  DEFAULT: 512
};
/**
 * 支持的语言代码枚举
 */
var LanguageCode;
/* istanbul ignore next */
cov_2rdfck1oki().s[3]++;
(function (LanguageCode) {
  /* istanbul ignore next */
  cov_2rdfck1oki().f[0]++;
  cov_2rdfck1oki().s[4]++;
  /** 中文 (简体) */
  LanguageCode["ZH_CN"] = "zh-CN";
  /** 英文 (美式) */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[5]++;
  LanguageCode["EN_US"] = "en-US";
  /** 日文 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[6]++;
  LanguageCode["JA_JP"] = "ja-JP";
  /** 韩文 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[7]++;
  LanguageCode["KO_KR"] = "ko-KR";
  /** 西班牙文 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[8]++;
  LanguageCode["ES_ES"] = "es-ES";
  /** 法文 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[9]++;
  LanguageCode["FR_FR"] = "fr-FR";
  /** 德文 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[10]++;
  LanguageCode["DE_DE"] = "de-DE";
  /** 阿拉伯文 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[11]++;
  LanguageCode["AR_SA"] = "ar-SA";
})(
/* istanbul ignore next */
(cov_2rdfck1oki().b[0][0]++, LanguageCode) ||
/* istanbul ignore next */
(cov_2rdfck1oki().b[0][1]++, exports.LanguageCode = LanguageCode = {}));
/**
 * 概念类别枚举 (语言无关)
 */
var ConceptCategory;
/* istanbul ignore next */
cov_2rdfck1oki().s[12]++;
(function (ConceptCategory) {
  /* istanbul ignore next */
  cov_2rdfck1oki().f[1]++;
  cov_2rdfck1oki().s[13]++;
  /** 情感概念 */
  ConceptCategory["EMOTIONS"] = "emotions";
  /** 职业概念 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[14]++;
  ConceptCategory["PROFESSIONS"] = "professions";
  /** 特征概念 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[15]++;
  ConceptCategory["CHARACTERISTICS"] = "characteristics";
  /** 物体概念 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[16]++;
  ConceptCategory["OBJECTS"] = "objects";
  /** 动作概念 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[17]++;
  ConceptCategory["ACTIONS"] = "actions";
  /** 抽象概念 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[18]++;
  ConceptCategory["CONCEPTS"] = "concepts";
})(
/* istanbul ignore next */
(cov_2rdfck1oki().b[1][0]++, ConceptCategory) ||
/* istanbul ignore next */
(cov_2rdfck1oki().b[1][1]++, exports.ConceptCategory = ConceptCategory = {}));
/**
 * 语域层次枚举
 */
var RegisterLevel;
/* istanbul ignore next */
cov_2rdfck1oki().s[19]++;
(function (RegisterLevel) {
  /* istanbul ignore next */
  cov_2rdfck1oki().f[2]++;
  cov_2rdfck1oki().s[20]++;
  /** 正式语域 */
  RegisterLevel["FORMAL"] = "formal";
  /** 中性语域 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[21]++;
  RegisterLevel["NEUTRAL"] = "neutral";
  /** 非正式语域 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[22]++;
  RegisterLevel["INFORMAL"] = "informal";
  /** 口语化语域 */
  /* istanbul ignore next */
  cov_2rdfck1oki().s[23]++;
  RegisterLevel["COLLOQUIAL"] = "colloquial";
})(
/* istanbul ignore next */
(cov_2rdfck1oki().b[2][0]++, RegisterLevel) ||
/* istanbul ignore next */
(cov_2rdfck1oki().b[2][1]++, exports.RegisterLevel = RegisterLevel = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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