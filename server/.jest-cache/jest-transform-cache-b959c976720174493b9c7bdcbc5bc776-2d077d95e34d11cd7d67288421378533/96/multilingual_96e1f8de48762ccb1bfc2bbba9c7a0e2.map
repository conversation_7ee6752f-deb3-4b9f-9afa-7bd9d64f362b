{"version": 3, "names": ["cov_2rdfck1oki", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "SEMANTIC_VECTOR_DIMENSIONS", "LEGACY", "MULTILINGUAL", "DEFAULT", "LanguageCode", "ConceptCategory", "RegisterLevel"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/types/multilingual.ts"], "sourcesContent": ["/**\n * 多语种数据模型定义 (v3.0)\n * \n * 基于概念-语言分离架构的多语种数据类型定义\n * 支持8种主流语言的语素生成和跨语言语义对齐\n * \n * <AUTHOR> team\n * @version 3.0.0\n * @created 2025-06-24\n */\n\n// ============================================================================\n// 语言代码和基础类型\n// ============================================================================\n\n/**\n * 语义向量维度配置\n */\nexport const SEMANTIC_VECTOR_DIMENSIONS = {\n  /** 传统语义向量维度 (兼容v2.0) */\n  LEGACY: 20,\n  /** 多语言语义向量维度 (基于mBERT/XLM-R) */\n  MULTILINGUAL: 512,\n  /** 默认使用的维度 */\n  DEFAULT: 512\n} as const\n\n/**\n * 支持的语言代码枚举\n */\nexport enum LanguageCode {\n  /** 中文 (简体) */\n  ZH_CN = 'zh-CN',\n  /** 英文 (美式) */\n  EN_US = 'en-US',\n  /** 日文 */\n  JA_JP = 'ja-JP',\n  /** 韩文 */\n  KO_KR = 'ko-KR',\n  /** 西班牙文 */\n  ES_ES = 'es-ES',\n  /** 法文 */\n  FR_FR = 'fr-FR',\n  /** 德文 */\n  DE_DE = 'de-DE',\n  /** 阿拉伯文 */\n  AR_SA = 'ar-SA'\n}\n\n/**\n * 概念类别枚举 (语言无关)\n */\nexport enum ConceptCategory {\n  /** 情感概念 */\n  EMOTIONS = 'emotions',\n  /** 职业概念 */\n  PROFESSIONS = 'professions',\n  /** 特征概念 */\n  CHARACTERISTICS = 'characteristics',\n  /** 物体概念 */\n  OBJECTS = 'objects',\n  /** 动作概念 */\n  ACTIONS = 'actions',\n  /** 抽象概念 */\n  CONCEPTS = 'concepts'\n}\n\n/**\n * 语域层次枚举\n */\nexport enum RegisterLevel {\n  /** 正式语域 */\n  FORMAL = 'formal',\n  /** 中性语域 */\n  NEUTRAL = 'neutral',\n  /** 非正式语域 */\n  INFORMAL = 'informal',\n  /** 口语化语域 */\n  COLLOQUIAL = 'colloquial'\n}\n\n// ============================================================================\n// 通用概念数据模型\n// ============================================================================\n\n/**\n * 通用语义向量接口\n */\nexport interface UniversalSemanticVector {\n  /** 多语言语义向量 (基于mBERT/XLM-R) */\n  vector: number[] // 长度为 SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL\n  /** 向量模型版本 */\n  model_version: string\n  /** 向量置信度 [0-1] */\n  confidence: number\n  /** 最后更新时间 */\n  updated_at: string\n  /** 兼容性向量 (用于与传统20维向量对齐) */\n  legacy_vector?: number[] // 长度为 SEMANTIC_VECTOR_DIMENSIONS.LEGACY\n}\n\n/**\n * 多维度文化语境接口\n */\nexport interface MultiDimensionalCulturalContext {\n  /** 传统性程度 [0-1] */\n  traditionality: number\n  /** 现代性程度 [0-1] */\n  modernity: number\n  /** 正式性程度 [0-1] */\n  formality: number\n  /** 地域特色程度 [0-1] */\n  regionality: number\n  /** 宗教敏感性 [0-1] */\n  religious_sensitivity: number\n  /** 年龄适宜性标签 */\n  age_appropriateness: string[]\n  /** 文化标签 */\n  cultural_tags: string[]\n}\n\n/**\n * 通用概念接口 - 语言无关的抽象概念\n */\nexport interface UniversalConcept {\n  /** 全局唯一概念ID */\n  concept_id: string\n  /** 语义向量 */\n  semantic_vector: UniversalSemanticVector\n  /** 概念类别 */\n  concept_category: ConceptCategory\n  /** 抽象程度 [0-1] */\n  abstraction_level: number\n  /** 文化中性度 [0-1] */\n  cultural_neutrality: number\n  /** 跨语言稳定性 [0-1] */\n  cross_lingual_stability: number\n  \n  // 认知属性\n  /** 认知负荷 [0-1] */\n  cognitive_load: number\n  /** 记忆性评分 [0-1] */\n  memorability_score: number\n  /** 情感价值 [-1,1] */\n  emotional_valence: number\n  /** 认知属性集合 */\n  cognitive_attributes: {\n    memorability: number\n    cognitive_load: number\n    emotional_valence: number\n  }\n  \n  // 关系映射\n  /** 相关概念ID列表 */\n  related_concepts: string[]\n  /** 层次父概念ID */\n  hierarchical_parent?: string\n  /** 层次子概念ID列表 */\n  hierarchical_children: string[]\n  \n  // 元数据\n  created_at: string\n  updated_at: string\n  version: string\n}\n\n// ============================================================================\n// 语言特定实现数据模型\n// ============================================================================\n\n/**\n * 语音特征接口\n */\nexport interface PhoneticFeatures {\n  /** IPA音标 */\n  ipa_transcription: string\n  /** 音节数量 */\n  syllable_count: number\n  /** 重音模式 (英文等) */\n  stress_pattern?: string\n  /** 声调信息 (中文等) */\n  tone_pattern?: string[]\n  /** 语音和谐性评分 [0-1] */\n  phonetic_harmony: number\n}\n\n/**\n * 词法信息接口\n */\nexport interface MorphologicalInfo {\n  /** 词性标注 (Universal Dependencies) */\n  pos_tag: string\n  /** 词法类型 */\n  morphological_type: string\n  /** 词根信息 */\n  root?: string\n  /** 前缀列表 */\n  prefixes: string[]\n  /** 后缀列表 */\n  suffixes: string[]\n  /** 屈折变化信息 */\n  inflection_info?: Record<string, string>\n}\n\n/**\n * 句法属性接口\n */\nexport interface SyntacticProperties {\n  /** 句法功能 */\n  syntactic_function: string[]\n  /** 搭配限制 */\n  collocation_constraints: string[]\n  /** 语法特征 */\n  grammatical_features: Record<string, string>\n}\n\n/**\n * 地区变体接口\n */\nexport interface RegionalVariant {\n  /** 地区代码 */\n  region_code: string\n  /** 变体文本 */\n  variant_text: string\n  /** 使用频率 [0-1] */\n  usage_frequency: number\n  /** 地区特色程度 [0-1] */\n  regional_specificity: number\n}\n\n/**\n * 语言特定质量评分接口\n */\nexport interface LanguageQualityScores {\n  /** 自然度 [0-1] */\n  naturalness: number\n  /** 流畅度 [0-1] */\n  fluency: number\n  /** 地道性 [0-1] */\n  authenticity: number\n  /** 美学吸引力 [0-1] */\n  aesthetic_appeal: number\n  /** 发音友好度 [0-1] */\n  pronunciation_ease: number\n  /** 记忆性 [0-1] */\n  memorability: number\n  /** 独特性 [0-1] */\n  uniqueness: number\n  /** 实用性 [0-1] */\n  practicality: number\n}\n\n/**\n * 语言特定语素接口 - 概念在特定语言中的实现\n */\nexport interface LanguageSpecificMorpheme {\n  /** 语素唯一ID */\n  morpheme_id: string\n  /** 对应的通用概念ID */\n  concept_id: string\n  /** 语言代码 */\n  language: LanguageCode\n  \n  // 语言形式\n  /** 文本形式 */\n  text: string\n  /** 替代形式列表 */\n  alternative_forms: string[]\n  \n  // 语言学属性\n  /** 语音特征 */\n  phonetic_features: PhoneticFeatures\n  /** 词法信息 */\n  morphological_info: MorphologicalInfo\n  /** 句法属性 */\n  syntactic_properties: SyntacticProperties\n  \n  // 文化适配\n  /** 文化语境 */\n  cultural_context: MultiDimensionalCulturalContext\n  /** 地区变体 */\n  regional_variants: RegionalVariant[]\n  /** 语域层次 */\n  register_level: RegisterLevel\n  \n  // 质量指标\n  /** 语言特定质量评分 */\n  language_quality_scores: LanguageQualityScores\n  /** 文化适宜性 [0-1] */\n  cultural_appropriateness: number\n  /** 母语者评分 [0-1] */\n  native_speaker_rating: number\n  \n  // 使用统计\n  /** 使用频率 [0-1] */\n  usage_frequency: number\n  /** 流行度趋势 */\n  popularity_trend: number\n  \n  // 元数据\n  created_at: string\n  updated_at: string\n  version: string\n  /** 数据来源 */\n  source: string\n  /** 验证状态 */\n  validation_status: 'pending' | 'validated' | 'rejected'\n}\n\n// ============================================================================\n// 跨语言对齐和质量评估\n// ============================================================================\n\n/**\n * 语义对齐结果接口\n */\nexport interface AlignmentResult {\n  /** 源概念 */\n  source_concept: UniversalConcept\n  /** 目标语言 */\n  target_language: LanguageCode\n  /** 对齐的语素列表 */\n  aligned_morphemes: LanguageSpecificMorpheme[]\n  /** 对齐置信度 [0-1] */\n  confidence_score: number\n  /** 语义距离 [0-1] */\n  semantic_distance: number\n  /** 文化适配说明 */\n  cultural_adaptation_notes: string[]\n  /** 对齐时间戳 */\n  aligned_at: string\n}\n\n/**\n * 批量对齐结果接口\n */\nexport interface BatchAlignmentResult {\n  /** 对齐结果列表 */\n  alignments: AlignmentResult[]\n  /** 总体成功率 [0-1] */\n  overall_success_rate: number\n  /** 平均置信度 [0-1] */\n  average_confidence: number\n  /** 处理时间 (毫秒) */\n  processing_time: number\n  /** 错误信息 */\n  errors: string[]\n}\n\n/**\n * 跨语言一致性报告接口\n */\nexport interface ConsistencyReport {\n  /** 概念ID */\n  concept_id: string\n  /** 检查的语言列表 */\n  languages: LanguageCode[]\n  /** 语义一致性评分 [0-1] */\n  semantic_consistency: number\n  /** 文化一致性评分 [0-1] */\n  cultural_consistency: number\n  /** 质量一致性评分 [0-1] */\n  quality_consistency: number\n  /** 不一致项详情 */\n  inconsistencies: {\n    type: 'semantic' | 'cultural' | 'quality'\n    description: string\n    affected_languages: LanguageCode[]\n    severity: 'low' | 'medium' | 'high'\n  }[]\n  /** 建议改进措施 */\n  improvement_suggestions: string[]\n  /** 检查时间戳 */\n  checked_at: string\n}\n\n/**\n * 通用质量评估接口\n */\nexport interface UniversalQualityAssessment {\n  /** 概念ID */\n  concept_id: string\n  /** 创意性评分 [0-1] */\n  creativity: number\n  /** 记忆性评分 [0-1] */\n  memorability: number\n  /** 独特性评分 [0-1] */\n  uniqueness: number\n  /** 语义连贯性评分 [0-1] */\n  semantic_coherence: number\n  /** 跨语言适应性评分 [0-1] */\n  cross_lingual_adaptability: number\n  /** 综合质量评分 [0-1] */\n  overall_quality: number\n  /** 评估时间戳 */\n  assessed_at: string\n}\n\n/**\n * 多语种用户名生成请求接口\n */\nexport interface MultilingualGenerationRequest {\n  /** 目标语言 */\n  target_language: LanguageCode\n  /** 概念偏好 */\n  concept_preferences?: {\n    categories: ConceptCategory[]\n    cultural_context?: Partial<MultiDimensionalCulturalContext>\n    abstraction_level?: [number, number] // 范围\n    emotional_valence?: [number, number] // 范围\n  }\n  /** 跨语言选项 */\n  cross_lingual_options?: {\n    /** 是否启用跨语言对比 */\n    enable_comparison: boolean\n    /** 对比语言列表 */\n    comparison_languages: LanguageCode[]\n    /** 语义一致性要求 [0-1] */\n    semantic_consistency_threshold: number\n  }\n  /** 生成数量 */\n  count: number\n  /** 质量阈值 [0-1] */\n  quality_threshold: number\n}\n\n/**\n * 多语种用户名生成结果接口\n */\nexport interface MultilingualGenerationResult {\n  /** 目标语言 */\n  target_language: LanguageCode\n  /** 生成的用户名列表 */\n  usernames: {\n    /** 用户名文本 */\n    text: string\n    /** 使用的语素组合 */\n    morpheme_combination: LanguageSpecificMorpheme[]\n    /** 对应的概念组合 */\n    concept_combination: UniversalConcept[]\n    /** 质量评估 */\n    quality_assessment: UniversalQualityAssessment\n    /** 跨语言对比 (如果启用) */\n    cross_lingual_comparison?: {\n      language: LanguageCode\n      equivalent_text: string\n      semantic_similarity: number\n    }[]\n  }[]\n  /** 生成统计 */\n  generation_stats: {\n    /** 请求处理时间 (毫秒) */\n    processing_time: number\n    /** 成功生成数量 */\n    successful_count: number\n    /** 平均质量评分 */\n    average_quality: number\n    /** 语义多样性评分 [0-1] */\n    semantic_diversity: number\n  }\n  /** 生成时间戳 */\n  generated_at: string\n}\n\n// ============================================================================\n// 数据迁移和兼容性\n// ============================================================================\n\n/**\n * v2.0到v3.0数据迁移映射接口\n */\nexport interface MigrationMapping {\n  /** v2.0语素ID */\n  v2_morpheme_id: string\n  /** 对应的v3.0概念ID */\n  v3_concept_id: string\n  /** 对应的v3.0语素ID */\n  v3_morpheme_id: string\n  /** 迁移置信度 [0-1] */\n  migration_confidence: number\n  /** 需要人工审核 */\n  requires_manual_review: boolean\n  /** 迁移说明 */\n  migration_notes: string[]\n}\n\n/**\n * 向后兼容性配置接口\n */\nexport interface BackwardCompatibilityConfig {\n  /** 是否启用v2.0兼容模式 */\n  enable_v2_compatibility: boolean\n  /** v2.0 API端点映射 */\n  v2_api_mappings: Record<string, string>\n  /** 数据格式转换规则 */\n  format_conversion_rules: Record<string, any>\n  /** 兼容性警告阈值 */\n  compatibility_warning_threshold: number\n}\n\n/**\n * 概念-语素映射接口\n */\nexport interface ConceptMorphemeMapping {\n  /** 概念ID */\n  conceptId: string\n  /** 对应的语素列表 */\n  morphemes: LanguageSpecificMorpheme[]\n  /** 对齐分数 */\n  alignmentScore: number\n  /** 最后更新时间 */\n  lastUpdated: string\n}\n"], "mappings": ";;AAAA;;;;;;;;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAU,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAA3B,IAAA;EAAA;EAAA,IAAA4B,QAAA,GAAA3B,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAyB,QAAA,CAAA7B,IAAA,KAAA6B,QAAA,CAAA7B,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAA4B,QAAA,CAAA7B,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAyB,cAAA,GAAAD,QAAA,CAAA7B,IAAA;EAAA;IAaA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAA+B,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAA/B,cAAA;AAAAA,cAAA,GAAAmB,CAAA;;;;;;;AAFA;AACA;AACA;AAEA;;;AAAA;AAAAnB,cAAA,GAAAmB,CAAA;AAGaa,OAAA,CAAAC,0BAA0B,GAAG;EACxC;EACAC,MAAM,EAAE,EAAE;EACV;EACAC,YAAY,EAAE,GAAG;EACjB;EACAC,OAAO,EAAE;CACD;AAEV;;;AAGA,IAAYC,YAiBX;AAAA;AAAArC,cAAA,GAAAmB,CAAA;AAjBD,WAAYkB,YAAY;EAAA;EAAArC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACtB;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;EACf;EAAA;EAAArC,cAAA,GAAAmB,CAAA;EACAkB,YAAA,mBAAe;AACjB,CAAC;AAjBW;AAAA,CAAArC,cAAA,GAAAqB,CAAA,UAAAgB,YAAY;AAAA;AAAA,CAAArC,cAAA,GAAAqB,CAAA,UAAAW,OAAA,CAAAK,YAAA,GAAZA,YAAY;AAmBxB;;;AAGA,IAAYC,eAaX;AAAA;AAAAtC,cAAA,GAAAmB,CAAA;AAbD,WAAYmB,eAAe;EAAA;EAAAtC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACzB;EACAmB,eAAA,yBAAqB;EACrB;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACAmB,eAAA,+BAA2B;EAC3B;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACAmB,eAAA,uCAAmC;EACnC;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACAmB,eAAA,uBAAmB;EACnB;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACAmB,eAAA,uBAAmB;EACnB;EAAA;EAAAtC,cAAA,GAAAmB,CAAA;EACAmB,eAAA,yBAAqB;AACvB,CAAC;AAbW;AAAA,CAAAtC,cAAA,GAAAqB,CAAA,UAAAiB,eAAe;AAAA;AAAA,CAAAtC,cAAA,GAAAqB,CAAA,UAAAW,OAAA,CAAAM,eAAA,GAAfA,eAAe;AAe3B;;;AAGA,IAAYC,aASX;AAAA;AAAAvC,cAAA,GAAAmB,CAAA;AATD,WAAYoB,aAAa;EAAA;EAAAvC,cAAA,GAAAoB,CAAA;EAAApB,cAAA,GAAAmB,CAAA;EACvB;EACAoB,aAAA,qBAAiB;EACjB;EAAA;EAAAvC,cAAA,GAAAmB,CAAA;EACAoB,aAAA,uBAAmB;EACnB;EAAA;EAAAvC,cAAA,GAAAmB,CAAA;EACAoB,aAAA,yBAAqB;EACrB;EAAA;EAAAvC,cAAA,GAAAmB,CAAA;EACAoB,aAAA,6BAAyB;AAC3B,CAAC;AATW;AAAA,CAAAvC,cAAA,GAAAqB,CAAA,UAAAkB,aAAa;AAAA;AAAA,CAAAvC,cAAA,GAAAqB,CAAA,UAAAW,OAAA,CAAAO,aAAA,GAAbA,aAAa", "ignoreList": []}