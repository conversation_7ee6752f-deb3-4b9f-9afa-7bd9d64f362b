{"version": 3, "names": ["cov_gpvvqkfzd", "actualCoverage", "CulturalContext", "LanguageCode", "STYLE_PREFERENCES", "s", "API", "VERSION", "REQUEST_ID_PREFIX", "DEFAULT_LANGUAGE", "ZH_CN", "SUPPORTED_LANGUAGES", "EN_US", "JA_JP", "KO_KR", "ES_ES", "FR_FR", "DE_DE", "AR_SA", "GENERATION", "MAX_GENERATE_COUNT", "DEFAULT_GENERATE_COUNT", "DEFAULT_CREATIVITY_LEVEL", "DEFAULT_QUALITY_THRESHOLD", "DEFAULT_LENGTH_PREFERENCE", "DEFAULT_CULTURAL_PREFERENCE", "NEUTRAL", "DEFAULT_STYLE_PREFERENCE", "MAX_RETRY_ATTEMPTS", "REQUEST_TIMEOUT", "ENABLE_CROSS_LINGUAL", "SEMANTIC_CONSISTENCY_THRESHOLD", "VALIDATION", "COUNT", "MIN", "MAX", "CREATIVITY_LEVEL", "QUALITY_THRESHOLD", "SEMANTIC_CONSISTENCY", "CULTURAL_SENSITIVITY", "ERROR_CODES", "INVALID_PARAMETERS", "MISSING_REQUIRED_FIELD", "VALIDATION_FAILED", "GENERATION_FAILED", "INTERNAL_ERROR", "UNSUPPORTED_LANGUAGE", "INVALID_MULTILINGUAL_OPTIONS", "CULTURAL_PREFERENCES", "LENGTH_PREFERENCES", "CACHE", "DEFAULT_TTL", "MAX_ITEMS", "KEY_PREFIX", "PERFORMANCE", "SLOW_QUERY_THRESHOLD", "MONITORING_SAMPLE_RATE", "ERROR_MESSAGES", "INVALID_COUNT", "min", "max", "f", "INVALID_CREATIVITY_LEVEL", "INVALID_QUALITY_THRESHOLD", "INVALID_CULTURAL_PREFERENCE", "validValues", "join", "INVALID_STYLE_PREFERENCE", "PATTERNS_MUST_BE_ARRAY", "INVALID_LANGUAGE", "validLanguages", "INVALID_SEMANTIC_CONSISTENCY", "INVALID_CULTURAL_SENSITIVITY", "UNSUPPORTED_LANGUAGE_COMBINATION", "CONCEPT_PREFERENCES_INVALID", "INTERNAL", "MULTILINGUAL_ENGINE_ERROR", "CROSS_LINGUAL_MAPPING_FAILED", "SEMANTIC_ALIGNMENT_ERROR"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts"], "sourcesContent": ["/**\n * 应用常量配置\n *\n * 包含API配置、生成参数、验证规则、缓存设置等全局常量\n *\n * @fileoverview 应用常量配置文件\n * @version 3.0.0\n * @since 2025-06-24\n * <AUTHOR> team\n */\n\nimport { CulturalContext } from \"../types/core\"\nimport { LanguageCode } from \"../types/multilingual\"\n\nexport const STYLE_PREFERENCES = ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional'] as const\n\n// API 相关常量\nexport const API = {\n  // API 版本 (v3.0多语种支持)\n  VERSION: '3.0.0-multilingual',\n  // 请求ID前缀\n  REQUEST_ID_PREFIX: 'req_',\n  // 默认语言\n  DEFAULT_LANGUAGE: LanguageCode.ZH_CN,\n  // 支持的语言列表\n  SUPPORTED_LANGUAGES: [\n    LanguageCode.ZH_CN,\n    LanguageCode.EN_US,\n    LanguageCode.JA_JP,\n    LanguageCode.KO_KR,\n    LanguageCode.ES_ES,\n    LanguageCode.FR_FR,\n    LanguageCode.DE_DE,\n    LanguageCode.AR_SA\n  ] as const\n} as const\n\n// 生成相关常量 (v3.0多语种优化)\nexport const GENERATION = {\n  // 每次请求最大生成数量 (v3.0简化为10)\n  MAX_GENERATE_COUNT: 10,\n  // 默认生成数量\n  DEFAULT_GENERATE_COUNT: 5,\n  // 默认创意程度\n  DEFAULT_CREATIVITY_LEVEL: 0.7,\n  // 默认质量阈值\n  DEFAULT_QUALITY_THRESHOLD: 0.6,\n  // 默认长度偏好\n  DEFAULT_LENGTH_PREFERENCE: 'medium' as const,\n  // 默认文化偏好\n  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,\n  // 默认风格偏好\n  DEFAULT_STYLE_PREFERENCE: 'humorous' as const,\n  // 默认最大重试次数\n  MAX_RETRY_ATTEMPTS: 3,\n  // 请求超时时间（毫秒）\n  REQUEST_TIMEOUT: 15000,\n  // v3.0多语种新增配置\n  DEFAULT_LANGUAGE: LanguageCode.ZH_CN,\n  ENABLE_CROSS_LINGUAL: false,\n  SEMANTIC_CONSISTENCY_THRESHOLD: 0.8\n} as const\n\n// 验证相关常量 (v3.0多语种扩展)\nexport const VALIDATION = {\n  // 生成数量范围 (v3.0调整为1-10)\n  COUNT: {\n    MIN: 1,\n    MAX: 10\n  },\n  // 创意程度范围\n  CREATIVITY_LEVEL: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // 质量阈值范围\n  QUALITY_THRESHOLD: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // 语义一致性范围 (v3.0新增)\n  SEMANTIC_CONSISTENCY: {\n    MIN: 0.0,\n    MAX: 1.0\n  },\n  // 文化敏感度范围 (v3.0新增)\n  CULTURAL_SENSITIVITY: {\n    MIN: 0.0,\n    MAX: 1.0\n  },\n  // 默认错误代码\n  ERROR_CODES: {\n    INVALID_PARAMETERS: 'E1001',\n    MISSING_REQUIRED_FIELD: 'E1002',\n    VALIDATION_FAILED: 'E1003',\n    GENERATION_FAILED: 'E3001',\n    INTERNAL_ERROR: 'E5001',\n    UNSUPPORTED_LANGUAGE: 'E1004',  // v3.0新增\n    INVALID_MULTILINGUAL_OPTIONS: 'E1005'  // v3.0新增\n  },\n  // 支持的语言 (v3.0新增)\n  SUPPORTED_LANGUAGES: API.SUPPORTED_LANGUAGES,\n  // 文化偏好选项\n  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'] as const,\n  // 风格偏好选项\n  STYLE_PREFERENCES: STYLE_PREFERENCES,\n  // 长度偏好选项\n  LENGTH_PREFERENCES: ['short', 'medium', 'long'] as const\n} as const\n\n// 缓存相关常量\nexport const CACHE = {\n  // 默认缓存时间（秒）\n  DEFAULT_TTL: 3600,\n  // 最大缓存条目数\n  MAX_ITEMS: 1000,\n  // 缓存键前缀\n  KEY_PREFIX: 'namer:'\n} as const\n\n// 性能相关常量\nexport const PERFORMANCE = {\n  // 慢查询阈值（毫秒）\n  SLOW_QUERY_THRESHOLD: 500,\n  // 监控采样率（0-1）\n  MONITORING_SAMPLE_RATE: 0.1\n} as const\n\n// 错误消息 (v3.0多语种扩展)\nexport const ERROR_MESSAGES = {\n  VALIDATION: {\n    INVALID_COUNT: (min: number, max: number) => `count must be a number between ${min} and ${max}`,\n    INVALID_CREATIVITY_LEVEL: (min: number, max: number) => `creativity_level must be a number between ${min} and ${max}`,\n    INVALID_QUALITY_THRESHOLD: (min: number, max: number) => `quality_threshold must be a number between ${min} and ${max}`,\n    INVALID_CULTURAL_PREFERENCE: (validValues: readonly string[]) => `cultural_preference must be one of: ${validValues.join(', ')}`,\n    INVALID_STYLE_PREFERENCE: (validValues: readonly string[]) => `style_preference must be one of: ${validValues.join(', ')}`,\n    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array',\n    // v3.0多语种新增错误消息\n    INVALID_LANGUAGE: (validLanguages: readonly string[]) => `language must be one of: ${validLanguages.join(', ')}`,\n    INVALID_SEMANTIC_CONSISTENCY: (min: number, max: number) => `semantic_consistency must be a number between ${min} and ${max}`,\n    INVALID_CULTURAL_SENSITIVITY: (min: number, max: number) => `cultural_sensitivity must be a number between ${min} and ${max}`,\n    UNSUPPORTED_LANGUAGE_COMBINATION: 'The specified source and target language combination is not supported',\n    INVALID_MULTILINGUAL_OPTIONS: 'Invalid multilingual options configuration',\n    CONCEPT_PREFERENCES_INVALID: 'concept_preferences must be a valid object with supported categories'\n  },\n  INTERNAL: {\n    GENERATION_FAILED: 'Failed to generate usernames',\n    VALIDATION_FAILED: 'Request validation failed',\n    INTERNAL_ERROR: 'Internal server error',\n    // v3.0多语种新增内部错误\n    MULTILINGUAL_ENGINE_ERROR: 'Multilingual generation engine error',\n    CROSS_LINGUAL_MAPPING_FAILED: 'Cross-lingual concept mapping failed',\n    SEMANTIC_ALIGNMENT_ERROR: 'Semantic alignment calculation error'\n  }\n} as const\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkBE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAlBF;;;;;;;;;;AAWA,SAASE,eAAe,QAAQ,eAAe;AAC/C,SAASC,YAAY,QAAQ,uBAAuB;AAEpD,OAAO,MAAMC,iBAAiB;AAAA;AAAA,CAAAJ,aAAA,GAAAK,CAAA,OAAG,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAU;AAExH;AACA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAN,aAAA,GAAAK,CAAA,OAAG;EACjB;EACAE,OAAO,EAAE,oBAAoB;EAC7B;EACAC,iBAAiB,EAAE,MAAM;EACzB;EACAC,gBAAgB,EAAEN,YAAY,CAACO,KAAK;EACpC;EACAC,mBAAmB,EAAE,CACnBR,YAAY,CAACO,KAAK,EAClBP,YAAY,CAACS,KAAK,EAClBT,YAAY,CAACU,KAAK,EAClBV,YAAY,CAACW,KAAK,EAClBX,YAAY,CAACY,KAAK,EAClBZ,YAAY,CAACa,KAAK,EAClBb,YAAY,CAACc,KAAK,EAClBd,YAAY,CAACe,KAAK;CAEZ;AAEV;AACA,OAAO,MAAMC,UAAU;AAAA;AAAA,CAAAnB,aAAA,GAAAK,CAAA,OAAG;EACxB;EACAe,kBAAkB,EAAE,EAAE;EACtB;EACAC,sBAAsB,EAAE,CAAC;EACzB;EACAC,wBAAwB,EAAE,GAAG;EAC7B;EACAC,yBAAyB,EAAE,GAAG;EAC9B;EACAC,yBAAyB,EAAE,QAAiB;EAC5C;EACAC,2BAA2B,EAAEvB,eAAe,CAACwB,OAAO;EACpD;EACAC,wBAAwB,EAAE,UAAmB;EAC7C;EACAC,kBAAkB,EAAE,CAAC;EACrB;EACAC,eAAe,EAAE,KAAK;EACtB;EACApB,gBAAgB,EAAEN,YAAY,CAACO,KAAK;EACpCoB,oBAAoB,EAAE,KAAK;EAC3BC,8BAA8B,EAAE;CACxB;AAEV;AACA,OAAO,MAAMC,UAAU;AAAA;AAAA,CAAAhC,aAAA,GAAAK,CAAA,OAAG;EACxB;EACA4B,KAAK,EAAE;IACLC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE;GACN;EACD;EACAC,gBAAgB,EAAE;IAChBF,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE;GACN;EACD;EACAE,iBAAiB,EAAE;IACjBH,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE;GACN;EACD;EACAG,oBAAoB,EAAE;IACpBJ,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE;GACN;EACD;EACAI,oBAAoB,EAAE;IACpBL,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE;GACN;EACD;EACAK,WAAW,EAAE;IACXC,kBAAkB,EAAE,OAAO;IAC3BC,sBAAsB,EAAE,OAAO;IAC/BC,iBAAiB,EAAE,OAAO;IAC1BC,iBAAiB,EAAE,OAAO;IAC1BC,cAAc,EAAE,OAAO;IACvBC,oBAAoB,EAAE,OAAO;IAAG;IAChCC,4BAA4B,EAAE,OAAO,CAAE;GACxC;EACD;EACApC,mBAAmB,EAAEL,GAAG,CAACK,mBAAmB;EAC5C;EACAqC,oBAAoB,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAU;EACxE;EACA5C,iBAAiB,EAAEA,iBAAiB;EACpC;EACA6C,kBAAkB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM;CACtC;AAEV;AACA,OAAO,MAAMC,KAAK;AAAA;AAAA,CAAAlD,aAAA,GAAAK,CAAA,OAAG;EACnB;EACA8C,WAAW,EAAE,IAAI;EACjB;EACAC,SAAS,EAAE,IAAI;EACf;EACAC,UAAU,EAAE;CACJ;AAEV;AACA,OAAO,MAAMC,WAAW;AAAA;AAAA,CAAAtD,aAAA,GAAAK,CAAA,OAAG;EACzB;EACAkD,oBAAoB,EAAE,GAAG;EACzB;EACAC,sBAAsB,EAAE;CAChB;AAEV;AACA,OAAO,MAAMC,cAAc;AAAA;AAAA,CAAAzD,aAAA,GAAAK,CAAA,OAAG;EAC5B2B,UAAU,EAAE;IACV0B,aAAa,EAAEA,CAACC,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5D,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,yCAAkCsD,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IAC/FE,wBAAwB,EAAEA,CAACH,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5D,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,oDAA6CsD,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IACrHG,yBAAyB,EAAEA,CAACJ,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5D,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,qDAA8CsD,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IACvHI,2BAA2B,EAAGC,WAA8B,IAAK;MAAA;MAAAjE,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,8CAAuC4D,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IAAF,CAAE;IAChIC,wBAAwB,EAAGF,WAA8B,IAAK;MAAA;MAAAjE,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,2CAAoC4D,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IAAF,CAAE;IAC1HE,sBAAsB,EAAE,2BAA2B;IACnD;IACAC,gBAAgB,EAAGC,cAAiC,IAAK;MAAA;MAAAtE,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,mCAA4BiE,cAAc,CAACJ,IAAI,CAAC,IAAI,CAAC,EAAE;IAAF,CAAE;IAChHK,4BAA4B,EAAEA,CAACZ,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5D,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,wDAAiDsD,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IAC7HY,4BAA4B,EAAEA,CAACb,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5D,aAAA,GAAA6D,CAAA;MAAA7D,aAAA,GAAAK,CAAA;MAAA,wDAAiDsD,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IAC7Ha,gCAAgC,EAAE,uEAAuE;IACzG1B,4BAA4B,EAAE,4CAA4C;IAC1E2B,2BAA2B,EAAE;GAC9B;EACDC,QAAQ,EAAE;IACR/B,iBAAiB,EAAE,8BAA8B;IACjDD,iBAAiB,EAAE,2BAA2B;IAC9CE,cAAc,EAAE,uBAAuB;IACvC;IACA+B,yBAAyB,EAAE,sCAAsC;IACjEC,4BAA4B,EAAE,sCAAsC;IACpEC,wBAAwB,EAAE;;CAEpB", "ignoreList": []}