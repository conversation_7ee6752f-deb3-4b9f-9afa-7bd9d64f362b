{"file": "/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,2CAAoD;AACpD,8CAAkD;AAClD,8DAAkG;AAElG,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,IAAA,gBAAM,EAAC,yBAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC/C,IAAA,gBAAM,EAAC,yBAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,yBAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,IAAA,gBAAM,EAAC,+BAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAA,gBAAM,EAAC,+BAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,+BAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC/C,IAAA,gBAAM,EAAC,+BAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,IAAA,gBAAM,EAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAClD,IAAA,gBAAM,EAAC,4CAA0B,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACzD,IAAA,gBAAM,EAAC,4CAA0B,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,YAAE,EAAC,cAAc,EAAE,GAAG,EAAE;YACtB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,yBAAe,CAAC,CAAA;YACvD,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC5C,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;QAC/C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,eAAe,EAAE,GAAG,EAAE;YACvB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,8BAAY,CAAC,CAAA;YACjD,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YACrC,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,+BAAa,CAAC,CAAA;YACnD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YACtC,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC1C,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;YAC3C,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YAC5C,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;QAChD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,cAAc,EAAE,GAAG,EAAE;YACtB,eAAe;YACf,IAAA,gBAAM,EAAC,4CAA0B,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4CAA0B,CAAC,YAAY,CAAC,CAAA;YACxF,IAAA,gBAAM,EAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,4CAA0B,CAAC,YAAY,CAAC,CAAA;YAE/F,YAAY;YACZ,IAAA,gBAAM,EAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;YAC5D,IAAA,gBAAM,EAAC,4CAA0B,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;YAClE,IAAA,gBAAM,EAAC,4CAA0B,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;QAC/D,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,eAAe,EAAE,GAAG,EAAE;YACvB,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC3E,MAAM,kBAAkB,GAAG,IAAI,KAAK,CAAC,4CAA0B,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAEvF,IAAA,gBAAM,EAAC,YAAY,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;YACrC,IAAA,gBAAM,EAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;YAE5C,cAAc;YACd,IAAA,gBAAM,EAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACrD,IAAA,gBAAM,EAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC7D,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,kBAAkB,GAAG;gBACzB,CAAC,8BAAY,CAAC,KAAK,CAAC,EAAE,IAAI;gBAC1B,CAAC,8BAAY,CAAC,KAAK,CAAC,EAAE,MAAM;gBAC5B,CAAC,8BAAY,CAAC,KAAK,CAAC,EAAE,KAAK;aAC5B,CAAA;YAED,IAAA,gBAAM,EAAC,kBAAkB,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACzD,IAAA,gBAAM,EAAC,kBAAkB,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3D,IAAA,gBAAM,EAAC,kBAAkB,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5D,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,kBAAkB,GAAG;gBACzB,cAAc,EAAE,GAAG;gBACnB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG;gBACd,WAAW,EAAE,GAAG;gBAChB,qBAAqB,EAAE,GAAG;gBAC1B,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;gBACtC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;aACtC,CAAA;YAED,SAAS;YACT,IAAA,gBAAM,EAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;YACnE,IAAA,gBAAM,EAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;YAChE,IAAA,gBAAM,EAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;YAC9D,IAAA,gBAAM,EAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;YAE3D,SAAS;YACT,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxE,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpE,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,MAAM,gBAAgB,GAAG;gBACvB,iBAAiB,EAAE,QAAQ;gBAC3B,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC,GAAG,CAAC;gBACnB,gBAAgB,EAAE,GAAG;aACtB,CAAA;YAED,IAAA,gBAAM,EAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;YAC1D,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC/D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;YACnE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;QACxB,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,iBAAiB,GAAG;gBACxB,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,GAAG;gBACjB,YAAY,EAAE,GAAG;gBACjB,gBAAgB,EAAE,GAAG;gBACrB,kBAAkB,EAAE,GAAG;gBACvB,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,GAAG;gBACjB,iBAAiB,EAAE,GAAG;aACvB,CAAA;YAED,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC/C,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;gBACvC,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACtC,CAAC,CAAC,CAAA;YAEF,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,MAAM,UAAU,GAAG;gBACjB,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,GAAG;gBACjB,YAAY,EAAE,GAAG;gBACjB,gBAAgB,EAAE,GAAG;gBACrB,kBAAkB,EAAE,GAAG;gBACvB,UAAU,EAAE,GAAG;gBACf,YAAY,EAAE,GAAG;gBACjB,iBAAiB,EAAE,GAAG;aACvB,CAAA;YAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;YACpF,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;YACtC,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YACpC,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QACnC,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,qCAAqC;YACrC,MAAM,oBAAoB,GAAG,yBAAe,CAAC,MAAM,CAAA;YACnD,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,yBAAe,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;QACxE,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,SAAS,EAAE,GAAG,EAAE;YACjB,aAAa;YACb,MAAM,QAAQ,GAAG,CAAC,CAAA;YAClB,MAAM,QAAQ,GAAG,CAAC,CAAA;YAElB,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;YAC1C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACvC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;YAC1C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,MAAM,WAAW,GAAwB,EAAE,CAAA;YAE3C,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YAClC,IAAA,gBAAM,EAAC,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACzC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;QAClD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA", "names": [], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts"], "sourcesContent": ["/**\n * 基础功能测试\n * \n * 测试核心类型定义和基本功能的正确性\n */\n\nimport { describe, it, expect } from '@jest/globals'\nimport { CulturalContext } from '../types/core.js'\nimport { LanguageCode, RegisterLevel, SEMANTIC_VECTOR_DIMENSIONS } from '../types/multilingual.js'\n\ndescribe('基础功能测试', () => {\n  describe('类型定义测试', () => {\n    it('应该正确定义 CulturalContext 枚举', () => {\n      expect(CulturalContext.ANCIENT).toBe('ancient')\n      expect(CulturalContext.MODERN).toBe('modern')\n      expect(CulturalContext.NEUTRAL).toBe('neutral')\n    })\n\n    it('应该正确定义 LanguageCode 枚举', () => {\n      expect(LanguageCode.ZH_CN).toBe('zh-CN')\n      expect(LanguageCode.EN_US).toBe('en-US')\n      expect(LanguageCode.JA_JP).toBe('ja-JP')\n      expect(LanguageCode.KO_KR).toBe('ko-KR')\n      expect(LanguageCode.ES_ES).toBe('es-ES')\n      expect(LanguageCode.FR_FR).toBe('fr-FR')\n      expect(LanguageCode.DE_DE).toBe('de-DE')\n      expect(LanguageCode.AR_SA).toBe('ar-SA')\n    })\n\n    it('应该正确定义 RegisterLevel 枚举', () => {\n      expect(RegisterLevel.FORMAL).toBe('FORMAL')\n      expect(RegisterLevel.NEUTRAL).toBe('NEUTRAL')\n      expect(RegisterLevel.INFORMAL).toBe('INFORMAL')\n      expect(RegisterLevel.COLLOQUIAL).toBe('COLLOQUIAL')\n    })\n\n    it('应该正确定义语义向量维度常量', () => {\n      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBe(20)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBe(512)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(512)\n    })\n  })\n\n  describe('类型兼容性测试', () => {\n    it('应该支持枚举值的类型检查', () => {\n      const culturalContexts = Object.values(CulturalContext)\n      expect(culturalContexts).toHaveLength(3)\n      expect(culturalContexts).toContain('ancient')\n      expect(culturalContexts).toContain('modern')\n      expect(culturalContexts).toContain('neutral')\n    })\n\n    it('应该支持语言代码的类型检查', () => {\n      const languageCodes = Object.values(LanguageCode)\n      expect(languageCodes).toHaveLength(8)\n      expect(languageCodes).toContain('zh-CN')\n      expect(languageCodes).toContain('en-US')\n    })\n\n    it('应该支持寄存器级别的类型检查', () => {\n      const registerLevels = Object.values(RegisterLevel)\n      expect(registerLevels).toHaveLength(4)\n      expect(registerLevels).toContain('FORMAL')\n      expect(registerLevels).toContain('NEUTRAL')\n      expect(registerLevels).toContain('INFORMAL')\n      expect(registerLevels).toContain('COLLOQUIAL')\n    })\n  })\n\n  describe('配置常量测试', () => {\n    it('应该正确配置语义向量维度', () => {\n      // 测试向量维度配置的一致性\n      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeLessThan(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)\n      \n      // 测试维度值的合理性\n      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeGreaterThan(0)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBeGreaterThan(0)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBeGreaterThan(0)\n    })\n\n    it('应该支持向量维度的数学运算', () => {\n      const legacyVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).fill(0.1)\n      const multilingualVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).fill(0.1)\n      \n      expect(legacyVector).toHaveLength(20)\n      expect(multilingualVector).toHaveLength(512)\n      \n      // 测试向量的基本数学属性\n      expect(legacyVector.every(v => v === 0.1)).toBe(true)\n      expect(multilingualVector.every(v => v === 0.1)).toBe(true)\n    })\n  })\n\n  describe('数据结构验证', () => {\n    it('应该支持多语言标签映射', () => {\n      const multilingualLabels = {\n        [LanguageCode.ZH_CN]: '测试',\n        [LanguageCode.EN_US]: 'test',\n        [LanguageCode.JA_JP]: 'テスト'\n      }\n      \n      expect(multilingualLabels[LanguageCode.ZH_CN]).toBe('测试')\n      expect(multilingualLabels[LanguageCode.EN_US]).toBe('test')\n      expect(multilingualLabels[LanguageCode.JA_JP]).toBe('テスト')\n    })\n\n    it('应该支持文化适应性配置', () => {\n      const culturalAdaptation = {\n        traditionality: 0.6,\n        modernity: 0.8,\n        formality: 0.5,\n        regionality: 0.7,\n        religious_sensitivity: 0.3,\n        age_appropriateness: ['adult', 'teen'],\n        cultural_tags: ['positive', 'modern']\n      }\n      \n      // 验证数值范围\n      expect(culturalAdaptation.traditionality).toBeGreaterThanOrEqual(0)\n      expect(culturalAdaptation.traditionality).toBeLessThanOrEqual(1)\n      expect(culturalAdaptation.modernity).toBeGreaterThanOrEqual(0)\n      expect(culturalAdaptation.modernity).toBeLessThanOrEqual(1)\n      \n      // 验证数组属性\n      expect(Array.isArray(culturalAdaptation.age_appropriateness)).toBe(true)\n      expect(Array.isArray(culturalAdaptation.cultural_tags)).toBe(true)\n    })\n\n    it('应该支持语音特征配置', () => {\n      const phoneticFeatures = {\n        ipa_transcription: '/test/',\n        syllable_count: 1,\n        tone_pattern: ['1'],\n        phonetic_harmony: 0.8\n      }\n      \n      expect(phoneticFeatures.ipa_transcription).toMatch(/^\\/.*\\/$/);\n      expect(phoneticFeatures.syllable_count).toBeGreaterThan(0)\n      expect(Array.isArray(phoneticFeatures.tone_pattern)).toBe(true)\n      expect(phoneticFeatures.phonetic_harmony).toBeGreaterThanOrEqual(0)\n      expect(phoneticFeatures.phonetic_harmony).toBeLessThanOrEqual(1)\n    })\n  })\n\n  describe('质量评估系统测试', () => {\n    it('应该支持8维度质量评分', () => {\n      const qualityDimensions = {\n        creativity: 0.9,\n        memorability: 0.8,\n        cultural_fit: 0.9,\n        aesthetic_appeal: 0.8,\n        pronunciation_ease: 0.8,\n        uniqueness: 0.7,\n        practicality: 0.8,\n        semantic_richness: 0.8\n      }\n      \n      // 验证所有维度都在有效范围内\n      Object.values(qualityDimensions).forEach(score => {\n        expect(score).toBeGreaterThanOrEqual(0)\n        expect(score).toBeLessThanOrEqual(1)\n      })\n      \n      // 验证维度数量\n      expect(Object.keys(qualityDimensions)).toHaveLength(8)\n    })\n\n    it('应该计算平均质量评分', () => {\n      const dimensions = {\n        creativity: 0.9,\n        memorability: 0.8,\n        cultural_fit: 0.9,\n        aesthetic_appeal: 0.8,\n        pronunciation_ease: 0.8,\n        uniqueness: 0.7,\n        practicality: 0.8,\n        semantic_richness: 0.8\n      }\n      \n      const average = Object.values(dimensions).reduce((sum, score) => sum + score, 0) / 8\n      expect(average).toBeCloseTo(0.8125, 4)\n      expect(average).toBeGreaterThan(0.7)\n      expect(average).toBeLessThan(0.9)\n    })\n  })\n\n  describe('错误处理测试', () => {\n    it('应该处理无效的枚举值', () => {\n      // TypeScript 编译时会捕获这些错误，但我们可以测试运行时行为\n      const validCulturalContext = CulturalContext.MODERN\n      expect(Object.values(CulturalContext)).toContain(validCulturalContext)\n    })\n\n    it('应该处理边界值', () => {\n      // 测试质量评分的边界值\n      const minScore = 0\n      const maxScore = 1\n      \n      expect(minScore).toBeGreaterThanOrEqual(0)\n      expect(minScore).toBeLessThanOrEqual(1)\n      expect(maxScore).toBeGreaterThanOrEqual(0)\n      expect(maxScore).toBeLessThanOrEqual(1)\n    })\n\n    it('应该处理空数组和空对象', () => {\n      const emptyArray: string[] = []\n      const emptyObject: Record<string, any> = {}\n      \n      expect(Array.isArray(emptyArray)).toBe(true)\n      expect(emptyArray).toHaveLength(0)\n      expect(typeof emptyObject).toBe('object')\n      expect(Object.keys(emptyObject)).toHaveLength(0)\n    })\n  })\n})\n"], "version": 3}