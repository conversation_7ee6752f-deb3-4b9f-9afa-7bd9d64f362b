d781186c6adff106f8a279cb1b43a924
"use strict";
/**
 * TypeScript 类型安全测试
 *
 * 确保TypeScript编译无错误，验证类型定义的正确性
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const core_js_1 = require("../../types/core.js");
const multilingual_js_1 = require("../../types/multilingual.js");
(0, globals_1.describe)('TypeScript 类型安全测试', () => {
    (0, globals_1.describe)('核心类型定义', () => {
        (0, globals_1.it)('应该正确定义 Morpheme 类型', () => {
            const morpheme = {
                id: 'test_001',
                text: '测试',
                category: 'emotions',
                subcategory: 'positive',
                cultural_context: core_js_1.CulturalContext.MODERN,
                usage_frequency: 0.8,
                quality_score: 0.9,
                semantic_vector: [0.1, 0.2, 0.3],
                tags: ['test', 'example'],
                language_properties: {
                    syllable_count: 2,
                    character_count: 2,
                    phonetic_features: ['tone2', 'tone4'],
                    morphological_type: 'compound',
                    pronunciation: 'cè shì'
                },
                quality_metrics: {
                    naturalness: 0.9,
                    frequency: 0.8,
                    acceptability: 0.9,
                    aesthetic_appeal: 0.7
                },
                created_at: Date.now(),
                source: 'test',
                version: '2.0.0'
            };
            (0, globals_1.expect)(morpheme.id).toBe('test_001');
            (0, globals_1.expect)(morpheme.text).toBe('测试');
            (0, globals_1.expect)(morpheme.quality_score).toBe(0.9);
        });
        (0, globals_1.it)('应该正确定义 GenerationContext 类型', () => {
            const context = {
                cultural_preference: core_js_1.CulturalContext.MODERN,
                style_preference: 'elegant',
                creativity_level: 0.8,
                quality_threshold: 0.7,
                patterns: ['形容词+名词'],
                exclude_patterns: ['小{adjective}']
            };
            (0, globals_1.expect)(context.style_preference).toBe('elegant');
            (0, globals_1.expect)(context.quality_threshold).toBe(0.7);
            (0, globals_1.expect)(context.creativity_level).toBe(0.8);
        });
        (0, globals_1.it)('应该正确定义 GeneratedUsername 类型', () => {
            const username = {
                text: '优雅设计师',
                pattern: '形容词+名词',
                quality_score: {
                    overall: 0.85,
                    dimensions: {
                        creativity: 0.9,
                        memorability: 0.8,
                        cultural_fit: 0.9,
                        uniqueness: 0.7,
                        pronunciation: 0.8,
                        semantic_coherence: 0.8,
                        aesthetic_appeal: 0.8,
                        practical_usability: 0.8
                    },
                    confidence: 0.9,
                    evaluation_time: 150,
                    algorithm_version: 'v2.0',
                    issues: [],
                    suggestions: [],
                    timestamp: Date.now()
                },
                explanation: '结合了优雅的特质和设计师的职业特色',
                components: [],
                metadata: {
                    cultural_fit: 0.8,
                    creativity: 0.7,
                    memorability: 0.9,
                    uniqueness: 0.7,
                    generation_time: 150
                }
            };
            (0, globals_1.expect)(username.text).toBe('优雅设计师');
            (0, globals_1.expect)(username.quality_score.overall).toBe(0.85);
            (0, globals_1.expect)(username.metadata.generation_time).toBe(150);
        });
    });
    (0, globals_1.describe)('多语种类型定义', () => {
        (0, globals_1.it)('应该正确定义 LanguageCode 枚举', () => {
            const languages = [
                multilingual_js_1.LanguageCode.ZH_CN,
                multilingual_js_1.LanguageCode.EN_US,
                multilingual_js_1.LanguageCode.JA_JP,
                multilingual_js_1.LanguageCode.KO_KR,
                multilingual_js_1.LanguageCode.ES_ES,
                multilingual_js_1.LanguageCode.FR_FR,
                multilingual_js_1.LanguageCode.DE_DE,
                multilingual_js_1.LanguageCode.AR_SA
            ];
            (0, globals_1.expect)(languages).toHaveLength(8);
            (0, globals_1.expect)(languages).toContain(multilingual_js_1.LanguageCode.ZH_CN);
            (0, globals_1.expect)(languages).toContain(multilingual_js_1.LanguageCode.EN_US);
        });
        (0, globals_1.it)('应该正确定义 UniversalConcept 类型', () => {
            const concept = {
                concept_id: 'concept_001',
                semantic_vector: {
                    vector: new Array(512).fill(0.1),
                    model_version: 'mBERT-v1.0',
                    confidence: 0.95,
                    updated_at: '2025-06-24T00:00:00Z'
                },
                concept_category: multilingual_js_1.ConceptCategory.EMOTIONS,
                abstraction_level: 0.6,
                cultural_neutrality: 0.8,
                cross_lingual_stability: 0.9,
                cognitive_load: 0.3,
                memorability_score: 0.8,
                emotional_valence: 0.7,
                cognitive_attributes: {
                    memorability: 0.8,
                    cognitive_load: 0.3,
                    emotional_valence: 0.7
                },
                related_concepts: ['joy_001', 'satisfaction_001'],
                hierarchical_children: [],
                created_at: '2025-06-24T00:00:00Z',
                updated_at: '2025-06-24T00:00:00Z',
                version: '3.0.0'
            };
            (0, globals_1.expect)(concept.concept_id).toBe('concept_001');
            (0, globals_1.expect)(concept.semantic_vector.vector).toHaveLength(512);
            (0, globals_1.expect)(concept.concept_category).toBe('emotions');
        });
        (0, globals_1.it)('应该正确定义 LanguageSpecificMorpheme 类型', () => {
            const morpheme = {
                morpheme_id: 'zh_morpheme_001',
                concept_id: 'concept_001',
                language: multilingual_js_1.LanguageCode.ZH_CN,
                text: '快乐',
                alternative_forms: ['愉快', '开心'],
                phonetic_features: {
                    ipa_transcription: '/kuài.lè/',
                    syllable_count: 2,
                    tone_pattern: ['4', '4'],
                    phonetic_harmony: 0.8
                },
                morphological_info: {
                    pos_tag: 'ADJ',
                    morphological_type: 'compound',
                    root: '快乐',
                    prefixes: [],
                    suffixes: []
                },
                syntactic_properties: {
                    syntactic_function: ['attributive', 'predicative'],
                    collocation_constraints: ['快乐的', '很快乐'],
                    grammatical_features: {}
                },
                cultural_context: {
                    traditionality: 0.6,
                    modernity: 0.8,
                    formality: 0.5,
                    regionality: 0.7,
                    religious_sensitivity: 0.3,
                    age_appropriateness: ['adult', 'teen', 'child'],
                    cultural_tags: ['positive', 'emotion', 'wellbeing']
                },
                regional_variants: [],
                register_level: multilingual_js_1.RegisterLevel.NEUTRAL,
                language_quality_scores: {
                    naturalness: 0.9,
                    fluency: 0.9,
                    authenticity: 0.95,
                    aesthetic_appeal: 0.8,
                    pronunciation_ease: 0.8,
                    memorability: 0.9,
                    uniqueness: 0.6,
                    practicality: 0.9
                },
                cultural_appropriateness: 0.9,
                native_speaker_rating: 0.95,
                usage_frequency: 0.8,
                popularity_trend: 0.1,
                created_at: '2025-06-24T00:00:00Z',
                updated_at: '2025-06-24T00:00:00Z',
                version: '3.0.0',
                source: 'native_corpus',
                validation_status: 'validated'
            };
            (0, globals_1.expect)(morpheme.morpheme_id).toBe('zh_morpheme_001');
            (0, globals_1.expect)(morpheme.language).toBe(multilingual_js_1.LanguageCode.ZH_CN);
            (0, globals_1.expect)(morpheme.phonetic_features.syllable_count).toBe(2);
            (0, globals_1.expect)(morpheme.language_quality_scores.naturalness).toBe(0.9);
        });
        (0, globals_1.it)('应该正确定义语义向量维度常量', () => {
            // 测试常量类型
            const legacyDim = multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY;
            const multilingualDim = multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL;
            const defaultDim = multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT;
            (0, globals_1.expect)(legacyDim).toBe(20);
            (0, globals_1.expect)(multilingualDim).toBe(512);
            (0, globals_1.expect)(defaultDim).toBe(512);
        });
    });
    (0, globals_1.describe)('类型兼容性测试', () => {
        (0, globals_1.it)('应该支持传统 CulturalContext 枚举', () => {
            const contexts = [core_js_1.CulturalContext.ANCIENT, core_js_1.CulturalContext.MODERN, core_js_1.CulturalContext.NEUTRAL];
            contexts.forEach(context => {
                (0, globals_1.expect)([core_js_1.CulturalContext.ANCIENT, core_js_1.CulturalContext.MODERN, core_js_1.CulturalContext.NEUTRAL]).toContain(context);
            });
        });
        (0, globals_1.it)('应该支持多维度 CulturalContext 接口', () => {
            const multiContext = {
                traditionality: 0.6,
                modernity: 0.8,
                formality: 0.5,
                regionality: 0.7,
                religious_sensitivity: 0.3,
                age_appropriateness: ['adult', 'teen'],
                cultural_tags: ['positive', 'modern']
            };
            (0, globals_1.expect)(multiContext.traditionality).toBe(0.6);
            (0, globals_1.expect)(multiContext.cultural_tags).toContain('positive');
        });
        (0, globals_1.it)('应该支持语义向量的向后兼容', () => {
            const universalVector = {
                vector: new Array(512).fill(0.1),
                model_version: 'mBERT-v1.0',
                confidence: 0.95,
                updated_at: '2025-06-24T00:00:00Z',
                legacy_vector: new Array(20).fill(0.1) // 向后兼容
            };
            (0, globals_1.expect)(universalVector.vector).toHaveLength(512);
            (0, globals_1.expect)(universalVector.legacy_vector).toHaveLength(20);
        });
    });
    (0, globals_1.describe)('类型推断测试', () => {
        (0, globals_1.it)('应该正确推断函数返回类型', () => {
            // 模拟函数类型推断
            function createMorpheme(text) {
                return {
                    id: 'test',
                    text,
                    category: 'emotions',
                    subcategory: 'positive',
                    cultural_context: core_js_1.CulturalContext.MODERN,
                    usage_frequency: 0.8,
                    quality_score: 0.9,
                    semantic_vector: [0.1, 0.2],
                    tags: ['test'],
                    language_properties: {
                        syllable_count: 1,
                        character_count: 2,
                        phonetic_features: ['tone4'],
                        morphological_type: 'simple'
                    },
                    quality_metrics: {
                        naturalness: 0.9,
                        frequency: 0.8,
                        acceptability: 0.9,
                        aesthetic_appeal: 0.7
                    },
                    created_at: Date.now(),
                    source: 'test',
                    version: '2.0.0'
                };
            }
            const morpheme = createMorpheme('测试');
            (0, globals_1.expect)(morpheme.text).toBe('测试');
        });
        (0, globals_1.it)('应该支持泛型类型约束', () => {
            // 测试泛型约束
            function processLanguageData(data) {
                return data.filter(item => item.language === 'zh-CN');
            }
            const mockData = [
                { language: multilingual_js_1.LanguageCode.ZH_CN, text: '测试1' },
                { language: multilingual_js_1.LanguageCode.EN_US, text: 'test2' }
            ];
            const filtered = processLanguageData(mockData);
            (0, globals_1.expect)(filtered).toHaveLength(1);
            (0, globals_1.expect)(filtered[0].language).toBe(multilingual_js_1.LanguageCode.ZH_CN);
        });
    });
    (0, globals_1.describe)('编译时类型检查', () => {
        (0, globals_1.it)('应该防止类型错误的赋值', () => {
            // 这些测试确保TypeScript编译器能够捕获类型错误
            // 正确的类型赋值
            const validLanguage = multilingual_js_1.LanguageCode.ZH_CN;
            const validRegister = multilingual_js_1.RegisterLevel.FORMAL;
            (0, globals_1.expect)(validLanguage).toBe(multilingual_js_1.LanguageCode.ZH_CN);
            (0, globals_1.expect)(validRegister).toBe(multilingual_js_1.RegisterLevel.FORMAL);
            // TypeScript应该在编译时捕获以下错误：
            // const invalidLanguage: LanguageCode = 'invalid-lang' // 编译错误
            // const invalidRegister: RegisterLevel = 'INVALID' // 编译错误
        });
        (0, globals_1.it)('应该确保必需字段的存在', () => {
            // TypeScript应该确保所有必需字段都存在
            const partialMorpheme = {
                id: 'test',
                text: '测试'
                // 缺少其他必需字段
            };
            // 完整的morpheme应该包含所有必需字段
            (0, globals_1.expect)(partialMorpheme.id).toBe('test');
            (0, globals_1.expect)(partialMorpheme.text).toBe('测试');
        });
    });
    (0, globals_1.describe)('质量评估系统准确性测试', () => {
        (0, globals_1.it)('应该正确计算8维度质量评分', () => {
            const qualityScore = {
                overall: 0.85,
                dimensions: {
                    creativity: 0.9,
                    memorability: 0.8,
                    cultural_fit: 0.9,
                    uniqueness: 0.7,
                    pronunciation: 0.8,
                    semantic_coherence: 0.8,
                    aesthetic_appeal: 0.8,
                    practical_usability: 0.8
                },
                confidence: 0.9,
                evaluation_time: 150,
                algorithm_version: 'v2.0',
                issues: [],
                suggestions: [],
                timestamp: Date.now()
            };
            // 验证所有维度都在有效范围内
            Object.values(qualityScore.dimensions).forEach(score => {
                (0, globals_1.expect)(score).toBeGreaterThanOrEqual(0);
                (0, globals_1.expect)(score).toBeLessThanOrEqual(1);
            });
            // 验证整体评分与各维度的一致性
            const avgScore = Object.values(qualityScore.dimensions).reduce((sum, score) => sum + score, 0) / 8;
            // 整体评分应该接近各维度平均值
            (0, globals_1.expect)(Math.abs(qualityScore.overall - avgScore)).toBeLessThan(0.1);
        });
        (0, globals_1.it)('应该验证多语种质量评分的一致性', () => {
            const langQualityScores = {
                naturalness: 0.9,
                fluency: 0.85,
                authenticity: 0.95,
                aesthetic_appeal: 0.8,
                pronunciation_ease: 0.75,
                memorability: 0.9,
                uniqueness: 0.6,
                practicality: 0.85
            };
            // 验证所有评分都在有效范围内
            Object.values(langQualityScores).forEach(score => {
                (0, globals_1.expect)(score).toBeGreaterThanOrEqual(0);
                (0, globals_1.expect)(score).toBeLessThanOrEqual(1);
            });
            // 验证评分的合理性
            (0, globals_1.expect)(langQualityScores.authenticity).toBeGreaterThan(0.8); // 真实性应该较高
            (0, globals_1.expect)(langQualityScores.naturalness).toBeGreaterThan(0.7); // 自然度应该较高
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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