{"version": 3, "names": ["cov_2i3pzto8wf", "actualCoverage", "readFile", "readdir", "stat", "join", "extname", "watch", "SUPPORTED_VERSIONS", "DATA_DIR", "s", "process", "cwd", "SUPPORTED_FORMATS", "VERSION_COMPATIBILITY", "current", "supported", "VALIDATION_CONFIG", "semantic_vector_dimension", "min_quality_score", "max_quality_score", "required_fields", "DataLoader", "config", "cache", "Map", "watchers", "loadPromise", "constructor", "b", "f", "dataDir", "enableHotReload", "enableValidation", "enableCache", "cacheTTL", "maxRetries", "loadAll", "_performLoad", "result", "startTime", "Date", "now", "dataFiles", "_scanDataFiles", "allMorphemes", "filePath", "rawMorphemes", "_loadDataFile", "adaptedMorphemes", "map", "m", "_adaptMorphemeData", "push", "validation", "_validateData", "passed", "errors", "warnings", "validated_count", "length", "validation_time", "stats", "_calculateStats", "_setupHotReload", "morphemes", "timestamp", "console", "log", "load_time", "error", "Error", "message", "String", "files", "file", "fileStat", "isFile", "includes", "cached", "get", "data", "content", "parsed", "JSON", "parse", "Array", "isArray", "split", "filter", "line", "trim", "set", "i", "morpheme", "prefix", "id", "field", "undefined", "usage_frequency", "quality_score", "semantic_vector", "version", "warn", "loadTime", "by_category", "by_context", "total_quality", "category", "cultural_context", "total_count", "avg_quality", "path", "watcher", "close", "clear", "eventType", "delete", "_debounceReload", "size", "timeout", "clearTimeout", "setTimeout", "catch", "rawMorpheme", "language_properties", "quality_metrics", "adaptedMorpheme", "syllable_count", "Math", "ceil", "text", "character_count", "phonetic_features", "morphological_type", "_inferMorphologicalType", "pronunciation", "naturalness", "frequency", "acceptability", "aesthetic_appeal", "typeMap", "destroy"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts"], "sourcesContent": ["/**\n * 数据加载器\n * \n * 负责从文件系统加载语素数据，支持多语种、版本管理和数据验证。\n * 实现了热重载、缓存优化和错误恢复机制。\n * \n * @fileoverview 数据加载和管理核心模块\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport { readFile, readdir, stat } from 'fs/promises'\nimport { join, extname } from 'path'\nimport { watch } from 'fs'\n\n// 类型导入\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\nimport { SUPPORTED_VERSIONS } from '../../types/common'\n\n// ============================================================================\n// 配置常量\n// ============================================================================\n\n/** 数据文件目录路径 */\nconst DATA_DIR = join(process.cwd(), 'data')\n\n/** 支持的数据文件格式 */\nconst SUPPORTED_FORMATS = ['.json', '.jsonl'] as const\n\n/** 数据版本兼容性配置 */\nconst VERSION_COMPATIBILITY = {\n  current: '2.0.0' as const,\n  supported: SUPPORTED_VERSIONS\n} as const\n\n/** 数据验证配置 */\nconst VALIDATION_CONFIG = {\n  /** 语义向量维度 */\n  semantic_vector_dimension: 20,\n  /** 最小质量评分 */\n  min_quality_score: 0.0,\n  /** 最大质量评分 */\n  max_quality_score: 1.0,\n  /** 必需字段 */\n  required_fields: [\n    'id', 'text', 'category', 'subcategory', 'cultural_context',\n    'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n    'language_properties', 'quality_metrics', 'created_at', 'source', 'version'\n  ] as const\n} as const\n\n// ============================================================================\n// 接口定义\n// ============================================================================\n\n/**\n * 数据加载配置接口\n */\nexport interface DataLoaderConfig {\n  /** 数据目录路径 */\n  dataDir?: string\n  /** 是否启用热重载 */\n  enableHotReload?: boolean\n  /** 是否启用数据验证 */\n  enableValidation?: boolean\n  /** 是否启用缓存 */\n  enableCache?: boolean\n  /** 缓存TTL (秒) */\n  cacheTTL?: number\n  /** 最大重试次数 */\n  maxRetries?: number\n}\n\n/**\n * 数据加载结果接口\n */\nexport interface DataLoadResult {\n  /** 加载的语素数据 */\n  morphemes: Morpheme[]\n  /** 加载统计信息 */\n  stats: {\n    total_count: number\n    by_category: Record<string, number>\n    by_context: Record<string, number>\n    avg_quality: number\n    load_time: number\n  }\n  /** 验证结果 */\n  validation: {\n    passed: boolean\n    errors: string[]\n    warnings: string[]\n  }\n  /** 加载时间戳 */\n  timestamp: number\n}\n\n/**\n * 数据验证结果接口\n */\nexport interface ValidationResult {\n  /** 验证是否通过 */\n  passed: boolean\n  /** 错误信息 */\n  errors: string[]\n  /** 警告信息 */\n  warnings: string[]\n  /** 验证的数据项数量 */\n  validated_count: number\n  /** 验证耗时 (毫秒) */\n  validation_time: number\n}\n\n// ============================================================================\n// 数据加载器类\n// ============================================================================\n\n/**\n * 数据加载器类\n * \n * 提供语素数据的加载、验证、缓存和热重载功能\n */\nexport class DataLoader {\n  private config: Required<DataLoaderConfig>\n  private cache: Map<string, { data: Morpheme[], timestamp: number }> = new Map()\n  private watchers: Map<string, any> = new Map()\n  private loadPromise: Promise<DataLoadResult> | null = null\n\n  /**\n   * 构造函数\n   * \n   * @param config 数据加载配置\n   */\n  constructor(config: DataLoaderConfig = {}) {\n    this.config = {\n      dataDir: config.dataDir || DATA_DIR,\n      enableHotReload: config.enableHotReload ?? true,\n      enableValidation: config.enableValidation ?? true,\n      enableCache: config.enableCache ?? true,\n      cacheTTL: config.cacheTTL ?? 3600, // 1小时\n      maxRetries: config.maxRetries ?? 3\n    }\n  }\n\n  /**\n   * 加载所有语素数据\n   * \n   * @returns 数据加载结果\n   */\n  async loadAll(): Promise<DataLoadResult> {\n    // 防止并发加载\n    if (this.loadPromise) {\n      return this.loadPromise\n    }\n\n    this.loadPromise = this._performLoad()\n    \n    try {\n      const result = await this.loadPromise\n      return result\n    } finally {\n      this.loadPromise = null\n    }\n  }\n\n  /**\n   * 执行数据加载\n   * \n   * @private\n   * @returns 数据加载结果\n   */\n  private async _performLoad(): Promise<DataLoadResult> {\n    const startTime = Date.now()\n    \n    try {\n      // 1. 扫描数据文件\n      const dataFiles = await this._scanDataFiles()\n      \n      // 2. 加载数据文件\n      const allMorphemes: Morpheme[] = []\n\n      for (const filePath of dataFiles) {\n        const rawMorphemes = await this._loadDataFile(filePath)\n        // 适配数据格式以支持v1.0兼容性\n        const adaptedMorphemes = rawMorphemes.map(m => this._adaptMorphemeData(m))\n        allMorphemes.push(...adaptedMorphemes)\n      }\n\n      // 3. 数据验证\n      const validation = this.config.enableValidation \n        ? await this._validateData(allMorphemes)\n        : { passed: true, errors: [], warnings: [], validated_count: allMorphemes.length, validation_time: 0 }\n\n      // 4. 计算统计信息\n      const stats = this._calculateStats(allMorphemes, Date.now() - startTime)\n\n      // 5. 设置热重载\n      if (this.config.enableHotReload) {\n        await this._setupHotReload(dataFiles)\n      }\n\n      const result: DataLoadResult = {\n        morphemes: allMorphemes,\n        stats,\n        validation,\n        timestamp: Date.now()\n      }\n\n      console.log(`✅ 数据加载完成: ${allMorphemes.length}个语素, 耗时${stats.load_time}ms`)\n      \n      return result\n\n    } catch (error) {\n      console.error('❌ 数据加载失败:', error)\n      throw new Error(`数据加载失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 扫描数据文件\n   * \n   * @private\n   * @returns 数据文件路径列表\n   */\n  private async _scanDataFiles(): Promise<string[]> {\n    try {\n      const files = await readdir(this.config.dataDir)\n      const dataFiles: string[] = []\n\n      for (const file of files) {\n        const filePath = join(this.config.dataDir, file)\n        const fileStat = await stat(filePath)\n\n        // 只加载语素数据文件，跳过其他文件\n        if (fileStat.isFile() &&\n            SUPPORTED_FORMATS.includes(extname(file) as any) &&\n            (file.includes('morpheme') || file.includes('语素'))) {\n          dataFiles.push(filePath)\n        }\n      }\n\n      if (dataFiles.length === 0) {\n        throw new Error(`未找到数据文件，目录: ${this.config.dataDir}`)\n      }\n\n      console.log(`📁 发现${dataFiles.length}个数据文件`)\n      return dataFiles\n\n    } catch (error) {\n      throw new Error(`扫描数据文件失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 加载单个数据文件\n   * \n   * @private\n   * @param filePath 文件路径\n   * @returns 语素数据数组\n   */\n  private async _loadDataFile(filePath: string): Promise<Morpheme[]> {\n    try {\n      // 检查缓存\n      if (this.config.enableCache) {\n        const cached = this.cache.get(filePath)\n        if (cached && Date.now() - cached.timestamp < this.config.cacheTTL * 1000) {\n          console.log(`📋 使用缓存数据: ${filePath}`)\n          return cached.data\n        }\n      }\n\n      // 读取文件内容\n      const content = await readFile(filePath, 'utf-8')\n      let morphemes: Morpheme[]\n\n      // 根据文件格式解析\n      if (extname(filePath) === '.json') {\n        const parsed = JSON.parse(content)\n\n        // 支持两种格式：直接数组或包含morphemes字段的对象\n        if (Array.isArray(parsed)) {\n          morphemes = parsed\n        } else if (parsed.morphemes && Array.isArray(parsed.morphemes)) {\n          morphemes = parsed.morphemes\n          console.log(`📋 检测到嵌套格式，提取${morphemes.length}个语素`)\n        } else {\n          throw new Error(`无效的JSON格式: 期望数组或包含morphemes字段的对象`)\n        }\n      } else if (extname(filePath) === '.jsonl') {\n        morphemes = content\n          .split('\\n')\n          .filter(line => line.trim())\n          .map(line => JSON.parse(line))\n      } else {\n        throw new Error(`不支持的文件格式: ${extname(filePath)}`)\n      }\n\n      // 更新缓存\n      if (this.config.enableCache) {\n        this.cache.set(filePath, {\n          data: morphemes,\n          timestamp: Date.now()\n        })\n      }\n\n      console.log(`📄 加载文件: ${filePath} (${morphemes.length}个语素)`)\n      return morphemes\n\n    } catch (error) {\n      throw new Error(`加载文件失败 ${filePath}: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 验证数据完整性和正确性\n   * \n   * @private\n   * @param morphemes 语素数据数组\n   * @returns 验证结果\n   */\n  private async _validateData(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: string[] = []\n    const warnings: string[] = []\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      const prefix = `语素[${i}](${morpheme.id || 'unknown'})`\n\n      // 验证必需字段\n      for (const field of VALIDATION_CONFIG.required_fields) {\n        if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined) {\n          errors.push(`${prefix}: 缺少必需字段 '${field}'`)\n        }\n      }\n\n      // 验证数据类型和范围\n      if (typeof morpheme.usage_frequency !== 'number' || \n          morpheme.usage_frequency < 0 || morpheme.usage_frequency > 1) {\n        errors.push(`${prefix}: usage_frequency 必须是 [0-1] 范围内的数字`)\n      }\n\n      if (typeof morpheme.quality_score !== 'number' || \n          morpheme.quality_score < VALIDATION_CONFIG.min_quality_score || \n          morpheme.quality_score > VALIDATION_CONFIG.max_quality_score) {\n        errors.push(`${prefix}: quality_score 必须是 [${VALIDATION_CONFIG.min_quality_score}-${VALIDATION_CONFIG.max_quality_score}] 范围内的数字`)\n      }\n\n      // 验证语义向量\n      if (!Array.isArray(morpheme.semantic_vector) || \n          morpheme.semantic_vector.length !== VALIDATION_CONFIG.semantic_vector_dimension) {\n        errors.push(`${prefix}: semantic_vector 必须是长度为 ${VALIDATION_CONFIG.semantic_vector_dimension} 的数字数组`)\n      }\n\n      // 验证版本兼容性\n      if (morpheme.version && !VERSION_COMPATIBILITY.supported.includes(morpheme.version)) {\n        warnings.push(`${prefix}: 版本 '${morpheme.version}' 可能不兼容，当前支持版本: ${VERSION_COMPATIBILITY.supported.join(', ')}`)\n      }\n    }\n\n    const validation_time = Date.now() - startTime\n    const passed = errors.length === 0\n\n    if (!passed) {\n      console.warn(`⚠️ 数据验证发现 ${errors.length} 个错误, ${warnings.length} 个警告`)\n    } else {\n      console.log(`✅ 数据验证通过: ${morphemes.length}个语素, 耗时${validation_time}ms`)\n    }\n\n    return {\n      passed,\n      errors,\n      warnings,\n      validated_count: morphemes.length,\n      validation_time\n    }\n  }\n\n  /**\n   * 计算统计信息\n   * \n   * @private\n   * @param morphemes 语素数据数组\n   * @param loadTime 加载耗时\n   * @returns 统计信息\n   */\n  private _calculateStats(morphemes: Morpheme[], loadTime: number) {\n    const by_category: Record<string, number> = {}\n    const by_context: Record<string, number> = {}\n    let total_quality = 0\n\n    for (const morpheme of morphemes) {\n      // 按类别统计\n      by_category[morpheme.category] = (by_category[morpheme.category] || 0) + 1\n      \n      // 按文化语境统计\n      by_context[morpheme.cultural_context] = (by_context[morpheme.cultural_context] || 0) + 1\n      \n      // 累计质量评分\n      total_quality += morpheme.quality_score\n    }\n\n    return {\n      total_count: morphemes.length,\n      by_category,\n      by_context,\n      avg_quality: morphemes.length > 0 ? total_quality / morphemes.length : 0,\n      load_time: loadTime\n    }\n  }\n\n  /**\n   * 设置热重载监听\n   * \n   * @private\n   * @param dataFiles 数据文件路径列表\n   */\n  private async _setupHotReload(dataFiles: string[]): Promise<void> {\n    // 清理现有监听器\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // 设置新的监听器\n    for (const filePath of dataFiles) {\n      try {\n        const watcher = watch(filePath, (eventType) => {\n          if (eventType === 'change') {\n            console.log(`🔄 检测到文件变化: ${filePath}`)\n            // 清除缓存\n            this.cache.delete(filePath)\n            // 触发重新加载 (可以添加防抖逻辑)\n            this._debounceReload()\n          }\n        })\n\n        this.watchers.set(filePath, watcher)\n      } catch (error) {\n        console.warn(`⚠️ 无法监听文件变化: ${filePath}`, error)\n      }\n    }\n\n    console.log(`👁️ 热重载已启用，监听 ${this.watchers.size} 个文件`)\n  }\n\n  /**\n   * 防抖重新加载\n   * \n   * @private\n   */\n  private _debounceReload = (() => {\n    let timeout: NodeJS.Timeout | null = null\n    return () => {\n      if (timeout) {\n        clearTimeout(timeout)\n      }\n      timeout = setTimeout(() => {\n        console.log('🔄 执行热重载...')\n        this.loadAll().catch(error => {\n          console.error('❌ 热重载失败:', error)\n        })\n      }, 1000) // 1秒防抖\n    }\n  })()\n\n  /**\n   * 适配语素数据格式\n   *\n   * 为v1.0数据添加v2.0字段的默认值\n   *\n   * @private\n   * @param rawMorpheme 原始语素数据\n   * @returns 适配后的语素数据\n   */\n  private _adaptMorphemeData(rawMorpheme: any): Morpheme {\n    // 如果已经是v2.0格式，直接返回\n    if (rawMorpheme.language_properties && rawMorpheme.quality_metrics) {\n      return rawMorpheme as Morpheme\n    }\n\n    // 为v1.0数据添加默认的v2.0字段\n    const adaptedMorpheme: Morpheme = {\n      ...rawMorpheme,\n      language_properties: rawMorpheme.language_properties || {\n        syllable_count: Math.ceil((rawMorpheme.text?.length || 1) / 2),\n        character_count: rawMorpheme.text?.length || 1,\n        phonetic_features: [],\n        morphological_type: this._inferMorphologicalType(rawMorpheme.category),\n        pronunciation: undefined\n      },\n      quality_metrics: rawMorpheme.quality_metrics || {\n        naturalness: (rawMorpheme.quality_score || 0.5) * 0.9,\n        frequency: rawMorpheme.usage_frequency || 0.5,\n        acceptability: (rawMorpheme.quality_score || 0.5) * 0.95,\n        aesthetic_appeal: (rawMorpheme.quality_score || 0.5) * 0.85\n      },\n      version: rawMorpheme.version || '1.0.0'\n    }\n\n    return adaptedMorpheme\n  }\n\n  /**\n   * 推断词法类型\n   *\n   * @private\n   * @param category 语素类别\n   * @returns 词法类型\n   */\n  private _inferMorphologicalType(category: string): string {\n    const typeMap: Record<string, string> = {\n      'emotions': '形容词',\n      'professions': '名词',\n      'characteristics': '形容词',\n      'objects': '名词',\n      'actions': '动词',\n      'concepts': '名词'\n    }\n\n    return typeMap[category] || '未知'\n  }\n\n  /**\n   * 清理资源\n   */\n  destroy(): void {\n    // 关闭文件监听器\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // 清空缓存\n    this.cache.clear()\n\n    console.log('🧹 数据加载器资源已清理')\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoBA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AApBA;;;;;;;;;;;AAYA,SAASE,QAAQ,EAAEC,OAAO,EAAEC,IAAI,QAAQ,aAAa;AACrD,SAASC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACpC,SAASC,KAAK,QAAQ,IAAI;AAI1B,SAASC,kBAAkB,QAAQ,oBAAoB;AAEvD;AACA;AACA;AAEA;AACA,MAAMC,QAAQ;AAAA;AAAA,CAAAT,cAAA,GAAAU,CAAA,OAAGL,IAAI,CAACM,OAAO,CAACC,GAAG,EAAE,EAAE,MAAM,CAAC;AAE5C;AACA,MAAMC,iBAAiB;AAAA;AAAA,CAAAb,cAAA,GAAAU,CAAA,OAAG,CAAC,OAAO,EAAE,QAAQ,CAAU;AAEtD;AACA,MAAMI,qBAAqB;AAAA;AAAA,CAAAd,cAAA,GAAAU,CAAA,OAAG;EAC5BK,OAAO,EAAE,OAAgB;EACzBC,SAAS,EAAER;CACH;AAEV;AACA,MAAMS,iBAAiB;AAAA;AAAA,CAAAjB,cAAA,GAAAU,CAAA,OAAG;EACxB;EACAQ,yBAAyB,EAAE,EAAE;EAC7B;EACAC,iBAAiB,EAAE,GAAG;EACtB;EACAC,iBAAiB,EAAE,GAAG;EACtB;EACAC,eAAe,EAAE,CACf,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,EAC7D,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS;CAErE;AAgEV;AACA;AACA;AAEA;;;;;AAKA,OAAM,MAAOC,UAAU;EACbC,MAAM;EACNC,KAAK;EAAA;EAAA,CAAAxB,cAAA,GAAAU,CAAA,OAAyD,IAAIe,GAAG,EAAE;EACvEC,QAAQ;EAAA;EAAA,CAAA1B,cAAA,GAAAU,CAAA,OAAqB,IAAIe,GAAG,EAAE;EACtCE,WAAW;EAAA;EAAA,CAAA3B,cAAA,GAAAU,CAAA,OAAmC,IAAI;EAE1D;;;;;EAKAkB,YAAYL,MAAA;EAAA;EAAA,CAAAvB,cAAA,GAAA6B,CAAA,UAA2B,EAAE;IAAA;IAAA7B,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IACvC,IAAI,CAACa,MAAM,GAAG;MACZQ,OAAO;MAAE;MAAA,CAAA/B,cAAA,GAAA6B,CAAA,UAAAN,MAAM,CAACQ,OAAO;MAAA;MAAA,CAAA/B,cAAA,GAAA6B,CAAA,UAAIpB,QAAQ;MACnCuB,eAAe;MAAE;MAAA,CAAAhC,cAAA,GAAA6B,CAAA,UAAAN,MAAM,CAACS,eAAe;MAAA;MAAA,CAAAhC,cAAA,GAAA6B,CAAA,UAAI,IAAI;MAC/CI,gBAAgB;MAAE;MAAA,CAAAjC,cAAA,GAAA6B,CAAA,UAAAN,MAAM,CAACU,gBAAgB;MAAA;MAAA,CAAAjC,cAAA,GAAA6B,CAAA,UAAI,IAAI;MACjDK,WAAW;MAAE;MAAA,CAAAlC,cAAA,GAAA6B,CAAA,UAAAN,MAAM,CAACW,WAAW;MAAA;MAAA,CAAAlC,cAAA,GAAA6B,CAAA,UAAI,IAAI;MACvCM,QAAQ;MAAE;MAAA,CAAAnC,cAAA,GAAA6B,CAAA,UAAAN,MAAM,CAACY,QAAQ;MAAA;MAAA,CAAAnC,cAAA,GAAA6B,CAAA,UAAI,IAAI;MAAE;MACnCO,UAAU;MAAE;MAAA,CAAApC,cAAA,GAAA6B,CAAA,UAAAN,MAAM,CAACa,UAAU;MAAA;MAAA,CAAApC,cAAA,GAAA6B,CAAA,UAAI,CAAC;KACnC;EACH;EAEA;;;;;EAKA,MAAMQ,OAAOA,CAAA;IAAA;IAAArC,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IACX;IACA,IAAI,IAAI,CAACiB,WAAW,EAAE;MAAA;MAAA3B,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAU,CAAA;MACpB,OAAO,IAAI,CAACiB,WAAW;IACzB,CAAC;IAAA;IAAA;MAAA3B,cAAA,GAAA6B,CAAA;IAAA;IAAA7B,cAAA,GAAAU,CAAA;IAED,IAAI,CAACiB,WAAW,GAAG,IAAI,CAACW,YAAY,EAAE;IAAA;IAAAtC,cAAA,GAAAU,CAAA;IAEtC,IAAI;MACF,MAAM6B,MAAM;MAAA;MAAA,CAAAvC,cAAA,GAAAU,CAAA,QAAG,MAAM,IAAI,CAACiB,WAAW;MAAA;MAAA3B,cAAA,GAAAU,CAAA;MACrC,OAAO6B,MAAM;IACf,CAAC,SAAS;MAAA;MAAAvC,cAAA,GAAAU,CAAA;MACR,IAAI,CAACiB,WAAW,GAAG,IAAI;IACzB;EACF;EAEA;;;;;;EAMQ,MAAMW,YAAYA,CAAA;IAAA;IAAAtC,cAAA,GAAA8B,CAAA;IACxB,MAAMU,SAAS;IAAA;IAAA,CAAAxC,cAAA,GAAAU,CAAA,QAAG+B,IAAI,CAACC,GAAG,EAAE;IAAA;IAAA1C,cAAA,GAAAU,CAAA;IAE5B,IAAI;MACF;MACA,MAAMiC,SAAS;MAAA;MAAA,CAAA3C,cAAA,GAAAU,CAAA,QAAG,MAAM,IAAI,CAACkC,cAAc,EAAE;MAE7C;MACA,MAAMC,YAAY;MAAA;MAAA,CAAA7C,cAAA,GAAAU,CAAA,QAAe,EAAE;MAAA;MAAAV,cAAA,GAAAU,CAAA;MAEnC,KAAK,MAAMoC,QAAQ,IAAIH,SAAS,EAAE;QAChC,MAAMI,YAAY;QAAA;QAAA,CAAA/C,cAAA,GAAAU,CAAA,QAAG,MAAM,IAAI,CAACsC,aAAa,CAACF,QAAQ,CAAC;QACvD;QACA,MAAMG,gBAAgB;QAAA;QAAA,CAAAjD,cAAA,GAAAU,CAAA,QAAGqC,YAAY,CAACG,GAAG,CAACC,CAAC,IAAI;UAAA;UAAAnD,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAU,CAAA;UAAA,WAAI,CAAC0C,kBAAkB,CAACD,CAAC,CAAC;QAAD,CAAC,CAAC;QAAA;QAAAnD,cAAA,GAAAU,CAAA;QAC1EmC,YAAY,CAACQ,IAAI,CAAC,GAAGJ,gBAAgB,CAAC;MACxC;MAEA;MACA,MAAMK,UAAU;MAAA;MAAA,CAAAtD,cAAA,GAAAU,CAAA,QAAG,IAAI,CAACa,MAAM,CAACU,gBAAgB;MAAA;MAAA,CAAAjC,cAAA,GAAA6B,CAAA,UAC3C,MAAM,IAAI,CAAC0B,aAAa,CAACV,YAAY,CAAC;MAAA;MAAA,CAAA7C,cAAA,GAAA6B,CAAA,UACtC;QAAE2B,MAAM,EAAE,IAAI;QAAEC,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,eAAe,EAAEd,YAAY,CAACe,MAAM;QAAEC,eAAe,EAAE;MAAC,CAAE;MAExG;MACA,MAAMC,KAAK;MAAA;MAAA,CAAA9D,cAAA,GAAAU,CAAA,QAAG,IAAI,CAACqD,eAAe,CAAClB,YAAY,EAAEJ,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,CAAC;MAExE;MAAA;MAAAxC,cAAA,GAAAU,CAAA;MACA,IAAI,IAAI,CAACa,MAAM,CAACS,eAAe,EAAE;QAAA;QAAAhC,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QAC/B,MAAM,IAAI,CAACsD,eAAe,CAACrB,SAAS,CAAC;MACvC,CAAC;MAAA;MAAA;QAAA3C,cAAA,GAAA6B,CAAA;MAAA;MAED,MAAMU,MAAM;MAAA;MAAA,CAAAvC,cAAA,GAAAU,CAAA,QAAmB;QAC7BuD,SAAS,EAAEpB,YAAY;QACvBiB,KAAK;QACLR,UAAU;QACVY,SAAS,EAAEzB,IAAI,CAACC,GAAG;OACpB;MAAA;MAAA1C,cAAA,GAAAU,CAAA;MAEDyD,OAAO,CAACC,GAAG,CAAC,aAAavB,YAAY,CAACe,MAAM,UAAUE,KAAK,CAACO,SAAS,IAAI,CAAC;MAAA;MAAArE,cAAA,GAAAU,CAAA;MAE1E,OAAO6B,MAAM;IAEf,CAAC,CAAC,OAAO+B,KAAK,EAAE;MAAA;MAAAtE,cAAA,GAAAU,CAAA;MACdyD,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MAAA;MAAAtE,cAAA,GAAAU,CAAA;MACjC,MAAM,IAAI6D,KAAK,CAAC,WAAWD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAvE,cAAA,GAAA6B,CAAA,WAAGyC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAxE,cAAA,GAAA6B,CAAA,WAAG4C,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IACtF;EACF;EAEA;;;;;;EAMQ,MAAM1B,cAAcA,CAAA;IAAA;IAAA5C,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IAC1B,IAAI;MACF,MAAMgE,KAAK;MAAA;MAAA,CAAA1E,cAAA,GAAAU,CAAA,QAAG,MAAMP,OAAO,CAAC,IAAI,CAACoB,MAAM,CAACQ,OAAO,CAAC;MAChD,MAAMY,SAAS;MAAA;MAAA,CAAA3C,cAAA,GAAAU,CAAA,QAAa,EAAE;MAAA;MAAAV,cAAA,GAAAU,CAAA;MAE9B,KAAK,MAAMiE,IAAI,IAAID,KAAK,EAAE;QACxB,MAAM5B,QAAQ;QAAA;QAAA,CAAA9C,cAAA,GAAAU,CAAA,QAAGL,IAAI,CAAC,IAAI,CAACkB,MAAM,CAACQ,OAAO,EAAE4C,IAAI,CAAC;QAChD,MAAMC,QAAQ;QAAA;QAAA,CAAA5E,cAAA,GAAAU,CAAA,QAAG,MAAMN,IAAI,CAAC0C,QAAQ,CAAC;QAErC;QAAA;QAAA9C,cAAA,GAAAU,CAAA;QACA;QAAI;QAAA,CAAAV,cAAA,GAAA6B,CAAA,WAAA+C,QAAQ,CAACC,MAAM,EAAE;QAAA;QAAA,CAAA7E,cAAA,GAAA6B,CAAA,WACjBhB,iBAAiB,CAACiE,QAAQ,CAACxE,OAAO,CAACqE,IAAI,CAAQ,CAAC;QAC/C;QAAA,CAAA3E,cAAA,GAAA6B,CAAA,WAAA8C,IAAI,CAACG,QAAQ,CAAC,UAAU,CAAC;QAAA;QAAA,CAAA9E,cAAA,GAAA6B,CAAA,WAAI8C,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAC,EAAE;UAAA;UAAA9E,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UACtDiC,SAAS,CAACU,IAAI,CAACP,QAAQ,CAAC;QAC1B,CAAC;QAAA;QAAA;UAAA9C,cAAA,GAAA6B,CAAA;QAAA;MACH;MAAC;MAAA7B,cAAA,GAAAU,CAAA;MAED,IAAIiC,SAAS,CAACiB,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA5D,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QAC1B,MAAM,IAAI6D,KAAK,CAAC,eAAe,IAAI,CAAChD,MAAM,CAACQ,OAAO,EAAE,CAAC;MACvD,CAAC;MAAA;MAAA;QAAA/B,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MAEDyD,OAAO,CAACC,GAAG,CAAC,QAAQzB,SAAS,CAACiB,MAAM,OAAO,CAAC;MAAA;MAAA5D,cAAA,GAAAU,CAAA;MAC5C,OAAOiC,SAAS;IAElB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MAAA;MAAAtE,cAAA,GAAAU,CAAA;MACd,MAAM,IAAI6D,KAAK,CAAC,aAAaD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAvE,cAAA,GAAA6B,CAAA,WAAGyC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAxE,cAAA,GAAA6B,CAAA,WAAG4C,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IACxF;EACF;EAEA;;;;;;;EAOQ,MAAMtB,aAAaA,CAACF,QAAgB;IAAA;IAAA9C,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IAC1C,IAAI;MAAA;MAAAV,cAAA,GAAAU,CAAA;MACF;MACA,IAAI,IAAI,CAACa,MAAM,CAACW,WAAW,EAAE;QAAA;QAAAlC,cAAA,GAAA6B,CAAA;QAC3B,MAAMkD,MAAM;QAAA;QAAA,CAAA/E,cAAA,GAAAU,CAAA,QAAG,IAAI,CAACc,KAAK,CAACwD,GAAG,CAAClC,QAAQ,CAAC;QAAA;QAAA9C,cAAA,GAAAU,CAAA;QACvC;QAAI;QAAA,CAAAV,cAAA,GAAA6B,CAAA,WAAAkD,MAAM;QAAA;QAAA,CAAA/E,cAAA,GAAA6B,CAAA,WAAIY,IAAI,CAACC,GAAG,EAAE,GAAGqC,MAAM,CAACb,SAAS,GAAG,IAAI,CAAC3C,MAAM,CAACY,QAAQ,GAAG,IAAI,GAAE;UAAA;UAAAnC,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UACzEyD,OAAO,CAACC,GAAG,CAAC,cAActB,QAAQ,EAAE,CAAC;UAAA;UAAA9C,cAAA,GAAAU,CAAA;UACrC,OAAOqE,MAAM,CAACE,IAAI;QACpB,CAAC;QAAA;QAAA;UAAAjF,cAAA,GAAA6B,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAA7B,cAAA,GAAA6B,CAAA;MAAA;MAED;MACA,MAAMqD,OAAO;MAAA;MAAA,CAAAlF,cAAA,GAAAU,CAAA,QAAG,MAAMR,QAAQ,CAAC4C,QAAQ,EAAE,OAAO,CAAC;MACjD,IAAImB,SAAqB;MAEzB;MAAA;MAAAjE,cAAA,GAAAU,CAAA;MACA,IAAIJ,OAAO,CAACwC,QAAQ,CAAC,KAAK,OAAO,EAAE;QAAA;QAAA9C,cAAA,GAAA6B,CAAA;QACjC,MAAMsD,MAAM;QAAA;QAAA,CAAAnF,cAAA,GAAAU,CAAA,QAAG0E,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;QAElC;QAAA;QAAAlF,cAAA,GAAAU,CAAA;QACA,IAAI4E,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;UAAA;UAAAnF,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UACzBuD,SAAS,GAAGkB,MAAM;QACpB,CAAC,MAAM;UAAA;UAAAnF,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UAAA;UAAI;UAAA,CAAAV,cAAA,GAAA6B,CAAA,WAAAsD,MAAM,CAAClB,SAAS;UAAA;UAAA,CAAAjE,cAAA,GAAA6B,CAAA,WAAIyD,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAClB,SAAS,CAAC,GAAE;YAAA;YAAAjE,cAAA,GAAA6B,CAAA;YAAA7B,cAAA,GAAAU,CAAA;YAC9DuD,SAAS,GAAGkB,MAAM,CAAClB,SAAS;YAAA;YAAAjE,cAAA,GAAAU,CAAA;YAC5ByD,OAAO,CAACC,GAAG,CAAC,gBAAgBH,SAAS,CAACL,MAAM,KAAK,CAAC;UACpD,CAAC,MAAM;YAAA;YAAA5D,cAAA,GAAA6B,CAAA;YAAA7B,cAAA,GAAAU,CAAA;YACL,MAAM,IAAI6D,KAAK,CAAC,kCAAkC,CAAC;UACrD;QAAA;MACF,CAAC,MAAM;QAAA;QAAAvE,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QAAA,IAAIJ,OAAO,CAACwC,QAAQ,CAAC,KAAK,QAAQ,EAAE;UAAA;UAAA9C,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UACzCuD,SAAS,GAAGiB,OAAO,CAChBM,KAAK,CAAC,IAAI,CAAC,CACXC,MAAM,CAACC,IAAI,IAAI;YAAA;YAAA1F,cAAA,GAAA8B,CAAA;YAAA9B,cAAA,GAAAU,CAAA;YAAA,OAAAgF,IAAI,CAACC,IAAI,EAAE;UAAF,CAAE,CAAC,CAC3BzC,GAAG,CAACwC,IAAI,IAAI;YAAA;YAAA1F,cAAA,GAAA8B,CAAA;YAAA9B,cAAA,GAAAU,CAAA;YAAA,OAAA0E,IAAI,CAACC,KAAK,CAACK,IAAI,CAAC;UAAD,CAAC,CAAC;QAClC,CAAC,MAAM;UAAA;UAAA1F,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UACL,MAAM,IAAI6D,KAAK,CAAC,aAAajE,OAAO,CAACwC,QAAQ,CAAC,EAAE,CAAC;QACnD;MAAA;MAEA;MAAA;MAAA9C,cAAA,GAAAU,CAAA;MACA,IAAI,IAAI,CAACa,MAAM,CAACW,WAAW,EAAE;QAAA;QAAAlC,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QAC3B,IAAI,CAACc,KAAK,CAACoE,GAAG,CAAC9C,QAAQ,EAAE;UACvBmC,IAAI,EAAEhB,SAAS;UACfC,SAAS,EAAEzB,IAAI,CAACC,GAAG;SACpB,CAAC;MACJ,CAAC;MAAA;MAAA;QAAA1C,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MAEDyD,OAAO,CAACC,GAAG,CAAC,YAAYtB,QAAQ,KAAKmB,SAAS,CAACL,MAAM,MAAM,CAAC;MAAA;MAAA5D,cAAA,GAAAU,CAAA;MAC5D,OAAOuD,SAAS;IAElB,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA;MAAAtE,cAAA,GAAAU,CAAA;MACd,MAAM,IAAI6D,KAAK,CAAC,UAAUzB,QAAQ,KAAKwB,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAvE,cAAA,GAAA6B,CAAA,WAAGyC,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAxE,cAAA,GAAA6B,CAAA,WAAG4C,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IAClG;EACF;EAEA;;;;;;;EAOQ,MAAMf,aAAaA,CAACU,SAAqB;IAAA;IAAAjE,cAAA,GAAA8B,CAAA;IAC/C,MAAMU,SAAS;IAAA;IAAA,CAAAxC,cAAA,GAAAU,CAAA,QAAG+B,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMe,MAAM;IAAA;IAAA,CAAAzD,cAAA,GAAAU,CAAA,QAAa,EAAE;IAC3B,MAAMgD,QAAQ;IAAA;IAAA,CAAA1D,cAAA,GAAAU,CAAA,QAAa,EAAE;IAAA;IAAAV,cAAA,GAAAU,CAAA;IAE7B,KAAK,IAAImF,CAAC;IAAA;IAAA,CAAA7F,cAAA,GAAAU,CAAA,QAAG,CAAC,GAAEmF,CAAC,GAAG5B,SAAS,CAACL,MAAM,EAAEiC,CAAC,EAAE,EAAE;MACzC,MAAMC,QAAQ;MAAA;MAAA,CAAA9F,cAAA,GAAAU,CAAA,QAAGuD,SAAS,CAAC4B,CAAC,CAAC;MAC7B,MAAME,MAAM;MAAA;MAAA,CAAA/F,cAAA,GAAAU,CAAA,QAAG,MAAMmF,CAAC;MAAK;MAAA,CAAA7F,cAAA,GAAA6B,CAAA,WAAAiE,QAAQ,CAACE,EAAE;MAAA;MAAA,CAAAhG,cAAA,GAAA6B,CAAA,WAAI,SAAS,IAAG;MAEtD;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MACA,KAAK,MAAMuF,KAAK,IAAIhF,iBAAiB,CAACI,eAAe,EAAE;QAAA;QAAArB,cAAA,GAAAU,CAAA;QACrD;QAAI;QAAA,CAAAV,cAAA,GAAA6B,CAAA,aAAEoE,KAAK,IAAIH,QAAQ,CAAC;QAAA;QAAA,CAAA9F,cAAA,GAAA6B,CAAA,WAAIiE,QAAQ,CAACG,KAAuB,CAAC,KAAKC,SAAS,GAAE;UAAA;UAAAlG,cAAA,GAAA6B,CAAA;UAAA7B,cAAA,GAAAU,CAAA;UAC3E+C,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,aAAaE,KAAK,GAAG,CAAC;QAC7C,CAAC;QAAA;QAAA;UAAAjG,cAAA,GAAA6B,CAAA;QAAA;MACH;MAEA;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MACA;MAAI;MAAA,CAAAV,cAAA,GAAA6B,CAAA,kBAAOiE,QAAQ,CAACK,eAAe,KAAK,QAAQ;MAAA;MAAA,CAAAnG,cAAA,GAAA6B,CAAA,WAC5CiE,QAAQ,CAACK,eAAe,GAAG,CAAC;MAAA;MAAA,CAAAnG,cAAA,GAAA6B,CAAA,WAAIiE,QAAQ,CAACK,eAAe,GAAG,CAAC,GAAE;QAAA;QAAAnG,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QAChE+C,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,oCAAoC,CAAC;MAC5D,CAAC;MAAA;MAAA;QAAA/F,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MAED;MAAI;MAAA,CAAAV,cAAA,GAAA6B,CAAA,kBAAOiE,QAAQ,CAACM,aAAa,KAAK,QAAQ;MAAA;MAAA,CAAApG,cAAA,GAAA6B,CAAA,WAC1CiE,QAAQ,CAACM,aAAa,GAAGnF,iBAAiB,CAACE,iBAAiB;MAAA;MAAA,CAAAnB,cAAA,GAAA6B,CAAA,WAC5DiE,QAAQ,CAACM,aAAa,GAAGnF,iBAAiB,CAACG,iBAAiB,GAAE;QAAA;QAAApB,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QAChE+C,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,wBAAwB9E,iBAAiB,CAACE,iBAAiB,IAAIF,iBAAiB,CAACG,iBAAiB,UAAU,CAAC;MACpI,CAAC;MAAA;MAAA;QAAApB,cAAA,GAAA6B,CAAA;MAAA;MAED;MAAA7B,cAAA,GAAAU,CAAA;MACA;MAAI;MAAA,CAAAV,cAAA,GAAA6B,CAAA,YAACyD,KAAK,CAACC,OAAO,CAACO,QAAQ,CAACO,eAAe,CAAC;MAAA;MAAA,CAAArG,cAAA,GAAA6B,CAAA,WACxCiE,QAAQ,CAACO,eAAe,CAACzC,MAAM,KAAK3C,iBAAiB,CAACC,yBAAyB,GAAE;QAAA;QAAAlB,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QACnF+C,MAAM,CAACJ,IAAI,CAAC,GAAG0C,MAAM,4BAA4B9E,iBAAiB,CAACC,yBAAyB,QAAQ,CAAC;MACvG,CAAC;MAAA;MAAA;QAAAlB,cAAA,GAAA6B,CAAA;MAAA;MAED;MAAA7B,cAAA,GAAAU,CAAA;MACA;MAAI;MAAA,CAAAV,cAAA,GAAA6B,CAAA,WAAAiE,QAAQ,CAACQ,OAAO;MAAA;MAAA,CAAAtG,cAAA,GAAA6B,CAAA,WAAI,CAACf,qBAAqB,CAACE,SAAS,CAAC8D,QAAQ,CAACgB,QAAQ,CAACQ,OAAO,CAAC,GAAE;QAAA;QAAAtG,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QACnFgD,QAAQ,CAACL,IAAI,CAAC,GAAG0C,MAAM,SAASD,QAAQ,CAACQ,OAAO,mBAAmBxF,qBAAqB,CAACE,SAAS,CAACX,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAClH,CAAC;MAAA;MAAA;QAAAL,cAAA,GAAA6B,CAAA;MAAA;IACH;IAEA,MAAMgC,eAAe;IAAA;IAAA,CAAA7D,cAAA,GAAAU,CAAA,QAAG+B,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;IAC9C,MAAMgB,MAAM;IAAA;IAAA,CAAAxD,cAAA,GAAAU,CAAA,QAAG+C,MAAM,CAACG,MAAM,KAAK,CAAC;IAAA;IAAA5D,cAAA,GAAAU,CAAA;IAElC,IAAI,CAAC8C,MAAM,EAAE;MAAA;MAAAxD,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAU,CAAA;MACXyD,OAAO,CAACoC,IAAI,CAAC,aAAa9C,MAAM,CAACG,MAAM,SAASF,QAAQ,CAACE,MAAM,MAAM,CAAC;IACxE,CAAC,MAAM;MAAA;MAAA5D,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAU,CAAA;MACLyD,OAAO,CAACC,GAAG,CAAC,aAAaH,SAAS,CAACL,MAAM,UAAUC,eAAe,IAAI,CAAC;IACzE;IAAC;IAAA7D,cAAA,GAAAU,CAAA;IAED,OAAO;MACL8C,MAAM;MACNC,MAAM;MACNC,QAAQ;MACRC,eAAe,EAAEM,SAAS,CAACL,MAAM;MACjCC;KACD;EACH;EAEA;;;;;;;;EAQQE,eAAeA,CAACE,SAAqB,EAAEuC,QAAgB;IAAA;IAAAxG,cAAA,GAAA8B,CAAA;IAC7D,MAAM2E,WAAW;IAAA;IAAA,CAAAzG,cAAA,GAAAU,CAAA,QAA2B,EAAE;IAC9C,MAAMgG,UAAU;IAAA;IAAA,CAAA1G,cAAA,GAAAU,CAAA,QAA2B,EAAE;IAC7C,IAAIiG,aAAa;IAAA;IAAA,CAAA3G,cAAA,GAAAU,CAAA,QAAG,CAAC;IAAA;IAAAV,cAAA,GAAAU,CAAA;IAErB,KAAK,MAAMoF,QAAQ,IAAI7B,SAAS,EAAE;MAAA;MAAAjE,cAAA,GAAAU,CAAA;MAChC;MACA+F,WAAW,CAACX,QAAQ,CAACc,QAAQ,CAAC,GAAG;MAAC;MAAA,CAAA5G,cAAA,GAAA6B,CAAA,WAAA4E,WAAW,CAACX,QAAQ,CAACc,QAAQ,CAAC;MAAA;MAAA,CAAA5G,cAAA,GAAA6B,CAAA,WAAI,CAAC,KAAI,CAAC;MAE1E;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MACAgG,UAAU,CAACZ,QAAQ,CAACe,gBAAgB,CAAC,GAAG;MAAC;MAAA,CAAA7G,cAAA,GAAA6B,CAAA,WAAA6E,UAAU,CAACZ,QAAQ,CAACe,gBAAgB,CAAC;MAAA;MAAA,CAAA7G,cAAA,GAAA6B,CAAA,WAAI,CAAC,KAAI,CAAC;MAExF;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MACAiG,aAAa,IAAIb,QAAQ,CAACM,aAAa;IACzC;IAAC;IAAApG,cAAA,GAAAU,CAAA;IAED,OAAO;MACLoG,WAAW,EAAE7C,SAAS,CAACL,MAAM;MAC7B6C,WAAW;MACXC,UAAU;MACVK,WAAW,EAAE9C,SAAS,CAACL,MAAM,GAAG,CAAC;MAAA;MAAA,CAAA5D,cAAA,GAAA6B,CAAA,WAAG8E,aAAa,GAAG1C,SAAS,CAACL,MAAM;MAAA;MAAA,CAAA5D,cAAA,GAAA6B,CAAA,WAAG,CAAC;MACxEwC,SAAS,EAAEmC;KACZ;EACH;EAEA;;;;;;EAMQ,MAAMxC,eAAeA,CAACrB,SAAmB;IAAA;IAAA3C,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IAC/C;IACA,KAAK,MAAM,CAACsG,IAAI,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACvF,QAAQ,EAAE;MAAA;MAAA1B,cAAA,GAAAU,CAAA;MAC3CuG,OAAO,CAACC,KAAK,EAAE;IACjB;IAAC;IAAAlH,cAAA,GAAAU,CAAA;IACD,IAAI,CAACgB,QAAQ,CAACyF,KAAK,EAAE;IAErB;IAAA;IAAAnH,cAAA,GAAAU,CAAA;IACA,KAAK,MAAMoC,QAAQ,IAAIH,SAAS,EAAE;MAAA;MAAA3C,cAAA,GAAAU,CAAA;MAChC,IAAI;QACF,MAAMuG,OAAO;QAAA;QAAA,CAAAjH,cAAA,GAAAU,CAAA,SAAGH,KAAK,CAACuC,QAAQ,EAAGsE,SAAS,IAAI;UAAA;UAAApH,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAU,CAAA;UAC5C,IAAI0G,SAAS,KAAK,QAAQ,EAAE;YAAA;YAAApH,cAAA,GAAA6B,CAAA;YAAA7B,cAAA,GAAAU,CAAA;YAC1ByD,OAAO,CAACC,GAAG,CAAC,eAAetB,QAAQ,EAAE,CAAC;YACtC;YAAA;YAAA9C,cAAA,GAAAU,CAAA;YACA,IAAI,CAACc,KAAK,CAAC6F,MAAM,CAACvE,QAAQ,CAAC;YAC3B;YAAA;YAAA9C,cAAA,GAAAU,CAAA;YACA,IAAI,CAAC4G,eAAe,EAAE;UACxB,CAAC;UAAA;UAAA;YAAAtH,cAAA,GAAA6B,CAAA;UAAA;QACH,CAAC,CAAC;QAAA;QAAA7B,cAAA,GAAAU,CAAA;QAEF,IAAI,CAACgB,QAAQ,CAACkE,GAAG,CAAC9C,QAAQ,EAAEmE,OAAO,CAAC;MACtC,CAAC,CAAC,OAAO3C,KAAK,EAAE;QAAA;QAAAtE,cAAA,GAAAU,CAAA;QACdyD,OAAO,CAACoC,IAAI,CAAC,gBAAgBzD,QAAQ,EAAE,EAAEwB,KAAK,CAAC;MACjD;IACF;IAAC;IAAAtE,cAAA,GAAAU,CAAA;IAEDyD,OAAO,CAACC,GAAG,CAAC,iBAAiB,IAAI,CAAC1C,QAAQ,CAAC6F,IAAI,MAAM,CAAC;EACxD;EAEA;;;;;EAKQD,eAAe;EAAA;EAAA,CAAAtH,cAAA,GAAAU,CAAA,SAAG,CAAC,MAAK;IAAA;IAAAV,cAAA,GAAA8B,CAAA;IAC9B,IAAI0F,OAAO;IAAA;IAAA,CAAAxH,cAAA,GAAAU,CAAA,SAA0B,IAAI;IAAA;IAAAV,cAAA,GAAAU,CAAA;IACzC,OAAO,MAAK;MAAA;MAAAV,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAU,CAAA;MACV,IAAI8G,OAAO,EAAE;QAAA;QAAAxH,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAU,CAAA;QACX+G,YAAY,CAACD,OAAO,CAAC;MACvB,CAAC;MAAA;MAAA;QAAAxH,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAU,CAAA;MACD8G,OAAO,GAAGE,UAAU,CAAC,MAAK;QAAA;QAAA1H,cAAA,GAAA8B,CAAA;QAAA9B,cAAA,GAAAU,CAAA;QACxByD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAAA;QAAApE,cAAA,GAAAU,CAAA;QAC1B,IAAI,CAAC2B,OAAO,EAAE,CAACsF,KAAK,CAACrD,KAAK,IAAG;UAAA;UAAAtE,cAAA,GAAA8B,CAAA;UAAA9B,cAAA,GAAAU,CAAA;UAC3ByD,OAAO,CAACG,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC,EAAC;IACX,CAAC;EACH,CAAC,EAAC,CAAE;EAEJ;;;;;;;;;EASQlB,kBAAkBA,CAACwE,WAAgB;IAAA;IAAA5H,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IACzC;IACA;IAAI;IAAA,CAAAV,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACC,mBAAmB;IAAA;IAAA,CAAA7H,cAAA,GAAA6B,CAAA,WAAI+F,WAAW,CAACE,eAAe,GAAE;MAAA;MAAA9H,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAU,CAAA;MAClE,OAAOkH,WAAuB;IAChC,CAAC;IAAA;IAAA;MAAA5H,cAAA,GAAA6B,CAAA;IAAA;IAED;IACA,MAAMkG,eAAe;IAAA;IAAA,CAAA/H,cAAA,GAAAU,CAAA,SAAa;MAChC,GAAGkH,WAAW;MACdC,mBAAmB;MAAE;MAAA,CAAA7H,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACC,mBAAmB;MAAA;MAAA,CAAA7H,cAAA,GAAA6B,CAAA,WAAI;QACtDmG,cAAc,EAAEC,IAAI,CAACC,IAAI,CAAC;QAAC;QAAA,CAAAlI,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACO,IAAI,EAAEvE,MAAM;QAAA;QAAA,CAAA5D,cAAA,GAAA6B,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;QAC9DuG,eAAe;QAAE;QAAA,CAAApI,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACO,IAAI,EAAEvE,MAAM;QAAA;QAAA,CAAA5D,cAAA,GAAA6B,CAAA,WAAI,CAAC;QAC9CwG,iBAAiB,EAAE,EAAE;QACrBC,kBAAkB,EAAE,IAAI,CAACC,uBAAuB,CAACX,WAAW,CAAChB,QAAQ,CAAC;QACtE4B,aAAa,EAAEtC;OAChB;MACD4B,eAAe;MAAE;MAAA,CAAA9H,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACE,eAAe;MAAA;MAAA,CAAA9H,cAAA,GAAA6B,CAAA,WAAI;QAC9C4G,WAAW,EAAE;QAAC;QAAA,CAAAzI,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACxB,aAAa;QAAA;QAAA,CAAApG,cAAA,GAAA6B,CAAA,WAAI,GAAG,KAAI,GAAG;QACrD6G,SAAS;QAAE;QAAA,CAAA1I,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACzB,eAAe;QAAA;QAAA,CAAAnG,cAAA,GAAA6B,CAAA,WAAI,GAAG;QAC7C8G,aAAa,EAAE;QAAC;QAAA,CAAA3I,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACxB,aAAa;QAAA;QAAA,CAAApG,cAAA,GAAA6B,CAAA,WAAI,GAAG,KAAI,IAAI;QACxD+G,gBAAgB,EAAE;QAAC;QAAA,CAAA5I,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACxB,aAAa;QAAA;QAAA,CAAApG,cAAA,GAAA6B,CAAA,WAAI,GAAG,KAAI;OACxD;MACDyE,OAAO;MAAE;MAAA,CAAAtG,cAAA,GAAA6B,CAAA,WAAA+F,WAAW,CAACtB,OAAO;MAAA;MAAA,CAAAtG,cAAA,GAAA6B,CAAA,WAAI,OAAO;KACxC;IAAA;IAAA7B,cAAA,GAAAU,CAAA;IAED,OAAOqH,eAAe;EACxB;EAEA;;;;;;;EAOQQ,uBAAuBA,CAAC3B,QAAgB;IAAA;IAAA5G,cAAA,GAAA8B,CAAA;IAC9C,MAAM+G,OAAO;IAAA;IAAA,CAAA7I,cAAA,GAAAU,CAAA,SAA2B;MACtC,UAAU,EAAE,KAAK;MACjB,aAAa,EAAE,IAAI;MACnB,iBAAiB,EAAE,KAAK;MACxB,SAAS,EAAE,IAAI;MACf,SAAS,EAAE,IAAI;MACf,UAAU,EAAE;KACb;IAAA;IAAAV,cAAA,GAAAU,CAAA;IAED,OAAO,2BAAAV,cAAA,GAAA6B,CAAA,WAAAgH,OAAO,CAACjC,QAAQ,CAAC;IAAA;IAAA,CAAA5G,cAAA,GAAA6B,CAAA,WAAI,IAAI;EAClC;EAEA;;;EAGAiH,OAAOA,CAAA;IAAA;IAAA9I,cAAA,GAAA8B,CAAA;IAAA9B,cAAA,GAAAU,CAAA;IACL;IACA,KAAK,MAAM,CAACsG,IAAI,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACvF,QAAQ,EAAE;MAAA;MAAA1B,cAAA,GAAAU,CAAA;MAC3CuG,OAAO,CAACC,KAAK,EAAE;IACjB;IAAC;IAAAlH,cAAA,GAAAU,CAAA;IACD,IAAI,CAACgB,QAAQ,CAACyF,KAAK,EAAE;IAErB;IAAA;IAAAnH,cAAA,GAAAU,CAAA;IACA,IAAI,CAACc,KAAK,CAAC2F,KAAK,EAAE;IAAA;IAAAnH,cAAA,GAAAU,CAAA;IAElByD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B", "ignoreList": []}