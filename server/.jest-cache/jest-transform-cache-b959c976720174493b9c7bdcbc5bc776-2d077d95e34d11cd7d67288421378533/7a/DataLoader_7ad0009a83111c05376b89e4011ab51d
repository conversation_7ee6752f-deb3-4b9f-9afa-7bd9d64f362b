8eac27fca2d80ad173598d8b677510a6
/* istanbul ignore next */
function cov_2i3pzto8wf() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts";
  var hash = "f95e990f5d70642e2e90faea2421fbb929b69c4b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts",
    statementMap: {
      "0": {
        start: {
          line: 20,
          column: 17
        },
        end: {
          line: 20,
          column: 44
        }
      },
      "1": {
        start: {
          line: 22,
          column: 26
        },
        end: {
          line: 22,
          column: 45
        }
      },
      "2": {
        start: {
          line: 24,
          column: 30
        },
        end: {
          line: 27,
          column: 1
        }
      },
      "3": {
        start: {
          line: 29,
          column: 26
        },
        end: {
          line: 42,
          column: 1
        }
      },
      "4": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 21
        }
      },
      "5": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 24
        }
      },
      "6": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 22
        }
      },
      "7": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 69,
          column: 10
        }
      },
      "8": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 80,
          column: 9
        }
      },
      "9": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 36
        }
      },
      "10": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 47
        }
      },
      "11": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "12": {
        start: {
          line: 83,
          column: 27
        },
        end: {
          line: 83,
          column: 49
        }
      },
      "13": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 84,
          column: 26
        }
      },
      "14": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 36
        }
      },
      "15": {
        start: {
          line: 97,
          column: 26
        },
        end: {
          line: 97,
          column: 36
        }
      },
      "16": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 131,
          column: 9
        }
      },
      "17": {
        start: {
          line: 100,
          column: 30
        },
        end: {
          line: 100,
          column: 57
        }
      },
      "18": {
        start: {
          line: 102,
          column: 33
        },
        end: {
          line: 102,
          column: 35
        }
      },
      "19": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 108,
          column: 13
        }
      },
      "20": {
        start: {
          line: 104,
          column: 37
        },
        end: {
          line: 104,
          column: 71
        }
      },
      "21": {
        start: {
          line: 106,
          column: 41
        },
        end: {
          line: 106,
          column: 90
        }
      },
      "22": {
        start: {
          line: 106,
          column: 63
        },
        end: {
          line: 106,
          column: 89
        }
      },
      "23": {
        start: {
          line: 107,
          column: 16
        },
        end: {
          line: 107,
          column: 55
        }
      },
      "24": {
        start: {
          line: 110,
          column: 31
        },
        end: {
          line: 112,
          column: 118
        }
      },
      "25": {
        start: {
          line: 114,
          column: 26
        },
        end: {
          line: 114,
          column: 84
        }
      },
      "26": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 118,
          column: 13
        }
      },
      "27": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 117,
          column: 54
        }
      },
      "28": {
        start: {
          line: 119,
          column: 27
        },
        end: {
          line: 124,
          column: 13
        }
      },
      "29": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 87
        }
      },
      "30": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 26
        }
      },
      "31": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 46
        }
      },
      "32": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 130,
          column: 97
        }
      },
      "33": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 161,
          column: 9
        }
      },
      "34": {
        start: {
          line: 141,
          column: 26
        },
        end: {
          line: 141,
          column: 60
        }
      },
      "35": {
        start: {
          line: 142,
          column: 30
        },
        end: {
          line: 142,
          column: 32
        }
      },
      "36": {
        start: {
          line: 143,
          column: 12
        },
        end: {
          line: 152,
          column: 13
        }
      },
      "37": {
        start: {
          line: 144,
          column: 33
        },
        end: {
          line: 144,
          column: 64
        }
      },
      "38": {
        start: {
          line: 145,
          column: 33
        },
        end: {
          line: 145,
          column: 53
        }
      },
      "39": {
        start: {
          line: 147,
          column: 16
        },
        end: {
          line: 151,
          column: 17
        }
      },
      "40": {
        start: {
          line: 150,
          column: 20
        },
        end: {
          line: 150,
          column: 45
        }
      },
      "41": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 155,
          column: 13
        }
      },
      "42": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 70
        }
      },
      "43": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 156,
          column: 57
        }
      },
      "44": {
        start: {
          line: 157,
          column: 12
        },
        end: {
          line: 157,
          column: 29
        }
      },
      "45": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 160,
          column: 99
        }
      },
      "46": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 219,
          column: 9
        }
      },
      "47": {
        start: {
          line: 173,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "48": {
        start: {
          line: 174,
          column: 31
        },
        end: {
          line: 174,
          column: 55
        }
      },
      "49": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 178,
          column: 17
        }
      },
      "50": {
        start: {
          line: 176,
          column: 20
        },
        end: {
          line: 176,
          column: 58
        }
      },
      "51": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 177,
          column: 39
        }
      },
      "52": {
        start: {
          line: 181,
          column: 28
        },
        end: {
          line: 181,
          column: 61
        }
      },
      "53": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "54": {
        start: {
          line: 185,
          column: 31
        },
        end: {
          line: 185,
          column: 50
        }
      },
      "55": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 196,
          column: 17
        }
      },
      "56": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 188,
          column: 39
        }
      },
      "57": {
        start: {
          line: 190,
          column: 21
        },
        end: {
          line: 196,
          column: 17
        }
      },
      "58": {
        start: {
          line: 191,
          column: 20
        },
        end: {
          line: 191,
          column: 49
        }
      },
      "59": {
        start: {
          line: 192,
          column: 20
        },
        end: {
          line: 192,
          column: 71
        }
      },
      "60": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 72
        }
      },
      "61": {
        start: {
          line: 198,
          column: 17
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "62": {
        start: {
          line: 199,
          column: 16
        },
        end: {
          line: 202,
          column: 51
        }
      },
      "63": {
        start: {
          line: 201,
          column: 36
        },
        end: {
          line: 201,
          column: 47
        }
      },
      "64": {
        start: {
          line: 202,
          column: 33
        },
        end: {
          line: 202,
          column: 49
        }
      },
      "65": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 205,
          column: 66
        }
      },
      "66": {
        start: {
          line: 208,
          column: 12
        },
        end: {
          line: 213,
          column: 13
        }
      },
      "67": {
        start: {
          line: 209,
          column: 16
        },
        end: {
          line: 212,
          column: 19
        }
      },
      "68": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 73
        }
      },
      "69": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 29
        }
      },
      "70": {
        start: {
          line: 218,
          column: 12
        },
        end: {
          line: 218,
          column: 109
        }
      },
      "71": {
        start: {
          line: 229,
          column: 26
        },
        end: {
          line: 229,
          column: 36
        }
      },
      "72": {
        start: {
          line: 230,
          column: 23
        },
        end: {
          line: 230,
          column: 25
        }
      },
      "73": {
        start: {
          line: 231,
          column: 25
        },
        end: {
          line: 231,
          column: 27
        }
      },
      "74": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "75": {
        start: {
          line: 232,
          column: 21
        },
        end: {
          line: 232,
          column: 22
        }
      },
      "76": {
        start: {
          line: 233,
          column: 29
        },
        end: {
          line: 233,
          column: 41
        }
      },
      "77": {
        start: {
          line: 234,
          column: 27
        },
        end: {
          line: 234,
          column: 66
        }
      },
      "78": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 240,
          column: 13
        }
      },
      "79": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 239,
          column: 17
        }
      },
      "80": {
        start: {
          line: 238,
          column: 20
        },
        end: {
          line: 238,
          column: 64
        }
      },
      "81": {
        start: {
          line: 242,
          column: 12
        },
        end: {
          line: 245,
          column: 13
        }
      },
      "82": {
        start: {
          line: 244,
          column: 16
        },
        end: {
          line: 244,
          column: 75
        }
      },
      "83": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 250,
          column: 13
        }
      },
      "84": {
        start: {
          line: 249,
          column: 16
        },
        end: {
          line: 249,
          column: 147
        }
      },
      "85": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 255,
          column: 13
        }
      },
      "86": {
        start: {
          line: 254,
          column: 16
        },
        end: {
          line: 254,
          column: 118
        }
      },
      "87": {
        start: {
          line: 257,
          column: 12
        },
        end: {
          line: 259,
          column: 13
        }
      },
      "88": {
        start: {
          line: 258,
          column: 16
        },
        end: {
          line: 258,
          column: 129
        }
      },
      "89": {
        start: {
          line: 261,
          column: 32
        },
        end: {
          line: 261,
          column: 54
        }
      },
      "90": {
        start: {
          line: 262,
          column: 23
        },
        end: {
          line: 262,
          column: 42
        }
      },
      "91": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 268,
          column: 9
        }
      },
      "92": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 264,
          column: 83
        }
      },
      "93": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 267,
          column: 84
        }
      },
      "94": {
        start: {
          line: 269,
          column: 8
        },
        end: {
          line: 275,
          column: 10
        }
      },
      "95": {
        start: {
          line: 286,
          column: 28
        },
        end: {
          line: 286,
          column: 30
        }
      },
      "96": {
        start: {
          line: 287,
          column: 27
        },
        end: {
          line: 287,
          column: 29
        }
      },
      "97": {
        start: {
          line: 288,
          column: 28
        },
        end: {
          line: 288,
          column: 29
        }
      },
      "98": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 296,
          column: 9
        }
      },
      "99": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 87
        }
      },
      "100": {
        start: {
          line: 293,
          column: 12
        },
        end: {
          line: 293,
          column: 101
        }
      },
      "101": {
        start: {
          line: 295,
          column: 12
        },
        end: {
          line: 295,
          column: 52
        }
      },
      "102": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 303,
          column: 10
        }
      },
      "103": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 315,
          column: 9
        }
      },
      "104": {
        start: {
          line: 314,
          column: 12
        },
        end: {
          line: 314,
          column: 28
        }
      },
      "105": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 30
        }
      },
      "106": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "107": {
        start: {
          line: 319,
          column: 12
        },
        end: {
          line: 333,
          column: 13
        }
      },
      "108": {
        start: {
          line: 320,
          column: 32
        },
        end: {
          line: 328,
          column: 18
        }
      },
      "109": {
        start: {
          line: 321,
          column: 20
        },
        end: {
          line: 327,
          column: 21
        }
      },
      "110": {
        start: {
          line: 322,
          column: 24
        },
        end: {
          line: 322,
          column: 63
        }
      },
      "111": {
        start: {
          line: 324,
          column: 24
        },
        end: {
          line: 324,
          column: 52
        }
      },
      "112": {
        start: {
          line: 326,
          column: 24
        },
        end: {
          line: 326,
          column: 47
        }
      },
      "113": {
        start: {
          line: 329,
          column: 16
        },
        end: {
          line: 329,
          column: 53
        }
      },
      "114": {
        start: {
          line: 332,
          column: 16
        },
        end: {
          line: 332,
          column: 64
        }
      },
      "115": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 335,
          column: 63
        }
      },
      "116": {
        start: {
          line: 342,
          column: 22
        },
        end: {
          line: 355,
          column: 8
        }
      },
      "117": {
        start: {
          line: 343,
          column: 22
        },
        end: {
          line: 343,
          column: 26
        }
      },
      "118": {
        start: {
          line: 344,
          column: 8
        },
        end: {
          line: 354,
          column: 10
        }
      },
      "119": {
        start: {
          line: 345,
          column: 12
        },
        end: {
          line: 347,
          column: 13
        }
      },
      "120": {
        start: {
          line: 346,
          column: 16
        },
        end: {
          line: 346,
          column: 38
        }
      },
      "121": {
        start: {
          line: 348,
          column: 12
        },
        end: {
          line: 353,
          column: 21
        }
      },
      "122": {
        start: {
          line: 349,
          column: 16
        },
        end: {
          line: 349,
          column: 43
        }
      },
      "123": {
        start: {
          line: 350,
          column: 16
        },
        end: {
          line: 352,
          column: 19
        }
      },
      "124": {
        start: {
          line: 351,
          column: 20
        },
        end: {
          line: 351,
          column: 53
        }
      },
      "125": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 369,
          column: 9
        }
      },
      "126": {
        start: {
          line: 368,
          column: 12
        },
        end: {
          line: 368,
          column: 31
        }
      },
      "127": {
        start: {
          line: 371,
          column: 32
        },
        end: {
          line: 387,
          column: 9
        }
      },
      "128": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 388,
          column: 31
        }
      },
      "129": {
        start: {
          line: 398,
          column: 24
        },
        end: {
          line: 405,
          column: 9
        }
      },
      "130": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 406,
          column: 41
        }
      },
      "131": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 415,
          column: 9
        }
      },
      "132": {
        start: {
          line: 414,
          column: 12
        },
        end: {
          line: 414,
          column: 28
        }
      },
      "133": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 416,
          column: 30
        }
      },
      "134": {
        start: {
          line: 418,
          column: 8
        },
        end: {
          line: 418,
          column: 27
        }
      },
      "135": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 419,
          column: 37
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 61,
            column: 4
          },
          end: {
            line: 61,
            column: 5
          }
        },
        loc: {
          start: {
            line: 61,
            column: 29
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 61
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 76,
            column: 4
          },
          end: {
            line: 76,
            column: 5
          }
        },
        loc: {
          start: {
            line: 76,
            column: 20
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 76
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 96,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        },
        loc: {
          start: {
            line: 96,
            column: 25
          },
          end: {
            line: 132,
            column: 5
          }
        },
        line: 96
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 106,
            column: 58
          },
          end: {
            line: 106,
            column: 59
          }
        },
        loc: {
          start: {
            line: 106,
            column: 63
          },
          end: {
            line: 106,
            column: 89
          }
        },
        line: 106
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 139,
            column: 5
          }
        },
        loc: {
          start: {
            line: 139,
            column: 27
          },
          end: {
            line: 162,
            column: 5
          }
        },
        line: 139
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 170,
            column: 4
          },
          end: {
            line: 170,
            column: 5
          }
        },
        loc: {
          start: {
            line: 170,
            column: 34
          },
          end: {
            line: 220,
            column: 5
          }
        },
        line: 170
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 201,
            column: 28
          },
          end: {
            line: 201,
            column: 29
          }
        },
        loc: {
          start: {
            line: 201,
            column: 36
          },
          end: {
            line: 201,
            column: 47
          }
        },
        line: 201
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 202,
            column: 25
          },
          end: {
            line: 202,
            column: 26
          }
        },
        loc: {
          start: {
            line: 202,
            column: 33
          },
          end: {
            line: 202,
            column: 49
          }
        },
        line: 202
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 228,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        },
        loc: {
          start: {
            line: 228,
            column: 35
          },
          end: {
            line: 276,
            column: 5
          }
        },
        line: 228
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 285,
            column: 5
          }
        },
        loc: {
          start: {
            line: 285,
            column: 41
          },
          end: {
            line: 304,
            column: 5
          }
        },
        line: 285
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 311,
            column: 5
          }
        },
        loc: {
          start: {
            line: 311,
            column: 37
          },
          end: {
            line: 336,
            column: 5
          }
        },
        line: 311
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 320,
            column: 48
          },
          end: {
            line: 320,
            column: 49
          }
        },
        loc: {
          start: {
            line: 320,
            column: 63
          },
          end: {
            line: 328,
            column: 17
          }
        },
        line: 320
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 342,
            column: 23
          },
          end: {
            line: 342,
            column: 24
          }
        },
        loc: {
          start: {
            line: 342,
            column: 29
          },
          end: {
            line: 355,
            column: 5
          }
        },
        line: 342
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 344,
            column: 15
          },
          end: {
            line: 344,
            column: 16
          }
        },
        loc: {
          start: {
            line: 344,
            column: 21
          },
          end: {
            line: 354,
            column: 9
          }
        },
        line: 344
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 348,
            column: 33
          },
          end: {
            line: 348,
            column: 34
          }
        },
        loc: {
          start: {
            line: 348,
            column: 39
          },
          end: {
            line: 353,
            column: 13
          }
        },
        line: 348
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 350,
            column: 37
          },
          end: {
            line: 350,
            column: 38
          }
        },
        loc: {
          start: {
            line: 350,
            column: 46
          },
          end: {
            line: 352,
            column: 17
          }
        },
        line: 350
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 365,
            column: 5
          }
        },
        loc: {
          start: {
            line: 365,
            column: 36
          },
          end: {
            line: 389,
            column: 5
          }
        },
        line: 365
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 397,
            column: 4
          },
          end: {
            line: 397,
            column: 5
          }
        },
        loc: {
          start: {
            line: 397,
            column: 38
          },
          end: {
            line: 407,
            column: 5
          }
        },
        line: 397
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 411,
            column: 5
          }
        },
        loc: {
          start: {
            line: 411,
            column: 14
          },
          end: {
            line: 420,
            column: 5
          }
        },
        line: 411
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 61,
            column: 16
          },
          end: {
            line: 61,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 61,
            column: 25
          },
          end: {
            line: 61,
            column: 27
          }
        }],
        line: 61
      },
      "1": {
        loc: {
          start: {
            line: 63,
            column: 21
          },
          end: {
            line: 63,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 21
          },
          end: {
            line: 63,
            column: 35
          }
        }, {
          start: {
            line: 63,
            column: 39
          },
          end: {
            line: 63,
            column: 47
          }
        }],
        line: 63
      },
      "2": {
        loc: {
          start: {
            line: 64,
            column: 29
          },
          end: {
            line: 64,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 29
          },
          end: {
            line: 64,
            column: 51
          }
        }, {
          start: {
            line: 64,
            column: 55
          },
          end: {
            line: 64,
            column: 59
          }
        }],
        line: 64
      },
      "3": {
        loc: {
          start: {
            line: 65,
            column: 30
          },
          end: {
            line: 65,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 65,
            column: 30
          },
          end: {
            line: 65,
            column: 53
          }
        }, {
          start: {
            line: 65,
            column: 57
          },
          end: {
            line: 65,
            column: 61
          }
        }],
        line: 65
      },
      "4": {
        loc: {
          start: {
            line: 66,
            column: 25
          },
          end: {
            line: 66,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 25
          },
          end: {
            line: 66,
            column: 43
          }
        }, {
          start: {
            line: 66,
            column: 47
          },
          end: {
            line: 66,
            column: 51
          }
        }],
        line: 66
      },
      "5": {
        loc: {
          start: {
            line: 67,
            column: 22
          },
          end: {
            line: 67,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 22
          },
          end: {
            line: 67,
            column: 37
          }
        }, {
          start: {
            line: 67,
            column: 41
          },
          end: {
            line: 67,
            column: 45
          }
        }],
        line: 67
      },
      "6": {
        loc: {
          start: {
            line: 68,
            column: 24
          },
          end: {
            line: 68,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 24
          },
          end: {
            line: 68,
            column: 41
          }
        }, {
          start: {
            line: 68,
            column: 45
          },
          end: {
            line: 68,
            column: 46
          }
        }],
        line: 68
      },
      "7": {
        loc: {
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "8": {
        loc: {
          start: {
            line: 110,
            column: 31
          },
          end: {
            line: 112,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 111,
            column: 18
          },
          end: {
            line: 111,
            column: 56
          }
        }, {
          start: {
            line: 112,
            column: 18
          },
          end: {
            line: 112,
            column: 118
          }
        }],
        line: 110
      },
      "9": {
        loc: {
          start: {
            line: 116,
            column: 12
          },
          end: {
            line: 118,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 12
          },
          end: {
            line: 118,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "10": {
        loc: {
          start: {
            line: 130,
            column: 39
          },
          end: {
            line: 130,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 130,
            column: 64
          },
          end: {
            line: 130,
            column: 77
          }
        }, {
          start: {
            line: 130,
            column: 80
          },
          end: {
            line: 130,
            column: 93
          }
        }],
        line: 130
      },
      "11": {
        loc: {
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 151,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 16
          },
          end: {
            line: 151,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "12": {
        loc: {
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 149,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 147,
            column: 37
          }
        }, {
          start: {
            line: 148,
            column: 20
          },
          end: {
            line: 148,
            column: 61
          }
        }, {
          start: {
            line: 149,
            column: 21
          },
          end: {
            line: 149,
            column: 46
          }
        }, {
          start: {
            line: 149,
            column: 50
          },
          end: {
            line: 149,
            column: 69
          }
        }],
        line: 147
      },
      "13": {
        loc: {
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 155,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 155,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "14": {
        loc: {
          start: {
            line: 160,
            column: 41
          },
          end: {
            line: 160,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 160,
            column: 66
          },
          end: {
            line: 160,
            column: 79
          }
        }, {
          start: {
            line: 160,
            column: 82
          },
          end: {
            line: 160,
            column: 95
          }
        }],
        line: 160
      },
      "15": {
        loc: {
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "16": {
        loc: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 178,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 178,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "17": {
        loc: {
          start: {
            line: 175,
            column: 20
          },
          end: {
            line: 175,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 175,
            column: 20
          },
          end: {
            line: 175,
            column: 26
          }
        }, {
          start: {
            line: 175,
            column: 30
          },
          end: {
            line: 175,
            column: 89
          }
        }],
        line: 175
      },
      "18": {
        loc: {
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        }, {
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 206,
            column: 13
          }
        }],
        line: 184
      },
      "19": {
        loc: {
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 196,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 196,
            column: 17
          }
        }, {
          start: {
            line: 190,
            column: 21
          },
          end: {
            line: 196,
            column: 17
          }
        }],
        line: 187
      },
      "20": {
        loc: {
          start: {
            line: 190,
            column: 21
          },
          end: {
            line: 196,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 21
          },
          end: {
            line: 196,
            column: 17
          }
        }, {
          start: {
            line: 194,
            column: 21
          },
          end: {
            line: 196,
            column: 17
          }
        }],
        line: 190
      },
      "21": {
        loc: {
          start: {
            line: 190,
            column: 25
          },
          end: {
            line: 190,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 25
          },
          end: {
            line: 190,
            column: 41
          }
        }, {
          start: {
            line: 190,
            column: 45
          },
          end: {
            line: 190,
            column: 76
          }
        }],
        line: 190
      },
      "22": {
        loc: {
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 206,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 206,
            column: 13
          }
        }, {
          start: {
            line: 204,
            column: 17
          },
          end: {
            line: 206,
            column: 13
          }
        }],
        line: 198
      },
      "23": {
        loc: {
          start: {
            line: 208,
            column: 12
          },
          end: {
            line: 213,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 12
          },
          end: {
            line: 213,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "24": {
        loc: {
          start: {
            line: 218,
            column: 51
          },
          end: {
            line: 218,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 218,
            column: 76
          },
          end: {
            line: 218,
            column: 89
          }
        }, {
          start: {
            line: 218,
            column: 92
          },
          end: {
            line: 218,
            column: 105
          }
        }],
        line: 218
      },
      "25": {
        loc: {
          start: {
            line: 234,
            column: 39
          },
          end: {
            line: 234,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 234,
            column: 39
          },
          end: {
            line: 234,
            column: 50
          }
        }, {
          start: {
            line: 234,
            column: 54
          },
          end: {
            line: 234,
            column: 63
          }
        }],
        line: 234
      },
      "26": {
        loc: {
          start: {
            line: 237,
            column: 16
          },
          end: {
            line: 239,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 16
          },
          end: {
            line: 239,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "27": {
        loc: {
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 237,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 237,
            column: 40
          }
        }, {
          start: {
            line: 237,
            column: 44
          },
          end: {
            line: 237,
            column: 73
          }
        }],
        line: 237
      },
      "28": {
        loc: {
          start: {
            line: 242,
            column: 12
          },
          end: {
            line: 245,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 242,
            column: 12
          },
          end: {
            line: 245,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 242
      },
      "29": {
        loc: {
          start: {
            line: 242,
            column: 16
          },
          end: {
            line: 243,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 242,
            column: 16
          },
          end: {
            line: 242,
            column: 60
          }
        }, {
          start: {
            line: 243,
            column: 16
          },
          end: {
            line: 243,
            column: 44
          }
        }, {
          start: {
            line: 243,
            column: 48
          },
          end: {
            line: 243,
            column: 76
          }
        }],
        line: 242
      },
      "30": {
        loc: {
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 250,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 12
          },
          end: {
            line: 250,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "31": {
        loc: {
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 248,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 246,
            column: 16
          },
          end: {
            line: 246,
            column: 58
          }
        }, {
          start: {
            line: 247,
            column: 16
          },
          end: {
            line: 247,
            column: 76
          }
        }, {
          start: {
            line: 248,
            column: 16
          },
          end: {
            line: 248,
            column: 76
          }
        }],
        line: 246
      },
      "32": {
        loc: {
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 255,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 12
          },
          end: {
            line: 255,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "33": {
        loc: {
          start: {
            line: 252,
            column: 16
          },
          end: {
            line: 253,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 16
          },
          end: {
            line: 252,
            column: 56
          }
        }, {
          start: {
            line: 253,
            column: 16
          },
          end: {
            line: 253,
            column: 95
          }
        }],
        line: 252
      },
      "34": {
        loc: {
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 259,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 259,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "35": {
        loc: {
          start: {
            line: 257,
            column: 16
          },
          end: {
            line: 257,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 257,
            column: 16
          },
          end: {
            line: 257,
            column: 32
          }
        }, {
          start: {
            line: 257,
            column: 36
          },
          end: {
            line: 257,
            column: 95
          }
        }],
        line: 257
      },
      "36": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 268,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 268,
            column: 9
          }
        }, {
          start: {
            line: 266,
            column: 13
          },
          end: {
            line: 268,
            column: 9
          }
        }],
        line: 263
      },
      "37": {
        loc: {
          start: {
            line: 291,
            column: 46
          },
          end: {
            line: 291,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 46
          },
          end: {
            line: 291,
            column: 76
          }
        }, {
          start: {
            line: 291,
            column: 80
          },
          end: {
            line: 291,
            column: 81
          }
        }],
        line: 291
      },
      "38": {
        loc: {
          start: {
            line: 293,
            column: 53
          },
          end: {
            line: 293,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 53
          },
          end: {
            line: 293,
            column: 90
          }
        }, {
          start: {
            line: 293,
            column: 94
          },
          end: {
            line: 293,
            column: 95
          }
        }],
        line: 293
      },
      "39": {
        loc: {
          start: {
            line: 301,
            column: 25
          },
          end: {
            line: 301,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 301,
            column: 48
          },
          end: {
            line: 301,
            column: 80
          }
        }, {
          start: {
            line: 301,
            column: 83
          },
          end: {
            line: 301,
            column: 84
          }
        }],
        line: 301
      },
      "40": {
        loc: {
          start: {
            line: 321,
            column: 20
          },
          end: {
            line: 327,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 20
          },
          end: {
            line: 327,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "41": {
        loc: {
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 347,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 347,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 345
      },
      "42": {
        loc: {
          start: {
            line: 367,
            column: 8
          },
          end: {
            line: 369,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 367,
            column: 8
          },
          end: {
            line: 369,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 367
      },
      "43": {
        loc: {
          start: {
            line: 367,
            column: 12
          },
          end: {
            line: 367,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 367,
            column: 12
          },
          end: {
            line: 367,
            column: 43
          }
        }, {
          start: {
            line: 367,
            column: 47
          },
          end: {
            line: 367,
            column: 74
          }
        }],
        line: 367
      },
      "44": {
        loc: {
          start: {
            line: 373,
            column: 33
          },
          end: {
            line: 379,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 373,
            column: 33
          },
          end: {
            line: 373,
            column: 64
          }
        }, {
          start: {
            line: 373,
            column: 68
          },
          end: {
            line: 379,
            column: 13
          }
        }],
        line: 373
      },
      "45": {
        loc: {
          start: {
            line: 374,
            column: 43
          },
          end: {
            line: 374,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 374,
            column: 43
          },
          end: {
            line: 374,
            column: 67
          }
        }, {
          start: {
            line: 374,
            column: 71
          },
          end: {
            line: 374,
            column: 72
          }
        }],
        line: 374
      },
      "46": {
        loc: {
          start: {
            line: 375,
            column: 33
          },
          end: {
            line: 375,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 375,
            column: 33
          },
          end: {
            line: 375,
            column: 57
          }
        }, {
          start: {
            line: 375,
            column: 61
          },
          end: {
            line: 375,
            column: 62
          }
        }],
        line: 375
      },
      "47": {
        loc: {
          start: {
            line: 380,
            column: 29
          },
          end: {
            line: 385,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 380,
            column: 29
          },
          end: {
            line: 380,
            column: 56
          }
        }, {
          start: {
            line: 380,
            column: 60
          },
          end: {
            line: 385,
            column: 13
          }
        }],
        line: 380
      },
      "48": {
        loc: {
          start: {
            line: 381,
            column: 30
          },
          end: {
            line: 381,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 381,
            column: 30
          },
          end: {
            line: 381,
            column: 55
          }
        }, {
          start: {
            line: 381,
            column: 59
          },
          end: {
            line: 381,
            column: 62
          }
        }],
        line: 381
      },
      "49": {
        loc: {
          start: {
            line: 382,
            column: 27
          },
          end: {
            line: 382,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 382,
            column: 27
          },
          end: {
            line: 382,
            column: 54
          }
        }, {
          start: {
            line: 382,
            column: 58
          },
          end: {
            line: 382,
            column: 61
          }
        }],
        line: 382
      },
      "50": {
        loc: {
          start: {
            line: 383,
            column: 32
          },
          end: {
            line: 383,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 383,
            column: 32
          },
          end: {
            line: 383,
            column: 57
          }
        }, {
          start: {
            line: 383,
            column: 61
          },
          end: {
            line: 383,
            column: 64
          }
        }],
        line: 383
      },
      "51": {
        loc: {
          start: {
            line: 384,
            column: 35
          },
          end: {
            line: 384,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 384,
            column: 35
          },
          end: {
            line: 384,
            column: 60
          }
        }, {
          start: {
            line: 384,
            column: 64
          },
          end: {
            line: 384,
            column: 67
          }
        }],
        line: 384
      },
      "52": {
        loc: {
          start: {
            line: 386,
            column: 21
          },
          end: {
            line: 386,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 21
          },
          end: {
            line: 386,
            column: 40
          }
        }, {
          start: {
            line: 386,
            column: 44
          },
          end: {
            line: 386,
            column: 51
          }
        }],
        line: 386
      },
      "53": {
        loc: {
          start: {
            line: 406,
            column: 15
          },
          end: {
            line: 406,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 406,
            column: 15
          },
          end: {
            line: 406,
            column: 32
          }
        }, {
          start: {
            line: 406,
            column: 36
          },
          end: {
            line: 406,
            column: 40
          }
        }],
        line: 406
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0, 0],
      "30": [0, 0],
      "31": [0, 0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts",
      mappings: "AAAA;;;;;;;;;;GAUG;AAEH,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAA;AACrD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AACpC,OAAO,EAAE,KAAK,EAAE,MAAM,IAAI,CAAA;AAI1B,OAAO,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAA;AAEvD,+EAA+E;AAC/E,OAAO;AACP,+EAA+E;AAE/E,eAAe;AACf,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAA;AAE5C,gBAAgB;AAChB,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAU,CAAA;AAEtD,gBAAgB;AAChB,MAAM,qBAAqB,GAAG;IAC5B,OAAO,EAAE,OAAgB;IACzB,SAAS,EAAE,kBAAkB;CACrB,CAAA;AAEV,aAAa;AACb,MAAM,iBAAiB,GAAG;IACxB,aAAa;IACb,yBAAyB,EAAE,EAAE;IAC7B,aAAa;IACb,iBAAiB,EAAE,GAAG;IACtB,aAAa;IACb,iBAAiB,EAAE,GAAG;IACtB,WAAW;IACX,eAAe,EAAE;QACf,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB;QAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM;QAC7D,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS;KACnE;CACF,CAAA;AAgEV,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,UAAU;IACb,MAAM,CAA4B;IAClC,KAAK,GAAyD,IAAI,GAAG,EAAE,CAAA;IACvE,QAAQ,GAAqB,IAAI,GAAG,EAAE,CAAA;IACtC,WAAW,GAAmC,IAAI,CAAA;IAE1D;;;;OAIG;IACH,YAAY,SAA2B,EAAE;QACvC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,QAAQ;YACnC,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,IAAI;YAC/C,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,IAAI;YACjD,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI;YACvC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE,MAAM;YACzC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;SACnC,CAAA;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACX,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,WAAW,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QAEtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAA;YACrC,OAAO,MAAM,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACzB,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,YAAY;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC;YACH,YAAY;YACZ,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;YAE7C,YAAY;YACZ,MAAM,YAAY,GAAe,EAAE,CAAA;YAEnC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;gBACvD,mBAAmB;gBACnB,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC1E,YAAY,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAA;YACxC,CAAC;YAED,UAAU;YACV,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBAC7C,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;gBACxC,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,eAAe,EAAE,YAAY,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,EAAE,CAAA;YAExG,YAAY;YACZ,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAA;YAExE,WAAW;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;YACvC,CAAC;YAED,MAAM,MAAM,GAAmB;gBAC7B,SAAS,EAAE,YAAY;gBACvB,KAAK;gBACL,UAAU;gBACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAA;YAED,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,CAAC,MAAM,UAAU,KAAK,CAAC,SAAS,IAAI,CAAC,CAAA;YAE1E,OAAO,MAAM,CAAA;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;YACjC,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACtF,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAChD,MAAM,SAAS,GAAa,EAAE,CAAA;YAE9B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;gBAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAErC,mBAAmB;gBACnB,IAAI,QAAQ,CAAC,MAAM,EAAE;oBACjB,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAQ,CAAC;oBAChD,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACvD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC1B,CAAC;YACH,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAA;YACvD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,QAAQ,SAAS,CAAC,MAAM,OAAO,CAAC,CAAA;YAC5C,OAAO,SAAS,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,aAAa,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACxF,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,aAAa,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,OAAO;YACP,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;gBACvC,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;oBAC1E,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAA;oBACrC,OAAO,MAAM,CAAC,IAAI,CAAA;gBACpB,CAAC;YACH,CAAC;YAED,SAAS;YACT,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACjD,IAAI,SAAqB,CAAA;YAEzB,WAAW;YACX,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;gBAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;gBAElC,+BAA+B;gBAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,SAAS,GAAG,MAAM,CAAA;gBACpB,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC/D,SAAS,GAAG,MAAM,CAAC,SAAS,CAAA;oBAC5B,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,CAAC,MAAM,KAAK,CAAC,CAAA;gBACpD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;gBACrD,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC1C,SAAS,GAAG,OAAO;qBAChB,KAAK,CAAC,IAAI,CAAC;qBACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;qBAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAClC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;YACnD,CAAC;YAED,OAAO;YACP,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACvB,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAA;YACJ,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,CAAA;YAC5D,OAAO,SAAS,CAAA;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QAClG,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,aAAa,CAAC,SAAqB;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,MAAM,QAAQ,GAAa,EAAE,CAAA;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,QAAQ,CAAC,EAAE,IAAI,SAAS,GAAG,CAAA;YAEtD,SAAS;YACT,KAAK,MAAM,KAAK,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACtD,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC5E,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA;gBAC7C,CAAC;YACH,CAAC;YAED,YAAY;YACZ,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ;gBAC5C,QAAQ,CAAC,eAAe,GAAG,CAAC,IAAI,QAAQ,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,oCAAoC,CAAC,CAAA;YAC5D,CAAC;YAED,IAAI,OAAO,QAAQ,CAAC,aAAa,KAAK,QAAQ;gBAC1C,QAAQ,CAAC,aAAa,GAAG,iBAAiB,CAAC,iBAAiB;gBAC5D,QAAQ,CAAC,aAAa,GAAG,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,wBAAwB,iBAAiB,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB,UAAU,CAAC,CAAA;YACpI,CAAC;YAED,SAAS;YACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;gBACxC,QAAQ,CAAC,eAAe,CAAC,MAAM,KAAK,iBAAiB,CAAC,yBAAyB,EAAE,CAAC;gBACpF,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,4BAA4B,iBAAiB,CAAC,yBAAyB,QAAQ,CAAC,CAAA;YACvG,CAAC;YAED,UAAU;YACV,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpF,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,SAAS,QAAQ,CAAC,OAAO,mBAAmB,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAClH,CAAC;QACH,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;QAElC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,SAAS,QAAQ,CAAC,MAAM,MAAM,CAAC,CAAA;QACxE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,MAAM,UAAU,eAAe,IAAI,CAAC,CAAA;QACzE,CAAC;QAED,OAAO;YACL,MAAM;YACN,MAAM;YACN,QAAQ;YACR,eAAe,EAAE,SAAS,CAAC,MAAM;YACjC,eAAe;SAChB,CAAA;IACH,CAAC;IAED;;;;;;;OAOG;IACK,eAAe,CAAC,SAAqB,EAAE,QAAgB;QAC7D,MAAM,WAAW,GAA2B,EAAE,CAAA;QAC9C,MAAM,UAAU,GAA2B,EAAE,CAAA;QAC7C,IAAI,aAAa,GAAG,CAAC,CAAA;QAErB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,QAAQ;YACR,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAE1E,UAAU;YACV,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAExF,SAAS;YACT,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAA;QACzC,CAAC;QAED,OAAO;YACL,WAAW,EAAE,SAAS,CAAC,MAAM;YAC7B,WAAW;YACX,UAAU;YACV,WAAW,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxE,SAAS,EAAE,QAAQ;SACpB,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,eAAe,CAAC,SAAmB;QAC/C,UAAU;QACV,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,OAAO,CAAC,KAAK,EAAE,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QAErB,UAAU;QACV,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,EAAE;oBAC5C,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;wBAC3B,OAAO,CAAC,GAAG,CAAC,eAAe,QAAQ,EAAE,CAAC,CAAA;wBACtC,OAAO;wBACP,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;wBAC3B,oBAAoB;wBACpB,IAAI,CAAC,eAAe,EAAE,CAAA;oBACxB,CAAC;gBACH,CAAC,CAAC,CAAA;gBAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,gBAAgB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAA;YACjD,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAA;IACxD,CAAC;IAED;;;;OAIG;IACK,eAAe,GAAG,CAAC,GAAG,EAAE;QAC9B,IAAI,OAAO,GAA0B,IAAI,CAAA;QACzC,OAAO,GAAG,EAAE;YACV,IAAI,OAAO,EAAE,CAAC;gBACZ,YAAY,CAAC,OAAO,CAAC,CAAA;YACvB,CAAC;YACD,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;gBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBAC3B,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;gBAClC,CAAC,CAAC,CAAA;YACJ,CAAC,EAAE,IAAI,CAAC,CAAA,CAAC,OAAO;QAClB,CAAC,CAAA;IACH,CAAC,CAAC,EAAE,CAAA;IAEJ;;;;;;;;OAQG;IACK,kBAAkB,CAAC,WAAgB;QACzC,mBAAmB;QACnB,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,eAAe,EAAE,CAAC;YACnE,OAAO,WAAuB,CAAA;QAChC,CAAC;QAED,qBAAqB;QACrB,MAAM,eAAe,GAAa;YAChC,GAAG,WAAW;YACd,mBAAmB,EAAE,WAAW,CAAC,mBAAmB,IAAI;gBACtD,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC9D,eAAe,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;gBAC9C,iBAAiB,EAAE,EAAE;gBACrB,kBAAkB,EAAE,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACtE,aAAa,EAAE,SAAS;aACzB;YACD,eAAe,EAAE,WAAW,CAAC,eAAe,IAAI;gBAC9C,WAAW,EAAE,CAAC,WAAW,CAAC,aAAa,IAAI,GAAG,CAAC,GAAG,GAAG;gBACrD,SAAS,EAAE,WAAW,CAAC,eAAe,IAAI,GAAG;gBAC7C,aAAa,EAAE,CAAC,WAAW,CAAC,aAAa,IAAI,GAAG,CAAC,GAAG,IAAI;gBACxD,gBAAgB,EAAE,CAAC,WAAW,CAAC,aAAa,IAAI,GAAG,CAAC,GAAG,IAAI;aAC5D;YACD,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,OAAO;SACxC,CAAA;QAED,OAAO,eAAe,CAAA;IACxB,CAAC;IAED;;;;;;OAMG;IACK,uBAAuB,CAAC,QAAgB;QAC9C,MAAM,OAAO,GAA2B;YACtC,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,IAAI;YACnB,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;SACjB,CAAA;QAED,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,UAAU;QACV,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,OAAO,CAAC,KAAK,EAAE,CAAA;QACjB,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QAErB,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QAElB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAC9B,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataLoader.ts"],
      sourcesContent: ["/**\n * \u6570\u636E\u52A0\u8F7D\u5668\n * \n * \u8D1F\u8D23\u4ECE\u6587\u4EF6\u7CFB\u7EDF\u52A0\u8F7D\u8BED\u7D20\u6570\u636E\uFF0C\u652F\u6301\u591A\u8BED\u79CD\u3001\u7248\u672C\u7BA1\u7406\u548C\u6570\u636E\u9A8C\u8BC1\u3002\n * \u5B9E\u73B0\u4E86\u70ED\u91CD\u8F7D\u3001\u7F13\u5B58\u4F18\u5316\u548C\u9519\u8BEF\u6062\u590D\u673A\u5236\u3002\n * \n * @fileoverview \u6570\u636E\u52A0\u8F7D\u548C\u7BA1\u7406\u6838\u5FC3\u6A21\u5757\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport { readFile, readdir, stat } from 'fs/promises'\nimport { join, extname } from 'path'\nimport { watch } from 'fs'\n\n// \u7C7B\u578B\u5BFC\u5165\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\nimport { SUPPORTED_VERSIONS } from '../../types/common'\n\n// ============================================================================\n// \u914D\u7F6E\u5E38\u91CF\n// ============================================================================\n\n/** \u6570\u636E\u6587\u4EF6\u76EE\u5F55\u8DEF\u5F84 */\nconst DATA_DIR = join(process.cwd(), 'data')\n\n/** \u652F\u6301\u7684\u6570\u636E\u6587\u4EF6\u683C\u5F0F */\nconst SUPPORTED_FORMATS = ['.json', '.jsonl'] as const\n\n/** \u6570\u636E\u7248\u672C\u517C\u5BB9\u6027\u914D\u7F6E */\nconst VERSION_COMPATIBILITY = {\n  current: '2.0.0' as const,\n  supported: SUPPORTED_VERSIONS\n} as const\n\n/** \u6570\u636E\u9A8C\u8BC1\u914D\u7F6E */\nconst VALIDATION_CONFIG = {\n  /** \u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 */\n  semantic_vector_dimension: 20,\n  /** \u6700\u5C0F\u8D28\u91CF\u8BC4\u5206 */\n  min_quality_score: 0.0,\n  /** \u6700\u5927\u8D28\u91CF\u8BC4\u5206 */\n  max_quality_score: 1.0,\n  /** \u5FC5\u9700\u5B57\u6BB5 */\n  required_fields: [\n    'id', 'text', 'category', 'subcategory', 'cultural_context',\n    'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n    'language_properties', 'quality_metrics', 'created_at', 'source', 'version'\n  ] as const\n} as const\n\n// ============================================================================\n// \u63A5\u53E3\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u6570\u636E\u52A0\u8F7D\u914D\u7F6E\u63A5\u53E3\n */\nexport interface DataLoaderConfig {\n  /** \u6570\u636E\u76EE\u5F55\u8DEF\u5F84 */\n  dataDir?: string\n  /** \u662F\u5426\u542F\u7528\u70ED\u91CD\u8F7D */\n  enableHotReload?: boolean\n  /** \u662F\u5426\u542F\u7528\u6570\u636E\u9A8C\u8BC1 */\n  enableValidation?: boolean\n  /** \u662F\u5426\u542F\u7528\u7F13\u5B58 */\n  enableCache?: boolean\n  /** \u7F13\u5B58TTL (\u79D2) */\n  cacheTTL?: number\n  /** \u6700\u5927\u91CD\u8BD5\u6B21\u6570 */\n  maxRetries?: number\n}\n\n/**\n * \u6570\u636E\u52A0\u8F7D\u7ED3\u679C\u63A5\u53E3\n */\nexport interface DataLoadResult {\n  /** \u52A0\u8F7D\u7684\u8BED\u7D20\u6570\u636E */\n  morphemes: Morpheme[]\n  /** \u52A0\u8F7D\u7EDF\u8BA1\u4FE1\u606F */\n  stats: {\n    total_count: number\n    by_category: Record<string, number>\n    by_context: Record<string, number>\n    avg_quality: number\n    load_time: number\n  }\n  /** \u9A8C\u8BC1\u7ED3\u679C */\n  validation: {\n    passed: boolean\n    errors: string[]\n    warnings: string[]\n  }\n  /** \u52A0\u8F7D\u65F6\u95F4\u6233 */\n  timestamp: number\n}\n\n/**\n * \u6570\u636E\u9A8C\u8BC1\u7ED3\u679C\u63A5\u53E3\n */\nexport interface ValidationResult {\n  /** \u9A8C\u8BC1\u662F\u5426\u901A\u8FC7 */\n  passed: boolean\n  /** \u9519\u8BEF\u4FE1\u606F */\n  errors: string[]\n  /** \u8B66\u544A\u4FE1\u606F */\n  warnings: string[]\n  /** \u9A8C\u8BC1\u7684\u6570\u636E\u9879\u6570\u91CF */\n  validated_count: number\n  /** \u9A8C\u8BC1\u8017\u65F6 (\u6BEB\u79D2) */\n  validation_time: number\n}\n\n// ============================================================================\n// \u6570\u636E\u52A0\u8F7D\u5668\u7C7B\n// ============================================================================\n\n/**\n * \u6570\u636E\u52A0\u8F7D\u5668\u7C7B\n * \n * \u63D0\u4F9B\u8BED\u7D20\u6570\u636E\u7684\u52A0\u8F7D\u3001\u9A8C\u8BC1\u3001\u7F13\u5B58\u548C\u70ED\u91CD\u8F7D\u529F\u80FD\n */\nexport class DataLoader {\n  private config: Required<DataLoaderConfig>\n  private cache: Map<string, { data: Morpheme[], timestamp: number }> = new Map()\n  private watchers: Map<string, any> = new Map()\n  private loadPromise: Promise<DataLoadResult> | null = null\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   * \n   * @param config \u6570\u636E\u52A0\u8F7D\u914D\u7F6E\n   */\n  constructor(config: DataLoaderConfig = {}) {\n    this.config = {\n      dataDir: config.dataDir || DATA_DIR,\n      enableHotReload: config.enableHotReload ?? true,\n      enableValidation: config.enableValidation ?? true,\n      enableCache: config.enableCache ?? true,\n      cacheTTL: config.cacheTTL ?? 3600, // 1\u5C0F\u65F6\n      maxRetries: config.maxRetries ?? 3\n    }\n  }\n\n  /**\n   * \u52A0\u8F7D\u6240\u6709\u8BED\u7D20\u6570\u636E\n   * \n   * @returns \u6570\u636E\u52A0\u8F7D\u7ED3\u679C\n   */\n  async loadAll(): Promise<DataLoadResult> {\n    // \u9632\u6B62\u5E76\u53D1\u52A0\u8F7D\n    if (this.loadPromise) {\n      return this.loadPromise\n    }\n\n    this.loadPromise = this._performLoad()\n    \n    try {\n      const result = await this.loadPromise\n      return result\n    } finally {\n      this.loadPromise = null\n    }\n  }\n\n  /**\n   * \u6267\u884C\u6570\u636E\u52A0\u8F7D\n   * \n   * @private\n   * @returns \u6570\u636E\u52A0\u8F7D\u7ED3\u679C\n   */\n  private async _performLoad(): Promise<DataLoadResult> {\n    const startTime = Date.now()\n    \n    try {\n      // 1. \u626B\u63CF\u6570\u636E\u6587\u4EF6\n      const dataFiles = await this._scanDataFiles()\n      \n      // 2. \u52A0\u8F7D\u6570\u636E\u6587\u4EF6\n      const allMorphemes: Morpheme[] = []\n\n      for (const filePath of dataFiles) {\n        const rawMorphemes = await this._loadDataFile(filePath)\n        // \u9002\u914D\u6570\u636E\u683C\u5F0F\u4EE5\u652F\u6301v1.0\u517C\u5BB9\u6027\n        const adaptedMorphemes = rawMorphemes.map(m => this._adaptMorphemeData(m))\n        allMorphemes.push(...adaptedMorphemes)\n      }\n\n      // 3. \u6570\u636E\u9A8C\u8BC1\n      const validation = this.config.enableValidation \n        ? await this._validateData(allMorphemes)\n        : { passed: true, errors: [], warnings: [], validated_count: allMorphemes.length, validation_time: 0 }\n\n      // 4. \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F\n      const stats = this._calculateStats(allMorphemes, Date.now() - startTime)\n\n      // 5. \u8BBE\u7F6E\u70ED\u91CD\u8F7D\n      if (this.config.enableHotReload) {\n        await this._setupHotReload(dataFiles)\n      }\n\n      const result: DataLoadResult = {\n        morphemes: allMorphemes,\n        stats,\n        validation,\n        timestamp: Date.now()\n      }\n\n      console.log(`\u2705 \u6570\u636E\u52A0\u8F7D\u5B8C\u6210: ${allMorphemes.length}\u4E2A\u8BED\u7D20, \u8017\u65F6${stats.load_time}ms`)\n      \n      return result\n\n    } catch (error) {\n      console.error('\u274C \u6570\u636E\u52A0\u8F7D\u5931\u8D25:', error)\n      throw new Error(`\u6570\u636E\u52A0\u8F7D\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u626B\u63CF\u6570\u636E\u6587\u4EF6\n   * \n   * @private\n   * @returns \u6570\u636E\u6587\u4EF6\u8DEF\u5F84\u5217\u8868\n   */\n  private async _scanDataFiles(): Promise<string[]> {\n    try {\n      const files = await readdir(this.config.dataDir)\n      const dataFiles: string[] = []\n\n      for (const file of files) {\n        const filePath = join(this.config.dataDir, file)\n        const fileStat = await stat(filePath)\n\n        // \u53EA\u52A0\u8F7D\u8BED\u7D20\u6570\u636E\u6587\u4EF6\uFF0C\u8DF3\u8FC7\u5176\u4ED6\u6587\u4EF6\n        if (fileStat.isFile() &&\n            SUPPORTED_FORMATS.includes(extname(file) as any) &&\n            (file.includes('morpheme') || file.includes('\u8BED\u7D20'))) {\n          dataFiles.push(filePath)\n        }\n      }\n\n      if (dataFiles.length === 0) {\n        throw new Error(`\u672A\u627E\u5230\u6570\u636E\u6587\u4EF6\uFF0C\u76EE\u5F55: ${this.config.dataDir}`)\n      }\n\n      console.log(`\uD83D\uDCC1 \u53D1\u73B0${dataFiles.length}\u4E2A\u6570\u636E\u6587\u4EF6`)\n      return dataFiles\n\n    } catch (error) {\n      throw new Error(`\u626B\u63CF\u6570\u636E\u6587\u4EF6\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u52A0\u8F7D\u5355\u4E2A\u6570\u636E\u6587\u4EF6\n   * \n   * @private\n   * @param filePath \u6587\u4EF6\u8DEF\u5F84\n   * @returns \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   */\n  private async _loadDataFile(filePath: string): Promise<Morpheme[]> {\n    try {\n      // \u68C0\u67E5\u7F13\u5B58\n      if (this.config.enableCache) {\n        const cached = this.cache.get(filePath)\n        if (cached && Date.now() - cached.timestamp < this.config.cacheTTL * 1000) {\n          console.log(`\uD83D\uDCCB \u4F7F\u7528\u7F13\u5B58\u6570\u636E: ${filePath}`)\n          return cached.data\n        }\n      }\n\n      // \u8BFB\u53D6\u6587\u4EF6\u5185\u5BB9\n      const content = await readFile(filePath, 'utf-8')\n      let morphemes: Morpheme[]\n\n      // \u6839\u636E\u6587\u4EF6\u683C\u5F0F\u89E3\u6790\n      if (extname(filePath) === '.json') {\n        const parsed = JSON.parse(content)\n\n        // \u652F\u6301\u4E24\u79CD\u683C\u5F0F\uFF1A\u76F4\u63A5\u6570\u7EC4\u6216\u5305\u542Bmorphemes\u5B57\u6BB5\u7684\u5BF9\u8C61\n        if (Array.isArray(parsed)) {\n          morphemes = parsed\n        } else if (parsed.morphemes && Array.isArray(parsed.morphemes)) {\n          morphemes = parsed.morphemes\n          console.log(`\uD83D\uDCCB \u68C0\u6D4B\u5230\u5D4C\u5957\u683C\u5F0F\uFF0C\u63D0\u53D6${morphemes.length}\u4E2A\u8BED\u7D20`)\n        } else {\n          throw new Error(`\u65E0\u6548\u7684JSON\u683C\u5F0F: \u671F\u671B\u6570\u7EC4\u6216\u5305\u542Bmorphemes\u5B57\u6BB5\u7684\u5BF9\u8C61`)\n        }\n      } else if (extname(filePath) === '.jsonl') {\n        morphemes = content\n          .split('\\n')\n          .filter(line => line.trim())\n          .map(line => JSON.parse(line))\n      } else {\n        throw new Error(`\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u683C\u5F0F: ${extname(filePath)}`)\n      }\n\n      // \u66F4\u65B0\u7F13\u5B58\n      if (this.config.enableCache) {\n        this.cache.set(filePath, {\n          data: morphemes,\n          timestamp: Date.now()\n        })\n      }\n\n      console.log(`\uD83D\uDCC4 \u52A0\u8F7D\u6587\u4EF6: ${filePath} (${morphemes.length}\u4E2A\u8BED\u7D20)`)\n      return morphemes\n\n    } catch (error) {\n      throw new Error(`\u52A0\u8F7D\u6587\u4EF6\u5931\u8D25 ${filePath}: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u9A8C\u8BC1\u6570\u636E\u5B8C\u6574\u6027\u548C\u6B63\u786E\u6027\n   * \n   * @private\n   * @param morphemes \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * @returns \u9A8C\u8BC1\u7ED3\u679C\n   */\n  private async _validateData(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: string[] = []\n    const warnings: string[] = []\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      const prefix = `\u8BED\u7D20[${i}](${morpheme.id || 'unknown'})`\n\n      // \u9A8C\u8BC1\u5FC5\u9700\u5B57\u6BB5\n      for (const field of VALIDATION_CONFIG.required_fields) {\n        if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined) {\n          errors.push(`${prefix}: \u7F3A\u5C11\u5FC5\u9700\u5B57\u6BB5 '${field}'`)\n        }\n      }\n\n      // \u9A8C\u8BC1\u6570\u636E\u7C7B\u578B\u548C\u8303\u56F4\n      if (typeof morpheme.usage_frequency !== 'number' || \n          morpheme.usage_frequency < 0 || morpheme.usage_frequency > 1) {\n        errors.push(`${prefix}: usage_frequency \u5FC5\u987B\u662F [0-1] \u8303\u56F4\u5185\u7684\u6570\u5B57`)\n      }\n\n      if (typeof morpheme.quality_score !== 'number' || \n          morpheme.quality_score < VALIDATION_CONFIG.min_quality_score || \n          morpheme.quality_score > VALIDATION_CONFIG.max_quality_score) {\n        errors.push(`${prefix}: quality_score \u5FC5\u987B\u662F [${VALIDATION_CONFIG.min_quality_score}-${VALIDATION_CONFIG.max_quality_score}] \u8303\u56F4\u5185\u7684\u6570\u5B57`)\n      }\n\n      // \u9A8C\u8BC1\u8BED\u4E49\u5411\u91CF\n      if (!Array.isArray(morpheme.semantic_vector) || \n          morpheme.semantic_vector.length !== VALIDATION_CONFIG.semantic_vector_dimension) {\n        errors.push(`${prefix}: semantic_vector \u5FC5\u987B\u662F\u957F\u5EA6\u4E3A ${VALIDATION_CONFIG.semantic_vector_dimension} \u7684\u6570\u5B57\u6570\u7EC4`)\n      }\n\n      // \u9A8C\u8BC1\u7248\u672C\u517C\u5BB9\u6027\n      if (morpheme.version && !VERSION_COMPATIBILITY.supported.includes(morpheme.version)) {\n        warnings.push(`${prefix}: \u7248\u672C '${morpheme.version}' \u53EF\u80FD\u4E0D\u517C\u5BB9\uFF0C\u5F53\u524D\u652F\u6301\u7248\u672C: ${VERSION_COMPATIBILITY.supported.join(', ')}`)\n      }\n    }\n\n    const validation_time = Date.now() - startTime\n    const passed = errors.length === 0\n\n    if (!passed) {\n      console.warn(`\u26A0\uFE0F \u6570\u636E\u9A8C\u8BC1\u53D1\u73B0 ${errors.length} \u4E2A\u9519\u8BEF, ${warnings.length} \u4E2A\u8B66\u544A`)\n    } else {\n      console.log(`\u2705 \u6570\u636E\u9A8C\u8BC1\u901A\u8FC7: ${morphemes.length}\u4E2A\u8BED\u7D20, \u8017\u65F6${validation_time}ms`)\n    }\n\n    return {\n      passed,\n      errors,\n      warnings,\n      validated_count: morphemes.length,\n      validation_time\n    }\n  }\n\n  /**\n   * \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F\n   * \n   * @private\n   * @param morphemes \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * @param loadTime \u52A0\u8F7D\u8017\u65F6\n   * @returns \u7EDF\u8BA1\u4FE1\u606F\n   */\n  private _calculateStats(morphemes: Morpheme[], loadTime: number) {\n    const by_category: Record<string, number> = {}\n    const by_context: Record<string, number> = {}\n    let total_quality = 0\n\n    for (const morpheme of morphemes) {\n      // \u6309\u7C7B\u522B\u7EDF\u8BA1\n      by_category[morpheme.category] = (by_category[morpheme.category] || 0) + 1\n      \n      // \u6309\u6587\u5316\u8BED\u5883\u7EDF\u8BA1\n      by_context[morpheme.cultural_context] = (by_context[morpheme.cultural_context] || 0) + 1\n      \n      // \u7D2F\u8BA1\u8D28\u91CF\u8BC4\u5206\n      total_quality += morpheme.quality_score\n    }\n\n    return {\n      total_count: morphemes.length,\n      by_category,\n      by_context,\n      avg_quality: morphemes.length > 0 ? total_quality / morphemes.length : 0,\n      load_time: loadTime\n    }\n  }\n\n  /**\n   * \u8BBE\u7F6E\u70ED\u91CD\u8F7D\u76D1\u542C\n   * \n   * @private\n   * @param dataFiles \u6570\u636E\u6587\u4EF6\u8DEF\u5F84\u5217\u8868\n   */\n  private async _setupHotReload(dataFiles: string[]): Promise<void> {\n    // \u6E05\u7406\u73B0\u6709\u76D1\u542C\u5668\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // \u8BBE\u7F6E\u65B0\u7684\u76D1\u542C\u5668\n    for (const filePath of dataFiles) {\n      try {\n        const watcher = watch(filePath, (eventType) => {\n          if (eventType === 'change') {\n            console.log(`\uD83D\uDD04 \u68C0\u6D4B\u5230\u6587\u4EF6\u53D8\u5316: ${filePath}`)\n            // \u6E05\u9664\u7F13\u5B58\n            this.cache.delete(filePath)\n            // \u89E6\u53D1\u91CD\u65B0\u52A0\u8F7D (\u53EF\u4EE5\u6DFB\u52A0\u9632\u6296\u903B\u8F91)\n            this._debounceReload()\n          }\n        })\n\n        this.watchers.set(filePath, watcher)\n      } catch (error) {\n        console.warn(`\u26A0\uFE0F \u65E0\u6CD5\u76D1\u542C\u6587\u4EF6\u53D8\u5316: ${filePath}`, error)\n      }\n    }\n\n    console.log(`\uD83D\uDC41\uFE0F \u70ED\u91CD\u8F7D\u5DF2\u542F\u7528\uFF0C\u76D1\u542C ${this.watchers.size} \u4E2A\u6587\u4EF6`)\n  }\n\n  /**\n   * \u9632\u6296\u91CD\u65B0\u52A0\u8F7D\n   * \n   * @private\n   */\n  private _debounceReload = (() => {\n    let timeout: NodeJS.Timeout | null = null\n    return () => {\n      if (timeout) {\n        clearTimeout(timeout)\n      }\n      timeout = setTimeout(() => {\n        console.log('\uD83D\uDD04 \u6267\u884C\u70ED\u91CD\u8F7D...')\n        this.loadAll().catch(error => {\n          console.error('\u274C \u70ED\u91CD\u8F7D\u5931\u8D25:', error)\n        })\n      }, 1000) // 1\u79D2\u9632\u6296\n    }\n  })()\n\n  /**\n   * \u9002\u914D\u8BED\u7D20\u6570\u636E\u683C\u5F0F\n   *\n   * \u4E3Av1.0\u6570\u636E\u6DFB\u52A0v2.0\u5B57\u6BB5\u7684\u9ED8\u8BA4\u503C\n   *\n   * @private\n   * @param rawMorpheme \u539F\u59CB\u8BED\u7D20\u6570\u636E\n   * @returns \u9002\u914D\u540E\u7684\u8BED\u7D20\u6570\u636E\n   */\n  private _adaptMorphemeData(rawMorpheme: any): Morpheme {\n    // \u5982\u679C\u5DF2\u7ECF\u662Fv2.0\u683C\u5F0F\uFF0C\u76F4\u63A5\u8FD4\u56DE\n    if (rawMorpheme.language_properties && rawMorpheme.quality_metrics) {\n      return rawMorpheme as Morpheme\n    }\n\n    // \u4E3Av1.0\u6570\u636E\u6DFB\u52A0\u9ED8\u8BA4\u7684v2.0\u5B57\u6BB5\n    const adaptedMorpheme: Morpheme = {\n      ...rawMorpheme,\n      language_properties: rawMorpheme.language_properties || {\n        syllable_count: Math.ceil((rawMorpheme.text?.length || 1) / 2),\n        character_count: rawMorpheme.text?.length || 1,\n        phonetic_features: [],\n        morphological_type: this._inferMorphologicalType(rawMorpheme.category),\n        pronunciation: undefined\n      },\n      quality_metrics: rawMorpheme.quality_metrics || {\n        naturalness: (rawMorpheme.quality_score || 0.5) * 0.9,\n        frequency: rawMorpheme.usage_frequency || 0.5,\n        acceptability: (rawMorpheme.quality_score || 0.5) * 0.95,\n        aesthetic_appeal: (rawMorpheme.quality_score || 0.5) * 0.85\n      },\n      version: rawMorpheme.version || '1.0.0'\n    }\n\n    return adaptedMorpheme\n  }\n\n  /**\n   * \u63A8\u65AD\u8BCD\u6CD5\u7C7B\u578B\n   *\n   * @private\n   * @param category \u8BED\u7D20\u7C7B\u522B\n   * @returns \u8BCD\u6CD5\u7C7B\u578B\n   */\n  private _inferMorphologicalType(category: string): string {\n    const typeMap: Record<string, string> = {\n      'emotions': '\u5F62\u5BB9\u8BCD',\n      'professions': '\u540D\u8BCD',\n      'characteristics': '\u5F62\u5BB9\u8BCD',\n      'objects': '\u540D\u8BCD',\n      'actions': '\u52A8\u8BCD',\n      'concepts': '\u540D\u8BCD'\n    }\n\n    return typeMap[category] || '\u672A\u77E5'\n  }\n\n  /**\n   * \u6E05\u7406\u8D44\u6E90\n   */\n  destroy(): void {\n    // \u5173\u95ED\u6587\u4EF6\u76D1\u542C\u5668\n    for (const [path, watcher] of this.watchers) {\n      watcher.close()\n    }\n    this.watchers.clear()\n\n    // \u6E05\u7A7A\u7F13\u5B58\n    this.cache.clear()\n\n    console.log('\uD83E\uDDF9 \u6570\u636E\u52A0\u8F7D\u5668\u8D44\u6E90\u5DF2\u6E05\u7406')\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f95e990f5d70642e2e90faea2421fbb929b69c4b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2i3pzto8wf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2i3pzto8wf();
/**
 * 数据加载器
 *
 * 负责从文件系统加载语素数据，支持多语种、版本管理和数据验证。
 * 实现了热重载、缓存优化和错误恢复机制。
 *
 * @fileoverview 数据加载和管理核心模块
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */
import { readFile, readdir, stat } from 'fs/promises';
import { join, extname } from 'path';
import { watch } from 'fs';
import { SUPPORTED_VERSIONS } from '../../types/common';
// ============================================================================
// 配置常量
// ============================================================================
/** 数据文件目录路径 */
const DATA_DIR =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[0]++, join(process.cwd(), 'data'));
/** 支持的数据文件格式 */
const SUPPORTED_FORMATS =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[1]++, ['.json', '.jsonl']);
/** 数据版本兼容性配置 */
const VERSION_COMPATIBILITY =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[2]++, {
  current: '2.0.0',
  supported: SUPPORTED_VERSIONS
});
/** 数据验证配置 */
const VALIDATION_CONFIG =
/* istanbul ignore next */
(cov_2i3pzto8wf().s[3]++, {
  /** 语义向量维度 */
  semantic_vector_dimension: 20,
  /** 最小质量评分 */
  min_quality_score: 0.0,
  /** 最大质量评分 */
  max_quality_score: 1.0,
  /** 必需字段 */
  required_fields: ['id', 'text', 'category', 'subcategory', 'cultural_context', 'usage_frequency', 'quality_score', 'semantic_vector', 'tags', 'language_properties', 'quality_metrics', 'created_at', 'source', 'version']
});
// ============================================================================
// 数据加载器类
// ============================================================================
/**
 * 数据加载器类
 *
 * 提供语素数据的加载、验证、缓存和热重载功能
 */
export class DataLoader {
  config;
  cache =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[4]++, new Map());
  watchers =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[5]++, new Map());
  loadPromise =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[6]++, null);
  /**
   * 构造函数
   *
   * @param config 数据加载配置
   */
  constructor(config =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[0]++;
    cov_2i3pzto8wf().s[7]++;
    this.config = {
      dataDir:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[1][0]++, config.dataDir) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[1][1]++, DATA_DIR),
      enableHotReload:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[2][0]++, config.enableHotReload) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[2][1]++, true),
      enableValidation:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[3][0]++, config.enableValidation) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[3][1]++, true),
      enableCache:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[4][0]++, config.enableCache) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[4][1]++, true),
      cacheTTL:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[5][0]++, config.cacheTTL) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[5][1]++, 3600),
      // 1小时
      maxRetries:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[6][0]++, config.maxRetries) ??
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[6][1]++, 3)
    };
  }
  /**
   * 加载所有语素数据
   *
   * @returns 数据加载结果
   */
  async loadAll() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[1]++;
    cov_2i3pzto8wf().s[8]++;
    // 防止并发加载
    if (this.loadPromise) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[7][0]++;
      cov_2i3pzto8wf().s[9]++;
      return this.loadPromise;
    } else
    /* istanbul ignore next */
    {
      cov_2i3pzto8wf().b[7][1]++;
    }
    cov_2i3pzto8wf().s[10]++;
    this.loadPromise = this._performLoad();
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[11]++;
    try {
      const result =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[12]++, await this.loadPromise);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[13]++;
      return result;
    } finally {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[14]++;
      this.loadPromise = null;
    }
  }
  /**
   * 执行数据加载
   *
   * @private
   * @returns 数据加载结果
   */
  async _performLoad() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[2]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[15]++, Date.now());
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[16]++;
    try {
      // 1. 扫描数据文件
      const dataFiles =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[17]++, await this._scanDataFiles());
      // 2. 加载数据文件
      const allMorphemes =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[18]++, []);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[19]++;
      for (const filePath of dataFiles) {
        const rawMorphemes =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[20]++, await this._loadDataFile(filePath));
        // 适配数据格式以支持v1.0兼容性
        const adaptedMorphemes =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[21]++, rawMorphemes.map(m => {
          /* istanbul ignore next */
          cov_2i3pzto8wf().f[3]++;
          cov_2i3pzto8wf().s[22]++;
          return this._adaptMorphemeData(m);
        }));
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[23]++;
        allMorphemes.push(...adaptedMorphemes);
      }
      // 3. 数据验证
      const validation =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[24]++, this.config.enableValidation ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[8][0]++, await this._validateData(allMorphemes)) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[8][1]++, {
        passed: true,
        errors: [],
        warnings: [],
        validated_count: allMorphemes.length,
        validation_time: 0
      }));
      // 4. 计算统计信息
      const stats =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[25]++, this._calculateStats(allMorphemes, Date.now() - startTime));
      // 5. 设置热重载
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[26]++;
      if (this.config.enableHotReload) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[9][0]++;
        cov_2i3pzto8wf().s[27]++;
        await this._setupHotReload(dataFiles);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[9][1]++;
      }
      const result =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[28]++, {
        morphemes: allMorphemes,
        stats,
        validation,
        timestamp: Date.now()
      });
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[29]++;
      console.log(`✅ 数据加载完成: ${allMorphemes.length}个语素, 耗时${stats.load_time}ms`);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[30]++;
      return result;
    } catch (error) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[31]++;
      console.error('❌ 数据加载失败:', error);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[32]++;
      throw new Error(`数据加载失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[10][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[10][1]++, String(error))}`);
    }
  }
  /**
   * 扫描数据文件
   *
   * @private
   * @returns 数据文件路径列表
   */
  async _scanDataFiles() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[4]++;
    cov_2i3pzto8wf().s[33]++;
    try {
      const files =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[34]++, await readdir(this.config.dataDir));
      const dataFiles =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[35]++, []);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[36]++;
      for (const file of files) {
        const filePath =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[37]++, join(this.config.dataDir, file));
        const fileStat =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[38]++, await stat(filePath));
        // 只加载语素数据文件，跳过其他文件
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[39]++;
        if (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][0]++, fileStat.isFile()) &&
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][1]++, SUPPORTED_FORMATS.includes(extname(file))) && (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][2]++, file.includes('morpheme')) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[12][3]++, file.includes('语素')))) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[11][0]++;
          cov_2i3pzto8wf().s[40]++;
          dataFiles.push(filePath);
        } else
        /* istanbul ignore next */
        {
          cov_2i3pzto8wf().b[11][1]++;
        }
      }
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[41]++;
      if (dataFiles.length === 0) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[13][0]++;
        cov_2i3pzto8wf().s[42]++;
        throw new Error(`未找到数据文件，目录: ${this.config.dataDir}`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[13][1]++;
      }
      cov_2i3pzto8wf().s[43]++;
      console.log(`📁 发现${dataFiles.length}个数据文件`);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[44]++;
      return dataFiles;
    } catch (error) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[45]++;
      throw new Error(`扫描数据文件失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[14][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[14][1]++, String(error))}`);
    }
  }
  /**
   * 加载单个数据文件
   *
   * @private
   * @param filePath 文件路径
   * @returns 语素数据数组
   */
  async _loadDataFile(filePath) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[5]++;
    cov_2i3pzto8wf().s[46]++;
    try {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[47]++;
      // 检查缓存
      if (this.config.enableCache) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[15][0]++;
        const cached =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[48]++, this.cache.get(filePath));
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[49]++;
        if (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[17][0]++, cached) &&
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[17][1]++, Date.now() - cached.timestamp < this.config.cacheTTL * 1000)) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[16][0]++;
          cov_2i3pzto8wf().s[50]++;
          console.log(`📋 使用缓存数据: ${filePath}`);
          /* istanbul ignore next */
          cov_2i3pzto8wf().s[51]++;
          return cached.data;
        } else
        /* istanbul ignore next */
        {
          cov_2i3pzto8wf().b[16][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[15][1]++;
      }
      // 读取文件内容
      const content =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[52]++, await readFile(filePath, 'utf-8'));
      let morphemes;
      // 根据文件格式解析
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[53]++;
      if (extname(filePath) === '.json') {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[18][0]++;
        const parsed =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[54]++, JSON.parse(content));
        // 支持两种格式：直接数组或包含morphemes字段的对象
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[55]++;
        if (Array.isArray(parsed)) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[19][0]++;
          cov_2i3pzto8wf().s[56]++;
          morphemes = parsed;
        } else {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[19][1]++;
          cov_2i3pzto8wf().s[57]++;
          if (
          /* istanbul ignore next */
          (cov_2i3pzto8wf().b[21][0]++, parsed.morphemes) &&
          /* istanbul ignore next */
          (cov_2i3pzto8wf().b[21][1]++, Array.isArray(parsed.morphemes))) {
            /* istanbul ignore next */
            cov_2i3pzto8wf().b[20][0]++;
            cov_2i3pzto8wf().s[58]++;
            morphemes = parsed.morphemes;
            /* istanbul ignore next */
            cov_2i3pzto8wf().s[59]++;
            console.log(`📋 检测到嵌套格式，提取${morphemes.length}个语素`);
          } else {
            /* istanbul ignore next */
            cov_2i3pzto8wf().b[20][1]++;
            cov_2i3pzto8wf().s[60]++;
            throw new Error(`无效的JSON格式: 期望数组或包含morphemes字段的对象`);
          }
        }
      } else {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[18][1]++;
        cov_2i3pzto8wf().s[61]++;
        if (extname(filePath) === '.jsonl') {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[22][0]++;
          cov_2i3pzto8wf().s[62]++;
          morphemes = content.split('\n').filter(line => {
            /* istanbul ignore next */
            cov_2i3pzto8wf().f[6]++;
            cov_2i3pzto8wf().s[63]++;
            return line.trim();
          }).map(line => {
            /* istanbul ignore next */
            cov_2i3pzto8wf().f[7]++;
            cov_2i3pzto8wf().s[64]++;
            return JSON.parse(line);
          });
        } else {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[22][1]++;
          cov_2i3pzto8wf().s[65]++;
          throw new Error(`不支持的文件格式: ${extname(filePath)}`);
        }
      }
      // 更新缓存
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[66]++;
      if (this.config.enableCache) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[23][0]++;
        cov_2i3pzto8wf().s[67]++;
        this.cache.set(filePath, {
          data: morphemes,
          timestamp: Date.now()
        });
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[23][1]++;
      }
      cov_2i3pzto8wf().s[68]++;
      console.log(`📄 加载文件: ${filePath} (${morphemes.length}个语素)`);
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[69]++;
      return morphemes;
    } catch (error) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[70]++;
      throw new Error(`加载文件失败 ${filePath}: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[24][0]++, error.message) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[24][1]++, String(error))}`);
    }
  }
  /**
   * 验证数据完整性和正确性
   *
   * @private
   * @param morphemes 语素数据数组
   * @returns 验证结果
   */
  async _validateData(morphemes) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[8]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[71]++, Date.now());
    const errors =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[72]++, []);
    const warnings =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[73]++, []);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[74]++;
    for (let i =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[75]++, 0); i < morphemes.length; i++) {
      const morpheme =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[76]++, morphemes[i]);
      const prefix =
      /* istanbul ignore next */
      (cov_2i3pzto8wf().s[77]++, `语素[${i}](${
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[25][0]++, morpheme.id) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[25][1]++, 'unknown')})`);
      // 验证必需字段
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[78]++;
      for (const field of VALIDATION_CONFIG.required_fields) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[79]++;
        if (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[27][0]++, !(field in morpheme)) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[27][1]++, morpheme[field] === undefined)) {
          /* istanbul ignore next */
          cov_2i3pzto8wf().b[26][0]++;
          cov_2i3pzto8wf().s[80]++;
          errors.push(`${prefix}: 缺少必需字段 '${field}'`);
        } else
        /* istanbul ignore next */
        {
          cov_2i3pzto8wf().b[26][1]++;
        }
      }
      // 验证数据类型和范围
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[81]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[29][0]++, typeof morpheme.usage_frequency !== 'number') ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[29][1]++, morpheme.usage_frequency < 0) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[29][2]++, morpheme.usage_frequency > 1)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[28][0]++;
        cov_2i3pzto8wf().s[82]++;
        errors.push(`${prefix}: usage_frequency 必须是 [0-1] 范围内的数字`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[28][1]++;
      }
      cov_2i3pzto8wf().s[83]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[31][0]++, typeof morpheme.quality_score !== 'number') ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[31][1]++, morpheme.quality_score < VALIDATION_CONFIG.min_quality_score) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[31][2]++, morpheme.quality_score > VALIDATION_CONFIG.max_quality_score)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[30][0]++;
        cov_2i3pzto8wf().s[84]++;
        errors.push(`${prefix}: quality_score 必须是 [${VALIDATION_CONFIG.min_quality_score}-${VALIDATION_CONFIG.max_quality_score}] 范围内的数字`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[30][1]++;
      }
      // 验证语义向量
      cov_2i3pzto8wf().s[85]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[33][0]++, !Array.isArray(morpheme.semantic_vector)) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[33][1]++, morpheme.semantic_vector.length !== VALIDATION_CONFIG.semantic_vector_dimension)) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[32][0]++;
        cov_2i3pzto8wf().s[86]++;
        errors.push(`${prefix}: semantic_vector 必须是长度为 ${VALIDATION_CONFIG.semantic_vector_dimension} 的数字数组`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[32][1]++;
      }
      // 验证版本兼容性
      cov_2i3pzto8wf().s[87]++;
      if (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[35][0]++, morpheme.version) &&
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[35][1]++, !VERSION_COMPATIBILITY.supported.includes(morpheme.version))) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[34][0]++;
        cov_2i3pzto8wf().s[88]++;
        warnings.push(`${prefix}: 版本 '${morpheme.version}' 可能不兼容，当前支持版本: ${VERSION_COMPATIBILITY.supported.join(', ')}`);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[34][1]++;
      }
    }
    const validation_time =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[89]++, Date.now() - startTime);
    const passed =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[90]++, errors.length === 0);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[91]++;
    if (!passed) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[36][0]++;
      cov_2i3pzto8wf().s[92]++;
      console.warn(`⚠️ 数据验证发现 ${errors.length} 个错误, ${warnings.length} 个警告`);
    } else {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[36][1]++;
      cov_2i3pzto8wf().s[93]++;
      console.log(`✅ 数据验证通过: ${morphemes.length}个语素, 耗时${validation_time}ms`);
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[94]++;
    return {
      passed,
      errors,
      warnings,
      validated_count: morphemes.length,
      validation_time
    };
  }
  /**
   * 计算统计信息
   *
   * @private
   * @param morphemes 语素数据数组
   * @param loadTime 加载耗时
   * @returns 统计信息
   */
  _calculateStats(morphemes, loadTime) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[9]++;
    const by_category =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[95]++, {});
    const by_context =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[96]++, {});
    let total_quality =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[97]++, 0);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[98]++;
    for (const morpheme of morphemes) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[99]++;
      // 按类别统计
      by_category[morpheme.category] = (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[37][0]++, by_category[morpheme.category]) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[37][1]++, 0)) + 1;
      // 按文化语境统计
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[100]++;
      by_context[morpheme.cultural_context] = (
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[38][0]++, by_context[morpheme.cultural_context]) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[38][1]++, 0)) + 1;
      // 累计质量评分
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[101]++;
      total_quality += morpheme.quality_score;
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[102]++;
    return {
      total_count: morphemes.length,
      by_category,
      by_context,
      avg_quality: morphemes.length > 0 ?
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[39][0]++, total_quality / morphemes.length) :
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[39][1]++, 0),
      load_time: loadTime
    };
  }
  /**
   * 设置热重载监听
   *
   * @private
   * @param dataFiles 数据文件路径列表
   */
  async _setupHotReload(dataFiles) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[10]++;
    cov_2i3pzto8wf().s[103]++;
    // 清理现有监听器
    for (const [path, watcher] of this.watchers) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[104]++;
      watcher.close();
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[105]++;
    this.watchers.clear();
    // 设置新的监听器
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[106]++;
    for (const filePath of dataFiles) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[107]++;
      try {
        const watcher =
        /* istanbul ignore next */
        (cov_2i3pzto8wf().s[108]++, watch(filePath, eventType => {
          /* istanbul ignore next */
          cov_2i3pzto8wf().f[11]++;
          cov_2i3pzto8wf().s[109]++;
          if (eventType === 'change') {
            /* istanbul ignore next */
            cov_2i3pzto8wf().b[40][0]++;
            cov_2i3pzto8wf().s[110]++;
            console.log(`🔄 检测到文件变化: ${filePath}`);
            // 清除缓存
            /* istanbul ignore next */
            cov_2i3pzto8wf().s[111]++;
            this.cache.delete(filePath);
            // 触发重新加载 (可以添加防抖逻辑)
            /* istanbul ignore next */
            cov_2i3pzto8wf().s[112]++;
            this._debounceReload();
          } else
          /* istanbul ignore next */
          {
            cov_2i3pzto8wf().b[40][1]++;
          }
        }));
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[113]++;
        this.watchers.set(filePath, watcher);
      } catch (error) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[114]++;
        console.warn(`⚠️ 无法监听文件变化: ${filePath}`, error);
      }
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[115]++;
    console.log(`👁️ 热重载已启用，监听 ${this.watchers.size} 个文件`);
  }
  /**
   * 防抖重新加载
   *
   * @private
   */
  _debounceReload =
  /* istanbul ignore next */
  (cov_2i3pzto8wf().s[116]++, (() => {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[12]++;
    let timeout =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[117]++, null);
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[118]++;
    return () => {
      /* istanbul ignore next */
      cov_2i3pzto8wf().f[13]++;
      cov_2i3pzto8wf().s[119]++;
      if (timeout) {
        /* istanbul ignore next */
        cov_2i3pzto8wf().b[41][0]++;
        cov_2i3pzto8wf().s[120]++;
        clearTimeout(timeout);
      } else
      /* istanbul ignore next */
      {
        cov_2i3pzto8wf().b[41][1]++;
      }
      cov_2i3pzto8wf().s[121]++;
      timeout = setTimeout(() => {
        /* istanbul ignore next */
        cov_2i3pzto8wf().f[14]++;
        cov_2i3pzto8wf().s[122]++;
        console.log('🔄 执行热重载...');
        /* istanbul ignore next */
        cov_2i3pzto8wf().s[123]++;
        this.loadAll().catch(error => {
          /* istanbul ignore next */
          cov_2i3pzto8wf().f[15]++;
          cov_2i3pzto8wf().s[124]++;
          console.error('❌ 热重载失败:', error);
        });
      }, 1000); // 1秒防抖
    };
  })());
  /**
   * 适配语素数据格式
   *
   * 为v1.0数据添加v2.0字段的默认值
   *
   * @private
   * @param rawMorpheme 原始语素数据
   * @returns 适配后的语素数据
   */
  _adaptMorphemeData(rawMorpheme) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[16]++;
    cov_2i3pzto8wf().s[125]++;
    // 如果已经是v2.0格式，直接返回
    if (
    /* istanbul ignore next */
    (cov_2i3pzto8wf().b[43][0]++, rawMorpheme.language_properties) &&
    /* istanbul ignore next */
    (cov_2i3pzto8wf().b[43][1]++, rawMorpheme.quality_metrics)) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().b[42][0]++;
      cov_2i3pzto8wf().s[126]++;
      return rawMorpheme;
    } else
    /* istanbul ignore next */
    {
      cov_2i3pzto8wf().b[42][1]++;
    }
    // 为v1.0数据添加默认的v2.0字段
    const adaptedMorpheme =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[127]++, {
      ...rawMorpheme,
      language_properties:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[44][0]++, rawMorpheme.language_properties) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[44][1]++, {
        syllable_count: Math.ceil((
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[45][0]++, rawMorpheme.text?.length) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[45][1]++, 1)) / 2),
        character_count:
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[46][0]++, rawMorpheme.text?.length) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[46][1]++, 1),
        phonetic_features: [],
        morphological_type: this._inferMorphologicalType(rawMorpheme.category),
        pronunciation: undefined
      }),
      quality_metrics:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[47][0]++, rawMorpheme.quality_metrics) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[47][1]++, {
        naturalness: (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[48][0]++, rawMorpheme.quality_score) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[48][1]++, 0.5)) * 0.9,
        frequency:
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[49][0]++, rawMorpheme.usage_frequency) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[49][1]++, 0.5),
        acceptability: (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[50][0]++, rawMorpheme.quality_score) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[50][1]++, 0.5)) * 0.95,
        aesthetic_appeal: (
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[51][0]++, rawMorpheme.quality_score) ||
        /* istanbul ignore next */
        (cov_2i3pzto8wf().b[51][1]++, 0.5)) * 0.85
      }),
      version:
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[52][0]++, rawMorpheme.version) ||
      /* istanbul ignore next */
      (cov_2i3pzto8wf().b[52][1]++, '1.0.0')
    });
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[128]++;
    return adaptedMorpheme;
  }
  /**
   * 推断词法类型
   *
   * @private
   * @param category 语素类别
   * @returns 词法类型
   */
  _inferMorphologicalType(category) {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[17]++;
    const typeMap =
    /* istanbul ignore next */
    (cov_2i3pzto8wf().s[129]++, {
      'emotions': '形容词',
      'professions': '名词',
      'characteristics': '形容词',
      'objects': '名词',
      'actions': '动词',
      'concepts': '名词'
    });
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[130]++;
    return /* istanbul ignore next */(cov_2i3pzto8wf().b[53][0]++, typeMap[category]) ||
    /* istanbul ignore next */
    (cov_2i3pzto8wf().b[53][1]++, '未知');
  }
  /**
   * 清理资源
   */
  destroy() {
    /* istanbul ignore next */
    cov_2i3pzto8wf().f[18]++;
    cov_2i3pzto8wf().s[131]++;
    // 关闭文件监听器
    for (const [path, watcher] of this.watchers) {
      /* istanbul ignore next */
      cov_2i3pzto8wf().s[132]++;
      watcher.close();
    }
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[133]++;
    this.watchers.clear();
    // 清空缓存
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[134]++;
    this.cache.clear();
    /* istanbul ignore next */
    cov_2i3pzto8wf().s[135]++;
    console.log('🧹 数据加载器资源已清理');
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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