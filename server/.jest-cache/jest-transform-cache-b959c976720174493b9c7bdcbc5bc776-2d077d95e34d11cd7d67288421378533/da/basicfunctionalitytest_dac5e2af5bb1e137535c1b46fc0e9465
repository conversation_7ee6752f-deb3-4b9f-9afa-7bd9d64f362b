96c841c30358e6aeb1698ffb6abd1b60
"use strict";
/**
 * 基础功能测试
 *
 * 测试核心类型定义和基本功能的正确性
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const core_js_1 = require("../types/core.js");
const multilingual_js_1 = require("../types/multilingual.js");
const test_constants_js_1 = require("../config/test-constants.js");
(0, globals_1.describe)('基础功能测试', () => {
    (0, globals_1.describe)('类型定义测试', () => {
        (0, globals_1.it)('应该正确定义 CulturalContext 枚举', () => {
            (0, globals_1.expect)(core_js_1.CulturalContext.ANCIENT).toBe('ancient');
            (0, globals_1.expect)(core_js_1.CulturalContext.MODERN).toBe('modern');
            (0, globals_1.expect)(core_js_1.CulturalContext.NEUTRAL).toBe('neutral');
        });
        (0, globals_1.it)('应该正确定义 LanguageCode 枚举', () => {
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.ZH_CN).toBe('zh-CN');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.EN_US).toBe('en-US');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.JA_JP).toBe('ja-JP');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.KO_KR).toBe('ko-KR');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.ES_ES).toBe('es-ES');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.FR_FR).toBe('fr-FR');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.DE_DE).toBe('de-DE');
            (0, globals_1.expect)(multilingual_js_1.LanguageCode.AR_SA).toBe('ar-SA');
        });
        (0, globals_1.it)('应该正确定义 RegisterLevel 枚举', () => {
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.FORMAL).toBe('formal');
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.NEUTRAL).toBe('neutral');
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.INFORMAL).toBe('informal');
            (0, globals_1.expect)(multilingual_js_1.RegisterLevel.COLLOQUIAL).toBe('colloquial');
        });
        (0, globals_1.it)('应该正确定义语义向量维度常量', () => {
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBe(test_constants_js_1.TEST_VECTOR_DIMENSIONS.LEGACY_EXPECTED);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBe(test_constants_js_1.TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(test_constants_js_1.TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED);
        });
    });
    (0, globals_1.describe)('类型兼容性测试', () => {
        (0, globals_1.it)('应该支持枚举值的类型检查', () => {
            const culturalContexts = Object.values(core_js_1.CulturalContext);
            (0, globals_1.expect)(culturalContexts).toHaveLength(test_constants_js_1.TEST_ENUM_COUNTS.CULTURAL_CONTEXT);
            (0, globals_1.expect)(culturalContexts).toContain('ancient');
            (0, globals_1.expect)(culturalContexts).toContain('modern');
            (0, globals_1.expect)(culturalContexts).toContain('neutral');
        });
        (0, globals_1.it)('应该支持语言代码的类型检查', () => {
            const languageCodes = Object.values(multilingual_js_1.LanguageCode);
            (0, globals_1.expect)(languageCodes).toHaveLength(test_constants_js_1.TEST_ENUM_COUNTS.LANGUAGE_CODE);
            (0, globals_1.expect)(languageCodes).toContain('zh-CN');
            (0, globals_1.expect)(languageCodes).toContain('en-US');
        });
        (0, globals_1.it)('应该支持寄存器级别的类型检查', () => {
            const registerLevels = Object.values(multilingual_js_1.RegisterLevel);
            (0, globals_1.expect)(registerLevels).toHaveLength(test_constants_js_1.TEST_ENUM_COUNTS.REGISTER_LEVEL);
            (0, globals_1.expect)(registerLevels).toContain('formal');
            (0, globals_1.expect)(registerLevels).toContain('neutral');
            (0, globals_1.expect)(registerLevels).toContain('informal');
            (0, globals_1.expect)(registerLevels).toContain('colloquial');
        });
    });
    (0, globals_1.describe)('配置常量测试', () => {
        (0, globals_1.it)('应该正确配置语义向量维度', () => {
            // 测试向量维度配置的一致性
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeLessThan(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL);
            // 测试维度值的合理性
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeGreaterThan(0);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBeGreaterThan(0);
            (0, globals_1.expect)(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBeGreaterThan(0);
        });
        (0, globals_1.it)('应该支持向量维度的数学运算', () => {
            const legacyVector = new Array(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY).fill(test_constants_js_1.TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE);
            const multilingualVector = new Array(multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).fill(test_constants_js_1.TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE);
            (0, globals_1.expect)(legacyVector).toHaveLength(test_constants_js_1.TEST_VECTOR_DIMENSIONS.LEGACY_EXPECTED);
            (0, globals_1.expect)(multilingualVector).toHaveLength(test_constants_js_1.TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED);
            // 测试向量的基本数学属性
            (0, globals_1.expect)(legacyVector.every(v => v === test_constants_js_1.TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)).toBe(true);
            (0, globals_1.expect)(multilingualVector.every(v => v === test_constants_js_1.TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)).toBe(true);
        });
    });
    (0, globals_1.describe)('数据结构验证', () => {
        (0, globals_1.it)('应该支持多语言标签映射', () => {
            const multilingualLabels = {
                [multilingual_js_1.LanguageCode.ZH_CN]: test_constants_js_1.TEST_MULTILINGUAL_LABELS.ZH_CN,
                [multilingual_js_1.LanguageCode.EN_US]: test_constants_js_1.TEST_MULTILINGUAL_LABELS.EN_US,
                [multilingual_js_1.LanguageCode.JA_JP]: test_constants_js_1.TEST_MULTILINGUAL_LABELS.JA_JP
            };
            (0, globals_1.expect)(multilingualLabels[multilingual_js_1.LanguageCode.ZH_CN]).toBe(test_constants_js_1.TEST_MULTILINGUAL_LABELS.ZH_CN);
            (0, globals_1.expect)(multilingualLabels[multilingual_js_1.LanguageCode.EN_US]).toBe(test_constants_js_1.TEST_MULTILINGUAL_LABELS.EN_US);
            (0, globals_1.expect)(multilingualLabels[multilingual_js_1.LanguageCode.JA_JP]).toBe(test_constants_js_1.TEST_MULTILINGUAL_LABELS.JA_JP);
        });
        (0, globals_1.it)('应该支持文化适应性配置', () => {
            const culturalAdaptation = test_constants_js_1.TEST_CULTURAL_ADAPTATION;
            // 验证数值范围
            (0, globals_1.expect)(culturalAdaptation.traditionality).toBeGreaterThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.CULTURAL_ADAPTATION.min);
            (0, globals_1.expect)(culturalAdaptation.traditionality).toBeLessThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.CULTURAL_ADAPTATION.max);
            (0, globals_1.expect)(culturalAdaptation.modernity).toBeGreaterThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.CULTURAL_ADAPTATION.min);
            (0, globals_1.expect)(culturalAdaptation.modernity).toBeLessThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.CULTURAL_ADAPTATION.max);
            // 验证数组属性
            (0, globals_1.expect)(Array.isArray(culturalAdaptation.age_appropriateness)).toBe(true);
            (0, globals_1.expect)(Array.isArray(culturalAdaptation.cultural_tags)).toBe(true);
        });
        (0, globals_1.it)('应该支持语音特征配置', () => {
            const phoneticFeatures = test_constants_js_1.TEST_PHONETIC_FEATURES;
            (0, globals_1.expect)(phoneticFeatures.ipa_transcription).toMatch(test_constants_js_1.TEST_IPA_PATTERN);
            (0, globals_1.expect)(phoneticFeatures.syllable_count).toBeGreaterThan(0);
            (0, globals_1.expect)(Array.isArray(phoneticFeatures.tone_pattern)).toBe(true);
            (0, globals_1.expect)(phoneticFeatures.phonetic_harmony).toBeGreaterThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.PHONETIC_HARMONY.min);
            (0, globals_1.expect)(phoneticFeatures.phonetic_harmony).toBeLessThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.PHONETIC_HARMONY.max);
        });
    });
    (0, globals_1.describe)('质量评估系统测试', () => {
        (0, globals_1.it)('应该支持8维度质量评分', () => {
            const qualityDimensions = test_constants_js_1.TEST_QUALITY_DIMENSIONS;
            // 验证所有维度都在有效范围内
            Object.values(qualityDimensions).forEach(score => {
                (0, globals_1.expect)(score).toBeGreaterThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.QUALITY_SCORE.min);
                (0, globals_1.expect)(score).toBeLessThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.QUALITY_SCORE.max);
            });
            // 验证维度数量
            (0, globals_1.expect)(Object.keys(qualityDimensions)).toHaveLength(test_constants_js_1.TEST_ENUM_COUNTS.QUALITY_DIMENSIONS);
        });
        (0, globals_1.it)('应该计算平均质量评分', () => {
            const dimensions = test_constants_js_1.TEST_QUALITY_DIMENSIONS;
            const average = Object.values(dimensions).reduce((sum, score) => sum + score, 0) / test_constants_js_1.TEST_ENUM_COUNTS.QUALITY_DIMENSIONS;
            (0, globals_1.expect)(average).toBeCloseTo(test_constants_js_1.TEST_AVERAGE_QUALITY, test_constants_js_1.TEST_PRECISION.FLOAT_PRECISION);
            (0, globals_1.expect)(average).toBeGreaterThan(test_constants_js_1.TEST_QUALITY_THRESHOLDS.HIGH_QUALITY);
            (0, globals_1.expect)(average).toBeLessThan(test_constants_js_1.TEST_QUALITY_THRESHOLDS.PERFECT);
        });
    });
    (0, globals_1.describe)('错误处理测试', () => {
        (0, globals_1.it)('应该处理无效的枚举值', () => {
            // TypeScript 编译时会捕获这些错误，但我们可以测试运行时行为
            const validCulturalContext = core_js_1.CulturalContext.MODERN;
            (0, globals_1.expect)(Object.values(core_js_1.CulturalContext)).toContain(validCulturalContext);
        });
        (0, globals_1.it)('应该处理边界值', () => {
            // 测试质量评分的边界值
            const minScore = test_constants_js_1.TEST_BOUNDARY_VALUES.MIN_SCORE;
            const maxScore = test_constants_js_1.TEST_BOUNDARY_VALUES.MAX_SCORE;
            (0, globals_1.expect)(minScore).toBeGreaterThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.QUALITY_SCORE.min);
            (0, globals_1.expect)(minScore).toBeLessThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.QUALITY_SCORE.max);
            (0, globals_1.expect)(maxScore).toBeGreaterThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.QUALITY_SCORE.min);
            (0, globals_1.expect)(maxScore).toBeLessThanOrEqual(test_constants_js_1.TEST_VALUE_RANGES.QUALITY_SCORE.max);
        });
        (0, globals_1.it)('应该处理空数组和空对象', () => {
            const emptyArray = [];
            const emptyObject = {};
            (0, globals_1.expect)(Array.isArray(emptyArray)).toBe(true);
            (0, globals_1.expect)(emptyArray).toHaveLength(test_constants_js_1.TEST_BOUNDARY_VALUES.EMPTY_ARRAY_LENGTH);
            (0, globals_1.expect)(typeof emptyObject).toBe('object');
            (0, globals_1.expect)(Object.keys(emptyObject)).toHaveLength(test_constants_js_1.TEST_BOUNDARY_VALUES.EMPTY_OBJECT_KEYS);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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