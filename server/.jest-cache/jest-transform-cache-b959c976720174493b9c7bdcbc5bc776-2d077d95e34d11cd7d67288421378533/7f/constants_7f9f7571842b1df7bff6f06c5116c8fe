2aae5468e53eb3f702cf835f1b28fea8
/* istanbul ignore next */
function cov_gpvvqkfzd() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts";
  var hash = "18115ab5a648ae9e95501b455cb7ee8a23cf6b41";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 33
        },
        end: {
          line: 12,
          column: 111
        }
      },
      "1": {
        start: {
          line: 14,
          column: 19
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "2": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 42,
          column: 1
        }
      },
      "3": {
        start: {
          line: 44,
          column: 26
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "4": {
        start: {
          line: 76,
          column: 21
        },
        end: {
          line: 83,
          column: 1
        }
      },
      "5": {
        start: {
          line: 85,
          column: 27
        },
        end: {
          line: 90,
          column: 1
        }
      },
      "6": {
        start: {
          line: 92,
          column: 30
        },
        end: {
          line: 106,
          column: 1
        }
      },
      "7": {
        start: {
          line: 94,
          column: 37
        },
        end: {
          line: 94,
          column: 87
        }
      },
      "8": {
        start: {
          line: 95,
          column: 48
        },
        end: {
          line: 95,
          column: 109
        }
      },
      "9": {
        start: {
          line: 96,
          column: 49
        },
        end: {
          line: 96,
          column: 111
        }
      },
      "10": {
        start: {
          line: 97,
          column: 54
        },
        end: {
          line: 97,
          column: 117
        }
      },
      "11": {
        start: {
          line: 98,
          column: 51
        },
        end: {
          line: 98,
          column: 111
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 94,
            column: 23
          },
          end: {
            line: 94,
            column: 24
          }
        },
        loc: {
          start: {
            line: 94,
            column: 37
          },
          end: {
            line: 94,
            column: 87
          }
        },
        line: 94
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 95,
            column: 34
          },
          end: {
            line: 95,
            column: 35
          }
        },
        loc: {
          start: {
            line: 95,
            column: 48
          },
          end: {
            line: 95,
            column: 109
          }
        },
        line: 95
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 96,
            column: 35
          },
          end: {
            line: 96,
            column: 36
          }
        },
        loc: {
          start: {
            line: 96,
            column: 49
          },
          end: {
            line: 96,
            column: 111
          }
        },
        line: 96
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 97,
            column: 37
          },
          end: {
            line: 97,
            column: 38
          }
        },
        loc: {
          start: {
            line: 97,
            column: 54
          },
          end: {
            line: 97,
            column: 117
          }
        },
        line: 97
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 98,
            column: 34
          },
          end: {
            line: 98,
            column: 35
          }
        },
        loc: {
          start: {
            line: 98,
            column: 51
          },
          end: {
            line: 98,
            column: 111
          }
        },
        line: 98
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {},
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts",
      mappings: "AAAA;;;;;;;;;GASG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAA;AAE/C,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAU,CAAA;AAExH,WAAW;AACX,MAAM,CAAC,MAAM,GAAG,GAAG;IACjB,SAAS;IACT,OAAO,EAAE,WAAW;IACpB,SAAS;IACT,iBAAiB,EAAE,MAAM;IACzB,OAAO;IACP,gBAAgB,EAAE,OAAO;CACjB,CAAA;AAEV,SAAS;AACT,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,aAAa;IACb,kBAAkB,EAAE,CAAC;IACrB,SAAS;IACT,sBAAsB,EAAE,CAAC;IACzB,SAAS;IACT,wBAAwB,EAAE,GAAG;IAC7B,SAAS;IACT,yBAAyB,EAAE,GAAG;IAC9B,SAAS;IACT,yBAAyB,EAAE,QAAiB;IAC5C,SAAS;IACT,2BAA2B,EAAE,eAAe,CAAC,OAAO;IACpD,SAAS;IACT,wBAAwB,EAAE,UAAmB;IAC7C,WAAW;IACX,kBAAkB,EAAE,CAAC;IACrB,aAAa;IACb,eAAe,EAAE,KAAK;CACd,CAAA;AAEV,SAAS;AACT,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,SAAS;IACT,KAAK,EAAE;QACL,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,EAAE;KACR;IACD,SAAS;IACT,gBAAgB,EAAE;QAChB,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;KACT;IACD,SAAS;IACT,iBAAiB,EAAE;QACjB,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;KACT;IACD,SAAS;IACT,WAAW,EAAE;QACX,kBAAkB,EAAE,OAAO;QAC3B,sBAAsB,EAAE,OAAO;QAC/B,iBAAiB,EAAE,OAAO;QAC1B,iBAAiB,EAAE,OAAO;QAC1B,cAAc,EAAE,OAAO;KACxB;IACD,SAAS;IACT,oBAAoB,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAU;IACxE,SAAS;IACT,iBAAiB,EAAE,iBAAiB;IACpC,SAAS;IACT,kBAAkB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAU;CAChD,CAAA;AAEV,SAAS;AACT,MAAM,CAAC,MAAM,KAAK,GAAG;IACnB,YAAY;IACZ,WAAW,EAAE,IAAI;IACjB,UAAU;IACV,SAAS,EAAE,IAAI;IACf,QAAQ;IACR,UAAU,EAAE,QAAQ;CACZ,CAAA;AAEV,SAAS;AACT,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,YAAY;IACZ,oBAAoB,EAAE,GAAG;IACzB,aAAa;IACb,sBAAsB,EAAE,GAAG;CACnB,CAAA;AAEV,OAAO;AACP,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,UAAU,EAAE;QACV,aAAa,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,kCAAkC,GAAG,QAAQ,GAAG,EAAE;QAC/F,wBAAwB,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,6CAA6C,GAAG,QAAQ,GAAG,EAAE;QACrH,yBAAyB,EAAE,CAAC,GAAW,EAAE,GAAW,EAAE,EAAE,CAAC,8CAA8C,GAAG,QAAQ,GAAG,EAAE;QACvH,2BAA2B,EAAE,CAAC,WAA8B,EAAE,EAAE,CAAC,uCAAuC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAChI,wBAAwB,EAAE,CAAC,WAA8B,EAAE,EAAE,CAAC,oCAAoC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC1H,sBAAsB,EAAE,2BAA2B;KACpD;IACD,QAAQ,EAAE;QACR,iBAAiB,EAAE,8BAA8B;QACjD,iBAAiB,EAAE,2BAA2B;QAC9C,cAAc,EAAE,uBAAuB;KACxC;CACO,CAAA",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts"],
      sourcesContent: ["/**\n * \u5E94\u7528\u5E38\u91CF\u914D\u7F6E\n *\n * \u5305\u542BAPI\u914D\u7F6E\u3001\u751F\u6210\u53C2\u6570\u3001\u9A8C\u8BC1\u89C4\u5219\u3001\u7F13\u5B58\u8BBE\u7F6E\u7B49\u5168\u5C40\u5E38\u91CF\n *\n * @fileoverview \u5E94\u7528\u5E38\u91CF\u914D\u7F6E\u6587\u4EF6\n * @version 1.0.0\n * @since 2025-06-24\n * <AUTHOR> team\n */\n\nimport { CulturalContext } from \"../types/core\"\n\nexport const STYLE_PREFERENCES = ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional'] as const\n\n// API \u76F8\u5173\u5E38\u91CF\nexport const API = {\n  // API \u7248\u672C\n  VERSION: '1.0.0-mvp',\n  // \u8BF7\u6C42ID\u524D\u7F00\n  REQUEST_ID_PREFIX: 'req_',\n  // \u9ED8\u8BA4\u8BED\u8A00\n  DEFAULT_LANGUAGE: 'zh-CN'\n} as const\n\n// \u751F\u6210\u76F8\u5173\u5E38\u91CF\nexport const GENERATION = {\n  // \u6BCF\u6B21\u8BF7\u6C42\u6700\u5927\u751F\u6210\u6570\u91CF\n  MAX_GENERATE_COUNT: 3,\n  // \u9ED8\u8BA4\u751F\u6210\u6570\u91CF\n  DEFAULT_GENERATE_COUNT: 1,\n  // \u9ED8\u8BA4\u521B\u610F\u7A0B\u5EA6\n  DEFAULT_CREATIVITY_LEVEL: 0.7,\n  // \u9ED8\u8BA4\u8D28\u91CF\u9608\u503C\n  DEFAULT_QUALITY_THRESHOLD: 0.5,\n  // \u9ED8\u8BA4\u957F\u5EA6\u504F\u597D\n  DEFAULT_LENGTH_PREFERENCE: 'medium' as const,\n  // \u9ED8\u8BA4\u6587\u5316\u504F\u597D\n  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,\n  // \u9ED8\u8BA4\u98CE\u683C\u504F\u597D\n  DEFAULT_STYLE_PREFERENCE: 'humorous' as const,\n  // \u9ED8\u8BA4\u6700\u5927\u91CD\u8BD5\u6B21\u6570\n  MAX_RETRY_ATTEMPTS: 3,\n  // \u8BF7\u6C42\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09\n  REQUEST_TIMEOUT: 10000\n} as const\n\n// \u9A8C\u8BC1\u76F8\u5173\u5E38\u91CF\nexport const VALIDATION = {\n  // \u751F\u6210\u6570\u91CF\u8303\u56F4\n  COUNT: {\n    MIN: 1,\n    MAX: 20\n  },\n  // \u521B\u610F\u7A0B\u5EA6\u8303\u56F4\n  CREATIVITY_LEVEL: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // \u8D28\u91CF\u9608\u503C\u8303\u56F4\n  QUALITY_THRESHOLD: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // \u9ED8\u8BA4\u9519\u8BEF\u4EE3\u7801\n  ERROR_CODES: {\n    INVALID_PARAMETERS: 'E1001',\n    MISSING_REQUIRED_FIELD: 'E1002',\n    VALIDATION_FAILED: 'E1003',\n    GENERATION_FAILED: 'E3001',\n    INTERNAL_ERROR: 'E5001'\n  },\n  // \u6587\u5316\u504F\u597D\u9009\u9879\n  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'] as const,\n  // \u98CE\u683C\u504F\u597D\u9009\u9879\n  STYLE_PREFERENCES: STYLE_PREFERENCES,\n  // \u957F\u5EA6\u504F\u597D\u9009\u9879\n  LENGTH_PREFERENCES: ['short', 'medium', 'long'] as const\n} as const\n\n// \u7F13\u5B58\u76F8\u5173\u5E38\u91CF\nexport const CACHE = {\n  // \u9ED8\u8BA4\u7F13\u5B58\u65F6\u95F4\uFF08\u79D2\uFF09\n  DEFAULT_TTL: 3600,\n  // \u6700\u5927\u7F13\u5B58\u6761\u76EE\u6570\n  MAX_ITEMS: 1000,\n  // \u7F13\u5B58\u952E\u524D\u7F00\n  KEY_PREFIX: 'namer:'\n} as const\n\n// \u6027\u80FD\u76F8\u5173\u5E38\u91CF\nexport const PERFORMANCE = {\n  // \u6162\u67E5\u8BE2\u9608\u503C\uFF08\u6BEB\u79D2\uFF09\n  SLOW_QUERY_THRESHOLD: 500,\n  // \u76D1\u63A7\u91C7\u6837\u7387\uFF080-1\uFF09\n  MONITORING_SAMPLE_RATE: 0.1\n} as const\n\n// \u9519\u8BEF\u6D88\u606F\nexport const ERROR_MESSAGES = {\n  VALIDATION: {\n    INVALID_COUNT: (min: number, max: number) => `count must be a number between ${min} and ${max}`,\n    INVALID_CREATIVITY_LEVEL: (min: number, max: number) => `creativity_level must be a number between ${min} and ${max}`,\n    INVALID_QUALITY_THRESHOLD: (min: number, max: number) => `quality_threshold must be a number between ${min} and ${max}`,\n    INVALID_CULTURAL_PREFERENCE: (validValues: readonly string[]) => `cultural_preference must be one of: ${validValues.join(', ')}`,\n    INVALID_STYLE_PREFERENCE: (validValues: readonly string[]) => `style_preference must be one of: ${validValues.join(', ')}`,\n    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array'\n  },\n  INTERNAL: {\n    GENERATION_FAILED: 'Failed to generate usernames',\n    VALIDATION_FAILED: 'Request validation failed',\n    INTERNAL_ERROR: 'Internal server error'\n  }\n} as const\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "18115ab5a648ae9e95501b455cb7ee8a23cf6b41"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_gpvvqkfzd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gpvvqkfzd();
/**
 * 应用常量配置
 *
 * 包含API配置、生成参数、验证规则、缓存设置等全局常量
 *
 * @fileoverview 应用常量配置文件
 * @version 1.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */
import { CulturalContext } from "../types/core";
export const STYLE_PREFERENCES =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[0]++, ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional']);
// API 相关常量
export const API =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[1]++, {
  // API 版本
  VERSION: '1.0.0-mvp',
  // 请求ID前缀
  REQUEST_ID_PREFIX: 'req_',
  // 默认语言
  DEFAULT_LANGUAGE: 'zh-CN'
});
// 生成相关常量
export const GENERATION =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[2]++, {
  // 每次请求最大生成数量
  MAX_GENERATE_COUNT: 3,
  // 默认生成数量
  DEFAULT_GENERATE_COUNT: 1,
  // 默认创意程度
  DEFAULT_CREATIVITY_LEVEL: 0.7,
  // 默认质量阈值
  DEFAULT_QUALITY_THRESHOLD: 0.5,
  // 默认长度偏好
  DEFAULT_LENGTH_PREFERENCE: 'medium',
  // 默认文化偏好
  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,
  // 默认风格偏好
  DEFAULT_STYLE_PREFERENCE: 'humorous',
  // 默认最大重试次数
  MAX_RETRY_ATTEMPTS: 3,
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 10000
});
// 验证相关常量
export const VALIDATION =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[3]++, {
  // 生成数量范围
  COUNT: {
    MIN: 1,
    MAX: 20
  },
  // 创意程度范围
  CREATIVITY_LEVEL: {
    MIN: 0.1,
    MAX: 1.0
  },
  // 质量阈值范围
  QUALITY_THRESHOLD: {
    MIN: 0.1,
    MAX: 1.0
  },
  // 默认错误代码
  ERROR_CODES: {
    INVALID_PARAMETERS: 'E1001',
    MISSING_REQUIRED_FIELD: 'E1002',
    VALIDATION_FAILED: 'E1003',
    GENERATION_FAILED: 'E3001',
    INTERNAL_ERROR: 'E5001'
  },
  // 文化偏好选项
  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'],
  // 风格偏好选项
  STYLE_PREFERENCES: STYLE_PREFERENCES,
  // 长度偏好选项
  LENGTH_PREFERENCES: ['short', 'medium', 'long']
});
// 缓存相关常量
export const CACHE =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[4]++, {
  // 默认缓存时间（秒）
  DEFAULT_TTL: 3600,
  // 最大缓存条目数
  MAX_ITEMS: 1000,
  // 缓存键前缀
  KEY_PREFIX: 'namer:'
});
// 性能相关常量
export const PERFORMANCE =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[5]++, {
  // 慢查询阈值（毫秒）
  SLOW_QUERY_THRESHOLD: 500,
  // 监控采样率（0-1）
  MONITORING_SAMPLE_RATE: 0.1
});
// 错误消息
export const ERROR_MESSAGES =
/* istanbul ignore next */
(cov_gpvvqkfzd().s[6]++, {
  VALIDATION: {
    INVALID_COUNT: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[0]++;
      cov_gpvvqkfzd().s[7]++;
      return `count must be a number between ${min} and ${max}`;
    },
    INVALID_CREATIVITY_LEVEL: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[1]++;
      cov_gpvvqkfzd().s[8]++;
      return `creativity_level must be a number between ${min} and ${max}`;
    },
    INVALID_QUALITY_THRESHOLD: (min, max) => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[2]++;
      cov_gpvvqkfzd().s[9]++;
      return `quality_threshold must be a number between ${min} and ${max}`;
    },
    INVALID_CULTURAL_PREFERENCE: validValues => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[3]++;
      cov_gpvvqkfzd().s[10]++;
      return `cultural_preference must be one of: ${validValues.join(', ')}`;
    },
    INVALID_STYLE_PREFERENCE: validValues => {
      /* istanbul ignore next */
      cov_gpvvqkfzd().f[4]++;
      cov_gpvvqkfzd().s[11]++;
      return `style_preference must be one of: ${validValues.join(', ')}`;
    },
    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array'
  },
  INTERNAL: {
    GENERATION_FAILED: 'Failed to generate usernames',
    VALIDATION_FAILED: 'Request validation failed',
    INTERNAL_ERROR: 'Internal server error'
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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