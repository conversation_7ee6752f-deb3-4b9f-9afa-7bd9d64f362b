{"version": 3, "names": ["cov_gpvvqkfzd", "actualCoverage", "CulturalContext", "STYLE_PREFERENCES", "s", "API", "VERSION", "REQUEST_ID_PREFIX", "DEFAULT_LANGUAGE", "GENERATION", "MAX_GENERATE_COUNT", "DEFAULT_GENERATE_COUNT", "DEFAULT_CREATIVITY_LEVEL", "DEFAULT_QUALITY_THRESHOLD", "DEFAULT_LENGTH_PREFERENCE", "DEFAULT_CULTURAL_PREFERENCE", "NEUTRAL", "DEFAULT_STYLE_PREFERENCE", "MAX_RETRY_ATTEMPTS", "REQUEST_TIMEOUT", "VALIDATION", "COUNT", "MIN", "MAX", "CREATIVITY_LEVEL", "QUALITY_THRESHOLD", "ERROR_CODES", "INVALID_PARAMETERS", "MISSING_REQUIRED_FIELD", "VALIDATION_FAILED", "GENERATION_FAILED", "INTERNAL_ERROR", "CULTURAL_PREFERENCES", "LENGTH_PREFERENCES", "CACHE", "DEFAULT_TTL", "MAX_ITEMS", "KEY_PREFIX", "PERFORMANCE", "SLOW_QUERY_THRESHOLD", "MONITORING_SAMPLE_RATE", "ERROR_MESSAGES", "INVALID_COUNT", "min", "max", "f", "INVALID_CREATIVITY_LEVEL", "INVALID_QUALITY_THRESHOLD", "INVALID_CULTURAL_PREFERENCE", "validValues", "join", "INVALID_STYLE_PREFERENCE", "PATTERNS_MUST_BE_ARRAY", "INTERNAL"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/config/constants.ts"], "sourcesContent": ["/**\n * 应用常量配置\n *\n * 包含API配置、生成参数、验证规则、缓存设置等全局常量\n *\n * @fileoverview 应用常量配置文件\n * @version 1.0.0\n * @since 2025-06-24\n * <AUTHOR> team\n */\n\nimport { CulturalContext } from \"../types/core\"\n\nexport const STYLE_PREFERENCES = ['humorous', 'artistic', 'cute', 'cool', 'elegant', 'playful', 'professional'] as const\n\n// API 相关常量\nexport const API = {\n  // API 版本\n  VERSION: '1.0.0-mvp',\n  // 请求ID前缀\n  REQUEST_ID_PREFIX: 'req_',\n  // 默认语言\n  DEFAULT_LANGUAGE: 'zh-CN'\n} as const\n\n// 生成相关常量\nexport const GENERATION = {\n  // 每次请求最大生成数量\n  MAX_GENERATE_COUNT: 3,\n  // 默认生成数量\n  DEFAULT_GENERATE_COUNT: 1,\n  // 默认创意程度\n  DEFAULT_CREATIVITY_LEVEL: 0.7,\n  // 默认质量阈值\n  DEFAULT_QUALITY_THRESHOLD: 0.5,\n  // 默认长度偏好\n  DEFAULT_LENGTH_PREFERENCE: 'medium' as const,\n  // 默认文化偏好\n  DEFAULT_CULTURAL_PREFERENCE: CulturalContext.NEUTRAL,\n  // 默认风格偏好\n  DEFAULT_STYLE_PREFERENCE: 'humorous' as const,\n  // 默认最大重试次数\n  MAX_RETRY_ATTEMPTS: 3,\n  // 请求超时时间（毫秒）\n  REQUEST_TIMEOUT: 10000\n} as const\n\n// 验证相关常量\nexport const VALIDATION = {\n  // 生成数量范围\n  COUNT: {\n    MIN: 1,\n    MAX: 20\n  },\n  // 创意程度范围\n  CREATIVITY_LEVEL: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // 质量阈值范围\n  QUALITY_THRESHOLD: {\n    MIN: 0.1,\n    MAX: 1.0\n  },\n  // 默认错误代码\n  ERROR_CODES: {\n    INVALID_PARAMETERS: 'E1001',\n    MISSING_REQUIRED_FIELD: 'E1002',\n    VALIDATION_FAILED: 'E1003',\n    GENERATION_FAILED: 'E3001',\n    INTERNAL_ERROR: 'E5001'\n  },\n  // 文化偏好选项\n  CULTURAL_PREFERENCES: ['ancient', 'modern', 'neutral', 'mixed'] as const,\n  // 风格偏好选项\n  STYLE_PREFERENCES: STYLE_PREFERENCES,\n  // 长度偏好选项\n  LENGTH_PREFERENCES: ['short', 'medium', 'long'] as const\n} as const\n\n// 缓存相关常量\nexport const CACHE = {\n  // 默认缓存时间（秒）\n  DEFAULT_TTL: 3600,\n  // 最大缓存条目数\n  MAX_ITEMS: 1000,\n  // 缓存键前缀\n  KEY_PREFIX: 'namer:'\n} as const\n\n// 性能相关常量\nexport const PERFORMANCE = {\n  // 慢查询阈值（毫秒）\n  SLOW_QUERY_THRESHOLD: 500,\n  // 监控采样率（0-1）\n  MONITORING_SAMPLE_RATE: 0.1\n} as const\n\n// 错误消息\nexport const ERROR_MESSAGES = {\n  VALIDATION: {\n    INVALID_COUNT: (min: number, max: number) => `count must be a number between ${min} and ${max}`,\n    INVALID_CREATIVITY_LEVEL: (min: number, max: number) => `creativity_level must be a number between ${min} and ${max}`,\n    INVALID_QUALITY_THRESHOLD: (min: number, max: number) => `quality_threshold must be a number between ${min} and ${max}`,\n    INVALID_CULTURAL_PREFERENCE: (validValues: readonly string[]) => `cultural_preference must be one of: ${validValues.join(', ')}`,\n    INVALID_STYLE_PREFERENCE: (validValues: readonly string[]) => `style_preference must be one of: ${validValues.join(', ')}`,\n    PATTERNS_MUST_BE_ARRAY: 'patterns must be an array'\n  },\n  INTERNAL: {\n    GENERATION_FAILED: 'Failed to generate usernames',\n    VALIDATION_FAILED: 'Request validation failed',\n    INTERNAL_ERROR: 'Internal server error'\n  }\n} as const\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkBS;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAlBT;;;;;;;;;;AAWA,SAASE,eAAe,QAAQ,eAAe;AAE/C,OAAO,MAAMC,iBAAiB;AAAA;AAAA,CAAAH,aAAA,GAAAI,CAAA,OAAG,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAU;AAExH;AACA,OAAO,MAAMC,GAAG;AAAA;AAAA,CAAAL,aAAA,GAAAI,CAAA,OAAG;EACjB;EACAE,OAAO,EAAE,WAAW;EACpB;EACAC,iBAAiB,EAAE,MAAM;EACzB;EACAC,gBAAgB,EAAE;CACV;AAEV;AACA,OAAO,MAAMC,UAAU;AAAA;AAAA,CAAAT,aAAA,GAAAI,CAAA,OAAG;EACxB;EACAM,kBAAkB,EAAE,CAAC;EACrB;EACAC,sBAAsB,EAAE,CAAC;EACzB;EACAC,wBAAwB,EAAE,GAAG;EAC7B;EACAC,yBAAyB,EAAE,GAAG;EAC9B;EACAC,yBAAyB,EAAE,QAAiB;EAC5C;EACAC,2BAA2B,EAAEb,eAAe,CAACc,OAAO;EACpD;EACAC,wBAAwB,EAAE,UAAmB;EAC7C;EACAC,kBAAkB,EAAE,CAAC;EACrB;EACAC,eAAe,EAAE;CACT;AAEV;AACA,OAAO,MAAMC,UAAU;AAAA;AAAA,CAAApB,aAAA,GAAAI,CAAA,OAAG;EACxB;EACAiB,KAAK,EAAE;IACLC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE;GACN;EACD;EACAC,gBAAgB,EAAE;IAChBF,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE;GACN;EACD;EACAE,iBAAiB,EAAE;IACjBH,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE;GACN;EACD;EACAG,WAAW,EAAE;IACXC,kBAAkB,EAAE,OAAO;IAC3BC,sBAAsB,EAAE,OAAO;IAC/BC,iBAAiB,EAAE,OAAO;IAC1BC,iBAAiB,EAAE,OAAO;IAC1BC,cAAc,EAAE;GACjB;EACD;EACAC,oBAAoB,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAU;EACxE;EACA7B,iBAAiB,EAAEA,iBAAiB;EACpC;EACA8B,kBAAkB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM;CACtC;AAEV;AACA,OAAO,MAAMC,KAAK;AAAA;AAAA,CAAAlC,aAAA,GAAAI,CAAA,OAAG;EACnB;EACA+B,WAAW,EAAE,IAAI;EACjB;EACAC,SAAS,EAAE,IAAI;EACf;EACAC,UAAU,EAAE;CACJ;AAEV;AACA,OAAO,MAAMC,WAAW;AAAA;AAAA,CAAAtC,aAAA,GAAAI,CAAA,OAAG;EACzB;EACAmC,oBAAoB,EAAE,GAAG;EACzB;EACAC,sBAAsB,EAAE;CAChB;AAEV;AACA,OAAO,MAAMC,cAAc;AAAA;AAAA,CAAAzC,aAAA,GAAAI,CAAA,OAAG;EAC5BgB,UAAU,EAAE;IACVsB,aAAa,EAAEA,CAACC,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5C,aAAA,GAAA6C,CAAA;MAAA7C,aAAA,GAAAI,CAAA;MAAA,yCAAkCuC,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IAC/FE,wBAAwB,EAAEA,CAACH,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5C,aAAA,GAAA6C,CAAA;MAAA7C,aAAA,GAAAI,CAAA;MAAA,oDAA6CuC,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IACrHG,yBAAyB,EAAEA,CAACJ,GAAW,EAAEC,GAAW,KAAK;MAAA;MAAA5C,aAAA,GAAA6C,CAAA;MAAA7C,aAAA,GAAAI,CAAA;MAAA,qDAA8CuC,GAAG,QAAQC,GAAG,EAAE;IAAF,CAAE;IACvHI,2BAA2B,EAAGC,WAA8B,IAAK;MAAA;MAAAjD,aAAA,GAAA6C,CAAA;MAAA7C,aAAA,GAAAI,CAAA;MAAA,8CAAuC6C,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IAAF,CAAE;IAChIC,wBAAwB,EAAGF,WAA8B,IAAK;MAAA;MAAAjD,aAAA,GAAA6C,CAAA;MAAA7C,aAAA,GAAAI,CAAA;MAAA,2CAAoC6C,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;IAAF,CAAE;IAC1HE,sBAAsB,EAAE;GACzB;EACDC,QAAQ,EAAE;IACRvB,iBAAiB,EAAE,8BAA8B;IACjDD,iBAAiB,EAAE,2BAA2B;IAC9CE,cAAc,EAAE;;CAEV", "ignoreList": []}