{"file": "/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts", "mappings": ";AAAA;;;;GAIG;;AAEH,2CAAoD;AACpD,8CAAkD;AAClD,8DAAkG;AAClG,mEAaoC;AAEpC,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,IAAA,gBAAM,EAAC,yBAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC/C,IAAA,gBAAM,EAAC,yBAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,yBAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,IAAA,gBAAM,EAAC,+BAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAA,gBAAM,EAAC,+BAAa,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,+BAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC/C,IAAA,gBAAM,EAAC,+BAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,IAAA,gBAAM,EAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0CAAsB,CAAC,eAAe,CAAC,CAAA;YACtF,IAAA,gBAAM,EAAC,4CAA0B,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,0CAAsB,CAAC,qBAAqB,CAAC,CAAA;YAClG,IAAA,gBAAM,EAAC,4CAA0B,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,0CAAsB,CAAC,qBAAqB,CAAC,CAAA;QAC/F,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,SAAS,EAAE,GAAG,EAAE;QACvB,IAAA,YAAE,EAAC,cAAc,EAAE,GAAG,EAAE;YACtB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,yBAAe,CAAC,CAAA;YACvD,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,oCAAgB,CAAC,gBAAgB,CAAC,CAAA;YACxE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;YAC7C,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC5C,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;QAC/C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,eAAe,EAAE,GAAG,EAAE;YACvB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,8BAAY,CAAC,CAAA;YACjD,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,YAAY,CAAC,oCAAgB,CAAC,aAAa,CAAC,CAAA;YAClE,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACxC,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,+BAAa,CAAC,CAAA;YACnD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,YAAY,CAAC,oCAAgB,CAAC,cAAc,CAAC,CAAA;YACpE,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YAC1C,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;YAC3C,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YAC5C,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;QAChD,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,cAAc,EAAE,GAAG,EAAE;YACtB,eAAe;YACf,IAAA,gBAAM,EAAC,4CAA0B,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4CAA0B,CAAC,YAAY,CAAC,CAAA;YACxF,IAAA,gBAAM,EAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,4CAA0B,CAAC,YAAY,CAAC,CAAA;YAE/F,YAAY;YACZ,IAAA,gBAAM,EAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;YAC5D,IAAA,gBAAM,EAAC,4CAA0B,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;YAClE,IAAA,gBAAM,EAAC,4CAA0B,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;QAC/D,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,eAAe,EAAE,GAAG,EAAE;YACvB,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,4CAA0B,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0CAAsB,CAAC,eAAe,CAAC,CAAA;YAC9G,MAAM,kBAAkB,GAAG,IAAI,KAAK,CAAC,4CAA0B,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,0CAAsB,CAAC,eAAe,CAAC,CAAA;YAE1H,IAAA,gBAAM,EAAC,YAAY,CAAC,CAAC,YAAY,CAAC,0CAAsB,CAAC,eAAe,CAAC,CAAA;YACzE,IAAA,gBAAM,EAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,0CAAsB,CAAC,qBAAqB,CAAC,CAAA;YAErF,cAAc;YACd,IAAA,gBAAM,EAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,0CAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxF,IAAA,gBAAM,EAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,0CAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChG,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,kBAAkB,GAAG;gBACzB,CAAC,8BAAY,CAAC,KAAK,CAAC,EAAE,4CAAwB,CAAC,KAAK;gBACpD,CAAC,8BAAY,CAAC,KAAK,CAAC,EAAE,4CAAwB,CAAC,KAAK;gBACpD,CAAC,8BAAY,CAAC,KAAK,CAAC,EAAE,4CAAwB,CAAC,KAAK;aACrD,CAAA;YAED,IAAA,gBAAM,EAAC,kBAAkB,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,4CAAwB,CAAC,KAAK,CAAC,CAAA;YACnF,IAAA,gBAAM,EAAC,kBAAkB,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,4CAAwB,CAAC,KAAK,CAAC,CAAA;YACnF,IAAA,gBAAM,EAAC,kBAAkB,CAAC,8BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,4CAAwB,CAAC,KAAK,CAAC,CAAA;QACrF,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,kBAAkB,GAAG,4CAAwB,CAAA;YAEnD,SAAS;YACT,IAAA,gBAAM,EAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,sBAAsB,CAAC,qCAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;YAC3G,IAAA,gBAAM,EAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,mBAAmB,CAAC,qCAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;YACxG,IAAA,gBAAM,EAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,qCAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;YACtG,IAAA,gBAAM,EAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,qCAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;YAEnG,SAAS;YACT,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxE,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpE,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,MAAM,gBAAgB,GAAG,0CAAsB,CAAA;YAE/C,IAAA,gBAAM,EAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,oCAAgB,CAAC,CAAA;YACpE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;YAC1D,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC/D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,sBAAsB,CAAC,qCAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;YACxG,IAAA,gBAAM,EAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,mBAAmB,CAAC,qCAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;QACvG,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,UAAU,EAAE,GAAG,EAAE;QACxB,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,iBAAiB,GAAG,2CAAuB,CAAA;YAEjD,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC/C,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,qCAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;gBACzE,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,qCAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YACxE,CAAC,CAAC,CAAA;YAEF,SAAS;YACT,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,CAAC,oCAAgB,CAAC,kBAAkB,CAAC,CAAA;QAC1F,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,MAAM,UAAU,GAAG,2CAAuB,CAAA;YAE1C,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,oCAAgB,CAAC,kBAAkB,CAAA;YACtH,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,WAAW,CAAC,wCAAoB,EAAE,kCAAc,CAAC,eAAe,CAAC,CAAA;YACjF,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,eAAe,CAAC,2CAAuB,CAAC,YAAY,CAAC,CAAA;YACrE,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,YAAY,CAAC,2CAAuB,CAAC,OAAO,CAAC,CAAA;QAC/D,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAA,kBAAQ,EAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,IAAA,YAAE,EAAC,YAAY,EAAE,GAAG,EAAE;YACpB,qCAAqC;YACrC,MAAM,oBAAoB,GAAG,yBAAe,CAAC,MAAM,CAAA;YACnD,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,yBAAe,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;QACxE,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,SAAS,EAAE,GAAG,EAAE;YACjB,aAAa;YACb,MAAM,QAAQ,GAAG,wCAAoB,CAAC,SAAS,CAAA;YAC/C,MAAM,QAAQ,GAAG,wCAAoB,CAAC,SAAS,CAAA;YAE/C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,qCAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YAC5E,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,qCAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YACzE,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,qCAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YAC5E,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,qCAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;QAC3E,CAAC,CAAC,CAAA;QAEF,IAAA,YAAE,EAAC,aAAa,EAAE,GAAG,EAAE;YACrB,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,MAAM,WAAW,GAAwB,EAAE,CAAA;YAE3C,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5C,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAC,YAAY,CAAC,wCAAoB,CAAC,kBAAkB,CAAC,CAAA;YACxE,IAAA,gBAAM,EAAC,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACzC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,wCAAoB,CAAC,iBAAiB,CAAC,CAAA;QACvF,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA", "names": [], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/test/basic-functionality.test.ts"], "sourcesContent": ["/**\n * 基础功能测试\n * \n * 测试核心类型定义和基本功能的正确性\n */\n\nimport { describe, it, expect } from '@jest/globals'\nimport { CulturalContext } from '../types/core.js'\nimport { LanguageCode, RegisterLevel, SEMANTIC_VECTOR_DIMENSIONS } from '../types/multilingual.js'\nimport {\n  TEST_QUALITY_THRESHOLDS,\n  TEST_VECTOR_DIMENSIONS,\n  TEST_ENUM_COUNTS,\n  TEST_VALUE_RANGES,\n  TEST_MULTILINGUAL_LABELS,\n  TEST_CULTURAL_ADAPTATION,\n  TEST_PHONETIC_FEATURES,\n  TEST_QUALITY_DIMENSIONS,\n  TEST_IPA_PATTERN,\n  TEST_AVERAGE_QUALITY,\n  TEST_PRECISION,\n  TEST_BOUNDARY_VALUES\n} from '../config/test-constants.js'\n\ndescribe('基础功能测试', () => {\n  describe('类型定义测试', () => {\n    it('应该正确定义 CulturalContext 枚举', () => {\n      expect(CulturalContext.ANCIENT).toBe('ancient')\n      expect(CulturalContext.MODERN).toBe('modern')\n      expect(CulturalContext.NEUTRAL).toBe('neutral')\n    })\n\n    it('应该正确定义 LanguageCode 枚举', () => {\n      expect(LanguageCode.ZH_CN).toBe('zh-CN')\n      expect(LanguageCode.EN_US).toBe('en-US')\n      expect(LanguageCode.JA_JP).toBe('ja-JP')\n      expect(LanguageCode.KO_KR).toBe('ko-KR')\n      expect(LanguageCode.ES_ES).toBe('es-ES')\n      expect(LanguageCode.FR_FR).toBe('fr-FR')\n      expect(LanguageCode.DE_DE).toBe('de-DE')\n      expect(LanguageCode.AR_SA).toBe('ar-SA')\n    })\n\n    it('应该正确定义 RegisterLevel 枚举', () => {\n      expect(RegisterLevel.FORMAL).toBe('formal')\n      expect(RegisterLevel.NEUTRAL).toBe('neutral')\n      expect(RegisterLevel.INFORMAL).toBe('informal')\n      expect(RegisterLevel.COLLOQUIAL).toBe('colloquial')\n    })\n\n    it('应该正确定义语义向量维度常量', () => {\n      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBe(TEST_VECTOR_DIMENSIONS.LEGACY_EXPECTED)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBe(TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED)\n    })\n  })\n\n  describe('类型兼容性测试', () => {\n    it('应该支持枚举值的类型检查', () => {\n      const culturalContexts = Object.values(CulturalContext)\n      expect(culturalContexts).toHaveLength(TEST_ENUM_COUNTS.CULTURAL_CONTEXT)\n      expect(culturalContexts).toContain('ancient')\n      expect(culturalContexts).toContain('modern')\n      expect(culturalContexts).toContain('neutral')\n    })\n\n    it('应该支持语言代码的类型检查', () => {\n      const languageCodes = Object.values(LanguageCode)\n      expect(languageCodes).toHaveLength(TEST_ENUM_COUNTS.LANGUAGE_CODE)\n      expect(languageCodes).toContain('zh-CN')\n      expect(languageCodes).toContain('en-US')\n    })\n\n    it('应该支持寄存器级别的类型检查', () => {\n      const registerLevels = Object.values(RegisterLevel)\n      expect(registerLevels).toHaveLength(TEST_ENUM_COUNTS.REGISTER_LEVEL)\n      expect(registerLevels).toContain('formal')\n      expect(registerLevels).toContain('neutral')\n      expect(registerLevels).toContain('informal')\n      expect(registerLevels).toContain('colloquial')\n    })\n  })\n\n  describe('配置常量测试', () => {\n    it('应该正确配置语义向量维度', () => {\n      // 测试向量维度配置的一致性\n      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeLessThan(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)\n      \n      // 测试维度值的合理性\n      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeGreaterThan(0)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBeGreaterThan(0)\n      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBeGreaterThan(0)\n    })\n\n    it('应该支持向量维度的数学运算', () => {\n      const legacyVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).fill(TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)\n      const multilingualVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).fill(TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)\n\n      expect(legacyVector).toHaveLength(TEST_VECTOR_DIMENSIONS.LEGACY_EXPECTED)\n      expect(multilingualVector).toHaveLength(TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED)\n\n      // 测试向量的基本数学属性\n      expect(legacyVector.every(v => v === TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)).toBe(true)\n      expect(multilingualVector.every(v => v === TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)).toBe(true)\n    })\n  })\n\n  describe('数据结构验证', () => {\n    it('应该支持多语言标签映射', () => {\n      const multilingualLabels = {\n        [LanguageCode.ZH_CN]: TEST_MULTILINGUAL_LABELS.ZH_CN,\n        [LanguageCode.EN_US]: TEST_MULTILINGUAL_LABELS.EN_US,\n        [LanguageCode.JA_JP]: TEST_MULTILINGUAL_LABELS.JA_JP\n      }\n\n      expect(multilingualLabels[LanguageCode.ZH_CN]).toBe(TEST_MULTILINGUAL_LABELS.ZH_CN)\n      expect(multilingualLabels[LanguageCode.EN_US]).toBe(TEST_MULTILINGUAL_LABELS.EN_US)\n      expect(multilingualLabels[LanguageCode.JA_JP]).toBe(TEST_MULTILINGUAL_LABELS.JA_JP)\n    })\n\n    it('应该支持文化适应性配置', () => {\n      const culturalAdaptation = TEST_CULTURAL_ADAPTATION\n\n      // 验证数值范围\n      expect(culturalAdaptation.traditionality).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.min)\n      expect(culturalAdaptation.traditionality).toBeLessThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.max)\n      expect(culturalAdaptation.modernity).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.min)\n      expect(culturalAdaptation.modernity).toBeLessThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.max)\n\n      // 验证数组属性\n      expect(Array.isArray(culturalAdaptation.age_appropriateness)).toBe(true)\n      expect(Array.isArray(culturalAdaptation.cultural_tags)).toBe(true)\n    })\n\n    it('应该支持语音特征配置', () => {\n      const phoneticFeatures = TEST_PHONETIC_FEATURES\n\n      expect(phoneticFeatures.ipa_transcription).toMatch(TEST_IPA_PATTERN)\n      expect(phoneticFeatures.syllable_count).toBeGreaterThan(0)\n      expect(Array.isArray(phoneticFeatures.tone_pattern)).toBe(true)\n      expect(phoneticFeatures.phonetic_harmony).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.PHONETIC_HARMONY.min)\n      expect(phoneticFeatures.phonetic_harmony).toBeLessThanOrEqual(TEST_VALUE_RANGES.PHONETIC_HARMONY.max)\n    })\n  })\n\n  describe('质量评估系统测试', () => {\n    it('应该支持8维度质量评分', () => {\n      const qualityDimensions = TEST_QUALITY_DIMENSIONS\n\n      // 验证所有维度都在有效范围内\n      Object.values(qualityDimensions).forEach(score => {\n        expect(score).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.min)\n        expect(score).toBeLessThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.max)\n      })\n\n      // 验证维度数量\n      expect(Object.keys(qualityDimensions)).toHaveLength(TEST_ENUM_COUNTS.QUALITY_DIMENSIONS)\n    })\n\n    it('应该计算平均质量评分', () => {\n      const dimensions = TEST_QUALITY_DIMENSIONS\n\n      const average = Object.values(dimensions).reduce((sum, score) => sum + score, 0) / TEST_ENUM_COUNTS.QUALITY_DIMENSIONS\n      expect(average).toBeCloseTo(TEST_AVERAGE_QUALITY, TEST_PRECISION.FLOAT_PRECISION)\n      expect(average).toBeGreaterThan(TEST_QUALITY_THRESHOLDS.HIGH_QUALITY)\n      expect(average).toBeLessThan(TEST_QUALITY_THRESHOLDS.PERFECT)\n    })\n  })\n\n  describe('错误处理测试', () => {\n    it('应该处理无效的枚举值', () => {\n      // TypeScript 编译时会捕获这些错误，但我们可以测试运行时行为\n      const validCulturalContext = CulturalContext.MODERN\n      expect(Object.values(CulturalContext)).toContain(validCulturalContext)\n    })\n\n    it('应该处理边界值', () => {\n      // 测试质量评分的边界值\n      const minScore = TEST_BOUNDARY_VALUES.MIN_SCORE\n      const maxScore = TEST_BOUNDARY_VALUES.MAX_SCORE\n\n      expect(minScore).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.min)\n      expect(minScore).toBeLessThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.max)\n      expect(maxScore).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.min)\n      expect(maxScore).toBeLessThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.max)\n    })\n\n    it('应该处理空数组和空对象', () => {\n      const emptyArray: string[] = []\n      const emptyObject: Record<string, any> = {}\n\n      expect(Array.isArray(emptyArray)).toBe(true)\n      expect(emptyArray).toHaveLength(TEST_BOUNDARY_VALUES.EMPTY_ARRAY_LENGTH)\n      expect(typeof emptyObject).toBe('object')\n      expect(Object.keys(emptyObject)).toHaveLength(TEST_BOUNDARY_VALUES.EMPTY_OBJECT_KEYS)\n    })\n  })\n})\n"], "version": 3}