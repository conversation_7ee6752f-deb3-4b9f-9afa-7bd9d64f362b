ea7e0bc405713612f107272979166cf0
/* istanbul ignore next */
function cov_1b7zfbkfuu() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/repositories/MorphemeRepository.ts";
  var hash = "29163e5fd18a5c51877527936469caa63ab11bf4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/repositories/MorphemeRepository.ts",
    statementMap: {
      "0": {
        start: {
          line: 24,
          column: 16
        },
        end: {
          line: 24,
          column: 25
        }
      },
      "1": {
        start: {
          line: 26,
          column: 14
        },
        end: {
          line: 33,
          column: 5
        }
      },
      "2": {
        start: {
          line: 35,
          column: 18
        },
        end: {
          line: 35,
          column: 27
        }
      },
      "3": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 25
        }
      },
      "4": {
        start: {
          line: 41,
          column: 21
        },
        end: {
          line: 41,
          column: 25
        }
      },
      "5": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 16
        }
      },
      "6": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 57
        }
      },
      "7": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 66
        }
      },
      "8": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 64,
          column: 9
        }
      },
      "9": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 47
        }
      },
      "10": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 19
        }
      },
      "11": {
        start: {
          line: 65,
          column: 26
        },
        end: {
          line: 65,
          column: 36
        }
      },
      "12": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 66,
          column: 39
        }
      },
      "13": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 95,
          column: 9
        }
      },
      "14": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 40
        }
      },
      "15": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 66
        }
      },
      "16": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 73,
          column: 13
        }
      },
      "17": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 95
        }
      },
      "18": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 41
        }
      },
      "19": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 76,
          column: 65
        }
      },
      "20": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 41
        }
      },
      "21": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 38
        }
      },
      "22": {
        start: {
          line: 81,
          column: 12
        },
        end: {
          line: 81,
          column: 39
        }
      },
      "23": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 36
        }
      },
      "24": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 84,
          column: 40
        }
      },
      "25": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 56
        }
      },
      "26": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 38
        }
      },
      "27": {
        start: {
          line: 87,
          column: 29
        },
        end: {
          line: 87,
          column: 51
        }
      },
      "28": {
        start: {
          line: 88,
          column: 12
        },
        end: {
          line: 88,
          column: 83
        }
      },
      "29": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 44
        }
      },
      "30": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 93,
          column: 49
        }
      },
      "31": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 100
        }
      },
      "32": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 38
        }
      },
      "33": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 31
        }
      },
      "34": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 28
        }
      },
      "35": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 33
        }
      },
      "36": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 110,
          column: 35
        }
      },
      "37": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 112,
          column: 32
        }
      },
      "38": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 121,
          column: 19
        }
      },
      "39": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 19
        }
      },
      "40": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 122,
          column: 36
        }
      },
      "41": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 54
        }
      },
      "42": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 124,
          column: 68
        }
      },
      "43": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 70
        }
      },
      "44": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 126,
          column: 32
        }
      },
      "45": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 130,
          column: 9
        }
      },
      "46": {
        start: {
          line: 128,
          column: 31
        },
        end: {
          line: 128,
          column: 76
        }
      },
      "47": {
        start: {
          line: 129,
          column: 12
        },
        end: {
          line: 129,
          column: 71
        }
      },
      "48": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 34
        }
      },
      "49": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 135,
          column: 9
        }
      },
      "50": {
        start: {
          line: 133,
          column: 31
        },
        end: {
          line: 133,
          column: 76
        }
      },
      "51": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 70
        }
      },
      "52": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 31
        }
      },
      "53": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 149,
          column: 9
        }
      },
      "54": {
        start: {
          line: 148,
          column: 12
        },
        end: {
          line: 148,
          column: 54
        }
      },
      "55": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 64
        }
      },
      "56": {
        start: {
          line: 161,
          column: 26
        },
        end: {
          line: 161,
          column: 36
        }
      },
      "57": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 28
        }
      },
      "58": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 182,
          column: 9
        }
      },
      "59": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 167,
          column: 82
        }
      },
      "60": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 89
        }
      },
      "61": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 88
        }
      },
      "62": {
        start: {
          line: 173,
          column: 34
        },
        end: {
          line: 173,
          column: 78
        }
      },
      "63": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 174,
          column: 77
        }
      },
      "64": {
        start: {
          line: 176,
          column: 36
        },
        end: {
          line: 176,
          column: 91
        }
      },
      "65": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 177,
          column: 87
        }
      },
      "66": {
        start: {
          line: 179,
          column: 12
        },
        end: {
          line: 181,
          column: 13
        }
      },
      "67": {
        start: {
          line: 180,
          column: 16
        },
        end: {
          line: 180,
          column: 68
        }
      },
      "68": {
        start: {
          line: 183,
          column: 26
        },
        end: {
          line: 183,
          column: 48
        }
      },
      "69": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 184,
          column: 54
        }
      },
      "70": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 186,
          column: 29
        }
      },
      "71": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 40
        }
      },
      "72": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 195,
          column: 39
        }
      },
      "73": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 43
        }
      },
      "74": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 197,
          column: 39
        }
      },
      "75": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 198,
          column: 47
        }
      },
      "76": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 36
        }
      },
      "77": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "78": {
        start: {
          line: 211,
          column: 12
        },
        end: {
          line: 211,
          column: 31
        }
      },
      "79": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 213,
          column: 38
        }
      },
      "80": {
        start: {
          line: 226,
          column: 25
        },
        end: {
          line: 226,
          column: 76
        }
      },
      "81": {
        start: {
          line: 227,
          column: 25
        },
        end: {
          line: 227,
          column: 98
        }
      },
      "82": {
        start: {
          line: 227,
          column: 61
        },
        end: {
          line: 227,
          column: 70
        }
      },
      "83": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 237,
          column: 9
        }
      },
      "84": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 230,
          column: 54
        }
      },
      "85": {
        start: {
          line: 232,
          column: 13
        },
        end: {
          line: 237,
          column: 9
        }
      },
      "86": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 233,
          column: 56
        }
      },
      "87": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 236,
          column: 53
        }
      },
      "88": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 245,
          column: 32
        }
      },
      "89": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 68
        }
      },
      "90": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 69
        }
      },
      "91": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 248,
          column: 73
        }
      },
      "92": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 249,
          column: 69
        }
      },
      "93": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 250,
          column: 77
        }
      },
      "94": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 64
        }
      },
      "95": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 261,
          column: 33
        }
      },
      "96": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 269,
          column: 9
        }
      },
      "97": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 265,
          column: 25
        }
      },
      "98": {
        start: {
          line: 265,
          column: 16
        },
        end: {
          line: 265,
          column: 25
        }
      },
      "99": {
        start: {
          line: 266,
          column: 28
        },
        end: {
          line: 266,
          column: 83
        }
      },
      "100": {
        start: {
          line: 266,
          column: 47
        },
        end: {
          line: 266,
          column: 82
        }
      },
      "101": {
        start: {
          line: 267,
          column: 31
        },
        end: {
          line: 267,
          column: 61
        }
      },
      "102": {
        start: {
          line: 268,
          column: 12
        },
        end: {
          line: 268,
          column: 55
        }
      },
      "103": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 277,
          column: 9
        }
      },
      "104": {
        start: {
          line: 272,
          column: 12
        },
        end: {
          line: 273,
          column: 25
        }
      },
      "105": {
        start: {
          line: 273,
          column: 16
        },
        end: {
          line: 273,
          column: 25
        }
      },
      "106": {
        start: {
          line: 274,
          column: 28
        },
        end: {
          line: 274,
          column: 83
        }
      },
      "107": {
        start: {
          line: 274,
          column: 47
        },
        end: {
          line: 274,
          column: 82
        }
      },
      "108": {
        start: {
          line: 275,
          column: 31
        },
        end: {
          line: 275,
          column: 61
        }
      },
      "109": {
        start: {
          line: 276,
          column: 12
        },
        end: {
          line: 276,
          column: 67
        }
      },
      "110": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 278,
          column: 73
        }
      },
      "111": {
        start: {
          line: 290,
          column: 18
        },
        end: {
          line: 290,
          column: 32
        }
      },
      "112": {
        start: {
          line: 291,
          column: 21
        },
        end: {
          line: 291,
          column: 33
        }
      },
      "113": {
        start: {
          line: 292,
          column: 22
        },
        end: {
          line: 292,
          column: 34
        }
      },
      "114": {
        start: {
          line: 294,
          column: 20
        },
        end: {
          line: 294,
          column: 54
        }
      },
      "115": {
        start: {
          line: 294,
          column: 45
        },
        end: {
          line: 294,
          column: 50
        }
      },
      "116": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 304,
          column: 9
        }
      },
      "117": {
        start: {
          line: 297,
          column: 12
        },
        end: {
          line: 297,
          column: 27
        }
      },
      "118": {
        start: {
          line: 298,
          column: 12
        },
        end: {
          line: 298,
          column: 26
        }
      },
      "119": {
        start: {
          line: 299,
          column: 12
        },
        end: {
          line: 303,
          column: 14
        }
      },
      "120": {
        start: {
          line: 302,
          column: 30
        },
        end: {
          line: 302,
          column: 59
        }
      },
      "121": {
        start: {
          line: 305,
          column: 34
        },
        end: {
          line: 305,
          column: 63
        }
      },
      "122": {
        start: {
          line: 305,
          column: 51
        },
        end: {
          line: 305,
          column: 62
        }
      },
      "123": {
        start: {
          line: 307,
          column: 22
        },
        end: {
          line: 307,
          column: 24
        }
      },
      "124": {
        start: {
          line: 308,
          column: 22
        },
        end: {
          line: 308,
          column: 24
        }
      },
      "125": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 316,
          column: 9
        }
      },
      "126": {
        start: {
          line: 309,
          column: 21
        },
        end: {
          line: 309,
          column: 22
        }
      },
      "127": {
        start: {
          line: 310,
          column: 12
        },
        end: {
          line: 315,
          column: 13
        }
      },
      "128": {
        start: {
          line: 311,
          column: 16
        },
        end: {
          line: 311,
          column: 30
        }
      },
      "129": {
        start: {
          line: 314,
          column: 16
        },
        end: {
          line: 314,
          column: 30
        }
      },
      "130": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 330,
          column: 9
        }
      },
      "131": {
        start: {
          line: 319,
          column: 22
        },
        end: {
          line: 319,
          column: 33
        }
      },
      "132": {
        start: {
          line: 320,
          column: 22
        },
        end: {
          line: 320,
          column: 33
        }
      },
      "133": {
        start: {
          line: 321,
          column: 12
        },
        end: {
          line: 321,
          column: 43
        }
      },
      "134": {
        start: {
          line: 322,
          column: 12
        },
        end: {
          line: 322,
          column: 25
        }
      },
      "135": {
        start: {
          line: 323,
          column: 12
        },
        end: {
          line: 323,
          column: 85
        }
      },
      "136": {
        start: {
          line: 324,
          column: 12
        },
        end: {
          line: 329,
          column: 13
        }
      },
      "137": {
        start: {
          line: 325,
          column: 16
        },
        end: {
          line: 325,
          column: 30
        }
      },
      "138": {
        start: {
          line: 328,
          column: 16
        },
        end: {
          line: 328,
          column: 30
        }
      },
      "139": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "140": {
        start: {
          line: 333,
          column: 12
        },
        end: {
          line: 333,
          column: 36
        }
      },
      "141": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 337,
          column: 9
        }
      },
      "142": {
        start: {
          line: 336,
          column: 12
        },
        end: {
          line: 336,
          column: 36
        }
      },
      "143": {
        start: {
          line: 338,
          column: 8
        },
        end: {
          line: 346,
          column: 10
        }
      },
      "144": {
        start: {
          line: 342,
          column: 26
        },
        end: {
          line: 342,
          column: 55
        }
      },
      "145": {
        start: {
          line: 343,
          column: 26
        },
        end: {
          line: 343,
          column: 39
        }
      },
      "146": {
        start: {
          line: 344,
          column: 16
        },
        end: {
          line: 344,
          column: 50
        }
      },
      "147": {
        start: {
          line: 355,
          column: 30
        },
        end: {
          line: 355,
          column: 65
        }
      },
      "148": {
        start: {
          line: 357,
          column: 29
        },
        end: {
          line: 357,
          column: 87
        }
      },
      "149": {
        start: {
          line: 357,
          column: 62
        },
        end: {
          line: 357,
          column: 83
        }
      },
      "150": {
        start: {
          line: 358,
          column: 31
        },
        end: {
          line: 358,
          column: 91
        }
      },
      "151": {
        start: {
          line: 358,
          column: 64
        },
        end: {
          line: 358,
          column: 87
        }
      },
      "152": {
        start: {
          line: 360,
          column: 27
        },
        end: {
          line: 360,
          column: 29
        }
      },
      "153": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 363,
          column: 9
        }
      },
      "154": {
        start: {
          line: 362,
          column: 12
        },
        end: {
          line: 362,
          column: 52
        }
      },
      "155": {
        start: {
          line: 365,
          column: 26
        },
        end: {
          line: 365,
          column: 28
        }
      },
      "156": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 368,
          column: 9
        }
      },
      "157": {
        start: {
          line: 367,
          column: 12
        },
        end: {
          line: 367,
          column: 50
        }
      },
      "158": {
        start: {
          line: 370,
          column: 30
        },
        end: {
          line: 370,
          column: 32
        }
      },
      "159": {
        start: {
          line: 371,
          column: 8
        },
        end: {
          line: 373,
          column: 9
        }
      },
      "160": {
        start: {
          line: 372,
          column: 12
        },
        end: {
          line: 372,
          column: 58
        }
      },
      "161": {
        start: {
          line: 374,
          column: 8
        },
        end: {
          line: 383,
          column: 10
        }
      },
      "162": {
        start: {
          line: 389,
          column: 8
        },
        end: {
          line: 389,
          column: 38
        }
      },
      "163": {
        start: {
          line: 395,
          column: 8
        },
        end: {
          line: 395,
          column: 59
        }
      },
      "164": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 401,
          column: 57
        }
      },
      "165": {
        start: {
          line: 410,
          column: 8
        },
        end: {
          line: 410,
          column: 65
        }
      },
      "166": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 419,
          column: 50
        }
      },
      "167": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 428,
          column: 65
        }
      },
      "168": {
        start: {
          line: 439,
          column: 24
        },
        end: {
          line: 439,
          column: 26
        }
      },
      "169": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 447,
          column: 9
        }
      },
      "170": {
        start: {
          line: 441,
          column: 12
        },
        end: {
          line: 442,
          column: 25
        }
      },
      "171": {
        start: {
          line: 442,
          column: 16
        },
        end: {
          line: 442,
          column: 25
        }
      },
      "172": {
        start: {
          line: 443,
          column: 31
        },
        end: {
          line: 443,
          column: 121
        }
      },
      "173": {
        start: {
          line: 444,
          column: 12
        },
        end: {
          line: 446,
          column: 13
        }
      },
      "174": {
        start: {
          line: 445,
          column: 16
        },
        end: {
          line: 445,
          column: 55
        }
      },
      "175": {
        start: {
          line: 449,
          column: 8
        },
        end: {
          line: 451,
          column: 29
        }
      },
      "176": {
        start: {
          line: 450,
          column: 28
        },
        end: {
          line: 450,
          column: 55
        }
      },
      "177": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 465,
          column: 9
        }
      },
      "178": {
        start: {
          line: 464,
          column: 12
        },
        end: {
          line: 464,
          column: 41
        }
      },
      "179": {
        start: {
          line: 466,
          column: 25
        },
        end: {
          line: 466,
          column: 26
        }
      },
      "180": {
        start: {
          line: 467,
          column: 20
        },
        end: {
          line: 467,
          column: 21
        }
      },
      "181": {
        start: {
          line: 468,
          column: 20
        },
        end: {
          line: 468,
          column: 21
        }
      },
      "182": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 473,
          column: 9
        }
      },
      "183": {
        start: {
          line: 469,
          column: 21
        },
        end: {
          line: 469,
          column: 22
        }
      },
      "184": {
        start: {
          line: 470,
          column: 12
        },
        end: {
          line: 470,
          column: 50
        }
      },
      "185": {
        start: {
          line: 471,
          column: 12
        },
        end: {
          line: 471,
          column: 45
        }
      },
      "186": {
        start: {
          line: 472,
          column: 12
        },
        end: {
          line: 472,
          column: 45
        }
      },
      "187": {
        start: {
          line: 474,
          column: 26
        },
        end: {
          line: 474,
          column: 61
        }
      },
      "188": {
        start: {
          line: 475,
          column: 8
        },
        end: {
          line: 475,
          column: 60
        }
      },
      "189": {
        start: {
          line: 485,
          column: 26
        },
        end: {
          line: 485,
          column: 63
        }
      },
      "190": {
        start: {
          line: 486,
          column: 27
        },
        end: {
          line: 486,
          column: 57
        }
      },
      "191": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 489,
          column: 9
        }
      },
      "192": {
        start: {
          line: 488,
          column: 12
        },
        end: {
          line: 488,
          column: 22
        }
      },
      "193": {
        start: {
          line: 490,
          column: 24
        },
        end: {
          line: 490,
          column: 26
        }
      },
      "194": {
        start: {
          line: 491,
          column: 28
        },
        end: {
          line: 491,
          column: 37
        }
      },
      "195": {
        start: {
          line: 492,
          column: 8
        },
        end: {
          line: 500,
          column: 9
        }
      },
      "196": {
        start: {
          line: 492,
          column: 21
        },
        end: {
          line: 492,
          column: 22
        }
      },
      "197": {
        start: {
          line: 493,
          column: 24
        },
        end: {
          line: 493,
          column: 43
        }
      },
      "198": {
        start: {
          line: 495,
          column: 12
        },
        end: {
          line: 497,
          column: 13
        }
      },
      "199": {
        start: {
          line: 496,
          column: 16
        },
        end: {
          line: 496,
          column: 44
        }
      },
      "200": {
        start: {
          line: 498,
          column: 12
        },
        end: {
          line: 498,
          column: 35
        }
      },
      "201": {
        start: {
          line: 499,
          column: 12
        },
        end: {
          line: 499,
          column: 43
        }
      },
      "202": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 501,
          column: 23
        }
      },
      "203": {
        start: {
          line: 511,
          column: 26
        },
        end: {
          line: 511,
          column: 61
        }
      },
      "204": {
        start: {
          line: 512,
          column: 27
        },
        end: {
          line: 512,
          column: 69
        }
      },
      "205": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 515,
          column: 9
        }
      },
      "206": {
        start: {
          line: 514,
          column: 12
        },
        end: {
          line: 514,
          column: 22
        }
      },
      "207": {
        start: {
          line: 516,
          column: 24
        },
        end: {
          line: 516,
          column: 26
        }
      },
      "208": {
        start: {
          line: 517,
          column: 28
        },
        end: {
          line: 517,
          column: 37
        }
      },
      "209": {
        start: {
          line: 518,
          column: 8
        },
        end: {
          line: 526,
          column: 9
        }
      },
      "210": {
        start: {
          line: 518,
          column: 21
        },
        end: {
          line: 518,
          column: 22
        }
      },
      "211": {
        start: {
          line: 519,
          column: 24
        },
        end: {
          line: 519,
          column: 43
        }
      },
      "212": {
        start: {
          line: 521,
          column: 12
        },
        end: {
          line: 523,
          column: 13
        }
      },
      "213": {
        start: {
          line: 522,
          column: 16
        },
        end: {
          line: 522,
          column: 44
        }
      },
      "214": {
        start: {
          line: 524,
          column: 12
        },
        end: {
          line: 524,
          column: 35
        }
      },
      "215": {
        start: {
          line: 525,
          column: 12
        },
        end: {
          line: 525,
          column: 43
        }
      },
      "216": {
        start: {
          line: 527,
          column: 8
        },
        end: {
          line: 527,
          column: 23
        }
      },
      "217": {
        start: {
          line: 533,
          column: 24
        },
        end: {
          line: 533,
          column: 26
        }
      },
      "218": {
        start: {
          line: 534,
          column: 8
        },
        end: {
          line: 538,
          column: 9
        }
      },
      "219": {
        start: {
          line: 535,
          column: 12
        },
        end: {
          line: 537,
          column: 13
        }
      },
      "220": {
        start: {
          line: 536,
          column: 16
        },
        end: {
          line: 536,
          column: 43
        }
      },
      "221": {
        start: {
          line: 539,
          column: 8
        },
        end: {
          line: 539,
          column: 23
        }
      },
      "222": {
        start: {
          line: 545,
          column: 25
        },
        end: {
          line: 545,
          column: 60
        }
      },
      "223": {
        start: {
          line: 547,
          column: 8
        },
        end: {
          line: 549,
          column: 9
        }
      },
      "224": {
        start: {
          line: 548,
          column: 12
        },
        end: {
          line: 548,
          column: 82
        }
      },
      "225": {
        start: {
          line: 548,
          column: 48
        },
        end: {
          line: 548,
          column: 80
        }
      },
      "226": {
        start: {
          line: 551,
          column: 8
        },
        end: {
          line: 553,
          column: 9
        }
      },
      "227": {
        start: {
          line: 552,
          column: 12
        },
        end: {
          line: 552,
          column: 98
        }
      },
      "228": {
        start: {
          line: 552,
          column: 48
        },
        end: {
          line: 552,
          column: 96
        }
      },
      "229": {
        start: {
          line: 555,
          column: 8
        },
        end: {
          line: 557,
          column: 9
        }
      },
      "230": {
        start: {
          line: 556,
          column: 12
        },
        end: {
          line: 556,
          column: 95
        }
      },
      "231": {
        start: {
          line: 556,
          column: 48
        },
        end: {
          line: 556,
          column: 93
        }
      },
      "232": {
        start: {
          line: 558,
          column: 8
        },
        end: {
          line: 560,
          column: 9
        }
      },
      "233": {
        start: {
          line: 559,
          column: 12
        },
        end: {
          line: 559,
          column: 95
        }
      },
      "234": {
        start: {
          line: 559,
          column: 48
        },
        end: {
          line: 559,
          column: 93
        }
      },
      "235": {
        start: {
          line: 562,
          column: 8
        },
        end: {
          line: 564,
          column: 9
        }
      },
      "236": {
        start: {
          line: 563,
          column: 12
        },
        end: {
          line: 563,
          column: 97
        }
      },
      "237": {
        start: {
          line: 563,
          column: 48
        },
        end: {
          line: 563,
          column: 95
        }
      },
      "238": {
        start: {
          line: 563,
          column: 74
        },
        end: {
          line: 563,
          column: 94
        }
      },
      "239": {
        start: {
          line: 566,
          column: 8
        },
        end: {
          line: 568,
          column: 9
        }
      },
      "240": {
        start: {
          line: 567,
          column: 12
        },
        end: {
          line: 567,
          column: 86
        }
      },
      "241": {
        start: {
          line: 567,
          column: 48
        },
        end: {
          line: 567,
          column: 84
        }
      },
      "242": {
        start: {
          line: 570,
          column: 8
        },
        end: {
          line: 574,
          column: 9
        }
      },
      "243": {
        start: {
          line: 572,
          column: 29
        },
        end: {
          line: 572,
          column: 76
        }
      },
      "244": {
        start: {
          line: 572,
          column: 56
        },
        end: {
          line: 572,
          column: 75
        }
      },
      "245": {
        start: {
          line: 573,
          column: 12
        },
        end: {
          line: 573,
          column: 59
        }
      },
      "246": {
        start: {
          line: 575,
          column: 8
        },
        end: {
          line: 575,
          column: 26
        }
      },
      "247": {
        start: {
          line: 581,
          column: 8
        },
        end: {
          line: 581,
          column: 51
        }
      },
      "248": {
        start: {
          line: 589,
          column: 8
        },
        end: {
          line: 591,
          column: 9
        }
      },
      "249": {
        start: {
          line: 590,
          column: 12
        },
        end: {
          line: 590,
          column: 48
        }
      },
      "250": {
        start: {
          line: 592,
          column: 8
        },
        end: {
          line: 592,
          column: 33
        }
      },
      "251": {
        start: {
          line: 600,
          column: 8
        },
        end: {
          line: 600,
          column: 35
        }
      },
      "252": {
        start: {
          line: 608,
          column: 8
        },
        end: {
          line: 608,
          column: 34
        }
      },
      "253": {
        start: {
          line: 616,
          column: 8
        },
        end: {
          line: 616,
          column: 35
        }
      },
      "254": {
        start: {
          line: 624,
          column: 8
        },
        end: {
          line: 624,
          column: 34
        }
      },
      "255": {
        start: {
          line: 625,
          column: 8
        },
        end: {
          line: 625,
          column: 31
        }
      },
      "256": {
        start: {
          line: 626,
          column: 8
        },
        end: {
          line: 626,
          column: 28
        }
      },
      "257": {
        start: {
          line: 627,
          column: 8
        },
        end: {
          line: 627,
          column: 33
        }
      },
      "258": {
        start: {
          line: 628,
          column: 8
        },
        end: {
          line: 628,
          column: 35
        }
      },
      "259": {
        start: {
          line: 629,
          column: 8
        },
        end: {
          line: 629,
          column: 26
        }
      },
      "260": {
        start: {
          line: 630,
          column: 8
        },
        end: {
          line: 630,
          column: 35
        }
      },
      "261": {
        start: {
          line: 631,
          column: 8
        },
        end: {
          line: 631,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 49,
            column: 4
          },
          end: {
            line: 49,
            column: 5
          }
        },
        loc: {
          start: {
            line: 49,
            column: 43
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 49
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        },
        loc: {
          start: {
            line: 60,
            column: 23
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 60
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        loc: {
          start: {
            line: 104,
            column: 19
          },
          end: {
            line: 113,
            column: 5
          }
        },
        line: 104
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        },
        loc: {
          start: {
            line: 119,
            column: 31
          },
          end: {
            line: 136,
            column: 5
          }
        },
        line: 119
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        },
        loc: {
          start: {
            line: 145,
            column: 32
          },
          end: {
            line: 151,
            column: 5
          }
        },
        line: 145
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 160,
            column: 4
          },
          end: {
            line: 160,
            column: 5
          }
        },
        loc: {
          start: {
            line: 160,
            column: 25
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 160
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 193,
            column: 4
          },
          end: {
            line: 193,
            column: 5
          }
        },
        loc: {
          start: {
            line: 193,
            column: 19
          },
          end: {
            line: 200,
            column: 5
          }
        },
        line: 193
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 209,
            column: 5
          }
        },
        loc: {
          start: {
            line: 209,
            column: 37
          },
          end: {
            line: 214,
            column: 5
          }
        },
        line: 209
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        },
        loc: {
          start: {
            line: 224,
            column: 45
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 224
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 227,
            column: 47
          },
          end: {
            line: 227,
            column: 48
          }
        },
        loc: {
          start: {
            line: 227,
            column: 61
          },
          end: {
            line: 227,
            column: 70
          }
        },
        line: 227
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 244,
            column: 4
          },
          end: {
            line: 244,
            column: 5
          }
        },
        loc: {
          start: {
            line: 244,
            column: 20
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 244
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 260,
            column: 4
          },
          end: {
            line: 260,
            column: 5
          }
        },
        loc: {
          start: {
            line: 260,
            column: 23
          },
          end: {
            line: 279,
            column: 5
          }
        },
        line: 260
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 266,
            column: 42
          },
          end: {
            line: 266,
            column: 43
          }
        },
        loc: {
          start: {
            line: 266,
            column: 47
          },
          end: {
            line: 266,
            column: 82
          }
        },
        line: 266
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 274,
            column: 42
          },
          end: {
            line: 274,
            column: 43
          }
        },
        loc: {
          start: {
            line: 274,
            column: 47
          },
          end: {
            line: 274,
            column: 82
          }
        },
        line: 274
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 289,
            column: 4
          },
          end: {
            line: 289,
            column: 5
          }
        },
        loc: {
          start: {
            line: 289,
            column: 30
          },
          end: {
            line: 347,
            column: 5
          }
        },
        line: 289
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 294,
            column: 35
          },
          end: {
            line: 294,
            column: 36
          }
        },
        loc: {
          start: {
            line: 294,
            column: 45
          },
          end: {
            line: 294,
            column: 50
          }
        },
        line: 294
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 302,
            column: 24
          },
          end: {
            line: 302,
            column: 25
          }
        },
        loc: {
          start: {
            line: 302,
            column: 30
          },
          end: {
            line: 302,
            column: 59
          }
        },
        line: 302
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 305,
            column: 46
          },
          end: {
            line: 305,
            column: 47
          }
        },
        loc: {
          start: {
            line: 305,
            column: 51
          },
          end: {
            line: 305,
            column: 62
          }
        },
        line: 305
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 341,
            column: 12
          },
          end: {
            line: 341,
            column: 13
          }
        },
        loc: {
          start: {
            line: 341,
            column: 21
          },
          end: {
            line: 345,
            column: 13
          }
        },
        line: 341
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 354,
            column: 5
          }
        },
        loc: {
          start: {
            line: 354,
            column: 35
          },
          end: {
            line: 384,
            column: 5
          }
        },
        line: 354
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 357,
            column: 50
          },
          end: {
            line: 357,
            column: 51
          }
        },
        loc: {
          start: {
            line: 357,
            column: 62
          },
          end: {
            line: 357,
            column: 83
          }
        },
        line: 357
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 358,
            column: 52
          },
          end: {
            line: 358,
            column: 53
          }
        },
        loc: {
          start: {
            line: 358,
            column: 64
          },
          end: {
            line: 358,
            column: 87
          }
        },
        line: 358
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 388,
            column: 5
          }
        },
        loc: {
          start: {
            line: 388,
            column: 17
          },
          end: {
            line: 390,
            column: 5
          }
        },
        line: 388
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 394,
            column: 4
          },
          end: {
            line: 394,
            column: 5
          }
        },
        loc: {
          start: {
            line: 394,
            column: 29
          },
          end: {
            line: 396,
            column: 5
          }
        },
        line: 394
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 400,
            column: 4
          },
          end: {
            line: 400,
            column: 5
          }
        },
        loc: {
          start: {
            line: 400,
            column: 27
          },
          end: {
            line: 402,
            column: 5
          }
        },
        line: 400
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 409,
            column: 4
          },
          end: {
            line: 409,
            column: 5
          }
        },
        loc: {
          start: {
            line: 409,
            column: 35
          },
          end: {
            line: 411,
            column: 5
          }
        },
        line: 409
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        },
        loc: {
          start: {
            line: 418,
            column: 19
          },
          end: {
            line: 420,
            column: 5
          }
        },
        line: 418
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 5
          }
        },
        loc: {
          start: {
            line: 427,
            column: 35
          },
          end: {
            line: 429,
            column: 5
          }
        },
        line: 427
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 438,
            column: 4
          },
          end: {
            line: 438,
            column: 5
          }
        },
        loc: {
          start: {
            line: 438,
            column: 61
          },
          end: {
            line: 452,
            column: 5
          }
        },
        line: 438
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 450,
            column: 18
          },
          end: {
            line: 450,
            column: 19
          }
        },
        loc: {
          start: {
            line: 450,
            column: 28
          },
          end: {
            line: 450,
            column: 55
          }
        },
        line: 450
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 462,
            column: 4
          },
          end: {
            line: 462,
            column: 5
          }
        },
        loc: {
          start: {
            line: 462,
            column: 50
          },
          end: {
            line: 476,
            column: 5
          }
        },
        line: 462
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 484,
            column: 4
          },
          end: {
            line: 484,
            column: 5
          }
        },
        loc: {
          start: {
            line: 484,
            column: 42
          },
          end: {
            line: 502,
            column: 5
          }
        },
        line: 484
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 510,
            column: 4
          },
          end: {
            line: 510,
            column: 5
          }
        },
        loc: {
          start: {
            line: 510,
            column: 40
          },
          end: {
            line: 528,
            column: 5
          }
        },
        line: 510
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 532,
            column: 4
          },
          end: {
            line: 532,
            column: 5
          }
        },
        loc: {
          start: {
            line: 532,
            column: 43
          },
          end: {
            line: 540,
            column: 5
          }
        },
        line: 532
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 544,
            column: 4
          },
          end: {
            line: 544,
            column: 5
          }
        },
        loc: {
          start: {
            line: 544,
            column: 21
          },
          end: {
            line: 576,
            column: 5
          }
        },
        line: 544
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 548,
            column: 43
          },
          end: {
            line: 548,
            column: 44
          }
        },
        loc: {
          start: {
            line: 548,
            column: 48
          },
          end: {
            line: 548,
            column: 80
          }
        },
        line: 548
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 552,
            column: 43
          },
          end: {
            line: 552,
            column: 44
          }
        },
        loc: {
          start: {
            line: 552,
            column: 48
          },
          end: {
            line: 552,
            column: 96
          }
        },
        line: 552
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 556,
            column: 43
          },
          end: {
            line: 556,
            column: 44
          }
        },
        loc: {
          start: {
            line: 556,
            column: 48
          },
          end: {
            line: 556,
            column: 93
          }
        },
        line: 556
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 559,
            column: 43
          },
          end: {
            line: 559,
            column: 44
          }
        },
        loc: {
          start: {
            line: 559,
            column: 48
          },
          end: {
            line: 559,
            column: 93
          }
        },
        line: 559
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 563,
            column: 43
          },
          end: {
            line: 563,
            column: 44
          }
        },
        loc: {
          start: {
            line: 563,
            column: 48
          },
          end: {
            line: 563,
            column: 95
          }
        },
        line: 563
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 563,
            column: 67
          },
          end: {
            line: 563,
            column: 68
          }
        },
        loc: {
          start: {
            line: 563,
            column: 74
          },
          end: {
            line: 563,
            column: 94
          }
        },
        line: 563
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 567,
            column: 43
          },
          end: {
            line: 567,
            column: 44
          }
        },
        loc: {
          start: {
            line: 567,
            column: 48
          },
          end: {
            line: 567,
            column: 84
          }
        },
        line: 567
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 572,
            column: 50
          },
          end: {
            line: 572,
            column: 51
          }
        },
        loc: {
          start: {
            line: 572,
            column: 56
          },
          end: {
            line: 572,
            column: 75
          }
        },
        line: 572
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 580,
            column: 4
          },
          end: {
            line: 580,
            column: 5
          }
        },
        loc: {
          start: {
            line: 580,
            column: 13
          },
          end: {
            line: 582,
            column: 5
          }
        },
        line: 580
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 588,
            column: 4
          },
          end: {
            line: 588,
            column: 5
          }
        },
        loc: {
          start: {
            line: 588,
            column: 15
          },
          end: {
            line: 593,
            column: 5
          }
        },
        line: 588
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 599,
            column: 4
          },
          end: {
            line: 599,
            column: 5
          }
        },
        loc: {
          start: {
            line: 599,
            column: 24
          },
          end: {
            line: 601,
            column: 5
          }
        },
        line: 599
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 607,
            column: 4
          },
          end: {
            line: 607,
            column: 5
          }
        },
        loc: {
          start: {
            line: 607,
            column: 14
          },
          end: {
            line: 609,
            column: 5
          }
        },
        line: 607
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 615,
            column: 4
          },
          end: {
            line: 615,
            column: 5
          }
        },
        loc: {
          start: {
            line: 615,
            column: 15
          },
          end: {
            line: 617,
            column: 5
          }
        },
        line: 615
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 623,
            column: 4
          },
          end: {
            line: 623,
            column: 5
          }
        },
        loc: {
          start: {
            line: 623,
            column: 14
          },
          end: {
            line: 632,
            column: 5
          }
        },
        line: 623
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 26
          },
          end: {
            line: 50,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 26
          },
          end: {
            line: 50,
            column: 36
          }
        }, {
          start: {
            line: 50,
            column: 40
          },
          end: {
            line: 50,
            column: 56
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 51,
            column: 29
          },
          end: {
            line: 51,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 29
          },
          end: {
            line: 51,
            column: 42
          }
        }, {
          start: {
            line: 51,
            column: 46
          },
          end: {
            line: 51,
            column: 65
          }
        }],
        line: 51
      },
      "2": {
        loc: {
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 64,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 64,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "3": {
        loc: {
          start: {
            line: 71,
            column: 12
          },
          end: {
            line: 73,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 12
          },
          end: {
            line: 73,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "4": {
        loc: {
          start: {
            line: 94,
            column: 42
          },
          end: {
            line: 94,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 94,
            column: 67
          },
          end: {
            line: 94,
            column: 80
          }
        }, {
          start: {
            line: 94,
            column: 83
          },
          end: {
            line: 94,
            column: 96
          }
        }],
        line: 94
      },
      "5": {
        loc: {
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 121,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 121,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "6": {
        loc: {
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 212,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 212,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "7": {
        loc: {
          start: {
            line: 229,
            column: 8
          },
          end: {
            line: 237,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 8
          },
          end: {
            line: 237,
            column: 9
          }
        }, {
          start: {
            line: 232,
            column: 13
          },
          end: {
            line: 237,
            column: 9
          }
        }],
        line: 229
      },
      "8": {
        loc: {
          start: {
            line: 232,
            column: 13
          },
          end: {
            line: 237,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 13
          },
          end: {
            line: 237,
            column: 9
          }
        }, {
          start: {
            line: 235,
            column: 13
          },
          end: {
            line: 237,
            column: 9
          }
        }],
        line: 232
      },
      "9": {
        loc: {
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 265,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 265,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "10": {
        loc: {
          start: {
            line: 272,
            column: 12
          },
          end: {
            line: 273,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 12
          },
          end: {
            line: 273,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "11": {
        loc: {
          start: {
            line: 295,
            column: 8
          },
          end: {
            line: 304,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 8
          },
          end: {
            line: 304,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "12": {
        loc: {
          start: {
            line: 310,
            column: 12
          },
          end: {
            line: 315,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 12
          },
          end: {
            line: 315,
            column: 13
          }
        }, {
          start: {
            line: 313,
            column: 17
          },
          end: {
            line: 315,
            column: 13
          }
        }],
        line: 310
      },
      "13": {
        loc: {
          start: {
            line: 318,
            column: 15
          },
          end: {
            line: 318,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 15
          },
          end: {
            line: 318,
            column: 31
          }
        }, {
          start: {
            line: 318,
            column: 35
          },
          end: {
            line: 318,
            column: 51
          }
        }],
        line: 318
      },
      "14": {
        loc: {
          start: {
            line: 324,
            column: 12
          },
          end: {
            line: 329,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 12
          },
          end: {
            line: 329,
            column: 13
          }
        }, {
          start: {
            line: 327,
            column: 17
          },
          end: {
            line: 329,
            column: 13
          }
        }],
        line: 324
      },
      "15": {
        loc: {
          start: {
            line: 344,
            column: 23
          },
          end: {
            line: 344,
            column: 49
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 344,
            column: 37
          },
          end: {
            line: 344,
            column: 38
          }
        }, {
          start: {
            line: 344,
            column: 41
          },
          end: {
            line: 344,
            column: 49
          }
        }],
        line: 344
      },
      "16": {
        loc: {
          start: {
            line: 379,
            column: 24
          },
          end: {
            line: 379,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 379,
            column: 50
          },
          end: {
            line: 379,
            column: 84
          }
        }, {
          start: {
            line: 379,
            column: 87
          },
          end: {
            line: 379,
            column: 88
          }
        }],
        line: 379
      },
      "17": {
        loc: {
          start: {
            line: 380,
            column: 26
          },
          end: {
            line: 380,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 380,
            column: 52
          },
          end: {
            line: 380,
            column: 88
          }
        }, {
          start: {
            line: 380,
            column: 91
          },
          end: {
            line: 380,
            column: 92
          }
        }],
        line: 380
      },
      "18": {
        loc: {
          start: {
            line: 395,
            column: 15
          },
          end: {
            line: 395,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 395,
            column: 15
          },
          end: {
            line: 395,
            column: 52
          }
        }, {
          start: {
            line: 395,
            column: 56
          },
          end: {
            line: 395,
            column: 58
          }
        }],
        line: 395
      },
      "19": {
        loc: {
          start: {
            line: 401,
            column: 15
          },
          end: {
            line: 401,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 401,
            column: 15
          },
          end: {
            line: 401,
            column: 50
          }
        }, {
          start: {
            line: 401,
            column: 54
          },
          end: {
            line: 401,
            column: 56
          }
        }],
        line: 401
      },
      "20": {
        loc: {
          start: {
            line: 410,
            column: 15
          },
          end: {
            line: 410,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 410,
            column: 15
          },
          end: {
            line: 410,
            column: 58
          }
        }, {
          start: {
            line: 410,
            column: 62
          },
          end: {
            line: 410,
            column: 64
          }
        }],
        line: 410
      },
      "21": {
        loc: {
          start: {
            line: 419,
            column: 15
          },
          end: {
            line: 419,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 419,
            column: 15
          },
          end: {
            line: 419,
            column: 43
          }
        }, {
          start: {
            line: 419,
            column: 47
          },
          end: {
            line: 419,
            column: 49
          }
        }],
        line: 419
      },
      "22": {
        loc: {
          start: {
            line: 428,
            column: 15
          },
          end: {
            line: 428,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 428,
            column: 15
          },
          end: {
            line: 428,
            column: 58
          }
        }, {
          start: {
            line: 428,
            column: 62
          },
          end: {
            line: 428,
            column: 64
          }
        }],
        line: 428
      },
      "23": {
        loc: {
          start: {
            line: 438,
            column: 32
          },
          end: {
            line: 438,
            column: 47
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 438,
            column: 44
          },
          end: {
            line: 438,
            column: 47
          }
        }],
        line: 438
      },
      "24": {
        loc: {
          start: {
            line: 438,
            column: 49
          },
          end: {
            line: 438,
            column: 59
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 438,
            column: 57
          },
          end: {
            line: 438,
            column: 59
          }
        }],
        line: 438
      },
      "25": {
        loc: {
          start: {
            line: 441,
            column: 12
          },
          end: {
            line: 442,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 441,
            column: 12
          },
          end: {
            line: 442,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 441
      },
      "26": {
        loc: {
          start: {
            line: 444,
            column: 12
          },
          end: {
            line: 446,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 444,
            column: 12
          },
          end: {
            line: 446,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 444
      },
      "27": {
        loc: {
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 465,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 465,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 463
      },
      "28": {
        loc: {
          start: {
            line: 475,
            column: 15
          },
          end: {
            line: 475,
            column: 59
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 475,
            column: 33
          },
          end: {
            line: 475,
            column: 34
          }
        }, {
          start: {
            line: 475,
            column: 37
          },
          end: {
            line: 475,
            column: 59
          }
        }],
        line: 475
      },
      "29": {
        loc: {
          start: {
            line: 484,
            column: 31
          },
          end: {
            line: 484,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 484,
            column: 39
          },
          end: {
            line: 484,
            column: 40
          }
        }],
        line: 484
      },
      "30": {
        loc: {
          start: {
            line: 487,
            column: 8
          },
          end: {
            line: 489,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 487,
            column: 8
          },
          end: {
            line: 489,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 487
      },
      "31": {
        loc: {
          start: {
            line: 487,
            column: 12
          },
          end: {
            line: 487,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 487,
            column: 12
          },
          end: {
            line: 487,
            column: 22
          }
        }, {
          start: {
            line: 487,
            column: 26
          },
          end: {
            line: 487,
            column: 37
          }
        }, {
          start: {
            line: 487,
            column: 41
          },
          end: {
            line: 487,
            column: 63
          }
        }],
        line: 487
      },
      "32": {
        loc: {
          start: {
            line: 492,
            column: 24
          },
          end: {
            line: 492,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 492,
            column: 24
          },
          end: {
            line: 492,
            column: 33
          }
        }, {
          start: {
            line: 492,
            column: 37
          },
          end: {
            line: 492,
            column: 72
          }
        }],
        line: 492
      },
      "33": {
        loc: {
          start: {
            line: 495,
            column: 19
          },
          end: {
            line: 495,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 495,
            column: 19
          },
          end: {
            line: 495,
            column: 41
          }
        }, {
          start: {
            line: 495,
            column: 45
          },
          end: {
            line: 495,
            column: 80
          }
        }],
        line: 495
      },
      "34": {
        loc: {
          start: {
            line: 510,
            column: 29
          },
          end: {
            line: 510,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 510,
            column: 37
          },
          end: {
            line: 510,
            column: 38
          }
        }],
        line: 510
      },
      "35": {
        loc: {
          start: {
            line: 513,
            column: 8
          },
          end: {
            line: 515,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 513,
            column: 8
          },
          end: {
            line: 515,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 513
      },
      "36": {
        loc: {
          start: {
            line: 513,
            column: 12
          },
          end: {
            line: 513,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 513,
            column: 12
          },
          end: {
            line: 513,
            column: 22
          }
        }, {
          start: {
            line: 513,
            column: 26
          },
          end: {
            line: 513,
            column: 37
          }
        }, {
          start: {
            line: 513,
            column: 41
          },
          end: {
            line: 513,
            column: 63
          }
        }],
        line: 513
      },
      "37": {
        loc: {
          start: {
            line: 518,
            column: 24
          },
          end: {
            line: 518,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 518,
            column: 24
          },
          end: {
            line: 518,
            column: 33
          }
        }, {
          start: {
            line: 518,
            column: 37
          },
          end: {
            line: 518,
            column: 72
          }
        }],
        line: 518
      },
      "38": {
        loc: {
          start: {
            line: 521,
            column: 19
          },
          end: {
            line: 521,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 521,
            column: 19
          },
          end: {
            line: 521,
            column: 41
          }
        }, {
          start: {
            line: 521,
            column: 45
          },
          end: {
            line: 521,
            column: 80
          }
        }],
        line: 521
      },
      "39": {
        loc: {
          start: {
            line: 535,
            column: 12
          },
          end: {
            line: 537,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 535,
            column: 12
          },
          end: {
            line: 537,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 535
      },
      "40": {
        loc: {
          start: {
            line: 535,
            column: 16
          },
          end: {
            line: 535,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 535,
            column: 16
          },
          end: {
            line: 535,
            column: 33
          }
        }, {
          start: {
            line: 535,
            column: 37
          },
          end: {
            line: 535,
            column: 54
          }
        }],
        line: 535
      },
      "41": {
        loc: {
          start: {
            line: 547,
            column: 8
          },
          end: {
            line: 549,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 547,
            column: 8
          },
          end: {
            line: 549,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 547
      },
      "42": {
        loc: {
          start: {
            line: 551,
            column: 8
          },
          end: {
            line: 553,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 551,
            column: 8
          },
          end: {
            line: 553,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 551
      },
      "43": {
        loc: {
          start: {
            line: 555,
            column: 8
          },
          end: {
            line: 557,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 555,
            column: 8
          },
          end: {
            line: 557,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 555
      },
      "44": {
        loc: {
          start: {
            line: 558,
            column: 8
          },
          end: {
            line: 560,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 558,
            column: 8
          },
          end: {
            line: 560,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 558
      },
      "45": {
        loc: {
          start: {
            line: 562,
            column: 8
          },
          end: {
            line: 564,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 562,
            column: 8
          },
          end: {
            line: 564,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 562
      },
      "46": {
        loc: {
          start: {
            line: 562,
            column: 12
          },
          end: {
            line: 562,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 562,
            column: 12
          },
          end: {
            line: 562,
            column: 25
          }
        }, {
          start: {
            line: 562,
            column: 29
          },
          end: {
            line: 562,
            column: 53
          }
        }],
        line: 562
      },
      "47": {
        loc: {
          start: {
            line: 566,
            column: 8
          },
          end: {
            line: 568,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 566,
            column: 8
          },
          end: {
            line: 568,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 566
      },
      "48": {
        loc: {
          start: {
            line: 566,
            column: 12
          },
          end: {
            line: 566,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 566,
            column: 12
          },
          end: {
            line: 566,
            column: 32
          }
        }, {
          start: {
            line: 566,
            column: 36
          },
          end: {
            line: 566,
            column: 67
          }
        }],
        line: 566
      },
      "49": {
        loc: {
          start: {
            line: 570,
            column: 8
          },
          end: {
            line: 574,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 570,
            column: 8
          },
          end: {
            line: 574,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 570
      },
      "50": {
        loc: {
          start: {
            line: 570,
            column: 12
          },
          end: {
            line: 570,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 570,
            column: 12
          },
          end: {
            line: 570,
            column: 26
          }
        }, {
          start: {
            line: 570,
            column: 30
          },
          end: {
            line: 570,
            column: 64
          }
        }],
        line: 570
      },
      "51": {
        loc: {
          start: {
            line: 589,
            column: 8
          },
          end: {
            line: 591,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 589,
            column: 8
          },
          end: {
            line: 591,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 589
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0],
      "24": [0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0],
      "30": [0, 0],
      "31": [0, 0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/repositories/MorphemeRepository.ts",
      mappings: "AAAA;;;;;;;;;;GAUG;AAQH,OAAO,EAAE,UAAU,EAAuB,MAAM,oBAAoB,CAAA;AACpE,OAAO,EAAE,aAAa,EAAyB,MAAM,uBAAuB,CAAA;AAoD5E,+EAA+E;AAC/E,QAAQ;AACR,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,kBAAkB;IAC7B,OAAO;IACC,SAAS,GAA0B,IAAI,GAAG,EAAE,CAAA;IAEpD,UAAU;IACF,OAAO,GAOX;QACF,UAAU,EAAE,IAAI,GAAG,EAAE;QACrB,SAAS,EAAE,IAAI,GAAG,EAAE;QACpB,aAAa,EAAE,IAAI,GAAG,EAAE;QACxB,SAAS,EAAE,IAAI,GAAG,EAAE;QACpB,iBAAiB,EAAE,IAAI,GAAG,EAAE;QAC5B,MAAM,EAAE,IAAI,GAAG,EAAE;KAClB,CAAA;IAED,aAAa;IACL,WAAW,GAA4B,IAAI,GAAG,EAAE,CAAA;IAExD,OAAO;IACC,UAAU,CAAY;IACtB,aAAa,CAAe;IAEpC,OAAO;IACC,aAAa,GAAG,KAAK,CAAA;IACrB,cAAc,GAA0B,IAAI,CAAA;IAC5C,KAAK,GAAmC,IAAI,CAAA;IAEpD;;;;;OAKG;IACH,YACE,UAAuB,EACvB,aAA6B;QAE7B,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,IAAI,UAAU,EAAE,CAAA;QAChD,IAAI,CAAC,aAAa,GAAG,aAAa,IAAI,IAAI,aAAa,EAAE,CAAA;IAC3D,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;YAClC,OAAM;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;QAE9B,IAAI,CAAC;YACH,YAAY;YACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAC3B,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBAC3C,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAChF,CAAC;YAED,YAAY;YACZ,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;YAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;YAEpD,aAAa;YACb,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;YAC5B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;YAEzB,sBAAsB;YACtB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;YAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAEvB,YAAY;YACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAA;YAE3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;YAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YACvC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,UAAU,QAAQ,IAAI,CAAC,CAAA;YAEtE,SAAS;YACT,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;YACpC,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;QACzF,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM;QACV,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAE7B,SAAS;QACT,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAE1B,QAAQ;QACR,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;IACzB,CAAC;IAED;;;;OAIG;IACK,wBAAwB;QAC9B,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAM;QAEvB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAC3B,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA;QAC7C,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAC3D,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAE7D,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACtE,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YAChE,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,KAAK,KAAK,KAAK,UAAU,IAAI,CAAC,CAAA;QAC5D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QACzB,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YAChE,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,KAAK,KAAK,KAAK,UAAU,IAAI,CAAC,CAAA;QAC3D,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACK,gBAAgB,CAAC,SAAqB;QAC5C,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QAEtB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;QAC3C,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,CAAA;IACzD,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,YAAY;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAE5B,SAAS;QACT,IAAI,CAAC,YAAY,EAAE,CAAA;QAEnB,UAAU;QACV,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,WAAW;YACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YAErE,aAAa;YACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAA;YAE5E,YAAY;YACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;YAE3E,uBAAuB;YACvB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC,GAAG,EAAE,CAAA;YAClE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAA;YAEhE,wBAAwB;YACxB,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAA;YAC/E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAA;YAE1E,WAAW;YACX,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;YACrD,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QACxC,OAAO,CAAC,GAAG,CAAC,mBAAmB,SAAS,IAAI,CAAC,CAAA;QAE7C,SAAS;QACT,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;IAED;;;;OAIG;IACK,YAAY;QAClB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;QAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;QAClC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QAC9B,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;IAC7B,CAAC;IAED;;;;;;;OAOG;IACK,UAAU,CAAI,KAAyB,EAAE,GAAM,EAAE,QAAkB;QACzE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACpB,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QACpB,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;;;OAQG;IACK,wBAAwB,CAAC,cAAwB;QACvD,oBAAoB;QACpB,MAAM,QAAQ,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAA;QACpE,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAA;QAE1F,kBAAkB;QAClB,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnB,OAAO,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAA;QAC3C,CAAC;aAAM,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YAC1B,OAAO,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAA;QAC7C,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAA;QAC1C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,aAAa;QACnB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QACvB,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,CAAA;QAC3D,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,CAAA;QAC5D,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,CAAA;QAChE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,CAAA;QAC5D,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,MAAM,CAAC,CAAA;QACpE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,CAAA;IACzD,CAAC;IAED;;;;;;OAMG;IACK,gBAAgB;QACtB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QAExB,qBAAqB;QACrB,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YACtE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEpC,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,aAAa,CAAC,CAAA;YACvE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;YACjD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QAC5C,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEpC,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,aAAa,CAAC,CAAA;YACvE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;YACjD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,EAAE,UAAU,CAAC,CAAA;QACxD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,CAAA;IAClE,CAAC;IAED;;;;;;;;OAQG;IACK,gBAAgB,CAAC,OAAiB;QACxC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QACxB,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAA;QACzB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAA;QAE1B,QAAQ;QACR,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QAC9C,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;YACd,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACd,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACb,OAAO;gBACL,IAAI;gBACJ,KAAK;gBACL,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aAC5C,CAAA;QACH,CAAC;QAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAA;QAEvD,iBAAiB;QACjB,MAAM,KAAK,GAAa,EAAE,CAAA;QAC1B,MAAM,KAAK,GAAa,EAAE,CAAA;QAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,IAAI,iBAAiB,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBAC/B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACf,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACf,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5C,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,EAAG,CAAA;YACtB,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,EAAG,CAAA;YAEtB,IAAI,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;YAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YAEZ,iBAAiB,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;YAExE,IAAI,iBAAiB,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBAC/B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACf,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACf,CAAC;QACH,CAAC;QAED,QAAQ;QACR,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC,GAAG,GAAG,CAAA;QAC1B,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC,GAAG,GAAG,CAAA;QAC1B,CAAC;QAED,OAAO;YACL,IAAI;YACJ,KAAK;YACL,MAAM;gBACJ,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA;gBACvC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;gBACvB,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YACnC,CAAC;SACF,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACK,cAAc,CAAC,cAAsB;QAC3C,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;QAEzD,YAAY;QACZ,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;QAC/E,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;QAEnF,QAAQ;QACR,MAAM,UAAU,GAA2B,EAAE,CAAA;QAC7C,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YACtE,UAAU,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAA;QACzC,CAAC;QAED,UAAU;QACV,MAAM,SAAS,GAA2B,EAAE,CAAA;QAC5C,KAAK,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YACpE,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,CAAA;QACvC,CAAC;QAED,SAAS;QACT,MAAM,aAAa,GAA2B,EAAE,CAAA;QAChD,KAAK,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5E,aAAa,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,MAAM,CAAA;QAC/C,CAAC;QAED,IAAI,CAAC,KAAK,GAAG;YACX,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC1B,UAAU;YACV,SAAS;YACT,aAAa;YACb,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5E,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChF,cAAc;YACd,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAA;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,EAAU;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAA0B;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;IACpD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,OAAwB;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA;IAClD,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CAAC,WAAmB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;IAC1D,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,GAAW;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;IAC3C,CAAC;IAED;;;;;OAKG;IACH,qBAAqB,CAAC,OAAe;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,WAAW,CAAC,cAAwB,EAAE,YAAoB,GAAG,EAAE,QAAgB,EAAE;QAC/E,MAAM,OAAO,GAAuB,EAAE,CAAA;QAEtC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,IAAI,QAAQ,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE;gBAAE,SAAQ;YAE/C,MAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CACjD,cAAc,CAAC,eAAe,EAC9B,QAAQ,CAAC,eAAe,CACzB,CAAA;YAED,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAA;YACxC,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,OAAO,OAAO;aACX,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;aAC3C,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACpB,CAAC;IAED;;;;;;;;OAQG;IACH,2BAA2B,CAAC,OAAiB,EAAE,OAAiB;QAC9D,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAA;QAC9B,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,CAAA;QAClB,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,KAAK,GAAG,CAAC,CAAA;QAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,UAAU,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YACrC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YAChC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QAClC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACrD,OAAO,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,SAAS,CAAA;IACrD,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CAAC,QAA0B,EAAE,QAAgB,CAAC;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAEjD,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,OAAO,EAAE,CAAA;QACX,CAAC;QAED,MAAM,OAAO,GAAe,EAAE,CAAA;QAC9B,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAA;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtE,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;YAE/B,SAAS;YACT,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;gBACrE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;YAC7B,CAAC;YAED,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACtB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;QAChC,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;;;OAMG;IACH,eAAe,CAAC,OAAwB,EAAE,QAAgB,CAAC;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,CAAC,CAAA;QAE7D,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxD,OAAO,EAAE,CAAA;QACX,CAAC;QAED,MAAM,OAAO,GAAe,EAAE,CAAA;QAC9B,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAA;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtE,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;YAE/B,SAAS;YACT,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;gBACrE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;YAC7B,CAAC;YAED,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACtB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;QAChC,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB,EAAE,QAAgB;QACnD,MAAM,OAAO,GAAe,EAAE,CAAA;QAE9B,KAAK,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;YAC5B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAwB;QAC7B,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;QAEpD,QAAQ;QACR,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAA;QACvE,CAAC;QAED,UAAU;QACV,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YAC9B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,KAAK,QAAQ,CAAC,gBAAgB,CAAC,CAAA;QACvF,CAAC;QAED,UAAU;QACV,IAAI,QAAQ,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YAC7C,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,IAAI,QAAQ,CAAC,iBAAkB,CAAC,CAAA;QACrF,CAAC;QACD,IAAI,QAAQ,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YAC7C,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,IAAI,QAAQ,CAAC,iBAAkB,CAAC,CAAA;QACrF,CAAC;QAED,QAAQ;QACR,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACjC,QAAQ,CAAC,IAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CACjD,CAAA;QACH,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAC5E,CAAC;QAED,OAAO;QACP,IAAI,QAAQ,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YACzD,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;YAChE,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAA;IAC5C,CAAC;IAED;;;;OAIG;IACH,QAAQ;QACN,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;QACrC,CAAC;QACD,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;IAC1B,CAAC;IAED;;;;OAIG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,cAAc,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,CAAA;IAC3B,CAAC;IAED;;;;OAIG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACH,OAAO;QACL,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAA;QACzB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAE1B,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IAC7B,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/repositories/MorphemeRepository.ts"],
      sourcesContent: ["/**\n * \u8BED\u7D20\u4ED3\u5E93\u7C7B\n *\n * \u8D1F\u8D23\u8BED\u7D20\u6570\u636E\u7684\u52A0\u8F7D\u3001\u7D22\u5F15\u3001\u67E5\u8BE2\u548C\u667A\u80FD\u91C7\u6837\u3002\n * \u652F\u6301\u591A\u7EF4\u5EA6\u7D22\u5F15\u3001\u8BED\u4E49\u76F8\u4F3C\u5EA6\u8BA1\u7B97\u548C\u9AD8\u6548\u7684O(1)\u91C7\u6837\u7B97\u6CD5\u3002\n *\n * @fileoverview \u8BED\u7D20\u6570\u636E\u4ED3\u5E93\u6838\u5FC3\u6A21\u5757\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type {\n  Morpheme,\n  MorphemeCategory,\n  CulturalContext,\n  SampleCriteria\n} from '../../types/core'\nimport { DataLoader, type DataLoadResult } from '../data/DataLoader'\nimport { DataValidator, type ValidationResult } from '../data/DataValidator'\n\n// ============================================================================\n// \u63A5\u53E3\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u8BED\u7D20\u4ED3\u5E93\u7EDF\u8BA1\u4FE1\u606F\u63A5\u53E3\n */\nexport interface MorphemeRepositoryStats {\n  /** \u8BED\u7D20\u603B\u6570 */\n  total: number\n  /** \u6309\u7C7B\u522B\u5206\u5E03 */\n  byCategory: Record<string, number>\n  /** \u6309\u6587\u5316\u8BED\u5883\u5206\u5E03 */\n  byContext: Record<string, number>\n  /** \u6309\u5B50\u5206\u7C7B\u5206\u5E03 */\n  bySubcategory: Record<string, number>\n  /** \u5E73\u5747\u8D28\u91CF\u8BC4\u5206 */\n  avgQuality: number\n  /** \u5E73\u5747\u4F7F\u7528\u9891\u7387 */\n  avgFrequency: number\n  /** \u7D22\u5F15\u6784\u5EFA\u65F6\u95F4 */\n  indexBuildTime: number\n  /** \u6700\u540E\u66F4\u65B0\u65F6\u95F4 */\n  lastUpdated: number\n}\n\n/**\n * \u8BED\u4E49\u76F8\u4F3C\u5EA6\u67E5\u8BE2\u7ED3\u679C\u63A5\u53E3\n */\nexport interface SimilarityResult {\n  /** \u8BED\u7D20 */\n  morpheme: Morpheme\n  /** \u76F8\u4F3C\u5EA6\u5206\u6570 [0-1] */\n  similarity: number\n}\n\n/**\n * Alias Table \u91C7\u6837\u5668\u63A5\u53E3\n *\n * \u5B9E\u73B0O(1)\u65F6\u95F4\u590D\u6742\u5EA6\u7684\u52A0\u6743\u968F\u673A\u91C7\u6837\n */\ninterface AliasTable {\n  /** \u6982\u7387\u6570\u7EC4 */\n  prob: number[]\n  /** \u522B\u540D\u6570\u7EC4 */\n  alias: number[]\n  /** \u91C7\u6837\u65B9\u6CD5 */\n  sample(): number\n}\n\n// ============================================================================\n// \u8BED\u7D20\u4ED3\u5E93\u7C7B\n// ============================================================================\n\n/**\n * \u8BED\u7D20\u4ED3\u5E93\u7C7B\n *\n * \u63D0\u4F9B\u9AD8\u6548\u7684\u8BED\u7D20\u6570\u636E\u7BA1\u7406\u548C\u67E5\u8BE2\u529F\u80FD\n */\nexport class MorphemeRepository {\n  // \u6570\u636E\u5B58\u50A8\n  private morphemes: Map<string, Morpheme> = new Map()\n\n  // \u591A\u7EF4\u5EA6\u7D22\u5F15\u7CFB\u7EDF\n  private indices: {\n    byCategory: Map<MorphemeCategory, Morpheme[]>\n    byContext: Map<CulturalContext, Morpheme[]>\n    bySubcategory: Map<string, Morpheme[]>\n    byQuality: Map<number, Morpheme[]>\n    bySemanticCluster: Map<string, Morpheme[]>\n    byTags: Map<string, Morpheme[]>\n  } = {\n    byCategory: new Map(),\n    byContext: new Map(),\n    bySubcategory: new Map(),\n    byQuality: new Map(),\n    bySemanticCluster: new Map(),\n    byTags: new Map()\n  }\n\n  // O(1)\u91C7\u6837\u7B97\u6CD5\u652F\u6301\n  private aliasTables: Map<string, AliasTable> = new Map()\n\n  // \u7EC4\u4EF6\u4F9D\u8D56\n  private dataLoader: DataLoader\n  private dataValidator: DataValidator\n\n  // \u72B6\u6001\u7BA1\u7406\n  private isInitialized = false\n  private lastLoadResult: DataLoadResult | null = null\n  private stats: MorphemeRepositoryStats | null = null\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   *\n   * @param dataLoader \u6570\u636E\u52A0\u8F7D\u5668\u5B9E\u4F8B\n   * @param dataValidator \u6570\u636E\u9A8C\u8BC1\u5668\u5B9E\u4F8B\n   */\n  constructor(\n    dataLoader?: DataLoader,\n    dataValidator?: DataValidator\n  ) {\n    this.dataLoader = dataLoader || new DataLoader()\n    this.dataValidator = dataValidator || new DataValidator()\n  }\n\n  /**\n   * \u521D\u59CB\u5316\u8BED\u7D20\u4ED3\u5E93\n   *\n   * \u52A0\u8F7D\u6570\u636E\u3001\u9A8C\u8BC1\u5B8C\u6574\u6027\u3001\u6784\u5EFA\u7D22\u5F15\u548C\u91C7\u6837\u8868\n   *\n   * @returns Promise<void>\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('\uD83D\uDCCB \u8BED\u7D20\u4ED3\u5E93\u5DF2\u521D\u59CB\u5316\uFF0C\u8DF3\u8FC7\u91CD\u590D\u521D\u59CB\u5316')\n      return\n    }\n\n    const startTime = Date.now()\n    console.log('\uD83D\uDE80 \u5F00\u59CB\u521D\u59CB\u5316\u8BED\u7D20\u4ED3\u5E93...')\n\n    try {\n      // 1. \u52A0\u8F7D\u8BED\u7D20\u6570\u636E\n      console.log('\uD83D\uDCC1 \u52A0\u8F7D\u8BED\u7D20\u6570\u636E...')\n      this.lastLoadResult = await this.dataLoader.loadAll()\n\n      if (!this.lastLoadResult.validation.passed) {\n        throw new Error(`\u6570\u636E\u9A8C\u8BC1\u5931\u8D25: ${this.lastLoadResult.validation.errors.join(', ')}`)\n      }\n\n      // 2. \u6784\u5EFA\u8BED\u7D20\u6620\u5C04\n      console.log('\uD83D\uDDC2\uFE0F \u6784\u5EFA\u8BED\u7D20\u6620\u5C04...')\n      this.buildMorphemeMap(this.lastLoadResult.morphemes)\n\n      // 3. \u6784\u5EFA\u591A\u7EF4\u5EA6\u7D22\u5F15\n      console.log('\uD83D\uDCCA \u6784\u5EFA\u591A\u7EF4\u5EA6\u7D22\u5F15...')\n      await this.buildIndices()\n\n      // 4. \u6784\u5EFAAlias Table\u91C7\u6837\u8868\n      console.log('\uD83C\uDFB2 \u6784\u5EFA\u91C7\u6837\u8868...')\n      this.buildAliasTables()\n\n      // 5. \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F\n      console.log('\uD83D\uDCC8 \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F...')\n      this.calculateStats(Date.now() - startTime)\n\n      this.isInitialized = true\n\n      const initTime = Date.now() - startTime\n      console.log(`\u2705 \u8BED\u7D20\u4ED3\u5E93\u521D\u59CB\u5316\u5B8C\u6210: ${this.morphemes.size}\u4E2A\u8BED\u7D20, \u8017\u65F6${initTime}ms`)\n\n      // \u8F93\u51FA\u7EDF\u8BA1\u6458\u8981\n      this.logInitializationSummary()\n\n    } catch (error) {\n      console.error('\u274C \u8BED\u7D20\u4ED3\u5E93\u521D\u59CB\u5316\u5931\u8D25:', error)\n      throw new Error(`\u8BED\u7D20\u4ED3\u5E93\u521D\u59CB\u5316\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * \u91CD\u65B0\u52A0\u8F7D\u6570\u636E\n   *\n   * \u7528\u4E8E\u70ED\u91CD\u8F7D\u6216\u6570\u636E\u66F4\u65B0\u573A\u666F\n   *\n   * @returns Promise<void>\n   */\n  async reload(): Promise<void> {\n    console.log('\uD83D\uDD04 \u91CD\u65B0\u52A0\u8F7D\u8BED\u7D20\u6570\u636E...')\n\n    // \u6E05\u7A7A\u73B0\u6709\u6570\u636E\n    this.morphemes.clear()\n    this.clearIndices()\n    this.aliasTables.clear()\n    this.isInitialized = false\n\n    // \u91CD\u65B0\u521D\u59CB\u5316\n    await this.initialize()\n  }\n\n  /**\n   * \u8F93\u51FA\u521D\u59CB\u5316\u6458\u8981\n   *\n   * @private\n   */\n  private logInitializationSummary(): void {\n    if (!this.stats) return\n\n    console.log('\uD83D\uDCCA \u8BED\u7D20\u4ED3\u5E93\u7EDF\u8BA1\u6458\u8981:')\n    console.log(`   \u603B\u8BA1: ${this.stats.total} \u4E2A\u8BED\u7D20`)\n    console.log(`   \u5E73\u5747\u8D28\u91CF: ${this.stats.avgQuality.toFixed(3)}`)\n    console.log(`   \u5E73\u5747\u9891\u7387: ${this.stats.avgFrequency.toFixed(3)}`)\n\n    console.log('   \u7C7B\u522B\u5206\u5E03:')\n    for (const [category, count] of Object.entries(this.stats.byCategory)) {\n      const percentage = ((count / this.stats.total) * 100).toFixed(1)\n      console.log(`     ${category}: ${count} (${percentage}%)`)\n    }\n\n    console.log('   \u6587\u5316\u8BED\u5883\u5206\u5E03:')\n    for (const [context, count] of Object.entries(this.stats.byContext)) {\n      const percentage = ((count / this.stats.total) * 100).toFixed(1)\n      console.log(`     ${context}: ${count} (${percentage}%)`)\n    }\n  }\n\n  /**\n   * \u6784\u5EFA\u8BED\u7D20\u6620\u5C04\n   *\n   * \u5C06\u8BED\u7D20\u6570\u7EC4\u8F6C\u6362\u4E3A\u9AD8\u6548\u7684Map\u7ED3\u6784\n   *\n   * @private\n   * @param morphemes \u8BED\u7D20\u6570\u7EC4\n   */\n  private buildMorphemeMap(morphemes: Morpheme[]): void {\n    this.morphemes.clear()\n\n    for (const morpheme of morphemes) {\n      this.morphemes.set(morpheme.id, morpheme)\n    }\n\n    console.log(`\uD83D\uDDC2\uFE0F \u6784\u5EFA\u8BED\u7D20\u6620\u5C04\u5B8C\u6210: ${this.morphemes.size} \u4E2A\u8BED\u7D20`)\n  }\n\n  /**\n   * \u6784\u5EFA\u591A\u7EF4\u5EA6\u7D22\u5F15\n   *\n   * \u4E3A\u9AD8\u6548\u67E5\u8BE2\u6784\u5EFA\u5404\u79CD\u7D22\u5F15\u7ED3\u6784\n   *\n   * @private\n   * @returns Promise<void>\n   */\n  private async buildIndices(): Promise<void> {\n    const startTime = Date.now()\n\n    // \u6E05\u7A7A\u73B0\u6709\u7D22\u5F15\n    this.clearIndices()\n\n    // \u6784\u5EFA\u5404\u7EF4\u5EA6\u7D22\u5F15\n    for (const morpheme of this.morphemes.values()) {\n      // 1. \u6309\u7C7B\u522B\u7D22\u5F15\n      this.addToIndex(this.indices.byCategory, morpheme.category, morpheme)\n\n      // 2. \u6309\u6587\u5316\u8BED\u5883\u7D22\u5F15\n      this.addToIndex(this.indices.byContext, morpheme.cultural_context, morpheme)\n\n      // 3. \u6309\u5B50\u5206\u7C7B\u7D22\u5F15\n      this.addToIndex(this.indices.bySubcategory, morpheme.subcategory, morpheme)\n\n      // 4. \u6309\u8D28\u91CF\u5206\u6570\u7D22\u5F15\uFF08\u5206\u6876\uFF1A0.1\u7CBE\u5EA6\uFF09\n      const qualityBucket = Math.round(morpheme.quality_score * 10) / 10\n      this.addToIndex(this.indices.byQuality, qualityBucket, morpheme)\n\n      // 5. \u6309\u8BED\u4E49\u805A\u7C7B\u7D22\u5F15\uFF08\u57FA\u4E8E\u8BED\u4E49\u5411\u91CF\u7684\u805A\u7C7B\uFF09\n      const semanticCluster = this.calculateSemanticCluster(morpheme.semantic_vector)\n      this.addToIndex(this.indices.bySemanticCluster, semanticCluster, morpheme)\n\n      // 6. \u6309\u6807\u7B7E\u7D22\u5F15\n      for (const tag of morpheme.tags) {\n        this.addToIndex(this.indices.byTags, tag, morpheme)\n      }\n    }\n\n    const indexTime = Date.now() - startTime\n    console.log(`\uD83D\uDCCA \u591A\u7EF4\u5EA6\u7D22\u5F15\u6784\u5EFA\u5B8C\u6210: \u8017\u65F6${indexTime}ms`)\n\n    // \u8F93\u51FA\u7D22\u5F15\u7EDF\u8BA1\n    this.logIndexStats()\n  }\n\n  /**\n   * \u6E05\u7A7A\u6240\u6709\u7D22\u5F15\n   *\n   * @private\n   */\n  private clearIndices(): void {\n    this.indices.byCategory.clear()\n    this.indices.byContext.clear()\n    this.indices.bySubcategory.clear()\n    this.indices.byQuality.clear()\n    this.indices.bySemanticCluster.clear()\n    this.indices.byTags.clear()\n  }\n\n  /**\n   * \u6DFB\u52A0\u5230\u7D22\u5F15\n   *\n   * @private\n   * @param index \u7D22\u5F15Map\n   * @param key \u7D22\u5F15\u952E\n   * @param morpheme \u8BED\u7D20\n   */\n  private addToIndex<K>(index: Map<K, Morpheme[]>, key: K, morpheme: Morpheme): void {\n    if (!index.has(key)) {\n      index.set(key, [])\n    }\n    index.get(key)!.push(morpheme)\n  }\n\n  /**\n   * \u8BA1\u7B97\u8BED\u4E49\u805A\u7C7B\n   *\n   * \u57FA\u4E8E\u8BED\u4E49\u5411\u91CF\u8BA1\u7B97\u805A\u7C7B\u6807\u8BC6\n   *\n   * @private\n   * @param semanticVector \u8BED\u4E49\u5411\u91CF\n   * @returns \u805A\u7C7B\u6807\u8BC6\n   */\n  private calculateSemanticCluster(semanticVector: number[]): string {\n    // \u7B80\u5316\u7684\u805A\u7C7B\u7B97\u6CD5\uFF1A\u57FA\u4E8E\u5411\u91CF\u7684\u4E3B\u8981\u7EF4\u5EA6\n    const maxIndex = semanticVector.indexOf(Math.max(...semanticVector))\n    const avgValue = semanticVector.reduce((sum, val) => sum + val, 0) / semanticVector.length\n\n    // \u6839\u636E\u6700\u5927\u503C\u7D22\u5F15\u548C\u5E73\u5747\u503C\u786E\u5B9A\u805A\u7C7B\n    if (avgValue > 0.7) {\n      return `high_${Math.floor(maxIndex / 5)}`\n    } else if (avgValue > 0.4) {\n      return `medium_${Math.floor(maxIndex / 5)}`\n    } else {\n      return `low_${Math.floor(maxIndex / 5)}`\n    }\n  }\n\n  /**\n   * \u8F93\u51FA\u7D22\u5F15\u7EDF\u8BA1\u4FE1\u606F\n   *\n   * @private\n   */\n  private logIndexStats(): void {\n    console.log('\uD83D\uDCCA \u7D22\u5F15\u7EDF\u8BA1:')\n    console.log(`   \u7C7B\u522B\u7D22\u5F15: ${this.indices.byCategory.size} \u4E2A\u7C7B\u522B`)\n    console.log(`   \u6587\u5316\u8BED\u5883\u7D22\u5F15: ${this.indices.byContext.size} \u4E2A\u8BED\u5883`)\n    console.log(`   \u5B50\u5206\u7C7B\u7D22\u5F15: ${this.indices.bySubcategory.size} \u4E2A\u5B50\u5206\u7C7B`)\n    console.log(`   \u8D28\u91CF\u5206\u6876\u7D22\u5F15: ${this.indices.byQuality.size} \u4E2A\u5206\u6876`)\n    console.log(`   \u8BED\u4E49\u805A\u7C7B\u7D22\u5F15: ${this.indices.bySemanticCluster.size} \u4E2A\u805A\u7C7B`)\n    console.log(`   \u6807\u7B7E\u7D22\u5F15: ${this.indices.byTags.size} \u4E2A\u6807\u7B7E`)\n  }\n\n  /**\n   * \u6784\u5EFAAlias Table\u91C7\u6837\u8868\n   *\n   * \u4E3A\u6BCF\u4E2A\u7C7B\u522B\u6784\u5EFAO(1)\u65F6\u95F4\u590D\u6742\u5EA6\u7684\u52A0\u6743\u91C7\u6837\u8868\n   *\n   * @private\n   */\n  private buildAliasTables(): void {\n    this.aliasTables.clear()\n\n    // \u4E3A\u6BCF\u4E2A\u7C7B\u522B\u6784\u5EFAAlias Table\n    for (const [category, morphemes] of this.indices.byCategory.entries()) {\n      if (morphemes.length === 0) continue\n\n      const weights = morphemes.map(m => m.usage_frequency * m.quality_score)\n      const aliasTable = this.createAliasTable(weights)\n      this.aliasTables.set(category, aliasTable)\n    }\n\n    // \u4E3A\u6BCF\u4E2A\u6587\u5316\u8BED\u5883\u6784\u5EFAAlias Table\n    for (const [context, morphemes] of this.indices.byContext.entries()) {\n      if (morphemes.length === 0) continue\n\n      const weights = morphemes.map(m => m.usage_frequency * m.quality_score)\n      const aliasTable = this.createAliasTable(weights)\n      this.aliasTables.set(`context_${context}`, aliasTable)\n    }\n\n    console.log(`\uD83C\uDFB2 Alias Table\u6784\u5EFA\u5B8C\u6210: ${this.aliasTables.size} \u4E2A\u91C7\u6837\u8868`)\n  }\n\n  /**\n   * \u521B\u5EFAAlias Table\n   *\n   * \u5B9E\u73B0Walker's Alias Method\u7B97\u6CD5\n   *\n   * @private\n   * @param weights \u6743\u91CD\u6570\u7EC4\n   * @returns Alias Table\n   */\n  private createAliasTable(weights: number[]): AliasTable {\n    const n = weights.length\n    const prob = new Array(n)\n    const alias = new Array(n)\n\n    // \u5F52\u4E00\u5316\u6743\u91CD\n    const sum = weights.reduce((a, b) => a + b, 0)\n    if (sum === 0) {\n      // \u5982\u679C\u6240\u6709\u6743\u91CD\u90FD\u4E3A0\uFF0C\u4F7F\u7528\u5747\u5300\u5206\u5E03\n      prob.fill(1.0)\n      alias.fill(0)\n      return {\n        prob,\n        alias,\n        sample: () => Math.floor(Math.random() * n)\n      }\n    }\n\n    const normalizedWeights = weights.map(w => w * n / sum)\n\n    // \u5206\u79BB\u5C0F\u4E8E1\u548C\u5927\u4E8E\u7B49\u4E8E1\u7684\u6743\u91CD\n    const small: number[] = []\n    const large: number[] = []\n\n    for (let i = 0; i < n; i++) {\n      if (normalizedWeights[i] < 1.0) {\n        small.push(i)\n      } else {\n        large.push(i)\n      }\n    }\n\n    // \u6784\u5EFAAlias Table\n    while (small.length > 0 && large.length > 0) {\n      const l = small.pop()!\n      const g = large.pop()!\n\n      prob[l] = normalizedWeights[l]\n      alias[l] = g\n\n      normalizedWeights[g] = normalizedWeights[g] + normalizedWeights[l] - 1.0\n\n      if (normalizedWeights[g] < 1.0) {\n        small.push(g)\n      } else {\n        large.push(g)\n      }\n    }\n\n    // \u5904\u7406\u5269\u4F59\u9879\n    while (large.length > 0) {\n      prob[large.pop()!] = 1.0\n    }\n\n    while (small.length > 0) {\n      prob[small.pop()!] = 1.0\n    }\n\n    return {\n      prob,\n      alias,\n      sample(): number {\n        const i = Math.floor(Math.random() * n)\n        const r = Math.random()\n        return r < prob[i] ? i : alias[i]\n      }\n    }\n  }\n\n  /**\n   * \u8BA1\u7B97\u7EDF\u8BA1\u4FE1\u606F\n   *\n   * @private\n   * @param indexBuildTime \u7D22\u5F15\u6784\u5EFA\u65F6\u95F4\n   */\n  private calculateStats(indexBuildTime: number): void {\n    const morphemeArray = Array.from(this.morphemes.values())\n\n    // \u8BA1\u7B97\u5E73\u5747\u8D28\u91CF\u548C\u9891\u7387\n    const totalQuality = morphemeArray.reduce((sum, m) => sum + m.quality_score, 0)\n    const totalFrequency = morphemeArray.reduce((sum, m) => sum + m.usage_frequency, 0)\n\n    // \u6309\u7C7B\u522B\u7EDF\u8BA1\n    const byCategory: Record<string, number> = {}\n    for (const [category, morphemes] of this.indices.byCategory.entries()) {\n      byCategory[category] = morphemes.length\n    }\n\n    // \u6309\u6587\u5316\u8BED\u5883\u7EDF\u8BA1\n    const byContext: Record<string, number> = {}\n    for (const [context, morphemes] of this.indices.byContext.entries()) {\n      byContext[context] = morphemes.length\n    }\n\n    // \u6309\u5B50\u5206\u7C7B\u7EDF\u8BA1\n    const bySubcategory: Record<string, number> = {}\n    for (const [subcategory, morphemes] of this.indices.bySubcategory.entries()) {\n      bySubcategory[subcategory] = morphemes.length\n    }\n\n    this.stats = {\n      total: this.morphemes.size,\n      byCategory,\n      byContext,\n      bySubcategory,\n      avgQuality: this.morphemes.size > 0 ? totalQuality / this.morphemes.size : 0,\n      avgFrequency: this.morphemes.size > 0 ? totalFrequency / this.morphemes.size : 0,\n      indexBuildTime,\n      lastUpdated: Date.now()\n    }\n  }\n\n  /**\n   * \u6839\u636EID\u83B7\u53D6\u8BED\u7D20\n   */\n  findById(id: string): Morpheme | undefined {\n    return this.morphemes.get(id)\n  }\n\n  /**\n   * \u6839\u636E\u7C7B\u522B\u83B7\u53D6\u8BED\u7D20\n   */\n  findByCategory(category: MorphemeCategory): Morpheme[] {\n    return this.indices.byCategory.get(category) || []\n  }\n\n  /**\n   * \u6839\u636E\u6587\u5316\u8BED\u5883\u83B7\u53D6\u8BED\u7D20\n   */\n  findByContext(context: CulturalContext): Morpheme[] {\n    return this.indices.byContext.get(context) || []\n  }\n\n  /**\n   * \u6839\u636E\u5B50\u5206\u7C7B\u83B7\u53D6\u8BED\u7D20\n   *\n   * @param subcategory \u5B50\u5206\u7C7B\n   * @returns \u8BED\u7D20\u6570\u7EC4\n   */\n  findBySubcategory(subcategory: string): Morpheme[] {\n    return this.indices.bySubcategory.get(subcategory) || []\n  }\n\n  /**\n   * \u6839\u636E\u6807\u7B7E\u83B7\u53D6\u8BED\u7D20\n   *\n   * @param tag \u6807\u7B7E\n   * @returns \u8BED\u7D20\u6570\u7EC4\n   */\n  findByTag(tag: string): Morpheme[] {\n    return this.indices.byTags.get(tag) || []\n  }\n\n  /**\n   * \u6839\u636E\u8BED\u4E49\u805A\u7C7B\u83B7\u53D6\u8BED\u7D20\n   *\n   * @param cluster \u805A\u7C7B\u6807\u8BC6\n   * @returns \u8BED\u7D20\u6570\u7EC4\n   */\n  findBySemanticCluster(cluster: string): Morpheme[] {\n    return this.indices.bySemanticCluster.get(cluster) || []\n  }\n\n  /**\n   * \u67E5\u627E\u8BED\u4E49\u76F8\u4F3C\u7684\u8BED\u7D20\n   *\n   * @param targetMorpheme \u76EE\u6807\u8BED\u7D20\n   * @param threshold \u76F8\u4F3C\u5EA6\u9608\u503C [0-1]\n   * @param limit \u8FD4\u56DE\u6570\u91CF\u9650\u5236\n   * @returns \u76F8\u4F3C\u5EA6\u7ED3\u679C\u6570\u7EC4\n   */\n  findSimilar(targetMorpheme: Morpheme, threshold: number = 0.7, limit: number = 10): SimilarityResult[] {\n    const results: SimilarityResult[] = []\n\n    for (const morpheme of this.morphemes.values()) {\n      if (morpheme.id === targetMorpheme.id) continue\n\n      const similarity = this.calculateSemanticSimilarity(\n        targetMorpheme.semantic_vector,\n        morpheme.semantic_vector\n      )\n\n      if (similarity >= threshold) {\n        results.push({ morpheme, similarity })\n      }\n    }\n\n    // \u6309\u76F8\u4F3C\u5EA6\u964D\u5E8F\u6392\u5E8F\u5E76\u9650\u5236\u6570\u91CF\n    return results\n      .sort((a, b) => b.similarity - a.similarity)\n      .slice(0, limit)\n  }\n\n  /**\n   * \u8BA1\u7B97\u8BED\u4E49\u76F8\u4F3C\u5EA6\n   *\n   * \u4F7F\u7528\u4F59\u5F26\u76F8\u4F3C\u5EA6\u7B97\u6CD5\n   *\n   * @param vector1 \u5411\u91CF1\n   * @param vector2 \u5411\u91CF2\n   * @returns \u76F8\u4F3C\u5EA6 [0-1]\n   */\n  calculateSemanticSimilarity(vector1: number[], vector2: number[]): number {\n    if (vector1.length !== vector2.length) {\n      throw new Error('\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6\u4E0D\u5339\u914D')\n    }\n\n    let dotProduct = 0\n    let norm1 = 0\n    let norm2 = 0\n\n    for (let i = 0; i < vector1.length; i++) {\n      dotProduct += vector1[i] * vector2[i]\n      norm1 += vector1[i] * vector1[i]\n      norm2 += vector2[i] * vector2[i]\n    }\n\n    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)\n    return magnitude === 0 ? 0 : dotProduct / magnitude\n  }\n\n  /**\n   * \u4F7F\u7528Alias Table\u8FDB\u884C\u9AD8\u6548\u91C7\u6837\n   *\n   * @param category \u7C7B\u522B\n   * @param count \u91C7\u6837\u6570\u91CF\n   * @returns \u91C7\u6837\u7ED3\u679C\n   */\n  sampleByCategory(category: MorphemeCategory, count: number = 1): Morpheme[] {\n    const morphemes = this.indices.byCategory.get(category)\n    const aliasTable = this.aliasTables.get(category)\n\n    if (!morphemes || !aliasTable || morphemes.length === 0) {\n      return []\n    }\n\n    const results: Morpheme[] = []\n    const usedIndices = new Set<number>()\n\n    for (let i = 0; i < count && usedIndices.size < morphemes.length; i++) {\n      let index = aliasTable.sample()\n\n      // \u907F\u514D\u91CD\u590D\u91C7\u6837\n      while (usedIndices.has(index) && usedIndices.size < morphemes.length) {\n        index = aliasTable.sample()\n      }\n\n      usedIndices.add(index)\n      results.push(morphemes[index])\n    }\n\n    return results\n  }\n\n  /**\n   * \u4F7F\u7528Alias Table\u6309\u6587\u5316\u8BED\u5883\u91C7\u6837\n   *\n   * @param context \u6587\u5316\u8BED\u5883\n   * @param count \u91C7\u6837\u6570\u91CF\n   * @returns \u91C7\u6837\u7ED3\u679C\n   */\n  sampleByContext(context: CulturalContext, count: number = 1): Morpheme[] {\n    const morphemes = this.indices.byContext.get(context)\n    const aliasTable = this.aliasTables.get(`context_${context}`)\n\n    if (!morphemes || !aliasTable || morphemes.length === 0) {\n      return []\n    }\n\n    const results: Morpheme[] = []\n    const usedIndices = new Set<number>()\n\n    for (let i = 0; i < count && usedIndices.size < morphemes.length; i++) {\n      let index = aliasTable.sample()\n\n      // \u907F\u514D\u91CD\u590D\u91C7\u6837\n      while (usedIndices.has(index) && usedIndices.size < morphemes.length) {\n        index = aliasTable.sample()\n      }\n\n      usedIndices.add(index)\n      results.push(morphemes[index])\n    }\n\n    return results\n  }\n\n  /**\n   * \u6839\u636E\u8D28\u91CF\u5206\u6570\u8303\u56F4\u83B7\u53D6\u8BED\u7D20\n   */\n  findByQualityRange(minScore: number, maxScore: number): Morpheme[] {\n    const results: Morpheme[] = []\n\n    for (const [score, morphemes] of this.indices.byQuality.entries()) {\n      if (score >= minScore && score <= maxScore) {\n        results.push(...morphemes)\n      }\n    }\n\n    return results\n  }\n\n  /**\n   * \u6839\u636E\u6761\u4EF6\u91C7\u6837\u8BED\u7D20\n   */\n  sample(criteria: SampleCriteria): Morpheme[] {\n    let candidates = Array.from(this.morphemes.values())\n\n    // \u6309\u7C7B\u522B\u8FC7\u6EE4\n    if (criteria.category) {\n      candidates = candidates.filter(m => m.category === criteria.category)\n    }\n\n    // \u6309\u6587\u5316\u8BED\u5883\u8FC7\u6EE4\n    if (criteria.cultural_context) {\n      candidates = candidates.filter(m => m.cultural_context === criteria.cultural_context)\n    }\n\n    // \u6309\u8D28\u91CF\u5206\u6570\u8FC7\u6EE4\n    if (criteria.min_quality_score !== undefined) {\n      candidates = candidates.filter(m => m.quality_score >= criteria.min_quality_score!)\n    }\n    if (criteria.max_quality_score !== undefined) {\n      candidates = candidates.filter(m => m.quality_score <= criteria.max_quality_score!)\n    }\n\n    // \u6309\u6807\u7B7E\u8FC7\u6EE4\n    if (criteria.tags && criteria.tags.length > 0) {\n      candidates = candidates.filter(m =>\n        criteria.tags!.some(tag => m.tags.includes(tag))\n      )\n    }\n\n    // \u6392\u9664\u6307\u5B9AID\n    if (criteria.exclude_ids && criteria.exclude_ids.length > 0) {\n      candidates = candidates.filter(m => !criteria.exclude_ids!.includes(m.id))\n    }\n\n    // \u968F\u673A\u91C7\u6837\n    if (criteria.limit && candidates.length > criteria.limit) {\n      // \u7B80\u5355\u7684\u968F\u673A\u91C7\u6837\uFF0C\u5B9E\u9645\u5B9E\u73B0\u4E2D\u53EF\u4EE5\u4F7F\u7528\u66F4\u590D\u6742\u7684\u91C7\u6837\u7B97\u6CD5\n      const shuffled = [...candidates].sort(() => Math.random() - 0.5)\n      candidates = shuffled.slice(0, criteria.limit)\n    }\n\n    return candidates\n  }\n\n  /**\n   * \u83B7\u53D6\u6240\u6709\u8BED\u7D20\n   */\n  getAll(): Morpheme[] {\n    return Array.from(this.morphemes.values())\n  }\n\n  /**\n   * \u83B7\u53D6\u4ED3\u5E93\u7EDF\u8BA1\u4FE1\u606F\n   *\n   * @returns \u8BE6\u7EC6\u7EDF\u8BA1\u4FE1\u606F\n   */\n  getStats(): MorphemeRepositoryStats {\n    if (!this.stats) {\n      throw new Error('\u8BED\u7D20\u4ED3\u5E93\u672A\u521D\u59CB\u5316\u6216\u7EDF\u8BA1\u4FE1\u606F\u4E0D\u53EF\u7528')\n    }\n    return { ...this.stats }\n  }\n\n  /**\n   * \u83B7\u53D6\u6570\u636E\u52A0\u8F7D\u7ED3\u679C\n   *\n   * @returns \u6700\u540E\u4E00\u6B21\u6570\u636E\u52A0\u8F7D\u7ED3\u679C\n   */\n  getLastLoadResult(): DataLoadResult | null {\n    return this.lastLoadResult\n  }\n\n  /**\n   * \u68C0\u67E5\u662F\u5426\u5DF2\u521D\u59CB\u5316\n   *\n   * @returns \u521D\u59CB\u5316\u72B6\u6001\n   */\n  isReady(): boolean {\n    return this.isInitialized\n  }\n\n  /**\n   * \u83B7\u53D6\u8BED\u7D20\u603B\u6570\n   *\n   * @returns \u8BED\u7D20\u6570\u91CF\n   */\n  getCount(): number {\n    return this.morphemes.size\n  }\n\n  /**\n   * \u6E05\u7406\u8D44\u6E90\n   *\n   * \u6E05\u7406\u6570\u636E\u52A0\u8F7D\u5668\u548C\u9A8C\u8BC1\u5668\u8D44\u6E90\n   */\n  destroy(): void {\n    this.dataLoader.destroy()\n    this.morphemes.clear()\n    this.clearIndices()\n    this.aliasTables.clear()\n    this.isInitialized = false\n    this.stats = null\n    this.lastLoadResult = null\n\n    console.log('\uD83E\uDDF9 \u8BED\u7D20\u4ED3\u5E93\u8D44\u6E90\u5DF2\u6E05\u7406')\n  }\n}"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "29163e5fd18a5c51877527936469caa63ab11bf4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1b7zfbkfuu = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1b7zfbkfuu();
/**
 * 语素仓库类
 *
 * 负责语素数据的加载、索引、查询和智能采样。
 * 支持多维度索引、语义相似度计算和高效的O(1)采样算法。
 *
 * @fileoverview 语素数据仓库核心模块
 * @version 2.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */
import { DataLoader } from '../data/DataLoader';
import { DataValidator } from '../data/DataValidator';
// ============================================================================
// 语素仓库类
// ============================================================================
/**
 * 语素仓库类
 *
 * 提供高效的语素数据管理和查询功能
 */
export class MorphemeRepository {
  // 数据存储
  morphemes =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().s[0]++, new Map());
  // 多维度索引系统
  indices =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().s[1]++, {
    byCategory: new Map(),
    byContext: new Map(),
    bySubcategory: new Map(),
    byQuality: new Map(),
    bySemanticCluster: new Map(),
    byTags: new Map()
  });
  // O(1)采样算法支持
  aliasTables =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().s[2]++, new Map());
  // 组件依赖
  dataLoader;
  dataValidator;
  // 状态管理
  isInitialized =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().s[3]++, false);
  lastLoadResult =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().s[4]++, null);
  stats =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().s[5]++, null);
  /**
   * 构造函数
   *
   * @param dataLoader 数据加载器实例
   * @param dataValidator 数据验证器实例
   */
  constructor(dataLoader, dataValidator) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[0]++;
    cov_1b7zfbkfuu().s[6]++;
    this.dataLoader =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[0][0]++, dataLoader) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[0][1]++, new DataLoader());
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[7]++;
    this.dataValidator =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[1][0]++, dataValidator) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[1][1]++, new DataValidator());
  }
  /**
   * 初始化语素仓库
   *
   * 加载数据、验证完整性、构建索引和采样表
   *
   * @returns Promise<void>
   */
  async initialize() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[1]++;
    cov_1b7zfbkfuu().s[8]++;
    if (this.isInitialized) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[2][0]++;
      cov_1b7zfbkfuu().s[9]++;
      console.log('📋 语素仓库已初始化，跳过重复初始化');
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[10]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[2][1]++;
    }
    const startTime =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[11]++, Date.now());
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[12]++;
    console.log('🚀 开始初始化语素仓库...');
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[13]++;
    try {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[14]++;
      // 1. 加载语素数据
      console.log('📁 加载语素数据...');
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[15]++;
      this.lastLoadResult = await this.dataLoader.loadAll();
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[16]++;
      if (!this.lastLoadResult.validation.passed) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[3][0]++;
        cov_1b7zfbkfuu().s[17]++;
        throw new Error(`数据验证失败: ${this.lastLoadResult.validation.errors.join(', ')}`);
      } else
      /* istanbul ignore next */
      {
        cov_1b7zfbkfuu().b[3][1]++;
      }
      // 2. 构建语素映射
      cov_1b7zfbkfuu().s[18]++;
      console.log('🗂️ 构建语素映射...');
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[19]++;
      this.buildMorphemeMap(this.lastLoadResult.morphemes);
      // 3. 构建多维度索引
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[20]++;
      console.log('📊 构建多维度索引...');
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[21]++;
      await this.buildIndices();
      // 4. 构建Alias Table采样表
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[22]++;
      console.log('🎲 构建采样表...');
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[23]++;
      this.buildAliasTables();
      // 5. 计算统计信息
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[24]++;
      console.log('📈 计算统计信息...');
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[25]++;
      this.calculateStats(Date.now() - startTime);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[26]++;
      this.isInitialized = true;
      const initTime =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[27]++, Date.now() - startTime);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[28]++;
      console.log(`✅ 语素仓库初始化完成: ${this.morphemes.size}个语素, 耗时${initTime}ms`);
      // 输出统计摘要
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[29]++;
      this.logInitializationSummary();
    } catch (error) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[30]++;
      console.error('❌ 语素仓库初始化失败:', error);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[31]++;
      throw new Error(`语素仓库初始化失败: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[4][0]++, error.message) :
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[4][1]++, String(error))}`);
    }
  }
  /**
   * 重新加载数据
   *
   * 用于热重载或数据更新场景
   *
   * @returns Promise<void>
   */
  async reload() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[2]++;
    cov_1b7zfbkfuu().s[32]++;
    console.log('🔄 重新加载语素数据...');
    // 清空现有数据
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[33]++;
    this.morphemes.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[34]++;
    this.clearIndices();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[35]++;
    this.aliasTables.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[36]++;
    this.isInitialized = false;
    // 重新初始化
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[37]++;
    await this.initialize();
  }
  /**
   * 输出初始化摘要
   *
   * @private
   */
  logInitializationSummary() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[3]++;
    cov_1b7zfbkfuu().s[38]++;
    if (!this.stats) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[5][0]++;
      cov_1b7zfbkfuu().s[39]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[5][1]++;
    }
    cov_1b7zfbkfuu().s[40]++;
    console.log('📊 语素仓库统计摘要:');
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[41]++;
    console.log(`   总计: ${this.stats.total} 个语素`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[42]++;
    console.log(`   平均质量: ${this.stats.avgQuality.toFixed(3)}`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[43]++;
    console.log(`   平均频率: ${this.stats.avgFrequency.toFixed(3)}`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[44]++;
    console.log('   类别分布:');
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[45]++;
    for (const [category, count] of Object.entries(this.stats.byCategory)) {
      const percentage =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[46]++, (count / this.stats.total * 100).toFixed(1));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[47]++;
      console.log(`     ${category}: ${count} (${percentage}%)`);
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[48]++;
    console.log('   文化语境分布:');
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[49]++;
    for (const [context, count] of Object.entries(this.stats.byContext)) {
      const percentage =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[50]++, (count / this.stats.total * 100).toFixed(1));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[51]++;
      console.log(`     ${context}: ${count} (${percentage}%)`);
    }
  }
  /**
   * 构建语素映射
   *
   * 将语素数组转换为高效的Map结构
   *
   * @private
   * @param morphemes 语素数组
   */
  buildMorphemeMap(morphemes) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[4]++;
    cov_1b7zfbkfuu().s[52]++;
    this.morphemes.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[53]++;
    for (const morpheme of morphemes) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[54]++;
      this.morphemes.set(morpheme.id, morpheme);
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[55]++;
    console.log(`🗂️ 构建语素映射完成: ${this.morphemes.size} 个语素`);
  }
  /**
   * 构建多维度索引
   *
   * 为高效查询构建各种索引结构
   *
   * @private
   * @returns Promise<void>
   */
  async buildIndices() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[5]++;
    const startTime =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[56]++, Date.now());
    // 清空现有索引
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[57]++;
    this.clearIndices();
    // 构建各维度索引
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[58]++;
    for (const morpheme of this.morphemes.values()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[59]++;
      // 1. 按类别索引
      this.addToIndex(this.indices.byCategory, morpheme.category, morpheme);
      // 2. 按文化语境索引
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[60]++;
      this.addToIndex(this.indices.byContext, morpheme.cultural_context, morpheme);
      // 3. 按子分类索引
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[61]++;
      this.addToIndex(this.indices.bySubcategory, morpheme.subcategory, morpheme);
      // 4. 按质量分数索引（分桶：0.1精度）
      const qualityBucket =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[62]++, Math.round(morpheme.quality_score * 10) / 10);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[63]++;
      this.addToIndex(this.indices.byQuality, qualityBucket, morpheme);
      // 5. 按语义聚类索引（基于语义向量的聚类）
      const semanticCluster =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[64]++, this.calculateSemanticCluster(morpheme.semantic_vector));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[65]++;
      this.addToIndex(this.indices.bySemanticCluster, semanticCluster, morpheme);
      // 6. 按标签索引
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[66]++;
      for (const tag of morpheme.tags) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().s[67]++;
        this.addToIndex(this.indices.byTags, tag, morpheme);
      }
    }
    const indexTime =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[68]++, Date.now() - startTime);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[69]++;
    console.log(`📊 多维度索引构建完成: 耗时${indexTime}ms`);
    // 输出索引统计
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[70]++;
    this.logIndexStats();
  }
  /**
   * 清空所有索引
   *
   * @private
   */
  clearIndices() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[6]++;
    cov_1b7zfbkfuu().s[71]++;
    this.indices.byCategory.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[72]++;
    this.indices.byContext.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[73]++;
    this.indices.bySubcategory.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[74]++;
    this.indices.byQuality.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[75]++;
    this.indices.bySemanticCluster.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[76]++;
    this.indices.byTags.clear();
  }
  /**
   * 添加到索引
   *
   * @private
   * @param index 索引Map
   * @param key 索引键
   * @param morpheme 语素
   */
  addToIndex(index, key, morpheme) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[7]++;
    cov_1b7zfbkfuu().s[77]++;
    if (!index.has(key)) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[6][0]++;
      cov_1b7zfbkfuu().s[78]++;
      index.set(key, []);
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[6][1]++;
    }
    cov_1b7zfbkfuu().s[79]++;
    index.get(key).push(morpheme);
  }
  /**
   * 计算语义聚类
   *
   * 基于语义向量计算聚类标识
   *
   * @private
   * @param semanticVector 语义向量
   * @returns 聚类标识
   */
  calculateSemanticCluster(semanticVector) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[8]++;
    // 简化的聚类算法：基于向量的主要维度
    const maxIndex =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[80]++, semanticVector.indexOf(Math.max(...semanticVector)));
    const avgValue =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[81]++, semanticVector.reduce((sum, val) => {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().f[9]++;
      cov_1b7zfbkfuu().s[82]++;
      return sum + val;
    }, 0) / semanticVector.length);
    // 根据最大值索引和平均值确定聚类
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[83]++;
    if (avgValue > 0.7) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[7][0]++;
      cov_1b7zfbkfuu().s[84]++;
      return `high_${Math.floor(maxIndex / 5)}`;
    } else {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[7][1]++;
      cov_1b7zfbkfuu().s[85]++;
      if (avgValue > 0.4) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[8][0]++;
        cov_1b7zfbkfuu().s[86]++;
        return `medium_${Math.floor(maxIndex / 5)}`;
      } else {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[8][1]++;
        cov_1b7zfbkfuu().s[87]++;
        return `low_${Math.floor(maxIndex / 5)}`;
      }
    }
  }
  /**
   * 输出索引统计信息
   *
   * @private
   */
  logIndexStats() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[10]++;
    cov_1b7zfbkfuu().s[88]++;
    console.log('📊 索引统计:');
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[89]++;
    console.log(`   类别索引: ${this.indices.byCategory.size} 个类别`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[90]++;
    console.log(`   文化语境索引: ${this.indices.byContext.size} 个语境`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[91]++;
    console.log(`   子分类索引: ${this.indices.bySubcategory.size} 个子分类`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[92]++;
    console.log(`   质量分桶索引: ${this.indices.byQuality.size} 个分桶`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[93]++;
    console.log(`   语义聚类索引: ${this.indices.bySemanticCluster.size} 个聚类`);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[94]++;
    console.log(`   标签索引: ${this.indices.byTags.size} 个标签`);
  }
  /**
   * 构建Alias Table采样表
   *
   * 为每个类别构建O(1)时间复杂度的加权采样表
   *
   * @private
   */
  buildAliasTables() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[11]++;
    cov_1b7zfbkfuu().s[95]++;
    this.aliasTables.clear();
    // 为每个类别构建Alias Table
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[96]++;
    for (const [category, morphemes] of this.indices.byCategory.entries()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[97]++;
      if (morphemes.length === 0) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[9][0]++;
        cov_1b7zfbkfuu().s[98]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_1b7zfbkfuu().b[9][1]++;
      }
      const weights =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[99]++, morphemes.map(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[12]++;
        cov_1b7zfbkfuu().s[100]++;
        return m.usage_frequency * m.quality_score;
      }));
      const aliasTable =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[101]++, this.createAliasTable(weights));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[102]++;
      this.aliasTables.set(category, aliasTable);
    }
    // 为每个文化语境构建Alias Table
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[103]++;
    for (const [context, morphemes] of this.indices.byContext.entries()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[104]++;
      if (morphemes.length === 0) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[10][0]++;
        cov_1b7zfbkfuu().s[105]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_1b7zfbkfuu().b[10][1]++;
      }
      const weights =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[106]++, morphemes.map(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[13]++;
        cov_1b7zfbkfuu().s[107]++;
        return m.usage_frequency * m.quality_score;
      }));
      const aliasTable =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[108]++, this.createAliasTable(weights));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[109]++;
      this.aliasTables.set(`context_${context}`, aliasTable);
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[110]++;
    console.log(`🎲 Alias Table构建完成: ${this.aliasTables.size} 个采样表`);
  }
  /**
   * 创建Alias Table
   *
   * 实现Walker's Alias Method算法
   *
   * @private
   * @param weights 权重数组
   * @returns Alias Table
   */
  createAliasTable(weights) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[14]++;
    const n =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[111]++, weights.length);
    const prob =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[112]++, new Array(n));
    const alias =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[113]++, new Array(n));
    // 归一化权重
    const sum =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[114]++, weights.reduce((a, b) => {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().f[15]++;
      cov_1b7zfbkfuu().s[115]++;
      return a + b;
    }, 0));
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[116]++;
    if (sum === 0) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[11][0]++;
      cov_1b7zfbkfuu().s[117]++;
      // 如果所有权重都为0，使用均匀分布
      prob.fill(1.0);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[118]++;
      alias.fill(0);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[119]++;
      return {
        prob,
        alias,
        sample: () => {
          /* istanbul ignore next */
          cov_1b7zfbkfuu().f[16]++;
          cov_1b7zfbkfuu().s[120]++;
          return Math.floor(Math.random() * n);
        }
      };
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[11][1]++;
    }
    const normalizedWeights =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[121]++, weights.map(w => {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().f[17]++;
      cov_1b7zfbkfuu().s[122]++;
      return w * n / sum;
    }));
    // 分离小于1和大于等于1的权重
    const small =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[123]++, []);
    const large =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[124]++, []);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[125]++;
    for (let i =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[126]++, 0); i < n; i++) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[127]++;
      if (normalizedWeights[i] < 1.0) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[12][0]++;
        cov_1b7zfbkfuu().s[128]++;
        small.push(i);
      } else {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[12][1]++;
        cov_1b7zfbkfuu().s[129]++;
        large.push(i);
      }
    }
    // 构建Alias Table
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[130]++;
    while (
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[13][0]++, small.length > 0) &&
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[13][1]++, large.length > 0)) {
      const l =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[131]++, small.pop());
      const g =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[132]++, large.pop());
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[133]++;
      prob[l] = normalizedWeights[l];
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[134]++;
      alias[l] = g;
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[135]++;
      normalizedWeights[g] = normalizedWeights[g] + normalizedWeights[l] - 1.0;
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[136]++;
      if (normalizedWeights[g] < 1.0) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[14][0]++;
        cov_1b7zfbkfuu().s[137]++;
        small.push(g);
      } else {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[14][1]++;
        cov_1b7zfbkfuu().s[138]++;
        large.push(g);
      }
    }
    // 处理剩余项
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[139]++;
    while (large.length > 0) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[140]++;
      prob[large.pop()] = 1.0;
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[141]++;
    while (small.length > 0) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[142]++;
      prob[small.pop()] = 1.0;
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[143]++;
    return {
      prob,
      alias,
      sample() {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[18]++;
        const i =
        /* istanbul ignore next */
        (cov_1b7zfbkfuu().s[144]++, Math.floor(Math.random() * n));
        const r =
        /* istanbul ignore next */
        (cov_1b7zfbkfuu().s[145]++, Math.random());
        /* istanbul ignore next */
        cov_1b7zfbkfuu().s[146]++;
        return r < prob[i] ?
        /* istanbul ignore next */
        (cov_1b7zfbkfuu().b[15][0]++, i) :
        /* istanbul ignore next */
        (cov_1b7zfbkfuu().b[15][1]++, alias[i]);
      }
    };
  }
  /**
   * 计算统计信息
   *
   * @private
   * @param indexBuildTime 索引构建时间
   */
  calculateStats(indexBuildTime) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[19]++;
    const morphemeArray =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[147]++, Array.from(this.morphemes.values()));
    // 计算平均质量和频率
    const totalQuality =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[148]++, morphemeArray.reduce((sum, m) => {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().f[20]++;
      cov_1b7zfbkfuu().s[149]++;
      return sum + m.quality_score;
    }, 0));
    const totalFrequency =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[150]++, morphemeArray.reduce((sum, m) => {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().f[21]++;
      cov_1b7zfbkfuu().s[151]++;
      return sum + m.usage_frequency;
    }, 0));
    // 按类别统计
    const byCategory =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[152]++, {});
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[153]++;
    for (const [category, morphemes] of this.indices.byCategory.entries()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[154]++;
      byCategory[category] = morphemes.length;
    }
    // 按文化语境统计
    const byContext =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[155]++, {});
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[156]++;
    for (const [context, morphemes] of this.indices.byContext.entries()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[157]++;
      byContext[context] = morphemes.length;
    }
    // 按子分类统计
    const bySubcategory =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[158]++, {});
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[159]++;
    for (const [subcategory, morphemes] of this.indices.bySubcategory.entries()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[160]++;
      bySubcategory[subcategory] = morphemes.length;
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[161]++;
    this.stats = {
      total: this.morphemes.size,
      byCategory,
      byContext,
      bySubcategory,
      avgQuality: this.morphemes.size > 0 ?
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[16][0]++, totalQuality / this.morphemes.size) :
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[16][1]++, 0),
      avgFrequency: this.morphemes.size > 0 ?
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[17][0]++, totalFrequency / this.morphemes.size) :
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[17][1]++, 0),
      indexBuildTime,
      lastUpdated: Date.now()
    };
  }
  /**
   * 根据ID获取语素
   */
  findById(id) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[22]++;
    cov_1b7zfbkfuu().s[162]++;
    return this.morphemes.get(id);
  }
  /**
   * 根据类别获取语素
   */
  findByCategory(category) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[23]++;
    cov_1b7zfbkfuu().s[163]++;
    return /* istanbul ignore next */(cov_1b7zfbkfuu().b[18][0]++, this.indices.byCategory.get(category)) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[18][1]++, []);
  }
  /**
   * 根据文化语境获取语素
   */
  findByContext(context) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[24]++;
    cov_1b7zfbkfuu().s[164]++;
    return /* istanbul ignore next */(cov_1b7zfbkfuu().b[19][0]++, this.indices.byContext.get(context)) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[19][1]++, []);
  }
  /**
   * 根据子分类获取语素
   *
   * @param subcategory 子分类
   * @returns 语素数组
   */
  findBySubcategory(subcategory) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[25]++;
    cov_1b7zfbkfuu().s[165]++;
    return /* istanbul ignore next */(cov_1b7zfbkfuu().b[20][0]++, this.indices.bySubcategory.get(subcategory)) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[20][1]++, []);
  }
  /**
   * 根据标签获取语素
   *
   * @param tag 标签
   * @returns 语素数组
   */
  findByTag(tag) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[26]++;
    cov_1b7zfbkfuu().s[166]++;
    return /* istanbul ignore next */(cov_1b7zfbkfuu().b[21][0]++, this.indices.byTags.get(tag)) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[21][1]++, []);
  }
  /**
   * 根据语义聚类获取语素
   *
   * @param cluster 聚类标识
   * @returns 语素数组
   */
  findBySemanticCluster(cluster) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[27]++;
    cov_1b7zfbkfuu().s[167]++;
    return /* istanbul ignore next */(cov_1b7zfbkfuu().b[22][0]++, this.indices.bySemanticCluster.get(cluster)) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[22][1]++, []);
  }
  /**
   * 查找语义相似的语素
   *
   * @param targetMorpheme 目标语素
   * @param threshold 相似度阈值 [0-1]
   * @param limit 返回数量限制
   * @returns 相似度结果数组
   */
  findSimilar(targetMorpheme, threshold =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().b[23][0]++, 0.7), limit =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().b[24][0]++, 10)) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[28]++;
    const results =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[168]++, []);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[169]++;
    for (const morpheme of this.morphemes.values()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[170]++;
      if (morpheme.id === targetMorpheme.id) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[25][0]++;
        cov_1b7zfbkfuu().s[171]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_1b7zfbkfuu().b[25][1]++;
      }
      const similarity =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[172]++, this.calculateSemanticSimilarity(targetMorpheme.semantic_vector, morpheme.semantic_vector));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[173]++;
      if (similarity >= threshold) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[26][0]++;
        cov_1b7zfbkfuu().s[174]++;
        results.push({
          morpheme,
          similarity
        });
      } else
      /* istanbul ignore next */
      {
        cov_1b7zfbkfuu().b[26][1]++;
      }
    }
    // 按相似度降序排序并限制数量
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[175]++;
    return results.sort((a, b) => {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().f[29]++;
      cov_1b7zfbkfuu().s[176]++;
      return b.similarity - a.similarity;
    }).slice(0, limit);
  }
  /**
   * 计算语义相似度
   *
   * 使用余弦相似度算法
   *
   * @param vector1 向量1
   * @param vector2 向量2
   * @returns 相似度 [0-1]
   */
  calculateSemanticSimilarity(vector1, vector2) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[30]++;
    cov_1b7zfbkfuu().s[177]++;
    if (vector1.length !== vector2.length) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[27][0]++;
      cov_1b7zfbkfuu().s[178]++;
      throw new Error('语义向量维度不匹配');
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[27][1]++;
    }
    let dotProduct =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[179]++, 0);
    let norm1 =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[180]++, 0);
    let norm2 =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[181]++, 0);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[182]++;
    for (let i =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[183]++, 0); i < vector1.length; i++) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[184]++;
      dotProduct += vector1[i] * vector2[i];
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[185]++;
      norm1 += vector1[i] * vector1[i];
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[186]++;
      norm2 += vector2[i] * vector2[i];
    }
    const magnitude =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[187]++, Math.sqrt(norm1) * Math.sqrt(norm2));
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[188]++;
    return magnitude === 0 ?
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[28][0]++, 0) :
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[28][1]++, dotProduct / magnitude);
  }
  /**
   * 使用Alias Table进行高效采样
   *
   * @param category 类别
   * @param count 采样数量
   * @returns 采样结果
   */
  sampleByCategory(category, count =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().b[29][0]++, 1)) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[31]++;
    const morphemes =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[189]++, this.indices.byCategory.get(category));
    const aliasTable =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[190]++, this.aliasTables.get(category));
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[191]++;
    if (
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[31][0]++, !morphemes) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[31][1]++, !aliasTable) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[31][2]++, morphemes.length === 0)) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[30][0]++;
      cov_1b7zfbkfuu().s[192]++;
      return [];
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[30][1]++;
    }
    const results =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[193]++, []);
    const usedIndices =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[194]++, new Set());
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[195]++;
    for (let i =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[196]++, 0);
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[32][0]++, i < count) &&
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[32][1]++, usedIndices.size < morphemes.length); i++) {
      let index =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[197]++, aliasTable.sample());
      // 避免重复采样
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[198]++;
      while (
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[33][0]++, usedIndices.has(index)) &&
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[33][1]++, usedIndices.size < morphemes.length)) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().s[199]++;
        index = aliasTable.sample();
      }
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[200]++;
      usedIndices.add(index);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[201]++;
      results.push(morphemes[index]);
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[202]++;
    return results;
  }
  /**
   * 使用Alias Table按文化语境采样
   *
   * @param context 文化语境
   * @param count 采样数量
   * @returns 采样结果
   */
  sampleByContext(context, count =
  /* istanbul ignore next */
  (cov_1b7zfbkfuu().b[34][0]++, 1)) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[32]++;
    const morphemes =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[203]++, this.indices.byContext.get(context));
    const aliasTable =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[204]++, this.aliasTables.get(`context_${context}`));
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[205]++;
    if (
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[36][0]++, !morphemes) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[36][1]++, !aliasTable) ||
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[36][2]++, morphemes.length === 0)) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[35][0]++;
      cov_1b7zfbkfuu().s[206]++;
      return [];
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[35][1]++;
    }
    const results =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[207]++, []);
    const usedIndices =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[208]++, new Set());
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[209]++;
    for (let i =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[210]++, 0);
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[37][0]++, i < count) &&
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[37][1]++, usedIndices.size < morphemes.length); i++) {
      let index =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[211]++, aliasTable.sample());
      // 避免重复采样
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[212]++;
      while (
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[38][0]++, usedIndices.has(index)) &&
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[38][1]++, usedIndices.size < morphemes.length)) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().s[213]++;
        index = aliasTable.sample();
      }
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[214]++;
      usedIndices.add(index);
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[215]++;
      results.push(morphemes[index]);
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[216]++;
    return results;
  }
  /**
   * 根据质量分数范围获取语素
   */
  findByQualityRange(minScore, maxScore) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[33]++;
    const results =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[217]++, []);
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[218]++;
    for (const [score, morphemes] of this.indices.byQuality.entries()) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[219]++;
      if (
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[40][0]++, score >= minScore) &&
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().b[40][1]++, score <= maxScore)) {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().b[39][0]++;
        cov_1b7zfbkfuu().s[220]++;
        results.push(...morphemes);
      } else
      /* istanbul ignore next */
      {
        cov_1b7zfbkfuu().b[39][1]++;
      }
    }
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[221]++;
    return results;
  }
  /**
   * 根据条件采样语素
   */
  sample(criteria) {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[34]++;
    let candidates =
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().s[222]++, Array.from(this.morphemes.values()));
    // 按类别过滤
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[223]++;
    if (criteria.category) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[41][0]++;
      cov_1b7zfbkfuu().s[224]++;
      candidates = candidates.filter(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[35]++;
        cov_1b7zfbkfuu().s[225]++;
        return m.category === criteria.category;
      });
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[41][1]++;
    }
    // 按文化语境过滤
    cov_1b7zfbkfuu().s[226]++;
    if (criteria.cultural_context) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[42][0]++;
      cov_1b7zfbkfuu().s[227]++;
      candidates = candidates.filter(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[36]++;
        cov_1b7zfbkfuu().s[228]++;
        return m.cultural_context === criteria.cultural_context;
      });
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[42][1]++;
    }
    // 按质量分数过滤
    cov_1b7zfbkfuu().s[229]++;
    if (criteria.min_quality_score !== undefined) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[43][0]++;
      cov_1b7zfbkfuu().s[230]++;
      candidates = candidates.filter(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[37]++;
        cov_1b7zfbkfuu().s[231]++;
        return m.quality_score >= criteria.min_quality_score;
      });
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[43][1]++;
    }
    cov_1b7zfbkfuu().s[232]++;
    if (criteria.max_quality_score !== undefined) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[44][0]++;
      cov_1b7zfbkfuu().s[233]++;
      candidates = candidates.filter(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[38]++;
        cov_1b7zfbkfuu().s[234]++;
        return m.quality_score <= criteria.max_quality_score;
      });
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[44][1]++;
    }
    // 按标签过滤
    cov_1b7zfbkfuu().s[235]++;
    if (
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[46][0]++, criteria.tags) &&
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[46][1]++, criteria.tags.length > 0)) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[45][0]++;
      cov_1b7zfbkfuu().s[236]++;
      candidates = candidates.filter(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[39]++;
        cov_1b7zfbkfuu().s[237]++;
        return criteria.tags.some(tag => {
          /* istanbul ignore next */
          cov_1b7zfbkfuu().f[40]++;
          cov_1b7zfbkfuu().s[238]++;
          return m.tags.includes(tag);
        });
      });
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[45][1]++;
    }
    // 排除指定ID
    cov_1b7zfbkfuu().s[239]++;
    if (
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[48][0]++, criteria.exclude_ids) &&
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[48][1]++, criteria.exclude_ids.length > 0)) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[47][0]++;
      cov_1b7zfbkfuu().s[240]++;
      candidates = candidates.filter(m => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[41]++;
        cov_1b7zfbkfuu().s[241]++;
        return !criteria.exclude_ids.includes(m.id);
      });
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[47][1]++;
    }
    // 随机采样
    cov_1b7zfbkfuu().s[242]++;
    if (
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[50][0]++, criteria.limit) &&
    /* istanbul ignore next */
    (cov_1b7zfbkfuu().b[50][1]++, candidates.length > criteria.limit)) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[49][0]++;
      // 简单的随机采样，实际实现中可以使用更复杂的采样算法
      const shuffled =
      /* istanbul ignore next */
      (cov_1b7zfbkfuu().s[243]++, [...candidates].sort(() => {
        /* istanbul ignore next */
        cov_1b7zfbkfuu().f[42]++;
        cov_1b7zfbkfuu().s[244]++;
        return Math.random() - 0.5;
      }));
      /* istanbul ignore next */
      cov_1b7zfbkfuu().s[245]++;
      candidates = shuffled.slice(0, criteria.limit);
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[49][1]++;
    }
    cov_1b7zfbkfuu().s[246]++;
    return candidates;
  }
  /**
   * 获取所有语素
   */
  getAll() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[43]++;
    cov_1b7zfbkfuu().s[247]++;
    return Array.from(this.morphemes.values());
  }
  /**
   * 获取仓库统计信息
   *
   * @returns 详细统计信息
   */
  getStats() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[44]++;
    cov_1b7zfbkfuu().s[248]++;
    if (!this.stats) {
      /* istanbul ignore next */
      cov_1b7zfbkfuu().b[51][0]++;
      cov_1b7zfbkfuu().s[249]++;
      throw new Error('语素仓库未初始化或统计信息不可用');
    } else
    /* istanbul ignore next */
    {
      cov_1b7zfbkfuu().b[51][1]++;
    }
    cov_1b7zfbkfuu().s[250]++;
    return {
      ...this.stats
    };
  }
  /**
   * 获取数据加载结果
   *
   * @returns 最后一次数据加载结果
   */
  getLastLoadResult() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[45]++;
    cov_1b7zfbkfuu().s[251]++;
    return this.lastLoadResult;
  }
  /**
   * 检查是否已初始化
   *
   * @returns 初始化状态
   */
  isReady() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[46]++;
    cov_1b7zfbkfuu().s[252]++;
    return this.isInitialized;
  }
  /**
   * 获取语素总数
   *
   * @returns 语素数量
   */
  getCount() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[47]++;
    cov_1b7zfbkfuu().s[253]++;
    return this.morphemes.size;
  }
  /**
   * 清理资源
   *
   * 清理数据加载器和验证器资源
   */
  destroy() {
    /* istanbul ignore next */
    cov_1b7zfbkfuu().f[48]++;
    cov_1b7zfbkfuu().s[254]++;
    this.dataLoader.destroy();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[255]++;
    this.morphemes.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[256]++;
    this.clearIndices();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[257]++;
    this.aliasTables.clear();
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[258]++;
    this.isInitialized = false;
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[259]++;
    this.stats = null;
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[260]++;
    this.lastLoadResult = null;
    /* istanbul ignore next */
    cov_1b7zfbkfuu().s[261]++;
    console.log('🧹 语素仓库资源已清理');
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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