{"version": 3, "names": ["cov_1b7zfbkfuu", "actualCoverage", "DataLoader", "DataValidator", "MorphemeRepository", "morphemes", "s", "Map", "indices", "byCategory", "byContext", "bySubcategory", "byQuality", "bySemanticCluster", "byTags", "aliasTables", "dataLoader", "dataValidator", "isInitialized", "lastLoadResult", "stats", "constructor", "f", "b", "initialize", "console", "log", "startTime", "Date", "now", "loadAll", "validation", "passed", "Error", "errors", "join", "buildMorphemeMap", "buildIndices", "buildAliasTables", "calculateStats", "initTime", "size", "logInitializationSummary", "error", "message", "String", "reload", "clear", "clearIndices", "total", "avgQuality", "toFixed", "avgFrequency", "category", "count", "Object", "entries", "percentage", "context", "morpheme", "set", "id", "values", "addToIndex", "cultural_context", "subcategory", "qualityBucket", "Math", "round", "quality_score", "semanticCluster", "calculateSemanticCluster", "semantic_vector", "tag", "tags", "indexTime", "logIndexStats", "index", "key", "has", "get", "push", "semanticVector", "maxIndex", "indexOf", "max", "avgValue", "reduce", "sum", "val", "length", "floor", "weights", "map", "m", "usage_frequency", "aliasTable", "createAliasTable", "n", "prob", "Array", "alias", "a", "fill", "sample", "random", "normalizedWeights", "w", "small", "large", "i", "l", "pop", "g", "r", "indexBuildTime", "morphemeArray", "from", "totalQuality", "totalFrequency", "lastUpdated", "findById", "findByCategory", "findByContext", "findBySubcategory", "findByTag", "findBySemanticCluster", "cluster", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetMorpheme", "threshold", "limit", "results", "similarity", "calculateSemanticSimilarity", "sort", "slice", "vector1", "vector2", "dotProduct", "norm1", "norm2", "magnitude", "sqrt", "sampleByCategory", "usedIndices", "Set", "add", "sampleByContext", "findByQualityRange", "minScore", "maxScore", "score", "criteria", "candidates", "filter", "min_quality_score", "undefined", "max_quality_score", "some", "includes", "exclude_ids", "shuffled", "getAll", "getStats", "getLastLoadResult", "isReady", "getCount", "destroy"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/repositories/MorphemeRepository.ts"], "sourcesContent": ["/**\n * 语素仓库类\n *\n * 负责语素数据的加载、索引、查询和智能采样。\n * 支持多维度索引、语义相似度计算和高效的O(1)采样算法。\n *\n * @fileoverview 语素数据仓库核心模块\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type {\n  Morpheme,\n  MorphemeCategory,\n  CulturalContext,\n  SampleCriteria\n} from '../../types/core'\nimport { DataLoader, type DataLoadResult } from '../data/DataLoader'\nimport { DataValidator, type ValidationResult } from '../data/DataValidator'\n\n// ============================================================================\n// 接口定义\n// ============================================================================\n\n/**\n * 语素仓库统计信息接口\n */\nexport interface MorphemeRepositoryStats {\n  /** 语素总数 */\n  total: number\n  /** 按类别分布 */\n  byCategory: Record<string, number>\n  /** 按文化语境分布 */\n  byContext: Record<string, number>\n  /** 按子分类分布 */\n  bySubcategory: Record<string, number>\n  /** 平均质量评分 */\n  avgQuality: number\n  /** 平均使用频率 */\n  avgFrequency: number\n  /** 索引构建时间 */\n  indexBuildTime: number\n  /** 最后更新时间 */\n  lastUpdated: number\n}\n\n/**\n * 语义相似度查询结果接口\n */\nexport interface SimilarityResult {\n  /** 语素 */\n  morpheme: Morpheme\n  /** 相似度分数 [0-1] */\n  similarity: number\n}\n\n/**\n * Alias Table 采样器接口\n *\n * 实现O(1)时间复杂度的加权随机采样\n */\ninterface AliasTable {\n  /** 概率数组 */\n  prob: number[]\n  /** 别名数组 */\n  alias: number[]\n  /** 采样方法 */\n  sample(): number\n}\n\n// ============================================================================\n// 语素仓库类\n// ============================================================================\n\n/**\n * 语素仓库类\n *\n * 提供高效的语素数据管理和查询功能\n */\nexport class MorphemeRepository {\n  // 数据存储\n  private morphemes: Map<string, Morpheme> = new Map()\n\n  // 多维度索引系统\n  private indices: {\n    byCategory: Map<MorphemeCategory, Morpheme[]>\n    byContext: Map<CulturalContext, Morpheme[]>\n    bySubcategory: Map<string, Morpheme[]>\n    byQuality: Map<number, Morpheme[]>\n    bySemanticCluster: Map<string, Morpheme[]>\n    byTags: Map<string, Morpheme[]>\n  } = {\n    byCategory: new Map(),\n    byContext: new Map(),\n    bySubcategory: new Map(),\n    byQuality: new Map(),\n    bySemanticCluster: new Map(),\n    byTags: new Map()\n  }\n\n  // O(1)采样算法支持\n  private aliasTables: Map<string, AliasTable> = new Map()\n\n  // 组件依赖\n  private dataLoader: DataLoader\n  private dataValidator: DataValidator\n\n  // 状态管理\n  private isInitialized = false\n  private lastLoadResult: DataLoadResult | null = null\n  private stats: MorphemeRepositoryStats | null = null\n\n  /**\n   * 构造函数\n   *\n   * @param dataLoader 数据加载器实例\n   * @param dataValidator 数据验证器实例\n   */\n  constructor(\n    dataLoader?: DataLoader,\n    dataValidator?: DataValidator\n  ) {\n    this.dataLoader = dataLoader || new DataLoader()\n    this.dataValidator = dataValidator || new DataValidator()\n  }\n\n  /**\n   * 初始化语素仓库\n   *\n   * 加载数据、验证完整性、构建索引和采样表\n   *\n   * @returns Promise<void>\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('📋 语素仓库已初始化，跳过重复初始化')\n      return\n    }\n\n    const startTime = Date.now()\n    console.log('🚀 开始初始化语素仓库...')\n\n    try {\n      // 1. 加载语素数据\n      console.log('📁 加载语素数据...')\n      this.lastLoadResult = await this.dataLoader.loadAll()\n\n      if (!this.lastLoadResult.validation.passed) {\n        throw new Error(`数据验证失败: ${this.lastLoadResult.validation.errors.join(', ')}`)\n      }\n\n      // 2. 构建语素映射\n      console.log('🗂️ 构建语素映射...')\n      this.buildMorphemeMap(this.lastLoadResult.morphemes)\n\n      // 3. 构建多维度索引\n      console.log('📊 构建多维度索引...')\n      await this.buildIndices()\n\n      // 4. 构建Alias Table采样表\n      console.log('🎲 构建采样表...')\n      this.buildAliasTables()\n\n      // 5. 计算统计信息\n      console.log('📈 计算统计信息...')\n      this.calculateStats(Date.now() - startTime)\n\n      this.isInitialized = true\n\n      const initTime = Date.now() - startTime\n      console.log(`✅ 语素仓库初始化完成: ${this.morphemes.size}个语素, 耗时${initTime}ms`)\n\n      // 输出统计摘要\n      this.logInitializationSummary()\n\n    } catch (error) {\n      console.error('❌ 语素仓库初始化失败:', error)\n      throw new Error(`语素仓库初始化失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 重新加载数据\n   *\n   * 用于热重载或数据更新场景\n   *\n   * @returns Promise<void>\n   */\n  async reload(): Promise<void> {\n    console.log('🔄 重新加载语素数据...')\n\n    // 清空现有数据\n    this.morphemes.clear()\n    this.clearIndices()\n    this.aliasTables.clear()\n    this.isInitialized = false\n\n    // 重新初始化\n    await this.initialize()\n  }\n\n  /**\n   * 输出初始化摘要\n   *\n   * @private\n   */\n  private logInitializationSummary(): void {\n    if (!this.stats) return\n\n    console.log('📊 语素仓库统计摘要:')\n    console.log(`   总计: ${this.stats.total} 个语素`)\n    console.log(`   平均质量: ${this.stats.avgQuality.toFixed(3)}`)\n    console.log(`   平均频率: ${this.stats.avgFrequency.toFixed(3)}`)\n\n    console.log('   类别分布:')\n    for (const [category, count] of Object.entries(this.stats.byCategory)) {\n      const percentage = ((count / this.stats.total) * 100).toFixed(1)\n      console.log(`     ${category}: ${count} (${percentage}%)`)\n    }\n\n    console.log('   文化语境分布:')\n    for (const [context, count] of Object.entries(this.stats.byContext)) {\n      const percentage = ((count / this.stats.total) * 100).toFixed(1)\n      console.log(`     ${context}: ${count} (${percentage}%)`)\n    }\n  }\n\n  /**\n   * 构建语素映射\n   *\n   * 将语素数组转换为高效的Map结构\n   *\n   * @private\n   * @param morphemes 语素数组\n   */\n  private buildMorphemeMap(morphemes: Morpheme[]): void {\n    this.morphemes.clear()\n\n    for (const morpheme of morphemes) {\n      this.morphemes.set(morpheme.id, morpheme)\n    }\n\n    console.log(`🗂️ 构建语素映射完成: ${this.morphemes.size} 个语素`)\n  }\n\n  /**\n   * 构建多维度索引\n   *\n   * 为高效查询构建各种索引结构\n   *\n   * @private\n   * @returns Promise<void>\n   */\n  private async buildIndices(): Promise<void> {\n    const startTime = Date.now()\n\n    // 清空现有索引\n    this.clearIndices()\n\n    // 构建各维度索引\n    for (const morpheme of this.morphemes.values()) {\n      // 1. 按类别索引\n      this.addToIndex(this.indices.byCategory, morpheme.category, morpheme)\n\n      // 2. 按文化语境索引\n      this.addToIndex(this.indices.byContext, morpheme.cultural_context, morpheme)\n\n      // 3. 按子分类索引\n      this.addToIndex(this.indices.bySubcategory, morpheme.subcategory, morpheme)\n\n      // 4. 按质量分数索引（分桶：0.1精度）\n      const qualityBucket = Math.round(morpheme.quality_score * 10) / 10\n      this.addToIndex(this.indices.byQuality, qualityBucket, morpheme)\n\n      // 5. 按语义聚类索引（基于语义向量的聚类）\n      const semanticCluster = this.calculateSemanticCluster(morpheme.semantic_vector)\n      this.addToIndex(this.indices.bySemanticCluster, semanticCluster, morpheme)\n\n      // 6. 按标签索引\n      for (const tag of morpheme.tags) {\n        this.addToIndex(this.indices.byTags, tag, morpheme)\n      }\n    }\n\n    const indexTime = Date.now() - startTime\n    console.log(`📊 多维度索引构建完成: 耗时${indexTime}ms`)\n\n    // 输出索引统计\n    this.logIndexStats()\n  }\n\n  /**\n   * 清空所有索引\n   *\n   * @private\n   */\n  private clearIndices(): void {\n    this.indices.byCategory.clear()\n    this.indices.byContext.clear()\n    this.indices.bySubcategory.clear()\n    this.indices.byQuality.clear()\n    this.indices.bySemanticCluster.clear()\n    this.indices.byTags.clear()\n  }\n\n  /**\n   * 添加到索引\n   *\n   * @private\n   * @param index 索引Map\n   * @param key 索引键\n   * @param morpheme 语素\n   */\n  private addToIndex<K>(index: Map<K, Morpheme[]>, key: K, morpheme: Morpheme): void {\n    if (!index.has(key)) {\n      index.set(key, [])\n    }\n    index.get(key)!.push(morpheme)\n  }\n\n  /**\n   * 计算语义聚类\n   *\n   * 基于语义向量计算聚类标识\n   *\n   * @private\n   * @param semanticVector 语义向量\n   * @returns 聚类标识\n   */\n  private calculateSemanticCluster(semanticVector: number[]): string {\n    // 简化的聚类算法：基于向量的主要维度\n    const maxIndex = semanticVector.indexOf(Math.max(...semanticVector))\n    const avgValue = semanticVector.reduce((sum, val) => sum + val, 0) / semanticVector.length\n\n    // 根据最大值索引和平均值确定聚类\n    if (avgValue > 0.7) {\n      return `high_${Math.floor(maxIndex / 5)}`\n    } else if (avgValue > 0.4) {\n      return `medium_${Math.floor(maxIndex / 5)}`\n    } else {\n      return `low_${Math.floor(maxIndex / 5)}`\n    }\n  }\n\n  /**\n   * 输出索引统计信息\n   *\n   * @private\n   */\n  private logIndexStats(): void {\n    console.log('📊 索引统计:')\n    console.log(`   类别索引: ${this.indices.byCategory.size} 个类别`)\n    console.log(`   文化语境索引: ${this.indices.byContext.size} 个语境`)\n    console.log(`   子分类索引: ${this.indices.bySubcategory.size} 个子分类`)\n    console.log(`   质量分桶索引: ${this.indices.byQuality.size} 个分桶`)\n    console.log(`   语义聚类索引: ${this.indices.bySemanticCluster.size} 个聚类`)\n    console.log(`   标签索引: ${this.indices.byTags.size} 个标签`)\n  }\n\n  /**\n   * 构建Alias Table采样表\n   *\n   * 为每个类别构建O(1)时间复杂度的加权采样表\n   *\n   * @private\n   */\n  private buildAliasTables(): void {\n    this.aliasTables.clear()\n\n    // 为每个类别构建Alias Table\n    for (const [category, morphemes] of this.indices.byCategory.entries()) {\n      if (morphemes.length === 0) continue\n\n      const weights = morphemes.map(m => m.usage_frequency * m.quality_score)\n      const aliasTable = this.createAliasTable(weights)\n      this.aliasTables.set(category, aliasTable)\n    }\n\n    // 为每个文化语境构建Alias Table\n    for (const [context, morphemes] of this.indices.byContext.entries()) {\n      if (morphemes.length === 0) continue\n\n      const weights = morphemes.map(m => m.usage_frequency * m.quality_score)\n      const aliasTable = this.createAliasTable(weights)\n      this.aliasTables.set(`context_${context}`, aliasTable)\n    }\n\n    console.log(`🎲 Alias Table构建完成: ${this.aliasTables.size} 个采样表`)\n  }\n\n  /**\n   * 创建Alias Table\n   *\n   * 实现Walker's Alias Method算法\n   *\n   * @private\n   * @param weights 权重数组\n   * @returns Alias Table\n   */\n  private createAliasTable(weights: number[]): AliasTable {\n    const n = weights.length\n    const prob = new Array(n)\n    const alias = new Array(n)\n\n    // 归一化权重\n    const sum = weights.reduce((a, b) => a + b, 0)\n    if (sum === 0) {\n      // 如果所有权重都为0，使用均匀分布\n      prob.fill(1.0)\n      alias.fill(0)\n      return {\n        prob,\n        alias,\n        sample: () => Math.floor(Math.random() * n)\n      }\n    }\n\n    const normalizedWeights = weights.map(w => w * n / sum)\n\n    // 分离小于1和大于等于1的权重\n    const small: number[] = []\n    const large: number[] = []\n\n    for (let i = 0; i < n; i++) {\n      if (normalizedWeights[i] < 1.0) {\n        small.push(i)\n      } else {\n        large.push(i)\n      }\n    }\n\n    // 构建Alias Table\n    while (small.length > 0 && large.length > 0) {\n      const l = small.pop()!\n      const g = large.pop()!\n\n      prob[l] = normalizedWeights[l]\n      alias[l] = g\n\n      normalizedWeights[g] = normalizedWeights[g] + normalizedWeights[l] - 1.0\n\n      if (normalizedWeights[g] < 1.0) {\n        small.push(g)\n      } else {\n        large.push(g)\n      }\n    }\n\n    // 处理剩余项\n    while (large.length > 0) {\n      prob[large.pop()!] = 1.0\n    }\n\n    while (small.length > 0) {\n      prob[small.pop()!] = 1.0\n    }\n\n    return {\n      prob,\n      alias,\n      sample(): number {\n        const i = Math.floor(Math.random() * n)\n        const r = Math.random()\n        return r < prob[i] ? i : alias[i]\n      }\n    }\n  }\n\n  /**\n   * 计算统计信息\n   *\n   * @private\n   * @param indexBuildTime 索引构建时间\n   */\n  private calculateStats(indexBuildTime: number): void {\n    const morphemeArray = Array.from(this.morphemes.values())\n\n    // 计算平均质量和频率\n    const totalQuality = morphemeArray.reduce((sum, m) => sum + m.quality_score, 0)\n    const totalFrequency = morphemeArray.reduce((sum, m) => sum + m.usage_frequency, 0)\n\n    // 按类别统计\n    const byCategory: Record<string, number> = {}\n    for (const [category, morphemes] of this.indices.byCategory.entries()) {\n      byCategory[category] = morphemes.length\n    }\n\n    // 按文化语境统计\n    const byContext: Record<string, number> = {}\n    for (const [context, morphemes] of this.indices.byContext.entries()) {\n      byContext[context] = morphemes.length\n    }\n\n    // 按子分类统计\n    const bySubcategory: Record<string, number> = {}\n    for (const [subcategory, morphemes] of this.indices.bySubcategory.entries()) {\n      bySubcategory[subcategory] = morphemes.length\n    }\n\n    this.stats = {\n      total: this.morphemes.size,\n      byCategory,\n      byContext,\n      bySubcategory,\n      avgQuality: this.morphemes.size > 0 ? totalQuality / this.morphemes.size : 0,\n      avgFrequency: this.morphemes.size > 0 ? totalFrequency / this.morphemes.size : 0,\n      indexBuildTime,\n      lastUpdated: Date.now()\n    }\n  }\n\n  /**\n   * 根据ID获取语素\n   */\n  findById(id: string): Morpheme | undefined {\n    return this.morphemes.get(id)\n  }\n\n  /**\n   * 根据类别获取语素\n   */\n  findByCategory(category: MorphemeCategory): Morpheme[] {\n    return this.indices.byCategory.get(category) || []\n  }\n\n  /**\n   * 根据文化语境获取语素\n   */\n  findByContext(context: CulturalContext): Morpheme[] {\n    return this.indices.byContext.get(context) || []\n  }\n\n  /**\n   * 根据子分类获取语素\n   *\n   * @param subcategory 子分类\n   * @returns 语素数组\n   */\n  findBySubcategory(subcategory: string): Morpheme[] {\n    return this.indices.bySubcategory.get(subcategory) || []\n  }\n\n  /**\n   * 根据标签获取语素\n   *\n   * @param tag 标签\n   * @returns 语素数组\n   */\n  findByTag(tag: string): Morpheme[] {\n    return this.indices.byTags.get(tag) || []\n  }\n\n  /**\n   * 根据语义聚类获取语素\n   *\n   * @param cluster 聚类标识\n   * @returns 语素数组\n   */\n  findBySemanticCluster(cluster: string): Morpheme[] {\n    return this.indices.bySemanticCluster.get(cluster) || []\n  }\n\n  /**\n   * 查找语义相似的语素\n   *\n   * @param targetMorpheme 目标语素\n   * @param threshold 相似度阈值 [0-1]\n   * @param limit 返回数量限制\n   * @returns 相似度结果数组\n   */\n  findSimilar(targetMorpheme: Morpheme, threshold: number = 0.7, limit: number = 10): SimilarityResult[] {\n    const results: SimilarityResult[] = []\n\n    for (const morpheme of this.morphemes.values()) {\n      if (morpheme.id === targetMorpheme.id) continue\n\n      const similarity = this.calculateSemanticSimilarity(\n        targetMorpheme.semantic_vector,\n        morpheme.semantic_vector\n      )\n\n      if (similarity >= threshold) {\n        results.push({ morpheme, similarity })\n      }\n    }\n\n    // 按相似度降序排序并限制数量\n    return results\n      .sort((a, b) => b.similarity - a.similarity)\n      .slice(0, limit)\n  }\n\n  /**\n   * 计算语义相似度\n   *\n   * 使用余弦相似度算法\n   *\n   * @param vector1 向量1\n   * @param vector2 向量2\n   * @returns 相似度 [0-1]\n   */\n  calculateSemanticSimilarity(vector1: number[], vector2: number[]): number {\n    if (vector1.length !== vector2.length) {\n      throw new Error('语义向量维度不匹配')\n    }\n\n    let dotProduct = 0\n    let norm1 = 0\n    let norm2 = 0\n\n    for (let i = 0; i < vector1.length; i++) {\n      dotProduct += vector1[i] * vector2[i]\n      norm1 += vector1[i] * vector1[i]\n      norm2 += vector2[i] * vector2[i]\n    }\n\n    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)\n    return magnitude === 0 ? 0 : dotProduct / magnitude\n  }\n\n  /**\n   * 使用Alias Table进行高效采样\n   *\n   * @param category 类别\n   * @param count 采样数量\n   * @returns 采样结果\n   */\n  sampleByCategory(category: MorphemeCategory, count: number = 1): Morpheme[] {\n    const morphemes = this.indices.byCategory.get(category)\n    const aliasTable = this.aliasTables.get(category)\n\n    if (!morphemes || !aliasTable || morphemes.length === 0) {\n      return []\n    }\n\n    const results: Morpheme[] = []\n    const usedIndices = new Set<number>()\n\n    for (let i = 0; i < count && usedIndices.size < morphemes.length; i++) {\n      let index = aliasTable.sample()\n\n      // 避免重复采样\n      while (usedIndices.has(index) && usedIndices.size < morphemes.length) {\n        index = aliasTable.sample()\n      }\n\n      usedIndices.add(index)\n      results.push(morphemes[index])\n    }\n\n    return results\n  }\n\n  /**\n   * 使用Alias Table按文化语境采样\n   *\n   * @param context 文化语境\n   * @param count 采样数量\n   * @returns 采样结果\n   */\n  sampleByContext(context: CulturalContext, count: number = 1): Morpheme[] {\n    const morphemes = this.indices.byContext.get(context)\n    const aliasTable = this.aliasTables.get(`context_${context}`)\n\n    if (!morphemes || !aliasTable || morphemes.length === 0) {\n      return []\n    }\n\n    const results: Morpheme[] = []\n    const usedIndices = new Set<number>()\n\n    for (let i = 0; i < count && usedIndices.size < morphemes.length; i++) {\n      let index = aliasTable.sample()\n\n      // 避免重复采样\n      while (usedIndices.has(index) && usedIndices.size < morphemes.length) {\n        index = aliasTable.sample()\n      }\n\n      usedIndices.add(index)\n      results.push(morphemes[index])\n    }\n\n    return results\n  }\n\n  /**\n   * 根据质量分数范围获取语素\n   */\n  findByQualityRange(minScore: number, maxScore: number): Morpheme[] {\n    const results: Morpheme[] = []\n\n    for (const [score, morphemes] of this.indices.byQuality.entries()) {\n      if (score >= minScore && score <= maxScore) {\n        results.push(...morphemes)\n      }\n    }\n\n    return results\n  }\n\n  /**\n   * 根据条件采样语素\n   */\n  sample(criteria: SampleCriteria): Morpheme[] {\n    let candidates = Array.from(this.morphemes.values())\n\n    // 按类别过滤\n    if (criteria.category) {\n      candidates = candidates.filter(m => m.category === criteria.category)\n    }\n\n    // 按文化语境过滤\n    if (criteria.cultural_context) {\n      candidates = candidates.filter(m => m.cultural_context === criteria.cultural_context)\n    }\n\n    // 按质量分数过滤\n    if (criteria.min_quality_score !== undefined) {\n      candidates = candidates.filter(m => m.quality_score >= criteria.min_quality_score!)\n    }\n    if (criteria.max_quality_score !== undefined) {\n      candidates = candidates.filter(m => m.quality_score <= criteria.max_quality_score!)\n    }\n\n    // 按标签过滤\n    if (criteria.tags && criteria.tags.length > 0) {\n      candidates = candidates.filter(m =>\n        criteria.tags!.some(tag => m.tags.includes(tag))\n      )\n    }\n\n    // 排除指定ID\n    if (criteria.exclude_ids && criteria.exclude_ids.length > 0) {\n      candidates = candidates.filter(m => !criteria.exclude_ids!.includes(m.id))\n    }\n\n    // 随机采样\n    if (criteria.limit && candidates.length > criteria.limit) {\n      // 简单的随机采样，实际实现中可以使用更复杂的采样算法\n      const shuffled = [...candidates].sort(() => Math.random() - 0.5)\n      candidates = shuffled.slice(0, criteria.limit)\n    }\n\n    return candidates\n  }\n\n  /**\n   * 获取所有语素\n   */\n  getAll(): Morpheme[] {\n    return Array.from(this.morphemes.values())\n  }\n\n  /**\n   * 获取仓库统计信息\n   *\n   * @returns 详细统计信息\n   */\n  getStats(): MorphemeRepositoryStats {\n    if (!this.stats) {\n      throw new Error('语素仓库未初始化或统计信息不可用')\n    }\n    return { ...this.stats }\n  }\n\n  /**\n   * 获取数据加载结果\n   *\n   * @returns 最后一次数据加载结果\n   */\n  getLastLoadResult(): DataLoadResult | null {\n    return this.lastLoadResult\n  }\n\n  /**\n   * 检查是否已初始化\n   *\n   * @returns 初始化状态\n   */\n  isReady(): boolean {\n    return this.isInitialized\n  }\n\n  /**\n   * 获取语素总数\n   *\n   * @returns 语素数量\n   */\n  getCount(): number {\n    return this.morphemes.size\n  }\n\n  /**\n   * 清理资源\n   *\n   * 清理数据加载器和验证器资源\n   */\n  destroy(): void {\n    this.dataLoader.destroy()\n    this.morphemes.clear()\n    this.clearIndices()\n    this.aliasTables.clear()\n    this.isInitialized = false\n    this.stats = null\n    this.lastLoadResult = null\n\n    console.log('🧹 语素仓库资源已清理')\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyEA;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAzEA;;;;;;;;;;;AAkBA,SAASE,UAAU,QAA6B,oBAAoB;AACpE,SAASC,aAAa,QAA+B,uBAAuB;AAoD5E;AACA;AACA;AAEA;;;;;AAKA,OAAM,MAAOC,kBAAkB;EAC7B;EACQC,SAAS;EAAA;EAAA,CAAAL,cAAA,GAAAM,CAAA,OAA0B,IAAIC,GAAG,EAAE;EAEpD;EACQC,OAAO;EAAA;EAAA,CAAAR,cAAA,GAAAM,CAAA,OAOX;IACFG,UAAU,EAAE,IAAIF,GAAG,EAAE;IACrBG,SAAS,EAAE,IAAIH,GAAG,EAAE;IACpBI,aAAa,EAAE,IAAIJ,GAAG,EAAE;IACxBK,SAAS,EAAE,IAAIL,GAAG,EAAE;IACpBM,iBAAiB,EAAE,IAAIN,GAAG,EAAE;IAC5BO,MAAM,EAAE,IAAIP,GAAG;GAChB;EAED;EACQQ,WAAW;EAAA;EAAA,CAAAf,cAAA,GAAAM,CAAA,OAA4B,IAAIC,GAAG,EAAE;EAExD;EACQS,UAAU;EACVC,aAAa;EAErB;EACQC,aAAa;EAAA;EAAA,CAAAlB,cAAA,GAAAM,CAAA,OAAG,KAAK;EACrBa,cAAc;EAAA;EAAA,CAAAnB,cAAA,GAAAM,CAAA,OAA0B,IAAI;EAC5Cc,KAAK;EAAA;EAAA,CAAApB,cAAA,GAAAM,CAAA,OAAmC,IAAI;EAEpD;;;;;;EAMAe,YACEL,UAAuB,EACvBC,aAA6B;IAAA;IAAAjB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IAE7B,IAAI,CAACU,UAAU;IAAG;IAAA,CAAAhB,cAAA,GAAAuB,CAAA,UAAAP,UAAU;IAAA;IAAA,CAAAhB,cAAA,GAAAuB,CAAA,UAAI,IAAIrB,UAAU,EAAE;IAAA;IAAAF,cAAA,GAAAM,CAAA;IAChD,IAAI,CAACW,aAAa;IAAG;IAAA,CAAAjB,cAAA,GAAAuB,CAAA,UAAAN,aAAa;IAAA;IAAA,CAAAjB,cAAA,GAAAuB,CAAA,UAAI,IAAIpB,aAAa,EAAE;EAC3D;EAEA;;;;;;;EAOA,MAAMqB,UAAUA,CAAA;IAAA;IAAAxB,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACd,IAAI,IAAI,CAACY,aAAa,EAAE;MAAA;MAAAlB,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACtBmB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAAA;MAAA1B,cAAA,GAAAM,CAAA;MAClC;IACF,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAuB,CAAA;IAAA;IAED,MAAMI,SAAS;IAAA;IAAA,CAAA3B,cAAA,GAAAM,CAAA,QAAGsB,IAAI,CAACC,GAAG,EAAE;IAAA;IAAA7B,cAAA,GAAAM,CAAA;IAC5BmB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAAA;IAAA1B,cAAA,GAAAM,CAAA;IAE9B,IAAI;MAAA;MAAAN,cAAA,GAAAM,CAAA;MACF;MACAmB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAAA;MAAA1B,cAAA,GAAAM,CAAA;MAC3B,IAAI,CAACa,cAAc,GAAG,MAAM,IAAI,CAACH,UAAU,CAACc,OAAO,EAAE;MAAA;MAAA9B,cAAA,GAAAM,CAAA;MAErD,IAAI,CAAC,IAAI,CAACa,cAAc,CAACY,UAAU,CAACC,MAAM,EAAE;QAAA;QAAAhC,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAC1C,MAAM,IAAI2B,KAAK,CAAC,WAAW,IAAI,CAACd,cAAc,CAACY,UAAU,CAACG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAChF,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAuB,CAAA;MAAA;MAED;MAAAvB,cAAA,GAAAM,CAAA;MACAmB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAAA;MAAA1B,cAAA,GAAAM,CAAA;MAC5B,IAAI,CAAC8B,gBAAgB,CAAC,IAAI,CAACjB,cAAc,CAACd,SAAS,CAAC;MAEpD;MAAA;MAAAL,cAAA,GAAAM,CAAA;MACAmB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAAA;MAAA1B,cAAA,GAAAM,CAAA;MAC5B,MAAM,IAAI,CAAC+B,YAAY,EAAE;MAEzB;MAAA;MAAArC,cAAA,GAAAM,CAAA;MACAmB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAAA;MAAA1B,cAAA,GAAAM,CAAA;MAC1B,IAAI,CAACgC,gBAAgB,EAAE;MAEvB;MAAA;MAAAtC,cAAA,GAAAM,CAAA;MACAmB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAAA;MAAA1B,cAAA,GAAAM,CAAA;MAC3B,IAAI,CAACiC,cAAc,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,CAAC;MAAA;MAAA3B,cAAA,GAAAM,CAAA;MAE3C,IAAI,CAACY,aAAa,GAAG,IAAI;MAEzB,MAAMsB,QAAQ;MAAA;MAAA,CAAAxC,cAAA,GAAAM,CAAA,QAAGsB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MAAA;MAAA3B,cAAA,GAAAM,CAAA;MACvCmB,OAAO,CAACC,GAAG,CAAC,gBAAgB,IAAI,CAACrB,SAAS,CAACoC,IAAI,UAAUD,QAAQ,IAAI,CAAC;MAEtE;MAAA;MAAAxC,cAAA,GAAAM,CAAA;MACA,IAAI,CAACoC,wBAAwB,EAAE;IAEjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAA3C,cAAA,GAAAM,CAAA;MACdmB,OAAO,CAACkB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MAAA;MAAA3C,cAAA,GAAAM,CAAA;MACpC,MAAM,IAAI2B,KAAK,CAAC,cAAcU,KAAK,YAAYV,KAAK;MAAA;MAAA,CAAAjC,cAAA,GAAAuB,CAAA,UAAGoB,KAAK,CAACC,OAAO;MAAA;MAAA,CAAA5C,cAAA,GAAAuB,CAAA,UAAGsB,MAAM,CAACF,KAAK,CAAC,GAAE,CAAC;IACzF;EACF;EAEA;;;;;;;EAOA,MAAMG,MAAMA,CAAA;IAAA;IAAA9C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACVmB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B;IAAA;IAAA1B,cAAA,GAAAM,CAAA;IACA,IAAI,CAACD,SAAS,CAAC0C,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IACtB,IAAI,CAAC0C,YAAY,EAAE;IAAA;IAAAhD,cAAA,GAAAM,CAAA;IACnB,IAAI,CAACS,WAAW,CAACgC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IACxB,IAAI,CAACY,aAAa,GAAG,KAAK;IAE1B;IAAA;IAAAlB,cAAA,GAAAM,CAAA;IACA,MAAM,IAAI,CAACkB,UAAU,EAAE;EACzB;EAEA;;;;;EAKQkB,wBAAwBA,CAAA;IAAA;IAAA1C,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IAC9B,IAAI,CAAC,IAAI,CAACc,KAAK,EAAE;MAAA;MAAApB,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAAA;IAAA,CAAM;IAAA;IAAA;MAAAN,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IAEvBmB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAAA;IAAA1B,cAAA,GAAAM,CAAA;IAC3BmB,OAAO,CAACC,GAAG,CAAC,UAAU,IAAI,CAACN,KAAK,CAAC6B,KAAK,MAAM,CAAC;IAAA;IAAAjD,cAAA,GAAAM,CAAA;IAC7CmB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACN,KAAK,CAAC8B,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAA;IAAAnD,cAAA,GAAAM,CAAA;IAC3DmB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACN,KAAK,CAACgC,YAAY,CAACD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAAA;IAAAnD,cAAA,GAAAM,CAAA;IAE7DmB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IAAA;IAAA1B,cAAA,GAAAM,CAAA;IACvB,KAAK,MAAM,CAAC+C,QAAQ,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACpC,KAAK,CAACX,UAAU,CAAC,EAAE;MACrE,MAAMgD,UAAU;MAAA;MAAA,CAAAzD,cAAA,GAAAM,CAAA,QAAG,CAAEgD,KAAK,GAAG,IAAI,CAAClC,KAAK,CAAC6B,KAAK,GAAI,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC;MAAA;MAAAnD,cAAA,GAAAM,CAAA;MAChEmB,OAAO,CAACC,GAAG,CAAC,QAAQ2B,QAAQ,KAAKC,KAAK,KAAKG,UAAU,IAAI,CAAC;IAC5D;IAAC;IAAAzD,cAAA,GAAAM,CAAA;IAEDmB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAAA;IAAA1B,cAAA,GAAAM,CAAA;IACzB,KAAK,MAAM,CAACoD,OAAO,EAAEJ,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACpC,KAAK,CAACV,SAAS,CAAC,EAAE;MACnE,MAAM+C,UAAU;MAAA;MAAA,CAAAzD,cAAA,GAAAM,CAAA,QAAG,CAAEgD,KAAK,GAAG,IAAI,CAAClC,KAAK,CAAC6B,KAAK,GAAI,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC;MAAA;MAAAnD,cAAA,GAAAM,CAAA;MAChEmB,OAAO,CAACC,GAAG,CAAC,QAAQgC,OAAO,KAAKJ,KAAK,KAAKG,UAAU,IAAI,CAAC;IAC3D;EACF;EAEA;;;;;;;;EAQQrB,gBAAgBA,CAAC/B,SAAqB;IAAA;IAAAL,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IAC5C,IAAI,CAACD,SAAS,CAAC0C,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IAEtB,KAAK,MAAMqD,QAAQ,IAAItD,SAAS,EAAE;MAAA;MAAAL,cAAA,GAAAM,CAAA;MAChC,IAAI,CAACD,SAAS,CAACuD,GAAG,CAACD,QAAQ,CAACE,EAAE,EAAEF,QAAQ,CAAC;IAC3C;IAAC;IAAA3D,cAAA,GAAAM,CAAA;IAEDmB,OAAO,CAACC,GAAG,CAAC,iBAAiB,IAAI,CAACrB,SAAS,CAACoC,IAAI,MAAM,CAAC;EACzD;EAEA;;;;;;;;EAQQ,MAAMJ,YAAYA,CAAA;IAAA;IAAArC,cAAA,GAAAsB,CAAA;IACxB,MAAMK,SAAS;IAAA;IAAA,CAAA3B,cAAA,GAAAM,CAAA,QAAGsB,IAAI,CAACC,GAAG,EAAE;IAE5B;IAAA;IAAA7B,cAAA,GAAAM,CAAA;IACA,IAAI,CAAC0C,YAAY,EAAE;IAEnB;IAAA;IAAAhD,cAAA,GAAAM,CAAA;IACA,KAAK,MAAMqD,QAAQ,IAAI,IAAI,CAACtD,SAAS,CAACyD,MAAM,EAAE,EAAE;MAAA;MAAA9D,cAAA,GAAAM,CAAA;MAC9C;MACA,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACvD,OAAO,CAACC,UAAU,EAAEkD,QAAQ,CAACN,QAAQ,EAAEM,QAAQ,CAAC;MAErE;MAAA;MAAA3D,cAAA,GAAAM,CAAA;MACA,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACvD,OAAO,CAACE,SAAS,EAAEiD,QAAQ,CAACK,gBAAgB,EAAEL,QAAQ,CAAC;MAE5E;MAAA;MAAA3D,cAAA,GAAAM,CAAA;MACA,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACvD,OAAO,CAACG,aAAa,EAAEgD,QAAQ,CAACM,WAAW,EAAEN,QAAQ,CAAC;MAE3E;MACA,MAAMO,aAAa;MAAA;MAAA,CAAAlE,cAAA,GAAAM,CAAA,QAAG6D,IAAI,CAACC,KAAK,CAACT,QAAQ,CAACU,aAAa,GAAG,EAAE,CAAC,GAAG,EAAE;MAAA;MAAArE,cAAA,GAAAM,CAAA;MAClE,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACvD,OAAO,CAACI,SAAS,EAAEsD,aAAa,EAAEP,QAAQ,CAAC;MAEhE;MACA,MAAMW,eAAe;MAAA;MAAA,CAAAtE,cAAA,GAAAM,CAAA,QAAG,IAAI,CAACiE,wBAAwB,CAACZ,QAAQ,CAACa,eAAe,CAAC;MAAA;MAAAxE,cAAA,GAAAM,CAAA;MAC/E,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACvD,OAAO,CAACK,iBAAiB,EAAEyD,eAAe,EAAEX,QAAQ,CAAC;MAE1E;MAAA;MAAA3D,cAAA,GAAAM,CAAA;MACA,KAAK,MAAMmE,GAAG,IAAId,QAAQ,CAACe,IAAI,EAAE;QAAA;QAAA1E,cAAA,GAAAM,CAAA;QAC/B,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACvD,OAAO,CAACM,MAAM,EAAE2D,GAAG,EAAEd,QAAQ,CAAC;MACrD;IACF;IAEA,MAAMgB,SAAS;IAAA;IAAA,CAAA3E,cAAA,GAAAM,CAAA,QAAGsB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;IAAA;IAAA3B,cAAA,GAAAM,CAAA;IACxCmB,OAAO,CAACC,GAAG,CAAC,mBAAmBiD,SAAS,IAAI,CAAC;IAE7C;IAAA;IAAA3E,cAAA,GAAAM,CAAA;IACA,IAAI,CAACsE,aAAa,EAAE;EACtB;EAEA;;;;;EAKQ5B,YAAYA,CAAA;IAAA;IAAAhD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IAClB,IAAI,CAACE,OAAO,CAACC,UAAU,CAACsC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IAC/B,IAAI,CAACE,OAAO,CAACE,SAAS,CAACqC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IAC9B,IAAI,CAACE,OAAO,CAACG,aAAa,CAACoC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IAClC,IAAI,CAACE,OAAO,CAACI,SAAS,CAACmC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IAC9B,IAAI,CAACE,OAAO,CAACK,iBAAiB,CAACkC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IACtC,IAAI,CAACE,OAAO,CAACM,MAAM,CAACiC,KAAK,EAAE;EAC7B;EAEA;;;;;;;;EAQQgB,UAAUA,CAAIc,KAAyB,EAAEC,GAAM,EAAEnB,QAAkB;IAAA;IAAA3D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACzE,IAAI,CAACuE,KAAK,CAACE,GAAG,CAACD,GAAG,CAAC,EAAE;MAAA;MAAA9E,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACnBuE,KAAK,CAACjB,GAAG,CAACkB,GAAG,EAAE,EAAE,CAAC;IACpB,CAAC;IAAA;IAAA;MAAA9E,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IACDuE,KAAK,CAACG,GAAG,CAACF,GAAG,CAAE,CAACG,IAAI,CAACtB,QAAQ,CAAC;EAChC;EAEA;;;;;;;;;EASQY,wBAAwBA,CAACW,cAAwB;IAAA;IAAAlF,cAAA,GAAAsB,CAAA;IACvD;IACA,MAAM6D,QAAQ;IAAA;IAAA,CAAAnF,cAAA,GAAAM,CAAA,QAAG4E,cAAc,CAACE,OAAO,CAACjB,IAAI,CAACkB,GAAG,CAAC,GAAGH,cAAc,CAAC,CAAC;IACpE,MAAMI,QAAQ;IAAA;IAAA,CAAAtF,cAAA,GAAAM,CAAA,QAAG4E,cAAc,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAAA;MAAAzF,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAM,CAAA;MAAA,OAAAkF,GAAG,GAAGC,GAAG;IAAH,CAAG,EAAE,CAAC,CAAC,GAAGP,cAAc,CAACQ,MAAM;IAE1F;IAAA;IAAA1F,cAAA,GAAAM,CAAA;IACA,IAAIgF,QAAQ,GAAG,GAAG,EAAE;MAAA;MAAAtF,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAClB,OAAO,QAAQ6D,IAAI,CAACwB,KAAK,CAACR,QAAQ,GAAG,CAAC,CAAC,EAAE;IAC3C,CAAC,MAAM;MAAA;MAAAnF,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAAA,IAAIgF,QAAQ,GAAG,GAAG,EAAE;QAAA;QAAAtF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QACzB,OAAO,UAAU6D,IAAI,CAACwB,KAAK,CAACR,QAAQ,GAAG,CAAC,CAAC,EAAE;MAC7C,CAAC,MAAM;QAAA;QAAAnF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QACL,OAAO,OAAO6D,IAAI,CAACwB,KAAK,CAACR,QAAQ,GAAG,CAAC,CAAC,EAAE;MAC1C;IAAA;EACF;EAEA;;;;;EAKQP,aAAaA,CAAA;IAAA;IAAA5E,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACnBmB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IAAA;IAAA1B,cAAA,GAAAM,CAAA;IACvBmB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAAClB,OAAO,CAACC,UAAU,CAACgC,IAAI,MAAM,CAAC;IAAA;IAAAzC,cAAA,GAAAM,CAAA;IAC3DmB,OAAO,CAACC,GAAG,CAAC,cAAc,IAAI,CAAClB,OAAO,CAACE,SAAS,CAAC+B,IAAI,MAAM,CAAC;IAAA;IAAAzC,cAAA,GAAAM,CAAA;IAC5DmB,OAAO,CAACC,GAAG,CAAC,aAAa,IAAI,CAAClB,OAAO,CAACG,aAAa,CAAC8B,IAAI,OAAO,CAAC;IAAA;IAAAzC,cAAA,GAAAM,CAAA;IAChEmB,OAAO,CAACC,GAAG,CAAC,cAAc,IAAI,CAAClB,OAAO,CAACI,SAAS,CAAC6B,IAAI,MAAM,CAAC;IAAA;IAAAzC,cAAA,GAAAM,CAAA;IAC5DmB,OAAO,CAACC,GAAG,CAAC,cAAc,IAAI,CAAClB,OAAO,CAACK,iBAAiB,CAAC4B,IAAI,MAAM,CAAC;IAAA;IAAAzC,cAAA,GAAAM,CAAA;IACpEmB,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAAClB,OAAO,CAACM,MAAM,CAAC2B,IAAI,MAAM,CAAC;EACzD;EAEA;;;;;;;EAOQH,gBAAgBA,CAAA;IAAA;IAAAtC,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACtB,IAAI,CAACS,WAAW,CAACgC,KAAK,EAAE;IAExB;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IACA,KAAK,MAAM,CAAC+C,QAAQ,EAAEhD,SAAS,CAAC,IAAI,IAAI,CAACG,OAAO,CAACC,UAAU,CAAC+C,OAAO,EAAE,EAAE;MAAA;MAAAxD,cAAA,GAAAM,CAAA;MACrE,IAAID,SAAS,CAACqF,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA1F,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAAA;MAAA,CAAQ;MAAA;MAAA;QAAAN,cAAA,GAAAuB,CAAA;MAAA;MAEpC,MAAMqE,OAAO;MAAA;MAAA,CAAA5F,cAAA,GAAAM,CAAA,QAAGD,SAAS,CAACwF,GAAG,CAACC,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAwF,CAAC,CAACC,eAAe,GAAGD,CAAC,CAACzB,aAAa;MAAb,CAAa,CAAC;MACvE,MAAM2B,UAAU;MAAA;MAAA,CAAAhG,cAAA,GAAAM,CAAA,SAAG,IAAI,CAAC2F,gBAAgB,CAACL,OAAO,CAAC;MAAA;MAAA5F,cAAA,GAAAM,CAAA;MACjD,IAAI,CAACS,WAAW,CAAC6C,GAAG,CAACP,QAAQ,EAAE2C,UAAU,CAAC;IAC5C;IAEA;IAAA;IAAAhG,cAAA,GAAAM,CAAA;IACA,KAAK,MAAM,CAACoD,OAAO,EAAErD,SAAS,CAAC,IAAI,IAAI,CAACG,OAAO,CAACE,SAAS,CAAC8C,OAAO,EAAE,EAAE;MAAA;MAAAxD,cAAA,GAAAM,CAAA;MACnE,IAAID,SAAS,CAACqF,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA1F,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAAA;MAAA,CAAQ;MAAA;MAAA;QAAAN,cAAA,GAAAuB,CAAA;MAAA;MAEpC,MAAMqE,OAAO;MAAA;MAAA,CAAA5F,cAAA,GAAAM,CAAA,SAAGD,SAAS,CAACwF,GAAG,CAACC,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAwF,CAAC,CAACC,eAAe,GAAGD,CAAC,CAACzB,aAAa;MAAb,CAAa,CAAC;MACvE,MAAM2B,UAAU;MAAA;MAAA,CAAAhG,cAAA,GAAAM,CAAA,SAAG,IAAI,CAAC2F,gBAAgB,CAACL,OAAO,CAAC;MAAA;MAAA5F,cAAA,GAAAM,CAAA;MACjD,IAAI,CAACS,WAAW,CAAC6C,GAAG,CAAC,WAAWF,OAAO,EAAE,EAAEsC,UAAU,CAAC;IACxD;IAAC;IAAAhG,cAAA,GAAAM,CAAA;IAEDmB,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAACX,WAAW,CAAC0B,IAAI,OAAO,CAAC;EAClE;EAEA;;;;;;;;;EASQwD,gBAAgBA,CAACL,OAAiB;IAAA;IAAA5F,cAAA,GAAAsB,CAAA;IACxC,MAAM4E,CAAC;IAAA;IAAA,CAAAlG,cAAA,GAAAM,CAAA,SAAGsF,OAAO,CAACF,MAAM;IACxB,MAAMS,IAAI;IAAA;IAAA,CAAAnG,cAAA,GAAAM,CAAA,SAAG,IAAI8F,KAAK,CAACF,CAAC,CAAC;IACzB,MAAMG,KAAK;IAAA;IAAA,CAAArG,cAAA,GAAAM,CAAA,SAAG,IAAI8F,KAAK,CAACF,CAAC,CAAC;IAE1B;IACA,MAAMV,GAAG;IAAA;IAAA,CAAAxF,cAAA,GAAAM,CAAA,SAAGsF,OAAO,CAACL,MAAM,CAAC,CAACe,CAAC,EAAE/E,CAAC,KAAK;MAAA;MAAAvB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAM,CAAA;MAAA,OAAAgG,CAAC,GAAG/E,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IAC9C,IAAIkF,GAAG,KAAK,CAAC,EAAE;MAAA;MAAAxF,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACb;MACA6F,IAAI,CAACI,IAAI,CAAC,GAAG,CAAC;MAAA;MAAAvG,cAAA,GAAAM,CAAA;MACd+F,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC;MAAA;MAAAvG,cAAA,GAAAM,CAAA;MACb,OAAO;QACL6F,IAAI;QACJE,KAAK;QACLG,MAAM,EAAEA,CAAA,KAAM;UAAA;UAAAxG,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAM,CAAA;UAAA,OAAA6D,IAAI,CAACwB,KAAK,CAACxB,IAAI,CAACsC,MAAM,EAAE,GAAGP,CAAC,CAAC;QAAD;OAC3C;IACH,CAAC;IAAA;IAAA;MAAAlG,cAAA,GAAAuB,CAAA;IAAA;IAED,MAAMmF,iBAAiB;IAAA;IAAA,CAAA1G,cAAA,GAAAM,CAAA,SAAGsF,OAAO,CAACC,GAAG,CAACc,CAAC,IAAI;MAAA;MAAA3G,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAM,CAAA;MAAA,OAAAqG,CAAC,GAAGT,CAAC,GAAGV,GAAG;IAAH,CAAG,CAAC;IAEvD;IACA,MAAMoB,KAAK;IAAA;IAAA,CAAA5G,cAAA,GAAAM,CAAA,SAAa,EAAE;IAC1B,MAAMuG,KAAK;IAAA;IAAA,CAAA7G,cAAA,GAAAM,CAAA,SAAa,EAAE;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAE1B,KAAK,IAAIwG,CAAC;IAAA;IAAA,CAAA9G,cAAA,GAAAM,CAAA,SAAG,CAAC,GAAEwG,CAAC,GAAGZ,CAAC,EAAEY,CAAC,EAAE,EAAE;MAAA;MAAA9G,cAAA,GAAAM,CAAA;MAC1B,IAAIoG,iBAAiB,CAACI,CAAC,CAAC,GAAG,GAAG,EAAE;QAAA;QAAA9G,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAC9BsG,KAAK,CAAC3B,IAAI,CAAC6B,CAAC,CAAC;MACf,CAAC,MAAM;QAAA;QAAA9G,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QACLuG,KAAK,CAAC5B,IAAI,CAAC6B,CAAC,CAAC;MACf;IACF;IAEA;IAAA;IAAA9G,cAAA,GAAAM,CAAA;IACA;IAAO;IAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAqF,KAAK,CAAClB,MAAM,GAAG,CAAC;IAAA;IAAA,CAAA1F,cAAA,GAAAuB,CAAA,WAAIsF,KAAK,CAACnB,MAAM,GAAG,CAAC,GAAE;MAC3C,MAAMqB,CAAC;MAAA;MAAA,CAAA/G,cAAA,GAAAM,CAAA,SAAGsG,KAAK,CAACI,GAAG,EAAG;MACtB,MAAMC,CAAC;MAAA;MAAA,CAAAjH,cAAA,GAAAM,CAAA,SAAGuG,KAAK,CAACG,GAAG,EAAG;MAAA;MAAAhH,cAAA,GAAAM,CAAA;MAEtB6F,IAAI,CAACY,CAAC,CAAC,GAAGL,iBAAiB,CAACK,CAAC,CAAC;MAAA;MAAA/G,cAAA,GAAAM,CAAA;MAC9B+F,KAAK,CAACU,CAAC,CAAC,GAAGE,CAAC;MAAA;MAAAjH,cAAA,GAAAM,CAAA;MAEZoG,iBAAiB,CAACO,CAAC,CAAC,GAAGP,iBAAiB,CAACO,CAAC,CAAC,GAAGP,iBAAiB,CAACK,CAAC,CAAC,GAAG,GAAG;MAAA;MAAA/G,cAAA,GAAAM,CAAA;MAExE,IAAIoG,iBAAiB,CAACO,CAAC,CAAC,GAAG,GAAG,EAAE;QAAA;QAAAjH,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAC9BsG,KAAK,CAAC3B,IAAI,CAACgC,CAAC,CAAC;MACf,CAAC,MAAM;QAAA;QAAAjH,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QACLuG,KAAK,CAAC5B,IAAI,CAACgC,CAAC,CAAC;MACf;IACF;IAEA;IAAA;IAAAjH,cAAA,GAAAM,CAAA;IACA,OAAOuG,KAAK,CAACnB,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA1F,cAAA,GAAAM,CAAA;MACvB6F,IAAI,CAACU,KAAK,CAACG,GAAG,EAAG,CAAC,GAAG,GAAG;IAC1B;IAAC;IAAAhH,cAAA,GAAAM,CAAA;IAED,OAAOsG,KAAK,CAAClB,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA1F,cAAA,GAAAM,CAAA;MACvB6F,IAAI,CAACS,KAAK,CAACI,GAAG,EAAG,CAAC,GAAG,GAAG;IAC1B;IAAC;IAAAhH,cAAA,GAAAM,CAAA;IAED,OAAO;MACL6F,IAAI;MACJE,KAAK;MACLG,MAAMA,CAAA;QAAA;QAAAxG,cAAA,GAAAsB,CAAA;QACJ,MAAMwF,CAAC;QAAA;QAAA,CAAA9G,cAAA,GAAAM,CAAA,SAAG6D,IAAI,CAACwB,KAAK,CAACxB,IAAI,CAACsC,MAAM,EAAE,GAAGP,CAAC,CAAC;QACvC,MAAMgB,CAAC;QAAA;QAAA,CAAAlH,cAAA,GAAAM,CAAA,SAAG6D,IAAI,CAACsC,MAAM,EAAE;QAAA;QAAAzG,cAAA,GAAAM,CAAA;QACvB,OAAO4G,CAAC,GAAGf,IAAI,CAACW,CAAC,CAAC;QAAA;QAAA,CAAA9G,cAAA,GAAAuB,CAAA,WAAGuF,CAAC;QAAA;QAAA,CAAA9G,cAAA,GAAAuB,CAAA,WAAG8E,KAAK,CAACS,CAAC,CAAC;MACnC;KACD;EACH;EAEA;;;;;;EAMQvE,cAAcA,CAAC4E,cAAsB;IAAA;IAAAnH,cAAA,GAAAsB,CAAA;IAC3C,MAAM8F,aAAa;IAAA;IAAA,CAAApH,cAAA,GAAAM,CAAA,SAAG8F,KAAK,CAACiB,IAAI,CAAC,IAAI,CAAChH,SAAS,CAACyD,MAAM,EAAE,CAAC;IAEzD;IACA,MAAMwD,YAAY;IAAA;IAAA,CAAAtH,cAAA,GAAAM,CAAA,SAAG8G,aAAa,CAAC7B,MAAM,CAAC,CAACC,GAAG,EAAEM,CAAC,KAAK;MAAA;MAAA9F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAM,CAAA;MAAA,OAAAkF,GAAG,GAAGM,CAAC,CAACzB,aAAa;IAAb,CAAa,EAAE,CAAC,CAAC;IAC/E,MAAMkD,cAAc;IAAA;IAAA,CAAAvH,cAAA,GAAAM,CAAA,SAAG8G,aAAa,CAAC7B,MAAM,CAAC,CAACC,GAAG,EAAEM,CAAC,KAAK;MAAA;MAAA9F,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAM,CAAA;MAAA,OAAAkF,GAAG,GAAGM,CAAC,CAACC,eAAe;IAAf,CAAe,EAAE,CAAC,CAAC;IAEnF;IACA,MAAMtF,UAAU;IAAA;IAAA,CAAAT,cAAA,GAAAM,CAAA,SAA2B,EAAE;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAC7C,KAAK,MAAM,CAAC+C,QAAQ,EAAEhD,SAAS,CAAC,IAAI,IAAI,CAACG,OAAO,CAACC,UAAU,CAAC+C,OAAO,EAAE,EAAE;MAAA;MAAAxD,cAAA,GAAAM,CAAA;MACrEG,UAAU,CAAC4C,QAAQ,CAAC,GAAGhD,SAAS,CAACqF,MAAM;IACzC;IAEA;IACA,MAAMhF,SAAS;IAAA;IAAA,CAAAV,cAAA,GAAAM,CAAA,SAA2B,EAAE;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAC5C,KAAK,MAAM,CAACoD,OAAO,EAAErD,SAAS,CAAC,IAAI,IAAI,CAACG,OAAO,CAACE,SAAS,CAAC8C,OAAO,EAAE,EAAE;MAAA;MAAAxD,cAAA,GAAAM,CAAA;MACnEI,SAAS,CAACgD,OAAO,CAAC,GAAGrD,SAAS,CAACqF,MAAM;IACvC;IAEA;IACA,MAAM/E,aAAa;IAAA;IAAA,CAAAX,cAAA,GAAAM,CAAA,SAA2B,EAAE;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAChD,KAAK,MAAM,CAAC2D,WAAW,EAAE5D,SAAS,CAAC,IAAI,IAAI,CAACG,OAAO,CAACG,aAAa,CAAC6C,OAAO,EAAE,EAAE;MAAA;MAAAxD,cAAA,GAAAM,CAAA;MAC3EK,aAAa,CAACsD,WAAW,CAAC,GAAG5D,SAAS,CAACqF,MAAM;IAC/C;IAAC;IAAA1F,cAAA,GAAAM,CAAA;IAED,IAAI,CAACc,KAAK,GAAG;MACX6B,KAAK,EAAE,IAAI,CAAC5C,SAAS,CAACoC,IAAI;MAC1BhC,UAAU;MACVC,SAAS;MACTC,aAAa;MACbuC,UAAU,EAAE,IAAI,CAAC7C,SAAS,CAACoC,IAAI,GAAG,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAuB,CAAA,WAAG+F,YAAY,GAAG,IAAI,CAACjH,SAAS,CAACoC,IAAI;MAAA;MAAA,CAAAzC,cAAA,GAAAuB,CAAA,WAAG,CAAC;MAC5E6B,YAAY,EAAE,IAAI,CAAC/C,SAAS,CAACoC,IAAI,GAAG,CAAC;MAAA;MAAA,CAAAzC,cAAA,GAAAuB,CAAA,WAAGgG,cAAc,GAAG,IAAI,CAAClH,SAAS,CAACoC,IAAI;MAAA;MAAA,CAAAzC,cAAA,GAAAuB,CAAA,WAAG,CAAC;MAChF4F,cAAc;MACdK,WAAW,EAAE5F,IAAI,CAACC,GAAG;KACtB;EACH;EAEA;;;EAGA4F,QAAQA,CAAC5D,EAAU;IAAA;IAAA7D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACjB,OAAO,IAAI,CAACD,SAAS,CAAC2E,GAAG,CAACnB,EAAE,CAAC;EAC/B;EAEA;;;EAGA6D,cAAcA,CAACrE,QAA0B;IAAA;IAAArD,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACvC,OAAO,2BAAAN,cAAA,GAAAuB,CAAA,eAAI,CAACf,OAAO,CAACC,UAAU,CAACuE,GAAG,CAAC3B,QAAQ,CAAC;IAAA;IAAA,CAAArD,cAAA,GAAAuB,CAAA,WAAI,EAAE;EACpD;EAEA;;;EAGAoG,aAAaA,CAACjE,OAAwB;IAAA;IAAA1D,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACpC,OAAO,2BAAAN,cAAA,GAAAuB,CAAA,eAAI,CAACf,OAAO,CAACE,SAAS,CAACsE,GAAG,CAACtB,OAAO,CAAC;IAAA;IAAA,CAAA1D,cAAA,GAAAuB,CAAA,WAAI,EAAE;EAClD;EAEA;;;;;;EAMAqG,iBAAiBA,CAAC3D,WAAmB;IAAA;IAAAjE,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACnC,OAAO,2BAAAN,cAAA,GAAAuB,CAAA,eAAI,CAACf,OAAO,CAACG,aAAa,CAACqE,GAAG,CAACf,WAAW,CAAC;IAAA;IAAA,CAAAjE,cAAA,GAAAuB,CAAA,WAAI,EAAE;EAC1D;EAEA;;;;;;EAMAsG,SAASA,CAACpD,GAAW;IAAA;IAAAzE,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACnB,OAAO,2BAAAN,cAAA,GAAAuB,CAAA,eAAI,CAACf,OAAO,CAACM,MAAM,CAACkE,GAAG,CAACP,GAAG,CAAC;IAAA;IAAA,CAAAzE,cAAA,GAAAuB,CAAA,WAAI,EAAE;EAC3C;EAEA;;;;;;EAMAuG,qBAAqBA,CAACC,OAAe;IAAA;IAAA/H,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACnC,OAAO,2BAAAN,cAAA,GAAAuB,CAAA,eAAI,CAACf,OAAO,CAACK,iBAAiB,CAACmE,GAAG,CAAC+C,OAAO,CAAC;IAAA;IAAA,CAAA/H,cAAA,GAAAuB,CAAA,WAAI,EAAE;EAC1D;EAEA;;;;;;;;EAQAyG,WAAWA,CAACC,cAAwB,EAAEC,SAAA;EAAA;EAAA,CAAAlI,cAAA,GAAAuB,CAAA,WAAoB,GAAG,GAAE4G,KAAA;EAAA;EAAA,CAAAnI,cAAA,GAAAuB,CAAA,WAAgB,EAAE;IAAA;IAAAvB,cAAA,GAAAsB,CAAA;IAC/E,MAAM8G,OAAO;IAAA;IAAA,CAAApI,cAAA,GAAAM,CAAA,SAAuB,EAAE;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAEtC,KAAK,MAAMqD,QAAQ,IAAI,IAAI,CAACtD,SAAS,CAACyD,MAAM,EAAE,EAAE;MAAA;MAAA9D,cAAA,GAAAM,CAAA;MAC9C,IAAIqD,QAAQ,CAACE,EAAE,KAAKoE,cAAc,CAACpE,EAAE,EAAE;QAAA;QAAA7D,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAAA;MAAA,CAAQ;MAAA;MAAA;QAAAN,cAAA,GAAAuB,CAAA;MAAA;MAE/C,MAAM8G,UAAU;MAAA;MAAA,CAAArI,cAAA,GAAAM,CAAA,SAAG,IAAI,CAACgI,2BAA2B,CACjDL,cAAc,CAACzD,eAAe,EAC9Bb,QAAQ,CAACa,eAAe,CACzB;MAAA;MAAAxE,cAAA,GAAAM,CAAA;MAED,IAAI+H,UAAU,IAAIH,SAAS,EAAE;QAAA;QAAAlI,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAC3B8H,OAAO,CAACnD,IAAI,CAAC;UAAEtB,QAAQ;UAAE0E;QAAU,CAAE,CAAC;MACxC,CAAC;MAAA;MAAA;QAAArI,cAAA,GAAAuB,CAAA;MAAA;IACH;IAEA;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IACA,OAAO8H,OAAO,CACXG,IAAI,CAAC,CAACjC,CAAC,EAAE/E,CAAC,KAAK;MAAA;MAAAvB,cAAA,GAAAsB,CAAA;MAAAtB,cAAA,GAAAM,CAAA;MAAA,OAAAiB,CAAC,CAAC8G,UAAU,GAAG/B,CAAC,CAAC+B,UAAU;IAAV,CAAU,CAAC,CAC3CG,KAAK,CAAC,CAAC,EAAEL,KAAK,CAAC;EACpB;EAEA;;;;;;;;;EASAG,2BAA2BA,CAACG,OAAiB,EAAEC,OAAiB;IAAA;IAAA1I,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IAC9D,IAAImI,OAAO,CAAC/C,MAAM,KAAKgD,OAAO,CAAChD,MAAM,EAAE;MAAA;MAAA1F,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACrC,MAAM,IAAI2B,KAAK,CAAC,WAAW,CAAC;IAC9B,CAAC;IAAA;IAAA;MAAAjC,cAAA,GAAAuB,CAAA;IAAA;IAED,IAAIoH,UAAU;IAAA;IAAA,CAAA3I,cAAA,GAAAM,CAAA,SAAG,CAAC;IAClB,IAAIsI,KAAK;IAAA;IAAA,CAAA5I,cAAA,GAAAM,CAAA,SAAG,CAAC;IACb,IAAIuI,KAAK;IAAA;IAAA,CAAA7I,cAAA,GAAAM,CAAA,SAAG,CAAC;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAEb,KAAK,IAAIwG,CAAC;IAAA;IAAA,CAAA9G,cAAA,GAAAM,CAAA,SAAG,CAAC,GAAEwG,CAAC,GAAG2B,OAAO,CAAC/C,MAAM,EAAEoB,CAAC,EAAE,EAAE;MAAA;MAAA9G,cAAA,GAAAM,CAAA;MACvCqI,UAAU,IAAIF,OAAO,CAAC3B,CAAC,CAAC,GAAG4B,OAAO,CAAC5B,CAAC,CAAC;MAAA;MAAA9G,cAAA,GAAAM,CAAA;MACrCsI,KAAK,IAAIH,OAAO,CAAC3B,CAAC,CAAC,GAAG2B,OAAO,CAAC3B,CAAC,CAAC;MAAA;MAAA9G,cAAA,GAAAM,CAAA;MAChCuI,KAAK,IAAIH,OAAO,CAAC5B,CAAC,CAAC,GAAG4B,OAAO,CAAC5B,CAAC,CAAC;IAClC;IAEA,MAAMgC,SAAS;IAAA;IAAA,CAAA9I,cAAA,GAAAM,CAAA,SAAG6D,IAAI,CAAC4E,IAAI,CAACH,KAAK,CAAC,GAAGzE,IAAI,CAAC4E,IAAI,CAACF,KAAK,CAAC;IAAA;IAAA7I,cAAA,GAAAM,CAAA;IACrD,OAAOwI,SAAS,KAAK,CAAC;IAAA;IAAA,CAAA9I,cAAA,GAAAuB,CAAA,WAAG,CAAC;IAAA;IAAA,CAAAvB,cAAA,GAAAuB,CAAA,WAAGoH,UAAU,GAAGG,SAAS;EACrD;EAEA;;;;;;;EAOAE,gBAAgBA,CAAC3F,QAA0B,EAAEC,KAAA;EAAA;EAAA,CAAAtD,cAAA,GAAAuB,CAAA,WAAgB,CAAC;IAAA;IAAAvB,cAAA,GAAAsB,CAAA;IAC5D,MAAMjB,SAAS;IAAA;IAAA,CAAAL,cAAA,GAAAM,CAAA,SAAG,IAAI,CAACE,OAAO,CAACC,UAAU,CAACuE,GAAG,CAAC3B,QAAQ,CAAC;IACvD,MAAM2C,UAAU;IAAA;IAAA,CAAAhG,cAAA,GAAAM,CAAA,SAAG,IAAI,CAACS,WAAW,CAACiE,GAAG,CAAC3B,QAAQ,CAAC;IAAA;IAAArD,cAAA,GAAAM,CAAA;IAEjD;IAAI;IAAA,CAAAN,cAAA,GAAAuB,CAAA,YAAClB,SAAS;IAAA;IAAA,CAAAL,cAAA,GAAAuB,CAAA,WAAI,CAACyE,UAAU;IAAA;IAAA,CAAAhG,cAAA,GAAAuB,CAAA,WAAIlB,SAAS,CAACqF,MAAM,KAAK,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACvD,OAAO,EAAE;IACX,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAuB,CAAA;IAAA;IAED,MAAM6G,OAAO;IAAA;IAAA,CAAApI,cAAA,GAAAM,CAAA,SAAe,EAAE;IAC9B,MAAM2I,WAAW;IAAA;IAAA,CAAAjJ,cAAA,GAAAM,CAAA,SAAG,IAAI4I,GAAG,EAAU;IAAA;IAAAlJ,cAAA,GAAAM,CAAA;IAErC,KAAK,IAAIwG,CAAC;IAAA;IAAA,CAAA9G,cAAA,GAAAM,CAAA,SAAG,CAAC;IAAE;IAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAuF,CAAC,GAAGxD,KAAK;IAAA;IAAA,CAAAtD,cAAA,GAAAuB,CAAA,WAAI0H,WAAW,CAACxG,IAAI,GAAGpC,SAAS,CAACqF,MAAM,GAAEoB,CAAC,EAAE,EAAE;MACrE,IAAIjC,KAAK;MAAA;MAAA,CAAA7E,cAAA,GAAAM,CAAA,SAAG0F,UAAU,CAACQ,MAAM,EAAE;MAE/B;MAAA;MAAAxG,cAAA,GAAAM,CAAA;MACA;MAAO;MAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAA0H,WAAW,CAAClE,GAAG,CAACF,KAAK,CAAC;MAAA;MAAA,CAAA7E,cAAA,GAAAuB,CAAA,WAAI0H,WAAW,CAACxG,IAAI,GAAGpC,SAAS,CAACqF,MAAM,GAAE;QAAA;QAAA1F,cAAA,GAAAM,CAAA;QACpEuE,KAAK,GAAGmB,UAAU,CAACQ,MAAM,EAAE;MAC7B;MAAC;MAAAxG,cAAA,GAAAM,CAAA;MAED2I,WAAW,CAACE,GAAG,CAACtE,KAAK,CAAC;MAAA;MAAA7E,cAAA,GAAAM,CAAA;MACtB8H,OAAO,CAACnD,IAAI,CAAC5E,SAAS,CAACwE,KAAK,CAAC,CAAC;IAChC;IAAC;IAAA7E,cAAA,GAAAM,CAAA;IAED,OAAO8H,OAAO;EAChB;EAEA;;;;;;;EAOAgB,eAAeA,CAAC1F,OAAwB,EAAEJ,KAAA;EAAA;EAAA,CAAAtD,cAAA,GAAAuB,CAAA,WAAgB,CAAC;IAAA;IAAAvB,cAAA,GAAAsB,CAAA;IACzD,MAAMjB,SAAS;IAAA;IAAA,CAAAL,cAAA,GAAAM,CAAA,SAAG,IAAI,CAACE,OAAO,CAACE,SAAS,CAACsE,GAAG,CAACtB,OAAO,CAAC;IACrD,MAAMsC,UAAU;IAAA;IAAA,CAAAhG,cAAA,GAAAM,CAAA,SAAG,IAAI,CAACS,WAAW,CAACiE,GAAG,CAAC,WAAWtB,OAAO,EAAE,CAAC;IAAA;IAAA1D,cAAA,GAAAM,CAAA;IAE7D;IAAI;IAAA,CAAAN,cAAA,GAAAuB,CAAA,YAAClB,SAAS;IAAA;IAAA,CAAAL,cAAA,GAAAuB,CAAA,WAAI,CAACyE,UAAU;IAAA;IAAA,CAAAhG,cAAA,GAAAuB,CAAA,WAAIlB,SAAS,CAACqF,MAAM,KAAK,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACvD,OAAO,EAAE;IACX,CAAC;IAAA;IAAA;MAAAN,cAAA,GAAAuB,CAAA;IAAA;IAED,MAAM6G,OAAO;IAAA;IAAA,CAAApI,cAAA,GAAAM,CAAA,SAAe,EAAE;IAC9B,MAAM2I,WAAW;IAAA;IAAA,CAAAjJ,cAAA,GAAAM,CAAA,SAAG,IAAI4I,GAAG,EAAU;IAAA;IAAAlJ,cAAA,GAAAM,CAAA;IAErC,KAAK,IAAIwG,CAAC;IAAA;IAAA,CAAA9G,cAAA,GAAAM,CAAA,SAAG,CAAC;IAAE;IAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAuF,CAAC,GAAGxD,KAAK;IAAA;IAAA,CAAAtD,cAAA,GAAAuB,CAAA,WAAI0H,WAAW,CAACxG,IAAI,GAAGpC,SAAS,CAACqF,MAAM,GAAEoB,CAAC,EAAE,EAAE;MACrE,IAAIjC,KAAK;MAAA;MAAA,CAAA7E,cAAA,GAAAM,CAAA,SAAG0F,UAAU,CAACQ,MAAM,EAAE;MAE/B;MAAA;MAAAxG,cAAA,GAAAM,CAAA;MACA;MAAO;MAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAA0H,WAAW,CAAClE,GAAG,CAACF,KAAK,CAAC;MAAA;MAAA,CAAA7E,cAAA,GAAAuB,CAAA,WAAI0H,WAAW,CAACxG,IAAI,GAAGpC,SAAS,CAACqF,MAAM,GAAE;QAAA;QAAA1F,cAAA,GAAAM,CAAA;QACpEuE,KAAK,GAAGmB,UAAU,CAACQ,MAAM,EAAE;MAC7B;MAAC;MAAAxG,cAAA,GAAAM,CAAA;MAED2I,WAAW,CAACE,GAAG,CAACtE,KAAK,CAAC;MAAA;MAAA7E,cAAA,GAAAM,CAAA;MACtB8H,OAAO,CAACnD,IAAI,CAAC5E,SAAS,CAACwE,KAAK,CAAC,CAAC;IAChC;IAAC;IAAA7E,cAAA,GAAAM,CAAA;IAED,OAAO8H,OAAO;EAChB;EAEA;;;EAGAiB,kBAAkBA,CAACC,QAAgB,EAAEC,QAAgB;IAAA;IAAAvJ,cAAA,GAAAsB,CAAA;IACnD,MAAM8G,OAAO;IAAA;IAAA,CAAApI,cAAA,GAAAM,CAAA,SAAe,EAAE;IAAA;IAAAN,cAAA,GAAAM,CAAA;IAE9B,KAAK,MAAM,CAACkJ,KAAK,EAAEnJ,SAAS,CAAC,IAAI,IAAI,CAACG,OAAO,CAACI,SAAS,CAAC4C,OAAO,EAAE,EAAE;MAAA;MAAAxD,cAAA,GAAAM,CAAA;MACjE;MAAI;MAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAiI,KAAK,IAAIF,QAAQ;MAAA;MAAA,CAAAtJ,cAAA,GAAAuB,CAAA,WAAIiI,KAAK,IAAID,QAAQ,GAAE;QAAA;QAAAvJ,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAM,CAAA;QAC1C8H,OAAO,CAACnD,IAAI,CAAC,GAAG5E,SAAS,CAAC;MAC5B,CAAC;MAAA;MAAA;QAAAL,cAAA,GAAAuB,CAAA;MAAA;IACH;IAAC;IAAAvB,cAAA,GAAAM,CAAA;IAED,OAAO8H,OAAO;EAChB;EAEA;;;EAGA5B,MAAMA,CAACiD,QAAwB;IAAA;IAAAzJ,cAAA,GAAAsB,CAAA;IAC7B,IAAIoI,UAAU;IAAA;IAAA,CAAA1J,cAAA,GAAAM,CAAA,SAAG8F,KAAK,CAACiB,IAAI,CAAC,IAAI,CAAChH,SAAS,CAACyD,MAAM,EAAE,CAAC;IAEpD;IAAA;IAAA9D,cAAA,GAAAM,CAAA;IACA,IAAImJ,QAAQ,CAACpG,QAAQ,EAAE;MAAA;MAAArD,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACrBoJ,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC7D,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAwF,CAAC,CAACzC,QAAQ,KAAKoG,QAAQ,CAACpG,QAAQ;MAAR,CAAQ,CAAC;IACvE,CAAC;IAAA;IAAA;MAAArD,cAAA,GAAAuB,CAAA;IAAA;IAED;IAAAvB,cAAA,GAAAM,CAAA;IACA,IAAImJ,QAAQ,CAACzF,gBAAgB,EAAE;MAAA;MAAAhE,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAC7BoJ,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC7D,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAwF,CAAC,CAAC9B,gBAAgB,KAAKyF,QAAQ,CAACzF,gBAAgB;MAAhB,CAAgB,CAAC;IACvF,CAAC;IAAA;IAAA;MAAAhE,cAAA,GAAAuB,CAAA;IAAA;IAED;IAAAvB,cAAA,GAAAM,CAAA;IACA,IAAImJ,QAAQ,CAACG,iBAAiB,KAAKC,SAAS,EAAE;MAAA;MAAA7J,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAC5CoJ,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC7D,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAwF,CAAC,CAACzB,aAAa,IAAIoF,QAAQ,CAACG,iBAAkB;MAAlB,CAAkB,CAAC;IACrF,CAAC;IAAA;IAAA;MAAA5J,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IACD,IAAImJ,QAAQ,CAACK,iBAAiB,KAAKD,SAAS,EAAE;MAAA;MAAA7J,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAC5CoJ,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC7D,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAwF,CAAC,CAACzB,aAAa,IAAIoF,QAAQ,CAACK,iBAAkB;MAAlB,CAAkB,CAAC;IACrF,CAAC;IAAA;IAAA;MAAA9J,cAAA,GAAAuB,CAAA;IAAA;IAED;IAAAvB,cAAA,GAAAM,CAAA;IACA;IAAI;IAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAkI,QAAQ,CAAC/E,IAAI;IAAA;IAAA,CAAA1E,cAAA,GAAAuB,CAAA,WAAIkI,QAAQ,CAAC/E,IAAI,CAACgB,MAAM,GAAG,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAC7CoJ,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC7D,CAAC,IAC9B;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAAmJ,QAAQ,CAAC/E,IAAK,CAACqF,IAAI,CAACtF,GAAG,IAAI;UAAA;UAAAzE,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAM,CAAA;UAAA,OAAAwF,CAAC,CAACpB,IAAI,CAACsF,QAAQ,CAACvF,GAAG,CAAC;QAAD,CAAC,CAAC;MAAD,CAAC,CACjD;IACH,CAAC;IAAA;IAAA;MAAAzE,cAAA,GAAAuB,CAAA;IAAA;IAED;IAAAvB,cAAA,GAAAM,CAAA;IACA;IAAI;IAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAkI,QAAQ,CAACQ,WAAW;IAAA;IAAA,CAAAjK,cAAA,GAAAuB,CAAA,WAAIkI,QAAQ,CAACQ,WAAW,CAACvE,MAAM,GAAG,CAAC,GAAE;MAAA;MAAA1F,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MAC3DoJ,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC7D,CAAC,IAAI;QAAA;QAAA9F,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,QAACmJ,QAAQ,CAACQ,WAAY,CAACD,QAAQ,CAAClE,CAAC,CAACjC,EAAE,CAAC;MAAD,CAAC,CAAC;IAC5E,CAAC;IAAA;IAAA;MAAA7D,cAAA,GAAAuB,CAAA;IAAA;IAED;IAAAvB,cAAA,GAAAM,CAAA;IACA;IAAI;IAAA,CAAAN,cAAA,GAAAuB,CAAA,WAAAkI,QAAQ,CAACtB,KAAK;IAAA;IAAA,CAAAnI,cAAA,GAAAuB,CAAA,WAAImI,UAAU,CAAChE,MAAM,GAAG+D,QAAQ,CAACtB,KAAK,GAAE;MAAA;MAAAnI,cAAA,GAAAuB,CAAA;MACxD;MACA,MAAM2I,QAAQ;MAAA;MAAA,CAAAlK,cAAA,GAAAM,CAAA,SAAG,CAAC,GAAGoJ,UAAU,CAAC,CAACnB,IAAI,CAAC,MAAM;QAAA;QAAAvI,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAM,CAAA;QAAA,OAAA6D,IAAI,CAACsC,MAAM,EAAE,GAAG,GAAG;MAAH,CAAG,CAAC;MAAA;MAAAzG,cAAA,GAAAM,CAAA;MAChEoJ,UAAU,GAAGQ,QAAQ,CAAC1B,KAAK,CAAC,CAAC,EAAEiB,QAAQ,CAACtB,KAAK,CAAC;IAChD,CAAC;IAAA;IAAA;MAAAnI,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IAED,OAAOoJ,UAAU;EACnB;EAEA;;;EAGAS,MAAMA,CAAA;IAAA;IAAAnK,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACJ,OAAO8F,KAAK,CAACiB,IAAI,CAAC,IAAI,CAAChH,SAAS,CAACyD,MAAM,EAAE,CAAC;EAC5C;EAEA;;;;;EAKAsG,QAAQA,CAAA;IAAA;IAAApK,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACN,IAAI,CAAC,IAAI,CAACc,KAAK,EAAE;MAAA;MAAApB,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAM,CAAA;MACf,MAAM,IAAI2B,KAAK,CAAC,kBAAkB,CAAC;IACrC,CAAC;IAAA;IAAA;MAAAjC,cAAA,GAAAuB,CAAA;IAAA;IAAAvB,cAAA,GAAAM,CAAA;IACD,OAAO;MAAE,GAAG,IAAI,CAACc;IAAK,CAAE;EAC1B;EAEA;;;;;EAKAiJ,iBAAiBA,CAAA;IAAA;IAAArK,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACf,OAAO,IAAI,CAACa,cAAc;EAC5B;EAEA;;;;;EAKAmJ,OAAOA,CAAA;IAAA;IAAAtK,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACL,OAAO,IAAI,CAACY,aAAa;EAC3B;EAEA;;;;;EAKAqJ,QAAQA,CAAA;IAAA;IAAAvK,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACN,OAAO,IAAI,CAACD,SAAS,CAACoC,IAAI;EAC5B;EAEA;;;;;EAKA+H,OAAOA,CAAA;IAAA;IAAAxK,cAAA,GAAAsB,CAAA;IAAAtB,cAAA,GAAAM,CAAA;IACL,IAAI,CAACU,UAAU,CAACwJ,OAAO,EAAE;IAAA;IAAAxK,cAAA,GAAAM,CAAA;IACzB,IAAI,CAACD,SAAS,CAAC0C,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IACtB,IAAI,CAAC0C,YAAY,EAAE;IAAA;IAAAhD,cAAA,GAAAM,CAAA;IACnB,IAAI,CAACS,WAAW,CAACgC,KAAK,EAAE;IAAA;IAAA/C,cAAA,GAAAM,CAAA;IACxB,IAAI,CAACY,aAAa,GAAG,KAAK;IAAA;IAAAlB,cAAA,GAAAM,CAAA;IAC1B,IAAI,CAACc,KAAK,GAAG,IAAI;IAAA;IAAApB,cAAA,GAAAM,CAAA;IACjB,IAAI,CAACa,cAAc,GAAG,IAAI;IAAA;IAAAnB,cAAA,GAAAM,CAAA;IAE1BmB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B", "ignoreList": []}