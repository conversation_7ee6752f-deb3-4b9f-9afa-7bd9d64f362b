{"version": 3, "names": ["cov_2f4y53u33d", "actualCoverage", "MorphemeCategory", "MorphemeRepository", "DataLoader", "DataValidator", "LanguageManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageCode", "CoreGenerationEngine", "morphemeRepo", "languageManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isInitialized", "s", "generationCount", "totalGenerationTime", "constructor", "f", "dataLoader", "enableHotReload", "enableValidation", "enableCache", "dataValidator", "strict_mode", "skip_warnings", "defaultLanguage", "ZH_CN", "enabledLanguages", "cacheTTL", "semanticSimilarityThreshold", "culturalFitWeight", "semanticWeight", "qualityWeight", "initialize", "b", "console", "log", "startTime", "Date", "now", "isReady", "Error", "stats", "getStats", "supportedLanguages", "getSupportedLanguages", "initTime", "total", "Object", "entries", "byCategory", "map", "k", "v", "join", "lang", "langStats", "getLanguageStats", "morphemeCount", "conceptCount", "error", "message", "String", "reload", "language", "reloadLanguage", "generate", "context", "count", "generateMultilingual", "isLanguageSupported", "results", "maxRetries", "attempts", "length", "username", "generateSingle", "generateMultilingualSingle", "isDuplicate", "some", "existing", "text", "push", "generationTime", "warn", "generationStart", "pattern", "selectPattern", "components", "selectMorphemes", "combineComponents", "qualityScore", "evaluateQuality", "overall", "quality_threshold", "name", "quality_score", "explanation", "generateExplanation", "metadata", "cultural_fit", "calculateCulturalFitScore", "creativity", "calculateCreativityScore", "memorability", "calculateMemorabilityScore", "uniqueness", "calculateUniquenessScore", "generation_time", "languageMorphemes", "getMorphemesByLanguage", "selectMultilingualPattern", "selectMultilingualMorphemes", "combineMultilingualComponents", "evaluateMultilingualQuality", "generateMultilingualExplanation", "adaptComponentsToTraditionalFormat", "calculateMultilingualCulturalFit", "calculateMultilingualCreativity", "calculateMultilingualMemorability", "calculateMultilingualUniqueness", "patterns", "template", "Math", "floor", "random", "languagePatterns", "EN_US", "JA_JP", "KO_KR", "ES_ES", "FR_FR", "DE_DE", "AR_SA", "includes", "sampled", "sampleByCategory", "EMOTIONS", "morpheme", "position", "role", "contribution_score", "calculateContributionScore", "PROFESSIONS", "CHARACTERISTICS", "OBJECTS", "cultural_preference", "filterByCulturalPreference", "score", "usage_frequency", "cultural_context", "min", "culturalPreference", "preferred", "filter", "c", "selectedMorphemes", "emotionMorphemes", "m", "morphological_info", "pos_tag", "cultural_tags", "tag", "toLowerCase", "selected", "selectBestMorphemeByQuality", "professionMorphemes", "characteristicMorphemes", "randomMorpheme", "morphemes", "scored", "calculateMultilingualMorphemeScore", "sort", "a", "qualityScores", "language_quality_scores", "avgQuality", "naturalness", "fluency", "authenticity", "aesthetic_appeal", "pronunciation_ease", "practicality", "cultural_appropriateness", "native_speaker_rating", "sorted", "index", "char<PERSON>t", "toUpperCase", "slice", "multilingualMorphemes", "id", "morpheme_id", "category", "mapPOSToCategory", "subcategory", "morphological_type", "mapCulturalContext", "semantic_vector", "extractSemanticVector", "tags", "language_properties", "syllable_count", "phonetic_features", "character_count", "ipa_transcription", "pronunciation", "quality_metrics", "frequency", "acceptability", "created_at", "source", "version", "posTag", "mapping", "ACTIONS", "CONCEPTS", "culturalContext", "traditionality", "modernity", "vector", "Array", "fill", "cultural", "formality", "regionality", "religious_sensitivity", "phonetic_harmony", "popularity_trend", "getLanguageSpecificityScore", "languageScores", "calculateMultilingualPronunciation", "semantic_coherence", "calculateMultilingualSemanticCoherence", "calculateMultilingualAestheticAppeal", "practical_usability", "calculateMultilingualPracticalUsability", "weights", "confidence", "calculateMultilingualEvaluationConfidence", "issues", "suggestions", "analyzeMultilingualQualityIssues", "max", "dimensions", "evaluation_time", "algorithm_version", "timestamp", "posTypes", "Set", "diversityScore", "size", "culturalDiversity", "calculateCulturalDiversity", "languageSpecificScore", "calculateLanguageSpecificCreativity", "lengthScore", "calculateLanguageSpecificLengthScore", "calculateLanguageSpecificMemorabilityLength", "phoneticScore", "calculateMultilingualPhoneticMemorability", "semanticScore", "calculateMultilingualSemanticMemorability", "culturalScore", "calculateCulturalMemorability", "avgCulturalAppropriatenesss", "reduce", "sum", "harmonyScore", "calculateMultilingualCulturalHarmony", "modernityScore", "calculateMultilingualModernityScore", "rarityScores", "avgRarity", "r", "languageUniqueness", "calculateLanguageSpecificUniqueness", "combinationScore", "calculateMultilingualCombinationUniqueness", "pronunciationScores", "totalCoherence", "pairCount", "i", "j", "coherence", "calculateMultilingualSemanticRelatedness", "aestheticScores", "practicalityScores", "languageNames", "langName", "morphemeTexts", "culturalScores", "traditionalityVariance", "calculateVariance", "modernityVariance", "formalityVariance", "hasCapitalizedWords", "hasKanjiHiraganaMix", "hasKoreanCharacters", "optimalRanges", "range", "values", "mean", "val", "variance", "pow", "test", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneticScores", "memoryScores", "culturalAttributes", "totalHarmony", "attrs", "modernityScores", "uniquenessScores", "morpheme1", "morpheme2", "tags1", "tags2", "intersection", "has", "union", "calculatePronunciationScore", "calculateSemanticCoherenceScore", "calculateAestheticAppealScore", "calculatePracticalUsabilityScore", "calculateEvaluationConfidence", "analyzeQualityIssues", "evaluationTime", "categoryDiversity", "maxCategories", "semanticDistances", "dist", "calculateSemanticDistance", "avgDistance", "d", "culturalContexts", "calculatePhoneticMemorability", "calculateSemanticMemorability", "structureScore", "calculateStructuralMemorability", "targetContext", "matchingComponents", "directMatchScore", "calculateCulturalHarmony", "calculateModernityScore", "calculateCombinationUniqueness", "lengthUniqueness", "calculateLengthUniqueness", "charUniqueness", "calculateCharacterUniqueness", "syllableFlow", "calculateSyllableFlow", "toneHarmony", "calculateToneHarmony", "<PERSON><PERSON><PERSON><PERSON><PERSON>y", "calculatePronunciationDifficulty", "internationalFriendliness", "calculateInternationalPronunciation", "semanticRelatedness", "calculateSemanticRelatedness", "conceptualConsistency", "calculateConceptualConsistency", "grammaticalReasonableness", "calculateGrammaticalReasonableness", "visualAesthetics", "calculateVisualAesthetics", "phoneticAesthetics", "calculatePhoneticAesthetics", "semanticAesthetics", "calculateSemanticAesthetics", "overallHarmony", "calculateOverallHarmony", "inputConvenience", "calculateInputConvenience", "platformCompatibility", "calculatePlatformCompatibility", "searchFriendliness", "calculateSearchFriendliness", "communicationEase", "calculateCommunicationEase", "vector1", "vector2", "sqrt", "syllableCount", "concreteCategories", "concreteCount", "contexts", "uniqueContexts", "modernCount", "categories", "combination", "commonCombinations", "commonChars", "uniqueScore", "char", "totalSyllables", "totalSimilarity", "similarity", "calculateSemanticSimilarity", "abstractCategories", "abstractCount", "morphTypes", "reasonableCombinations", "isReasonable", "rc", "poeticCategories", "poeticCount", "lengthHarmony", "categoryHarmony", "avgGenerationTime", "morphemeStats", "morpheme_count", "morpheme_stats", "engine_status", "total_generations", "avg_generation_time", "success_rate", "getMultilingualStats", "languageStats", "totalConcepts", "isMultilingualReady", "getMorphemeStats", "destroy"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/engines/CoreGenerationEngine.ts"], "sourcesContent": ["/**\n * 核心生成引擎\n *\n * 负责协调各个组件完成用户名生成，支持新的数据加载机制、\n * 多维度索引查询和高效的采样算法。\n *\n * @fileoverview 核心生成引擎\n * @version 2.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport {\n  type GeneratedUsername,\n  type GenerationContext,\n  type QualityScore,\n  type MorphemeComponent,\n  type Morpheme,\n  type EngineStats,\n  MorphemeCategory\n} from '../../types/core'\nimport { MorphemeRepository } from '../repositories/MorphemeRepository'\nimport { DataLoader } from '../data/DataLoader'\nimport { DataValidator } from '../data/DataValidator'\nimport { LanguageManager } from '../multilingual/LanguageManager'\nimport { SemanticAligner } from '../multilingual/SemanticAligner'\nimport { LanguageCode } from '../../types/multilingual'\n\n/**\n * 核心生成引擎类\n *\n * 提供高效的用户名生成功能，支持多种创意模式、智能质量评估和多语种生成\n */\nexport class CoreGenerationEngine {\n  private morphemeRepo: MorphemeRepository\n  private languageManager: LanguageManager\n  private semanticAligner: SemanticAligner\n  private isInitialized = false\n  private generationCount = 0\n  private totalGenerationTime = 0\n\n  /**\n   * 构造函数\n   *\n   * 初始化生成引擎和依赖组件，支持多语种功能\n   */\n  constructor() {\n    // 使用新的数据加载和验证机制\n    const dataLoader = new DataLoader({\n      enableHotReload: true,\n      enableValidation: true,\n      enableCache: true\n    })\n\n    const dataValidator = new DataValidator({\n      strict_mode: true,\n      skip_warnings: false\n    })\n\n    this.morphemeRepo = new MorphemeRepository(dataLoader, dataValidator)\n\n    // 初始化多语种组件\n    this.languageManager = new LanguageManager({\n      defaultLanguage: LanguageCode.ZH_CN,\n      enabledLanguages: [LanguageCode.ZH_CN], // 暂时只启用中文\n      enableCache: true,\n      cacheTTL: 3600\n    })\n\n    this.semanticAligner = new SemanticAligner({\n      semanticSimilarityThreshold: 0.75,\n      culturalFitWeight: 0.3,\n      semanticWeight: 0.5,\n      qualityWeight: 0.2\n    })\n  }\n\n  /**\n   * 初始化生成引擎\n   *\n   * 加载语素数据、构建索引、验证数据完整性，初始化多语种组件\n   *\n   * @returns Promise<void>\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('🔄 核心生成引擎已初始化，跳过重复初始化')\n      return\n    }\n\n    const startTime = Date.now()\n    console.log('🚀 开始初始化多语种核心生成引擎...')\n\n    try {\n      // 1. 初始化语素仓库（传统中文数据）\n      await this.morphemeRepo.initialize()\n\n      // 验证仓库状态\n      if (!this.morphemeRepo.isReady()) {\n        throw new Error('语素仓库初始化失败')\n      }\n\n      // 2. 初始化多语种语言管理器\n      await this.languageManager.initialize()\n\n      // 验证语言管理器状态\n      if (!this.languageManager.isReady()) {\n        throw new Error('语言管理器初始化失败')\n      }\n\n      // 获取初始化统计信息\n      const stats = this.morphemeRepo.getStats()\n      const supportedLanguages = this.languageManager.getSupportedLanguages()\n\n      this.isInitialized = true\n      const initTime = Date.now() - startTime\n\n      console.log(`✅ 多语种核心生成引擎初始化完成: 耗时${initTime}ms`)\n      console.log(`📊 传统语素: ${stats.total}个, 分布: ${Object.entries(stats.byCategory).map(([k, v]) => `${k}:${v}`).join(', ')}`)\n      console.log(`🌍 支持语言: ${supportedLanguages.join(', ')}`)\n\n      // 输出各语言统计\n      for (const lang of supportedLanguages) {\n        const langStats = this.languageManager.getLanguageStats(lang)\n        console.log(`   ${lang}: ${langStats.morphemeCount}个语素, ${langStats.conceptCount}个概念`)\n      }\n\n    } catch (error) {\n      console.error('❌ 多语种核心生成引擎初始化失败:', error)\n      throw new Error(`多语种核心生成引擎初始化失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 重新加载数据\n   *\n   * 用于热重载场景，支持多语种数据重载\n   *\n   * @returns Promise<void>\n   */\n  async reload(): Promise<void> {\n    console.log('🔄 重新加载多语种核心生成引擎...')\n    this.isInitialized = false\n    await this.morphemeRepo.reload()\n\n    // 重新加载所有支持的语言\n    const supportedLanguages = this.languageManager.getSupportedLanguages()\n    for (const language of supportedLanguages) {\n      await this.languageManager.reloadLanguage(language)\n    }\n\n    await this.initialize()\n  }\n\n  /**\n   * 生成用户名 (传统中文模式)\n   *\n   * 支持1-10个用户名的批量生成，专注单用户场景\n   *\n   * @param context 生成上下文\n   * @param count 生成数量 (1-10)\n   * @returns 生成的用户名数组\n   */\n  async generate(context: GenerationContext, count: number = 5): Promise<GeneratedUsername[]> {\n    return this.generateMultilingual(context, count, LanguageCode.ZH_CN)\n  }\n\n  /**\n   * 多语种用户名生成\n   *\n   * 支持多语种的核心生成方法，基于v3.0多语种架构\n   *\n   * @param context 生成上下文\n   * @param count 生成数量 (1-10)\n   * @param language 目标语言\n   * @returns Promise<GeneratedUsername[]>\n   */\n  async generateMultilingual(\n    context: GenerationContext,\n    count: number = 5,\n    language: LanguageCode = LanguageCode.ZH_CN\n  ): Promise<GeneratedUsername[]> {\n    // 验证参数\n    if (count < 1 || count > 10) {\n      throw new Error('生成数量必须在1-10之间')\n    }\n\n    if (!this.isInitialized) {\n      await this.initialize()\n    }\n\n    if (!this.languageManager.isLanguageSupported(language)) {\n      throw new Error(`不支持的语言: ${language}`)\n    }\n\n    const startTime = Date.now()\n    const results: GeneratedUsername[] = []\n    const maxRetries = count * 3 // 最大重试次数\n\n    console.log(`🌍 开始生成${count}个${language}用户名...`)\n\n    try {\n      let attempts = 0\n      while (results.length < count && attempts < maxRetries) {\n        const username = language === LanguageCode.ZH_CN\n          ? await this.generateSingle(context) // 使用传统中文生成\n          : await this.generateMultilingualSingle(context, language) // 使用多语种生成\n\n        if (username) {\n          // 检查重复\n          const isDuplicate = results.some(existing => existing.text === username.text)\n          if (!isDuplicate) {\n            results.push(username)\n          }\n        }\n        attempts++\n      }\n\n      const generationTime = Date.now() - startTime\n      this.generationCount += results.length\n      this.totalGenerationTime += generationTime\n\n      console.log(`✅ ${language}生成完成: ${results.length}/${count}个用户名, 耗时${generationTime}ms`)\n\n      if (results.length < count) {\n        console.warn(`⚠️ 仅生成了${results.length}个用户名，未达到目标${count}个`)\n      }\n\n      return results\n    } catch (error) {\n      console.error(`❌ ${language}用户名生成失败:`, error)\n      throw new Error(`${language}用户名生成失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 生成单个用户名 (传统中文模式)\n   */\n  private async generateSingle(context: GenerationContext): Promise<GeneratedUsername | null> {\n    const generationStart = Date.now()\n\n    try {\n      // 简化的生成逻辑 - MVP版本\n      const pattern = this.selectPattern(context)\n      const components = await this.selectMorphemes(pattern, context)\n\n      if (components.length === 0) {\n        return null\n      }\n\n      const text = this.combineComponents(components)\n      const qualityScore = this.evaluateQuality(text, components, context)\n\n      // 检查质量阈值\n      if (qualityScore.overall < context.quality_threshold) {\n        return null\n      }\n\n      return {\n        text,\n        pattern: pattern.name,\n        quality_score: qualityScore,\n        explanation: this.generateExplanation(text, components, pattern),\n        components,\n        metadata: {\n          cultural_fit: this.calculateCulturalFitScore(text, components, context),\n          creativity: this.calculateCreativityScore(text, components, context),\n          memorability: this.calculateMemorabilityScore(text, components, context),\n          uniqueness: this.calculateUniquenessScore(text, components, context),\n          generation_time: Date.now() - generationStart\n        }\n      }\n    } catch (error) {\n      console.error('Failed to generate single username:', error)\n      return null\n    }\n  }\n\n  /**\n   * 生成单个多语种用户名\n   *\n   * 基于v3.0多语种架构的单个用户名生成\n   */\n  private async generateMultilingualSingle(\n    context: GenerationContext,\n    language: LanguageCode\n  ): Promise<GeneratedUsername | null> {\n    const generationStart = Date.now()\n\n    try {\n      // 1. 获取目标语言的语素\n      const languageMorphemes = this.languageManager.getMorphemesByLanguage(language)\n      if (languageMorphemes.length === 0) {\n        console.warn(`⚠️ ${language}语言没有可用语素`)\n        return null\n      }\n\n      // 2. 选择创意模式（多语种适配）\n      const pattern = this.selectMultilingualPattern(context, language)\n\n      // 3. 基于语义对齐选择语素\n      const components = await this.selectMultilingualMorphemes(pattern, context, language)\n\n      if (components.length === 0) {\n        return null\n      }\n\n      // 4. 组合语素\n      const text = this.combineMultilingualComponents(components, language)\n\n      // 5. 多语种质量评估\n      const qualityScore = this.evaluateMultilingualQuality(text, components, context, language)\n\n      // 检查质量阈值\n      if (qualityScore.overall < context.quality_threshold) {\n        return null\n      }\n\n      return {\n        text,\n        pattern: pattern.name,\n        quality_score: qualityScore,\n        explanation: this.generateMultilingualExplanation(text, components, pattern, language),\n        components: this.adaptComponentsToTraditionalFormat(components),\n        metadata: {\n          cultural_fit: this.calculateMultilingualCulturalFit(text, components, context, language),\n          creativity: this.calculateMultilingualCreativity(text, components, context, language),\n          memorability: this.calculateMultilingualMemorability(text, components, context, language),\n          uniqueness: this.calculateMultilingualUniqueness(text, components, context, language),\n          generation_time: Date.now() - generationStart\n        }\n      }\n    } catch (error) {\n      console.error(`Failed to generate single ${language} username:`, error)\n      return null\n    }\n  }\n\n  /**\n   * 选择创意模式 (传统中文)\n   */\n  private selectPattern(context: GenerationContext): { name: string; template: string } {\n    // MVP版本使用简化的模式选择\n    const patterns = [\n      { name: '形容词+名词', template: '{adjective}{noun}' },\n      { name: '职业特色', template: '{characteristic}的{profession}' },\n      { name: '可爱萌系', template: '小{adjective}' }\n    ]\n\n    // 简单随机选择\n    return patterns[Math.floor(Math.random() * patterns.length)]\n  }\n\n  /**\n   * 选择多语种创意模式\n   */\n  private selectMultilingualPattern(\n    context: GenerationContext,\n    language: LanguageCode\n  ): { name: string; template: string } {\n    // 根据语言选择适合的模式\n    const languagePatterns: Record<LanguageCode, Array<{ name: string; template: string }>> = {\n      [LanguageCode.ZH_CN]: [\n        { name: '形容词+名词', template: '{adjective}{noun}' },\n        { name: '职业特色', template: '{characteristic}的{profession}' },\n        { name: '可爱萌系', template: '小{adjective}' }\n      ],\n      [LanguageCode.EN_US]: [\n        { name: 'Adjective+Noun', template: '{adjective}{noun}' },\n        { name: 'Professional Style', template: '{profession}{characteristic}' },\n        { name: 'Creative Blend', template: '{characteristic}{emotion}' }\n      ],\n      [LanguageCode.JA_JP]: [\n        { name: '形容詞+名詞', template: '{adjective}{noun}' },\n        { name: '職業系', template: '{profession}{characteristic}' }\n      ],\n      [LanguageCode.KO_KR]: [\n        { name: '형용사+명사', template: '{adjective}{noun}' },\n        { name: '직업형', template: '{profession}{characteristic}' }\n      ],\n      [LanguageCode.ES_ES]: [\n        { name: 'Adjetivo+Sustantivo', template: '{adjective}{noun}' },\n        { name: 'Estilo Profesional', template: '{profession}{characteristic}' }\n      ],\n      [LanguageCode.FR_FR]: [\n        { name: 'Adjectif+Nom', template: '{adjective}{noun}' },\n        { name: 'Style Professionnel', template: '{profession}{characteristic}' }\n      ],\n      [LanguageCode.DE_DE]: [\n        { name: 'Adjektiv+Substantiv', template: '{adjective}{noun}' },\n        { name: 'Berufsstil', template: '{profession}{characteristic}' }\n      ],\n      [LanguageCode.AR_SA]: [\n        { name: 'صفة+اسم', template: '{adjective}{noun}' },\n        { name: 'نمط مهني', template: '{profession}{characteristic}' }\n      ]\n    }\n\n    const patterns = languagePatterns[language] || languagePatterns[LanguageCode.EN_US]\n    return patterns[Math.floor(Math.random() * patterns.length)]\n  }\n\n  /**\n   * 选择语素\n   *\n   * 使用新的采样算法和多维度索引进行智能语素选择\n   *\n   * @private\n   * @param pattern 创意模式\n   * @param context 生成上下文\n   * @returns 语素组件数组\n   */\n  private async selectMorphemes(pattern: { name: string; template: string }, context: GenerationContext): Promise<MorphemeComponent[]> {\n    const components: MorphemeComponent[] = []\n\n    try {\n      // 使用新的高效采样算法\n      if (pattern.template.includes('{adjective}')) {\n        // 优先从情感类语素中采样\n        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.EMOTIONS, 1)\n        if (sampled.length > 0) {\n          components.push({\n            morpheme: sampled[0],\n            position: 0,\n            role: 'modifier',\n            contribution_score: this.calculateContributionScore(sampled[0], context)\n          })\n        }\n      }\n\n      if (pattern.template.includes('{noun}') || pattern.template.includes('{profession}')) {\n        // 从职业类语素中采样\n        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.PROFESSIONS, 1)\n        if (sampled.length > 0) {\n          components.push({\n            morpheme: sampled[0],\n            position: 1,\n            role: 'root',\n            contribution_score: this.calculateContributionScore(sampled[0], context)\n          })\n        }\n      }\n\n      if (pattern.template.includes('{characteristic}')) {\n        // 从特征类语素中采样\n        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.CHARACTERISTICS, 1)\n        if (sampled.length > 0) {\n          components.push({\n            morpheme: sampled[0],\n            position: 0,\n            role: 'modifier',\n            contribution_score: this.calculateContributionScore(sampled[0], context)\n          })\n        }\n      }\n\n      // 如果需要更多语素，可以从其他类别补充\n      if (components.length < 2 && pattern.template.includes('{object}')) {\n        const sampled = this.morphemeRepo.sampleByCategory(MorphemeCategory.OBJECTS, 1)\n        if (sampled.length > 0) {\n          components.push({\n            morpheme: sampled[0],\n            position: components.length,\n            role: 'complement',\n            contribution_score: this.calculateContributionScore(sampled[0], context)\n          })\n        }\n      }\n\n      // 根据文化偏好进行二次筛选\n      if (context.cultural_preference && context.cultural_preference !== 'neutral') {\n        return this.filterByCulturalPreference(components, context.cultural_preference)\n      }\n\n      return components\n    } catch (error) {\n      console.error('❌ 语素选择失败:', error)\n      return []\n    }\n  }\n\n  /**\n   * 计算语素贡献分数\n   *\n   * @private\n   * @param morpheme 语素\n   * @param context 生成上下文\n   * @returns 贡献分数\n   */\n  private calculateContributionScore(morpheme: Morpheme, context: GenerationContext): number {\n    let score = morpheme.quality_score * 0.6 + morpheme.usage_frequency * 0.4\n\n    // 文化适配加成\n    if (context.cultural_preference === morpheme.cultural_context) {\n      score *= 1.2\n    } else if (morpheme.cultural_context === 'neutral') {\n      score *= 1.1\n    }\n\n    // 质量阈值加成\n    if (morpheme.quality_score >= context.quality_threshold) {\n      score *= 1.1\n    }\n\n    return Math.min(score, 1.0)\n  }\n\n  /**\n   * 根据文化偏好筛选语素\n   *\n   * @private\n   * @param components 语素组件数组\n   * @param culturalPreference 文化偏好\n   * @returns 筛选后的语素组件数组\n   */\n  private filterByCulturalPreference(components: MorphemeComponent[], culturalPreference: string): MorphemeComponent[] {\n    // 优先保留匹配文化偏好的语素\n    const preferred = components.filter(c =>\n      c.morpheme.cultural_context === culturalPreference ||\n      c.morpheme.cultural_context === 'neutral'\n    )\n\n    // 如果筛选后语素不足，保留原始结果\n    return preferred.length >= 1 ? preferred : components\n  }\n\n  /**\n   * 选择多语种语素\n   *\n   * 基于语义对齐和概念映射选择目标语言的语素\n   */\n  private async selectMultilingualMorphemes(\n    pattern: { name: string; template: string },\n    context: GenerationContext,\n    language: LanguageCode\n  ): Promise<import('../../types/multilingual').LanguageSpecificMorpheme[]> {\n    const selectedMorphemes: import('../../types/multilingual').LanguageSpecificMorpheme[] = []\n\n    try {\n      // 获取目标语言的所有语素\n      const languageMorphemes = this.languageManager.getMorphemesByLanguage(language)\n\n      // 根据模式模板选择语素\n      if (pattern.template.includes('{adjective}') || pattern.template.includes('{emotion}')) {\n        // 选择情感类语素\n        const emotionMorphemes = languageMorphemes.filter(m =>\n          m.morphological_info.pos_tag === 'ADJ' ||\n          m.cultural_context.cultural_tags.some(tag =>\n            ['emotion', 'feeling', 'mood', 'sentiment'].includes(tag.toLowerCase())\n          )\n        )\n\n        if (emotionMorphemes.length > 0) {\n          const selected = this.selectBestMorphemeByQuality(emotionMorphemes, context)\n          if (selected) selectedMorphemes.push(selected)\n        }\n      }\n\n      if (pattern.template.includes('{noun}') || pattern.template.includes('{profession}')) {\n        // 选择职业类语素\n        const professionMorphemes = languageMorphemes.filter(m =>\n          m.morphological_info.pos_tag === 'NOUN' ||\n          m.cultural_context.cultural_tags.some(tag =>\n            ['profession', 'job', 'career', 'work'].includes(tag.toLowerCase())\n          )\n        )\n\n        if (professionMorphemes.length > 0) {\n          const selected = this.selectBestMorphemeByQuality(professionMorphemes, context)\n          if (selected) selectedMorphemes.push(selected)\n        }\n      }\n\n      if (pattern.template.includes('{characteristic}')) {\n        // 选择特征类语素\n        const characteristicMorphemes = languageMorphemes.filter(m =>\n          m.morphological_info.pos_tag === 'ADJ' ||\n          m.cultural_context.cultural_tags.some(tag =>\n            ['characteristic', 'trait', 'quality', 'attribute'].includes(tag.toLowerCase())\n          )\n        )\n\n        if (characteristicMorphemes.length > 0) {\n          const selected = this.selectBestMorphemeByQuality(characteristicMorphemes, context)\n          if (selected) selectedMorphemes.push(selected)\n        }\n      }\n\n      // 如果没有选到足够的语素，随机补充\n      if (selectedMorphemes.length === 0) {\n        const randomMorpheme = languageMorphemes[Math.floor(Math.random() * languageMorphemes.length)]\n        selectedMorphemes.push(randomMorpheme)\n      }\n\n      return selectedMorphemes\n    } catch (error) {\n      console.error(`❌ 选择${language}语素失败:`, error)\n      return []\n    }\n  }\n\n  /**\n   * 根据质量选择最佳语素\n   */\n  private selectBestMorphemeByQuality(\n    morphemes: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext\n  ): import('../../types/multilingual').LanguageSpecificMorpheme | null {\n    if (morphemes.length === 0) return null\n\n    // 根据质量评分和文化适配度排序\n    const scored = morphemes.map(morpheme => ({\n      morpheme,\n      score: this.calculateMultilingualMorphemeScore(morpheme, context)\n    }))\n\n    scored.sort((a, b) => b.score - a.score)\n    return scored[0].morpheme\n  }\n\n  /**\n   * 计算多语种语素评分\n   */\n  private calculateMultilingualMorphemeScore(\n    morpheme: import('../../types/multilingual').LanguageSpecificMorpheme,\n    context: GenerationContext\n  ): number {\n    let score = 0\n\n    // 基础质量评分 (40%)\n    const qualityScores = morpheme.language_quality_scores\n    const avgQuality = (\n      qualityScores.naturalness +\n      qualityScores.fluency +\n      qualityScores.authenticity +\n      qualityScores.aesthetic_appeal +\n      qualityScores.pronunciation_ease +\n      qualityScores.memorability +\n      qualityScores.uniqueness +\n      qualityScores.practicality\n    ) / 8\n    score += avgQuality * 0.4\n\n    // 文化适配度 (30%)\n    score += morpheme.cultural_appropriateness * 0.3\n\n    // 使用频率 (20%)\n    score += morpheme.usage_frequency * 0.2\n\n    // 母语者评分 (10%)\n    score += morpheme.native_speaker_rating * 0.1\n\n    return Math.min(score, 1.0)\n  }\n\n  /**\n   * 组合语素 (传统中文)\n   */\n  private combineComponents(components: MorphemeComponent[]): string {\n    // 按位置排序\n    const sorted = components.sort((a, b) => a.position - b.position)\n\n    // 简单拼接\n    return sorted.map(c => c.morpheme.text).join('')\n  }\n\n  /**\n   * 组合多语种语素\n   */\n  private combineMultilingualComponents(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    language: LanguageCode\n  ): string {\n    if (components.length === 0) return ''\n\n    // 根据语言特性进行组合\n    switch (language) {\n      case LanguageCode.EN_US:\n        // 英文：首字母大写，驼峰命名\n        return components.map((morpheme, index) => {\n          const text = morpheme.text\n          return index === 0\n            ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()\n            : text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()\n        }).join('')\n\n      case LanguageCode.JA_JP:\n      case LanguageCode.KO_KR:\n        // 日韩文：直接拼接\n        return components.map(m => m.text).join('')\n\n      case LanguageCode.ES_ES:\n      case LanguageCode.FR_FR:\n      case LanguageCode.DE_DE:\n        // 欧洲语言：首字母大写，驼峰命名\n        return components.map((morpheme, index) => {\n          const text = morpheme.text\n          return index === 0\n            ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()\n            : text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()\n        }).join('')\n\n      case LanguageCode.AR_SA:\n        // 阿拉伯文：从右到左，但用户名通常从左到右显示\n        return components.map(m => m.text).join('')\n\n      default:\n        // 默认：简单拼接\n        return components.map(m => m.text).join('')\n    }\n  }\n\n  /**\n   * 适配组件到传统格式\n   *\n   * 将多语种语素适配为传统MorphemeComponent格式以保持兼容性\n   */\n  private adaptComponentsToTraditionalFormat(\n    multilingualMorphemes: import('../../types/multilingual').LanguageSpecificMorpheme[]\n  ): MorphemeComponent[] {\n    return multilingualMorphemes.map((morpheme, index) => ({\n      morpheme: {\n        id: morpheme.morpheme_id,\n        text: morpheme.text,\n        category: this.mapPOSToCategory(morpheme.morphological_info.pos_tag),\n        subcategory: morpheme.morphological_info.morphological_type,\n        cultural_context: this.mapCulturalContext(morpheme.cultural_context),\n        usage_frequency: morpheme.usage_frequency,\n        quality_score: morpheme.native_speaker_rating,\n        semantic_vector: this.extractSemanticVector(morpheme),\n        tags: morpheme.cultural_context.cultural_tags,\n        language_properties: {\n          syllable_count: morpheme.phonetic_features.syllable_count,\n          character_count: morpheme.text.length,\n          phonetic_features: [morpheme.phonetic_features.ipa_transcription],\n          morphological_type: morpheme.morphological_info.morphological_type,\n          pronunciation: morpheme.phonetic_features.ipa_transcription\n        },\n        quality_metrics: {\n          naturalness: morpheme.language_quality_scores.naturalness,\n          frequency: morpheme.usage_frequency,\n          acceptability: morpheme.cultural_appropriateness,\n          aesthetic_appeal: morpheme.language_quality_scores.aesthetic_appeal\n        },\n        created_at: Date.now(),\n        source: morpheme.source,\n        version: morpheme.version as any\n      },\n      position: index,\n      role: index === 0 ? 'root' : 'modifier',\n      contribution_score: morpheme.native_speaker_rating\n    }))\n  }\n\n  /**\n   * 映射词性到类别\n   */\n  private mapPOSToCategory(posTag: string): MorphemeCategory {\n    const mapping: Record<string, MorphemeCategory> = {\n      'ADJ': MorphemeCategory.CHARACTERISTICS,\n      'NOUN': MorphemeCategory.PROFESSIONS,\n      'VERB': MorphemeCategory.ACTIONS,\n      'ADV': MorphemeCategory.CHARACTERISTICS\n    }\n    return mapping[posTag] || MorphemeCategory.CONCEPTS\n  }\n\n  /**\n   * 映射文化语境\n   */\n  private mapCulturalContext(\n    culturalContext: import('../../types/multilingual').LanguageSpecificMorpheme['cultural_context']\n  ): import('../../types/core').CulturalContext {\n    // 基于传统性和现代性评分映射\n    if (culturalContext.traditionality > 0.7) {\n      return 'ancient' as any\n    } else if (culturalContext.modernity > 0.7) {\n      return 'modern' as any\n    } else {\n      return 'neutral' as any\n    }\n  }\n\n  /**\n   * 提取语义向量\n   */\n  private extractSemanticVector(\n    morpheme: import('../../types/multilingual').LanguageSpecificMorpheme\n  ): number[] {\n    // 基于语素的各种特征构建20维语义向量\n    const vector: number[] = new Array(20).fill(0)\n\n    const qualityScores = morpheme.language_quality_scores\n    vector[0] = qualityScores.naturalness\n    vector[1] = qualityScores.fluency\n    vector[2] = qualityScores.authenticity\n    vector[3] = qualityScores.aesthetic_appeal\n    vector[4] = qualityScores.pronunciation_ease\n    vector[5] = qualityScores.memorability\n    vector[6] = qualityScores.uniqueness\n    vector[7] = qualityScores.practicality\n\n    const cultural = morpheme.cultural_context\n    vector[8] = cultural.traditionality\n    vector[9] = cultural.modernity\n    vector[10] = cultural.formality\n    vector[11] = cultural.regionality\n    vector[12] = cultural.religious_sensitivity\n\n    vector[13] = morpheme.phonetic_features.phonetic_harmony\n    vector[14] = morpheme.phonetic_features.syllable_count / 5\n    vector[15] = morpheme.usage_frequency\n    vector[16] = morpheme.native_speaker_rating\n    vector[17] = morpheme.cultural_appropriateness\n    vector[18] = morpheme.popularity_trend\n    vector[19] = this.getLanguageSpecificityScore(morpheme.language)\n\n    return vector\n  }\n\n  /**\n   * 计算语言特异性评分\n   */\n  private getLanguageSpecificityScore(language: LanguageCode): number {\n    // 基于语言的特异性评分\n    const languageScores: Record<LanguageCode, number> = {\n      [LanguageCode.ZH_CN]: 0.8, // 中文特异性较高\n      [LanguageCode.EN_US]: 0.6, // 英文通用性较强\n      [LanguageCode.JA_JP]: 0.9, // 日文特异性很高\n      [LanguageCode.KO_KR]: 0.85, // 韩文特异性高\n      [LanguageCode.ES_ES]: 0.5, // 西班牙文通用性强\n      [LanguageCode.FR_FR]: 0.55, // 法文通用性较强\n      [LanguageCode.DE_DE]: 0.65, // 德文特异性中等\n      [LanguageCode.AR_SA]: 0.95  // 阿拉伯文特异性最高\n    }\n\n    return languageScores[language] || 0.5\n  }\n\n  /**\n   * 多语种质量评估\n   */\n  private evaluateMultilingualQuality(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): QualityScore {\n    const startTime = Date.now()\n\n    // 基于多语种语素的质量评分计算8维度评分\n    const creativity = this.calculateMultilingualCreativity(text, components, context, language)\n    const memorability = this.calculateMultilingualMemorability(text, components, context, language)\n    const cultural_fit = this.calculateMultilingualCulturalFit(text, components, context, language)\n    const uniqueness = this.calculateMultilingualUniqueness(text, components, context, language)\n    const pronunciation = this.calculateMultilingualPronunciation(text, components, context, language)\n    const semantic_coherence = this.calculateMultilingualSemanticCoherence(text, components, context, language)\n    const aesthetic_appeal = this.calculateMultilingualAestheticAppeal(text, components, context, language)\n    const practical_usability = this.calculateMultilingualPracticalUsability(text, components, context, language)\n\n    // 计算综合评分\n    const weights = {\n      creativity: 0.15,\n      memorability: 0.20,\n      cultural_fit: 0.15,\n      uniqueness: 0.12,\n      pronunciation: 0.13,\n      semantic_coherence: 0.10,\n      aesthetic_appeal: 0.08,\n      practical_usability: 0.07\n    }\n\n    const overall = (\n      creativity * weights.creativity +\n      memorability * weights.memorability +\n      cultural_fit * weights.cultural_fit +\n      uniqueness * weights.uniqueness +\n      pronunciation * weights.pronunciation +\n      semantic_coherence * weights.semantic_coherence +\n      aesthetic_appeal * weights.aesthetic_appeal +\n      practical_usability * weights.practical_usability\n    )\n\n    const confidence = this.calculateMultilingualEvaluationConfidence(text, components, context, language)\n    const { issues, suggestions } = this.analyzeMultilingualQualityIssues(text, components, language, {\n      creativity, memorability, cultural_fit, uniqueness,\n      pronunciation, semantic_coherence, aesthetic_appeal, practical_usability\n    })\n\n    return {\n      overall: Math.min(Math.max(overall, 0), 1),\n      dimensions: {\n        creativity,\n        memorability,\n        cultural_fit,\n        uniqueness,\n        pronunciation,\n        semantic_coherence,\n        aesthetic_appeal,\n        practical_usability\n      },\n      confidence,\n      evaluation_time: Date.now() - startTime,\n      algorithm_version: '3.0.0-multilingual',\n      issues,\n      suggestions,\n      timestamp: Date.now()\n    }\n  }\n\n  /**\n   * 多语种创意性评分\n   */\n  private calculateMultilingualCreativity(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    let score = 0\n\n    // 1. 语素组合新颖性 (40%)\n    const posTypes = new Set(components.map(c => c.morphological_info.pos_tag))\n    const diversityScore = posTypes.size / Math.min(components.length, 3)\n    score += diversityScore * 0.4\n\n    // 2. 跨文化创新性 (30%)\n    const culturalDiversity = this.calculateCulturalDiversity(components)\n    score += culturalDiversity * 0.3\n\n    // 3. 语言特色创新 (20%)\n    const languageSpecificScore = this.calculateLanguageSpecificCreativity(text, language)\n    score += languageSpecificScore * 0.2\n\n    // 4. 长度适配性 (10%)\n    const lengthScore = this.calculateLanguageSpecificLengthScore(text, language)\n    score += lengthScore * 0.1\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 多语种记忆性评分\n   */\n  private calculateMultilingualMemorability(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    let score = 0\n\n    // 1. 语言特定长度记忆性 (35%)\n    const lengthScore = this.calculateLanguageSpecificMemorabilityLength(text, language)\n    score += lengthScore * 0.35\n\n    // 2. 音韵记忆性 (25%)\n    const phoneticScore = this.calculateMultilingualPhoneticMemorability(components, language)\n    score += phoneticScore * 0.25\n\n    // 3. 语义记忆性 (25%)\n    const semanticScore = this.calculateMultilingualSemanticMemorability(components)\n    score += semanticScore * 0.25\n\n    // 4. 文化记忆性 (15%)\n    const culturalScore = this.calculateCulturalMemorability(components, language)\n    score += culturalScore * 0.15\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 多语种文化适配度评分\n   */\n  private calculateMultilingualCulturalFit(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    let score = 0\n\n    // 1. 语言文化一致性 (50%)\n    const avgCulturalAppropriatenesss = components.reduce((sum, c) => sum + c.cultural_appropriateness, 0) / components.length\n    score += avgCulturalAppropriatenesss * 0.5\n\n    // 2. 跨文化和谐性 (30%)\n    const harmonyScore = this.calculateMultilingualCulturalHarmony(components)\n    score += harmonyScore * 0.3\n\n    // 3. 现代适应性 (20%)\n    const modernityScore = this.calculateMultilingualModernityScore(components, language)\n    score += modernityScore * 0.2\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 多语种独特性评分\n   */\n  private calculateMultilingualUniqueness(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    let score = 0\n\n    // 1. 语素稀有性 (40%)\n    const rarityScores = components.map(c => 1 - c.usage_frequency)\n    const avgRarity = rarityScores.reduce((sum, r) => sum + r, 0) / rarityScores.length\n    score += avgRarity * 0.4\n\n    // 2. 语言特定独特性 (35%)\n    const languageUniqueness = this.calculateLanguageSpecificUniqueness(text, language)\n    score += languageUniqueness * 0.35\n\n    // 3. 组合独特性 (25%)\n    const combinationScore = this.calculateMultilingualCombinationUniqueness(components)\n    score += combinationScore * 0.25\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 多语种发音友好度评分\n   */\n  private calculateMultilingualPronunciation(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    // 基于语素的发音友好度评分\n    const pronunciationScores = components.map(c => c.language_quality_scores.pronunciation_ease)\n    return pronunciationScores.reduce((sum, score) => sum + score, 0) / pronunciationScores.length\n  }\n\n  /**\n   * 多语种语义连贯性评分\n   */\n  private calculateMultilingualSemanticCoherence(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    if (components.length < 2) return 0.8\n\n    // 基于语素间的语义相关性\n    let totalCoherence = 0\n    let pairCount = 0\n\n    for (let i = 0; i < components.length - 1; i++) {\n      for (let j = i + 1; j < components.length; j++) {\n        const coherence = this.calculateMultilingualSemanticRelatedness(components[i], components[j])\n        totalCoherence += coherence\n        pairCount++\n      }\n    }\n\n    return pairCount > 0 ? totalCoherence / pairCount : 0.5\n  }\n\n  /**\n   * 多语种美学吸引力评分\n   */\n  private calculateMultilingualAestheticAppeal(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    // 基于语素的美学评分\n    const aestheticScores = components.map(c => c.language_quality_scores.aesthetic_appeal)\n    return aestheticScores.reduce((sum, score) => sum + score, 0) / aestheticScores.length\n  }\n\n  /**\n   * 多语种实用性评分\n   */\n  private calculateMultilingualPracticalUsability(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    // 基于语素的实用性评分\n    const practicalityScores = components.map(c => c.language_quality_scores.practicality)\n    return practicalityScores.reduce((sum, score) => sum + score, 0) / practicalityScores.length\n  }\n\n  /**\n   * 多语种解释生成\n   */\n  private generateMultilingualExplanation(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    pattern: { name: string; template: string },\n    language: LanguageCode\n  ): string {\n    const languageNames: Record<LanguageCode, string> = {\n      [LanguageCode.ZH_CN]: '中文',\n      [LanguageCode.EN_US]: 'English',\n      [LanguageCode.JA_JP]: '日本語',\n      [LanguageCode.KO_KR]: '한국어',\n      [LanguageCode.ES_ES]: 'Español',\n      [LanguageCode.FR_FR]: 'Français',\n      [LanguageCode.DE_DE]: 'Deutsch',\n      [LanguageCode.AR_SA]: 'العربية'\n    }\n\n    const langName = languageNames[language] || language\n    const morphemeTexts = components.map(c => c.text).join(' + ')\n\n    return `${langName}用户名\"${text}\"采用${pattern.name}模式，由语素[${morphemeTexts}]组合而成，体现了${language}语言的文化特色和语言美感。`\n  }\n\n  // ============================================================================\n  // 多语种辅助方法\n  // ============================================================================\n\n  /**\n   * 计算文化多样性\n   */\n  private calculateCulturalDiversity(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[]\n  ): number {\n    const culturalScores = components.map(c => ({\n      traditionality: c.cultural_context.traditionality,\n      modernity: c.cultural_context.modernity,\n      formality: c.cultural_context.formality\n    }))\n\n    // 计算文化维度的方差作为多样性指标\n    const traditionalityVariance = this.calculateVariance(culturalScores.map(c => c.traditionality))\n    const modernityVariance = this.calculateVariance(culturalScores.map(c => c.modernity))\n    const formalityVariance = this.calculateVariance(culturalScores.map(c => c.formality))\n\n    return (traditionalityVariance + modernityVariance + formalityVariance) / 3\n  }\n\n  /**\n   * 计算语言特定创意性\n   */\n  private calculateLanguageSpecificCreativity(text: string, language: LanguageCode): number {\n    // 根据不同语言的特点计算创意性\n    switch (language) {\n      case LanguageCode.EN_US:\n        // 英文：驼峰命名、词汇组合创新\n        return this.hasCapitalizedWords(text) ? 0.8 : 0.6\n      case LanguageCode.JA_JP:\n        // 日文：假名汉字混合的创新性\n        return this.hasKanjiHiraganaMix(text) ? 0.9 : 0.7\n      case LanguageCode.KO_KR:\n        // 韩文：韩文字符的组合创新\n        return this.hasKoreanCharacters(text) ? 0.8 : 0.6\n      default:\n        return 0.7\n    }\n  }\n\n  /**\n   * 计算语言特定长度评分\n   */\n  private calculateLanguageSpecificLengthScore(text: string, language: LanguageCode): number {\n    const length = text.length\n\n    // 不同语言的最佳长度范围\n    const optimalRanges: Record<LanguageCode, { min: number; max: number }> = {\n      [LanguageCode.ZH_CN]: { min: 2, max: 4 },\n      [LanguageCode.EN_US]: { min: 6, max: 12 },\n      [LanguageCode.JA_JP]: { min: 3, max: 6 },\n      [LanguageCode.KO_KR]: { min: 3, max: 6 },\n      [LanguageCode.ES_ES]: { min: 6, max: 12 },\n      [LanguageCode.FR_FR]: { min: 6, max: 12 },\n      [LanguageCode.DE_DE]: { min: 6, max: 15 },\n      [LanguageCode.AR_SA]: { min: 4, max: 8 }\n    }\n\n    const range = optimalRanges[language] || { min: 4, max: 10 }\n\n    if (length >= range.min && length <= range.max) {\n      return 1.0\n    } else if (length < range.min) {\n      return Math.max(0.3, length / range.min)\n    } else {\n      return Math.max(0.3, range.max / length)\n    }\n  }\n\n  /**\n   * 计算方差\n   */\n  private calculateVariance(values: number[]): number {\n    if (values.length === 0) return 0\n    const mean = values.reduce((sum, val) => sum + val, 0) / values.length\n    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length\n    return variance\n  }\n\n  /**\n   * 检查是否有大写单词\n   */\n  private hasCapitalizedWords(text: string): boolean {\n    return /[A-Z]/.test(text)\n  }\n\n  /**\n   * 检查是否有汉字假名混合\n   */\n  private hasKanjiHiraganaMix(text: string): boolean {\n    const hasKanji = /[\\u4e00-\\u9faf]/.test(text)\n    const hasHiragana = /[\\u3040-\\u309f]/.test(text)\n    return hasKanji && hasHiragana\n  }\n\n  /**\n   * 检查是否有韩文字符\n   */\n  private hasKoreanCharacters(text: string): boolean {\n    return /[\\uac00-\\ud7af]/.test(text)\n  }\n\n  // 其他简化的多语种辅助方法\n  private calculateLanguageSpecificMemorabilityLength(text: string, language: LanguageCode): number {\n    return this.calculateLanguageSpecificLengthScore(text, language)\n  }\n\n  private calculateMultilingualPhoneticMemorability(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    language: LanguageCode\n  ): number {\n    const phoneticScores = components.map(c => c.phonetic_features.phonetic_harmony)\n    return phoneticScores.reduce((sum, score) => sum + score, 0) / phoneticScores.length\n  }\n\n  private calculateMultilingualSemanticMemorability(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[]\n  ): number {\n    const memoryScores = components.map(c => c.language_quality_scores.memorability)\n    return memoryScores.reduce((sum, score) => sum + score, 0) / memoryScores.length\n  }\n\n  private calculateCulturalMemorability(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    language: LanguageCode\n  ): number {\n    return components.reduce((sum, c) => sum + c.cultural_appropriateness, 0) / components.length\n  }\n\n  private calculateMultilingualCulturalHarmony(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[]\n  ): number {\n    if (components.length < 2) return 1.0\n\n    // 计算文化属性的一致性\n    const culturalAttributes = components.map(c => [\n      c.cultural_context.traditionality,\n      c.cultural_context.modernity,\n      c.cultural_context.formality\n    ])\n\n    let totalHarmony = 0\n    for (let i = 0; i < culturalAttributes[0].length; i++) {\n      const values = culturalAttributes.map(attrs => attrs[i])\n      const variance = this.calculateVariance(values)\n      totalHarmony += (1 - Math.min(variance, 1))\n    }\n\n    return totalHarmony / culturalAttributes[0].length\n  }\n\n  private calculateMultilingualModernityScore(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    language: LanguageCode\n  ): number {\n    const modernityScores = components.map(c => c.cultural_context.modernity)\n    return modernityScores.reduce((sum, score) => sum + score, 0) / modernityScores.length\n  }\n\n  private calculateLanguageSpecificUniqueness(text: string, language: LanguageCode): number {\n    // 基于语言特定的独特性计算\n    return 0.7 // 简化实现\n  }\n\n  private calculateMultilingualCombinationUniqueness(\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[]\n  ): number {\n    // 基于语素组合的独特性\n    const uniquenessScores = components.map(c => c.language_quality_scores.uniqueness)\n    return uniquenessScores.reduce((sum, score) => sum + score, 0) / uniquenessScores.length\n  }\n\n  private calculateMultilingualSemanticRelatedness(\n    morpheme1: import('../../types/multilingual').LanguageSpecificMorpheme,\n    morpheme2: import('../../types/multilingual').LanguageSpecificMorpheme\n  ): number {\n    // 基于文化标签的相关性\n    const tags1 = new Set(morpheme1.cultural_context.cultural_tags)\n    const tags2 = new Set(morpheme2.cultural_context.cultural_tags)\n    const intersection = new Set([...tags1].filter(tag => tags2.has(tag)))\n    const union = new Set([...tags1, ...tags2])\n\n    return union.size > 0 ? intersection.size / union.size : 0.5\n  }\n\n  private calculateMultilingualEvaluationConfidence(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    context: GenerationContext,\n    language: LanguageCode\n  ): number {\n    // 基于语素质量的置信度\n    const qualityScores = components.map(c => c.native_speaker_rating)\n    const avgQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length\n    const variance = this.calculateVariance(qualityScores)\n\n    return avgQuality * (1 - Math.min(variance, 1))\n  }\n\n  private analyzeMultilingualQualityIssues(\n    text: string,\n    components: import('../../types/multilingual').LanguageSpecificMorpheme[],\n    language: LanguageCode,\n    dimensions: any\n  ): { issues: string[]; suggestions: string[] } {\n    const issues: string[] = []\n    const suggestions: string[] = []\n\n    // 基于维度评分分析问题\n    if (dimensions.pronunciation < 0.6) {\n      issues.push(`${language}发音难度较高`)\n      suggestions.push(`选择发音更简单的${language}语素`)\n    }\n\n    if (dimensions.cultural_fit < 0.7) {\n      issues.push(`${language}文化适配度不足`)\n      suggestions.push(`增强${language}文化特色元素`)\n    }\n\n    if (dimensions.memorability < 0.6) {\n      issues.push(`${language}记忆性较低`)\n      suggestions.push(`优化${language}用户名长度和结构`)\n    }\n\n    return { issues, suggestions }\n  }\n\n  /**\n   * 评估质量\n   *\n   * 基于8维度质量评估体系和第一性原理的精确质量评分算法\n   *\n   * @private\n   * @param text 生成的用户名文本\n   * @param components 语素组件数组\n   * @param context 生成上下文\n   * @returns 详细的质量评分结果\n   */\n  private evaluateQuality(text: string, components: MorphemeComponent[], context: GenerationContext): QualityScore {\n    const startTime = Date.now()\n\n    // 计算8个维度的质量评分\n    const creativity = this.calculateCreativityScore(text, components, context)\n    const memorability = this.calculateMemorabilityScore(text, components, context)\n    const cultural_fit = this.calculateCulturalFitScore(text, components, context)\n    const uniqueness = this.calculateUniquenessScore(text, components, context)\n    const pronunciation = this.calculatePronunciationScore(text, components, context)\n    const semantic_coherence = this.calculateSemanticCoherenceScore(text, components, context)\n    const aesthetic_appeal = this.calculateAestheticAppealScore(text, components, context)\n    const practical_usability = this.calculatePracticalUsabilityScore(text, components, context)\n\n    // 计算综合评分 - 基于认知心理学权重分配\n    const weights = {\n      creativity: 0.15,        // 创意性 - 用户表达个性的需求\n      memorability: 0.20,      // 记忆性 - 认知便利性的核心\n      cultural_fit: 0.15,      // 文化适配 - 社交归属感需求\n      uniqueness: 0.12,        // 独特性 - 个体差异化需求\n      pronunciation: 0.13,     // 发音友好 - 交流便利性\n      semantic_coherence: 0.10, // 语义连贯 - 逻辑理解需求\n      aesthetic_appeal: 0.08,   // 美学吸引 - 审美愉悦需求\n      practical_usability: 0.07 // 实用性 - 使用便利性\n    }\n\n    const overall = (\n      creativity * weights.creativity +\n      memorability * weights.memorability +\n      cultural_fit * weights.cultural_fit +\n      uniqueness * weights.uniqueness +\n      pronunciation * weights.pronunciation +\n      semantic_coherence * weights.semantic_coherence +\n      aesthetic_appeal * weights.aesthetic_appeal +\n      practical_usability * weights.practical_usability\n    )\n\n    // 计算评估置信度\n    const confidence = this.calculateEvaluationConfidence(text, components, context)\n\n    // 识别问题和建议\n    const { issues, suggestions } = this.analyzeQualityIssues(text, components, {\n      creativity, memorability, cultural_fit, uniqueness,\n      pronunciation, semantic_coherence, aesthetic_appeal, practical_usability\n    })\n\n    const evaluationTime = Date.now() - startTime\n\n    return {\n      overall: Math.min(Math.max(overall, 0), 1), // 确保在[0,1]范围内\n      dimensions: {\n        creativity,\n        memorability,\n        cultural_fit,\n        uniqueness,\n        pronunciation,\n        semantic_coherence,\n        aesthetic_appeal,\n        practical_usability\n      },\n      confidence,\n      evaluation_time: evaluationTime,\n      algorithm_version: '2.0.0',\n      issues,\n      suggestions,\n      timestamp: Date.now()\n    }\n  }\n\n  /**\n   * 计算创意性评分\n   *\n   * 基于语素组合的新颖性、语义距离和创新模式\n   *\n   * @private\n   */\n  private calculateCreativityScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 语素组合新颖性 (40%)\n    const categoryDiversity = new Set(components.map(c => c.morpheme.category)).size\n    const maxCategories = Math.min(components.length, 3)\n    const diversityScore = categoryDiversity / maxCategories\n    score += diversityScore * 0.4\n\n    // 2. 语义距离 (30%) - 不同语义向量的距离表示创意性\n    if (components.length >= 2) {\n      const semanticDistances = []\n      for (let i = 0; i < components.length - 1; i++) {\n        const dist = this.calculateSemanticDistance(\n          components[i].morpheme.semantic_vector,\n          components[i + 1].morpheme.semantic_vector\n        )\n        semanticDistances.push(dist)\n      }\n      const avgDistance = semanticDistances.reduce((sum, d) => sum + d, 0) / semanticDistances.length\n      score += Math.min(avgDistance, 1) * 0.3\n    } else {\n      score += 0.15 // 单语素的基础创意分\n    }\n\n    // 3. 文化创新性 (20%) - 跨文化语境的组合\n    const culturalContexts = new Set(components.map(c => c.morpheme.cultural_context))\n    if (culturalContexts.size > 1) {\n      score += 0.2\n    } else {\n      score += 0.1\n    }\n\n    // 4. 长度创新性 (10%) - 适中长度更有创意空间\n    const lengthScore = text.length >= 3 && text.length <= 8 ? 1 : 0.5\n    score += lengthScore * 0.1\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算记忆性评分\n   *\n   * 基于认知负荷理论和记忆心理学原理\n   *\n   * @private\n   */\n  private calculateMemorabilityScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 长度记忆性 (35%) - 基于Miller's Rule (7±2)\n    const length = text.length\n    let lengthScore = 0\n    if (length >= 2 && length <= 4) {\n      lengthScore = 1.0 // 最佳记忆长度\n    } else if (length >= 5 && length <= 6) {\n      lengthScore = 0.9\n    } else if (length >= 7 && length <= 8) {\n      lengthScore = 0.7\n    } else {\n      lengthScore = 0.4\n    }\n    score += lengthScore * 0.35\n\n    // 2. 音韵记忆性 (25%) - 基于语音相似性和节奏\n    const phoneticScore = this.calculatePhoneticMemorability(text, components)\n    score += phoneticScore * 0.25\n\n    // 3. 语义记忆性 (25%) - 具象概念比抽象概念更易记忆\n    const semanticScore = this.calculateSemanticMemorability(components)\n    score += semanticScore * 0.25\n\n    // 4. 结构记忆性 (15%) - 规律性结构更易记忆\n    const structureScore = this.calculateStructuralMemorability(text, components)\n    score += structureScore * 0.15\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算文化适配度评分\n   *\n   * 基于文化心理学和社会认同理论\n   *\n   * @private\n   */\n  private calculateCulturalFitScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 直接文化匹配 (50%)\n    const targetContext = context.cultural_preference || 'neutral'\n    const matchingComponents = components.filter(c =>\n      c.morpheme.cultural_context === targetContext ||\n      c.morpheme.cultural_context === 'neutral'\n    )\n    const directMatchScore = matchingComponents.length / components.length\n    score += directMatchScore * 0.5\n\n    // 2. 文化和谐性 (30%) - 不同文化语境的和谐程度\n    const culturalContexts = components.map(c =>\n      typeof c.morpheme.cultural_context === 'string'\n        ? c.morpheme.cultural_context\n        : `${c.morpheme.cultural_context.traditionality}_${c.morpheme.cultural_context.formality}`\n    )\n    const harmonyScore = this.calculateCulturalHarmony(culturalContexts)\n    score += harmonyScore * 0.3\n\n    // 3. 时代适应性 (20%) - 现代使用场景的适应度\n    const modernityScore = this.calculateModernityScore(components)\n    score += modernityScore * 0.2\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算独特性评分\n   *\n   * 基于信息论和统计稀有性\n   *\n   * @private\n   */\n  private calculateUniquenessScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 语素稀有性 (40%) - 基于使用频率的反向计算\n    const rarityScores = components.map(c => 1 - c.morpheme.usage_frequency)\n    const avgRarity = rarityScores.reduce((sum, r) => sum + r, 0) / rarityScores.length\n    score += avgRarity * 0.4\n\n    // 2. 组合独特性 (35%) - 语素组合的稀有程度\n    const combinationScore = this.calculateCombinationUniqueness(components)\n    score += combinationScore * 0.35\n\n    // 3. 长度独特性 (15%) - 非常见长度的加分\n    const lengthUniqueness = this.calculateLengthUniqueness(text.length)\n    score += lengthUniqueness * 0.15\n\n    // 4. 字符独特性 (10%) - 特殊字符或罕见字符\n    const charUniqueness = this.calculateCharacterUniqueness(text)\n    score += charUniqueness * 0.1\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算发音友好度评分\n   *\n   * 基于语音学和发音便利性原理\n   *\n   * @private\n   */\n  private calculatePronunciationScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 音节流畅性 (40%) - 音节组合的流畅程度\n    const syllableFlow = this.calculateSyllableFlow(components)\n    score += syllableFlow * 0.4\n\n    // 2. 声调和谐性 (30%) - 中文声调的和谐程度\n    const toneHarmony = this.calculateToneHarmony(components)\n    score += toneHarmony * 0.3\n\n    // 3. 发音难度 (20%) - 基于音素复杂度\n    const pronunciationDifficulty = this.calculatePronunciationDifficulty(text)\n    score += (1 - pronunciationDifficulty) * 0.2\n\n    // 4. 国际化友好度 (10%) - 跨语言发音便利性\n    const internationalFriendliness = this.calculateInternationalPronunciation(text)\n    score += internationalFriendliness * 0.1\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算语义连贯性评分\n   *\n   * 基于语义学和认知连贯性理论\n   *\n   * @private\n   */\n  private calculateSemanticCoherenceScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 语义相关性 (50%) - 语素间的语义关联度\n    if (components.length >= 2) {\n      const semanticRelatedness = this.calculateSemanticRelatedness(components)\n      score += semanticRelatedness * 0.5\n    } else {\n      score += 0.25 // 单语素的基础连贯性\n    }\n\n    // 2. 概念层次一致性 (30%) - 抽象层次的一致性\n    const conceptualConsistency = this.calculateConceptualConsistency(components)\n    score += conceptualConsistency * 0.3\n\n    // 3. 语法合理性 (20%) - 语法结构的合理性\n    const grammaticalReasonableness = this.calculateGrammaticalReasonableness(components)\n    score += grammaticalReasonableness * 0.2\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算美学吸引力评分\n   *\n   * 基于美学心理学和视觉认知理论\n   *\n   * @private\n   */\n  private calculateAestheticAppealScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 字形美感 (35%) - 汉字字形的美学价值\n    const visualAesthetics = this.calculateVisualAesthetics(text)\n    score += visualAesthetics * 0.35\n\n    // 2. 音韵美感 (30%) - 音韵的美学价值\n    const phoneticAesthetics = this.calculatePhoneticAesthetics(components)\n    score += phoneticAesthetics * 0.3\n\n    // 3. 意境美感 (25%) - 语义意境的美学价值\n    const semanticAesthetics = this.calculateSemanticAesthetics(components)\n    score += semanticAesthetics * 0.25\n\n    // 4. 整体和谐性 (10%) - 整体的美学和谐度\n    const overallHarmony = this.calculateOverallHarmony(text, components)\n    score += overallHarmony * 0.1\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 计算实用性评分\n   *\n   * 基于使用便利性和实际应用场景\n   *\n   * @private\n   */\n  private calculatePracticalUsabilityScore(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let score = 0\n\n    // 1. 输入便利性 (30%) - 键盘输入的便利程度\n    const inputConvenience = this.calculateInputConvenience(text)\n    score += inputConvenience * 0.3\n\n    // 2. 平台兼容性 (25%) - 各平台的兼容性\n    const platformCompatibility = this.calculatePlatformCompatibility(text)\n    score += platformCompatibility * 0.25\n\n    // 3. 搜索友好性 (25%) - 搜索引擎友好程度\n    const searchFriendliness = this.calculateSearchFriendliness(text)\n    score += searchFriendliness * 0.25\n\n    // 4. 传播便利性 (20%) - 口头传播的便利性\n    const communicationEase = this.calculateCommunicationEase(text)\n    score += communicationEase * 0.2\n\n    return Math.min(score, 1)\n  }\n\n  /**\n   * 生成解释\n   */\n  private generateExplanation(text: string, components: MorphemeComponent[], pattern: { name: string }): string {\n    const morphemeTexts = components.map(c => c.morpheme.text).join('、')\n    return `使用${pattern.name}模式，结合语素：${morphemeTexts}，生成具有${components[0]?.morpheme.tags[0] || '特色'}风格的用户名\"${text}\"`\n  }\n\n  // ============================================================================\n  // 辅助计算方法\n  // ============================================================================\n\n  /**\n   * 计算语义距离\n   *\n   * @private\n   */\n  private calculateSemanticDistance(vector1: number[], vector2: number[]): number {\n    // 使用欧几里得距离\n    let sum = 0\n    for (let i = 0; i < Math.min(vector1.length, vector2.length); i++) {\n      sum += Math.pow(vector1[i] - vector2[i], 2)\n    }\n    return Math.sqrt(sum) / Math.sqrt(vector1.length) // 归一化\n  }\n\n  /**\n   * 计算语音记忆性\n   *\n   * @private\n   */\n  private calculatePhoneticMemorability(text: string, components: MorphemeComponent[]): number {\n    // 简化实现：基于音节数量和重复音素\n    const syllableCount = components.reduce((sum, c) => sum + (c.morpheme.language_properties?.syllable_count || 1), 0)\n\n    // 2-4个音节最易记忆\n    if (syllableCount >= 2 && syllableCount <= 4) {\n      return 0.9\n    } else if (syllableCount <= 6) {\n      return 0.7\n    } else {\n      return 0.5\n    }\n  }\n\n  /**\n   * 计算语义记忆性\n   *\n   * @private\n   */\n  private calculateSemanticMemorability(components: MorphemeComponent[]): number {\n    // 具象概念比抽象概念更易记忆\n    const concreteCategories = ['professions', 'objects', 'actions']\n    const concreteCount = components.filter(c => concreteCategories.includes(c.morpheme.category)).length\n    return concreteCount / components.length\n  }\n\n  /**\n   * 计算结构记忆性\n   *\n   * @private\n   */\n  private calculateStructuralMemorability(text: string, components: MorphemeComponent[]): number {\n    // 规律性结构更易记忆\n    if (components.length === 2) {\n      return 0.9 // 双字结构最规律\n    } else if (components.length === 3) {\n      return 0.8\n    } else {\n      return 0.6\n    }\n  }\n\n  /**\n   * 计算文化和谐性\n   *\n   * @private\n   */\n  private calculateCulturalHarmony(contexts: string[]): number {\n    const uniqueContexts = new Set(contexts)\n\n    // 单一文化语境最和谐\n    if (uniqueContexts.size === 1) {\n      return 1.0\n    }\n\n    // 包含neutral的组合较和谐\n    if (uniqueContexts.has('neutral')) {\n      return 0.8\n    }\n\n    // 多文化混合\n    return 0.6\n  }\n\n  /**\n   * 计算现代性评分\n   *\n   * @private\n   */\n  private calculateModernityScore(components: MorphemeComponent[]): number {\n    const modernCount = components.filter(c => {\n      const context = c.morpheme.cultural_context\n      if (typeof context === 'string') {\n        return context === 'modern' || context === 'neutral'\n      } else {\n        // 对于多维度文化语境，检查现代性指标\n        return context.modernity >= 0.7 || context.traditionality <= 0.3\n      }\n    }).length\n    return modernCount / components.length\n  }\n\n  /**\n   * 计算组合独特性\n   *\n   * @private\n   */\n  private calculateCombinationUniqueness(components: MorphemeComponent[]): number {\n    // 基于类别组合的稀有性\n    const categories = components.map(c => c.morpheme.category).sort()\n    const combination = categories.join('-')\n\n    // 常见组合降分，罕见组合加分\n    const commonCombinations = ['emotions-professions', 'characteristics-professions']\n    if (commonCombinations.includes(combination)) {\n      return 0.6\n    } else {\n      return 0.8\n    }\n  }\n\n  /**\n   * 计算长度独特性\n   *\n   * @private\n   */\n  private calculateLengthUniqueness(length: number): number {\n    // 2-4字符最常见，5-6字符较独特，7+字符很独特但可能过长\n    if (length >= 2 && length <= 4) {\n      return 0.3 // 常见长度\n    } else if (length >= 5 && length <= 6) {\n      return 0.8 // 较独特\n    } else if (length >= 7 && length <= 8) {\n      return 0.9 // 很独特\n    } else {\n      return 0.5 // 过短或过长\n    }\n  }\n\n  /**\n   * 计算字符独特性\n   *\n   * @private\n   */\n  private calculateCharacterUniqueness(text: string): number {\n    // 简化实现：检查是否包含生僻字或特殊字符\n    const commonChars = '的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞'\n\n    let uniqueScore = 0\n    for (const char of text) {\n      if (!commonChars.includes(char)) {\n        uniqueScore += 0.2\n      }\n    }\n\n    return Math.min(uniqueScore / text.length, 1)\n  }\n\n  /**\n   * 计算音节流畅性\n   *\n   * @private\n   */\n  private calculateSyllableFlow(components: MorphemeComponent[]): number {\n    // 简化实现：基于音节数量的流畅性\n    const totalSyllables = components.reduce((sum, c) =>\n      sum + (c.morpheme.language_properties?.syllable_count || 1), 0)\n\n    // 2-4音节最流畅\n    if (totalSyllables >= 2 && totalSyllables <= 4) {\n      return 0.9\n    } else if (totalSyllables <= 6) {\n      return 0.7\n    } else {\n      return 0.5\n    }\n  }\n\n  /**\n   * 计算声调和谐性\n   *\n   * @private\n   */\n  private calculateToneHarmony(components: MorphemeComponent[]): number {\n    // 简化实现：假设大部分组合都有基本的声调和谐性\n    return 0.7\n  }\n\n  /**\n   * 计算发音难度\n   *\n   * @private\n   */\n  private calculatePronunciationDifficulty(text: string): number {\n    // 基于字符数量的简化计算\n    const length = text.length\n    if (length <= 4) {\n      return 0.2 // 低难度\n    } else if (length <= 6) {\n      return 0.4 // 中等难度\n    } else {\n      return 0.7 // 高难度\n    }\n  }\n\n  /**\n   * 计算国际化发音友好度\n   *\n   * @private\n   */\n  private calculateInternationalPronunciation(text: string): number {\n    // 简化实现：较短的用户名通常更国际化友好\n    return text.length <= 6 ? 0.8 : 0.5\n  }\n\n  /**\n   * 计算语义相关性\n   *\n   * @private\n   */\n  private calculateSemanticRelatedness(components: MorphemeComponent[]): number {\n    if (components.length < 2) return 1.0\n\n    // 计算语义向量的相似度\n    let totalSimilarity = 0\n    let pairCount = 0\n\n    for (let i = 0; i < components.length - 1; i++) {\n      for (let j = i + 1; j < components.length; j++) {\n        const similarity = this.morphemeRepo.calculateSemanticSimilarity(\n          components[i].morpheme.semantic_vector,\n          components[j].morpheme.semantic_vector\n        )\n        totalSimilarity += similarity\n        pairCount++\n      }\n    }\n\n    return pairCount > 0 ? totalSimilarity / pairCount : 0.5\n  }\n\n  /**\n   * 计算概念层次一致性\n   *\n   * @private\n   */\n  private calculateConceptualConsistency(components: MorphemeComponent[]): number {\n    // 检查语素是否在相似的抽象层次\n    const categories = components.map(c => c.morpheme.category)\n    const abstractCategories = ['emotions', 'concepts', 'characteristics']\n    const concreteCategories = ['professions', 'objects', 'actions']\n\n    const abstractCount = categories.filter(c => abstractCategories.includes(c)).length\n    const concreteCount = categories.filter(c => concreteCategories.includes(c)).length\n\n    // 同一抽象层次的一致性更高\n    if (abstractCount === components.length || concreteCount === components.length) {\n      return 0.9\n    } else {\n      return 0.6 // 混合抽象层次\n    }\n  }\n\n  /**\n   * 计算语法合理性\n   *\n   * @private\n   */\n  private calculateGrammaticalReasonableness(components: MorphemeComponent[]): number {\n    // 简化实现：基于词性组合的合理性\n    const morphTypes = components.map(c => c.morpheme.language_properties?.morphological_type || '未知')\n\n    // 常见的合理组合\n    const reasonableCombinations = [\n      ['形容词', '名词'],\n      ['名词', '名词'],\n      ['动词', '名词']\n    ]\n\n    if (morphTypes.length === 2) {\n      const combination = [morphTypes[0], morphTypes[1]]\n      const isReasonable = reasonableCombinations.some(rc =>\n        rc[0] === combination[0] && rc[1] === combination[1]\n      )\n      return isReasonable ? 0.9 : 0.6\n    }\n\n    return 0.7 // 默认合理性\n  }\n\n  /**\n   * 计算视觉美学\n   *\n   * @private\n   */\n  private calculateVisualAesthetics(text: string): number {\n    // 简化实现：基于字符的视觉平衡性\n    const length = text.length\n\n    // 2-4字符视觉最平衡\n    if (length >= 2 && length <= 4) {\n      return 0.9\n    } else if (length <= 6) {\n      return 0.7\n    } else {\n      return 0.5\n    }\n  }\n\n  /**\n   * 计算语音美学\n   *\n   * @private\n   */\n  private calculatePhoneticAesthetics(components: MorphemeComponent[]): number {\n    // 基于音韵的美学价值\n    return this.calculateSyllableFlow(components) * 0.8 + this.calculateToneHarmony(components) * 0.2\n  }\n\n  /**\n   * 计算语义美学\n   *\n   * @private\n   */\n  private calculateSemanticAesthetics(components: MorphemeComponent[]): number {\n    // 基于语义的美学价值\n    const poeticCategories = ['emotions', 'concepts', 'objects']\n    const poeticCount = components.filter(c => poeticCategories.includes(c.morpheme.category)).length\n    return poeticCount / components.length\n  }\n\n  /**\n   * 计算整体和谐性\n   *\n   * @private\n   */\n  private calculateOverallHarmony(text: string, components: MorphemeComponent[]): number {\n    // 综合各方面的和谐性\n    const lengthHarmony = text.length >= 2 && text.length <= 6 ? 1 : 0.6\n    const categoryHarmony = this.calculateConceptualConsistency(components)\n    return (lengthHarmony + categoryHarmony) / 2\n  }\n\n  /**\n   * 计算输入便利性\n   *\n   * @private\n   */\n  private calculateInputConvenience(text: string): number {\n    // 基于常用字符的输入便利性\n    return text.length <= 8 ? 0.8 : 0.5\n  }\n\n  /**\n   * 计算平台兼容性\n   *\n   * @private\n   */\n  private calculatePlatformCompatibility(text: string): number {\n    // 简化实现：中文字符在大多数平台都兼容\n    return 0.9\n  }\n\n  /**\n   * 计算搜索友好性\n   *\n   * @private\n   */\n  private calculateSearchFriendliness(text: string): number {\n    // 较短的用户名搜索更友好\n    return text.length <= 6 ? 0.9 : 0.6\n  }\n\n  /**\n   * 计算传播便利性\n   *\n   * @private\n   */\n  private calculateCommunicationEase(text: string): number {\n    // 基于长度和发音的传播便利性\n    const lengthScore = text.length <= 4 ? 1 : (text.length <= 6 ? 0.8 : 0.5)\n    return lengthScore\n  }\n\n  /**\n   * 计算评估置信度\n   *\n   * @private\n   */\n  private calculateEvaluationConfidence(text: string, components: MorphemeComponent[], context: GenerationContext): number {\n    let confidence = 0.8 // 基础置信度\n\n    // 语素数量影响置信度\n    if (components.length >= 2) {\n      confidence += 0.1\n    }\n\n    // 语素质量影响置信度\n    const avgQuality = components.reduce((sum, c) => sum + c.morpheme.quality_score, 0) / components.length\n    confidence += (avgQuality - 0.5) * 0.2\n\n    // 长度合理性影响置信度\n    if (text.length >= 2 && text.length <= 6) {\n      confidence += 0.1\n    }\n\n    return Math.min(Math.max(confidence, 0.3), 1.0)\n  }\n\n  /**\n   * 分析质量问题和建议\n   *\n   * @private\n   */\n  private analyzeQualityIssues(text: string, components: MorphemeComponent[], dimensions: any): { issues: string[], suggestions: string[] } {\n    const issues: string[] = []\n    const suggestions: string[] = []\n\n    // 检查各维度的问题\n    if (dimensions.memorability < 0.6) {\n      issues.push('记忆性较低')\n      suggestions.push('考虑使用更短或更有规律的组合')\n    }\n\n    if (dimensions.pronunciation < 0.6) {\n      issues.push('发音不够友好')\n      suggestions.push('选择音节更流畅的语素组合')\n    }\n\n    if (dimensions.cultural_fit < 0.7) {\n      issues.push('文化适配度不足')\n      suggestions.push('选择更符合目标文化语境的语素')\n    }\n\n    if (dimensions.semantic_coherence < 0.6) {\n      issues.push('语义连贯性不足')\n      suggestions.push('选择语义更相关的语素组合')\n    }\n\n    if (text.length > 8) {\n      issues.push('用户名过长')\n      suggestions.push('考虑使用更简洁的语素组合')\n    }\n\n    if (text.length < 2) {\n      issues.push('用户名过短')\n      suggestions.push('考虑添加更多语素以增加表达力')\n    }\n\n    return { issues, suggestions }\n  }\n\n  /**\n   * 获取引擎统计信息 (包含多语种信息)\n   *\n   * @returns 详细的引擎统计信息\n   */\n  getStats(): EngineStats {\n    const avgGenerationTime = this.generationCount > 0\n      ? this.totalGenerationTime / this.generationCount\n      : 0\n\n    const morphemeStats = this.morphemeRepo.getStats()\n\n    return {\n      morpheme_count: morphemeStats.total,\n      morpheme_stats: morphemeStats,\n      engine_status: this.isInitialized ? 'ready' : 'not_initialized',\n      total_generations: this.generationCount,\n      avg_generation_time: avgGenerationTime,\n      success_rate: 1.0\n    }\n  }\n\n  /**\n   * 获取多语种统计信息\n   *\n   * @returns 多语种引擎统计信息\n   */\n  getMultilingualStats(): {\n    supportedLanguages: LanguageCode[]\n    languageStats: Record<LanguageCode, { morphemeCount: number; conceptCount: number }>\n    totalConcepts: number\n    isMultilingualReady: boolean\n  } {\n    const supportedLanguages = this.languageManager.getSupportedLanguages()\n    const languageStats: Record<LanguageCode, { morphemeCount: number; conceptCount: number }> = {\n      [LanguageCode.ZH_CN]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.EN_US]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.JA_JP]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.KO_KR]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.ES_ES]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.FR_FR]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.DE_DE]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      },\n      [LanguageCode.AR_SA]: {\n        morphemeCount: 0,\n        conceptCount: 0\n      }\n    }\n\n    for (const language of supportedLanguages) {\n      languageStats[language] = this.languageManager.getLanguageStats(language)\n    }\n\n    return {\n      supportedLanguages,\n      languageStats,\n      totalConcepts: Object.values(languageStats).reduce((sum, stats) => sum + stats.conceptCount, 0),\n      isMultilingualReady: this.languageManager.isReady()\n    }\n  }\n\n  /**\n   * 获取语素仓库统计信息\n   *\n   * @returns 语素仓库统计信息\n   */\n  getMorphemeStats() {\n    return this.morphemeRepo.getStats()\n  }\n\n  /**\n   * 检查引擎是否就绪 (包含多语种检查)\n   *\n   * @returns 就绪状态\n   */\n  isReady(): boolean {\n    return this.isInitialized &&\n           this.morphemeRepo.isReady() &&\n           this.languageManager.isReady()\n  }\n\n  /**\n   * 检查特定语言是否支持\n   *\n   * @param language 语言代码\n   * @returns 是否支持该语言\n   */\n  isLanguageSupported(language: LanguageCode): boolean {\n    return this.languageManager.isLanguageSupported(language)\n  }\n\n  /**\n   * 获取支持的语言列表\n   *\n   * @returns 支持的语言代码数组\n   */\n  getSupportedLanguages(): LanguageCode[] {\n    return this.languageManager.getSupportedLanguages()\n  }\n\n  /**\n   * 清理资源\n   *\n   * 清理引擎和依赖组件的资源，包含多语种组件\n   */\n  destroy(): void {\n    this.morphemeRepo.destroy()\n    // 多语种组件无需特殊清理，由垃圾回收处理\n    this.isInitialized = false\n    this.generationCount = 0\n    this.totalGenerationTime = 0\n\n    console.log('🧹 多语种核心生成引擎资源已清理')\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwBS;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAxBT;;;;;;;;;;;AAYA,SAOEE,gBAAgB,QACX,kBAAkB;AACzB,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,YAAY,QAAQ,0BAA0B;AAEvD;;;;;AAKA,OAAM,MAAOC,oBAAoB;EACvBC,YAAY;EACZC,eAAe;EACfC,eAAe;EACfC,aAAa;EAAA;EAAA,CAAAb,cAAA,GAAAc,CAAA,OAAG,KAAK;EACrBC,eAAe;EAAA;EAAA,CAAAf,cAAA,GAAAc,CAAA,OAAG,CAAC;EACnBE,mBAAmB;EAAA;EAAA,CAAAhB,cAAA,GAAAc,CAAA,OAAG,CAAC;EAE/B;;;;;EAKAG,YAAA;IAAA;IAAAjB,cAAA,GAAAkB,CAAA;IACE;IACA,MAAMC,UAAU;IAAA;IAAA,CAAAnB,cAAA,GAAAc,CAAA,OAAG,IAAIV,UAAU,CAAC;MAChCgB,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,IAAI;MACtBC,WAAW,EAAE;KACd,CAAC;IAEF,MAAMC,aAAa;IAAA;IAAA,CAAAvB,cAAA,GAAAc,CAAA,OAAG,IAAIT,aAAa,CAAC;MACtCmB,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE;KAChB,CAAC;IAAA;IAAAzB,cAAA,GAAAc,CAAA;IAEF,IAAI,CAACJ,YAAY,GAAG,IAAIP,kBAAkB,CAACgB,UAAU,EAAEI,aAAa,CAAC;IAErE;IAAA;IAAAvB,cAAA,GAAAc,CAAA;IACA,IAAI,CAACH,eAAe,GAAG,IAAIL,eAAe,CAAC;MACzCoB,eAAe,EAAElB,YAAY,CAACmB,KAAK;MACnCC,gBAAgB,EAAE,CAACpB,YAAY,CAACmB,KAAK,CAAC;MAAE;MACxCL,WAAW,EAAE,IAAI;MACjBO,QAAQ,EAAE;KACX,CAAC;IAAA;IAAA7B,cAAA,GAAAc,CAAA;IAEF,IAAI,CAACF,eAAe,GAAG,IAAIL,eAAe,CAAC;MACzCuB,2BAA2B,EAAE,IAAI;MACjCC,iBAAiB,EAAE,GAAG;MACtBC,cAAc,EAAE,GAAG;MACnBC,aAAa,EAAE;KAChB,CAAC;EACJ;EAEA;;;;;;;EAOA,MAAMC,UAAUA,CAAA;IAAA;IAAAlC,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACd,IAAI,IAAI,CAACD,aAAa,EAAE;MAAA;MAAAb,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACtBsB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MAAA;MAAArC,cAAA,GAAAc,CAAA;MACpC;IACF,CAAC;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAED,MAAMG,SAAS;IAAA;IAAA,CAAAtC,cAAA,GAAAc,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE;IAAA;IAAAxC,cAAA,GAAAc,CAAA;IAC5BsB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAAA;IAAArC,cAAA,GAAAc,CAAA;IAEnC,IAAI;MAAA;MAAAd,cAAA,GAAAc,CAAA;MACF;MACA,MAAM,IAAI,CAACJ,YAAY,CAACwB,UAAU,EAAE;MAEpC;MAAA;MAAAlC,cAAA,GAAAc,CAAA;MACA,IAAI,CAAC,IAAI,CAACJ,YAAY,CAAC+B,OAAO,EAAE,EAAE;QAAA;QAAAzC,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAChC,MAAM,IAAI4B,KAAK,CAAC,WAAW,CAAC;MAC9B,CAAC;MAAA;MAAA;QAAA1C,cAAA,GAAAmC,CAAA;MAAA;MAED;MAAAnC,cAAA,GAAAc,CAAA;MACA,MAAM,IAAI,CAACH,eAAe,CAACuB,UAAU,EAAE;MAEvC;MAAA;MAAAlC,cAAA,GAAAc,CAAA;MACA,IAAI,CAAC,IAAI,CAACH,eAAe,CAAC8B,OAAO,EAAE,EAAE;QAAA;QAAAzC,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACnC,MAAM,IAAI4B,KAAK,CAAC,YAAY,CAAC;MAC/B,CAAC;MAAA;MAAA;QAAA1C,cAAA,GAAAmC,CAAA;MAAA;MAED;MACA,MAAMQ,KAAK;MAAA;MAAA,CAAA3C,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACJ,YAAY,CAACkC,QAAQ,EAAE;MAC1C,MAAMC,kBAAkB;MAAA;MAAA,CAAA7C,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACH,eAAe,CAACmC,qBAAqB,EAAE;MAAA;MAAA9C,cAAA,GAAAc,CAAA;MAEvE,IAAI,CAACD,aAAa,GAAG,IAAI;MACzB,MAAMkC,QAAQ;MAAA;MAAA,CAAA/C,cAAA,GAAAc,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MAAA;MAAAtC,cAAA,GAAAc,CAAA;MAEvCsB,OAAO,CAACC,GAAG,CAAC,uBAAuBU,QAAQ,IAAI,CAAC;MAAA;MAAA/C,cAAA,GAAAc,CAAA;MAChDsB,OAAO,CAACC,GAAG,CAAC,YAAYM,KAAK,CAACK,KAAK,UAAUC,MAAM,CAACC,OAAO,CAACP,KAAK,CAACQ,UAAU,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK;QAAA;QAAAtD,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAc,CAAA;QAAA,UAAGuC,CAAC,IAAIC,CAAC,EAAE;MAAF,CAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAAA;MAAAvD,cAAA,GAAAc,CAAA;MACxHsB,OAAO,CAACC,GAAG,CAAC,YAAYQ,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAExD;MAAA;MAAAvD,cAAA,GAAAc,CAAA;MACA,KAAK,MAAM0C,IAAI,IAAIX,kBAAkB,EAAE;QACrC,MAAMY,SAAS;QAAA;QAAA,CAAAzD,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACH,eAAe,CAAC+C,gBAAgB,CAACF,IAAI,CAAC;QAAA;QAAAxD,cAAA,GAAAc,CAAA;QAC7DsB,OAAO,CAACC,GAAG,CAAC,MAAMmB,IAAI,KAAKC,SAAS,CAACE,aAAa,QAAQF,SAAS,CAACG,YAAY,KAAK,CAAC;MACxF;IAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACdsB,OAAO,CAACyB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACzC,MAAM,IAAI4B,KAAK,CAAC,mBAAmBmB,KAAK,YAAYnB,KAAK;MAAA;MAAA,CAAA1C,cAAA,GAAAmC,CAAA,UAAG0B,KAAK,CAACC,OAAO;MAAA;MAAA,CAAA9D,cAAA,GAAAmC,CAAA,UAAG4B,MAAM,CAACF,KAAK,CAAC,GAAE,CAAC;IAC9F;EACF;EAEA;;;;;;;EAOA,MAAMG,MAAMA,CAAA;IAAA;IAAAhE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACVsB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAAA;IAAArC,cAAA,GAAAc,CAAA;IAClC,IAAI,CAACD,aAAa,GAAG,KAAK;IAAA;IAAAb,cAAA,GAAAc,CAAA;IAC1B,MAAM,IAAI,CAACJ,YAAY,CAACsD,MAAM,EAAE;IAEhC;IACA,MAAMnB,kBAAkB;IAAA;IAAA,CAAA7C,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACH,eAAe,CAACmC,qBAAqB,EAAE;IAAA;IAAA9C,cAAA,GAAAc,CAAA;IACvE,KAAK,MAAMmD,QAAQ,IAAIpB,kBAAkB,EAAE;MAAA;MAAA7C,cAAA,GAAAc,CAAA;MACzC,MAAM,IAAI,CAACH,eAAe,CAACuD,cAAc,CAACD,QAAQ,CAAC;IACrD;IAAC;IAAAjE,cAAA,GAAAc,CAAA;IAED,MAAM,IAAI,CAACoB,UAAU,EAAE;EACzB;EAEA;;;;;;;;;EASA,MAAMiC,QAAQA,CAACC,OAA0B,EAAEC,KAAA;EAAA;EAAA,CAAArE,cAAA,GAAAmC,CAAA,UAAgB,CAAC;IAAA;IAAAnC,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC1D,OAAO,IAAI,CAACwD,oBAAoB,CAACF,OAAO,EAAEC,KAAK,EAAE7D,YAAY,CAACmB,KAAK,CAAC;EACtE;EAEA;;;;;;;;;;EAUA,MAAM2C,oBAAoBA,CACxBF,OAA0B,EAC1BC,KAAA;EAAA;EAAA,CAAArE,cAAA,GAAAmC,CAAA,UAAgB,CAAC,GACjB8B,QAAA;EAAA;EAAA,CAAAjE,cAAA,GAAAmC,CAAA,UAAyB3B,YAAY,CAACmB,KAAK;IAAA;IAAA3B,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAE3C;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,UAAAkC,KAAK,GAAG,CAAC;IAAA;IAAA,CAAArE,cAAA,GAAAmC,CAAA,UAAIkC,KAAK,GAAG,EAAE,GAAE;MAAA;MAAArE,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC3B,MAAM,IAAI4B,KAAK,CAAC,eAAe,CAAC;IAClC,CAAC;IAAA;IAAA;MAAA1C,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAI,CAAC,IAAI,CAACD,aAAa,EAAE;MAAA;MAAAb,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACvB,MAAM,IAAI,CAACoB,UAAU,EAAE;IACzB,CAAC;IAAA;IAAA;MAAAlC,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAI,CAAC,IAAI,CAACH,eAAe,CAAC4D,mBAAmB,CAACN,QAAQ,CAAC,EAAE;MAAA;MAAAjE,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACvD,MAAM,IAAI4B,KAAK,CAAC,WAAWuB,QAAQ,EAAE,CAAC;IACxC,CAAC;IAAA;IAAA;MAAAjE,cAAA,GAAAmC,CAAA;IAAA;IAED,MAAMG,SAAS;IAAA;IAAA,CAAAtC,cAAA,GAAAc,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMgC,OAAO;IAAA;IAAA,CAAAxE,cAAA,GAAAc,CAAA,QAAwB,EAAE;IACvC,MAAM2D,UAAU;IAAA;IAAA,CAAAzE,cAAA,GAAAc,CAAA,QAAGuD,KAAK,GAAG,CAAC,GAAC;IAAA;IAAArE,cAAA,GAAAc,CAAA;IAE7BsB,OAAO,CAACC,GAAG,CAAC,UAAUgC,KAAK,IAAIJ,QAAQ,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IAEhD,IAAI;MACF,IAAI4D,QAAQ;MAAA;MAAA,CAAA1E,cAAA,GAAAc,CAAA,QAAG,CAAC;MAAA;MAAAd,cAAA,GAAAc,CAAA;MAChB;MAAO;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAqC,OAAO,CAACG,MAAM,GAAGN,KAAK;MAAA;MAAA,CAAArE,cAAA,GAAAmC,CAAA,WAAIuC,QAAQ,GAAGD,UAAU,GAAE;QACtD,MAAMG,QAAQ;QAAA;QAAA,CAAA5E,cAAA,GAAAc,CAAA,QAAGmD,QAAQ,KAAKzD,YAAY,CAACmB,KAAK;QAAA;QAAA,CAAA3B,cAAA,GAAAmC,CAAA,WAC5C,MAAM,IAAI,CAAC0C,cAAc,CAACT,OAAO,CAAC,CAAC;QAAA;QAAA;QAAA,CAAApE,cAAA,GAAAmC,CAAA,WACnC,MAAM,IAAI,CAAC2C,0BAA0B,CAACV,OAAO,EAAEH,QAAQ,CAAC,IAAC;QAAA;QAAAjE,cAAA,GAAAc,CAAA;QAE7D,IAAI8D,QAAQ,EAAE;UAAA;UAAA5E,cAAA,GAAAmC,CAAA;UACZ;UACA,MAAM4C,WAAW;UAAA;UAAA,CAAA/E,cAAA,GAAAc,CAAA,QAAG0D,OAAO,CAACQ,IAAI,CAACC,QAAQ,IAAI;YAAA;YAAAjF,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAc,CAAA;YAAA,OAAAmE,QAAQ,CAACC,IAAI,KAAKN,QAAQ,CAACM,IAAI;UAAJ,CAAI,CAAC;UAAA;UAAAlF,cAAA,GAAAc,CAAA;UAC7E,IAAI,CAACiE,WAAW,EAAE;YAAA;YAAA/E,cAAA,GAAAmC,CAAA;YAAAnC,cAAA,GAAAc,CAAA;YAChB0D,OAAO,CAACW,IAAI,CAACP,QAAQ,CAAC;UACxB,CAAC;UAAA;UAAA;YAAA5E,cAAA,GAAAmC,CAAA;UAAA;QACH,CAAC;QAAA;QAAA;UAAAnC,cAAA,GAAAmC,CAAA;QAAA;QAAAnC,cAAA,GAAAc,CAAA;QACD4D,QAAQ,EAAE;MACZ;MAEA,MAAMU,cAAc;MAAA;MAAA,CAAApF,cAAA,GAAAc,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MAAA;MAAAtC,cAAA,GAAAc,CAAA;MAC7C,IAAI,CAACC,eAAe,IAAIyD,OAAO,CAACG,MAAM;MAAA;MAAA3E,cAAA,GAAAc,CAAA;MACtC,IAAI,CAACE,mBAAmB,IAAIoE,cAAc;MAAA;MAAApF,cAAA,GAAAc,CAAA;MAE1CsB,OAAO,CAACC,GAAG,CAAC,KAAK4B,QAAQ,SAASO,OAAO,CAACG,MAAM,IAAIN,KAAK,WAAWe,cAAc,IAAI,CAAC;MAAA;MAAApF,cAAA,GAAAc,CAAA;MAEvF,IAAI0D,OAAO,CAACG,MAAM,GAAGN,KAAK,EAAE;QAAA;QAAArE,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC1BsB,OAAO,CAACiD,IAAI,CAAC,UAAUb,OAAO,CAACG,MAAM,aAAaN,KAAK,GAAG,CAAC;MAC7D,CAAC;MAAA;MAAA;QAAArE,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,OAAO0D,OAAO;IAChB,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACdsB,OAAO,CAACyB,KAAK,CAAC,KAAKI,QAAQ,UAAU,EAAEJ,KAAK,CAAC;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MAC7C,MAAM,IAAI4B,KAAK,CAAC,GAAGuB,QAAQ,YAAYJ,KAAK,YAAYnB,KAAK;MAAA;MAAA,CAAA1C,cAAA,GAAAmC,CAAA,WAAG0B,KAAK,CAACC,OAAO;MAAA;MAAA,CAAA9D,cAAA,GAAAmC,CAAA,WAAG4B,MAAM,CAACF,KAAK,CAAC,GAAE,CAAC;IAClG;EACF;EAEA;;;EAGQ,MAAMgB,cAAcA,CAACT,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IACrD,MAAMoE,eAAe;IAAA;IAAA,CAAAtF,cAAA,GAAAc,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE;IAAA;IAAAxC,cAAA,GAAAc,CAAA;IAElC,IAAI;MACF;MACA,MAAMyE,OAAO;MAAA;MAAA,CAAAvF,cAAA,GAAAc,CAAA,QAAG,IAAI,CAAC0E,aAAa,CAACpB,OAAO,CAAC;MAC3C,MAAMqB,UAAU;MAAA;MAAA,CAAAzF,cAAA,GAAAc,CAAA,QAAG,MAAM,IAAI,CAAC4E,eAAe,CAACH,OAAO,EAAEnB,OAAO,CAAC;MAAA;MAAApE,cAAA,GAAAc,CAAA;MAE/D,IAAI2E,UAAU,CAACd,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC3B,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAd,cAAA,GAAAmC,CAAA;MAAA;MAED,MAAM+C,IAAI;MAAA;MAAA,CAAAlF,cAAA,GAAAc,CAAA,QAAG,IAAI,CAAC6E,iBAAiB,CAACF,UAAU,CAAC;MAC/C,MAAMG,YAAY;MAAA;MAAA,CAAA5F,cAAA,GAAAc,CAAA,QAAG,IAAI,CAAC+E,eAAe,CAACX,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;MAEpE;MAAA;MAAApE,cAAA,GAAAc,CAAA;MACA,IAAI8E,YAAY,CAACE,OAAO,GAAG1B,OAAO,CAAC2B,iBAAiB,EAAE;QAAA;QAAA/F,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACpD,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAd,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,OAAO;QACLoE,IAAI;QACJK,OAAO,EAAEA,OAAO,CAACS,IAAI;QACrBC,aAAa,EAAEL,YAAY;QAC3BM,WAAW,EAAE,IAAI,CAACC,mBAAmB,CAACjB,IAAI,EAAEO,UAAU,EAAEF,OAAO,CAAC;QAChEE,UAAU;QACVW,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI,CAACC,yBAAyB,CAACpB,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;UACvEmC,UAAU,EAAE,IAAI,CAACC,wBAAwB,CAACtB,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;UACpEqC,YAAY,EAAE,IAAI,CAACC,0BAA0B,CAACxB,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;UACxEuC,UAAU,EAAE,IAAI,CAACC,wBAAwB,CAAC1B,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;UACpEyC,eAAe,EAAEtE,IAAI,CAACC,GAAG,EAAE,GAAG8C;;OAEjC;IACH,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACdsB,OAAO,CAACyB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MAC3D,OAAO,IAAI;IACb;EACF;EAEA;;;;;EAKQ,MAAMgE,0BAA0BA,CACtCV,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,MAAMoE,eAAe;IAAA;IAAA,CAAAtF,cAAA,GAAAc,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE;IAAA;IAAAxC,cAAA,GAAAc,CAAA;IAElC,IAAI;MACF;MACA,MAAMgG,iBAAiB;MAAA;MAAA,CAAA9G,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACH,eAAe,CAACoG,sBAAsB,CAAC9C,QAAQ,CAAC;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MAC/E,IAAIgG,iBAAiB,CAACnC,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAClCsB,OAAO,CAACiD,IAAI,CAAC,MAAMpB,QAAQ,UAAU,CAAC;QAAA;QAAAjE,cAAA,GAAAc,CAAA;QACtC,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAd,cAAA,GAAAmC,CAAA;MAAA;MAED;MACA,MAAMoD,OAAO;MAAA;MAAA,CAAAvF,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACkG,yBAAyB,CAAC5C,OAAO,EAAEH,QAAQ,CAAC;MAEjE;MACA,MAAMwB,UAAU;MAAA;MAAA,CAAAzF,cAAA,GAAAc,CAAA,QAAG,MAAM,IAAI,CAACmG,2BAA2B,CAAC1B,OAAO,EAAEnB,OAAO,EAAEH,QAAQ,CAAC;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MAErF,IAAI2E,UAAU,CAACd,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC3B,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAd,cAAA,GAAAmC,CAAA;MAAA;MAED;MACA,MAAM+C,IAAI;MAAA;MAAA,CAAAlF,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACoG,6BAA6B,CAACzB,UAAU,EAAExB,QAAQ,CAAC;MAErE;MACA,MAAM2B,YAAY;MAAA;MAAA,CAAA5F,cAAA,GAAAc,CAAA,QAAG,IAAI,CAACqG,2BAA2B,CAACjC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;MAE1F;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MACA,IAAI8E,YAAY,CAACE,OAAO,GAAG1B,OAAO,CAAC2B,iBAAiB,EAAE;QAAA;QAAA/F,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACpD,OAAO,IAAI;MACb,CAAC;MAAA;MAAA;QAAAd,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,OAAO;QACLoE,IAAI;QACJK,OAAO,EAAEA,OAAO,CAACS,IAAI;QACrBC,aAAa,EAAEL,YAAY;QAC3BM,WAAW,EAAE,IAAI,CAACkB,+BAA+B,CAAClC,IAAI,EAAEO,UAAU,EAAEF,OAAO,EAAEtB,QAAQ,CAAC;QACtFwB,UAAU,EAAE,IAAI,CAAC4B,kCAAkC,CAAC5B,UAAU,CAAC;QAC/DW,QAAQ,EAAE;UACRC,YAAY,EAAE,IAAI,CAACiB,gCAAgC,CAACpC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;UACxFsC,UAAU,EAAE,IAAI,CAACgB,+BAA+B,CAACrC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;UACrFwC,YAAY,EAAE,IAAI,CAACe,iCAAiC,CAACtC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;UACzF0C,UAAU,EAAE,IAAI,CAACc,+BAA+B,CAACvC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;UACrF4C,eAAe,EAAEtE,IAAI,CAACC,GAAG,EAAE,GAAG8C;;OAEjC;IACH,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACdsB,OAAO,CAACyB,KAAK,CAAC,6BAA6BI,QAAQ,YAAY,EAAEJ,KAAK,CAAC;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACvE,OAAO,IAAI;IACb;EACF;EAEA;;;EAGQ0E,aAAaA,CAACpB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC9C;IACA,MAAMwG,QAAQ;IAAA;IAAA,CAAA1H,cAAA,GAAAc,CAAA,SAAG,CACf;MAAEkF,IAAI,EAAE,QAAQ;MAAE2B,QAAQ,EAAE;IAAmB,CAAE,EACjD;MAAE3B,IAAI,EAAE,MAAM;MAAE2B,QAAQ,EAAE;IAA+B,CAAE,EAC3D;MAAE3B,IAAI,EAAE,MAAM;MAAE2B,QAAQ,EAAE;IAAc,CAAE,CAC3C;IAED;IAAA;IAAA3H,cAAA,GAAAc,CAAA;IACA,OAAO4G,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGJ,QAAQ,CAAC/C,MAAM,CAAC,CAAC;EAC9D;EAEA;;;EAGQqC,yBAAyBA,CAC/B5C,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB;IACA,MAAM6G,gBAAgB;IAAA;IAAA,CAAA/H,cAAA,GAAAc,CAAA,SAAoE;MACxF,CAACN,YAAY,CAACmB,KAAK,GAAG,CACpB;QAAEqE,IAAI,EAAE,QAAQ;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EACjD;QAAE3B,IAAI,EAAE,MAAM;QAAE2B,QAAQ,EAAE;MAA+B,CAAE,EAC3D;QAAE3B,IAAI,EAAE,MAAM;QAAE2B,QAAQ,EAAE;MAAc,CAAE,CAC3C;MACD,CAACnH,YAAY,CAACwH,KAAK,GAAG,CACpB;QAAEhC,IAAI,EAAE,gBAAgB;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EACzD;QAAE3B,IAAI,EAAE,oBAAoB;QAAE2B,QAAQ,EAAE;MAA8B,CAAE,EACxE;QAAE3B,IAAI,EAAE,gBAAgB;QAAE2B,QAAQ,EAAE;MAA2B,CAAE,CAClE;MACD,CAACnH,YAAY,CAACyH,KAAK,GAAG,CACpB;QAAEjC,IAAI,EAAE,QAAQ;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EACjD;QAAE3B,IAAI,EAAE,KAAK;QAAE2B,QAAQ,EAAE;MAA8B,CAAE,CAC1D;MACD,CAACnH,YAAY,CAAC0H,KAAK,GAAG,CACpB;QAAElC,IAAI,EAAE,QAAQ;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EACjD;QAAE3B,IAAI,EAAE,KAAK;QAAE2B,QAAQ,EAAE;MAA8B,CAAE,CAC1D;MACD,CAACnH,YAAY,CAAC2H,KAAK,GAAG,CACpB;QAAEnC,IAAI,EAAE,qBAAqB;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EAC9D;QAAE3B,IAAI,EAAE,oBAAoB;QAAE2B,QAAQ,EAAE;MAA8B,CAAE,CACzE;MACD,CAACnH,YAAY,CAAC4H,KAAK,GAAG,CACpB;QAAEpC,IAAI,EAAE,cAAc;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EACvD;QAAE3B,IAAI,EAAE,qBAAqB;QAAE2B,QAAQ,EAAE;MAA8B,CAAE,CAC1E;MACD,CAACnH,YAAY,CAAC6H,KAAK,GAAG,CACpB;QAAErC,IAAI,EAAE,qBAAqB;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EAC9D;QAAE3B,IAAI,EAAE,YAAY;QAAE2B,QAAQ,EAAE;MAA8B,CAAE,CACjE;MACD,CAACnH,YAAY,CAAC8H,KAAK,GAAG,CACpB;QAAEtC,IAAI,EAAE,SAAS;QAAE2B,QAAQ,EAAE;MAAmB,CAAE,EAClD;QAAE3B,IAAI,EAAE,UAAU;QAAE2B,QAAQ,EAAE;MAA8B,CAAE;KAEjE;IAED,MAAMD,QAAQ;IAAA;IAAA,CAAA1H,cAAA,GAAAc,CAAA;IAAG;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAA4F,gBAAgB,CAAC9D,QAAQ,CAAC;IAAA;IAAA,CAAAjE,cAAA,GAAAmC,CAAA,WAAI4F,gBAAgB,CAACvH,YAAY,CAACwH,KAAK,CAAC;IAAA;IAAAhI,cAAA,GAAAc,CAAA;IACnF,OAAO4G,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGJ,QAAQ,CAAC/C,MAAM,CAAC,CAAC;EAC9D;EAEA;;;;;;;;;;EAUQ,MAAMe,eAAeA,CAACH,OAA2C,EAAEnB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IACnG,MAAMuE,UAAU;IAAA;IAAA,CAAAzF,cAAA,GAAAc,CAAA,SAAwB,EAAE;IAAA;IAAAd,cAAA,GAAAc,CAAA;IAE1C,IAAI;MAAA;MAAAd,cAAA,GAAAc,CAAA;MACF;MACA,IAAIyE,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,aAAa,CAAC,EAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QAC5C;QACA,MAAMqG,OAAO;QAAA;QAAA,CAAAxI,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACJ,YAAY,CAAC+H,gBAAgB,CAACvI,gBAAgB,CAACwI,QAAQ,EAAE,CAAC,CAAC;QAAA;QAAA1I,cAAA,GAAAc,CAAA;QAChF,IAAI0H,OAAO,CAAC7D,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACtB2E,UAAU,CAACN,IAAI,CAAC;YACdwD,QAAQ,EAAEH,OAAO,CAAC,CAAC,CAAC;YACpBI,QAAQ,EAAE,CAAC;YACXC,IAAI,EAAE,UAAU;YAChBC,kBAAkB,EAAE,IAAI,CAACC,0BAA0B,CAACP,OAAO,CAAC,CAAC,CAAC,EAAEpE,OAAO;WACxE,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApE,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,QAAQ,CAAC;MAAA;MAAA,CAAAvI,cAAA,GAAAmC,CAAA,WAAIoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,cAAc,CAAC,GAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QACpF;QACA,MAAMqG,OAAO;QAAA;QAAA,CAAAxI,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACJ,YAAY,CAAC+H,gBAAgB,CAACvI,gBAAgB,CAAC8I,WAAW,EAAE,CAAC,CAAC;QAAA;QAAAhJ,cAAA,GAAAc,CAAA;QACnF,IAAI0H,OAAO,CAAC7D,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACtB2E,UAAU,CAACN,IAAI,CAAC;YACdwD,QAAQ,EAAEH,OAAO,CAAC,CAAC,CAAC;YACpBI,QAAQ,EAAE,CAAC;YACXC,IAAI,EAAE,MAAM;YACZC,kBAAkB,EAAE,IAAI,CAACC,0BAA0B,CAACP,OAAO,CAAC,CAAC,CAAC,EAAEpE,OAAO;WACxE,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApE,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,IAAIyE,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QACjD;QACA,MAAMqG,OAAO;QAAA;QAAA,CAAAxI,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACJ,YAAY,CAAC+H,gBAAgB,CAACvI,gBAAgB,CAAC+I,eAAe,EAAE,CAAC,CAAC;QAAA;QAAAjJ,cAAA,GAAAc,CAAA;QACvF,IAAI0H,OAAO,CAAC7D,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACtB2E,UAAU,CAACN,IAAI,CAAC;YACdwD,QAAQ,EAAEH,OAAO,CAAC,CAAC,CAAC;YACpBI,QAAQ,EAAE,CAAC;YACXC,IAAI,EAAE,UAAU;YAChBC,kBAAkB,EAAE,IAAI,CAACC,0BAA0B,CAACP,OAAO,CAAC,CAAC,CAAC,EAAEpE,OAAO;WACxE,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApE,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAED;MAAAnC,cAAA,GAAAc,CAAA;MACA;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAsD,UAAU,CAACd,MAAM,GAAG,CAAC;MAAA;MAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAIoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,UAAU,CAAC,GAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QAClE,MAAMqG,OAAO;QAAA;QAAA,CAAAxI,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACJ,YAAY,CAAC+H,gBAAgB,CAACvI,gBAAgB,CAACgJ,OAAO,EAAE,CAAC,CAAC;QAAA;QAAAlJ,cAAA,GAAAc,CAAA;QAC/E,IAAI0H,OAAO,CAAC7D,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACtB2E,UAAU,CAACN,IAAI,CAAC;YACdwD,QAAQ,EAAEH,OAAO,CAAC,CAAC,CAAC;YACpBI,QAAQ,EAAEnD,UAAU,CAACd,MAAM;YAC3BkE,IAAI,EAAE,YAAY;YAClBC,kBAAkB,EAAE,IAAI,CAACC,0BAA0B,CAACP,OAAO,CAAC,CAAC,CAAC,EAAEpE,OAAO;WACxE,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApE,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAED;MAAAnC,cAAA,GAAAc,CAAA;MACA;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAiC,OAAO,CAAC+E,mBAAmB;MAAA;MAAA,CAAAnJ,cAAA,GAAAmC,CAAA,WAAIiC,OAAO,CAAC+E,mBAAmB,KAAK,SAAS,GAAE;QAAA;QAAAnJ,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC5E,OAAO,IAAI,CAACsI,0BAA0B,CAAC3D,UAAU,EAAErB,OAAO,CAAC+E,mBAAmB,CAAC;MACjF,CAAC;MAAA;MAAA;QAAAnJ,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,OAAO2E,UAAU;IACnB,CAAC,CAAC,OAAO5B,KAAK,EAAE;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACdsB,OAAO,CAACyB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACjC,OAAO,EAAE;IACX;EACF;EAEA;;;;;;;;EAQQiI,0BAA0BA,CAACJ,QAAkB,EAAEvE,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC/E,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG6H,QAAQ,CAAC1C,aAAa,GAAG,GAAG,GAAG0C,QAAQ,CAACW,eAAe,GAAG,GAAG;IAEzE;IAAA;IAAAtJ,cAAA,GAAAc,CAAA;IACA,IAAIsD,OAAO,CAAC+E,mBAAmB,KAAKR,QAAQ,CAACY,gBAAgB,EAAE;MAAA;MAAAvJ,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC7DuI,KAAK,IAAI,GAAG;IACd,CAAC,MAAM;MAAA;MAAArJ,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAI6H,QAAQ,CAACY,gBAAgB,KAAK,SAAS,EAAE;QAAA;QAAAvJ,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAClDuI,KAAK,IAAI,GAAG;MACd,CAAC;MAAA;MAAA;QAAArJ,cAAA,GAAAmC,CAAA;MAAA;IAAD;IAEA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IACA,IAAI6H,QAAQ,CAAC1C,aAAa,IAAI7B,OAAO,CAAC2B,iBAAiB,EAAE;MAAA;MAAA/F,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACvDuI,KAAK,IAAI,GAAG;IACd,CAAC;IAAA;IAAA;MAAArJ,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,GAAG,CAAC;EAC7B;EAEA;;;;;;;;EAQQD,0BAA0BA,CAAC3D,UAA+B,EAAEgE,kBAA0B;IAAA;IAAAzJ,cAAA,GAAAkB,CAAA;IAC5F;IACA,MAAMwI,SAAS;IAAA;IAAA,CAAA1J,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkE,MAAM,CAACC,CAAC,IACnC;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,kCAAAd,cAAA,GAAAmC,CAAA,WAAAyH,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,KAAKE,kBAAkB;MAAA;MAAA,CAAAzJ,cAAA,GAAAmC,CAAA,WAClDyH,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,KAAK,SAAS;IAAT,CAAS,CAC1C;IAED;IAAA;IAAAvJ,cAAA,GAAAc,CAAA;IACA,OAAO4I,SAAS,CAAC/E,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAGuH,SAAS;IAAA;IAAA,CAAA1J,cAAA,GAAAmC,CAAA,WAAGsD,UAAU;EACvD;EAEA;;;;;EAKQ,MAAMwB,2BAA2BA,CACvC1B,OAA2C,EAC3CnB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,MAAM2I,iBAAiB;IAAA;IAAA,CAAA7J,cAAA,GAAAc,CAAA,SAAkE,EAAE;IAAA;IAAAd,cAAA,GAAAc,CAAA;IAE3F,IAAI;MACF;MACA,MAAMgG,iBAAiB;MAAA;MAAA,CAAA9G,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACH,eAAe,CAACoG,sBAAsB,CAAC9C,QAAQ,CAAC;MAE/E;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MACA;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,aAAa,CAAC;MAAA;MAAA,CAAAvI,cAAA,GAAAmC,CAAA,WAAIoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,WAAW,CAAC,GAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QACtF;QACA,MAAM2H,gBAAgB;QAAA;QAAA,CAAA9J,cAAA,GAAAc,CAAA,SAAGgG,iBAAiB,CAAC6C,MAAM,CAACI,CAAC,IACjD;UAAA;UAAA/J,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAc,CAAA;UAAA,kCAAAd,cAAA,GAAAmC,CAAA,WAAA4H,CAAC,CAACC,kBAAkB,CAACC,OAAO,KAAK,KAAK;UAAA;UAAA,CAAAjK,cAAA,GAAAmC,CAAA,WACtC4H,CAAC,CAACR,gBAAgB,CAACW,aAAa,CAAClF,IAAI,CAACmF,GAAG,IACvC;YAAA;YAAAnK,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAc,CAAA;YAAA,QAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC,CAACyH,QAAQ,CAAC4B,GAAG,CAACC,WAAW,EAAE,CAAC;UAAD,CAAC,CACxE;QADwE,CACxE,CACF;QAAA;QAAApK,cAAA,GAAAc,CAAA;QAED,IAAIgJ,gBAAgB,CAACnF,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAC/B,MAAMkI,QAAQ;UAAA;UAAA,CAAArK,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwJ,2BAA2B,CAACR,gBAAgB,EAAE1F,OAAO,CAAC;UAAA;UAAApE,cAAA,GAAAc,CAAA;UAC5E,IAAIuJ,QAAQ,EAAE;YAAA;YAAArK,cAAA,GAAAmC,CAAA;YAAAnC,cAAA,GAAAc,CAAA;YAAA+I,iBAAiB,CAAC1E,IAAI,CAACkF,QAAQ,CAAC;UAAA;UAAA;UAAA;YAAArK,cAAA,GAAAmC,CAAA;UAAA;QAChD,CAAC;QAAA;QAAA;UAAAnC,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,QAAQ,CAAC;MAAA;MAAA,CAAAvI,cAAA,GAAAmC,CAAA,WAAIoD,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,cAAc,CAAC,GAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QACpF;QACA,MAAMoI,mBAAmB;QAAA;QAAA,CAAAvK,cAAA,GAAAc,CAAA,SAAGgG,iBAAiB,CAAC6C,MAAM,CAACI,CAAC,IACpD;UAAA;UAAA/J,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAc,CAAA;UAAA,kCAAAd,cAAA,GAAAmC,CAAA,WAAA4H,CAAC,CAACC,kBAAkB,CAACC,OAAO,KAAK,MAAM;UAAA;UAAA,CAAAjK,cAAA,GAAAmC,CAAA,WACvC4H,CAAC,CAACR,gBAAgB,CAACW,aAAa,CAAClF,IAAI,CAACmF,GAAG,IACvC;YAAA;YAAAnK,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAc,CAAA;YAAA,QAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACyH,QAAQ,CAAC4B,GAAG,CAACC,WAAW,EAAE,CAAC;UAAD,CAAC,CACpE;QADoE,CACpE,CACF;QAAA;QAAApK,cAAA,GAAAc,CAAA;QAED,IAAIyJ,mBAAmB,CAAC5F,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAClC,MAAMkI,QAAQ;UAAA;UAAA,CAAArK,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwJ,2BAA2B,CAACC,mBAAmB,EAAEnG,OAAO,CAAC;UAAA;UAAApE,cAAA,GAAAc,CAAA;UAC/E,IAAIuJ,QAAQ,EAAE;YAAA;YAAArK,cAAA,GAAAmC,CAAA;YAAAnC,cAAA,GAAAc,CAAA;YAAA+I,iBAAiB,CAAC1E,IAAI,CAACkF,QAAQ,CAAC;UAAA;UAAA;UAAA;YAAArK,cAAA,GAAAmC,CAAA;UAAA;QAChD,CAAC;QAAA;QAAA;UAAAnC,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,IAAIyE,OAAO,CAACoC,QAAQ,CAACY,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAAA;QAAAvI,cAAA,GAAAmC,CAAA;QACjD;QACA,MAAMqI,uBAAuB;QAAA;QAAA,CAAAxK,cAAA,GAAAc,CAAA,SAAGgG,iBAAiB,CAAC6C,MAAM,CAACI,CAAC,IACxD;UAAA;UAAA/J,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAc,CAAA;UAAA,kCAAAd,cAAA,GAAAmC,CAAA,WAAA4H,CAAC,CAACC,kBAAkB,CAACC,OAAO,KAAK,KAAK;UAAA;UAAA,CAAAjK,cAAA,GAAAmC,CAAA,WACtC4H,CAAC,CAACR,gBAAgB,CAACW,aAAa,CAAClF,IAAI,CAACmF,GAAG,IACvC;YAAA;YAAAnK,cAAA,GAAAkB,CAAA;YAAAlB,cAAA,GAAAc,CAAA;YAAA,QAAC,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAACyH,QAAQ,CAAC4B,GAAG,CAACC,WAAW,EAAE,CAAC;UAAD,CAAC,CAChF;QADgF,CAChF,CACF;QAAA;QAAApK,cAAA,GAAAc,CAAA;QAED,IAAI0J,uBAAuB,CAAC7F,MAAM,GAAG,CAAC,EAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UACtC,MAAMkI,QAAQ;UAAA;UAAA,CAAArK,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwJ,2BAA2B,CAACE,uBAAuB,EAAEpG,OAAO,CAAC;UAAA;UAAApE,cAAA,GAAAc,CAAA;UACnF,IAAIuJ,QAAQ,EAAE;YAAA;YAAArK,cAAA,GAAAmC,CAAA;YAAAnC,cAAA,GAAAc,CAAA;YAAA+I,iBAAiB,CAAC1E,IAAI,CAACkF,QAAQ,CAAC;UAAA;UAAA;UAAA;YAAArK,cAAA,GAAAmC,CAAA;UAAA;QAChD,CAAC;QAAA;QAAA;UAAAnC,cAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA;MAAA;QAAAnC,cAAA,GAAAmC,CAAA;MAAA;MAED;MAAAnC,cAAA,GAAAc,CAAA;MACA,IAAI+I,iBAAiB,CAAClF,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAClC,MAAMsI,cAAc;QAAA;QAAA,CAAAzK,cAAA,GAAAc,CAAA,SAAGgG,iBAAiB,CAACc,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGhB,iBAAiB,CAACnC,MAAM,CAAC,CAAC;QAAA;QAAA3E,cAAA,GAAAc,CAAA;QAC9F+I,iBAAiB,CAAC1E,IAAI,CAACsF,cAAc,CAAC;MACxC,CAAC;MAAA;MAAA;QAAAzK,cAAA,GAAAmC,CAAA;MAAA;MAAAnC,cAAA,GAAAc,CAAA;MAED,OAAO+I,iBAAiB;IAC1B,CAAC,CAAC,OAAOhG,KAAK,EAAE;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MACdsB,OAAO,CAACyB,KAAK,CAAC,OAAOI,QAAQ,OAAO,EAAEJ,KAAK,CAAC;MAAA;MAAA7D,cAAA,GAAAc,CAAA;MAC5C,OAAO,EAAE;IACX;EACF;EAEA;;;EAGQwJ,2BAA2BA,CACjCI,SAAwE,EACxEtG,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAE1B,IAAI4J,SAAS,CAAC/F,MAAM,KAAK,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,OAAO,IAAI;IAAA;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAEvC;IACA,MAAMwI,MAAM;IAAA;IAAA,CAAA3K,cAAA,GAAAc,CAAA,SAAG4J,SAAS,CAACtH,GAAG,CAACuF,QAAQ,IAAK;MAAA;MAAA3I,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA;QACxC6H,QAAQ;QACRU,KAAK,EAAE,IAAI,CAACuB,kCAAkC,CAACjC,QAAQ,EAAEvE,OAAO;OACjE;KAAC,CAAC;IAAA;IAAApE,cAAA,GAAAc,CAAA;IAEH6J,MAAM,CAACE,IAAI,CAAC,CAACC,CAAC,EAAE3I,CAAC,KAAK;MAAA;MAAAnC,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAAqB,CAAC,CAACkH,KAAK,GAAGyB,CAAC,CAACzB,KAAK;IAAL,CAAK,CAAC;IAAA;IAAArJ,cAAA,GAAAc,CAAA;IACxC,OAAO6J,MAAM,CAAC,CAAC,CAAC,CAAChC,QAAQ;EAC3B;EAEA;;;EAGQiC,kCAAkCA,CACxCjC,QAAqE,EACrEvE,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAE1B,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMiK,aAAa;IAAA;IAAA,CAAA/K,cAAA,GAAAc,CAAA,SAAG6H,QAAQ,CAACqC,uBAAuB;IACtD,MAAMC,UAAU;IAAA;IAAA,CAAAjL,cAAA,GAAAc,CAAA,SAAG,CACjBiK,aAAa,CAACG,WAAW,GACzBH,aAAa,CAACI,OAAO,GACrBJ,aAAa,CAACK,YAAY,GAC1BL,aAAa,CAACM,gBAAgB,GAC9BN,aAAa,CAACO,kBAAkB,GAChCP,aAAa,CAACtE,YAAY,GAC1BsE,aAAa,CAACpE,UAAU,GACxBoE,aAAa,CAACQ,YAAY,IACxB,CAAC;IAAA;IAAAvL,cAAA,GAAAc,CAAA;IACLuI,KAAK,IAAI4B,UAAU,GAAG,GAAG;IAEzB;IAAA;IAAAjL,cAAA,GAAAc,CAAA;IACAuI,KAAK,IAAIV,QAAQ,CAAC6C,wBAAwB,GAAG,GAAG;IAEhD;IAAA;IAAAxL,cAAA,GAAAc,CAAA;IACAuI,KAAK,IAAIV,QAAQ,CAACW,eAAe,GAAG,GAAG;IAEvC;IAAA;IAAAtJ,cAAA,GAAAc,CAAA;IACAuI,KAAK,IAAIV,QAAQ,CAAC8C,qBAAqB,GAAG,GAAG;IAAA;IAAAzL,cAAA,GAAAc,CAAA;IAE7C,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,GAAG,CAAC;EAC7B;EAEA;;;EAGQ1D,iBAAiBA,CAACF,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACvD;IACA,MAAMwK,MAAM;IAAA;IAAA,CAAA1L,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACoF,IAAI,CAAC,CAACC,CAAC,EAAE3I,CAAC,KAAK;MAAA;MAAAnC,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAAgK,CAAC,CAAClC,QAAQ,GAAGzG,CAAC,CAACyG,QAAQ;IAAR,CAAQ,CAAC;IAEjE;IAAA;IAAA5I,cAAA,GAAAc,CAAA;IACA,OAAO4K,MAAM,CAACtI,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACjB,QAAQ,CAACzD,IAAI;IAAJ,CAAI,CAAC,CAAC3B,IAAI,CAAC,EAAE,CAAC;EAClD;EAEA;;;EAGQ2D,6BAA6BA,CACnCzB,UAAyE,EACzExB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAEtB,IAAI2E,UAAU,CAACd,MAAM,KAAK,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,OAAO,EAAE;IAAA;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAEtC;IAAAnC,cAAA,GAAAc,CAAA;IACA,QAAQmD,QAAQ;MACd,KAAKzD,YAAY,CAACwH,KAAK;QAAA;QAAAhI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO2E,UAAU,CAACrC,GAAG,CAAC,CAACuF,QAAQ,EAAEgD,KAAK,KAAI;UAAA;UAAA3L,cAAA,GAAAkB,CAAA;UACxC,MAAMgE,IAAI;UAAA;UAAA,CAAAlF,cAAA,GAAAc,CAAA,SAAG6H,QAAQ,CAACzD,IAAI;UAAA;UAAAlF,cAAA,GAAAc,CAAA;UAC1B,OAAO6K,KAAK,KAAK,CAAC;UAAA;UAAA,CAAA3L,cAAA,GAAAmC,CAAA,WACd+C,IAAI,CAAC0G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG3G,IAAI,CAAC4G,KAAK,CAAC,CAAC,CAAC,CAAC1B,WAAW,EAAE;UAAA;UAAA,CAAApK,cAAA,GAAAmC,CAAA,WAC1D+C,IAAI,CAAC0G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG3G,IAAI,CAAC4G,KAAK,CAAC,CAAC,CAAC,CAAC1B,WAAW,EAAE;QAChE,CAAC,CAAC,CAAC7G,IAAI,CAAC,EAAE,CAAC;MAEb,KAAK/C,YAAY,CAACyH,KAAK;QAAA;QAAAjI,cAAA,GAAAmC,CAAA;MACvB,KAAK3B,YAAY,CAAC0H,KAAK;QAAA;QAAAlI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO2E,UAAU,CAACrC,GAAG,CAAC2G,CAAC,IAAI;UAAA;UAAA/J,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAc,CAAA;UAAA,OAAAiJ,CAAC,CAAC7E,IAAI;QAAJ,CAAI,CAAC,CAAC3B,IAAI,CAAC,EAAE,CAAC;MAE7C,KAAK/C,YAAY,CAAC2H,KAAK;QAAA;QAAAnI,cAAA,GAAAmC,CAAA;MACvB,KAAK3B,YAAY,CAAC4H,KAAK;QAAA;QAAApI,cAAA,GAAAmC,CAAA;MACvB,KAAK3B,YAAY,CAAC6H,KAAK;QAAA;QAAArI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO2E,UAAU,CAACrC,GAAG,CAAC,CAACuF,QAAQ,EAAEgD,KAAK,KAAI;UAAA;UAAA3L,cAAA,GAAAkB,CAAA;UACxC,MAAMgE,IAAI;UAAA;UAAA,CAAAlF,cAAA,GAAAc,CAAA,SAAG6H,QAAQ,CAACzD,IAAI;UAAA;UAAAlF,cAAA,GAAAc,CAAA;UAC1B,OAAO6K,KAAK,KAAK,CAAC;UAAA;UAAA,CAAA3L,cAAA,GAAAmC,CAAA,WACd+C,IAAI,CAAC0G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG3G,IAAI,CAAC4G,KAAK,CAAC,CAAC,CAAC,CAAC1B,WAAW,EAAE;UAAA;UAAA,CAAApK,cAAA,GAAAmC,CAAA,WAC1D+C,IAAI,CAAC0G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG3G,IAAI,CAAC4G,KAAK,CAAC,CAAC,CAAC,CAAC1B,WAAW,EAAE;QAChE,CAAC,CAAC,CAAC7G,IAAI,CAAC,EAAE,CAAC;MAEb,KAAK/C,YAAY,CAAC8H,KAAK;QAAA;QAAAtI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO2E,UAAU,CAACrC,GAAG,CAAC2G,CAAC,IAAI;UAAA;UAAA/J,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAc,CAAA;UAAA,OAAAiJ,CAAC,CAAC7E,IAAI;QAAJ,CAAI,CAAC,CAAC3B,IAAI,CAAC,EAAE,CAAC;MAE7C;QAAA;QAAAvD,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACE;QACA,OAAO2E,UAAU,CAACrC,GAAG,CAAC2G,CAAC,IAAI;UAAA;UAAA/J,cAAA,GAAAkB,CAAA;UAAAlB,cAAA,GAAAc,CAAA;UAAA,OAAAiJ,CAAC,CAAC7E,IAAI;QAAJ,CAAI,CAAC,CAAC3B,IAAI,CAAC,EAAE,CAAC;IAC/C;EACF;EAEA;;;;;EAKQ8D,kCAAkCA,CACxC0E,qBAAoF;IAAA;IAAA/L,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAEpF,OAAOiL,qBAAqB,CAAC3I,GAAG,CAAC,CAACuF,QAAQ,EAAEgD,KAAK,KAAM;MAAA;MAAA3L,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA;QACrD6H,QAAQ,EAAE;UACRqD,EAAE,EAAErD,QAAQ,CAACsD,WAAW;UACxB/G,IAAI,EAAEyD,QAAQ,CAACzD,IAAI;UACnBgH,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACxD,QAAQ,CAACqB,kBAAkB,CAACC,OAAO,CAAC;UACpEmC,WAAW,EAAEzD,QAAQ,CAACqB,kBAAkB,CAACqC,kBAAkB;UAC3D9C,gBAAgB,EAAE,IAAI,CAAC+C,kBAAkB,CAAC3D,QAAQ,CAACY,gBAAgB,CAAC;UACpED,eAAe,EAAEX,QAAQ,CAACW,eAAe;UACzCrD,aAAa,EAAE0C,QAAQ,CAAC8C,qBAAqB;UAC7Cc,eAAe,EAAE,IAAI,CAACC,qBAAqB,CAAC7D,QAAQ,CAAC;UACrD8D,IAAI,EAAE9D,QAAQ,CAACY,gBAAgB,CAACW,aAAa;UAC7CwC,mBAAmB,EAAE;YACnBC,cAAc,EAAEhE,QAAQ,CAACiE,iBAAiB,CAACD,cAAc;YACzDE,eAAe,EAAElE,QAAQ,CAACzD,IAAI,CAACP,MAAM;YACrCiI,iBAAiB,EAAE,CAACjE,QAAQ,CAACiE,iBAAiB,CAACE,iBAAiB,CAAC;YACjET,kBAAkB,EAAE1D,QAAQ,CAACqB,kBAAkB,CAACqC,kBAAkB;YAClEU,aAAa,EAAEpE,QAAQ,CAACiE,iBAAiB,CAACE;WAC3C;UACDE,eAAe,EAAE;YACf9B,WAAW,EAAEvC,QAAQ,CAACqC,uBAAuB,CAACE,WAAW;YACzD+B,SAAS,EAAEtE,QAAQ,CAACW,eAAe;YACnC4D,aAAa,EAAEvE,QAAQ,CAAC6C,wBAAwB;YAChDH,gBAAgB,EAAE1C,QAAQ,CAACqC,uBAAuB,CAACK;WACpD;UACD8B,UAAU,EAAE5K,IAAI,CAACC,GAAG,EAAE;UACtB4K,MAAM,EAAEzE,QAAQ,CAACyE,MAAM;UACvBC,OAAO,EAAE1E,QAAQ,CAAC0E;SACnB;QACDzE,QAAQ,EAAE+C,KAAK;QACf9C,IAAI,EAAE8C,KAAK,KAAK,CAAC;QAAA;QAAA,CAAA3L,cAAA,GAAAmC,CAAA,WAAG,MAAM;QAAA;QAAA,CAAAnC,cAAA,GAAAmC,CAAA,WAAG,UAAU;QACvC2G,kBAAkB,EAAEH,QAAQ,CAAC8C;OAC9B;KAAC,CAAC;EACL;EAEA;;;EAGQU,gBAAgBA,CAACmB,MAAc;IAAA;IAAAtN,cAAA,GAAAkB,CAAA;IACrC,MAAMqM,OAAO;IAAA;IAAA,CAAAvN,cAAA,GAAAc,CAAA,SAAqC;MAChD,KAAK,EAAEZ,gBAAgB,CAAC+I,eAAe;MACvC,MAAM,EAAE/I,gBAAgB,CAAC8I,WAAW;MACpC,MAAM,EAAE9I,gBAAgB,CAACsN,OAAO;MAChC,KAAK,EAAEtN,gBAAgB,CAAC+I;KACzB;IAAA;IAAAjJ,cAAA,GAAAc,CAAA;IACD,OAAO,2BAAAd,cAAA,GAAAmC,CAAA,WAAAoL,OAAO,CAACD,MAAM,CAAC;IAAA;IAAA,CAAAtN,cAAA,GAAAmC,CAAA,WAAIjC,gBAAgB,CAACuN,QAAQ;EACrD;EAEA;;;EAGQnB,kBAAkBA,CACxBoB,eAAgG;IAAA;IAAA1N,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAEhG;IACA,IAAI4M,eAAe,CAACC,cAAc,GAAG,GAAG,EAAE;MAAA;MAAA3N,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACxC,OAAO,SAAgB;IACzB,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAI4M,eAAe,CAACE,SAAS,GAAG,GAAG,EAAE;QAAA;QAAA5N,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC1C,OAAO,QAAe;MACxB,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO,SAAgB;MACzB;IAAA;EACF;EAEA;;;EAGQ0L,qBAAqBA,CAC3B7D,QAAqE;IAAA;IAAA3I,cAAA,GAAAkB,CAAA;IAErE;IACA,MAAM2M,MAAM;IAAA;IAAA,CAAA7N,cAAA,GAAAc,CAAA,SAAa,IAAIgN,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAE9C,MAAMhD,aAAa;IAAA;IAAA,CAAA/K,cAAA,GAAAc,CAAA,SAAG6H,QAAQ,CAACqC,uBAAuB;IAAA;IAAAhL,cAAA,GAAAc,CAAA;IACtD+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACG,WAAW;IAAA;IAAAlL,cAAA,GAAAc,CAAA;IACrC+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACI,OAAO;IAAA;IAAAnL,cAAA,GAAAc,CAAA;IACjC+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACK,YAAY;IAAA;IAAApL,cAAA,GAAAc,CAAA;IACtC+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACM,gBAAgB;IAAA;IAAArL,cAAA,GAAAc,CAAA;IAC1C+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACO,kBAAkB;IAAA;IAAAtL,cAAA,GAAAc,CAAA;IAC5C+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACtE,YAAY;IAAA;IAAAzG,cAAA,GAAAc,CAAA;IACtC+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACpE,UAAU;IAAA;IAAA3G,cAAA,GAAAc,CAAA;IACpC+M,MAAM,CAAC,CAAC,CAAC,GAAG9C,aAAa,CAACQ,YAAY;IAEtC,MAAMyC,QAAQ;IAAA;IAAA,CAAAhO,cAAA,GAAAc,CAAA,SAAG6H,QAAQ,CAACY,gBAAgB;IAAA;IAAAvJ,cAAA,GAAAc,CAAA;IAC1C+M,MAAM,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACL,cAAc;IAAA;IAAA3N,cAAA,GAAAc,CAAA;IACnC+M,MAAM,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACJ,SAAS;IAAA;IAAA5N,cAAA,GAAAc,CAAA;IAC9B+M,MAAM,CAAC,EAAE,CAAC,GAAGG,QAAQ,CAACC,SAAS;IAAA;IAAAjO,cAAA,GAAAc,CAAA;IAC/B+M,MAAM,CAAC,EAAE,CAAC,GAAGG,QAAQ,CAACE,WAAW;IAAA;IAAAlO,cAAA,GAAAc,CAAA;IACjC+M,MAAM,CAAC,EAAE,CAAC,GAAGG,QAAQ,CAACG,qBAAqB;IAAA;IAAAnO,cAAA,GAAAc,CAAA;IAE3C+M,MAAM,CAAC,EAAE,CAAC,GAAGlF,QAAQ,CAACiE,iBAAiB,CAACwB,gBAAgB;IAAA;IAAApO,cAAA,GAAAc,CAAA;IACxD+M,MAAM,CAAC,EAAE,CAAC,GAAGlF,QAAQ,CAACiE,iBAAiB,CAACD,cAAc,GAAG,CAAC;IAAA;IAAA3M,cAAA,GAAAc,CAAA;IAC1D+M,MAAM,CAAC,EAAE,CAAC,GAAGlF,QAAQ,CAACW,eAAe;IAAA;IAAAtJ,cAAA,GAAAc,CAAA;IACrC+M,MAAM,CAAC,EAAE,CAAC,GAAGlF,QAAQ,CAAC8C,qBAAqB;IAAA;IAAAzL,cAAA,GAAAc,CAAA;IAC3C+M,MAAM,CAAC,EAAE,CAAC,GAAGlF,QAAQ,CAAC6C,wBAAwB;IAAA;IAAAxL,cAAA,GAAAc,CAAA;IAC9C+M,MAAM,CAAC,EAAE,CAAC,GAAGlF,QAAQ,CAAC0F,gBAAgB;IAAA;IAAArO,cAAA,GAAAc,CAAA;IACtC+M,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAACS,2BAA2B,CAAC3F,QAAQ,CAAC1E,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IAEhE,OAAO+M,MAAM;EACf;EAEA;;;EAGQS,2BAA2BA,CAACrK,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IACxD;IACA,MAAMqN,cAAc;IAAA;IAAA,CAAAvO,cAAA,GAAAc,CAAA,SAAiC;MACnD,CAACN,YAAY,CAACmB,KAAK,GAAG,GAAG;MAAE;MAC3B,CAACnB,YAAY,CAACwH,KAAK,GAAG,GAAG;MAAE;MAC3B,CAACxH,YAAY,CAACyH,KAAK,GAAG,GAAG;MAAE;MAC3B,CAACzH,YAAY,CAAC0H,KAAK,GAAG,IAAI;MAAE;MAC5B,CAAC1H,YAAY,CAAC2H,KAAK,GAAG,GAAG;MAAE;MAC3B,CAAC3H,YAAY,CAAC4H,KAAK,GAAG,IAAI;MAAE;MAC5B,CAAC5H,YAAY,CAAC6H,KAAK,GAAG,IAAI;MAAE;MAC5B,CAAC7H,YAAY,CAAC8H,KAAK,GAAG,IAAI,CAAE;KAC7B;IAAA;IAAAtI,cAAA,GAAAc,CAAA;IAED,OAAO,2BAAAd,cAAA,GAAAmC,CAAA,WAAAoM,cAAc,CAACtK,QAAQ,CAAC;IAAA;IAAA,CAAAjE,cAAA,GAAAmC,CAAA,WAAI,GAAG;EACxC;EAEA;;;EAGQgF,2BAA2BA,CACjCjC,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,MAAMoB,SAAS;IAAA;IAAA,CAAAtC,cAAA,GAAAc,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE;IAE5B;IACA,MAAM+D,UAAU;IAAA;IAAA,CAAAvG,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACyG,+BAA+B,CAACrC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAC5F,MAAMwC,YAAY;IAAA;IAAA,CAAAzG,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC0G,iCAAiC,CAACtC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAChG,MAAMoC,YAAY;IAAA;IAAA,CAAArG,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwG,gCAAgC,CAACpC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAC/F,MAAM0C,UAAU;IAAA;IAAA,CAAA3G,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC2G,+BAA+B,CAACvC,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAC5F,MAAM8I,aAAa;IAAA;IAAA,CAAA/M,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC0N,kCAAkC,CAACtJ,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAClG,MAAMwK,kBAAkB;IAAA;IAAA,CAAAzO,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC4N,sCAAsC,CAACxJ,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAC3G,MAAMoH,gBAAgB;IAAA;IAAA,CAAArL,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC6N,oCAAoC,CAACzJ,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IACvG,MAAM2K,mBAAmB;IAAA;IAAA,CAAA5O,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC+N,uCAAuC,CAAC3J,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IAE7G;IACA,MAAM6K,OAAO;IAAA;IAAA,CAAA9O,cAAA,GAAAc,CAAA,SAAG;MACdyF,UAAU,EAAE,IAAI;MAChBE,YAAY,EAAE,IAAI;MAClBJ,YAAY,EAAE,IAAI;MAClBM,UAAU,EAAE,IAAI;MAChBoG,aAAa,EAAE,IAAI;MACnB0B,kBAAkB,EAAE,IAAI;MACxBpD,gBAAgB,EAAE,IAAI;MACtBuD,mBAAmB,EAAE;KACtB;IAED,MAAM9I,OAAO;IAAA;IAAA,CAAA9F,cAAA,GAAAc,CAAA,SACXyF,UAAU,GAAGuI,OAAO,CAACvI,UAAU,GAC/BE,YAAY,GAAGqI,OAAO,CAACrI,YAAY,GACnCJ,YAAY,GAAGyI,OAAO,CAACzI,YAAY,GACnCM,UAAU,GAAGmI,OAAO,CAACnI,UAAU,GAC/BoG,aAAa,GAAG+B,OAAO,CAAC/B,aAAa,GACrC0B,kBAAkB,GAAGK,OAAO,CAACL,kBAAkB,GAC/CpD,gBAAgB,GAAGyD,OAAO,CAACzD,gBAAgB,GAC3CuD,mBAAmB,GAAGE,OAAO,CAACF,mBAAmB,CAClD;IAED,MAAMG,UAAU;IAAA;IAAA,CAAA/O,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACkO,yCAAyC,CAAC9J,IAAI,EAAEO,UAAU,EAAErB,OAAO,EAAEH,QAAQ,CAAC;IACtG,MAAM;MAAEgL,MAAM;MAAEC;IAAW,CAAE;IAAA;IAAA,CAAAlP,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACqO,gCAAgC,CAACjK,IAAI,EAAEO,UAAU,EAAExB,QAAQ,EAAE;MAChGsC,UAAU;MAAEE,YAAY;MAAEJ,YAAY;MAAEM,UAAU;MAClDoG,aAAa;MAAE0B,kBAAkB;MAAEpD,gBAAgB;MAAEuD;KACtD,CAAC;IAAA;IAAA5O,cAAA,GAAAc,CAAA;IAEF,OAAO;MACLgF,OAAO,EAAE8B,IAAI,CAAC4B,GAAG,CAAC5B,IAAI,CAACwH,GAAG,CAACtJ,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1CuJ,UAAU,EAAE;QACV9I,UAAU;QACVE,YAAY;QACZJ,YAAY;QACZM,UAAU;QACVoG,aAAa;QACb0B,kBAAkB;QAClBpD,gBAAgB;QAChBuD;OACD;MACDG,UAAU;MACVO,eAAe,EAAE/M,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MACvCiN,iBAAiB,EAAE,oBAAoB;MACvCN,MAAM;MACNC,WAAW;MACXM,SAAS,EAAEjN,IAAI,CAACC,GAAG;KACpB;EACH;EAEA;;;EAGQ+E,+BAA+BA,CACrCrC,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAM2O,QAAQ;IAAA;IAAA,CAAAzP,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAACjK,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACI,kBAAkB,CAACC,OAAO;IAAP,CAAO,CAAC,CAAC;IAC3E,MAAM0F,cAAc;IAAA;IAAA,CAAA3P,cAAA,GAAAc,CAAA,SAAG2O,QAAQ,CAACG,IAAI,GAAGhI,IAAI,CAAC4B,GAAG,CAAC/D,UAAU,CAACd,MAAM,EAAE,CAAC,CAAC;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACrEuI,KAAK,IAAIsG,cAAc,GAAG,GAAG;IAE7B;IACA,MAAME,iBAAiB;IAAA;IAAA,CAAA7P,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACgP,0BAA0B,CAACrK,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACrEuI,KAAK,IAAIwG,iBAAiB,GAAG,GAAG;IAEhC;IACA,MAAME,qBAAqB;IAAA;IAAA,CAAA/P,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACkP,mCAAmC,CAAC9K,IAAI,EAAEjB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IACtFuI,KAAK,IAAI0G,qBAAqB,GAAG,GAAG;IAEpC;IACA,MAAME,WAAW;IAAA;IAAA,CAAAjQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACoP,oCAAoC,CAAChL,IAAI,EAAEjB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IAC7EuI,KAAK,IAAI4G,WAAW,GAAG,GAAG;IAAA;IAAAjQ,cAAA,GAAAc,CAAA;IAE1B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;EAGQ7B,iCAAiCA,CACvCtC,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMmP,WAAW;IAAA;IAAA,CAAAjQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACqP,2CAA2C,CAACjL,IAAI,EAAEjB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IACpFuI,KAAK,IAAI4G,WAAW,GAAG,IAAI;IAE3B;IACA,MAAMG,aAAa;IAAA;IAAA,CAAApQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACuP,yCAAyC,CAAC5K,UAAU,EAAExB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IAC1FuI,KAAK,IAAI+G,aAAa,GAAG,IAAI;IAE7B;IACA,MAAME,aAAa;IAAA;IAAA,CAAAtQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACyP,yCAAyC,CAAC9K,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAChFuI,KAAK,IAAIiH,aAAa,GAAG,IAAI;IAE7B;IACA,MAAME,aAAa;IAAA;IAAA,CAAAxQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC2P,6BAA6B,CAAChL,UAAU,EAAExB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IAC9EuI,KAAK,IAAImH,aAAa,GAAG,IAAI;IAAA;IAAAxQ,cAAA,GAAAc,CAAA;IAE7B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;EAGQ/B,gCAAgCA,CACtCpC,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAM4P,2BAA2B;IAAA;IAAA,CAAA1Q,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkL,MAAM,CAAC,CAACC,GAAG,EAAEhH,CAAC,KAAK;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGhH,CAAC,CAAC4B,wBAAwB;IAAxB,CAAwB,EAAE,CAAC,CAAC,GAAG/F,UAAU,CAACd,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IAC1HuI,KAAK,IAAIqH,2BAA2B,GAAG,GAAG;IAE1C;IACA,MAAMG,YAAY;IAAA;IAAA,CAAA7Q,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACgQ,oCAAoC,CAACrL,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAC1EuI,KAAK,IAAIwH,YAAY,GAAG,GAAG;IAE3B;IACA,MAAME,cAAc;IAAA;IAAA,CAAA/Q,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACkQ,mCAAmC,CAACvL,UAAU,EAAExB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IACrFuI,KAAK,IAAI0H,cAAc,GAAG,GAAG;IAAA;IAAA/Q,cAAA,GAAAc,CAAA;IAE7B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;EAGQ5B,+BAA+BA,CACrCvC,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMmQ,YAAY;IAAA;IAAA,CAAAjR,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,QAAC,GAAG8I,CAAC,CAACN,eAAe;IAAf,CAAe,CAAC;IAC/D,MAAM4H,SAAS;IAAA;IAAA,CAAAlR,cAAA,GAAAc,CAAA,SAAGmQ,YAAY,CAACN,MAAM,CAAC,CAACC,GAAG,EAAEO,CAAC,KAAK;MAAA;MAAAnR,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGO,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGF,YAAY,CAACtM,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACnFuI,KAAK,IAAI6H,SAAS,GAAG,GAAG;IAExB;IACA,MAAME,kBAAkB;IAAA;IAAA,CAAApR,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACuQ,mCAAmC,CAACnM,IAAI,EAAEjB,QAAQ,CAAC;IAAA;IAAAjE,cAAA,GAAAc,CAAA;IACnFuI,KAAK,IAAI+H,kBAAkB,GAAG,IAAI;IAElC;IACA,MAAME,gBAAgB;IAAA;IAAA,CAAAtR,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACyQ,0CAA0C,CAAC9L,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACpFuI,KAAK,IAAIiI,gBAAgB,GAAG,IAAI;IAAA;IAAAtR,cAAA,GAAAc,CAAA;IAEhC,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;EAGQmF,kCAAkCA,CACxCtJ,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB;IACA,MAAMsQ,mBAAmB;IAAA;IAAA,CAAAxR,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACoB,uBAAuB,CAACM,kBAAkB;IAAlB,CAAkB,CAAC;IAAA;IAAAtL,cAAA,GAAAc,CAAA;IAC7F,OAAO0Q,mBAAmB,CAACb,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAGmI,mBAAmB,CAAC7M,MAAM;EAChG;EAEA;;;EAGQ+J,sCAAsCA,CAC5CxJ,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAEtB,IAAI2E,UAAU,CAACd,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,OAAO,GAAG;IAAA;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAErC;IACA,IAAIsP,cAAc;IAAA;IAAA,CAAAzR,cAAA,GAAAc,CAAA,SAAG,CAAC;IACtB,IAAI4Q,SAAS;IAAA;IAAA,CAAA1R,cAAA,GAAAc,CAAA,SAAG,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IAEjB,KAAK,IAAI6Q,CAAC;IAAA;IAAA,CAAA3R,cAAA,GAAAc,CAAA,SAAG,CAAC,GAAE6Q,CAAC,GAAGlM,UAAU,CAACd,MAAM,GAAG,CAAC,EAAEgN,CAAC,EAAE,EAAE;MAAA;MAAA3R,cAAA,GAAAc,CAAA;MAC9C,KAAK,IAAI8Q,CAAC;MAAA;MAAA,CAAA5R,cAAA,GAAAc,CAAA,SAAG6Q,CAAC,GAAG,CAAC,GAAEC,CAAC,GAAGnM,UAAU,CAACd,MAAM,EAAEiN,CAAC,EAAE,EAAE;QAC9C,MAAMC,SAAS;QAAA;QAAA,CAAA7R,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACgR,wCAAwC,CAACrM,UAAU,CAACkM,CAAC,CAAC,EAAElM,UAAU,CAACmM,CAAC,CAAC,CAAC;QAAA;QAAA5R,cAAA,GAAAc,CAAA;QAC7F2Q,cAAc,IAAII,SAAS;QAAA;QAAA7R,cAAA,GAAAc,CAAA;QAC3B4Q,SAAS,EAAE;MACb;IACF;IAAC;IAAA1R,cAAA,GAAAc,CAAA;IAED,OAAO4Q,SAAS,GAAG,CAAC;IAAA;IAAA,CAAA1R,cAAA,GAAAmC,CAAA,WAAGsP,cAAc,GAAGC,SAAS;IAAA;IAAA,CAAA1R,cAAA,GAAAmC,CAAA,WAAG,GAAG;EACzD;EAEA;;;EAGQwM,oCAAoCA,CAC1CzJ,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB;IACA,MAAM6Q,eAAe;IAAA;IAAA,CAAA/R,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACoB,uBAAuB,CAACK,gBAAgB;IAAhB,CAAgB,CAAC;IAAA;IAAArL,cAAA,GAAAc,CAAA;IACvF,OAAOiR,eAAe,CAACpB,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAG0I,eAAe,CAACpN,MAAM;EACxF;EAEA;;;EAGQkK,uCAAuCA,CAC7C3J,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB;IACA,MAAM8Q,kBAAkB;IAAA;IAAA,CAAAhS,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACoB,uBAAuB,CAACO,YAAY;IAAZ,CAAY,CAAC;IAAA;IAAAvL,cAAA,GAAAc,CAAA;IACtF,OAAOkR,kBAAkB,CAACrB,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAG2I,kBAAkB,CAACrN,MAAM;EAC9F;EAEA;;;EAGQyC,+BAA+BA,CACrClC,IAAY,EACZO,UAAyE,EACzEF,OAA2C,EAC3CtB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,MAAM+Q,aAAa;IAAA;IAAA,CAAAjS,cAAA,GAAAc,CAAA,SAAiC;MAClD,CAACN,YAAY,CAACmB,KAAK,GAAG,IAAI;MAC1B,CAACnB,YAAY,CAACwH,KAAK,GAAG,SAAS;MAC/B,CAACxH,YAAY,CAACyH,KAAK,GAAG,KAAK;MAC3B,CAACzH,YAAY,CAAC0H,KAAK,GAAG,KAAK;MAC3B,CAAC1H,YAAY,CAAC2H,KAAK,GAAG,SAAS;MAC/B,CAAC3H,YAAY,CAAC4H,KAAK,GAAG,UAAU;MAChC,CAAC5H,YAAY,CAAC6H,KAAK,GAAG,SAAS;MAC/B,CAAC7H,YAAY,CAAC8H,KAAK,GAAG;KACvB;IAED,MAAM4J,QAAQ;IAAA;IAAA,CAAAlS,cAAA,GAAAc,CAAA;IAAG;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAA8P,aAAa,CAAChO,QAAQ,CAAC;IAAA;IAAA,CAAAjE,cAAA,GAAAmC,CAAA,WAAI8B,QAAQ;IACpD,MAAMkO,aAAa;IAAA;IAAA,CAAAnS,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAAC1E,IAAI;IAAJ,CAAI,CAAC,CAAC3B,IAAI,CAAC,KAAK,CAAC;IAAA;IAAAvD,cAAA,GAAAc,CAAA;IAE7D,OAAO,GAAGoR,QAAQ,OAAOhN,IAAI,MAAMK,OAAO,CAACS,IAAI,UAAUmM,aAAa,YAAYlO,QAAQ,eAAe;EAC3G;EAEA;EACA;EACA;EAEA;;;EAGQ6L,0BAA0BA,CAChCrK,UAAyE;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAEzE,MAAMkR,cAAc;IAAA;IAAA,CAAApS,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAK;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA;QAC1C6M,cAAc,EAAE/D,CAAC,CAACL,gBAAgB,CAACoE,cAAc;QACjDC,SAAS,EAAEhE,CAAC,CAACL,gBAAgB,CAACqE,SAAS;QACvCK,SAAS,EAAErE,CAAC,CAACL,gBAAgB,CAAC0E;OAC/B;KAAC,CAAC;IAEH;IACA,MAAMoE,sBAAsB;IAAA;IAAA,CAAArS,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwR,iBAAiB,CAACF,cAAc,CAAChP,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAAC+D,cAAc;IAAd,CAAc,CAAC,CAAC;IAChG,MAAM4E,iBAAiB;IAAA;IAAA,CAAAvS,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwR,iBAAiB,CAACF,cAAc,CAAChP,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACgE,SAAS;IAAT,CAAS,CAAC,CAAC;IACtF,MAAM4E,iBAAiB;IAAA;IAAA,CAAAxS,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwR,iBAAiB,CAACF,cAAc,CAAChP,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACqE,SAAS;IAAT,CAAS,CAAC,CAAC;IAAA;IAAAjO,cAAA,GAAAc,CAAA;IAEtF,OAAO,CAACuR,sBAAsB,GAAGE,iBAAiB,GAAGC,iBAAiB,IAAI,CAAC;EAC7E;EAEA;;;EAGQxC,mCAAmCA,CAAC9K,IAAY,EAAEjB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC9E;IACA,QAAQmD,QAAQ;MACd,KAAKzD,YAAY,CAACwH,KAAK;QAAA;QAAAhI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO,IAAI,CAAC2R,mBAAmB,CAACvN,IAAI,CAAC;QAAA;QAAA,CAAAlF,cAAA,GAAAmC,CAAA,WAAG,GAAG;QAAA;QAAA,CAAAnC,cAAA,GAAAmC,CAAA,WAAG,GAAG;MACnD,KAAK3B,YAAY,CAACyH,KAAK;QAAA;QAAAjI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO,IAAI,CAAC4R,mBAAmB,CAACxN,IAAI,CAAC;QAAA;QAAA,CAAAlF,cAAA,GAAAmC,CAAA,WAAG,GAAG;QAAA;QAAA,CAAAnC,cAAA,GAAAmC,CAAA,WAAG,GAAG;MACnD,KAAK3B,YAAY,CAAC0H,KAAK;QAAA;QAAAlI,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrB;QACA,OAAO,IAAI,CAAC6R,mBAAmB,CAACzN,IAAI,CAAC;QAAA;QAAA,CAAAlF,cAAA,GAAAmC,CAAA,WAAG,GAAG;QAAA;QAAA,CAAAnC,cAAA,GAAAmC,CAAA,WAAG,GAAG;MACnD;QAAA;QAAAnC,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACE,OAAO,GAAG;IACd;EACF;EAEA;;;EAGQoP,oCAAoCA,CAAChL,IAAY,EAAEjB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAC/E,MAAMyD,MAAM;IAAA;IAAA,CAAA3E,cAAA,GAAAc,CAAA,SAAGoE,IAAI,CAACP,MAAM;IAE1B;IACA,MAAMiO,aAAa;IAAA;IAAA,CAAA5S,cAAA,GAAAc,CAAA,SAAuD;MACxE,CAACN,YAAY,CAACmB,KAAK,GAAG;QAAE6H,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAC,CAAE;MACxC,CAAC5O,YAAY,CAACwH,KAAK,GAAG;QAAEwB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAE,CAAE;MACzC,CAAC5O,YAAY,CAACyH,KAAK,GAAG;QAAEuB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAC,CAAE;MACxC,CAAC5O,YAAY,CAAC0H,KAAK,GAAG;QAAEsB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAC,CAAE;MACxC,CAAC5O,YAAY,CAAC2H,KAAK,GAAG;QAAEqB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAE,CAAE;MACzC,CAAC5O,YAAY,CAAC4H,KAAK,GAAG;QAAEoB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAE,CAAE;MACzC,CAAC5O,YAAY,CAAC6H,KAAK,GAAG;QAAEmB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAE,CAAE;MACzC,CAAC5O,YAAY,CAAC8H,KAAK,GAAG;QAAEkB,GAAG,EAAE,CAAC;QAAE4F,GAAG,EAAE;MAAC;KACvC;IAED,MAAMyD,KAAK;IAAA;IAAA,CAAA7S,cAAA,GAAAc,CAAA;IAAG;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAyQ,aAAa,CAAC3O,QAAQ,CAAC;IAAA;IAAA,CAAAjE,cAAA,GAAAmC,CAAA,WAAI;MAAEqH,GAAG,EAAE,CAAC;MAAE4F,GAAG,EAAE;IAAE,CAAE;IAAA;IAAApP,cAAA,GAAAc,CAAA;IAE5D;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAwC,MAAM,IAAIkO,KAAK,CAACrJ,GAAG;IAAA;IAAA,CAAAxJ,cAAA,GAAAmC,CAAA,WAAIwC,MAAM,IAAIkO,KAAK,CAACzD,GAAG,GAAE;MAAA;MAAApP,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC9C,OAAO,GAAG;IACZ,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAI6D,MAAM,GAAGkO,KAAK,CAACrJ,GAAG,EAAE;QAAA;QAAAxJ,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC7B,OAAO8G,IAAI,CAACwH,GAAG,CAAC,GAAG,EAAEzK,MAAM,GAAGkO,KAAK,CAACrJ,GAAG,CAAC;MAC1C,CAAC,MAAM;QAAA;QAAAxJ,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO8G,IAAI,CAACwH,GAAG,CAAC,GAAG,EAAEyD,KAAK,CAACzD,GAAG,GAAGzK,MAAM,CAAC;MAC1C;IAAA;EACF;EAEA;;;EAGQ2N,iBAAiBA,CAACQ,MAAgB;IAAA;IAAA9S,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACxC,IAAIgS,MAAM,CAACnO,MAAM,KAAK,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,OAAO,CAAC;IAAA;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IACjC,MAAM4Q,IAAI;IAAA;IAAA,CAAA/S,cAAA,GAAAc,CAAA,SAAGgS,MAAM,CAACnC,MAAM,CAAC,CAACC,GAAG,EAAEoC,GAAG,KAAK;MAAA;MAAAhT,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGoC,GAAG;IAAH,CAAG,EAAE,CAAC,CAAC,GAAGF,MAAM,CAACnO,MAAM;IACtE,MAAMsO,QAAQ;IAAA;IAAA,CAAAjT,cAAA,GAAAc,CAAA,SAAGgS,MAAM,CAACnC,MAAM,CAAC,CAACC,GAAG,EAAEoC,GAAG,KAAK;MAAA;MAAAhT,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGhJ,IAAI,CAACsL,GAAG,CAACF,GAAG,GAAGD,IAAI,EAAE,CAAC,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGD,MAAM,CAACnO,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IAC9F,OAAOmS,QAAQ;EACjB;EAEA;;;EAGQR,mBAAmBA,CAACvN,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACtC,OAAO,OAAO,CAACqS,IAAI,CAACjO,IAAI,CAAC;EAC3B;EAEA;;;EAGQwN,mBAAmBA,CAACxN,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IACtC,MAAMkS,QAAQ;IAAA;IAAA,CAAApT,cAAA,GAAAc,CAAA,SAAG,iBAAiB,CAACqS,IAAI,CAACjO,IAAI,CAAC;IAC7C,MAAMmO,WAAW;IAAA;IAAA,CAAArT,cAAA,GAAAc,CAAA,SAAG,iBAAiB,CAACqS,IAAI,CAACjO,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAChD,OAAO,2BAAAd,cAAA,GAAAmC,CAAA,WAAAiR,QAAQ;IAAA;IAAA,CAAApT,cAAA,GAAAmC,CAAA,WAAIkR,WAAW;EAChC;EAEA;;;EAGQV,mBAAmBA,CAACzN,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACtC,OAAO,iBAAiB,CAACqS,IAAI,CAACjO,IAAI,CAAC;EACrC;EAEA;EACQiL,2CAA2CA,CAACjL,IAAY,EAAEjB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACtF,OAAO,IAAI,CAACoP,oCAAoC,CAAChL,IAAI,EAAEjB,QAAQ,CAAC;EAClE;EAEQoM,yCAAyCA,CAC/C5K,UAAyE,EACzExB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,MAAMoS,cAAc;IAAA;IAAA,CAAAtT,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACgD,iBAAiB,CAACwB,gBAAgB;IAAhB,CAAgB,CAAC;IAAA;IAAApO,cAAA,GAAAc,CAAA;IAChF,OAAOwS,cAAc,CAAC3C,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAGiK,cAAc,CAAC3O,MAAM;EACtF;EAEQ4L,yCAAyCA,CAC/C9K,UAAyE;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAEzE,MAAMqS,YAAY;IAAA;IAAA,CAAAvT,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACoB,uBAAuB,CAACvE,YAAY;IAAZ,CAAY,CAAC;IAAA;IAAAzG,cAAA,GAAAc,CAAA;IAChF,OAAOyS,YAAY,CAAC5C,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAGkK,YAAY,CAAC5O,MAAM;EAClF;EAEQ8L,6BAA6BA,CACnChL,UAAyE,EACzExB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAEtB,OAAO2E,UAAU,CAACkL,MAAM,CAAC,CAACC,GAAG,EAAEhH,CAAC,KAAK;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGhH,CAAC,CAAC4B,wBAAwB;IAAxB,CAAwB,EAAE,CAAC,CAAC,GAAG/F,UAAU,CAACd,MAAM;EAC/F;EAEQmM,oCAAoCA,CAC1CrL,UAAyE;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAEzE,IAAI2E,UAAU,CAACd,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,OAAO,GAAG;IAAA;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAErC;IACA,MAAMqR,kBAAkB;IAAA;IAAA,CAAAxT,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,QAC7C8I,CAAC,CAACL,gBAAgB,CAACoE,cAAc,EACjC/D,CAAC,CAACL,gBAAgB,CAACqE,SAAS,EAC5BhE,CAAC,CAACL,gBAAgB,CAAC0E,SAAS,CAC7B;KAAA,CAAC;IAEF,IAAIwF,YAAY;IAAA;IAAA,CAAAzT,cAAA,GAAAc,CAAA,SAAG,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACpB,KAAK,IAAI6Q,CAAC;IAAA;IAAA,CAAA3R,cAAA,GAAAc,CAAA,SAAG,CAAC,GAAE6Q,CAAC,GAAG6B,kBAAkB,CAAC,CAAC,CAAC,CAAC7O,MAAM,EAAEgN,CAAC,EAAE,EAAE;MACrD,MAAMmB,MAAM;MAAA;MAAA,CAAA9S,cAAA,GAAAc,CAAA,SAAG0S,kBAAkB,CAACpQ,GAAG,CAACsQ,KAAK,IAAI;QAAA;QAAA1T,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAc,CAAA;QAAA,OAAA4S,KAAK,CAAC/B,CAAC,CAAC;MAAD,CAAC,CAAC;MACxD,MAAMsB,QAAQ;MAAA;MAAA,CAAAjT,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwR,iBAAiB,CAACQ,MAAM,CAAC;MAAA;MAAA9S,cAAA,GAAAc,CAAA;MAC/C2S,YAAY,IAAK,CAAC,GAAG7L,IAAI,CAAC4B,GAAG,CAACyJ,QAAQ,EAAE,CAAC,CAAE;IAC7C;IAAC;IAAAjT,cAAA,GAAAc,CAAA;IAED,OAAO2S,YAAY,GAAGD,kBAAkB,CAAC,CAAC,CAAC,CAAC7O,MAAM;EACpD;EAEQqM,mCAAmCA,CACzCvL,UAAyE,EACzExB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB,MAAMyS,eAAe;IAAA;IAAA,CAAA3T,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACL,gBAAgB,CAACqE,SAAS;IAAT,CAAS,CAAC;IAAA;IAAA5N,cAAA,GAAAc,CAAA;IACzE,OAAO6S,eAAe,CAAChD,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAGsK,eAAe,CAAChP,MAAM;EACxF;EAEQ0M,mCAAmCA,CAACnM,IAAY,EAAEjB,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC9E;IACA,OAAO,GAAG,EAAC;EACb;EAEQyQ,0CAA0CA,CAChD9L,UAAyE;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAEzE;IACA,MAAM0S,gBAAgB;IAAA;IAAA,CAAA5T,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACoB,uBAAuB,CAACrE,UAAU;IAAV,CAAU,CAAC;IAAA;IAAA3G,cAAA,GAAAc,CAAA;IAClF,OAAO8S,gBAAgB,CAACjD,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAGuK,gBAAgB,CAACjP,MAAM;EAC1F;EAEQmN,wCAAwCA,CAC9C+B,SAAsE,EACtEC,SAAsE;IAAA;IAAA9T,cAAA,GAAAkB,CAAA;IAEtE;IACA,MAAM6S,KAAK;IAAA;IAAA,CAAA/T,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAACmE,SAAS,CAACtK,gBAAgB,CAACW,aAAa,CAAC;IAC/D,MAAM8J,KAAK;IAAA;IAAA,CAAAhU,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAACoE,SAAS,CAACvK,gBAAgB,CAACW,aAAa,CAAC;IAC/D,MAAM+J,YAAY;IAAA;IAAA,CAAAjU,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAAC,CAAC,GAAGqE,KAAK,CAAC,CAACpK,MAAM,CAACQ,GAAG,IAAI;MAAA;MAAAnK,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAAkT,KAAK,CAACE,GAAG,CAAC/J,GAAG,CAAC;IAAD,CAAC,CAAC,CAAC;IACtE,MAAMgK,KAAK;IAAA;IAAA,CAAAnU,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAAC,CAAC,GAAGqE,KAAK,EAAE,GAAGC,KAAK,CAAC,CAAC;IAAA;IAAAhU,cAAA,GAAAc,CAAA;IAE3C,OAAOqT,KAAK,CAACvE,IAAI,GAAG,CAAC;IAAA;IAAA,CAAA5P,cAAA,GAAAmC,CAAA,WAAG8R,YAAY,CAACrE,IAAI,GAAGuE,KAAK,CAACvE,IAAI;IAAA;IAAA,CAAA5P,cAAA,GAAAmC,CAAA,WAAG,GAAG;EAC9D;EAEQ6M,yCAAyCA,CAC/C9J,IAAY,EACZO,UAAyE,EACzErB,OAA0B,EAC1BH,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAEtB;IACA,MAAM6J,aAAa;IAAA;IAAA,CAAA/K,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAAC6B,qBAAqB;IAArB,CAAqB,CAAC;IAClE,MAAMR,UAAU;IAAA;IAAA,CAAAjL,cAAA,GAAAc,CAAA,SAAGiK,aAAa,CAAC4F,MAAM,CAAC,CAACC,GAAG,EAAEvH,KAAK,KAAK;MAAA;MAAArJ,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGvH,KAAK;IAAL,CAAK,EAAE,CAAC,CAAC,GAAG0B,aAAa,CAACpG,MAAM;IAC9F,MAAMsO,QAAQ;IAAA;IAAA,CAAAjT,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwR,iBAAiB,CAACvH,aAAa,CAAC;IAAA;IAAA/K,cAAA,GAAAc,CAAA;IAEtD,OAAOmK,UAAU,IAAI,CAAC,GAAGrD,IAAI,CAAC4B,GAAG,CAACyJ,QAAQ,EAAE,CAAC,CAAC,CAAC;EACjD;EAEQ9D,gCAAgCA,CACtCjK,IAAY,EACZO,UAAyE,EACzExB,QAAsB,EACtBoL,UAAe;IAAA;IAAArP,cAAA,GAAAkB,CAAA;IAEf,MAAM+N,MAAM;IAAA;IAAA,CAAAjP,cAAA,GAAAc,CAAA,SAAa,EAAE;IAC3B,MAAMoO,WAAW;IAAA;IAAA,CAAAlP,cAAA,GAAAc,CAAA,SAAa,EAAE;IAEhC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACA,IAAIuO,UAAU,CAACtC,aAAa,GAAG,GAAG,EAAE;MAAA;MAAA/M,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAClCmO,MAAM,CAAC9J,IAAI,CAAC,GAAGlB,QAAQ,QAAQ,CAAC;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MAChCoO,WAAW,CAAC/J,IAAI,CAAC,WAAWlB,QAAQ,IAAI,CAAC;IAC3C,CAAC;IAAA;IAAA;MAAAjE,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIuO,UAAU,CAAChJ,YAAY,GAAG,GAAG,EAAE;MAAA;MAAArG,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACjCmO,MAAM,CAAC9J,IAAI,CAAC,GAAGlB,QAAQ,SAAS,CAAC;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MACjCoO,WAAW,CAAC/J,IAAI,CAAC,KAAKlB,QAAQ,QAAQ,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAjE,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIuO,UAAU,CAAC5I,YAAY,GAAG,GAAG,EAAE;MAAA;MAAAzG,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACjCmO,MAAM,CAAC9J,IAAI,CAAC,GAAGlB,QAAQ,OAAO,CAAC;MAAA;MAAAjE,cAAA,GAAAc,CAAA;MAC/BoO,WAAW,CAAC/J,IAAI,CAAC,KAAKlB,QAAQ,UAAU,CAAC;IAC3C,CAAC;IAAA;IAAA;MAAAjE,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,OAAO;MAAEmO,MAAM;MAAEC;IAAW,CAAE;EAChC;EAEA;;;;;;;;;;;EAWQrJ,eAAeA,CAACX,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC/F,MAAMoB,SAAS;IAAA;IAAA,CAAAtC,cAAA,GAAAc,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE;IAE5B;IACA,MAAM+D,UAAU;IAAA;IAAA,CAAAvG,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC0F,wBAAwB,CAACtB,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAC3E,MAAMqC,YAAY;IAAA;IAAA,CAAAzG,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC4F,0BAA0B,CAACxB,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAC/E,MAAMiC,YAAY;IAAA;IAAA,CAAArG,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwF,yBAAyB,CAACpB,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAC9E,MAAMuC,UAAU;IAAA;IAAA,CAAA3G,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC8F,wBAAwB,CAAC1B,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAC3E,MAAM2I,aAAa;IAAA;IAAA,CAAA/M,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACsT,2BAA2B,CAAClP,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IACjF,MAAMqK,kBAAkB;IAAA;IAAA,CAAAzO,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACuT,+BAA+B,CAACnP,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAC1F,MAAMiH,gBAAgB;IAAA;IAAA,CAAArL,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwT,6BAA6B,CAACpP,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IACtF,MAAMwK,mBAAmB;IAAA;IAAA,CAAA5O,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACyT,gCAAgC,CAACrP,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAE5F;IACA,MAAM0K,OAAO;IAAA;IAAA,CAAA9O,cAAA,GAAAc,CAAA,SAAG;MACdyF,UAAU,EAAE,IAAI;MAAS;MACzBE,YAAY,EAAE,IAAI;MAAO;MACzBJ,YAAY,EAAE,IAAI;MAAO;MACzBM,UAAU,EAAE,IAAI;MAAS;MACzBoG,aAAa,EAAE,IAAI;MAAM;MACzB0B,kBAAkB,EAAE,IAAI;MAAE;MAC1BpD,gBAAgB,EAAE,IAAI;MAAI;MAC1BuD,mBAAmB,EAAE,IAAI,CAAC;KAC3B;IAED,MAAM9I,OAAO;IAAA;IAAA,CAAA9F,cAAA,GAAAc,CAAA,SACXyF,UAAU,GAAGuI,OAAO,CAACvI,UAAU,GAC/BE,YAAY,GAAGqI,OAAO,CAACrI,YAAY,GACnCJ,YAAY,GAAGyI,OAAO,CAACzI,YAAY,GACnCM,UAAU,GAAGmI,OAAO,CAACnI,UAAU,GAC/BoG,aAAa,GAAG+B,OAAO,CAAC/B,aAAa,GACrC0B,kBAAkB,GAAGK,OAAO,CAACL,kBAAkB,GAC/CpD,gBAAgB,GAAGyD,OAAO,CAACzD,gBAAgB,GAC3CuD,mBAAmB,GAAGE,OAAO,CAACF,mBAAmB,CAClD;IAED;IACA,MAAMG,UAAU;IAAA;IAAA,CAAA/O,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC0T,6BAA6B,CAACtP,IAAI,EAAEO,UAAU,EAAErB,OAAO,CAAC;IAEhF;IACA,MAAM;MAAE6K,MAAM;MAAEC;IAAW,CAAE;IAAA;IAAA,CAAAlP,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC2T,oBAAoB,CAACvP,IAAI,EAAEO,UAAU,EAAE;MAC1Ec,UAAU;MAAEE,YAAY;MAAEJ,YAAY;MAAEM,UAAU;MAClDoG,aAAa;MAAE0B,kBAAkB;MAAEpD,gBAAgB;MAAEuD;KACtD,CAAC;IAEF,MAAM8F,cAAc;IAAA;IAAA,CAAA1U,cAAA,GAAAc,CAAA,SAAGyB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;IAAA;IAAAtC,cAAA,GAAAc,CAAA;IAE7C,OAAO;MACLgF,OAAO,EAAE8B,IAAI,CAAC4B,GAAG,CAAC5B,IAAI,CAACwH,GAAG,CAACtJ,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAAE;MAC5CuJ,UAAU,EAAE;QACV9I,UAAU;QACVE,YAAY;QACZJ,YAAY;QACZM,UAAU;QACVoG,aAAa;QACb0B,kBAAkB;QAClBpD,gBAAgB;QAChBuD;OACD;MACDG,UAAU;MACVO,eAAe,EAAEoF,cAAc;MAC/BnF,iBAAiB,EAAE,OAAO;MAC1BN,MAAM;MACNC,WAAW;MACXM,SAAS,EAAEjN,IAAI,CAACC,GAAG;KACpB;EACH;EAEA;;;;;;;EAOQgE,wBAAwBA,CAACtB,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IACxG,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAM6T,iBAAiB;IAAA;IAAA,CAAA3U,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAACjK,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACjB,QAAQ,CAACuD,QAAQ;IAAR,CAAQ,CAAC,CAAC,CAAC0D,IAAI;IAChF,MAAMgF,aAAa;IAAA;IAAA,CAAA5U,cAAA,GAAAc,CAAA,SAAG8G,IAAI,CAAC4B,GAAG,CAAC/D,UAAU,CAACd,MAAM,EAAE,CAAC,CAAC;IACpD,MAAMgL,cAAc;IAAA;IAAA,CAAA3P,cAAA,GAAAc,CAAA,SAAG6T,iBAAiB,GAAGC,aAAa;IAAA;IAAA5U,cAAA,GAAAc,CAAA;IACxDuI,KAAK,IAAIsG,cAAc,GAAG,GAAG;IAE7B;IAAA;IAAA3P,cAAA,GAAAc,CAAA;IACA,IAAI2E,UAAU,CAACd,MAAM,IAAI,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAC1B,MAAM0S,iBAAiB;MAAA;MAAA,CAAA7U,cAAA,GAAAc,CAAA,SAAG,EAAE;MAAA;MAAAd,cAAA,GAAAc,CAAA;MAC5B,KAAK,IAAI6Q,CAAC;MAAA;MAAA,CAAA3R,cAAA,GAAAc,CAAA,SAAG,CAAC,GAAE6Q,CAAC,GAAGlM,UAAU,CAACd,MAAM,GAAG,CAAC,EAAEgN,CAAC,EAAE,EAAE;QAC9C,MAAMmD,IAAI;QAAA;QAAA,CAAA9U,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACiU,yBAAyB,CACzCtP,UAAU,CAACkM,CAAC,CAAC,CAAChJ,QAAQ,CAAC4D,eAAe,EACtC9G,UAAU,CAACkM,CAAC,GAAG,CAAC,CAAC,CAAChJ,QAAQ,CAAC4D,eAAe,CAC3C;QAAA;QAAAvM,cAAA,GAAAc,CAAA;QACD+T,iBAAiB,CAAC1P,IAAI,CAAC2P,IAAI,CAAC;MAC9B;MACA,MAAME,WAAW;MAAA;MAAA,CAAAhV,cAAA,GAAAc,CAAA,SAAG+T,iBAAiB,CAAClE,MAAM,CAAC,CAACC,GAAG,EAAEqE,CAAC,KAAK;QAAA;QAAAjV,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAc,CAAA;QAAA,OAAA8P,GAAG,GAAGqE,CAAC;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGJ,iBAAiB,CAAClQ,MAAM;MAAA;MAAA3E,cAAA,GAAAc,CAAA;MAC/FuI,KAAK,IAAIzB,IAAI,CAAC4B,GAAG,CAACwL,WAAW,EAAE,CAAC,CAAC,GAAG,GAAG;IACzC,CAAC,MAAM;MAAA;MAAAhV,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACLuI,KAAK,IAAI,IAAI,EAAC;IAChB;IAEA;IACA,MAAM6L,gBAAgB;IAAA;IAAA,CAAAlV,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAACjK,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACjB,QAAQ,CAACY,gBAAgB;IAAhB,CAAgB,CAAC,CAAC;IAAA;IAAAvJ,cAAA,GAAAc,CAAA;IAClF,IAAIoU,gBAAgB,CAACtF,IAAI,GAAG,CAAC,EAAE;MAAA;MAAA5P,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC7BuI,KAAK,IAAI,GAAG;IACd,CAAC,MAAM;MAAA;MAAArJ,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACLuI,KAAK,IAAI,GAAG;IACd;IAEA;IACA,MAAM4G,WAAW;IAAA;IAAA,CAAAjQ,cAAA,GAAAc,CAAA;IAAG;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAA+C,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAI+C,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAG,CAAC;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,WAAG,GAAG;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAClEuI,KAAK,IAAI4G,WAAW,GAAG,GAAG;IAAA;IAAAjQ,cAAA,GAAAc,CAAA;IAE1B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQ3C,0BAA0BA,CAACxB,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC1G,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAM6D,MAAM;IAAA;IAAA,CAAA3E,cAAA,GAAAc,CAAA,SAAGoE,IAAI,CAACP,MAAM;IAC1B,IAAIsL,WAAW;IAAA;IAAA,CAAAjQ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACnB;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAwC,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAIwC,MAAM,IAAI,CAAC,GAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC9BmP,WAAW,GAAG,GAAG,EAAC;IACpB,CAAC,MAAM;MAAA;MAAAjQ,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAwC,MAAM,IAAI,CAAC;MAAA;MAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAIwC,MAAM,IAAI,CAAC,GAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrCmP,WAAW,GAAG,GAAG;MACnB,CAAC,MAAM;QAAA;QAAAjQ,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAAA;QAAI;QAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAwC,MAAM,IAAI,CAAC;QAAA;QAAA,CAAA3E,cAAA,GAAAmC,CAAA,WAAIwC,MAAM,IAAI,CAAC,GAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACrCmP,WAAW,GAAG,GAAG;QACnB,CAAC,MAAM;UAAA;UAAAjQ,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACLmP,WAAW,GAAG,GAAG;QACnB;MAAA;IAAA;IAAC;IAAAjQ,cAAA,GAAAc,CAAA;IACDuI,KAAK,IAAI4G,WAAW,GAAG,IAAI;IAE3B;IACA,MAAMG,aAAa;IAAA;IAAA,CAAApQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACqU,6BAA6B,CAACjQ,IAAI,EAAEO,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAC1EuI,KAAK,IAAI+G,aAAa,GAAG,IAAI;IAE7B;IACA,MAAME,aAAa;IAAA;IAAA,CAAAtQ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACsU,6BAA6B,CAAC3P,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACpEuI,KAAK,IAAIiH,aAAa,GAAG,IAAI;IAE7B;IACA,MAAM+E,cAAc;IAAA;IAAA,CAAArV,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwU,+BAA+B,CAACpQ,IAAI,EAAEO,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAC7EuI,KAAK,IAAIgM,cAAc,GAAG,IAAI;IAAA;IAAArV,cAAA,GAAAc,CAAA;IAE9B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQ/C,yBAAyBA,CAACpB,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IACzG,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMyU,aAAa;IAAA;IAAA,CAAAvV,cAAA,GAAAc,CAAA;IAAG;IAAA,CAAAd,cAAA,GAAAmC,CAAA,WAAAiC,OAAO,CAAC+E,mBAAmB;IAAA;IAAA,CAAAnJ,cAAA,GAAAmC,CAAA,WAAI,SAAS;IAC9D,MAAMqT,kBAAkB;IAAA;IAAA,CAAAxV,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkE,MAAM,CAACC,CAAC,IAC5C;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,kCAAAd,cAAA,GAAAmC,CAAA,WAAAyH,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,KAAKgM,aAAa;MAAA;MAAA,CAAAvV,cAAA,GAAAmC,CAAA,WAC7CyH,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,KAAK,SAAS;IAAT,CAAS,CAC1C;IACD,MAAMkM,gBAAgB;IAAA;IAAA,CAAAzV,cAAA,GAAAc,CAAA,SAAG0U,kBAAkB,CAAC7Q,MAAM,GAAGc,UAAU,CAACd,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACtEuI,KAAK,IAAIoM,gBAAgB,GAAG,GAAG;IAE/B;IACA,MAAMP,gBAAgB;IAAA;IAAA,CAAAlV,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IACvC;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,cAAO8I,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,KAAK,QAAQ;MAAA;MAAA,CAAAvJ,cAAA,GAAAmC,CAAA,WAC3CyH,CAAC,CAACjB,QAAQ,CAACY,gBAAgB;MAAA;MAAA,CAAAvJ,cAAA,GAAAmC,CAAA,WAC3B,GAAGyH,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,CAACoE,cAAc,IAAI/D,CAAC,CAACjB,QAAQ,CAACY,gBAAgB,CAAC0E,SAAS,EAAE;IAAF,CAAE,CAC7F;IACD,MAAM4C,YAAY;IAAA;IAAA,CAAA7Q,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC4U,wBAAwB,CAACR,gBAAgB,CAAC;IAAA;IAAAlV,cAAA,GAAAc,CAAA;IACpEuI,KAAK,IAAIwH,YAAY,GAAG,GAAG;IAE3B;IACA,MAAME,cAAc;IAAA;IAAA,CAAA/Q,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC6U,uBAAuB,CAAClQ,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAC/DuI,KAAK,IAAI0H,cAAc,GAAG,GAAG;IAAA;IAAA/Q,cAAA,GAAAc,CAAA;IAE7B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQzC,wBAAwBA,CAAC1B,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IACxG,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMmQ,YAAY;IAAA;IAAA,CAAAjR,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,QAAC,GAAG8I,CAAC,CAACjB,QAAQ,CAACW,eAAe;IAAf,CAAe,CAAC;IACxE,MAAM4H,SAAS;IAAA;IAAA,CAAAlR,cAAA,GAAAc,CAAA,SAAGmQ,YAAY,CAACN,MAAM,CAAC,CAACC,GAAG,EAAEO,CAAC,KAAK;MAAA;MAAAnR,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGO,CAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGF,YAAY,CAACtM,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACnFuI,KAAK,IAAI6H,SAAS,GAAG,GAAG;IAExB;IACA,MAAMI,gBAAgB;IAAA;IAAA,CAAAtR,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC8U,8BAA8B,CAACnQ,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACxEuI,KAAK,IAAIiI,gBAAgB,GAAG,IAAI;IAEhC;IACA,MAAMuE,gBAAgB;IAAA;IAAA,CAAA7V,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACgV,yBAAyB,CAAC5Q,IAAI,CAACP,MAAM,CAAC;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACpEuI,KAAK,IAAIwM,gBAAgB,GAAG,IAAI;IAEhC;IACA,MAAME,cAAc;IAAA;IAAA,CAAA/V,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACkV,4BAA4B,CAAC9Q,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAC9DuI,KAAK,IAAI0M,cAAc,GAAG,GAAG;IAAA;IAAA/V,cAAA,GAAAc,CAAA;IAE7B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQ+K,2BAA2BA,CAAClP,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC3G,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMmV,YAAY;IAAA;IAAA,CAAAjW,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACoV,qBAAqB,CAACzQ,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAC3DuI,KAAK,IAAI4M,YAAY,GAAG,GAAG;IAE3B;IACA,MAAME,WAAW;IAAA;IAAA,CAAAnW,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACsV,oBAAoB,CAAC3Q,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACzDuI,KAAK,IAAI8M,WAAW,GAAG,GAAG;IAE1B;IACA,MAAME,uBAAuB;IAAA;IAAA,CAAArW,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwV,gCAAgC,CAACpR,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAC3EuI,KAAK,IAAI,CAAC,CAAC,GAAGgN,uBAAuB,IAAI,GAAG;IAE5C;IACA,MAAME,yBAAyB;IAAA;IAAA,CAAAvW,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC0V,mCAAmC,CAACtR,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAChFuI,KAAK,IAAIkN,yBAAyB,GAAG,GAAG;IAAA;IAAAvW,cAAA,GAAAc,CAAA;IAExC,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQgL,+BAA+BA,CAACnP,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC/G,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACA,IAAI2E,UAAU,CAACd,MAAM,IAAI,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAC1B,MAAMsU,mBAAmB;MAAA;MAAA,CAAAzW,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC4V,4BAA4B,CAACjR,UAAU,CAAC;MAAA;MAAAzF,cAAA,GAAAc,CAAA;MACzEuI,KAAK,IAAIoN,mBAAmB,GAAG,GAAG;IACpC,CAAC,MAAM;MAAA;MAAAzW,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACLuI,KAAK,IAAI,IAAI,EAAC;IAChB;IAEA;IACA,MAAMsN,qBAAqB;IAAA;IAAA,CAAA3W,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC8V,8BAA8B,CAACnR,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IAC7EuI,KAAK,IAAIsN,qBAAqB,GAAG,GAAG;IAEpC;IACA,MAAME,yBAAyB;IAAA;IAAA,CAAA7W,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACgW,kCAAkC,CAACrR,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACrFuI,KAAK,IAAIwN,yBAAyB,GAAG,GAAG;IAAA;IAAA7W,cAAA,GAAAc,CAAA;IAExC,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQiL,6BAA6BA,CAACpP,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC7G,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMiW,gBAAgB;IAAA;IAAA,CAAA/W,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACkW,yBAAyB,CAAC9R,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAC7DuI,KAAK,IAAI0N,gBAAgB,GAAG,IAAI;IAEhC;IACA,MAAME,kBAAkB;IAAA;IAAA,CAAAjX,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACoW,2BAA2B,CAACzR,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACvEuI,KAAK,IAAI4N,kBAAkB,GAAG,GAAG;IAEjC;IACA,MAAME,kBAAkB;IAAA;IAAA,CAAAnX,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACsW,2BAA2B,CAAC3R,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACvEuI,KAAK,IAAI8N,kBAAkB,GAAG,IAAI;IAElC;IACA,MAAME,cAAc;IAAA;IAAA,CAAArX,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACwW,uBAAuB,CAACpS,IAAI,EAAEO,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACrEuI,KAAK,IAAIgO,cAAc,GAAG,GAAG;IAAA;IAAArX,cAAA,GAAAc,CAAA;IAE7B,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;;;;;EAOQkL,gCAAgCA,CAACrP,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAChH,IAAImI,KAAK;IAAA;IAAA,CAAArJ,cAAA,GAAAc,CAAA,SAAG,CAAC;IAEb;IACA,MAAMyW,gBAAgB;IAAA;IAAA,CAAAvX,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC0W,yBAAyB,CAACtS,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAC7DuI,KAAK,IAAIkO,gBAAgB,GAAG,GAAG;IAE/B;IACA,MAAME,qBAAqB;IAAA;IAAA,CAAAzX,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC4W,8BAA8B,CAACxS,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IACvEuI,KAAK,IAAIoO,qBAAqB,GAAG,IAAI;IAErC;IACA,MAAME,kBAAkB;IAAA;IAAA,CAAA3X,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC8W,2BAA2B,CAAC1S,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IACjEuI,KAAK,IAAIsO,kBAAkB,GAAG,IAAI;IAElC;IACA,MAAME,iBAAiB;IAAA;IAAA,CAAA7X,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACgX,0BAA0B,CAAC5S,IAAI,CAAC;IAAA;IAAAlF,cAAA,GAAAc,CAAA;IAC/DuI,KAAK,IAAIwO,iBAAiB,GAAG,GAAG;IAAA;IAAA7X,cAAA,GAAAc,CAAA;IAEhC,OAAO8G,IAAI,CAAC4B,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;EAC3B;EAEA;;;EAGQlD,mBAAmBA,CAACjB,IAAY,EAAEO,UAA+B,EAAEF,OAAyB;IAAA;IAAAvF,cAAA,GAAAkB,CAAA;IAClG,MAAMiR,aAAa;IAAA;IAAA,CAAAnS,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACjB,QAAQ,CAACzD,IAAI;IAAJ,CAAI,CAAC,CAAC3B,IAAI,CAAC,GAAG,CAAC;IAAA;IAAAvD,cAAA,GAAAc,CAAA;IACpE,OAAO,KAAKyE,OAAO,CAACS,IAAI,WAAWmM,aAAa;IAAQ;IAAA,CAAAnS,cAAA,GAAAmC,CAAA,WAAAsD,UAAU,CAAC,CAAC,CAAC,EAAEkD,QAAQ,CAAC8D,IAAI,CAAC,CAAC,CAAC;IAAA;IAAA,CAAAzM,cAAA,GAAAmC,CAAA,WAAI,IAAI,WAAU+C,IAAI,GAAG;EAClH;EAEA;EACA;EACA;EAEA;;;;;EAKQ6P,yBAAyBA,CAACgD,OAAiB,EAAEC,OAAiB;IAAA;IAAAhY,cAAA,GAAAkB,CAAA;IACpE;IACA,IAAI0P,GAAG;IAAA;IAAA,CAAA5Q,cAAA,GAAAc,CAAA,SAAG,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACX,KAAK,IAAI6Q,CAAC;IAAA;IAAA,CAAA3R,cAAA,GAAAc,CAAA,SAAG,CAAC,GAAE6Q,CAAC,GAAG/J,IAAI,CAAC4B,GAAG,CAACuO,OAAO,CAACpT,MAAM,EAAEqT,OAAO,CAACrT,MAAM,CAAC,EAAEgN,CAAC,EAAE,EAAE;MAAA;MAAA3R,cAAA,GAAAc,CAAA;MACjE8P,GAAG,IAAIhJ,IAAI,CAACsL,GAAG,CAAC6E,OAAO,CAACpG,CAAC,CAAC,GAAGqG,OAAO,CAACrG,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C;IAAC;IAAA3R,cAAA,GAAAc,CAAA;IACD,OAAO8G,IAAI,CAACqQ,IAAI,CAACrH,GAAG,CAAC,GAAGhJ,IAAI,CAACqQ,IAAI,CAACF,OAAO,CAACpT,MAAM,CAAC,EAAC;EACpD;EAEA;;;;;EAKQwQ,6BAA6BA,CAACjQ,IAAY,EAAEO,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACjF;IACA,MAAMgX,aAAa;IAAA;IAAA,CAAAlY,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkL,MAAM,CAAC,CAACC,GAAG,EAAEhH,CAAC,KAAK;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG;MAAI;MAAA,CAAA5Q,cAAA,GAAAmC,CAAA,WAAAyH,CAAC,CAACjB,QAAQ,CAAC+D,mBAAmB,EAAEC,cAAc;MAAA;MAAA,CAAA3M,cAAA,GAAAmC,CAAA,WAAI,CAAC,EAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IAEnH;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAA+V,aAAa,IAAI,CAAC;IAAA;IAAA,CAAAlY,cAAA,GAAAmC,CAAA,YAAI+V,aAAa,IAAI,CAAC,GAAE;MAAA;MAAAlY,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC5C,OAAO,GAAG;IACZ,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAIoX,aAAa,IAAI,CAAC,EAAE;QAAA;QAAAlY,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC7B,OAAO,GAAG;MACZ,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO,GAAG;MACZ;IAAA;EACF;EAEA;;;;;EAKQsU,6BAA6BA,CAAC3P,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACnE;IACA,MAAMiX,kBAAkB;IAAA;IAAA,CAAAnY,cAAA,GAAAc,CAAA,SAAG,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;IAChE,MAAMsX,aAAa;IAAA;IAAA,CAAApY,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkE,MAAM,CAACC,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAAqX,kBAAkB,CAAC5P,QAAQ,CAACqB,CAAC,CAACjB,QAAQ,CAACuD,QAAQ,CAAC;IAAD,CAAC,CAAC,CAACvH,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACrG,OAAOsX,aAAa,GAAG3S,UAAU,CAACd,MAAM;EAC1C;EAEA;;;;;EAKQ2Q,+BAA+BA,CAACpQ,IAAY,EAAEO,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACnF;IACA,IAAI2E,UAAU,CAACd,MAAM,KAAK,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC3B,OAAO,GAAG,EAAC;IACb,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAI2E,UAAU,CAACd,MAAM,KAAK,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAClC,OAAO,GAAG;MACZ,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO,GAAG;MACZ;IAAA;EACF;EAEA;;;;;EAKQ4U,wBAAwBA,CAAC2C,QAAkB;IAAA;IAAArY,cAAA,GAAAkB,CAAA;IACjD,MAAMoX,cAAc;IAAA;IAAA,CAAAtY,cAAA,GAAAc,CAAA,SAAG,IAAI4O,GAAG,CAAC2I,QAAQ,CAAC;IAExC;IAAA;IAAArY,cAAA,GAAAc,CAAA;IACA,IAAIwX,cAAc,CAAC1I,IAAI,KAAK,CAAC,EAAE;MAAA;MAAA5P,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC7B,OAAO,GAAG;IACZ,CAAC;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAED;IAAAnC,cAAA,GAAAc,CAAA;IACA,IAAIwX,cAAc,CAACpE,GAAG,CAAC,SAAS,CAAC,EAAE;MAAA;MAAAlU,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACjC,OAAO,GAAG;IACZ,CAAC;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAED;IAAAnC,cAAA,GAAAc,CAAA;IACA,OAAO,GAAG;EACZ;EAEA;;;;;EAKQ6U,uBAAuBA,CAAClQ,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAC7D,MAAMqX,WAAW;IAAA;IAAA,CAAAvY,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkE,MAAM,CAACC,CAAC,IAAG;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MACxC,MAAMkD,OAAO;MAAA;MAAA,CAAApE,cAAA,GAAAc,CAAA,SAAG8I,CAAC,CAACjB,QAAQ,CAACY,gBAAgB;MAAA;MAAAvJ,cAAA,GAAAc,CAAA;MAC3C,IAAI,OAAOsD,OAAO,KAAK,QAAQ,EAAE;QAAA;QAAApE,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC/B,OAAO,2BAAAd,cAAA,GAAAmC,CAAA,YAAAiC,OAAO,KAAK,QAAQ;QAAA;QAAA,CAAApE,cAAA,GAAAmC,CAAA,YAAIiC,OAAO,KAAK,SAAS;MACtD,CAAC,MAAM;QAAA;QAAApE,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL;QACA,OAAO,2BAAAd,cAAA,GAAAmC,CAAA,YAAAiC,OAAO,CAACwJ,SAAS,IAAI,GAAG;QAAA;QAAA,CAAA5N,cAAA,GAAAmC,CAAA,YAAIiC,OAAO,CAACuJ,cAAc,IAAI,GAAG;MAClE;IACF,CAAC,CAAC,CAAChJ,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACT,OAAOyX,WAAW,GAAG9S,UAAU,CAACd,MAAM;EACxC;EAEA;;;;;EAKQiR,8BAA8BA,CAACnQ,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACpE;IACA,MAAMsX,UAAU;IAAA;IAAA,CAAAxY,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACjB,QAAQ,CAACuD,QAAQ;IAAR,CAAQ,CAAC,CAACrB,IAAI,EAAE;IAClE,MAAM4N,WAAW;IAAA;IAAA,CAAAzY,cAAA,GAAAc,CAAA,SAAG0X,UAAU,CAACjV,IAAI,CAAC,GAAG,CAAC;IAExC;IACA,MAAMmV,kBAAkB;IAAA;IAAA,CAAA1Y,cAAA,GAAAc,CAAA,SAAG,CAAC,sBAAsB,EAAE,6BAA6B,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IAClF,IAAI4X,kBAAkB,CAACnQ,QAAQ,CAACkQ,WAAW,CAAC,EAAE;MAAA;MAAAzY,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC5C,OAAO,GAAG;IACZ,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACL,OAAO,GAAG;IACZ;EACF;EAEA;;;;;EAKQgV,yBAAyBA,CAACnR,MAAc;IAAA;IAAA3E,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC9C;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAAwC,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAIwC,MAAM,IAAI,CAAC,GAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC9B,OAAO,GAAG,EAAC;IACb,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA;MAAI;MAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAAwC,MAAM,IAAI,CAAC;MAAA;MAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAIwC,MAAM,IAAI,CAAC,GAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACrC,OAAO,GAAG,EAAC;MACb,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAAA;QAAI;QAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAAwC,MAAM,IAAI,CAAC;QAAA;QAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAIwC,MAAM,IAAI,CAAC,GAAE;UAAA;UAAA3E,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACrC,OAAO,GAAG,EAAC;QACb,CAAC,MAAM;UAAA;UAAAd,cAAA,GAAAmC,CAAA;UAAAnC,cAAA,GAAAc,CAAA;UACL,OAAO,GAAG,EAAC;QACb;MAAA;IAAA;EACF;EAEA;;;;;EAKQkV,4BAA4BA,CAAC9Q,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAC/C;IACA,MAAMyX,WAAW;IAAA;IAAA,CAAA3Y,cAAA,GAAAc,CAAA,SAAG,sfAAsf;IAE1gB,IAAI8X,WAAW;IAAA;IAAA,CAAA5Y,cAAA,GAAAc,CAAA,SAAG,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACnB,KAAK,MAAM+X,IAAI,IAAI3T,IAAI,EAAE;MAAA;MAAAlF,cAAA,GAAAc,CAAA;MACvB,IAAI,CAAC6X,WAAW,CAACpQ,QAAQ,CAACsQ,IAAI,CAAC,EAAE;QAAA;QAAA7Y,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC/B8X,WAAW,IAAI,GAAG;MACpB,CAAC;MAAA;MAAA;QAAA5Y,cAAA,GAAAmC,CAAA;MAAA;IACH;IAAC;IAAAnC,cAAA,GAAAc,CAAA;IAED,OAAO8G,IAAI,CAAC4B,GAAG,CAACoP,WAAW,GAAG1T,IAAI,CAACP,MAAM,EAAE,CAAC,CAAC;EAC/C;EAEA;;;;;EAKQuR,qBAAqBA,CAACzQ,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAC3D;IACA,MAAM4X,cAAc;IAAA;IAAA,CAAA9Y,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkL,MAAM,CAAC,CAACC,GAAG,EAAEhH,CAAC,KAC9C;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG;MAAI;MAAA,CAAA5Q,cAAA,GAAAmC,CAAA,YAAAyH,CAAC,CAACjB,QAAQ,CAAC+D,mBAAmB,EAAEC,cAAc;MAAA;MAAA,CAAA3M,cAAA,GAAAmC,CAAA,YAAI,CAAC,EAAC;IAAD,CAAC,EAAE,CAAC,CAAC;IAEjE;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAA2W,cAAc,IAAI,CAAC;IAAA;IAAA,CAAA9Y,cAAA,GAAAmC,CAAA,YAAI2W,cAAc,IAAI,CAAC,GAAE;MAAA;MAAA9Y,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC9C,OAAO,GAAG;IACZ,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAIgY,cAAc,IAAI,CAAC,EAAE;QAAA;QAAA9Y,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QAC9B,OAAO,GAAG;MACZ,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO,GAAG;MACZ;IAAA;EACF;EAEA;;;;;EAKQsV,oBAAoBA,CAAC3Q,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC1D;IACA,OAAO,GAAG;EACZ;EAEA;;;;;EAKQwV,gCAAgCA,CAACpR,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IACnD;IACA,MAAMyD,MAAM;IAAA;IAAA,CAAA3E,cAAA,GAAAc,CAAA,SAAGoE,IAAI,CAACP,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IAC1B,IAAI6D,MAAM,IAAI,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACf,OAAO,GAAG,EAAC;IACb,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAI6D,MAAM,IAAI,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACtB,OAAO,GAAG,EAAC;MACb,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO,GAAG,EAAC;MACb;IAAA;EACF;EAEA;;;;;EAKQ0V,mCAAmCA,CAACtR,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACtD;IACA,OAAOoE,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAG,GAAG;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,GAAG;EACrC;EAEA;;;;;EAKQuU,4BAA4BA,CAACjR,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAClE,IAAI2E,UAAU,CAACd,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,OAAO,GAAG;IAAA;IAAA;IAAA;MAAAd,cAAA,GAAAmC,CAAA;IAAA;IAErC;IACA,IAAI4W,eAAe;IAAA;IAAA,CAAA/Y,cAAA,GAAAc,CAAA,SAAG,CAAC;IACvB,IAAI4Q,SAAS;IAAA;IAAA,CAAA1R,cAAA,GAAAc,CAAA,SAAG,CAAC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IAEjB,KAAK,IAAI6Q,CAAC;IAAA;IAAA,CAAA3R,cAAA,GAAAc,CAAA,SAAG,CAAC,GAAE6Q,CAAC,GAAGlM,UAAU,CAACd,MAAM,GAAG,CAAC,EAAEgN,CAAC,EAAE,EAAE;MAAA;MAAA3R,cAAA,GAAAc,CAAA;MAC9C,KAAK,IAAI8Q,CAAC;MAAA;MAAA,CAAA5R,cAAA,GAAAc,CAAA,SAAG6Q,CAAC,GAAG,CAAC,GAAEC,CAAC,GAAGnM,UAAU,CAACd,MAAM,EAAEiN,CAAC,EAAE,EAAE;QAC9C,MAAMoH,UAAU;QAAA;QAAA,CAAAhZ,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACJ,YAAY,CAACuY,2BAA2B,CAC9DxT,UAAU,CAACkM,CAAC,CAAC,CAAChJ,QAAQ,CAAC4D,eAAe,EACtC9G,UAAU,CAACmM,CAAC,CAAC,CAACjJ,QAAQ,CAAC4D,eAAe,CACvC;QAAA;QAAAvM,cAAA,GAAAc,CAAA;QACDiY,eAAe,IAAIC,UAAU;QAAA;QAAAhZ,cAAA,GAAAc,CAAA;QAC7B4Q,SAAS,EAAE;MACb;IACF;IAAC;IAAA1R,cAAA,GAAAc,CAAA;IAED,OAAO4Q,SAAS,GAAG,CAAC;IAAA;IAAA,CAAA1R,cAAA,GAAAmC,CAAA,YAAG4W,eAAe,GAAGrH,SAAS;IAAA;IAAA,CAAA1R,cAAA,GAAAmC,CAAA,YAAG,GAAG;EAC1D;EAEA;;;;;EAKQyU,8BAA8BA,CAACnR,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACpE;IACA,MAAMsX,UAAU;IAAA;IAAA,CAAAxY,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8I,CAAC,CAACjB,QAAQ,CAACuD,QAAQ;IAAR,CAAQ,CAAC;IAC3D,MAAMgN,kBAAkB;IAAA;IAAA,CAAAlZ,cAAA,GAAAc,CAAA,SAAG,CAAC,UAAU,EAAE,UAAU,EAAE,iBAAiB,CAAC;IACtE,MAAMqX,kBAAkB;IAAA;IAAA,CAAAnY,cAAA,GAAAc,CAAA,SAAG,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;IAEhE,MAAMqY,aAAa;IAAA;IAAA,CAAAnZ,cAAA,GAAAc,CAAA,SAAG0X,UAAU,CAAC7O,MAAM,CAACC,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAAoY,kBAAkB,CAAC3Q,QAAQ,CAACqB,CAAC,CAAC;IAAD,CAAC,CAAC,CAACjF,MAAM;IACnF,MAAMyT,aAAa;IAAA;IAAA,CAAApY,cAAA,GAAAc,CAAA,SAAG0X,UAAU,CAAC7O,MAAM,CAACC,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAAqX,kBAAkB,CAAC5P,QAAQ,CAACqB,CAAC,CAAC;IAAD,CAAC,CAAC,CAACjF,MAAM;IAEnF;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAAgX,aAAa,KAAK1T,UAAU,CAACd,MAAM;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAIiW,aAAa,KAAK3S,UAAU,CAACd,MAAM,GAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC9E,OAAO,GAAG;IACZ,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACL,OAAO,GAAG,EAAC;IACb;EACF;EAEA;;;;;EAKQgW,kCAAkCA,CAACrR,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACxE;IACA,MAAMkY,UAAU;IAAA;IAAA,CAAApZ,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACrC,GAAG,CAACwG,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,kCAAAd,cAAA,GAAAmC,CAAA,YAAAyH,CAAC,CAACjB,QAAQ,CAAC+D,mBAAmB,EAAEL,kBAAkB;MAAA;MAAA,CAAArM,cAAA,GAAAmC,CAAA,YAAI,IAAI;IAAJ,CAAI,CAAC;IAElG;IACA,MAAMkX,sBAAsB;IAAA;IAAA,CAAArZ,cAAA,GAAAc,CAAA,SAAG,CAC7B,CAAC,KAAK,EAAE,IAAI,CAAC,EACb,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,IAAI,EAAE,IAAI,CAAC,CACb;IAAA;IAAAd,cAAA,GAAAc,CAAA;IAED,IAAIsY,UAAU,CAACzU,MAAM,KAAK,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAC3B,MAAMsW,WAAW;MAAA;MAAA,CAAAzY,cAAA,GAAAc,CAAA,SAAG,CAACsY,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;MAClD,MAAME,YAAY;MAAA;MAAA,CAAAtZ,cAAA,GAAAc,CAAA,SAAGuY,sBAAsB,CAACrU,IAAI,CAACuU,EAAE,IACjD;QAAA;QAAAvZ,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAc,CAAA;QAAA,kCAAAd,cAAA,GAAAmC,CAAA,YAAAoX,EAAE,CAAC,CAAC,CAAC,KAAKd,WAAW,CAAC,CAAC,CAAC;QAAA;QAAA,CAAAzY,cAAA,GAAAmC,CAAA,YAAIoX,EAAE,CAAC,CAAC,CAAC,KAAKd,WAAW,CAAC,CAAC,CAAC;MAAD,CAAC,CACrD;MAAA;MAAAzY,cAAA,GAAAc,CAAA;MACD,OAAOwY,YAAY;MAAA;MAAA,CAAAtZ,cAAA,GAAAmC,CAAA,YAAG,GAAG;MAAA;MAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,GAAG;IACjC,CAAC;IAAA;IAAA;MAAAnC,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,OAAO,GAAG,EAAC;EACb;EAEA;;;;;EAKQkW,yBAAyBA,CAAC9R,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAC5C;IACA,MAAMyD,MAAM;IAAA;IAAA,CAAA3E,cAAA,GAAAc,CAAA,SAAGoE,IAAI,CAACP,MAAM;IAE1B;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAAwC,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAIwC,MAAM,IAAI,CAAC,GAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC9B,OAAO,GAAG;IACZ,CAAC,MAAM;MAAA;MAAAd,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAAA,IAAI6D,MAAM,IAAI,CAAC,EAAE;QAAA;QAAA3E,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACtB,OAAO,GAAG;MACZ,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAmC,CAAA;QAAAnC,cAAA,GAAAc,CAAA;QACL,OAAO,GAAG;MACZ;IAAA;EACF;EAEA;;;;;EAKQoW,2BAA2BA,CAACzR,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACjE;IACA,OAAO,IAAI,CAACoV,qBAAqB,CAACzQ,UAAU,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC2Q,oBAAoB,CAAC3Q,UAAU,CAAC,GAAG,GAAG;EACnG;EAEA;;;;;EAKQ2R,2BAA2BA,CAAC3R,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IACjE;IACA,MAAMsY,gBAAgB;IAAA;IAAA,CAAAxZ,cAAA,GAAAc,CAAA,SAAG,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;IAC5D,MAAM2Y,WAAW;IAAA;IAAA,CAAAzZ,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkE,MAAM,CAACC,CAAC,IAAI;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA0Y,gBAAgB,CAACjR,QAAQ,CAACqB,CAAC,CAACjB,QAAQ,CAACuD,QAAQ,CAAC;IAAD,CAAC,CAAC,CAACvH,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACjG,OAAO2Y,WAAW,GAAGhU,UAAU,CAACd,MAAM;EACxC;EAEA;;;;;EAKQ2S,uBAAuBA,CAACpS,IAAY,EAAEO,UAA+B;IAAA;IAAAzF,cAAA,GAAAkB,CAAA;IAC3E;IACA,MAAMwY,aAAa;IAAA;IAAA,CAAA1Z,cAAA,GAAAc,CAAA;IAAG;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAA+C,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAI+C,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAG,CAAC;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,GAAG;IACpE,MAAMwX,eAAe;IAAA;IAAA,CAAA3Z,cAAA,GAAAc,CAAA,SAAG,IAAI,CAAC8V,8BAA8B,CAACnR,UAAU,CAAC;IAAA;IAAAzF,cAAA,GAAAc,CAAA;IACvE,OAAO,CAAC4Y,aAAa,GAAGC,eAAe,IAAI,CAAC;EAC9C;EAEA;;;;;EAKQnC,yBAAyBA,CAACtS,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC5C;IACA,OAAOoE,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAG,GAAG;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,GAAG;EACrC;EAEA;;;;;EAKQuV,8BAA8BA,CAACxS,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACjD;IACA,OAAO,GAAG;EACZ;EAEA;;;;;EAKQ8W,2BAA2BA,CAAC1S,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IAC9C;IACA,OAAOoE,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAG,GAAG;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,GAAG;EACrC;EAEA;;;;;EAKQ2V,0BAA0BA,CAAC5S,IAAY;IAAA;IAAAlF,cAAA,GAAAkB,CAAA;IAC7C;IACA,MAAM+O,WAAW;IAAA;IAAA,CAAAjQ,cAAA,GAAAc,CAAA,SAAGoE,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAG,CAAC;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAI+C,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAG,GAAG;IAAA;IAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,GAAG,EAAC;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IACzE,OAAOmP,WAAW;EACpB;EAEA;;;;;EAKQuE,6BAA6BA,CAACtP,IAAY,EAAEO,UAA+B,EAAErB,OAA0B;IAAA;IAAApE,cAAA,GAAAkB,CAAA;IAC7G,IAAI6N,UAAU;IAAA;IAAA,CAAA/O,cAAA,GAAAc,CAAA,SAAG,GAAG,GAAC;IAErB;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACA,IAAI2E,UAAU,CAACd,MAAM,IAAI,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAC1BiO,UAAU,IAAI,GAAG;IACnB,CAAC;IAAA;IAAA;MAAA/O,cAAA,GAAAmC,CAAA;IAAA;IAED;IACA,MAAM8I,UAAU;IAAA;IAAA,CAAAjL,cAAA,GAAAc,CAAA,SAAG2E,UAAU,CAACkL,MAAM,CAAC,CAACC,GAAG,EAAEhH,CAAC,KAAK;MAAA;MAAA5J,cAAA,GAAAkB,CAAA;MAAAlB,cAAA,GAAAc,CAAA;MAAA,OAAA8P,GAAG,GAAGhH,CAAC,CAACjB,QAAQ,CAAC1C,aAAa;IAAb,CAAa,EAAE,CAAC,CAAC,GAAGR,UAAU,CAACd,MAAM;IAAA;IAAA3E,cAAA,GAAAc,CAAA;IACvGiO,UAAU,IAAI,CAAC9D,UAAU,GAAG,GAAG,IAAI,GAAG;IAEtC;IAAA;IAAAjL,cAAA,GAAAc,CAAA;IACA;IAAI;IAAA,CAAAd,cAAA,GAAAmC,CAAA,YAAA+C,IAAI,CAACP,MAAM,IAAI,CAAC;IAAA;IAAA,CAAA3E,cAAA,GAAAmC,CAAA,YAAI+C,IAAI,CAACP,MAAM,IAAI,CAAC,GAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACxCiO,UAAU,IAAI,GAAG;IACnB,CAAC;IAAA;IAAA;MAAA/O,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,OAAO8G,IAAI,CAAC4B,GAAG,CAAC5B,IAAI,CAACwH,GAAG,CAACL,UAAU,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;EACjD;EAEA;;;;;EAKQ0F,oBAAoBA,CAACvP,IAAY,EAAEO,UAA+B,EAAE4J,UAAe;IAAA;IAAArP,cAAA,GAAAkB,CAAA;IACzF,MAAM+N,MAAM;IAAA;IAAA,CAAAjP,cAAA,GAAAc,CAAA,SAAa,EAAE;IAC3B,MAAMoO,WAAW;IAAA;IAAA,CAAAlP,cAAA,GAAAc,CAAA,SAAa,EAAE;IAEhC;IAAA;IAAAd,cAAA,GAAAc,CAAA;IACA,IAAIuO,UAAU,CAAC5I,YAAY,GAAG,GAAG,EAAE;MAAA;MAAAzG,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACjCmO,MAAM,CAAC9J,IAAI,CAAC,OAAO,CAAC;MAAA;MAAAnF,cAAA,GAAAc,CAAA;MACpBoO,WAAW,CAAC/J,IAAI,CAAC,gBAAgB,CAAC;IACpC,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIuO,UAAU,CAACtC,aAAa,GAAG,GAAG,EAAE;MAAA;MAAA/M,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MAClCmO,MAAM,CAAC9J,IAAI,CAAC,QAAQ,CAAC;MAAA;MAAAnF,cAAA,GAAAc,CAAA;MACrBoO,WAAW,CAAC/J,IAAI,CAAC,cAAc,CAAC;IAClC,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIuO,UAAU,CAAChJ,YAAY,GAAG,GAAG,EAAE;MAAA;MAAArG,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACjCmO,MAAM,CAAC9J,IAAI,CAAC,SAAS,CAAC;MAAA;MAAAnF,cAAA,GAAAc,CAAA;MACtBoO,WAAW,CAAC/J,IAAI,CAAC,gBAAgB,CAAC;IACpC,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIuO,UAAU,CAACZ,kBAAkB,GAAG,GAAG,EAAE;MAAA;MAAAzO,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACvCmO,MAAM,CAAC9J,IAAI,CAAC,SAAS,CAAC;MAAA;MAAAnF,cAAA,GAAAc,CAAA;MACtBoO,WAAW,CAAC/J,IAAI,CAAC,cAAc,CAAC;IAClC,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIoE,IAAI,CAACP,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACnBmO,MAAM,CAAC9J,IAAI,CAAC,OAAO,CAAC;MAAA;MAAAnF,cAAA,GAAAc,CAAA;MACpBoO,WAAW,CAAC/J,IAAI,CAAC,cAAc,CAAC;IAClC,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,IAAIoE,IAAI,CAACP,MAAM,GAAG,CAAC,EAAE;MAAA;MAAA3E,cAAA,GAAAmC,CAAA;MAAAnC,cAAA,GAAAc,CAAA;MACnBmO,MAAM,CAAC9J,IAAI,CAAC,OAAO,CAAC;MAAA;MAAAnF,cAAA,GAAAc,CAAA;MACpBoO,WAAW,CAAC/J,IAAI,CAAC,gBAAgB,CAAC;IACpC,CAAC;IAAA;IAAA;MAAAnF,cAAA,GAAAmC,CAAA;IAAA;IAAAnC,cAAA,GAAAc,CAAA;IAED,OAAO;MAAEmO,MAAM;MAAEC;IAAW,CAAE;EAChC;EAEA;;;;;EAKAtM,QAAQA,CAAA;IAAA;IAAA5C,cAAA,GAAAkB,CAAA;IACN,MAAM0Y,iBAAiB;IAAA;IAAA,CAAA5Z,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACC,eAAe,GAAG,CAAC;IAAA;IAAA,CAAAf,cAAA,GAAAmC,CAAA,YAC9C,IAAI,CAACnB,mBAAmB,GAAG,IAAI,CAACD,eAAe;IAAA;IAAA,CAAAf,cAAA,GAAAmC,CAAA,YAC/C,CAAC;IAEL,MAAM0X,aAAa;IAAA;IAAA,CAAA7Z,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACJ,YAAY,CAACkC,QAAQ,EAAE;IAAA;IAAA5C,cAAA,GAAAc,CAAA;IAElD,OAAO;MACLgZ,cAAc,EAAED,aAAa,CAAC7W,KAAK;MACnC+W,cAAc,EAAEF,aAAa;MAC7BG,aAAa,EAAE,IAAI,CAACnZ,aAAa;MAAA;MAAA,CAAAb,cAAA,GAAAmC,CAAA,YAAG,OAAO;MAAA;MAAA,CAAAnC,cAAA,GAAAmC,CAAA,YAAG,iBAAiB;MAC/D8X,iBAAiB,EAAE,IAAI,CAAClZ,eAAe;MACvCmZ,mBAAmB,EAAEN,iBAAiB;MACtCO,YAAY,EAAE;KACf;EACH;EAEA;;;;;EAKAC,oBAAoBA,CAAA;IAAA;IAAApa,cAAA,GAAAkB,CAAA;IAMlB,MAAM2B,kBAAkB;IAAA;IAAA,CAAA7C,cAAA,GAAAc,CAAA,SAAG,IAAI,CAACH,eAAe,CAACmC,qBAAqB,EAAE;IACvE,MAAMuX,aAAa;IAAA;IAAA,CAAAra,cAAA,GAAAc,CAAA,SAA0E;MAC3F,CAACN,YAAY,CAACmB,KAAK,GAAG;QACpBgC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAACwH,KAAK,GAAG;QACpBrE,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAACyH,KAAK,GAAG;QACpBtE,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAAC0H,KAAK,GAAG;QACpBvE,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAAC2H,KAAK,GAAG;QACpBxE,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAAC4H,KAAK,GAAG;QACpBzE,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAAC6H,KAAK,GAAG;QACpB1E,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;OACf;MACD,CAACpD,YAAY,CAAC8H,KAAK,GAAG;QACpB3E,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;;KAEjB;IAAA;IAAA5D,cAAA,GAAAc,CAAA;IAED,KAAK,MAAMmD,QAAQ,IAAIpB,kBAAkB,EAAE;MAAA;MAAA7C,cAAA,GAAAc,CAAA;MACzCuZ,aAAa,CAACpW,QAAQ,CAAC,GAAG,IAAI,CAACtD,eAAe,CAAC+C,gBAAgB,CAACO,QAAQ,CAAC;IAC3E;IAAC;IAAAjE,cAAA,GAAAc,CAAA;IAED,OAAO;MACL+B,kBAAkB;MAClBwX,aAAa;MACbC,aAAa,EAAErX,MAAM,CAAC6P,MAAM,CAACuH,aAAa,CAAC,CAAC1J,MAAM,CAAC,CAACC,GAAG,EAAEjO,KAAK,KAAK;QAAA;QAAA3C,cAAA,GAAAkB,CAAA;QAAAlB,cAAA,GAAAc,CAAA;QAAA,OAAA8P,GAAG,GAAGjO,KAAK,CAACiB,YAAY;MAAZ,CAAY,EAAE,CAAC,CAAC;MAC/F2W,mBAAmB,EAAE,IAAI,CAAC5Z,eAAe,CAAC8B,OAAO;KAClD;EACH;EAEA;;;;;EAKA+X,gBAAgBA,CAAA;IAAA;IAAAxa,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACd,OAAO,IAAI,CAACJ,YAAY,CAACkC,QAAQ,EAAE;EACrC;EAEA;;;;;EAKAH,OAAOA,CAAA;IAAA;IAAAzC,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACL,OAAO,2BAAAd,cAAA,GAAAmC,CAAA,gBAAI,CAACtB,aAAa;IAAA;IAAA,CAAAb,cAAA,GAAAmC,CAAA,YAClB,IAAI,CAACzB,YAAY,CAAC+B,OAAO,EAAE;IAAA;IAAA,CAAAzC,cAAA,GAAAmC,CAAA,YAC3B,IAAI,CAACxB,eAAe,CAAC8B,OAAO,EAAE;EACvC;EAEA;;;;;;EAMA8B,mBAAmBA,CAACN,QAAsB;IAAA;IAAAjE,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACxC,OAAO,IAAI,CAACH,eAAe,CAAC4D,mBAAmB,CAACN,QAAQ,CAAC;EAC3D;EAEA;;;;;EAKAnB,qBAAqBA,CAAA;IAAA;IAAA9C,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACnB,OAAO,IAAI,CAACH,eAAe,CAACmC,qBAAqB,EAAE;EACrD;EAEA;;;;;EAKA2X,OAAOA,CAAA;IAAA;IAAAza,cAAA,GAAAkB,CAAA;IAAAlB,cAAA,GAAAc,CAAA;IACL,IAAI,CAACJ,YAAY,CAAC+Z,OAAO,EAAE;IAC3B;IAAA;IAAAza,cAAA,GAAAc,CAAA;IACA,IAAI,CAACD,aAAa,GAAG,KAAK;IAAA;IAAAb,cAAA,GAAAc,CAAA;IAC1B,IAAI,CAACC,eAAe,GAAG,CAAC;IAAA;IAAAf,cAAA,GAAAc,CAAA;IACxB,IAAI,CAACE,mBAAmB,GAAG,CAAC;IAAA;IAAAhB,cAAA,GAAAc,CAAA;IAE5BsB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClC", "ignoreList": []}