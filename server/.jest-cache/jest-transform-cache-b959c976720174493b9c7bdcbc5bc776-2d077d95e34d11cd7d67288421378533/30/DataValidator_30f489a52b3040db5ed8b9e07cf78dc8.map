{"version": 3, "names": ["cov_bk4eiopux", "actualCoverage", "VALIDATION_RULES", "s", "id_pattern", "text_length", "min", "max", "subcategory_pattern", "tags_count", "semantic_vector_dimension", "quality_range", "frequency_range", "syllable_count", "character_count", "VALID_CATEGORIES", "Object", "values", "EMOTIONS", "PROFESSIONS", "CHARACTERISTICS", "OBJECTS", "ACTIONS", "CONCEPTS", "VALID_CONTEXTS", "ANCIENT", "MODERN", "NEUTRAL", "DataValidator", "config", "customRules", "constructor", "b", "f", "strict_mode", "skip_warnings", "custom_rules", "max_errors", "validate", "morphemes", "startTime", "Date", "now", "errors", "warnings", "passedCount", "console", "log", "length", "i", "morpheme", "basicErrors", "_validateBasicStructure", "typeErrors", "_validateDataTypes", "logicErrors", "_validateBusinessLogic", "consistencyErrors", "_validateConsistency", "customErrors", "_validateCustomRules", "allErrors", "morphemeErrors", "filter", "e", "type", "morphemeWarnings", "push", "warn", "error", "code", "message", "Error", "String", "morpheme_id", "id", "validationTime", "passed", "total_count", "passed_count", "failed_count", "validation_time", "timestamp", "index", "morphemeId", "requiredFields", "field", "undefined", "stringFields", "value", "expected", "actual", "numberFields", "semantic_vector", "Array", "isArray", "tags", "test", "toString", "text", "textLength", "category", "includes", "cultural_context", "quality_score", "usage_frequency", "expectedPrefix", "startsWith", "language_properties", "actualCharCount", "declaredCharCount", "rule", "enabled", "ruleErrors", "validator", "name", "addCustomRule", "removeCustomRule", "ruleName", "findIndex", "splice"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts"], "sourcesContent": ["/**\n * 数据验证器\n * \n * 提供语素数据的完整性验证、质量检查和一致性校验功能。\n * 支持多层次验证规则和自定义验证策略。\n * \n * @fileoverview 数据验证核心模块\n * @version 3.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\n\n// ============================================================================\n// 验证规则配置\n// ============================================================================\n\n/** 验证规则配置 */\nconst VALIDATION_RULES = {\n  /** ID格式正则表达式 */\n  id_pattern: /^[a-z_]+_\\d{3}$/,\n  /** 文本长度限制 */\n  text_length: { min: 1, max: 10 },\n  /** 子分类命名规则 */\n  subcategory_pattern: /^[a-z_]+$/,\n  /** 标签数量限制 */\n  tags_count: { min: 1, max: 10 },\n  /** 语义向量维度 */\n  semantic_vector_dimension: 20,\n  /** 质量评分范围 */\n  quality_range: { min: 0.0, max: 1.0 },\n  /** 使用频率范围 */\n  frequency_range: { min: 0.0, max: 1.0 },\n  /** 音节数量限制 */\n  syllable_count: { min: 1, max: 5 },\n  /** 字符数量限制 */\n  character_count: { min: 1, max: 10 }\n} as const\n\n/** 有效的语素类别 */\nconst VALID_CATEGORIES = Object.values({\n  EMOTIONS: 'emotions',\n  PROFESSIONS: 'professions',\n  CHARACTERISTICS: 'characteristics',\n  OBJECTS: 'objects',\n  ACTIONS: 'actions',\n  CONCEPTS: 'concepts'\n})\n\n/** 有效的文化语境 */\nconst VALID_CONTEXTS = Object.values({\n  ANCIENT: 'ancient',\n  MODERN: 'modern',\n  NEUTRAL: 'neutral'\n})\n\n// ============================================================================\n// 接口定义\n// ============================================================================\n\n/**\n * 验证错误接口\n */\nexport interface ValidationError {\n  /** 错误类型 */\n  type: 'error' | 'warning' | 'info'\n  /** 错误代码 */\n  code: string\n  /** 错误消息 */\n  message: string\n  /** 字段路径 */\n  field?: string\n  /** 期望值 */\n  expected?: any\n  /** 实际值 */\n  actual?: any\n  /** 语素ID */\n  morpheme_id?: string\n}\n\n/**\n * 验证结果接口\n */\nexport interface ValidationResult {\n  /** 验证是否通过 */\n  passed: boolean\n  /** 验证的语素数量 */\n  total_count: number\n  /** 通过验证的语素数量 */\n  passed_count: number\n  /** 失败的语素数量 */\n  failed_count: number\n  /** 错误列表 */\n  errors: ValidationError[]\n  /** 警告列表 */\n  warnings: ValidationError[]\n  /** 验证耗时 (毫秒) */\n  validation_time: number\n  /** 验证时间戳 */\n  timestamp: number\n}\n\n/**\n * 验证配置接口\n */\nexport interface ValidationConfig {\n  /** 是否启用严格模式 */\n  strict_mode?: boolean\n  /** 是否跳过警告 */\n  skip_warnings?: boolean\n  /** 自定义验证规则 */\n  custom_rules?: ValidationRule[]\n  /** 最大错误数量 (超过则停止验证) */\n  max_errors?: number\n}\n\n/**\n * 验证规则接口\n */\nexport interface ValidationRule {\n  /** 规则名称 */\n  name: string\n  /** 规则描述 */\n  description: string\n  /** 验证函数 */\n  validator: (morpheme: Morpheme) => ValidationError[]\n  /** 规则优先级 */\n  priority: number\n  /** 是否启用 */\n  enabled: boolean\n}\n\n// ============================================================================\n// 数据验证器类\n// ============================================================================\n\n/**\n * 数据验证器类\n * \n * 提供全面的语素数据验证功能\n */\nexport class DataValidator {\n  private config: Required<ValidationConfig>\n  private customRules: ValidationRule[]\n\n  /**\n   * 构造函数\n   * \n   * @param config 验证配置\n   */\n  constructor(config: ValidationConfig = {}) {\n    this.config = {\n      strict_mode: config.strict_mode ?? true,\n      skip_warnings: config.skip_warnings ?? false,\n      custom_rules: config.custom_rules ?? [],\n      max_errors: config.max_errors ?? 1000\n    }\n    \n    this.customRules = [...this.config.custom_rules]\n  }\n\n  /**\n   * 验证语素数据数组\n   * \n   * @param morphemes 语素数据数组\n   * @returns 验证结果\n   */\n  async validate(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: ValidationError[] = []\n    const warnings: ValidationError[] = []\n    let passedCount = 0\n\n    console.log(`🔍 开始验证 ${morphemes.length} 个语素...`)\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      \n      try {\n        // 基础验证\n        const basicErrors = this._validateBasicStructure(morpheme, i)\n        \n        // 数据类型验证\n        const typeErrors = this._validateDataTypes(morpheme, i)\n        \n        // 业务逻辑验证\n        const logicErrors = this._validateBusinessLogic(morpheme, i)\n        \n        // 一致性验证\n        const consistencyErrors = this._validateConsistency(morpheme, i)\n        \n        // 自定义规则验证\n        const customErrors = this._validateCustomRules(morpheme, i)\n        \n        // 收集所有错误\n        const allErrors = [\n          ...basicErrors,\n          ...typeErrors,\n          ...logicErrors,\n          ...consistencyErrors,\n          ...customErrors\n        ]\n        \n        // 分类错误和警告\n        const morphemeErrors = allErrors.filter(e => e.type === 'error')\n        const morphemeWarnings = allErrors.filter(e => e.type === 'warning')\n        \n        errors.push(...morphemeErrors)\n        warnings.push(...morphemeWarnings)\n        \n        // 如果没有错误，则通过验证\n        if (morphemeErrors.length === 0) {\n          passedCount++\n        }\n        \n        // 检查是否超过最大错误数量\n        if (errors.length >= this.config.max_errors) {\n          console.warn(`⚠️ 错误数量超过限制 (${this.config.max_errors})，停止验证`)\n          break\n        }\n        \n      } catch (error) {\n        errors.push({\n          type: 'error',\n          code: 'VALIDATION_EXCEPTION',\n          message: `验证过程中发生异常: ${error instanceof Error ? error.message : String(error)}`,\n          morpheme_id: morpheme.id || `index_${i}`\n        })\n      }\n    }\n\n    const validationTime = Date.now() - startTime\n    const passed = errors.length === 0\n\n    // 输出验证结果摘要\n    if (passed) {\n      console.log(`✅ 验证通过: ${passedCount}/${morphemes.length} 个语素, 耗时 ${validationTime}ms`)\n    } else {\n      console.warn(`❌ 验证失败: ${passedCount}/${morphemes.length} 个语素通过, ${errors.length} 个错误, ${warnings.length} 个警告, 耗时 ${validationTime}ms`)\n    }\n\n    return {\n      passed,\n      total_count: morphemes.length,\n      passed_count: passedCount,\n      failed_count: morphemes.length - passedCount,\n      errors,\n      warnings,\n      validation_time: validationTime,\n      timestamp: Date.now()\n    }\n  }\n\n  /**\n   * 验证基础结构\n   * \n   * @private\n   * @param morpheme 语素数据\n   * @param index 索引\n   * @returns 验证错误列表\n   */\n  private _validateBasicStructure(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // 验证必需字段 (v3.0标准)\n    const requiredFields = [\n      'id', 'text', 'category', 'subcategory', 'cultural_context',\n      'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n      'language_properties', 'quality_metrics', 'created_at', 'source', 'version'\n    ]\n\n    for (const field of requiredFields) {\n      if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined || morpheme[field as keyof Morpheme] === null) {\n        errors.push({\n          type: 'error',\n          code: 'MISSING_REQUIRED_FIELD',\n          message: `缺少必需字段: ${field}`,\n          field,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 所有字段现在都是必需的 (v3.0标准)\n    // 不再有可选字段验证\n\n    return errors\n  }\n\n  /**\n   * 验证数据类型\n   * \n   * @private\n   * @param morpheme 语素数据\n   * @param index 索引\n   * @returns 验证错误列表\n   */\n  private _validateDataTypes(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // 验证字符串字段\n    const stringFields = ['id', 'text', 'subcategory', 'source', 'version']\n    for (const field of stringFields) {\n      const value = morpheme[field as keyof Morpheme]\n      if (value !== undefined && typeof value !== 'string') {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_DATA_TYPE',\n          message: `字段 ${field} 必须是字符串类型`,\n          field,\n          expected: 'string',\n          actual: typeof value,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 验证数字字段\n    const numberFields = ['usage_frequency', 'quality_score', 'created_at']\n    for (const field of numberFields) {\n      const value = morpheme[field as keyof Morpheme]\n      if (value !== undefined && typeof value !== 'number') {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_DATA_TYPE',\n          message: `字段 ${field} 必须是数字类型`,\n          field,\n          expected: 'number',\n          actual: typeof value,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 验证数组字段\n    if (morpheme.semantic_vector !== undefined && !Array.isArray(morpheme.semantic_vector)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_DATA_TYPE',\n        message: 'semantic_vector 必须是数组类型',\n        field: 'semantic_vector',\n        expected: 'array',\n        actual: typeof morpheme.semantic_vector,\n        morpheme_id: morphemeId\n      })\n    }\n\n    if (morpheme.tags !== undefined && !Array.isArray(morpheme.tags)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_DATA_TYPE',\n        message: 'tags 必须是数组类型',\n        field: 'tags',\n        expected: 'array',\n        actual: typeof morpheme.tags,\n        morpheme_id: morphemeId\n      })\n    }\n\n    return errors\n  }\n\n  /**\n   * 验证业务逻辑\n   * \n   * @private\n   * @param morpheme 语素数据\n   * @param index 索引\n   * @returns 验证错误列表\n   */\n  private _validateBusinessLogic(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // 验证ID格式\n    if (morpheme.id && !VALIDATION_RULES.id_pattern.test(morpheme.id)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_ID_FORMAT',\n        message: `ID格式不正确，应为: {category}_{number}`,\n        field: 'id',\n        expected: VALIDATION_RULES.id_pattern.toString(),\n        actual: morpheme.id,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // 验证文本长度\n    if (morpheme.text) {\n      const textLength = morpheme.text.length\n      if (textLength < VALIDATION_RULES.text_length.min || textLength > VALIDATION_RULES.text_length.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_TEXT_LENGTH',\n          message: `文本长度必须在 ${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max} 之间`,\n          field: 'text',\n          expected: `${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max}`,\n          actual: textLength,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 验证类别\n    if (morpheme.category && !VALID_CATEGORIES.includes(morpheme.category as any)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_CATEGORY',\n        message: `无效的语素类别`,\n        field: 'category',\n        expected: VALID_CATEGORIES,\n        actual: morpheme.category,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // 验证文化语境\n    if (morpheme.cultural_context && !VALID_CONTEXTS.includes(morpheme.cultural_context as any)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_CULTURAL_CONTEXT',\n        message: `无效的文化语境`,\n        field: 'cultural_context',\n        expected: VALID_CONTEXTS,\n        actual: morpheme.cultural_context,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // 验证质量评分范围\n    if (typeof morpheme.quality_score === 'number') {\n      if (morpheme.quality_score < VALIDATION_RULES.quality_range.min || \n          morpheme.quality_score > VALIDATION_RULES.quality_range.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_QUALITY_SCORE',\n          message: `质量评分必须在 ${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max} 之间`,\n          field: 'quality_score',\n          expected: `${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max}`,\n          actual: morpheme.quality_score,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 验证使用频率范围\n    if (typeof morpheme.usage_frequency === 'number') {\n      if (morpheme.usage_frequency < VALIDATION_RULES.frequency_range.min || \n          morpheme.usage_frequency > VALIDATION_RULES.frequency_range.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_USAGE_FREQUENCY',\n          message: `使用频率必须在 ${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max} 之间`,\n          field: 'usage_frequency',\n          expected: `${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max}`,\n          actual: morpheme.usage_frequency,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 验证语义向量\n    if (Array.isArray(morpheme.semantic_vector)) {\n      if (morpheme.semantic_vector.length !== VALIDATION_RULES.semantic_vector_dimension) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_SEMANTIC_VECTOR_DIMENSION',\n          message: `语义向量维度必须为 ${VALIDATION_RULES.semantic_vector_dimension}`,\n          field: 'semantic_vector',\n          expected: VALIDATION_RULES.semantic_vector_dimension,\n          actual: morpheme.semantic_vector.length,\n          morpheme_id: morphemeId\n        })\n      }\n\n      // 验证向量元素是否为数字\n      for (let i = 0; i < morpheme.semantic_vector.length; i++) {\n        if (typeof morpheme.semantic_vector[i] !== 'number') {\n          errors.push({\n            type: 'error',\n            code: 'INVALID_SEMANTIC_VECTOR_ELEMENT',\n            message: `语义向量元素必须是数字类型`,\n            field: `semantic_vector[${i}]`,\n            expected: 'number',\n            actual: typeof morpheme.semantic_vector[i],\n            morpheme_id: morphemeId\n          })\n        }\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * 验证一致性\n   * \n   * @private\n   * @param morpheme 语素数据\n   * @param index 索引\n   * @returns 验证错误列表\n   */\n  private _validateConsistency(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // 验证ID与类别的一致性\n    if (morpheme.id && morpheme.category) {\n      const expectedPrefix = morpheme.category\n      if (!morpheme.id.startsWith(expectedPrefix)) {\n        errors.push({\n          type: 'warning',\n          code: 'INCONSISTENT_ID_CATEGORY',\n          message: `ID前缀与类别不一致`,\n          field: 'id',\n          expected: `${expectedPrefix}_xxx`,\n          actual: morpheme.id,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // 验证语言属性一致性\n    if (morpheme.language_properties && morpheme.text) {\n      const actualCharCount = morpheme.text.length\n      const declaredCharCount = morpheme.language_properties.character_count\n\n      if (declaredCharCount !== actualCharCount) {\n        errors.push({\n          type: 'warning',\n          code: 'INCONSISTENT_CHARACTER_COUNT',\n          message: `声明的字符数与实际不符`,\n          field: 'language_properties.character_count',\n          expected: actualCharCount,\n          actual: declaredCharCount,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * 验证自定义规则\n   * \n   * @private\n   * @param morpheme 语素数据\n   * @param index 索引\n   * @returns 验证错误列表\n   */\n  private _validateCustomRules(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n\n    for (const rule of this.customRules) {\n      if (!rule.enabled) continue\n\n      try {\n        const ruleErrors = rule.validator(morpheme)\n        errors.push(...ruleErrors)\n      } catch (error) {\n        errors.push({\n          type: 'error',\n          code: 'CUSTOM_RULE_ERROR',\n          message: `自定义规则 '${rule.name}' 执行失败: ${error instanceof Error ? error.message : String(error)}`,\n          morpheme_id: morpheme.id || `index_${index}`\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * 添加自定义验证规则\n   * \n   * @param rule 验证规则\n   */\n  addCustomRule(rule: ValidationRule): void {\n    this.customRules.push(rule)\n    console.log(`📋 添加自定义验证规则: ${rule.name}`)\n  }\n\n  /**\n   * 移除自定义验证规则\n   * \n   * @param ruleName 规则名称\n   */\n  removeCustomRule(ruleName: string): void {\n    const index = this.customRules.findIndex(rule => rule.name === ruleName)\n    if (index !== -1) {\n      this.customRules.splice(index, 1)\n      console.log(`🗑️ 移除自定义验证规则: ${ruleName}`)\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAnBN;;;;;;;;;;;AAcA;AACA;AACA;AAEA;AACA,MAAME,gBAAgB;AAAA;AAAA,CAAAF,aAAA,GAAAG,CAAA,OAAG;EACvB;EACAC,UAAU,EAAE,iBAAiB;EAC7B;EACAC,WAAW,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAE,CAAE;EAChC;EACAC,mBAAmB,EAAE,WAAW;EAChC;EACAC,UAAU,EAAE;IAAEH,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAE,CAAE;EAC/B;EACAG,yBAAyB,EAAE,EAAE;EAC7B;EACAC,aAAa,EAAE;IAAEL,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE;EAAG,CAAE;EACrC;EACAK,eAAe,EAAE;IAAEN,GAAG,EAAE,GAAG;IAAEC,GAAG,EAAE;EAAG,CAAE;EACvC;EACAM,cAAc,EAAE;IAAEP,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAC,CAAE;EAClC;EACAO,eAAe,EAAE;IAAER,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAE;CAC1B;AAEV;AACA,MAAMQ,gBAAgB;AAAA;AAAA,CAAAf,aAAA,GAAAG,CAAA,OAAGa,MAAM,CAACC,MAAM,CAAC;EACrCC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE,iBAAiB;EAClCC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE;CACX,CAAC;AAEF;AACA,MAAMC,cAAc;AAAA;AAAA,CAAAxB,aAAA,GAAAG,CAAA,OAAGa,MAAM,CAACC,MAAM,CAAC;EACnCQ,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;CACV,CAAC;AA8EF;AACA;AACA;AAEA;;;;;AAKA,OAAM,MAAOC,aAAa;EAChBC,MAAM;EACNC,WAAW;EAEnB;;;;;EAKAC,YAAYF,MAAA;EAAA;EAAA,CAAA7B,aAAA,GAAAgC,CAAA,UAA2B,EAAE;IAAA;IAAAhC,aAAA,GAAAiC,CAAA;IAAAjC,aAAA,GAAAG,CAAA;IACvC,IAAI,CAAC0B,MAAM,GAAG;MACZK,WAAW;MAAE;MAAA,CAAAlC,aAAA,GAAAgC,CAAA,UAAAH,MAAM,CAACK,WAAW;MAAA;MAAA,CAAAlC,aAAA,GAAAgC,CAAA,UAAI,IAAI;MACvCG,aAAa;MAAE;MAAA,CAAAnC,aAAA,GAAAgC,CAAA,UAAAH,MAAM,CAACM,aAAa;MAAA;MAAA,CAAAnC,aAAA,GAAAgC,CAAA,UAAI,KAAK;MAC5CI,YAAY;MAAE;MAAA,CAAApC,aAAA,GAAAgC,CAAA,UAAAH,MAAM,CAACO,YAAY;MAAA;MAAA,CAAApC,aAAA,GAAAgC,CAAA,UAAI,EAAE;MACvCK,UAAU;MAAE;MAAA,CAAArC,aAAA,GAAAgC,CAAA,UAAAH,MAAM,CAACQ,UAAU;MAAA;MAAA,CAAArC,aAAA,GAAAgC,CAAA,UAAI,IAAI;KACtC;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IAED,IAAI,CAAC2B,WAAW,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAACO,YAAY,CAAC;EAClD;EAEA;;;;;;EAMA,MAAME,QAAQA,CAACC,SAAqB;IAAA;IAAAvC,aAAA,GAAAiC,CAAA;IAClC,MAAMO,SAAS;IAAA;IAAA,CAAAxC,aAAA,GAAAG,CAAA,OAAGsC,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,MAAM;IAAA;IAAA,CAAA3C,aAAA,GAAAG,CAAA,OAAsB,EAAE;IACpC,MAAMyC,QAAQ;IAAA;IAAA,CAAA5C,aAAA,GAAAG,CAAA,OAAsB,EAAE;IACtC,IAAI0C,WAAW;IAAA;IAAA,CAAA7C,aAAA,GAAAG,CAAA,OAAG,CAAC;IAAA;IAAAH,aAAA,GAAAG,CAAA;IAEnB2C,OAAO,CAACC,GAAG,CAAC,WAAWR,SAAS,CAACS,MAAM,SAAS,CAAC;IAAA;IAAAhD,aAAA,GAAAG,CAAA;IAEjD,KAAK,IAAI8C,CAAC;IAAA;IAAA,CAAAjD,aAAA,GAAAG,CAAA,QAAG,CAAC,GAAE8C,CAAC,GAAGV,SAAS,CAACS,MAAM,EAAEC,CAAC,EAAE,EAAE;MACzC,MAAMC,QAAQ;MAAA;MAAA,CAAAlD,aAAA,GAAAG,CAAA,QAAGoC,SAAS,CAACU,CAAC,CAAC;MAAA;MAAAjD,aAAA,GAAAG,CAAA;MAE7B,IAAI;QACF;QACA,MAAMgD,WAAW;QAAA;QAAA,CAAAnD,aAAA,GAAAG,CAAA,QAAG,IAAI,CAACiD,uBAAuB,CAACF,QAAQ,EAAED,CAAC,CAAC;QAE7D;QACA,MAAMI,UAAU;QAAA;QAAA,CAAArD,aAAA,GAAAG,CAAA,QAAG,IAAI,CAACmD,kBAAkB,CAACJ,QAAQ,EAAED,CAAC,CAAC;QAEvD;QACA,MAAMM,WAAW;QAAA;QAAA,CAAAvD,aAAA,GAAAG,CAAA,QAAG,IAAI,CAACqD,sBAAsB,CAACN,QAAQ,EAAED,CAAC,CAAC;QAE5D;QACA,MAAMQ,iBAAiB;QAAA;QAAA,CAAAzD,aAAA,GAAAG,CAAA,QAAG,IAAI,CAACuD,oBAAoB,CAACR,QAAQ,EAAED,CAAC,CAAC;QAEhE;QACA,MAAMU,YAAY;QAAA;QAAA,CAAA3D,aAAA,GAAAG,CAAA,QAAG,IAAI,CAACyD,oBAAoB,CAACV,QAAQ,EAAED,CAAC,CAAC;QAE3D;QACA,MAAMY,SAAS;QAAA;QAAA,CAAA7D,aAAA,GAAAG,CAAA,QAAG,CAChB,GAAGgD,WAAW,EACd,GAAGE,UAAU,EACb,GAAGE,WAAW,EACd,GAAGE,iBAAiB,EACpB,GAAGE,YAAY,CAChB;QAED;QACA,MAAMG,cAAc;QAAA;QAAA,CAAA9D,aAAA,GAAAG,CAAA,QAAG0D,SAAS,CAACE,MAAM,CAACC,CAAC,IAAI;UAAA;UAAAhE,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAG,CAAA;UAAA,OAAA6D,CAAC,CAACC,IAAI,KAAK,OAAO;QAAP,CAAO,CAAC;QAChE,MAAMC,gBAAgB;QAAA;QAAA,CAAAlE,aAAA,GAAAG,CAAA,QAAG0D,SAAS,CAACE,MAAM,CAACC,CAAC,IAAI;UAAA;UAAAhE,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAG,CAAA;UAAA,OAAA6D,CAAC,CAACC,IAAI,KAAK,SAAS;QAAT,CAAS,CAAC;QAAA;QAAAjE,aAAA,GAAAG,CAAA;QAEpEwC,MAAM,CAACwB,IAAI,CAAC,GAAGL,cAAc,CAAC;QAAA;QAAA9D,aAAA,GAAAG,CAAA;QAC9ByC,QAAQ,CAACuB,IAAI,CAAC,GAAGD,gBAAgB,CAAC;QAElC;QAAA;QAAAlE,aAAA,GAAAG,CAAA;QACA,IAAI2D,cAAc,CAACd,MAAM,KAAK,CAAC,EAAE;UAAA;UAAAhD,aAAA,GAAAgC,CAAA;UAAAhC,aAAA,GAAAG,CAAA;UAC/B0C,WAAW,EAAE;QACf,CAAC;QAAA;QAAA;UAAA7C,aAAA,GAAAgC,CAAA;QAAA;QAED;QAAAhC,aAAA,GAAAG,CAAA;QACA,IAAIwC,MAAM,CAACK,MAAM,IAAI,IAAI,CAACnB,MAAM,CAACQ,UAAU,EAAE;UAAA;UAAArC,aAAA,GAAAgC,CAAA;UAAAhC,aAAA,GAAAG,CAAA;UAC3C2C,OAAO,CAACsB,IAAI,CAAC,gBAAgB,IAAI,CAACvC,MAAM,CAACQ,UAAU,QAAQ,CAAC;UAAA;UAAArC,aAAA,GAAAG,CAAA;UAC5D;QACF,CAAC;QAAA;QAAA;UAAAH,aAAA,GAAAgC,CAAA;QAAA;MAEH,CAAC,CAAC,OAAOqC,KAAK,EAAE;QAAA;QAAArE,aAAA,GAAAG,CAAA;QACdwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,sBAAsB;UAC5BC,OAAO,EAAE,cAAcF,KAAK,YAAYG,KAAK;UAAA;UAAA,CAAAxE,aAAA,GAAAgC,CAAA,UAAGqC,KAAK,CAACE,OAAO;UAAA;UAAA,CAAAvE,aAAA,GAAAgC,CAAA,UAAGyC,MAAM,CAACJ,KAAK,CAAC,GAAE;UAC/EK,WAAW;UAAE;UAAA,CAAA1E,aAAA,GAAAgC,CAAA,UAAAkB,QAAQ,CAACyB,EAAE;UAAA;UAAA,CAAA3E,aAAA,GAAAgC,CAAA,UAAI,SAASiB,CAAC,EAAE;SACzC,CAAC;MACJ;IACF;IAEA,MAAM2B,cAAc;IAAA;IAAA,CAAA5E,aAAA,GAAAG,CAAA,QAAGsC,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;IAC7C,MAAMqC,MAAM;IAAA;IAAA,CAAA7E,aAAA,GAAAG,CAAA,QAAGwC,MAAM,CAACK,MAAM,KAAK,CAAC;IAElC;IAAA;IAAAhD,aAAA,GAAAG,CAAA;IACA,IAAI0E,MAAM,EAAE;MAAA;MAAA7E,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MACV2C,OAAO,CAACC,GAAG,CAAC,WAAWF,WAAW,IAAIN,SAAS,CAACS,MAAM,YAAY4B,cAAc,IAAI,CAAC;IACvF,CAAC,MAAM;MAAA;MAAA5E,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MACL2C,OAAO,CAACsB,IAAI,CAAC,WAAWvB,WAAW,IAAIN,SAAS,CAACS,MAAM,WAAWL,MAAM,CAACK,MAAM,SAASJ,QAAQ,CAACI,MAAM,YAAY4B,cAAc,IAAI,CAAC;IACxI;IAAC;IAAA5E,aAAA,GAAAG,CAAA;IAED,OAAO;MACL0E,MAAM;MACNC,WAAW,EAAEvC,SAAS,CAACS,MAAM;MAC7B+B,YAAY,EAAElC,WAAW;MACzBmC,YAAY,EAAEzC,SAAS,CAACS,MAAM,GAAGH,WAAW;MAC5CF,MAAM;MACNC,QAAQ;MACRqC,eAAe,EAAEL,cAAc;MAC/BM,SAAS,EAAEzC,IAAI,CAACC,GAAG;KACpB;EACH;EAEA;;;;;;;;EAQQU,uBAAuBA,CAACF,QAAkB,EAAEiC,KAAa;IAAA;IAAAnF,aAAA,GAAAiC,CAAA;IAC/D,MAAMU,MAAM;IAAA;IAAA,CAAA3C,aAAA,GAAAG,CAAA,QAAsB,EAAE;IACpC,MAAMiF,UAAU;IAAA;IAAA,CAAApF,aAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAI,SAASmD,KAAK,EAAE;IAElD;IACA,MAAME,cAAc;IAAA;IAAA,CAAArF,aAAA,GAAAG,CAAA,QAAG,CACrB,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,EAC7D,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAC5E;IAAA;IAAAH,aAAA,GAAAG,CAAA;IAED,KAAK,MAAMmF,KAAK,IAAID,cAAc,EAAE;MAAA;MAAArF,aAAA,GAAAG,CAAA;MAClC;MAAI;MAAA,CAAAH,aAAA,GAAAgC,CAAA,aAAEsD,KAAK,IAAIpC,QAAQ,CAAC;MAAA;MAAA,CAAAlD,aAAA,GAAAgC,CAAA,WAAIkB,QAAQ,CAACoC,KAAuB,CAAC,KAAKC,SAAS;MAAA;MAAA,CAAAvF,aAAA,GAAAgC,CAAA,WAAIkB,QAAQ,CAACoC,KAAuB,CAAC,KAAK,IAAI,GAAE;QAAA;QAAAtF,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QACzHwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,wBAAwB;UAC9BC,OAAO,EAAE,WAAWe,KAAK,EAAE;UAC3BA,KAAK;UACLZ,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH;IAEA;IACA;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IAEA,OAAOwC,MAAM;EACf;EAEA;;;;;;;;EAQQW,kBAAkBA,CAACJ,QAAkB,EAAEiC,KAAa;IAAA;IAAAnF,aAAA,GAAAiC,CAAA;IAC1D,MAAMU,MAAM;IAAA;IAAA,CAAA3C,aAAA,GAAAG,CAAA,QAAsB,EAAE;IACpC,MAAMiF,UAAU;IAAA;IAAA,CAAApF,aAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAI,SAASmD,KAAK,EAAE;IAElD;IACA,MAAMK,YAAY;IAAA;IAAA,CAAAxF,aAAA,GAAAG,CAAA,QAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC;IAAA;IAAAH,aAAA,GAAAG,CAAA;IACvE,KAAK,MAAMmF,KAAK,IAAIE,YAAY,EAAE;MAChC,MAAMC,KAAK;MAAA;MAAA,CAAAzF,aAAA,GAAAG,CAAA,QAAG+C,QAAQ,CAACoC,KAAuB,CAAC;MAAA;MAAAtF,aAAA,GAAAG,CAAA;MAC/C;MAAI;MAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAyD,KAAK,KAAKF,SAAS;MAAA;MAAA,CAAAvF,aAAA,GAAAgC,CAAA,WAAI,OAAOyD,KAAK,KAAK,QAAQ,GAAE;QAAA;QAAAzF,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QACpDwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,mBAAmB;UACzBC,OAAO,EAAE,MAAMe,KAAK,WAAW;UAC/BA,KAAK;UACLI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,OAAOF,KAAK;UACpBf,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH;IAEA;IACA,MAAM4D,YAAY;IAAA;IAAA,CAAA5F,aAAA,GAAAG,CAAA,QAAG,CAAC,iBAAiB,EAAE,eAAe,EAAE,YAAY,CAAC;IAAA;IAAAH,aAAA,GAAAG,CAAA;IACvE,KAAK,MAAMmF,KAAK,IAAIM,YAAY,EAAE;MAChC,MAAMH,KAAK;MAAA;MAAA,CAAAzF,aAAA,GAAAG,CAAA,QAAG+C,QAAQ,CAACoC,KAAuB,CAAC;MAAA;MAAAtF,aAAA,GAAAG,CAAA;MAC/C;MAAI;MAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAyD,KAAK,KAAKF,SAAS;MAAA;MAAA,CAAAvF,aAAA,GAAAgC,CAAA,WAAI,OAAOyD,KAAK,KAAK,QAAQ,GAAE;QAAA;QAAAzF,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QACpDwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,mBAAmB;UACzBC,OAAO,EAAE,MAAMe,KAAK,UAAU;UAC9BA,KAAK;UACLI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,OAAOF,KAAK;UACpBf,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH;IAEA;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAAC2C,eAAe,KAAKN,SAAS;IAAA;IAAA,CAAAvF,aAAA,GAAAgC,CAAA,WAAI,CAAC8D,KAAK,CAACC,OAAO,CAAC7C,QAAQ,CAAC2C,eAAe,CAAC,GAAE;MAAA;MAAA7F,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MACtFwC,MAAM,CAACwB,IAAI,CAAC;QACVF,IAAI,EAAE,OAAO;QACbK,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,yBAAyB;QAClCe,KAAK,EAAE,iBAAiB;QACxBI,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,OAAOzC,QAAQ,CAAC2C,eAAe;QACvCnB,WAAW,EAAEU;OACd,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApF,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IAED;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAAC8C,IAAI,KAAKT,SAAS;IAAA;IAAA,CAAAvF,aAAA,GAAAgC,CAAA,WAAI,CAAC8D,KAAK,CAACC,OAAO,CAAC7C,QAAQ,CAAC8C,IAAI,CAAC,GAAE;MAAA;MAAAhG,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAChEwC,MAAM,CAACwB,IAAI,CAAC;QACVF,IAAI,EAAE,OAAO;QACbK,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,cAAc;QACvBe,KAAK,EAAE,MAAM;QACbI,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,OAAOzC,QAAQ,CAAC8C,IAAI;QAC5BtB,WAAW,EAAEU;OACd,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApF,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IAED,OAAOwC,MAAM;EACf;EAEA;;;;;;;;EAQQa,sBAAsBA,CAACN,QAAkB,EAAEiC,KAAa;IAAA;IAAAnF,aAAA,GAAAiC,CAAA;IAC9D,MAAMU,MAAM;IAAA;IAAA,CAAA3C,aAAA,GAAAG,CAAA,QAAsB,EAAE;IACpC,MAAMiF,UAAU;IAAA;IAAA,CAAApF,aAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAI,SAASmD,KAAK,EAAE;IAElD;IAAA;IAAAnF,aAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAI,CAAC9B,gBAAgB,CAACE,UAAU,CAAC6F,IAAI,CAAC/C,QAAQ,CAACyB,EAAE,CAAC,GAAE;MAAA;MAAA3E,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MACjEwC,MAAM,CAACwB,IAAI,CAAC;QACVF,IAAI,EAAE,OAAO;QACbK,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,iCAAiC;QAC1Ce,KAAK,EAAE,IAAI;QACXI,QAAQ,EAAExF,gBAAgB,CAACE,UAAU,CAAC8F,QAAQ,EAAE;QAChDP,MAAM,EAAEzC,QAAQ,CAACyB,EAAE;QACnBD,WAAW,EAAEU;OACd,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApF,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA,IAAI+C,QAAQ,CAACiD,IAAI,EAAE;MAAA;MAAAnG,aAAA,GAAAgC,CAAA;MACjB,MAAMoE,UAAU;MAAA;MAAA,CAAApG,aAAA,GAAAG,CAAA,QAAG+C,QAAQ,CAACiD,IAAI,CAACnD,MAAM;MAAA;MAAAhD,aAAA,GAAAG,CAAA;MACvC;MAAI;MAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAoE,UAAU,GAAGlG,gBAAgB,CAACG,WAAW,CAACC,GAAG;MAAA;MAAA,CAAAN,aAAA,GAAAgC,CAAA,WAAIoE,UAAU,GAAGlG,gBAAgB,CAACG,WAAW,CAACE,GAAG,GAAE;QAAA;QAAAP,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QAClGwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,qBAAqB;UAC3BC,OAAO,EAAE,WAAWrE,gBAAgB,CAACG,WAAW,CAACC,GAAG,IAAIJ,gBAAgB,CAACG,WAAW,CAACE,GAAG,KAAK;UAC7F+E,KAAK,EAAE,MAAM;UACbI,QAAQ,EAAE,GAAGxF,gBAAgB,CAACG,WAAW,CAACC,GAAG,IAAIJ,gBAAgB,CAACG,WAAW,CAACE,GAAG,EAAE;UACnFoF,MAAM,EAAES,UAAU;UAClB1B,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAhC,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACmD,QAAQ;IAAA;IAAA,CAAArG,aAAA,GAAAgC,CAAA,WAAI,CAACjB,gBAAgB,CAACuF,QAAQ,CAACpD,QAAQ,CAACmD,QAAe,CAAC,GAAE;MAAA;MAAArG,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAC7EwC,MAAM,CAACwB,IAAI,CAAC;QACVF,IAAI,EAAE,OAAO;QACbK,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE,SAAS;QAClBe,KAAK,EAAE,UAAU;QACjBI,QAAQ,EAAE3E,gBAAgB;QAC1B4E,MAAM,EAAEzC,QAAQ,CAACmD,QAAQ;QACzB3B,WAAW,EAAEU;OACd,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApF,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACqD,gBAAgB;IAAA;IAAA,CAAAvG,aAAA,GAAAgC,CAAA,WAAI,CAACR,cAAc,CAAC8E,QAAQ,CAACpD,QAAQ,CAACqD,gBAAuB,CAAC,GAAE;MAAA;MAAAvG,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAC3FwC,MAAM,CAACwB,IAAI,CAAC;QACVF,IAAI,EAAE,OAAO;QACbK,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAE,SAAS;QAClBe,KAAK,EAAE,kBAAkB;QACzBI,QAAQ,EAAElE,cAAc;QACxBmE,MAAM,EAAEzC,QAAQ,CAACqD,gBAAgB;QACjC7B,WAAW,EAAEU;OACd,CAAC;IACJ,CAAC;IAAA;IAAA;MAAApF,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA,IAAI,OAAO+C,QAAQ,CAACsD,aAAa,KAAK,QAAQ,EAAE;MAAA;MAAAxG,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAC9C;MAAI;MAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACsD,aAAa,GAAGtG,gBAAgB,CAACS,aAAa,CAACL,GAAG;MAAA;MAAA,CAAAN,aAAA,GAAAgC,CAAA,WAC3DkB,QAAQ,CAACsD,aAAa,GAAGtG,gBAAgB,CAACS,aAAa,CAACJ,GAAG,GAAE;QAAA;QAAAP,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QAC/DwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,uBAAuB;UAC7BC,OAAO,EAAE,WAAWrE,gBAAgB,CAACS,aAAa,CAACL,GAAG,IAAIJ,gBAAgB,CAACS,aAAa,CAACJ,GAAG,KAAK;UACjG+E,KAAK,EAAE,eAAe;UACtBI,QAAQ,EAAE,GAAGxF,gBAAgB,CAACS,aAAa,CAACL,GAAG,IAAIJ,gBAAgB,CAACS,aAAa,CAACJ,GAAG,EAAE;UACvFoF,MAAM,EAAEzC,QAAQ,CAACsD,aAAa;UAC9B9B,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAhC,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA,IAAI,OAAO+C,QAAQ,CAACuD,eAAe,KAAK,QAAQ,EAAE;MAAA;MAAAzG,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAChD;MAAI;MAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACuD,eAAe,GAAGvG,gBAAgB,CAACU,eAAe,CAACN,GAAG;MAAA;MAAA,CAAAN,aAAA,GAAAgC,CAAA,WAC/DkB,QAAQ,CAACuD,eAAe,GAAGvG,gBAAgB,CAACU,eAAe,CAACL,GAAG,GAAE;QAAA;QAAAP,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QACnEwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,yBAAyB;UAC/BC,OAAO,EAAE,WAAWrE,gBAAgB,CAACU,eAAe,CAACN,GAAG,IAAIJ,gBAAgB,CAACU,eAAe,CAACL,GAAG,KAAK;UACrG+E,KAAK,EAAE,iBAAiB;UACxBI,QAAQ,EAAE,GAAGxF,gBAAgB,CAACU,eAAe,CAACN,GAAG,IAAIJ,gBAAgB,CAACU,eAAe,CAACL,GAAG,EAAE;UAC3FoF,MAAM,EAAEzC,QAAQ,CAACuD,eAAe;UAChC/B,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAhC,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA,IAAI2F,KAAK,CAACC,OAAO,CAAC7C,QAAQ,CAAC2C,eAAe,CAAC,EAAE;MAAA;MAAA7F,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAC3C,IAAI+C,QAAQ,CAAC2C,eAAe,CAAC7C,MAAM,KAAK9C,gBAAgB,CAACQ,yBAAyB,EAAE;QAAA;QAAAV,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QAClFwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,mCAAmC;UACzCC,OAAO,EAAE,aAAarE,gBAAgB,CAACQ,yBAAyB,EAAE;UAClE4E,KAAK,EAAE,iBAAiB;UACxBI,QAAQ,EAAExF,gBAAgB,CAACQ,yBAAyB;UACpDiF,MAAM,EAAEzC,QAAQ,CAAC2C,eAAe,CAAC7C,MAAM;UACvC0B,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;MAED;MAAAhC,aAAA,GAAAG,CAAA;MACA,KAAK,IAAI8C,CAAC;MAAA;MAAA,CAAAjD,aAAA,GAAAG,CAAA,QAAG,CAAC,GAAE8C,CAAC,GAAGC,QAAQ,CAAC2C,eAAe,CAAC7C,MAAM,EAAEC,CAAC,EAAE,EAAE;QAAA;QAAAjD,aAAA,GAAAG,CAAA;QACxD,IAAI,OAAO+C,QAAQ,CAAC2C,eAAe,CAAC5C,CAAC,CAAC,KAAK,QAAQ,EAAE;UAAA;UAAAjD,aAAA,GAAAgC,CAAA;UAAAhC,aAAA,GAAAG,CAAA;UACnDwC,MAAM,CAACwB,IAAI,CAAC;YACVF,IAAI,EAAE,OAAO;YACbK,IAAI,EAAE,iCAAiC;YACvCC,OAAO,EAAE,eAAe;YACxBe,KAAK,EAAE,mBAAmBrC,CAAC,GAAG;YAC9ByC,QAAQ,EAAE,QAAQ;YAClBC,MAAM,EAAE,OAAOzC,QAAQ,CAAC2C,eAAe,CAAC5C,CAAC,CAAC;YAC1CyB,WAAW,EAAEU;WACd,CAAC;QACJ,CAAC;QAAA;QAAA;UAAApF,aAAA,GAAAgC,CAAA;QAAA;MACH;IACF,CAAC;IAAA;IAAA;MAAAhC,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IAED,OAAOwC,MAAM;EACf;EAEA;;;;;;;;EAQQe,oBAAoBA,CAACR,QAAkB,EAAEiC,KAAa;IAAA;IAAAnF,aAAA,GAAAiC,CAAA;IAC5D,MAAMU,MAAM;IAAA;IAAA,CAAA3C,aAAA,GAAAG,CAAA,QAAsB,EAAE;IACpC,MAAMiF,UAAU;IAAA;IAAA,CAAApF,aAAA,GAAAG,CAAA;IAAG;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAI,SAASmD,KAAK,EAAE;IAElD;IAAA;IAAAnF,aAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;IAAA;IAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAIkB,QAAQ,CAACmD,QAAQ,GAAE;MAAA;MAAArG,aAAA,GAAAgC,CAAA;MACpC,MAAM0E,cAAc;MAAA;MAAA,CAAA1G,aAAA,GAAAG,CAAA,QAAG+C,QAAQ,CAACmD,QAAQ;MAAA;MAAArG,aAAA,GAAAG,CAAA;MACxC,IAAI,CAAC+C,QAAQ,CAACyB,EAAE,CAACgC,UAAU,CAACD,cAAc,CAAC,EAAE;QAAA;QAAA1G,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QAC3CwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,SAAS;UACfK,IAAI,EAAE,0BAA0B;UAChCC,OAAO,EAAE,YAAY;UACrBe,KAAK,EAAE,IAAI;UACXI,QAAQ,EAAE,GAAGgB,cAAc,MAAM;UACjCf,MAAM,EAAEzC,QAAQ,CAACyB,EAAE;UACnBD,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAhC,aAAA,GAAAgC,CAAA;IAAA;IAED;IAAAhC,aAAA,GAAAG,CAAA;IACA;IAAI;IAAA,CAAAH,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAAC0D,mBAAmB;IAAA;IAAA,CAAA5G,aAAA,GAAAgC,CAAA,WAAIkB,QAAQ,CAACiD,IAAI,GAAE;MAAA;MAAAnG,aAAA,GAAAgC,CAAA;MACjD,MAAM6E,eAAe;MAAA;MAAA,CAAA7G,aAAA,GAAAG,CAAA,QAAG+C,QAAQ,CAACiD,IAAI,CAACnD,MAAM;MAC5C,MAAM8D,iBAAiB;MAAA;MAAA,CAAA9G,aAAA,GAAAG,CAAA,QAAG+C,QAAQ,CAAC0D,mBAAmB,CAAC9F,eAAe;MAAA;MAAAd,aAAA,GAAAG,CAAA;MAEtE,IAAI2G,iBAAiB,KAAKD,eAAe,EAAE;QAAA;QAAA7G,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QACzCwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,SAAS;UACfK,IAAI,EAAE,8BAA8B;UACpCC,OAAO,EAAE,aAAa;UACtBe,KAAK,EAAE,qCAAqC;UAC5CI,QAAQ,EAAEmB,eAAe;UACzBlB,MAAM,EAAEmB,iBAAiB;UACzBpC,WAAW,EAAEU;SACd,CAAC;MACJ,CAAC;MAAA;MAAA;QAAApF,aAAA,GAAAgC,CAAA;MAAA;IACH,CAAC;IAAA;IAAA;MAAAhC,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAG,CAAA;IAED,OAAOwC,MAAM;EACf;EAEA;;;;;;;;EAQQiB,oBAAoBA,CAACV,QAAkB,EAAEiC,KAAa;IAAA;IAAAnF,aAAA,GAAAiC,CAAA;IAC5D,MAAMU,MAAM;IAAA;IAAA,CAAA3C,aAAA,GAAAG,CAAA,SAAsB,EAAE;IAAA;IAAAH,aAAA,GAAAG,CAAA;IAEpC,KAAK,MAAM4G,IAAI,IAAI,IAAI,CAACjF,WAAW,EAAE;MAAA;MAAA9B,aAAA,GAAAG,CAAA;MACnC,IAAI,CAAC4G,IAAI,CAACC,OAAO,EAAE;QAAA;QAAAhH,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAG,CAAA;QAAA;MAAA,CAAQ;MAAA;MAAA;QAAAH,aAAA,GAAAgC,CAAA;MAAA;MAAAhC,aAAA,GAAAG,CAAA;MAE3B,IAAI;QACF,MAAM8G,UAAU;QAAA;QAAA,CAAAjH,aAAA,GAAAG,CAAA,SAAG4G,IAAI,CAACG,SAAS,CAAChE,QAAQ,CAAC;QAAA;QAAAlD,aAAA,GAAAG,CAAA;QAC3CwC,MAAM,CAACwB,IAAI,CAAC,GAAG8C,UAAU,CAAC;MAC5B,CAAC,CAAC,OAAO5C,KAAK,EAAE;QAAA;QAAArE,aAAA,GAAAG,CAAA;QACdwC,MAAM,CAACwB,IAAI,CAAC;UACVF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,mBAAmB;UACzBC,OAAO,EAAE,UAAUwC,IAAI,CAACI,IAAI,WAAW9C,KAAK,YAAYG,KAAK;UAAA;UAAA,CAAAxE,aAAA,GAAAgC,CAAA,WAAGqC,KAAK,CAACE,OAAO;UAAA;UAAA,CAAAvE,aAAA,GAAAgC,CAAA,WAAGyC,MAAM,CAACJ,KAAK,CAAC,GAAE;UAC/FK,WAAW;UAAE;UAAA,CAAA1E,aAAA,GAAAgC,CAAA,WAAAkB,QAAQ,CAACyB,EAAE;UAAA;UAAA,CAAA3E,aAAA,GAAAgC,CAAA,WAAI,SAASmD,KAAK,EAAE;SAC7C,CAAC;MACJ;IACF;IAAC;IAAAnF,aAAA,GAAAG,CAAA;IAED,OAAOwC,MAAM;EACf;EAEA;;;;;EAKAyE,aAAaA,CAACL,IAAoB;IAAA;IAAA/G,aAAA,GAAAiC,CAAA;IAAAjC,aAAA,GAAAG,CAAA;IAChC,IAAI,CAAC2B,WAAW,CAACqC,IAAI,CAAC4C,IAAI,CAAC;IAAA;IAAA/G,aAAA,GAAAG,CAAA;IAC3B2C,OAAO,CAACC,GAAG,CAAC,iBAAiBgE,IAAI,CAACI,IAAI,EAAE,CAAC;EAC3C;EAEA;;;;;EAKAE,gBAAgBA,CAACC,QAAgB;IAAA;IAAAtH,aAAA,GAAAiC,CAAA;IAC/B,MAAMkD,KAAK;IAAA;IAAA,CAAAnF,aAAA,GAAAG,CAAA,SAAG,IAAI,CAAC2B,WAAW,CAACyF,SAAS,CAACR,IAAI,IAAI;MAAA;MAAA/G,aAAA,GAAAiC,CAAA;MAAAjC,aAAA,GAAAG,CAAA;MAAA,OAAA4G,IAAI,CAACI,IAAI,KAAKG,QAAQ;IAAR,CAAQ,CAAC;IAAA;IAAAtH,aAAA,GAAAG,CAAA;IACxE,IAAIgF,KAAK,KAAK,CAAC,CAAC,EAAE;MAAA;MAAAnF,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAG,CAAA;MAChB,IAAI,CAAC2B,WAAW,CAAC0F,MAAM,CAACrC,KAAK,EAAE,CAAC,CAAC;MAAA;MAAAnF,aAAA,GAAAG,CAAA;MACjC2C,OAAO,CAACC,GAAG,CAAC,kBAAkBuE,QAAQ,EAAE,CAAC;IAC3C,CAAC;IAAA;IAAA;MAAAtH,aAAA,GAAAgC,CAAA;IAAA;EACH", "ignoreList": []}