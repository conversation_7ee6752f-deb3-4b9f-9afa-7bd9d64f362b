352c76aec254af8231fe439b78073eb6
/* istanbul ignore next */
function cov_bk4eiopux() {
  var path = "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts";
  var hash = "6557cebfead73145c700f457ab2efa1fc390653b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 25
        },
        end: {
          line: 35,
          column: 1
        }
      },
      "1": {
        start: {
          line: 37,
          column: 25
        },
        end: {
          line: 44,
          column: 2
        }
      },
      "2": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 50,
          column: 2
        }
      },
      "3": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 73,
          column: 10
        }
      },
      "4": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 74,
          column: 57
        }
      },
      "5": {
        start: {
          line: 83,
          column: 26
        },
        end: {
          line: 83,
          column: 36
        }
      },
      "6": {
        start: {
          line: 84,
          column: 23
        },
        end: {
          line: 84,
          column: 25
        }
      },
      "7": {
        start: {
          line: 85,
          column: 25
        },
        end: {
          line: 85,
          column: 27
        }
      },
      "8": {
        start: {
          line: 86,
          column: 26
        },
        end: {
          line: 86,
          column: 27
        }
      },
      "9": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 87,
          column: 58
        }
      },
      "10": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 132,
          column: 9
        }
      },
      "11": {
        start: {
          line: 88,
          column: 21
        },
        end: {
          line: 88,
          column: 22
        }
      },
      "12": {
        start: {
          line: 89,
          column: 29
        },
        end: {
          line: 89,
          column: 41
        }
      },
      "13": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 131,
          column: 13
        }
      },
      "14": {
        start: {
          line: 92,
          column: 36
        },
        end: {
          line: 92,
          column: 77
        }
      },
      "15": {
        start: {
          line: 94,
          column: 35
        },
        end: {
          line: 94,
          column: 71
        }
      },
      "16": {
        start: {
          line: 96,
          column: 36
        },
        end: {
          line: 96,
          column: 76
        }
      },
      "17": {
        start: {
          line: 98,
          column: 42
        },
        end: {
          line: 98,
          column: 80
        }
      },
      "18": {
        start: {
          line: 100,
          column: 37
        },
        end: {
          line: 100,
          column: 75
        }
      },
      "19": {
        start: {
          line: 102,
          column: 34
        },
        end: {
          line: 108,
          column: 17
        }
      },
      "20": {
        start: {
          line: 110,
          column: 39
        },
        end: {
          line: 110,
          column: 80
        }
      },
      "21": {
        start: {
          line: 110,
          column: 61
        },
        end: {
          line: 110,
          column: 79
        }
      },
      "22": {
        start: {
          line: 111,
          column: 41
        },
        end: {
          line: 111,
          column: 84
        }
      },
      "23": {
        start: {
          line: 111,
          column: 63
        },
        end: {
          line: 111,
          column: 83
        }
      },
      "24": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 47
        }
      },
      "25": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 51
        }
      },
      "26": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 117,
          column: 17
        }
      },
      "27": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 116,
          column: 34
        }
      },
      "28": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 122,
          column: 17
        }
      },
      "29": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 81
        }
      },
      "30": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 121,
          column: 26
        }
      },
      "31": {
        start: {
          line: 125,
          column: 16
        },
        end: {
          line: 130,
          column: 19
        }
      },
      "32": {
        start: {
          line: 133,
          column: 31
        },
        end: {
          line: 133,
          column: 53
        }
      },
      "33": {
        start: {
          line: 134,
          column: 23
        },
        end: {
          line: 134,
          column: 42
        }
      },
      "34": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 141,
          column: 9
        }
      },
      "35": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 137,
          column: 98
        }
      },
      "36": {
        start: {
          line: 140,
          column: 12
        },
        end: {
          line: 140,
          column: 147
        }
      },
      "37": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 151,
          column: 10
        }
      },
      "38": {
        start: {
          line: 162,
          column: 23
        },
        end: {
          line: 162,
          column: 25
        }
      },
      "39": {
        start: {
          line: 163,
          column: 27
        },
        end: {
          line: 163,
          column: 58
        }
      },
      "40": {
        start: {
          line: 165,
          column: 31
        },
        end: {
          line: 169,
          column: 9
        }
      },
      "41": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 180,
          column: 9
        }
      },
      "42": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 179,
          column: 13
        }
      },
      "43": {
        start: {
          line: 172,
          column: 16
        },
        end: {
          line: 178,
          column: 19
        }
      },
      "44": {
        start: {
          line: 183,
          column: 8
        },
        end: {
          line: 183,
          column: 22
        }
      },
      "45": {
        start: {
          line: 194,
          column: 23
        },
        end: {
          line: 194,
          column: 25
        }
      },
      "46": {
        start: {
          line: 195,
          column: 27
        },
        end: {
          line: 195,
          column: 58
        }
      },
      "47": {
        start: {
          line: 197,
          column: 29
        },
        end: {
          line: 197,
          column: 79
        }
      },
      "48": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "49": {
        start: {
          line: 199,
          column: 26
        },
        end: {
          line: 199,
          column: 41
        }
      },
      "50": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 210,
          column: 13
        }
      },
      "51": {
        start: {
          line: 201,
          column: 16
        },
        end: {
          line: 209,
          column: 19
        }
      },
      "52": {
        start: {
          line: 213,
          column: 29
        },
        end: {
          line: 213,
          column: 79
        }
      },
      "53": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 227,
          column: 9
        }
      },
      "54": {
        start: {
          line: 215,
          column: 26
        },
        end: {
          line: 215,
          column: 41
        }
      },
      "55": {
        start: {
          line: 216,
          column: 12
        },
        end: {
          line: 226,
          column: 13
        }
      },
      "56": {
        start: {
          line: 217,
          column: 16
        },
        end: {
          line: 225,
          column: 19
        }
      },
      "57": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 239,
          column: 9
        }
      },
      "58": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 238,
          column: 15
        }
      },
      "59": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "60": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 249,
          column: 15
        }
      },
      "61": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 22
        }
      },
      "62": {
        start: {
          line: 262,
          column: 23
        },
        end: {
          line: 262,
          column: 25
        }
      },
      "63": {
        start: {
          line: 263,
          column: 27
        },
        end: {
          line: 263,
          column: 58
        }
      },
      "64": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 275,
          column: 9
        }
      },
      "65": {
        start: {
          line: 266,
          column: 12
        },
        end: {
          line: 274,
          column: 15
        }
      },
      "66": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 290,
          column: 9
        }
      },
      "67": {
        start: {
          line: 278,
          column: 31
        },
        end: {
          line: 278,
          column: 51
        }
      },
      "68": {
        start: {
          line: 279,
          column: 12
        },
        end: {
          line: 289,
          column: 13
        }
      },
      "69": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 288,
          column: 19
        }
      },
      "70": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 302,
          column: 9
        }
      },
      "71": {
        start: {
          line: 293,
          column: 12
        },
        end: {
          line: 301,
          column: 15
        }
      },
      "72": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 314,
          column: 9
        }
      },
      "73": {
        start: {
          line: 305,
          column: 12
        },
        end: {
          line: 313,
          column: 15
        }
      },
      "74": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 329,
          column: 9
        }
      },
      "75": {
        start: {
          line: 317,
          column: 12
        },
        end: {
          line: 328,
          column: 13
        }
      },
      "76": {
        start: {
          line: 319,
          column: 16
        },
        end: {
          line: 327,
          column: 19
        }
      },
      "77": {
        start: {
          line: 331,
          column: 8
        },
        end: {
          line: 344,
          column: 9
        }
      },
      "78": {
        start: {
          line: 332,
          column: 12
        },
        end: {
          line: 343,
          column: 13
        }
      },
      "79": {
        start: {
          line: 334,
          column: 16
        },
        end: {
          line: 342,
          column: 19
        }
      },
      "80": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 372,
          column: 9
        }
      },
      "81": {
        start: {
          line: 347,
          column: 12
        },
        end: {
          line: 357,
          column: 13
        }
      },
      "82": {
        start: {
          line: 348,
          column: 16
        },
        end: {
          line: 356,
          column: 19
        }
      },
      "83": {
        start: {
          line: 359,
          column: 12
        },
        end: {
          line: 371,
          column: 13
        }
      },
      "84": {
        start: {
          line: 359,
          column: 25
        },
        end: {
          line: 359,
          column: 26
        }
      },
      "85": {
        start: {
          line: 360,
          column: 16
        },
        end: {
          line: 370,
          column: 17
        }
      },
      "86": {
        start: {
          line: 361,
          column: 20
        },
        end: {
          line: 369,
          column: 23
        }
      },
      "87": {
        start: {
          line: 373,
          column: 8
        },
        end: {
          line: 373,
          column: 22
        }
      },
      "88": {
        start: {
          line: 384,
          column: 23
        },
        end: {
          line: 384,
          column: 25
        }
      },
      "89": {
        start: {
          line: 385,
          column: 27
        },
        end: {
          line: 385,
          column: 58
        }
      },
      "90": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 400,
          column: 9
        }
      },
      "91": {
        start: {
          line: 388,
          column: 35
        },
        end: {
          line: 388,
          column: 52
        }
      },
      "92": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 399,
          column: 13
        }
      },
      "93": {
        start: {
          line: 390,
          column: 16
        },
        end: {
          line: 398,
          column: 19
        }
      },
      "94": {
        start: {
          line: 402,
          column: 8
        },
        end: {
          line: 416,
          column: 9
        }
      },
      "95": {
        start: {
          line: 403,
          column: 36
        },
        end: {
          line: 403,
          column: 56
        }
      },
      "96": {
        start: {
          line: 404,
          column: 38
        },
        end: {
          line: 404,
          column: 82
        }
      },
      "97": {
        start: {
          line: 405,
          column: 12
        },
        end: {
          line: 415,
          column: 13
        }
      },
      "98": {
        start: {
          line: 406,
          column: 16
        },
        end: {
          line: 414,
          column: 19
        }
      },
      "99": {
        start: {
          line: 417,
          column: 8
        },
        end: {
          line: 417,
          column: 22
        }
      },
      "100": {
        start: {
          line: 428,
          column: 23
        },
        end: {
          line: 428,
          column: 25
        }
      },
      "101": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 444,
          column: 9
        }
      },
      "102": {
        start: {
          line: 430,
          column: 12
        },
        end: {
          line: 431,
          column: 25
        }
      },
      "103": {
        start: {
          line: 431,
          column: 16
        },
        end: {
          line: 431,
          column: 25
        }
      },
      "104": {
        start: {
          line: 432,
          column: 12
        },
        end: {
          line: 443,
          column: 13
        }
      },
      "105": {
        start: {
          line: 433,
          column: 35
        },
        end: {
          line: 433,
          column: 59
        }
      },
      "106": {
        start: {
          line: 434,
          column: 16
        },
        end: {
          line: 434,
          column: 43
        }
      },
      "107": {
        start: {
          line: 437,
          column: 16
        },
        end: {
          line: 442,
          column: 19
        }
      },
      "108": {
        start: {
          line: 445,
          column: 8
        },
        end: {
          line: 445,
          column: 22
        }
      },
      "109": {
        start: {
          line: 453,
          column: 8
        },
        end: {
          line: 453,
          column: 36
        }
      },
      "110": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 50
        }
      },
      "111": {
        start: {
          line: 462,
          column: 22
        },
        end: {
          line: 462,
          column: 80
        }
      },
      "112": {
        start: {
          line: 462,
          column: 57
        },
        end: {
          line: 462,
          column: 79
        }
      },
      "113": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 466,
          column: 9
        }
      },
      "114": {
        start: {
          line: 464,
          column: 12
        },
        end: {
          line: 464,
          column: 46
        }
      },
      "115": {
        start: {
          line: 465,
          column: 12
        },
        end: {
          line: 465,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        loc: {
          start: {
            line: 67,
            column: 29
          },
          end: {
            line: 75,
            column: 5
          }
        },
        line: 67
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        loc: {
          start: {
            line: 82,
            column: 30
          },
          end: {
            line: 152,
            column: 5
          }
        },
        line: 82
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 110,
            column: 56
          },
          end: {
            line: 110,
            column: 57
          }
        },
        loc: {
          start: {
            line: 110,
            column: 61
          },
          end: {
            line: 110,
            column: 79
          }
        },
        line: 110
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 111,
            column: 58
          },
          end: {
            line: 111,
            column: 59
          }
        },
        loc: {
          start: {
            line: 111,
            column: 63
          },
          end: {
            line: 111,
            column: 83
          }
        },
        line: 111
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 161,
            column: 5
          }
        },
        loc: {
          start: {
            line: 161,
            column: 45
          },
          end: {
            line: 184,
            column: 5
          }
        },
        line: 161
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 193,
            column: 4
          },
          end: {
            line: 193,
            column: 5
          }
        },
        loc: {
          start: {
            line: 193,
            column: 40
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 193
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 261,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        },
        loc: {
          start: {
            line: 261,
            column: 44
          },
          end: {
            line: 374,
            column: 5
          }
        },
        line: 261
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 383,
            column: 4
          },
          end: {
            line: 383,
            column: 5
          }
        },
        loc: {
          start: {
            line: 383,
            column: 42
          },
          end: {
            line: 418,
            column: 5
          }
        },
        line: 383
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 5
          }
        },
        loc: {
          start: {
            line: 427,
            column: 42
          },
          end: {
            line: 446,
            column: 5
          }
        },
        line: 427
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 452,
            column: 4
          },
          end: {
            line: 452,
            column: 5
          }
        },
        loc: {
          start: {
            line: 452,
            column: 24
          },
          end: {
            line: 455,
            column: 5
          }
        },
        line: 452
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 461,
            column: 4
          },
          end: {
            line: 461,
            column: 5
          }
        },
        loc: {
          start: {
            line: 461,
            column: 31
          },
          end: {
            line: 467,
            column: 5
          }
        },
        line: 461
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 462,
            column: 49
          },
          end: {
            line: 462,
            column: 50
          }
        },
        loc: {
          start: {
            line: 462,
            column: 57
          },
          end: {
            line: 462,
            column: 79
          }
        },
        line: 462
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 67,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 27
          }
        }],
        line: 67
      },
      "1": {
        loc: {
          start: {
            line: 69,
            column: 25
          },
          end: {
            line: 69,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 25
          },
          end: {
            line: 69,
            column: 43
          }
        }, {
          start: {
            line: 69,
            column: 47
          },
          end: {
            line: 69,
            column: 51
          }
        }],
        line: 69
      },
      "2": {
        loc: {
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 70,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 70,
            column: 47
          }
        }, {
          start: {
            line: 70,
            column: 51
          },
          end: {
            line: 70,
            column: 56
          }
        }],
        line: 70
      },
      "3": {
        loc: {
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 71,
            column: 45
          }
        }, {
          start: {
            line: 71,
            column: 49
          },
          end: {
            line: 71,
            column: 51
          }
        }],
        line: 71
      },
      "4": {
        loc: {
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 41
          }
        }, {
          start: {
            line: 72,
            column: 45
          },
          end: {
            line: 72,
            column: 49
          }
        }],
        line: 72
      },
      "5": {
        loc: {
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 117,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 117,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "6": {
        loc: {
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "7": {
        loc: {
          start: {
            line: 128,
            column: 43
          },
          end: {
            line: 128,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 128,
            column: 68
          },
          end: {
            line: 128,
            column: 81
          }
        }, {
          start: {
            line: 128,
            column: 84
          },
          end: {
            line: 128,
            column: 97
          }
        }],
        line: 128
      },
      "8": {
        loc: {
          start: {
            line: 129,
            column: 33
          },
          end: {
            line: 129,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 129,
            column: 33
          },
          end: {
            line: 129,
            column: 44
          }
        }, {
          start: {
            line: 129,
            column: 48
          },
          end: {
            line: 129,
            column: 60
          }
        }],
        line: 129
      },
      "9": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        }, {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 141,
            column: 9
          }
        }],
        line: 136
      },
      "10": {
        loc: {
          start: {
            line: 163,
            column: 27
          },
          end: {
            line: 163,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 27
          },
          end: {
            line: 163,
            column: 38
          }
        }, {
          start: {
            line: 163,
            column: 42
          },
          end: {
            line: 163,
            column: 58
          }
        }],
        line: 163
      },
      "11": {
        loc: {
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 12
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "12": {
        loc: {
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 171,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 16
          },
          end: {
            line: 171,
            column: 36
          }
        }, {
          start: {
            line: 171,
            column: 40
          },
          end: {
            line: 171,
            column: 69
          }
        }, {
          start: {
            line: 171,
            column: 73
          },
          end: {
            line: 171,
            column: 97
          }
        }],
        line: 171
      },
      "13": {
        loc: {
          start: {
            line: 195,
            column: 27
          },
          end: {
            line: 195,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 195,
            column: 27
          },
          end: {
            line: 195,
            column: 38
          }
        }, {
          start: {
            line: 195,
            column: 42
          },
          end: {
            line: 195,
            column: 58
          }
        }],
        line: 195
      },
      "14": {
        loc: {
          start: {
            line: 200,
            column: 12
          },
          end: {
            line: 210,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 12
          },
          end: {
            line: 210,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "15": {
        loc: {
          start: {
            line: 200,
            column: 16
          },
          end: {
            line: 200,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 16
          },
          end: {
            line: 200,
            column: 35
          }
        }, {
          start: {
            line: 200,
            column: 39
          },
          end: {
            line: 200,
            column: 64
          }
        }],
        line: 200
      },
      "16": {
        loc: {
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 226,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 226,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 216
      },
      "17": {
        loc: {
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 216,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 216,
            column: 35
          }
        }, {
          start: {
            line: 216,
            column: 39
          },
          end: {
            line: 216,
            column: 64
          }
        }],
        line: 216
      },
      "18": {
        loc: {
          start: {
            line: 229,
            column: 8
          },
          end: {
            line: 239,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 8
          },
          end: {
            line: 239,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 229
      },
      "19": {
        loc: {
          start: {
            line: 229,
            column: 12
          },
          end: {
            line: 229,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 12
          },
          end: {
            line: 229,
            column: 50
          }
        }, {
          start: {
            line: 229,
            column: 54
          },
          end: {
            line: 229,
            column: 94
          }
        }],
        line: 229
      },
      "20": {
        loc: {
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "21": {
        loc: {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 240,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 240,
            column: 39
          }
        }, {
          start: {
            line: 240,
            column: 43
          },
          end: {
            line: 240,
            column: 72
          }
        }],
        line: 240
      },
      "22": {
        loc: {
          start: {
            line: 263,
            column: 27
          },
          end: {
            line: 263,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 27
          },
          end: {
            line: 263,
            column: 38
          }
        }, {
          start: {
            line: 263,
            column: 42
          },
          end: {
            line: 263,
            column: 58
          }
        }],
        line: 263
      },
      "23": {
        loc: {
          start: {
            line: 265,
            column: 8
          },
          end: {
            line: 275,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 265,
            column: 8
          },
          end: {
            line: 275,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 265
      },
      "24": {
        loc: {
          start: {
            line: 265,
            column: 12
          },
          end: {
            line: 265,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 12
          },
          end: {
            line: 265,
            column: 23
          }
        }, {
          start: {
            line: 265,
            column: 27
          },
          end: {
            line: 265,
            column: 73
          }
        }],
        line: 265
      },
      "25": {
        loc: {
          start: {
            line: 277,
            column: 8
          },
          end: {
            line: 290,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 8
          },
          end: {
            line: 290,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "26": {
        loc: {
          start: {
            line: 279,
            column: 12
          },
          end: {
            line: 289,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 12
          },
          end: {
            line: 289,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "27": {
        loc: {
          start: {
            line: 279,
            column: 16
          },
          end: {
            line: 279,
            column: 110
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 279,
            column: 16
          },
          end: {
            line: 279,
            column: 61
          }
        }, {
          start: {
            line: 279,
            column: 65
          },
          end: {
            line: 279,
            column: 110
          }
        }],
        line: 279
      },
      "28": {
        loc: {
          start: {
            line: 292,
            column: 8
          },
          end: {
            line: 302,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 8
          },
          end: {
            line: 302,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 292
      },
      "29": {
        loc: {
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 292,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 292,
            column: 29
          }
        }, {
          start: {
            line: 292,
            column: 33
          },
          end: {
            line: 292,
            column: 78
          }
        }],
        line: 292
      },
      "30": {
        loc: {
          start: {
            line: 304,
            column: 8
          },
          end: {
            line: 314,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 304,
            column: 8
          },
          end: {
            line: 314,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 304
      },
      "31": {
        loc: {
          start: {
            line: 304,
            column: 12
          },
          end: {
            line: 304,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 304,
            column: 12
          },
          end: {
            line: 304,
            column: 37
          }
        }, {
          start: {
            line: 304,
            column: 41
          },
          end: {
            line: 304,
            column: 92
          }
        }],
        line: 304
      },
      "32": {
        loc: {
          start: {
            line: 316,
            column: 8
          },
          end: {
            line: 329,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 316,
            column: 8
          },
          end: {
            line: 329,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 316
      },
      "33": {
        loc: {
          start: {
            line: 317,
            column: 12
          },
          end: {
            line: 328,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 317,
            column: 12
          },
          end: {
            line: 328,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 317
      },
      "34": {
        loc: {
          start: {
            line: 317,
            column: 16
          },
          end: {
            line: 318,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 317,
            column: 16
          },
          end: {
            line: 317,
            column: 75
          }
        }, {
          start: {
            line: 318,
            column: 16
          },
          end: {
            line: 318,
            column: 75
          }
        }],
        line: 317
      },
      "35": {
        loc: {
          start: {
            line: 331,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 331,
            column: 8
          },
          end: {
            line: 344,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 331
      },
      "36": {
        loc: {
          start: {
            line: 332,
            column: 12
          },
          end: {
            line: 343,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 12
          },
          end: {
            line: 343,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "37": {
        loc: {
          start: {
            line: 332,
            column: 16
          },
          end: {
            line: 333,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 332,
            column: 16
          },
          end: {
            line: 332,
            column: 79
          }
        }, {
          start: {
            line: 333,
            column: 16
          },
          end: {
            line: 333,
            column: 79
          }
        }],
        line: 332
      },
      "38": {
        loc: {
          start: {
            line: 346,
            column: 8
          },
          end: {
            line: 372,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 8
          },
          end: {
            line: 372,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      },
      "39": {
        loc: {
          start: {
            line: 347,
            column: 12
          },
          end: {
            line: 357,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 347,
            column: 12
          },
          end: {
            line: 357,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 347
      },
      "40": {
        loc: {
          start: {
            line: 360,
            column: 16
          },
          end: {
            line: 370,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 16
          },
          end: {
            line: 370,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      },
      "41": {
        loc: {
          start: {
            line: 385,
            column: 27
          },
          end: {
            line: 385,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 385,
            column: 27
          },
          end: {
            line: 385,
            column: 38
          }
        }, {
          start: {
            line: 385,
            column: 42
          },
          end: {
            line: 385,
            column: 58
          }
        }],
        line: 385
      },
      "42": {
        loc: {
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 400,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 400,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "43": {
        loc: {
          start: {
            line: 387,
            column: 12
          },
          end: {
            line: 387,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 387,
            column: 12
          },
          end: {
            line: 387,
            column: 23
          }
        }, {
          start: {
            line: 387,
            column: 27
          },
          end: {
            line: 387,
            column: 44
          }
        }],
        line: 387
      },
      "44": {
        loc: {
          start: {
            line: 389,
            column: 12
          },
          end: {
            line: 399,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 12
          },
          end: {
            line: 399,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "45": {
        loc: {
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 416,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 416,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 402
      },
      "46": {
        loc: {
          start: {
            line: 402,
            column: 12
          },
          end: {
            line: 402,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 402,
            column: 12
          },
          end: {
            line: 402,
            column: 40
          }
        }, {
          start: {
            line: 402,
            column: 44
          },
          end: {
            line: 402,
            column: 57
          }
        }],
        line: 402
      },
      "47": {
        loc: {
          start: {
            line: 405,
            column: 12
          },
          end: {
            line: 415,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 405,
            column: 12
          },
          end: {
            line: 415,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 405
      },
      "48": {
        loc: {
          start: {
            line: 430,
            column: 12
          },
          end: {
            line: 431,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 430,
            column: 12
          },
          end: {
            line: 431,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 430
      },
      "49": {
        loc: {
          start: {
            line: 440,
            column: 59
          },
          end: {
            line: 440,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 440,
            column: 84
          },
          end: {
            line: 440,
            column: 97
          }
        }, {
          start: {
            line: 440,
            column: 100
          },
          end: {
            line: 440,
            column: 113
          }
        }],
        line: 440
      },
      "50": {
        loc: {
          start: {
            line: 441,
            column: 33
          },
          end: {
            line: 441,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 441,
            column: 33
          },
          end: {
            line: 441,
            column: 44
          }
        }, {
          start: {
            line: 441,
            column: 48
          },
          end: {
            line: 441,
            column: 64
          }
        }],
        line: 441
      },
      "51": {
        loc: {
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 466,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 8
          },
          end: {
            line: 466,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 463
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0]
    },
    inputSourceMap: {
      file: "/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts",
      mappings: "AAAA;;;;;;;;;;GAUG;AAIH,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,aAAa;AACb,MAAM,gBAAgB,GAAG;IACvB,gBAAgB;IAChB,UAAU,EAAE,iBAAiB;IAC7B,aAAa;IACb,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;IAChC,cAAc;IACd,mBAAmB,EAAE,WAAW;IAChC,aAAa;IACb,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;IAC/B,aAAa;IACb,yBAAyB,EAAE,EAAE;IAC7B,aAAa;IACb,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IACrC,aAAa;IACb,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;IACvC,aAAa;IACb,cAAc,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IAClC,aAAa;IACb,eAAe,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;CAC5B,CAAA;AAEV,cAAc;AACd,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE,UAAU;IACpB,WAAW,EAAE,aAAa;IAC1B,eAAe,EAAE,iBAAiB;IAClC,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;CACrB,CAAC,CAAA;AAEF,cAAc;AACd,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;IACnC,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;CACnB,CAAC,CAAA;AA8EF,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,OAAO,aAAa;IAChB,MAAM,CAA4B;IAClC,WAAW,CAAkB;IAErC;;;;OAIG;IACH,YAAY,SAA2B,EAAE;QACvC,IAAI,CAAC,MAAM,GAAG;YACZ,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI;YACvC,aAAa,EAAE,MAAM,CAAC,aAAa,IAAI,KAAK;YAC5C,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;YACvC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI;SACtC,CAAA;QAED,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;IAClD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,CAAC,SAAqB;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,QAAQ,GAAsB,EAAE,CAAA;QACtC,IAAI,WAAW,GAAG,CAAC,CAAA;QAEnB,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,CAAC,MAAM,SAAS,CAAC,CAAA;QAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;YAE7B,IAAI,CAAC;gBACH,OAAO;gBACP,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAE7D,SAAS;gBACT,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAEvD,SAAS;gBACT,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAE5D,QAAQ;gBACR,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAEhE,UAAU;gBACV,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBAE3D,SAAS;gBACT,MAAM,SAAS,GAAG;oBAChB,GAAG,WAAW;oBACd,GAAG,UAAU;oBACb,GAAG,WAAW;oBACd,GAAG,iBAAiB;oBACpB,GAAG,YAAY;iBAChB,CAAA;gBAED,UAAU;gBACV,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAA;gBAChE,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;gBAEpE,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;gBAC9B,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAA;gBAElC,eAAe;gBACf,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,WAAW,EAAE,CAAA;gBACf,CAAC;gBAED,eAAe;gBACf,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;oBAC5C,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,UAAU,QAAQ,CAAC,CAAA;oBAC5D,MAAK;gBACP,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,sBAAsB;oBAC5B,OAAO,EAAE,cAAc,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC/E,WAAW,EAAE,QAAQ,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;iBACzC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;QAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;QAElC,WAAW;QACX,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,WAAW,WAAW,IAAI,SAAS,CAAC,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA;QACvF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,WAAW,WAAW,IAAI,SAAS,CAAC,MAAM,WAAW,MAAM,CAAC,MAAM,SAAS,QAAQ,CAAC,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA;QACxI,CAAC;QAED,OAAO;YACL,MAAM;YACN,WAAW,EAAE,SAAS,CAAC,MAAM;YAC7B,YAAY,EAAE,WAAW;YACzB,YAAY,EAAE,SAAS,CAAC,MAAM,GAAG,WAAW;YAC5C,MAAM;YACN,QAAQ;YACR,eAAe,EAAE,cAAc;YAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;IACH,CAAC;IAED;;;;;;;OAOG;IACK,uBAAuB,CAAC,QAAkB,EAAE,KAAa;QAC/D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,kBAAkB;QAClB,MAAM,cAAc,GAAG;YACrB,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB;YAC3D,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM;YAC7D,qBAAqB,EAAE,iBAAiB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS;SAC5E,CAAA;QAED,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAuB,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC1H,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,wBAAwB;oBAC9B,OAAO,EAAE,WAAW,KAAK,EAAE;oBAC3B,KAAK;oBACL,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,YAAY;QAEZ,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,kBAAkB,CAAC,QAAkB,EAAE,KAAa;QAC1D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,UAAU;QACV,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;QACvE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAuB,CAAC,CAAA;YAC/C,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,MAAM,KAAK,WAAW;oBAC/B,KAAK;oBACL,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,OAAO,KAAK;oBACpB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,SAAS;QACT,MAAM,YAAY,GAAG,CAAC,iBAAiB,EAAE,eAAe,EAAE,YAAY,CAAC,CAAA;QACvE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAuB,CAAC,CAAA;YAC/C,IAAI,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,MAAM,KAAK,UAAU;oBAC9B,KAAK;oBACL,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,OAAO,KAAK;oBACpB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,eAAe,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACvF,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,yBAAyB;gBAClC,KAAK,EAAE,iBAAiB;gBACxB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE,OAAO,QAAQ,CAAC,eAAe;gBACvC,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,cAAc;gBACvB,KAAK,EAAE,MAAM;gBACb,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE,OAAO,QAAQ,CAAC,IAAI;gBAC5B,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,sBAAsB,CAAC,QAAkB,EAAE,KAAa;QAC9D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,SAAS;QACT,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,iCAAiC;gBAC1C,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,gBAAgB,CAAC,UAAU,CAAC,QAAQ,EAAE;gBAChD,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAA;YACvC,IAAI,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;gBACnG,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,qBAAqB;oBAC3B,OAAO,EAAE,WAAW,gBAAgB,CAAC,WAAW,CAAC,GAAG,IAAI,gBAAgB,CAAC,WAAW,CAAC,GAAG,KAAK;oBAC7F,KAAK,EAAE,MAAM;oBACb,QAAQ,EAAE,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,IAAI,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE;oBACnF,MAAM,EAAE,UAAU;oBAClB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO;QACP,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAe,CAAC,EAAE,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE,gBAAgB;gBAC1B,MAAM,EAAE,QAAQ,CAAC,QAAQ;gBACzB,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,gBAAgB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAuB,CAAC,EAAE,CAAC;YAC5F,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,0BAA0B;gBAChC,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,QAAQ,CAAC,gBAAgB;gBACjC,WAAW,EAAE,UAAU;aACxB,CAAC,CAAA;QACJ,CAAC;QAED,WAAW;QACX,IAAI,OAAO,QAAQ,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YAC/C,IAAI,QAAQ,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG;gBAC3D,QAAQ,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;gBAChE,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,uBAAuB;oBAC7B,OAAO,EAAE,WAAW,gBAAgB,CAAC,aAAa,CAAC,GAAG,IAAI,gBAAgB,CAAC,aAAa,CAAC,GAAG,KAAK;oBACjG,KAAK,EAAE,eAAe;oBACtB,QAAQ,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,IAAI,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAE;oBACvF,MAAM,EAAE,QAAQ,CAAC,aAAa;oBAC9B,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,WAAW;QACX,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;YACjD,IAAI,QAAQ,CAAC,eAAe,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG;gBAC/D,QAAQ,CAAC,eAAe,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;gBACpE,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,yBAAyB;oBAC/B,OAAO,EAAE,WAAW,gBAAgB,CAAC,eAAe,CAAC,GAAG,IAAI,gBAAgB,CAAC,eAAe,CAAC,GAAG,KAAK;oBACrG,KAAK,EAAE,iBAAiB;oBACxB,QAAQ,EAAE,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,IAAI,gBAAgB,CAAC,eAAe,CAAC,GAAG,EAAE;oBAC3F,MAAM,EAAE,QAAQ,CAAC,eAAe;oBAChC,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5C,IAAI,QAAQ,CAAC,eAAe,CAAC,MAAM,KAAK,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;gBACnF,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mCAAmC;oBACzC,OAAO,EAAE,aAAa,gBAAgB,CAAC,yBAAyB,EAAE;oBAClE,KAAK,EAAE,iBAAiB;oBACxB,QAAQ,EAAE,gBAAgB,CAAC,yBAAyB;oBACpD,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC,MAAM;oBACvC,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;YAED,cAAc;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzD,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACpD,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,iCAAiC;wBACvC,OAAO,EAAE,eAAe;wBACxB,KAAK,EAAE,mBAAmB,CAAC,GAAG;wBAC9B,QAAQ,EAAE,QAAQ;wBAClB,MAAM,EAAE,OAAO,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;wBAC1C,WAAW,EAAE,UAAU;qBACxB,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,oBAAoB,CAAC,QAAkB,EAAE,KAAa;QAC5D,MAAM,MAAM,GAAsB,EAAE,CAAA;QACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE,CAAA;QAElD,cAAc;QACd,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAA;YACxC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,YAAY;oBACrB,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,GAAG,cAAc,MAAM;oBACjC,MAAM,EAAE,QAAQ,CAAC,EAAE;oBACnB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,YAAY;QACZ,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAA;YAC5C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,mBAAmB,CAAC,eAAe,CAAA;YAEtE,IAAI,iBAAiB,KAAK,eAAe,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,8BAA8B;oBACpC,OAAO,EAAE,aAAa;oBACtB,KAAK,EAAE,qCAAqC;oBAC5C,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,iBAAiB;oBACzB,WAAW,EAAE,UAAU;iBACxB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;OAOG;IACK,oBAAoB,CAAC,QAAkB,EAAE,KAAa;QAC5D,MAAM,MAAM,GAAsB,EAAE,CAAA;QAEpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,OAAO;gBAAE,SAAQ;YAE3B,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;gBAC3C,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAA;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,mBAAmB;oBACzB,OAAO,EAAE,UAAU,IAAI,CAAC,IAAI,WAAW,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC/F,WAAW,EAAE,QAAQ,CAAC,EAAE,IAAI,SAAS,KAAK,EAAE;iBAC7C,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,IAAoB;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3B,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IAC3C,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,QAAgB;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QACxE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YACjC,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAA;QAC3C,CAAC;IACH,CAAC;CACF",
      names: [],
      sources: ["/home/<USER>/develop/workspace/namer-v6/server/core/data/DataValidator.ts"],
      sourcesContent: ["/**\n * \u6570\u636E\u9A8C\u8BC1\u5668\n * \n * \u63D0\u4F9B\u8BED\u7D20\u6570\u636E\u7684\u5B8C\u6574\u6027\u9A8C\u8BC1\u3001\u8D28\u91CF\u68C0\u67E5\u548C\u4E00\u81F4\u6027\u6821\u9A8C\u529F\u80FD\u3002\n * \u652F\u6301\u591A\u5C42\u6B21\u9A8C\u8BC1\u89C4\u5219\u548C\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u7B56\u7565\u3002\n * \n * @fileoverview \u6570\u636E\u9A8C\u8BC1\u6838\u5FC3\u6A21\u5757\n * @version 3.0.0\n * @since 2025-06-22\n * <AUTHOR> team\n */\n\nimport type { Morpheme, MorphemeCategory, CulturalContext } from '../../types/core'\n\n// ============================================================================\n// \u9A8C\u8BC1\u89C4\u5219\u914D\u7F6E\n// ============================================================================\n\n/** \u9A8C\u8BC1\u89C4\u5219\u914D\u7F6E */\nconst VALIDATION_RULES = {\n  /** ID\u683C\u5F0F\u6B63\u5219\u8868\u8FBE\u5F0F */\n  id_pattern: /^[a-z_]+_\\d{3}$/,\n  /** \u6587\u672C\u957F\u5EA6\u9650\u5236 */\n  text_length: { min: 1, max: 10 },\n  /** \u5B50\u5206\u7C7B\u547D\u540D\u89C4\u5219 */\n  subcategory_pattern: /^[a-z_]+$/,\n  /** \u6807\u7B7E\u6570\u91CF\u9650\u5236 */\n  tags_count: { min: 1, max: 10 },\n  /** \u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6 */\n  semantic_vector_dimension: 20,\n  /** \u8D28\u91CF\u8BC4\u5206\u8303\u56F4 */\n  quality_range: { min: 0.0, max: 1.0 },\n  /** \u4F7F\u7528\u9891\u7387\u8303\u56F4 */\n  frequency_range: { min: 0.0, max: 1.0 },\n  /** \u97F3\u8282\u6570\u91CF\u9650\u5236 */\n  syllable_count: { min: 1, max: 5 },\n  /** \u5B57\u7B26\u6570\u91CF\u9650\u5236 */\n  character_count: { min: 1, max: 10 }\n} as const\n\n/** \u6709\u6548\u7684\u8BED\u7D20\u7C7B\u522B */\nconst VALID_CATEGORIES = Object.values({\n  EMOTIONS: 'emotions',\n  PROFESSIONS: 'professions',\n  CHARACTERISTICS: 'characteristics',\n  OBJECTS: 'objects',\n  ACTIONS: 'actions',\n  CONCEPTS: 'concepts'\n})\n\n/** \u6709\u6548\u7684\u6587\u5316\u8BED\u5883 */\nconst VALID_CONTEXTS = Object.values({\n  ANCIENT: 'ancient',\n  MODERN: 'modern',\n  NEUTRAL: 'neutral'\n})\n\n// ============================================================================\n// \u63A5\u53E3\u5B9A\u4E49\n// ============================================================================\n\n/**\n * \u9A8C\u8BC1\u9519\u8BEF\u63A5\u53E3\n */\nexport interface ValidationError {\n  /** \u9519\u8BEF\u7C7B\u578B */\n  type: 'error' | 'warning' | 'info'\n  /** \u9519\u8BEF\u4EE3\u7801 */\n  code: string\n  /** \u9519\u8BEF\u6D88\u606F */\n  message: string\n  /** \u5B57\u6BB5\u8DEF\u5F84 */\n  field?: string\n  /** \u671F\u671B\u503C */\n  expected?: any\n  /** \u5B9E\u9645\u503C */\n  actual?: any\n  /** \u8BED\u7D20ID */\n  morpheme_id?: string\n}\n\n/**\n * \u9A8C\u8BC1\u7ED3\u679C\u63A5\u53E3\n */\nexport interface ValidationResult {\n  /** \u9A8C\u8BC1\u662F\u5426\u901A\u8FC7 */\n  passed: boolean\n  /** \u9A8C\u8BC1\u7684\u8BED\u7D20\u6570\u91CF */\n  total_count: number\n  /** \u901A\u8FC7\u9A8C\u8BC1\u7684\u8BED\u7D20\u6570\u91CF */\n  passed_count: number\n  /** \u5931\u8D25\u7684\u8BED\u7D20\u6570\u91CF */\n  failed_count: number\n  /** \u9519\u8BEF\u5217\u8868 */\n  errors: ValidationError[]\n  /** \u8B66\u544A\u5217\u8868 */\n  warnings: ValidationError[]\n  /** \u9A8C\u8BC1\u8017\u65F6 (\u6BEB\u79D2) */\n  validation_time: number\n  /** \u9A8C\u8BC1\u65F6\u95F4\u6233 */\n  timestamp: number\n}\n\n/**\n * \u9A8C\u8BC1\u914D\u7F6E\u63A5\u53E3\n */\nexport interface ValidationConfig {\n  /** \u662F\u5426\u542F\u7528\u4E25\u683C\u6A21\u5F0F */\n  strict_mode?: boolean\n  /** \u662F\u5426\u8DF3\u8FC7\u8B66\u544A */\n  skip_warnings?: boolean\n  /** \u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219 */\n  custom_rules?: ValidationRule[]\n  /** \u6700\u5927\u9519\u8BEF\u6570\u91CF (\u8D85\u8FC7\u5219\u505C\u6B62\u9A8C\u8BC1) */\n  max_errors?: number\n}\n\n/**\n * \u9A8C\u8BC1\u89C4\u5219\u63A5\u53E3\n */\nexport interface ValidationRule {\n  /** \u89C4\u5219\u540D\u79F0 */\n  name: string\n  /** \u89C4\u5219\u63CF\u8FF0 */\n  description: string\n  /** \u9A8C\u8BC1\u51FD\u6570 */\n  validator: (morpheme: Morpheme) => ValidationError[]\n  /** \u89C4\u5219\u4F18\u5148\u7EA7 */\n  priority: number\n  /** \u662F\u5426\u542F\u7528 */\n  enabled: boolean\n}\n\n// ============================================================================\n// \u6570\u636E\u9A8C\u8BC1\u5668\u7C7B\n// ============================================================================\n\n/**\n * \u6570\u636E\u9A8C\u8BC1\u5668\u7C7B\n * \n * \u63D0\u4F9B\u5168\u9762\u7684\u8BED\u7D20\u6570\u636E\u9A8C\u8BC1\u529F\u80FD\n */\nexport class DataValidator {\n  private config: Required<ValidationConfig>\n  private customRules: ValidationRule[]\n\n  /**\n   * \u6784\u9020\u51FD\u6570\n   * \n   * @param config \u9A8C\u8BC1\u914D\u7F6E\n   */\n  constructor(config: ValidationConfig = {}) {\n    this.config = {\n      strict_mode: config.strict_mode ?? true,\n      skip_warnings: config.skip_warnings ?? false,\n      custom_rules: config.custom_rules ?? [],\n      max_errors: config.max_errors ?? 1000\n    }\n    \n    this.customRules = [...this.config.custom_rules]\n  }\n\n  /**\n   * \u9A8C\u8BC1\u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * \n   * @param morphemes \u8BED\u7D20\u6570\u636E\u6570\u7EC4\n   * @returns \u9A8C\u8BC1\u7ED3\u679C\n   */\n  async validate(morphemes: Morpheme[]): Promise<ValidationResult> {\n    const startTime = Date.now()\n    const errors: ValidationError[] = []\n    const warnings: ValidationError[] = []\n    let passedCount = 0\n\n    console.log(`\uD83D\uDD0D \u5F00\u59CB\u9A8C\u8BC1 ${morphemes.length} \u4E2A\u8BED\u7D20...`)\n\n    for (let i = 0; i < morphemes.length; i++) {\n      const morpheme = morphemes[i]\n      \n      try {\n        // \u57FA\u7840\u9A8C\u8BC1\n        const basicErrors = this._validateBasicStructure(morpheme, i)\n        \n        // \u6570\u636E\u7C7B\u578B\u9A8C\u8BC1\n        const typeErrors = this._validateDataTypes(morpheme, i)\n        \n        // \u4E1A\u52A1\u903B\u8F91\u9A8C\u8BC1\n        const logicErrors = this._validateBusinessLogic(morpheme, i)\n        \n        // \u4E00\u81F4\u6027\u9A8C\u8BC1\n        const consistencyErrors = this._validateConsistency(morpheme, i)\n        \n        // \u81EA\u5B9A\u4E49\u89C4\u5219\u9A8C\u8BC1\n        const customErrors = this._validateCustomRules(morpheme, i)\n        \n        // \u6536\u96C6\u6240\u6709\u9519\u8BEF\n        const allErrors = [\n          ...basicErrors,\n          ...typeErrors,\n          ...logicErrors,\n          ...consistencyErrors,\n          ...customErrors\n        ]\n        \n        // \u5206\u7C7B\u9519\u8BEF\u548C\u8B66\u544A\n        const morphemeErrors = allErrors.filter(e => e.type === 'error')\n        const morphemeWarnings = allErrors.filter(e => e.type === 'warning')\n        \n        errors.push(...morphemeErrors)\n        warnings.push(...morphemeWarnings)\n        \n        // \u5982\u679C\u6CA1\u6709\u9519\u8BEF\uFF0C\u5219\u901A\u8FC7\u9A8C\u8BC1\n        if (morphemeErrors.length === 0) {\n          passedCount++\n        }\n        \n        // \u68C0\u67E5\u662F\u5426\u8D85\u8FC7\u6700\u5927\u9519\u8BEF\u6570\u91CF\n        if (errors.length >= this.config.max_errors) {\n          console.warn(`\u26A0\uFE0F \u9519\u8BEF\u6570\u91CF\u8D85\u8FC7\u9650\u5236 (${this.config.max_errors})\uFF0C\u505C\u6B62\u9A8C\u8BC1`)\n          break\n        }\n        \n      } catch (error) {\n        errors.push({\n          type: 'error',\n          code: 'VALIDATION_EXCEPTION',\n          message: `\u9A8C\u8BC1\u8FC7\u7A0B\u4E2D\u53D1\u751F\u5F02\u5E38: ${error instanceof Error ? error.message : String(error)}`,\n          morpheme_id: morpheme.id || `index_${i}`\n        })\n      }\n    }\n\n    const validationTime = Date.now() - startTime\n    const passed = errors.length === 0\n\n    // \u8F93\u51FA\u9A8C\u8BC1\u7ED3\u679C\u6458\u8981\n    if (passed) {\n      console.log(`\u2705 \u9A8C\u8BC1\u901A\u8FC7: ${passedCount}/${morphemes.length} \u4E2A\u8BED\u7D20, \u8017\u65F6 ${validationTime}ms`)\n    } else {\n      console.warn(`\u274C \u9A8C\u8BC1\u5931\u8D25: ${passedCount}/${morphemes.length} \u4E2A\u8BED\u7D20\u901A\u8FC7, ${errors.length} \u4E2A\u9519\u8BEF, ${warnings.length} \u4E2A\u8B66\u544A, \u8017\u65F6 ${validationTime}ms`)\n    }\n\n    return {\n      passed,\n      total_count: morphemes.length,\n      passed_count: passedCount,\n      failed_count: morphemes.length - passedCount,\n      errors,\n      warnings,\n      validation_time: validationTime,\n      timestamp: Date.now()\n    }\n  }\n\n  /**\n   * \u9A8C\u8BC1\u57FA\u7840\u7ED3\u6784\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateBasicStructure(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1\u5FC5\u9700\u5B57\u6BB5 (v3.0\u6807\u51C6)\n    const requiredFields = [\n      'id', 'text', 'category', 'subcategory', 'cultural_context',\n      'usage_frequency', 'quality_score', 'semantic_vector', 'tags',\n      'language_properties', 'quality_metrics', 'created_at', 'source', 'version'\n    ]\n\n    for (const field of requiredFields) {\n      if (!(field in morpheme) || morpheme[field as keyof Morpheme] === undefined || morpheme[field as keyof Morpheme] === null) {\n        errors.push({\n          type: 'error',\n          code: 'MISSING_REQUIRED_FIELD',\n          message: `\u7F3A\u5C11\u5FC5\u9700\u5B57\u6BB5: ${field}`,\n          field,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u6240\u6709\u5B57\u6BB5\u73B0\u5728\u90FD\u662F\u5FC5\u9700\u7684 (v3.0\u6807\u51C6)\n    // \u4E0D\u518D\u6709\u53EF\u9009\u5B57\u6BB5\u9A8C\u8BC1\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u6570\u636E\u7C7B\u578B\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateDataTypes(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1\u5B57\u7B26\u4E32\u5B57\u6BB5\n    const stringFields = ['id', 'text', 'subcategory', 'source', 'version']\n    for (const field of stringFields) {\n      const value = morpheme[field as keyof Morpheme]\n      if (value !== undefined && typeof value !== 'string') {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_DATA_TYPE',\n          message: `\u5B57\u6BB5 ${field} \u5FC5\u987B\u662F\u5B57\u7B26\u4E32\u7C7B\u578B`,\n          field,\n          expected: 'string',\n          actual: typeof value,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u6570\u5B57\u5B57\u6BB5\n    const numberFields = ['usage_frequency', 'quality_score', 'created_at']\n    for (const field of numberFields) {\n      const value = morpheme[field as keyof Morpheme]\n      if (value !== undefined && typeof value !== 'number') {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_DATA_TYPE',\n          message: `\u5B57\u6BB5 ${field} \u5FC5\u987B\u662F\u6570\u5B57\u7C7B\u578B`,\n          field,\n          expected: 'number',\n          actual: typeof value,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u6570\u7EC4\u5B57\u6BB5\n    if (morpheme.semantic_vector !== undefined && !Array.isArray(morpheme.semantic_vector)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_DATA_TYPE',\n        message: 'semantic_vector \u5FC5\u987B\u662F\u6570\u7EC4\u7C7B\u578B',\n        field: 'semantic_vector',\n        expected: 'array',\n        actual: typeof morpheme.semantic_vector,\n        morpheme_id: morphemeId\n      })\n    }\n\n    if (morpheme.tags !== undefined && !Array.isArray(morpheme.tags)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_DATA_TYPE',\n        message: 'tags \u5FC5\u987B\u662F\u6570\u7EC4\u7C7B\u578B',\n        field: 'tags',\n        expected: 'array',\n        actual: typeof morpheme.tags,\n        morpheme_id: morphemeId\n      })\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u4E1A\u52A1\u903B\u8F91\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateBusinessLogic(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1ID\u683C\u5F0F\n    if (morpheme.id && !VALIDATION_RULES.id_pattern.test(morpheme.id)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_ID_FORMAT',\n        message: `ID\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u5E94\u4E3A: {category}_{number}`,\n        field: 'id',\n        expected: VALIDATION_RULES.id_pattern.toString(),\n        actual: morpheme.id,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // \u9A8C\u8BC1\u6587\u672C\u957F\u5EA6\n    if (morpheme.text) {\n      const textLength = morpheme.text.length\n      if (textLength < VALIDATION_RULES.text_length.min || textLength > VALIDATION_RULES.text_length.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_TEXT_LENGTH',\n          message: `\u6587\u672C\u957F\u5EA6\u5FC5\u987B\u5728 ${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max} \u4E4B\u95F4`,\n          field: 'text',\n          expected: `${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max}`,\n          actual: textLength,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u7C7B\u522B\n    if (morpheme.category && !VALID_CATEGORIES.includes(morpheme.category as any)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_CATEGORY',\n        message: `\u65E0\u6548\u7684\u8BED\u7D20\u7C7B\u522B`,\n        field: 'category',\n        expected: VALID_CATEGORIES,\n        actual: morpheme.category,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // \u9A8C\u8BC1\u6587\u5316\u8BED\u5883\n    if (morpheme.cultural_context && !VALID_CONTEXTS.includes(morpheme.cultural_context as any)) {\n      errors.push({\n        type: 'error',\n        code: 'INVALID_CULTURAL_CONTEXT',\n        message: `\u65E0\u6548\u7684\u6587\u5316\u8BED\u5883`,\n        field: 'cultural_context',\n        expected: VALID_CONTEXTS,\n        actual: morpheme.cultural_context,\n        morpheme_id: morphemeId\n      })\n    }\n\n    // \u9A8C\u8BC1\u8D28\u91CF\u8BC4\u5206\u8303\u56F4\n    if (typeof morpheme.quality_score === 'number') {\n      if (morpheme.quality_score < VALIDATION_RULES.quality_range.min || \n          morpheme.quality_score > VALIDATION_RULES.quality_range.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_QUALITY_SCORE',\n          message: `\u8D28\u91CF\u8BC4\u5206\u5FC5\u987B\u5728 ${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max} \u4E4B\u95F4`,\n          field: 'quality_score',\n          expected: `${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max}`,\n          actual: morpheme.quality_score,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u4F7F\u7528\u9891\u7387\u8303\u56F4\n    if (typeof morpheme.usage_frequency === 'number') {\n      if (morpheme.usage_frequency < VALIDATION_RULES.frequency_range.min || \n          morpheme.usage_frequency > VALIDATION_RULES.frequency_range.max) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_USAGE_FREQUENCY',\n          message: `\u4F7F\u7528\u9891\u7387\u5FC5\u987B\u5728 ${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max} \u4E4B\u95F4`,\n          field: 'usage_frequency',\n          expected: `${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max}`,\n          actual: morpheme.usage_frequency,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u8BED\u4E49\u5411\u91CF\n    if (Array.isArray(morpheme.semantic_vector)) {\n      if (morpheme.semantic_vector.length !== VALIDATION_RULES.semantic_vector_dimension) {\n        errors.push({\n          type: 'error',\n          code: 'INVALID_SEMANTIC_VECTOR_DIMENSION',\n          message: `\u8BED\u4E49\u5411\u91CF\u7EF4\u5EA6\u5FC5\u987B\u4E3A ${VALIDATION_RULES.semantic_vector_dimension}`,\n          field: 'semantic_vector',\n          expected: VALIDATION_RULES.semantic_vector_dimension,\n          actual: morpheme.semantic_vector.length,\n          morpheme_id: morphemeId\n        })\n      }\n\n      // \u9A8C\u8BC1\u5411\u91CF\u5143\u7D20\u662F\u5426\u4E3A\u6570\u5B57\n      for (let i = 0; i < morpheme.semantic_vector.length; i++) {\n        if (typeof morpheme.semantic_vector[i] !== 'number') {\n          errors.push({\n            type: 'error',\n            code: 'INVALID_SEMANTIC_VECTOR_ELEMENT',\n            message: `\u8BED\u4E49\u5411\u91CF\u5143\u7D20\u5FC5\u987B\u662F\u6570\u5B57\u7C7B\u578B`,\n            field: `semantic_vector[${i}]`,\n            expected: 'number',\n            actual: typeof morpheme.semantic_vector[i],\n            morpheme_id: morphemeId\n          })\n        }\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u4E00\u81F4\u6027\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateConsistency(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n    const morphemeId = morpheme.id || `index_${index}`\n\n    // \u9A8C\u8BC1ID\u4E0E\u7C7B\u522B\u7684\u4E00\u81F4\u6027\n    if (morpheme.id && morpheme.category) {\n      const expectedPrefix = morpheme.category\n      if (!morpheme.id.startsWith(expectedPrefix)) {\n        errors.push({\n          type: 'warning',\n          code: 'INCONSISTENT_ID_CATEGORY',\n          message: `ID\u524D\u7F00\u4E0E\u7C7B\u522B\u4E0D\u4E00\u81F4`,\n          field: 'id',\n          expected: `${expectedPrefix}_xxx`,\n          actual: morpheme.id,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    // \u9A8C\u8BC1\u8BED\u8A00\u5C5E\u6027\u4E00\u81F4\u6027\n    if (morpheme.language_properties && morpheme.text) {\n      const actualCharCount = morpheme.text.length\n      const declaredCharCount = morpheme.language_properties.character_count\n\n      if (declaredCharCount !== actualCharCount) {\n        errors.push({\n          type: 'warning',\n          code: 'INCONSISTENT_CHARACTER_COUNT',\n          message: `\u58F0\u660E\u7684\u5B57\u7B26\u6570\u4E0E\u5B9E\u9645\u4E0D\u7B26`,\n          field: 'language_properties.character_count',\n          expected: actualCharCount,\n          actual: declaredCharCount,\n          morpheme_id: morphemeId\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u9A8C\u8BC1\u81EA\u5B9A\u4E49\u89C4\u5219\n   * \n   * @private\n   * @param morpheme \u8BED\u7D20\u6570\u636E\n   * @param index \u7D22\u5F15\n   * @returns \u9A8C\u8BC1\u9519\u8BEF\u5217\u8868\n   */\n  private _validateCustomRules(morpheme: Morpheme, index: number): ValidationError[] {\n    const errors: ValidationError[] = []\n\n    for (const rule of this.customRules) {\n      if (!rule.enabled) continue\n\n      try {\n        const ruleErrors = rule.validator(morpheme)\n        errors.push(...ruleErrors)\n      } catch (error) {\n        errors.push({\n          type: 'error',\n          code: 'CUSTOM_RULE_ERROR',\n          message: `\u81EA\u5B9A\u4E49\u89C4\u5219 '${rule.name}' \u6267\u884C\u5931\u8D25: ${error instanceof Error ? error.message : String(error)}`,\n          morpheme_id: morpheme.id || `index_${index}`\n        })\n      }\n    }\n\n    return errors\n  }\n\n  /**\n   * \u6DFB\u52A0\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219\n   * \n   * @param rule \u9A8C\u8BC1\u89C4\u5219\n   */\n  addCustomRule(rule: ValidationRule): void {\n    this.customRules.push(rule)\n    console.log(`\uD83D\uDCCB \u6DFB\u52A0\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219: ${rule.name}`)\n  }\n\n  /**\n   * \u79FB\u9664\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219\n   * \n   * @param ruleName \u89C4\u5219\u540D\u79F0\n   */\n  removeCustomRule(ruleName: string): void {\n    const index = this.customRules.findIndex(rule => rule.name === ruleName)\n    if (index !== -1) {\n      this.customRules.splice(index, 1)\n      console.log(`\uD83D\uDDD1\uFE0F \u79FB\u9664\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u89C4\u5219: ${ruleName}`)\n    }\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6557cebfead73145c700f457ab2efa1fc390653b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bk4eiopux = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bk4eiopux();
/**
 * 数据验证器
 *
 * 提供语素数据的完整性验证、质量检查和一致性校验功能。
 * 支持多层次验证规则和自定义验证策略。
 *
 * @fileoverview 数据验证核心模块
 * @version 3.0.0
 * @since 2025-06-22
 * <AUTHOR> team
 */
// ============================================================================
// 验证规则配置
// ============================================================================
/** 验证规则配置 */
const VALIDATION_RULES =
/* istanbul ignore next */
(cov_bk4eiopux().s[0]++, {
  /** ID格式正则表达式 */
  id_pattern: /^[a-z_]+_\d{3}$/,
  /** 文本长度限制 */
  text_length: {
    min: 1,
    max: 10
  },
  /** 子分类命名规则 */
  subcategory_pattern: /^[a-z_]+$/,
  /** 标签数量限制 */
  tags_count: {
    min: 1,
    max: 10
  },
  /** 语义向量维度 */
  semantic_vector_dimension: 20,
  /** 质量评分范围 */
  quality_range: {
    min: 0.0,
    max: 1.0
  },
  /** 使用频率范围 */
  frequency_range: {
    min: 0.0,
    max: 1.0
  },
  /** 音节数量限制 */
  syllable_count: {
    min: 1,
    max: 5
  },
  /** 字符数量限制 */
  character_count: {
    min: 1,
    max: 10
  }
});
/** 有效的语素类别 */
const VALID_CATEGORIES =
/* istanbul ignore next */
(cov_bk4eiopux().s[1]++, Object.values({
  EMOTIONS: 'emotions',
  PROFESSIONS: 'professions',
  CHARACTERISTICS: 'characteristics',
  OBJECTS: 'objects',
  ACTIONS: 'actions',
  CONCEPTS: 'concepts'
}));
/** 有效的文化语境 */
const VALID_CONTEXTS =
/* istanbul ignore next */
(cov_bk4eiopux().s[2]++, Object.values({
  ANCIENT: 'ancient',
  MODERN: 'modern',
  NEUTRAL: 'neutral'
}));
// ============================================================================
// 数据验证器类
// ============================================================================
/**
 * 数据验证器类
 *
 * 提供全面的语素数据验证功能
 */
export class DataValidator {
  config;
  customRules;
  /**
   * 构造函数
   *
   * @param config 验证配置
   */
  constructor(config =
  /* istanbul ignore next */
  (cov_bk4eiopux().b[0][0]++, {})) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[0]++;
    cov_bk4eiopux().s[3]++;
    this.config = {
      strict_mode:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[1][0]++, config.strict_mode) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[1][1]++, true),
      skip_warnings:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[2][0]++, config.skip_warnings) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[2][1]++, false),
      custom_rules:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[3][0]++, config.custom_rules) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[3][1]++, []),
      max_errors:
      /* istanbul ignore next */
      (cov_bk4eiopux().b[4][0]++, config.max_errors) ??
      /* istanbul ignore next */
      (cov_bk4eiopux().b[4][1]++, 1000)
    };
    /* istanbul ignore next */
    cov_bk4eiopux().s[4]++;
    this.customRules = [...this.config.custom_rules];
  }
  /**
   * 验证语素数据数组
   *
   * @param morphemes 语素数据数组
   * @returns 验证结果
   */
  async validate(morphemes) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[5]++, Date.now());
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[6]++, []);
    const warnings =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[7]++, []);
    let passedCount =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[8]++, 0);
    /* istanbul ignore next */
    cov_bk4eiopux().s[9]++;
    console.log(`🔍 开始验证 ${morphemes.length} 个语素...`);
    /* istanbul ignore next */
    cov_bk4eiopux().s[10]++;
    for (let i =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[11]++, 0); i < morphemes.length; i++) {
      const morpheme =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[12]++, morphemes[i]);
      /* istanbul ignore next */
      cov_bk4eiopux().s[13]++;
      try {
        // 基础验证
        const basicErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[14]++, this._validateBasicStructure(morpheme, i));
        // 数据类型验证
        const typeErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[15]++, this._validateDataTypes(morpheme, i));
        // 业务逻辑验证
        const logicErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[16]++, this._validateBusinessLogic(morpheme, i));
        // 一致性验证
        const consistencyErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[17]++, this._validateConsistency(morpheme, i));
        // 自定义规则验证
        const customErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[18]++, this._validateCustomRules(morpheme, i));
        // 收集所有错误
        const allErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[19]++, [...basicErrors, ...typeErrors, ...logicErrors, ...consistencyErrors, ...customErrors]);
        // 分类错误和警告
        const morphemeErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[20]++, allErrors.filter(e => {
          /* istanbul ignore next */
          cov_bk4eiopux().f[2]++;
          cov_bk4eiopux().s[21]++;
          return e.type === 'error';
        }));
        const morphemeWarnings =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[22]++, allErrors.filter(e => {
          /* istanbul ignore next */
          cov_bk4eiopux().f[3]++;
          cov_bk4eiopux().s[23]++;
          return e.type === 'warning';
        }));
        /* istanbul ignore next */
        cov_bk4eiopux().s[24]++;
        errors.push(...morphemeErrors);
        /* istanbul ignore next */
        cov_bk4eiopux().s[25]++;
        warnings.push(...morphemeWarnings);
        // 如果没有错误，则通过验证
        /* istanbul ignore next */
        cov_bk4eiopux().s[26]++;
        if (morphemeErrors.length === 0) {
          /* istanbul ignore next */
          cov_bk4eiopux().b[5][0]++;
          cov_bk4eiopux().s[27]++;
          passedCount++;
        } else
        /* istanbul ignore next */
        {
          cov_bk4eiopux().b[5][1]++;
        }
        // 检查是否超过最大错误数量
        cov_bk4eiopux().s[28]++;
        if (errors.length >= this.config.max_errors) {
          /* istanbul ignore next */
          cov_bk4eiopux().b[6][0]++;
          cov_bk4eiopux().s[29]++;
          console.warn(`⚠️ 错误数量超过限制 (${this.config.max_errors})，停止验证`);
          /* istanbul ignore next */
          cov_bk4eiopux().s[30]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_bk4eiopux().b[6][1]++;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_bk4eiopux().s[31]++;
        errors.push({
          type: 'error',
          code: 'VALIDATION_EXCEPTION',
          message: `验证过程中发生异常: ${error instanceof Error ?
          /* istanbul ignore next */
          (cov_bk4eiopux().b[7][0]++, error.message) :
          /* istanbul ignore next */
          (cov_bk4eiopux().b[7][1]++, String(error))}`,
          morpheme_id:
          /* istanbul ignore next */
          (cov_bk4eiopux().b[8][0]++, morpheme.id) ||
          /* istanbul ignore next */
          (cov_bk4eiopux().b[8][1]++, `index_${i}`)
        });
      }
    }
    const validationTime =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[32]++, Date.now() - startTime);
    const passed =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[33]++, errors.length === 0);
    // 输出验证结果摘要
    /* istanbul ignore next */
    cov_bk4eiopux().s[34]++;
    if (passed) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[9][0]++;
      cov_bk4eiopux().s[35]++;
      console.log(`✅ 验证通过: ${passedCount}/${morphemes.length} 个语素, 耗时 ${validationTime}ms`);
    } else {
      /* istanbul ignore next */
      cov_bk4eiopux().b[9][1]++;
      cov_bk4eiopux().s[36]++;
      console.warn(`❌ 验证失败: ${passedCount}/${morphemes.length} 个语素通过, ${errors.length} 个错误, ${warnings.length} 个警告, 耗时 ${validationTime}ms`);
    }
    /* istanbul ignore next */
    cov_bk4eiopux().s[37]++;
    return {
      passed,
      total_count: morphemes.length,
      passed_count: passedCount,
      failed_count: morphemes.length - passedCount,
      errors,
      warnings,
      validation_time: validationTime,
      timestamp: Date.now()
    };
  }
  /**
   * 验证基础结构
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateBasicStructure(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[4]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[38]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[39]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[10][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[10][1]++, `index_${index}`));
    // 验证必需字段 (v3.0标准)
    const requiredFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[40]++, ['id', 'text', 'category', 'subcategory', 'cultural_context', 'usage_frequency', 'quality_score', 'semantic_vector', 'tags', 'language_properties', 'quality_metrics', 'created_at', 'source', 'version']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[41]++;
    for (const field of requiredFields) {
      /* istanbul ignore next */
      cov_bk4eiopux().s[42]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[12][0]++, !(field in morpheme)) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[12][1]++, morpheme[field] === undefined) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[12][2]++, morpheme[field] === null)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[11][0]++;
        cov_bk4eiopux().s[43]++;
        errors.push({
          type: 'error',
          code: 'MISSING_REQUIRED_FIELD',
          message: `缺少必需字段: ${field}`,
          field,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[11][1]++;
      }
    }
    // 所有字段现在都是必需的 (v3.0标准)
    // 不再有可选字段验证
    /* istanbul ignore next */
    cov_bk4eiopux().s[44]++;
    return errors;
  }
  /**
   * 验证数据类型
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateDataTypes(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[5]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[45]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[46]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[13][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[13][1]++, `index_${index}`));
    // 验证字符串字段
    const stringFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[47]++, ['id', 'text', 'subcategory', 'source', 'version']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[48]++;
    for (const field of stringFields) {
      const value =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[49]++, morpheme[field]);
      /* istanbul ignore next */
      cov_bk4eiopux().s[50]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[15][0]++, value !== undefined) &&
      /* istanbul ignore next */
      (cov_bk4eiopux().b[15][1]++, typeof value !== 'string')) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[14][0]++;
        cov_bk4eiopux().s[51]++;
        errors.push({
          type: 'error',
          code: 'INVALID_DATA_TYPE',
          message: `字段 ${field} 必须是字符串类型`,
          field,
          expected: 'string',
          actual: typeof value,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[14][1]++;
      }
    }
    // 验证数字字段
    const numberFields =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[52]++, ['usage_frequency', 'quality_score', 'created_at']);
    /* istanbul ignore next */
    cov_bk4eiopux().s[53]++;
    for (const field of numberFields) {
      const value =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[54]++, morpheme[field]);
      /* istanbul ignore next */
      cov_bk4eiopux().s[55]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[17][0]++, value !== undefined) &&
      /* istanbul ignore next */
      (cov_bk4eiopux().b[17][1]++, typeof value !== 'number')) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[16][0]++;
        cov_bk4eiopux().s[56]++;
        errors.push({
          type: 'error',
          code: 'INVALID_DATA_TYPE',
          message: `字段 ${field} 必须是数字类型`,
          field,
          expected: 'number',
          actual: typeof value,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[16][1]++;
      }
    }
    // 验证数组字段
    /* istanbul ignore next */
    cov_bk4eiopux().s[57]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[19][0]++, morpheme.semantic_vector !== undefined) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[19][1]++, !Array.isArray(morpheme.semantic_vector))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[18][0]++;
      cov_bk4eiopux().s[58]++;
      errors.push({
        type: 'error',
        code: 'INVALID_DATA_TYPE',
        message: 'semantic_vector 必须是数组类型',
        field: 'semantic_vector',
        expected: 'array',
        actual: typeof morpheme.semantic_vector,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[18][1]++;
    }
    cov_bk4eiopux().s[59]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[21][0]++, morpheme.tags !== undefined) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[21][1]++, !Array.isArray(morpheme.tags))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[20][0]++;
      cov_bk4eiopux().s[60]++;
      errors.push({
        type: 'error',
        code: 'INVALID_DATA_TYPE',
        message: 'tags 必须是数组类型',
        field: 'tags',
        expected: 'array',
        actual: typeof morpheme.tags,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[20][1]++;
    }
    cov_bk4eiopux().s[61]++;
    return errors;
  }
  /**
   * 验证业务逻辑
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateBusinessLogic(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[6]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[62]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[63]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[22][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[22][1]++, `index_${index}`));
    // 验证ID格式
    /* istanbul ignore next */
    cov_bk4eiopux().s[64]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[24][0]++, morpheme.id) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[24][1]++, !VALIDATION_RULES.id_pattern.test(morpheme.id))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[23][0]++;
      cov_bk4eiopux().s[65]++;
      errors.push({
        type: 'error',
        code: 'INVALID_ID_FORMAT',
        message: `ID格式不正确，应为: {category}_{number}`,
        field: 'id',
        expected: VALIDATION_RULES.id_pattern.toString(),
        actual: morpheme.id,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[23][1]++;
    }
    // 验证文本长度
    cov_bk4eiopux().s[66]++;
    if (morpheme.text) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[25][0]++;
      const textLength =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[67]++, morpheme.text.length);
      /* istanbul ignore next */
      cov_bk4eiopux().s[68]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[27][0]++, textLength < VALIDATION_RULES.text_length.min) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[27][1]++, textLength > VALIDATION_RULES.text_length.max)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[26][0]++;
        cov_bk4eiopux().s[69]++;
        errors.push({
          type: 'error',
          code: 'INVALID_TEXT_LENGTH',
          message: `文本长度必须在 ${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max} 之间`,
          field: 'text',
          expected: `${VALIDATION_RULES.text_length.min}-${VALIDATION_RULES.text_length.max}`,
          actual: textLength,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[26][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[25][1]++;
    }
    // 验证类别
    cov_bk4eiopux().s[70]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[29][0]++, morpheme.category) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[29][1]++, !VALID_CATEGORIES.includes(morpheme.category))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[28][0]++;
      cov_bk4eiopux().s[71]++;
      errors.push({
        type: 'error',
        code: 'INVALID_CATEGORY',
        message: `无效的语素类别`,
        field: 'category',
        expected: VALID_CATEGORIES,
        actual: morpheme.category,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[28][1]++;
    }
    // 验证文化语境
    cov_bk4eiopux().s[72]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[31][0]++, morpheme.cultural_context) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[31][1]++, !VALID_CONTEXTS.includes(morpheme.cultural_context))) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[30][0]++;
      cov_bk4eiopux().s[73]++;
      errors.push({
        type: 'error',
        code: 'INVALID_CULTURAL_CONTEXT',
        message: `无效的文化语境`,
        field: 'cultural_context',
        expected: VALID_CONTEXTS,
        actual: morpheme.cultural_context,
        morpheme_id: morphemeId
      });
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[30][1]++;
    }
    // 验证质量评分范围
    cov_bk4eiopux().s[74]++;
    if (typeof morpheme.quality_score === 'number') {
      /* istanbul ignore next */
      cov_bk4eiopux().b[32][0]++;
      cov_bk4eiopux().s[75]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[34][0]++, morpheme.quality_score < VALIDATION_RULES.quality_range.min) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[34][1]++, morpheme.quality_score > VALIDATION_RULES.quality_range.max)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[33][0]++;
        cov_bk4eiopux().s[76]++;
        errors.push({
          type: 'error',
          code: 'INVALID_QUALITY_SCORE',
          message: `质量评分必须在 ${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max} 之间`,
          field: 'quality_score',
          expected: `${VALIDATION_RULES.quality_range.min}-${VALIDATION_RULES.quality_range.max}`,
          actual: morpheme.quality_score,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[33][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[32][1]++;
    }
    // 验证使用频率范围
    cov_bk4eiopux().s[77]++;
    if (typeof morpheme.usage_frequency === 'number') {
      /* istanbul ignore next */
      cov_bk4eiopux().b[35][0]++;
      cov_bk4eiopux().s[78]++;
      if (
      /* istanbul ignore next */
      (cov_bk4eiopux().b[37][0]++, morpheme.usage_frequency < VALIDATION_RULES.frequency_range.min) ||
      /* istanbul ignore next */
      (cov_bk4eiopux().b[37][1]++, morpheme.usage_frequency > VALIDATION_RULES.frequency_range.max)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[36][0]++;
        cov_bk4eiopux().s[79]++;
        errors.push({
          type: 'error',
          code: 'INVALID_USAGE_FREQUENCY',
          message: `使用频率必须在 ${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max} 之间`,
          field: 'usage_frequency',
          expected: `${VALIDATION_RULES.frequency_range.min}-${VALIDATION_RULES.frequency_range.max}`,
          actual: morpheme.usage_frequency,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[36][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[35][1]++;
    }
    // 验证语义向量
    cov_bk4eiopux().s[80]++;
    if (Array.isArray(morpheme.semantic_vector)) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[38][0]++;
      cov_bk4eiopux().s[81]++;
      if (morpheme.semantic_vector.length !== VALIDATION_RULES.semantic_vector_dimension) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[39][0]++;
        cov_bk4eiopux().s[82]++;
        errors.push({
          type: 'error',
          code: 'INVALID_SEMANTIC_VECTOR_DIMENSION',
          message: `语义向量维度必须为 ${VALIDATION_RULES.semantic_vector_dimension}`,
          field: 'semantic_vector',
          expected: VALIDATION_RULES.semantic_vector_dimension,
          actual: morpheme.semantic_vector.length,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[39][1]++;
      }
      // 验证向量元素是否为数字
      cov_bk4eiopux().s[83]++;
      for (let i =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[84]++, 0); i < morpheme.semantic_vector.length; i++) {
        /* istanbul ignore next */
        cov_bk4eiopux().s[85]++;
        if (typeof morpheme.semantic_vector[i] !== 'number') {
          /* istanbul ignore next */
          cov_bk4eiopux().b[40][0]++;
          cov_bk4eiopux().s[86]++;
          errors.push({
            type: 'error',
            code: 'INVALID_SEMANTIC_VECTOR_ELEMENT',
            message: `语义向量元素必须是数字类型`,
            field: `semantic_vector[${i}]`,
            expected: 'number',
            actual: typeof morpheme.semantic_vector[i],
            morpheme_id: morphemeId
          });
        } else
        /* istanbul ignore next */
        {
          cov_bk4eiopux().b[40][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[38][1]++;
    }
    cov_bk4eiopux().s[87]++;
    return errors;
  }
  /**
   * 验证一致性
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateConsistency(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[7]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[88]++, []);
    const morphemeId =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[89]++,
    /* istanbul ignore next */
    (cov_bk4eiopux().b[41][0]++, morpheme.id) ||
    /* istanbul ignore next */
    (cov_bk4eiopux().b[41][1]++, `index_${index}`));
    // 验证ID与类别的一致性
    /* istanbul ignore next */
    cov_bk4eiopux().s[90]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[43][0]++, morpheme.id) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[43][1]++, morpheme.category)) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[42][0]++;
      const expectedPrefix =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[91]++, morpheme.category);
      /* istanbul ignore next */
      cov_bk4eiopux().s[92]++;
      if (!morpheme.id.startsWith(expectedPrefix)) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[44][0]++;
        cov_bk4eiopux().s[93]++;
        errors.push({
          type: 'warning',
          code: 'INCONSISTENT_ID_CATEGORY',
          message: `ID前缀与类别不一致`,
          field: 'id',
          expected: `${expectedPrefix}_xxx`,
          actual: morpheme.id,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[44][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[42][1]++;
    }
    // 验证语言属性一致性
    cov_bk4eiopux().s[94]++;
    if (
    /* istanbul ignore next */
    (cov_bk4eiopux().b[46][0]++, morpheme.language_properties) &&
    /* istanbul ignore next */
    (cov_bk4eiopux().b[46][1]++, morpheme.text)) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[45][0]++;
      const actualCharCount =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[95]++, morpheme.text.length);
      const declaredCharCount =
      /* istanbul ignore next */
      (cov_bk4eiopux().s[96]++, morpheme.language_properties.character_count);
      /* istanbul ignore next */
      cov_bk4eiopux().s[97]++;
      if (declaredCharCount !== actualCharCount) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[47][0]++;
        cov_bk4eiopux().s[98]++;
        errors.push({
          type: 'warning',
          code: 'INCONSISTENT_CHARACTER_COUNT',
          message: `声明的字符数与实际不符`,
          field: 'language_properties.character_count',
          expected: actualCharCount,
          actual: declaredCharCount,
          morpheme_id: morphemeId
        });
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[47][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[45][1]++;
    }
    cov_bk4eiopux().s[99]++;
    return errors;
  }
  /**
   * 验证自定义规则
   *
   * @private
   * @param morpheme 语素数据
   * @param index 索引
   * @returns 验证错误列表
   */
  _validateCustomRules(morpheme, index) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[8]++;
    const errors =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[100]++, []);
    /* istanbul ignore next */
    cov_bk4eiopux().s[101]++;
    for (const rule of this.customRules) {
      /* istanbul ignore next */
      cov_bk4eiopux().s[102]++;
      if (!rule.enabled) {
        /* istanbul ignore next */
        cov_bk4eiopux().b[48][0]++;
        cov_bk4eiopux().s[103]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_bk4eiopux().b[48][1]++;
      }
      cov_bk4eiopux().s[104]++;
      try {
        const ruleErrors =
        /* istanbul ignore next */
        (cov_bk4eiopux().s[105]++, rule.validator(morpheme));
        /* istanbul ignore next */
        cov_bk4eiopux().s[106]++;
        errors.push(...ruleErrors);
      } catch (error) {
        /* istanbul ignore next */
        cov_bk4eiopux().s[107]++;
        errors.push({
          type: 'error',
          code: 'CUSTOM_RULE_ERROR',
          message: `自定义规则 '${rule.name}' 执行失败: ${error instanceof Error ?
          /* istanbul ignore next */
          (cov_bk4eiopux().b[49][0]++, error.message) :
          /* istanbul ignore next */
          (cov_bk4eiopux().b[49][1]++, String(error))}`,
          morpheme_id:
          /* istanbul ignore next */
          (cov_bk4eiopux().b[50][0]++, morpheme.id) ||
          /* istanbul ignore next */
          (cov_bk4eiopux().b[50][1]++, `index_${index}`)
        });
      }
    }
    /* istanbul ignore next */
    cov_bk4eiopux().s[108]++;
    return errors;
  }
  /**
   * 添加自定义验证规则
   *
   * @param rule 验证规则
   */
  addCustomRule(rule) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[9]++;
    cov_bk4eiopux().s[109]++;
    this.customRules.push(rule);
    /* istanbul ignore next */
    cov_bk4eiopux().s[110]++;
    console.log(`📋 添加自定义验证规则: ${rule.name}`);
  }
  /**
   * 移除自定义验证规则
   *
   * @param ruleName 规则名称
   */
  removeCustomRule(ruleName) {
    /* istanbul ignore next */
    cov_bk4eiopux().f[10]++;
    const index =
    /* istanbul ignore next */
    (cov_bk4eiopux().s[111]++, this.customRules.findIndex(rule => {
      /* istanbul ignore next */
      cov_bk4eiopux().f[11]++;
      cov_bk4eiopux().s[112]++;
      return rule.name === ruleName;
    }));
    /* istanbul ignore next */
    cov_bk4eiopux().s[113]++;
    if (index !== -1) {
      /* istanbul ignore next */
      cov_bk4eiopux().b[51][0]++;
      cov_bk4eiopux().s[114]++;
      this.customRules.splice(index, 1);
      /* istanbul ignore next */
      cov_bk4eiopux().s[115]++;
      console.log(`🗑️ 移除自定义验证规则: ${ruleName}`);
    } else
    /* istanbul ignore next */
    {
      cov_bk4eiopux().b[51][1]++;
    }
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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