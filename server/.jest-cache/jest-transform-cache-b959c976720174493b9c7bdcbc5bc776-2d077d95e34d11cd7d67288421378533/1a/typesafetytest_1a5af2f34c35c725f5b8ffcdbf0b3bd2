89d2e4e2ff94da0e64cb87e675ce9877
"use strict";
/**
 * TypeScript 类型安全测试
 *
 * 确保TypeScript编译无错误，验证类型定义的正确性
 */
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const core_js_1 = require("../../types/core.js");
const multilingual_js_1 = require("../../types/multilingual.js");
(0, globals_1.describe)('TypeScript 类型安全测试', () => {
    (0, globals_1.describe)('核心类型定义', () => {
        (0, globals_1.it)('应该正确定义 Morpheme 类型', () => {
            const morpheme = {
                id: 'test_001',
                text: '测试',
                category: 'emotions',
                subcategory: 'positive',
                cultural_context: core_js_1.CulturalContext.MODERN,
                usage_frequency: 0.8,
                quality_score: 0.9,
                semantic_vector: [0.1, 0.2, 0.3],
                tags: ['test', 'example'],
                language_properties: {
                    syllable_count: 2,
                    character_count: 2,
                    phonetic_features: ['tone2', 'tone4'],
                    morphological_type: 'compound',
                    pronunciation: 'cè shì'
                },
                quality_metrics: {
                    naturalness: 0.9,
                    frequency: 0.8,
                    acceptability: 0.9,
                    aesthetic_appeal: 0.7
                },
                created_at: Date.now(),
                source: 'test',
                version: '2.0.0'
            };
            (0, globals_1.expect)(morpheme.id).toBe('test_001');
            (0, globals_1.expect)(morpheme.text).toBe('测试');
            (0, globals_1.expect)(morpheme.quality_score).toBe(0.9);
        });
        (0, globals_1.it)('应该正确定义 GenerationContext 类型', () => {
            const context = {
                cultural_preference: core_js_1.CulturalContext.MODERN,
                style_preference: 'elegant',
                creativity_level: 0.8,
                quality_threshold: 0.7,
                patterns: ['形容词+名词'],
                exclude_patterns: ['小{adjective}']
            };
            (0, globals_1.expect)(context.style_preference).toBe('elegant');
            (0, globals_1.expect)(context.quality_threshold).toBe(0.7);
            (0, globals_1.expect)(context.creativity_level).toBe(0.8);
        });
        (0, globals_1.it)('应该正确定义 GeneratedUsername 类型', () => {
            const username = {
                text: '优雅设计师',
                pattern: '形容词+名词',
                quality_score: {
                    overall: 0.85,
                    dimensions: {
                        creativity: 0.9,
                        memorability: 0.8,
                        cultural_fit: 0.9,
                        uniqueness: 0.7,
                        pronunciation: 0.8,
                        semantic_coherence: 0.8,
                        aesthetic_appeal: 0.8,
                        practical_usability: 0.8
                    },
                    confidence: 0.9,
                    evaluation_time: 150,
                    algorithm_version: 'v2.0',
                    issues: [],
                    suggestions: [],
                    timestamp: Date.now()
                },
                explanation: '结合了优雅的特质和设计师的职业特色',
                components: [],
                metadata: {
                    cultural_fit: 0.8,
                    creativity: 0.7,
                    memorability: 0.9,
                    uniqueness: 0.7,
                    generation_time: 150
                }
            };
            (0, globals_1.expect)(username.text).toBe('优雅设计师');
            (0, globals_1.expect)(username.quality_score.overall).toBe(0.85);
            (0, globals_1.expect)(username.metadata.generation_time).toBe(150);
        });
    });
    (0, globals_1.describe)('多语种类型定义', () => {
        (0, globals_1.it)('应该正确定义 LanguageCode 枚举', () => {
            const languages = [
                multilingual_js_1.LanguageCode.ZH_CN,
                multilingual_js_1.LanguageCode.EN_US,
                multilingual_js_1.LanguageCode.JA_JP,
                multilingual_js_1.LanguageCode.KO_KR,
                multilingual_js_1.LanguageCode.ES_ES,
                multilingual_js_1.LanguageCode.FR_FR,
                multilingual_js_1.LanguageCode.DE_DE,
                multilingual_js_1.LanguageCode.AR_SA
            ];
            (0, globals_1.expect)(languages).toHaveLength(8);
            (0, globals_1.expect)(languages).toContain(multilingual_js_1.LanguageCode.ZH_CN);
            (0, globals_1.expect)(languages).toContain(multilingual_js_1.LanguageCode.EN_US);
        });
        (0, globals_1.it)('应该正确定义 UniversalConcept 类型', () => {
            const concept = {
                concept_id: 'concept_001',
                semantic_vector: {
                    vector: new Array(512).fill(0.1),
                    model_version: 'mBERT-v1.0',
                    confidence: 0.95,
                    updated_at: '2025-06-24T00:00:00Z'
                },
                concept_category: multilingual_js_1.ConceptCategory.EMOTIONS,
                abstraction_level: 0.6,
                cultural_neutrality: 0.8,
                cross_lingual_stability: 0.9,
                cognitive_load: 0.3,
                memorability_score: 0.8,
                emotional_valence: 0.7,
                cognitive_attributes: {
                    memorability: 0.8,
                    cognitive_load: 0.3,
                    emotional_valence: 0.7
                },
                related_concepts: ['joy_001', 'satisfaction_001'],
                hierarchical_children: [],
                created_at: '2025-06-24T00:00:00Z',
                updated_at: '2025-06-24T00:00:00Z',
                version: '3.0.0'
            };
            (0, globals_1.expect)(concept.concept_id).toBe('concept_001');
            (0, globals_1.expect)(concept.semantic_vector.vector).toHaveLength(512);
            (0, globals_1.expect)(concept.concept_category).toBe('emotion');
        });
        (0, globals_1.it)('应该正确定义 LanguageSpecificMorpheme 类型', () => {
            const morpheme = {
                morpheme_id: 'zh_morpheme_001',
                concept_id: 'concept_001',
                language: multilingual_js_1.LanguageCode.ZH_CN,
                text: '快乐',
                alternative_forms: ['愉快', '开心'],
                phonetic_features: {
                    ipa_transcription: '/kuài.lè/',
                    syllable_count: 2,
                    tone_pattern: ['4', '4'],
                    phonetic_harmony: 0.8
                },
                morphological_info: {
                    pos_tag: 'ADJ',
                    morphological_type: 'compound',
                    root: '快乐',
                    prefixes: [],
                    suffixes: []
                },
                syntactic_properties: {
                    syntactic_function: ['attributive', 'predicative'],
                    collocation_constraints: ['快乐的', '很快乐'],
                    grammatical_features: {}
                },
                cultural_context: {
                    traditionality: 0.6,
                    modernity: 0.8,
                    formality: 0.5,
                    regionality: 0.7,
                    religious_sensitivity: 0.3,
                    age_appropriateness: ['adult', 'teen', 'child'],
                    cultural_tags: ['positive', 'emotion', 'wellbeing']
                },
                regional_variants: [],
                register_level: multilingual_js_1.RegisterLevel.NEUTRAL,
                language_quality_scores: {
                    naturalness: 0.9,
                    fluency: 0.9,
                    authenticity: 0.95,
                    aesthetic_appeal: 0.8,
                    pronunciation_ease: 0.8,
                    memorability: 0.9,
                    uniqueness: 0.6,
                    practicality: 0.9
                },
                cultural_appropriateness: 0.9,
                native_speaker_rating: 0.95,
                usage_frequency: 0.8,
                popularity_trend: 0.1,
                created_at: '2025-06-24T00:00:00Z',
                updated_at: '2025-06-24T00:00:00Z',
                version: '3.0.0',
                source: 'native_corpus',
                validation_status: 'validated'
            };
            (0, globals_1.expect)(morpheme.morpheme_id).toBe('zh_morpheme_001');
            (0, globals_1.expect)(morpheme.language).toBe(multilingual_js_1.LanguageCode.ZH_CN);
            (0, globals_1.expect)(morpheme.phonetic_features.syllable_count).toBe(2);
            (0, globals_1.expect)(morpheme.language_quality_scores.naturalness).toBe(0.9);
        });
        (0, globals_1.it)('应该正确定义语义向量维度常量', () => {
            // 测试常量类型
            const legacyDim = multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.LEGACY;
            const multilingualDim = multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL;
            const defaultDim = multilingual_js_1.SEMANTIC_VECTOR_DIMENSIONS.DEFAULT;
            (0, globals_1.expect)(legacyDim).toBe(20);
            (0, globals_1.expect)(multilingualDim).toBe(512);
            (0, globals_1.expect)(defaultDim).toBe(512);
        });
    });
    (0, globals_1.describe)('类型兼容性测试', () => {
        (0, globals_1.it)('应该支持传统 CulturalContext 枚举', () => {
            const contexts = [core_js_1.CulturalContext.ANCIENT, core_js_1.CulturalContext.MODERN, core_js_1.CulturalContext.NEUTRAL];
            contexts.forEach(context => {
                (0, globals_1.expect)([core_js_1.CulturalContext.ANCIENT, core_js_1.CulturalContext.MODERN, core_js_1.CulturalContext.NEUTRAL]).toContain(context);
            });
        });
        (0, globals_1.it)('应该支持多维度 CulturalContext 接口', () => {
            const multiContext = {
                traditionality: 0.6,
                modernity: 0.8,
                formality: 0.5,
                regionality: 0.7,
                religious_sensitivity: 0.3,
                age_appropriateness: ['adult', 'teen'],
                cultural_tags: ['positive', 'modern']
            };
            (0, globals_1.expect)(multiContext.traditionality).toBe(0.6);
            (0, globals_1.expect)(multiContext.cultural_tags).toContain('positive');
        });
        (0, globals_1.it)('应该支持语义向量的向后兼容', () => {
            const universalVector = {
                vector: new Array(512).fill(0.1),
                model_version: 'mBERT-v1.0',
                confidence: 0.95,
                updated_at: '2025-06-24T00:00:00Z',
                legacy_vector: new Array(20).fill(0.1) // 向后兼容
            };
            (0, globals_1.expect)(universalVector.vector).toHaveLength(512);
            (0, globals_1.expect)(universalVector.legacy_vector).toHaveLength(20);
        });
    });
    (0, globals_1.describe)('类型推断测试', () => {
        (0, globals_1.it)('应该正确推断函数返回类型', () => {
            // 模拟函数类型推断
            function createMorpheme(text) {
                return {
                    id: 'test',
                    text,
                    category: 'emotions',
                    subcategory: 'positive',
                    cultural_context: core_js_1.CulturalContext.MODERN,
                    usage_frequency: 0.8,
                    quality_score: 0.9,
                    semantic_vector: [0.1, 0.2],
                    tags: ['test'],
                    language_properties: {
                        syllable_count: 1,
                        character_count: 2,
                        phonetic_features: ['tone4'],
                        morphological_type: 'simple'
                    },
                    quality_metrics: {
                        naturalness: 0.9,
                        frequency: 0.8,
                        acceptability: 0.9,
                        aesthetic_appeal: 0.7
                    },
                    created_at: Date.now(),
                    source: 'test',
                    version: '2.0.0'
                };
            }
            const morpheme = createMorpheme('测试');
            (0, globals_1.expect)(morpheme.text).toBe('测试');
        });
        (0, globals_1.it)('应该支持泛型类型约束', () => {
            // 测试泛型约束
            function processLanguageData(data) {
                return data.filter(item => item.language === 'zh-CN');
            }
            const mockData = [
                { language: multilingual_js_1.LanguageCode.ZH_CN, text: '测试1' },
                { language: multilingual_js_1.LanguageCode.EN_US, text: 'test2' }
            ];
            const filtered = processLanguageData(mockData);
            (0, globals_1.expect)(filtered).toHaveLength(1);
            (0, globals_1.expect)(filtered[0].language).toBe(multilingual_js_1.LanguageCode.ZH_CN);
        });
    });
    (0, globals_1.describe)('编译时类型检查', () => {
        (0, globals_1.it)('应该防止类型错误的赋值', () => {
            // 这些测试确保TypeScript编译器能够捕获类型错误
            // 正确的类型赋值
            const validLanguage = multilingual_js_1.LanguageCode.ZH_CN;
            const validRegister = multilingual_js_1.RegisterLevel.FORMAL;
            (0, globals_1.expect)(validLanguage).toBe(multilingual_js_1.LanguageCode.ZH_CN);
            (0, globals_1.expect)(validRegister).toBe(multilingual_js_1.RegisterLevel.FORMAL);
            // TypeScript应该在编译时捕获以下错误：
            // const invalidLanguage: LanguageCode = 'invalid-lang' // 编译错误
            // const invalidRegister: RegisterLevel = 'INVALID' // 编译错误
        });
        (0, globals_1.it)('应该确保必需字段的存在', () => {
            // TypeScript应该确保所有必需字段都存在
            const partialMorpheme = {
                id: 'test',
                text: '测试'
                // 缺少其他必需字段
            };
            // 完整的morpheme应该包含所有必需字段
            (0, globals_1.expect)(partialMorpheme.id).toBe('test');
            (0, globals_1.expect)(partialMorpheme.text).toBe('测试');
        });
    });
    (0, globals_1.describe)('质量评估系统准确性测试', () => {
        (0, globals_1.it)('应该正确计算8维度质量评分', () => {
            const qualityScore = {
                overall: 0.85,
                dimensions: {
                    creativity: 0.9,
                    memorability: 0.8,
                    cultural_fit: 0.9,
                    uniqueness: 0.7,
                    pronunciation: 0.8,
                    semantic_coherence: 0.8,
                    aesthetic_appeal: 0.8,
                    practical_usability: 0.8
                },
                confidence: 0.9,
                evaluation_time: 150,
                algorithm_version: 'v2.0',
                issues: [],
                suggestions: [],
                timestamp: Date.now()
            };
            // 验证所有维度都在有效范围内
            Object.values(qualityScore.dimensions).forEach(score => {
                (0, globals_1.expect)(score).toBeGreaterThanOrEqual(0);
                (0, globals_1.expect)(score).toBeLessThanOrEqual(1);
            });
            // 验证整体评分与各维度的一致性
            const avgScore = Object.values(qualityScore.dimensions).reduce((sum, score) => sum + score, 0) / 8;
            // 整体评分应该接近各维度平均值
            (0, globals_1.expect)(Math.abs(qualityScore.overall - avgScore)).toBeLessThan(0.1);
        });
        (0, globals_1.it)('应该验证多语种质量评分的一致性', () => {
            const langQualityScores = {
                naturalness: 0.9,
                fluency: 0.85,
                authenticity: 0.95,
                aesthetic_appeal: 0.8,
                pronunciation_ease: 0.75,
                memorability: 0.9,
                uniqueness: 0.6,
                practicality: 0.85
            };
            // 验证所有评分都在有效范围内
            Object.values(langQualityScores).forEach(score => {
                (0, globals_1.expect)(score).toBeGreaterThanOrEqual(0);
                (0, globals_1.expect)(score).toBeLessThanOrEqual(1);
            });
            // 验证评分的合理性
            (0, globals_1.expect)(langQualityScores.authenticity).toBeGreaterThan(0.8); // 真实性应该较高
            (0, globals_1.expect)(langQualityScores.naturalness).toBeGreaterThan(0.7); // 自然度应该较高
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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