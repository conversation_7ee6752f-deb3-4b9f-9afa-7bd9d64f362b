{"version": 3, "names": ["cov_6si821j0y", "actualCoverage", "fs", "path", "fileURLToPath", "__filename", "s", "import", "meta", "url", "__dirname", "dirname", "LanguageCode", "RegisterLevel", "DATA_DIR", "join", "SUPPORTED_LANGUAGES", "ZH_CN", "name", "dataFile", "conceptFile", "enabled", "EN_US", "JA_JP", "KO_KR", "ES_ES", "FR_FR", "DE_DE", "AR_SA", "LanguageManager", "config", "languageDataSets", "Map", "conceptsCache", "isInitialized", "constructor", "b", "f", "defaultLanguage", "enabledLanguages", "enableCache", "cacheTTL", "initialize", "console", "log", "startTime", "Date", "now", "language", "loadLanguageData", "buildConceptsCache", "initTime", "length", "logInitializationSummary", "error", "Error", "message", "String", "dataFilePath", "existsSync", "morphemeData", "JSON", "parse", "readFileSync", "morphemes", "concepts", "adaptChineseMorphemes", "conceptFilePath", "conceptMorphemeMapping", "buildConceptMorphemeMapping", "languageDataSet", "loadedAt", "set", "chineseData", "map", "item", "index", "morpheme_id", "id", "concept_id", "text", "alternative_forms", "phonetic_features", "ipa_transcription", "language_properties", "pronunciation", "syllable_count", "tone_pattern", "phonetic_harmony", "morphological_info", "pos_tag", "mapCategoryToPOS", "category", "morphological_type", "prefixes", "suffixes", "syntactic_properties", "syntactic_function", "collocation_constraints", "grammatical_features", "cultural_context", "traditionality", "modernity", "formality", "regionality", "religious_sensitivity", "age_appropriateness", "cultural_tags", "tags", "regional_variants", "register_level", "NEUTRAL", "language_quality_scores", "naturalness", "quality_metrics", "fluency", "authenticity", "aesthetic_appeal", "pronunciation_ease", "memorability", "uniqueness", "practicality", "cultural_appropriateness", "native_speaker_rating", "quality_score", "usage_frequency", "popularity_trend", "created_at", "toISOString", "updated_at", "version", "source", "validation_status", "mapping", "morpheme", "conceptId", "has", "get", "push", "dataSet", "values", "concept", "size", "getMorphemesByLanguage", "getMorphemesByConceptAndLanguage", "getConcept", "getSupportedLanguages", "isLanguageSupported", "getLanguageStats", "morphemeCount", "conceptCount", "reloadLanguage", "stats", "isReady"], "sources": ["/home/<USER>/develop/workspace/namer-v6/server/core/multilingual/LanguageManager.ts"], "sourcesContent": ["/**\n * 语言管理器\n * \n * 负责多语种数据加载、语言切换、跨语言映射等核心功能\n * 支持v3.0多语种架构的概念-语言分离模式\n * \n * <AUTHOR> team\n * @version 3.0.0\n * @created 2025-06-24\n */\n\nimport * as fs from 'fs'\nimport * as path from 'path'\nimport { fileURLToPath } from 'url'\n\n// ES模块中的__dirname替代方案\nconst __filename = fileURLToPath(import.meta.url)\nconst __dirname = path.dirname(__filename)\n\n// 导入类型定义和枚举\nimport { LanguageCode, RegisterLevel } from '../../types/multilingual.js'\nimport type {\n  UniversalConcept,\n  LanguageSpecificMorpheme,\n  ConceptMorphemeMapping\n} from '../../types/multilingual.js'\n\n// ============================================================================\n// 配置和常量\n// ============================================================================\n\nconst DATA_DIR = path.join(__dirname, '../../data')\n\n// 支持的语言配置\nconst SUPPORTED_LANGUAGES: Record<LanguageCode, {\n  name: string\n  dataFile: string\n  conceptFile: string | null\n  enabled: boolean\n}> = {\n  [LanguageCode.ZH_CN]: {\n    name: '中文(简体)',\n    dataFile: 'morphemes_current.json',\n    conceptFile: null, // 中文使用传统格式\n    enabled: true\n  },\n  [LanguageCode.EN_US]: {\n    name: 'English (US)',\n    dataFile: 'english_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: true\n  },\n  [LanguageCode.JA_JP]: {\n    name: '日本語',\n    dataFile: 'japanese_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.KO_KR]: {\n    name: '한국어',\n    dataFile: 'korean_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.ES_ES]: {\n    name: 'Español',\n    dataFile: 'spanish_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.FR_FR]: {\n    name: 'Français',\n    dataFile: 'french_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.DE_DE]: {\n    name: 'Deutsch',\n    dataFile: 'german_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  },\n  [LanguageCode.AR_SA]: {\n    name: 'العربية',\n    dataFile: 'arabic_morphemes_v3.json',\n    conceptFile: 'universal_concepts_v3.json',\n    enabled: false\n  }\n  // 其他语言将在后续阶段添加\n}\n\n// ============================================================================\n// 语言管理器接口\n// ============================================================================\n\nexport interface LanguageManagerConfig {\n  /** 默认语言 */\n  defaultLanguage: LanguageCode\n  /** 启用的语言列表 */\n  enabledLanguages: LanguageCode[]\n  /** 是否启用缓存 */\n  enableCache: boolean\n  /** 缓存TTL (秒) */\n  cacheTTL: number\n}\n\nexport interface LanguageDataSet {\n  /** 语言代码 */\n  language: LanguageCode\n  /** 语言特定语素 */\n  morphemes: LanguageSpecificMorpheme[]\n  /** 通用概念 (仅v3.0格式) */\n  concepts?: UniversalConcept[]\n  /** 概念-语素映射 */\n  conceptMorphemeMapping: Map<string, LanguageSpecificMorpheme[]>\n  /** 加载时间戳 */\n  loadedAt: number\n}\n\n// ============================================================================\n// 语言管理器类\n// ============================================================================\n\n/**\n * 语言管理器类\n * \n * 提供多语种数据管理、语言切换、跨语言映射等功能\n */\nexport class LanguageManager {\n  private config: LanguageManagerConfig\n  private languageDataSets: Map<LanguageCode, LanguageDataSet> = new Map()\n  private conceptsCache: Map<string, UniversalConcept> = new Map()\n  private isInitialized = false\n\n  /**\n   * 构造函数\n   */\n  constructor(config: Partial<LanguageManagerConfig> = {}) {\n    this.config = {\n      defaultLanguage: config.defaultLanguage || LanguageCode.ZH_CN,\n      enabledLanguages: config.enabledLanguages || [LanguageCode.ZH_CN], // 暂时只启用中文\n      enableCache: config.enableCache ?? true,\n      cacheTTL: config.cacheTTL || 3600 // 1小时\n    }\n  }\n\n  /**\n   * 初始化语言管理器\n   */\n  async initialize(): Promise<void> {\n    if (this.isInitialized) {\n      console.log('🔄 语言管理器已初始化，跳过重复初始化')\n      return\n    }\n\n    const startTime = Date.now()\n    console.log('🌍 开始初始化多语种语言管理器...')\n\n    try {\n      // 加载启用的语言数据\n      for (const language of this.config.enabledLanguages) {\n        if (SUPPORTED_LANGUAGES[language]?.enabled) {\n          await this.loadLanguageData(language)\n        }\n      }\n\n      // 构建概念缓存\n      this.buildConceptsCache()\n\n      this.isInitialized = true\n      const initTime = Date.now() - startTime\n\n      console.log(`✅ 语言管理器初始化完成: ${this.config.enabledLanguages.length}种语言, 耗时${initTime}ms`)\n      this.logInitializationSummary()\n\n    } catch (error) {\n      console.error('❌ 语言管理器初始化失败:', error)\n      throw new Error(`语言管理器初始化失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 加载指定语言的数据\n   */\n  private async loadLanguageData(language: LanguageCode): Promise<void> {\n    const config = SUPPORTED_LANGUAGES[language]\n    if (!config) {\n      throw new Error(`不支持的语言: ${language}`)\n    }\n\n    console.log(`📁 加载${config.name}数据...`)\n\n    try {\n      const dataFilePath = path.join(DATA_DIR, config.dataFile)\n      \n      if (!fs.existsSync(dataFilePath)) {\n        throw new Error(`数据文件不存在: ${dataFilePath}`)\n      }\n\n      // 加载语素数据\n      const morphemeData = JSON.parse(fs.readFileSync(dataFilePath, 'utf8'))\n      let morphemes: LanguageSpecificMorpheme[]\n      let concepts: UniversalConcept[] | undefined\n\n      if (language === LanguageCode.ZH_CN) {\n        // 中文使用传统格式，需要适配\n        morphemes = this.adaptChineseMorphemes(morphemeData)\n      } else {\n        // 其他语言使用v3.0格式\n        morphemes = morphemeData as LanguageSpecificMorpheme[]\n        \n        // 加载通用概念\n        if (config.conceptFile) {\n          const conceptFilePath = path.join(DATA_DIR, config.conceptFile)\n          if (fs.existsSync(conceptFilePath)) {\n            concepts = JSON.parse(fs.readFileSync(conceptFilePath, 'utf8'))\n          }\n        }\n      }\n\n      // 构建概念-语素映射\n      const conceptMorphemeMapping = this.buildConceptMorphemeMapping(morphemes)\n\n      // 创建语言数据集\n      const languageDataSet: LanguageDataSet = {\n        language,\n        morphemes,\n        concepts,\n        conceptMorphemeMapping,\n        loadedAt: Date.now()\n      }\n\n      this.languageDataSets.set(language, languageDataSet)\n      console.log(`✅ ${config.name}数据加载完成: ${morphemes.length}个语素`)\n\n    } catch (error) {\n      throw new Error(`加载${config.name}数据失败: ${error instanceof Error ? error.message : String(error)}`)\n    }\n  }\n\n  /**\n   * 适配中文语素数据到v3.0格式\n   */\n  private adaptChineseMorphemes(chineseData: any[]): LanguageSpecificMorpheme[] {\n    return chineseData.map((item, index) => ({\n      morpheme_id: `zh_${item.id || index}`,\n      concept_id: `concept_${item.text}`, // 临时概念ID\n      language: LanguageCode.ZH_CN,\n      text: item.text,\n      alternative_forms: [],\n      phonetic_features: {\n        ipa_transcription: item.language_properties?.pronunciation || '',\n        syllable_count: item.language_properties?.syllable_count || 1,\n        tone_pattern: [], // 中文声调信息\n        phonetic_harmony: 0.8\n      },\n      morphological_info: {\n        pos_tag: this.mapCategoryToPOS(item.category),\n        morphological_type: item.language_properties?.morphological_type || 'root',\n        prefixes: [],\n        suffixes: []\n      },\n      syntactic_properties: {\n        syntactic_function: ['root'],\n        collocation_constraints: [],\n        grammatical_features: {}\n      },\n      cultural_context: {\n        traditionality: item.cultural_context === 'ancient' ? 0.9 : 0.3,\n        modernity: item.cultural_context === 'modern' ? 0.9 : 0.3,\n        formality: 0.5,\n        regionality: 0.2,\n        religious_sensitivity: 0.1,\n        age_appropriateness: ['all'],\n        cultural_tags: item.tags || []\n      },\n      regional_variants: [],\n      register_level: RegisterLevel.NEUTRAL,\n      language_quality_scores: {\n        naturalness: item.quality_metrics?.naturalness || 0.8,\n        fluency: 0.9,\n        authenticity: 0.9,\n        aesthetic_appeal: item.quality_metrics?.aesthetic_appeal || 0.8,\n        pronunciation_ease: 0.8,\n        memorability: 0.8,\n        uniqueness: 0.6,\n        practicality: 0.9\n      },\n      cultural_appropriateness: 0.9,\n      native_speaker_rating: item.quality_score || 0.8,\n      usage_frequency: item.usage_frequency || 0.5,\n      popularity_trend: 0.0,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      version: '3.0.0',\n      source: 'chinese_morphemes_adapted',\n      validation_status: 'validated'\n    }))\n  }\n\n  /**\n   * 映射类别到词性标注\n   */\n  private mapCategoryToPOS(category: string): string {\n    const mapping: Record<string, string> = {\n      'emotions': 'ADJ',\n      'professions': 'NOUN',\n      'characteristics': 'ADJ',\n      'objects': 'NOUN',\n      'actions': 'VERB',\n      'concepts': 'NOUN'\n    }\n    return mapping[category] || 'NOUN'\n  }\n\n  /**\n   * 构建概念-语素映射\n   */\n  private buildConceptMorphemeMapping(morphemes: LanguageSpecificMorpheme[]): Map<string, LanguageSpecificMorpheme[]> {\n    const mapping = new Map<string, LanguageSpecificMorpheme[]>()\n    \n    for (const morpheme of morphemes) {\n      const conceptId = morpheme.concept_id\n      if (!mapping.has(conceptId)) {\n        mapping.set(conceptId, [])\n      }\n      mapping.get(conceptId)!.push(morpheme)\n    }\n    \n    return mapping\n  }\n\n  /**\n   * 构建概念缓存\n   */\n  private buildConceptsCache(): void {\n    for (const dataSet of this.languageDataSets.values()) {\n      if (dataSet.concepts) {\n        for (const concept of dataSet.concepts) {\n          this.conceptsCache.set(concept.concept_id, concept)\n        }\n      }\n    }\n    console.log(`📊 概念缓存构建完成: ${this.conceptsCache.size}个概念`)\n  }\n\n  /**\n   * 获取指定语言的语素\n   */\n  getMorphemesByLanguage(language: LanguageCode): LanguageSpecificMorpheme[] {\n    const dataSet = this.languageDataSets.get(language)\n    if (!dataSet) {\n      throw new Error(`语言数据未加载: ${language}`)\n    }\n    return dataSet.morphemes\n  }\n\n  /**\n   * 根据概念ID获取指定语言的语素\n   */\n  getMorphemesByConceptAndLanguage(conceptId: string, language: LanguageCode): LanguageSpecificMorpheme[] {\n    const dataSet = this.languageDataSets.get(language)\n    if (!dataSet) {\n      throw new Error(`语言数据未加载: ${language}`)\n    }\n    return dataSet.conceptMorphemeMapping.get(conceptId) || []\n  }\n\n  /**\n   * 获取通用概念\n   */\n  getConcept(conceptId: string): UniversalConcept | undefined {\n    return this.conceptsCache.get(conceptId)\n  }\n\n  /**\n   * 获取所有支持的语言\n   */\n  getSupportedLanguages(): LanguageCode[] {\n    return this.config.enabledLanguages\n  }\n\n  /**\n   * 检查语言是否支持\n   */\n  isLanguageSupported(language: LanguageCode): boolean {\n    return this.languageDataSets.has(language)\n  }\n\n  /**\n   * 获取语言统计信息\n   */\n  getLanguageStats(language: LanguageCode): { morphemeCount: number; conceptCount: number } {\n    const dataSet = this.languageDataSets.get(language)\n    if (!dataSet) {\n      return { morphemeCount: 0, conceptCount: 0 }\n    }\n    \n    return {\n      morphemeCount: dataSet.morphemes.length,\n      conceptCount: dataSet.concepts?.length || 0\n    }\n  }\n\n  /**\n   * 重新加载语言数据\n   */\n  async reloadLanguage(language: LanguageCode): Promise<void> {\n    console.log(`🔄 重新加载${language}数据...`)\n    await this.loadLanguageData(language)\n    this.buildConceptsCache()\n  }\n\n  /**\n   * 输出初始化摘要\n   */\n  private logInitializationSummary(): void {\n    console.log('\\n🌍 多语种语言管理器初始化摘要:')\n    console.log(`   默认语言: ${this.config.defaultLanguage}`)\n    console.log(`   启用语言: ${this.config.enabledLanguages.join(', ')}`)\n    console.log(`   概念总数: ${this.conceptsCache.size}`)\n    \n    for (const language of this.config.enabledLanguages) {\n      const stats = this.getLanguageStats(language)\n      console.log(`   ${language}: ${stats.morphemeCount}个语素, ${stats.conceptCount}个概念`)\n    }\n  }\n\n  /**\n   * 检查是否已初始化\n   */\n  isReady(): boolean {\n    return this.isInitialized\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiBM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAjBN;;;;;;;;;;AAWA,OAAO,KAAKE,EAAE,MAAM,IAAI;AACxB,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,aAAa,QAAQ,KAAK;AAEnC;AACA,MAAMC,UAAU;AAAA;AAAA,CAAAL,aAAA,GAAAM,CAAA,OAAGF,aAAa,CAACG,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AACjD,MAAMC,SAAS;AAAA;AAAA,CAAAV,aAAA,GAAAM,CAAA,OAAGH,IAAI,CAACQ,OAAO,CAACN,UAAU,CAAC;AAE1C;AACA,SAASO,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;AAOzE;AACA;AACA;AAEA,MAAMC,QAAQ;AAAA;AAAA,CAAAd,aAAA,GAAAM,CAAA,OAAGH,IAAI,CAACY,IAAI,CAACL,SAAS,EAAE,YAAY,CAAC;AAEnD;AACA,MAAMM,mBAAmB;AAAA;AAAA,CAAAhB,aAAA,GAAAM,CAAA,OAKpB;EACH,CAACM,YAAY,CAACK,KAAK,GAAG;IACpBC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,wBAAwB;IAClCC,WAAW,EAAE,IAAI;IAAE;IACnBC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACU,KAAK,GAAG;IACpBJ,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,2BAA2B;IACrCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACW,KAAK,GAAG;IACpBL,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,4BAA4B;IACtCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACY,KAAK,GAAG;IACpBN,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACa,KAAK,GAAG;IACpBP,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,2BAA2B;IACrCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACc,KAAK,GAAG;IACpBR,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACe,KAAK,GAAG;IACpBT,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;GACV;EACD,CAACT,YAAY,CAACgB,KAAK,GAAG;IACpBV,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE,4BAA4B;IACzCC,OAAO,EAAE;;EAEX;CACD;AA8BD;AACA;AACA;AAEA;;;;;AAKA,OAAM,MAAOQ,eAAe;EAClBC,MAAM;EACNC,gBAAgB;EAAA;EAAA,CAAA/B,aAAA,GAAAM,CAAA,OAAuC,IAAI0B,GAAG,EAAE;EAChEC,aAAa;EAAA;EAAA,CAAAjC,aAAA,GAAAM,CAAA,OAAkC,IAAI0B,GAAG,EAAE;EACxDE,aAAa;EAAA;EAAA,CAAAlC,aAAA,GAAAM,CAAA,OAAG,KAAK;EAE7B;;;EAGA6B,YAAYL,MAAA;EAAA;EAAA,CAAA9B,aAAA,GAAAoC,CAAA,UAAyC,EAAE;IAAA;IAAApC,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACrD,IAAI,CAACwB,MAAM,GAAG;MACZQ,eAAe;MAAE;MAAA,CAAAtC,aAAA,GAAAoC,CAAA,UAAAN,MAAM,CAACQ,eAAe;MAAA;MAAA,CAAAtC,aAAA,GAAAoC,CAAA,UAAIxB,YAAY,CAACK,KAAK;MAC7DsB,gBAAgB;MAAE;MAAA,CAAAvC,aAAA,GAAAoC,CAAA,UAAAN,MAAM,CAACS,gBAAgB;MAAA;MAAA,CAAAvC,aAAA,GAAAoC,CAAA,UAAI,CAACxB,YAAY,CAACK,KAAK,CAAC;MAAE;MACnEuB,WAAW;MAAE;MAAA,CAAAxC,aAAA,GAAAoC,CAAA,UAAAN,MAAM,CAACU,WAAW;MAAA;MAAA,CAAAxC,aAAA,GAAAoC,CAAA,UAAI,IAAI;MACvCK,QAAQ;MAAE;MAAA,CAAAzC,aAAA,GAAAoC,CAAA,UAAAN,MAAM,CAACW,QAAQ;MAAA;MAAA,CAAAzC,aAAA,GAAAoC,CAAA,UAAI,IAAI,EAAC;KACnC;EACH;EAEA;;;EAGA,MAAMM,UAAUA,CAAA;IAAA;IAAA1C,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACd,IAAI,IAAI,CAAC4B,aAAa,EAAE;MAAA;MAAAlC,aAAA,GAAAoC,CAAA;MAAApC,aAAA,GAAAM,CAAA;MACtBqC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MAAA;MAAA5C,aAAA,GAAAM,CAAA;MACnC;IACF,CAAC;IAAA;IAAA;MAAAN,aAAA,GAAAoC,CAAA;IAAA;IAED,MAAMS,SAAS;IAAA;IAAA,CAAA7C,aAAA,GAAAM,CAAA,QAAGwC,IAAI,CAACC,GAAG,EAAE;IAAA;IAAA/C,aAAA,GAAAM,CAAA;IAC5BqC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAAA;IAAA5C,aAAA,GAAAM,CAAA;IAElC,IAAI;MAAA;MAAAN,aAAA,GAAAM,CAAA;MACF;MACA,KAAK,MAAM0C,QAAQ,IAAI,IAAI,CAAClB,MAAM,CAACS,gBAAgB,EAAE;QAAA;QAAAvC,aAAA,GAAAM,CAAA;QACnD,IAAIU,mBAAmB,CAACgC,QAAQ,CAAC,EAAE3B,OAAO,EAAE;UAAA;UAAArB,aAAA,GAAAoC,CAAA;UAAApC,aAAA,GAAAM,CAAA;UAC1C,MAAM,IAAI,CAAC2C,gBAAgB,CAACD,QAAQ,CAAC;QACvC,CAAC;QAAA;QAAA;UAAAhD,aAAA,GAAAoC,CAAA;QAAA;MACH;MAEA;MAAA;MAAApC,aAAA,GAAAM,CAAA;MACA,IAAI,CAAC4C,kBAAkB,EAAE;MAAA;MAAAlD,aAAA,GAAAM,CAAA;MAEzB,IAAI,CAAC4B,aAAa,GAAG,IAAI;MACzB,MAAMiB,QAAQ;MAAA;MAAA,CAAAnD,aAAA,GAAAM,CAAA,QAAGwC,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MAAA;MAAA7C,aAAA,GAAAM,CAAA;MAEvCqC,OAAO,CAACC,GAAG,CAAC,iBAAiB,IAAI,CAACd,MAAM,CAACS,gBAAgB,CAACa,MAAM,UAAUD,QAAQ,IAAI,CAAC;MAAA;MAAAnD,aAAA,GAAAM,CAAA;MACvF,IAAI,CAAC+C,wBAAwB,EAAE;IAEjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA;MAAAtD,aAAA,GAAAM,CAAA;MACdqC,OAAO,CAACW,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MAAA;MAAAtD,aAAA,GAAAM,CAAA;MACrC,MAAM,IAAIiD,KAAK,CAAC,eAAeD,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAvD,aAAA,GAAAoC,CAAA,UAAGkB,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAxD,aAAA,GAAAoC,CAAA,UAAGqB,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IAC1F;EACF;EAEA;;;EAGQ,MAAML,gBAAgBA,CAACD,QAAsB;IAAA;IAAAhD,aAAA,GAAAqC,CAAA;IACnD,MAAMP,MAAM;IAAA;IAAA,CAAA9B,aAAA,GAAAM,CAAA,QAAGU,mBAAmB,CAACgC,QAAQ,CAAC;IAAA;IAAAhD,aAAA,GAAAM,CAAA;IAC5C,IAAI,CAACwB,MAAM,EAAE;MAAA;MAAA9B,aAAA,GAAAoC,CAAA;MAAApC,aAAA,GAAAM,CAAA;MACX,MAAM,IAAIiD,KAAK,CAAC,WAAWP,QAAQ,EAAE,CAAC;IACxC,CAAC;IAAA;IAAA;MAAAhD,aAAA,GAAAoC,CAAA;IAAA;IAAApC,aAAA,GAAAM,CAAA;IAEDqC,OAAO,CAACC,GAAG,CAAC,QAAQd,MAAM,CAACZ,IAAI,OAAO,CAAC;IAAA;IAAAlB,aAAA,GAAAM,CAAA;IAEvC,IAAI;MACF,MAAMoD,YAAY;MAAA;MAAA,CAAA1D,aAAA,GAAAM,CAAA,QAAGH,IAAI,CAACY,IAAI,CAACD,QAAQ,EAAEgB,MAAM,CAACX,QAAQ,CAAC;MAAA;MAAAnB,aAAA,GAAAM,CAAA;MAEzD,IAAI,CAACJ,EAAE,CAACyD,UAAU,CAACD,YAAY,CAAC,EAAE;QAAA;QAAA1D,aAAA,GAAAoC,CAAA;QAAApC,aAAA,GAAAM,CAAA;QAChC,MAAM,IAAIiD,KAAK,CAAC,YAAYG,YAAY,EAAE,CAAC;MAC7C,CAAC;MAAA;MAAA;QAAA1D,aAAA,GAAAoC,CAAA;MAAA;MAED;MACA,MAAMwB,YAAY;MAAA;MAAA,CAAA5D,aAAA,GAAAM,CAAA,QAAGuD,IAAI,CAACC,KAAK,CAAC5D,EAAE,CAAC6D,YAAY,CAACL,YAAY,EAAE,MAAM,CAAC,CAAC;MACtE,IAAIM,SAAqC;MACzC,IAAIC,QAAwC;MAAA;MAAAjE,aAAA,GAAAM,CAAA;MAE5C,IAAI0C,QAAQ,KAAKpC,YAAY,CAACK,KAAK,EAAE;QAAA;QAAAjB,aAAA,GAAAoC,CAAA;QAAApC,aAAA,GAAAM,CAAA;QACnC;QACA0D,SAAS,GAAG,IAAI,CAACE,qBAAqB,CAACN,YAAY,CAAC;MACtD,CAAC,MAAM;QAAA;QAAA5D,aAAA,GAAAoC,CAAA;QAAApC,aAAA,GAAAM,CAAA;QACL;QACA0D,SAAS,GAAGJ,YAA0C;QAEtD;QAAA;QAAA5D,aAAA,GAAAM,CAAA;QACA,IAAIwB,MAAM,CAACV,WAAW,EAAE;UAAA;UAAApB,aAAA,GAAAoC,CAAA;UACtB,MAAM+B,eAAe;UAAA;UAAA,CAAAnE,aAAA,GAAAM,CAAA,QAAGH,IAAI,CAACY,IAAI,CAACD,QAAQ,EAAEgB,MAAM,CAACV,WAAW,CAAC;UAAA;UAAApB,aAAA,GAAAM,CAAA;UAC/D,IAAIJ,EAAE,CAACyD,UAAU,CAACQ,eAAe,CAAC,EAAE;YAAA;YAAAnE,aAAA,GAAAoC,CAAA;YAAApC,aAAA,GAAAM,CAAA;YAClC2D,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAAC5D,EAAE,CAAC6D,YAAY,CAACI,eAAe,EAAE,MAAM,CAAC,CAAC;UACjE,CAAC;UAAA;UAAA;YAAAnE,aAAA,GAAAoC,CAAA;UAAA;QACH,CAAC;QAAA;QAAA;UAAApC,aAAA,GAAAoC,CAAA;QAAA;MACH;MAEA;MACA,MAAMgC,sBAAsB;MAAA;MAAA,CAAApE,aAAA,GAAAM,CAAA,QAAG,IAAI,CAAC+D,2BAA2B,CAACL,SAAS,CAAC;MAE1E;MACA,MAAMM,eAAe;MAAA;MAAA,CAAAtE,aAAA,GAAAM,CAAA,QAAoB;QACvC0C,QAAQ;QACRgB,SAAS;QACTC,QAAQ;QACRG,sBAAsB;QACtBG,QAAQ,EAAEzB,IAAI,CAACC,GAAG;OACnB;MAAA;MAAA/C,aAAA,GAAAM,CAAA;MAED,IAAI,CAACyB,gBAAgB,CAACyC,GAAG,CAACxB,QAAQ,EAAEsB,eAAe,CAAC;MAAA;MAAAtE,aAAA,GAAAM,CAAA;MACpDqC,OAAO,CAACC,GAAG,CAAC,KAAKd,MAAM,CAACZ,IAAI,WAAW8C,SAAS,CAACZ,MAAM,KAAK,CAAC;IAE/D,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAAtD,aAAA,GAAAM,CAAA;MACd,MAAM,IAAIiD,KAAK,CAAC,KAAKzB,MAAM,CAACZ,IAAI,SAASoC,KAAK,YAAYC,KAAK;MAAA;MAAA,CAAAvD,aAAA,GAAAoC,CAAA,WAAGkB,KAAK,CAACE,OAAO;MAAA;MAAA,CAAAxD,aAAA,GAAAoC,CAAA,WAAGqB,MAAM,CAACH,KAAK,CAAC,GAAE,CAAC;IACpG;EACF;EAEA;;;EAGQY,qBAAqBA,CAACO,WAAkB;IAAA;IAAAzE,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IAC9C,OAAOmE,WAAW,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAM;MAAA;MAAA5E,aAAA,GAAAqC,CAAA;MAAArC,aAAA,GAAAM,CAAA;MAAA;QACvCuE,WAAW,EAAE;QAAM;QAAA,CAAA7E,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACG,EAAE;QAAA;QAAA,CAAA9E,aAAA,GAAAoC,CAAA,WAAIwC,KAAK,GAAE;QACrCG,UAAU,EAAE,WAAWJ,IAAI,CAACK,IAAI,EAAE;QAAE;QACpChC,QAAQ,EAAEpC,YAAY,CAACK,KAAK;QAC5B+D,IAAI,EAAEL,IAAI,CAACK,IAAI;QACfC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE;UACjBC,iBAAiB;UAAE;UAAA,CAAAnF,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACS,mBAAmB,EAAEC,aAAa;UAAA;UAAA,CAAArF,aAAA,GAAAoC,CAAA,WAAI,EAAE;UAChEkD,cAAc;UAAE;UAAA,CAAAtF,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACS,mBAAmB,EAAEE,cAAc;UAAA;UAAA,CAAAtF,aAAA,GAAAoC,CAAA,WAAI,CAAC;UAC7DmD,YAAY,EAAE,EAAE;UAAE;UAClBC,gBAAgB,EAAE;SACnB;QACDC,kBAAkB,EAAE;UAClBC,OAAO,EAAE,IAAI,CAACC,gBAAgB,CAAChB,IAAI,CAACiB,QAAQ,CAAC;UAC7CC,kBAAkB;UAAE;UAAA,CAAA7F,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACS,mBAAmB,EAAES,kBAAkB;UAAA;UAAA,CAAA7F,aAAA,GAAAoC,CAAA,WAAI,MAAM;UAC1E0D,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE;SACX;QACDC,oBAAoB,EAAE;UACpBC,kBAAkB,EAAE,CAAC,MAAM,CAAC;UAC5BC,uBAAuB,EAAE,EAAE;UAC3BC,oBAAoB,EAAE;SACvB;QACDC,gBAAgB,EAAE;UAChBC,cAAc,EAAE1B,IAAI,CAACyB,gBAAgB,KAAK,SAAS;UAAA;UAAA,CAAApG,aAAA,GAAAoC,CAAA,WAAG,GAAG;UAAA;UAAA,CAAApC,aAAA,GAAAoC,CAAA,WAAG,GAAG;UAC/DkE,SAAS,EAAE3B,IAAI,CAACyB,gBAAgB,KAAK,QAAQ;UAAA;UAAA,CAAApG,aAAA,GAAAoC,CAAA,WAAG,GAAG;UAAA;UAAA,CAAApC,aAAA,GAAAoC,CAAA,WAAG,GAAG;UACzDmE,SAAS,EAAE,GAAG;UACdC,WAAW,EAAE,GAAG;UAChBC,qBAAqB,EAAE,GAAG;UAC1BC,mBAAmB,EAAE,CAAC,KAAK,CAAC;UAC5BC,aAAa;UAAE;UAAA,CAAA3G,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACiC,IAAI;UAAA;UAAA,CAAA5G,aAAA,GAAAoC,CAAA,WAAI,EAAE;SAC/B;QACDyE,iBAAiB,EAAE,EAAE;QACrBC,cAAc,EAAEjG,aAAa,CAACkG,OAAO;QACrCC,uBAAuB,EAAE;UACvBC,WAAW;UAAE;UAAA,CAAAjH,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACuC,eAAe,EAAED,WAAW;UAAA;UAAA,CAAAjH,aAAA,GAAAoC,CAAA,WAAI,GAAG;UACrD+E,OAAO,EAAE,GAAG;UACZC,YAAY,EAAE,GAAG;UACjBC,gBAAgB;UAAE;UAAA,CAAArH,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACuC,eAAe,EAAEG,gBAAgB;UAAA;UAAA,CAAArH,aAAA,GAAAoC,CAAA,WAAI,GAAG;UAC/DkF,kBAAkB,EAAE,GAAG;UACvBC,YAAY,EAAE,GAAG;UACjBC,UAAU,EAAE,GAAG;UACfC,YAAY,EAAE;SACf;QACDC,wBAAwB,EAAE,GAAG;QAC7BC,qBAAqB;QAAE;QAAA,CAAA3H,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACiD,aAAa;QAAA;QAAA,CAAA5H,aAAA,GAAAoC,CAAA,WAAI,GAAG;QAChDyF,eAAe;QAAE;QAAA,CAAA7H,aAAA,GAAAoC,CAAA,WAAAuC,IAAI,CAACkD,eAAe;QAAA;QAAA,CAAA7H,aAAA,GAAAoC,CAAA,WAAI,GAAG;QAC5C0F,gBAAgB,EAAE,GAAG;QACrBC,UAAU,EAAE,IAAIjF,IAAI,EAAE,CAACkF,WAAW,EAAE;QACpCC,UAAU,EAAE,IAAInF,IAAI,EAAE,CAACkF,WAAW,EAAE;QACpCE,OAAO,EAAE,OAAO;QAChBC,MAAM,EAAE,2BAA2B;QACnCC,iBAAiB,EAAE;OACpB;KAAC,CAAC;EACL;EAEA;;;EAGQzC,gBAAgBA,CAACC,QAAgB;IAAA;IAAA5F,aAAA,GAAAqC,CAAA;IACvC,MAAMgG,OAAO;IAAA;IAAA,CAAArI,aAAA,GAAAM,CAAA,QAA2B;MACtC,UAAU,EAAE,KAAK;MACjB,aAAa,EAAE,MAAM;MACrB,iBAAiB,EAAE,KAAK;MACxB,SAAS,EAAE,MAAM;MACjB,SAAS,EAAE,MAAM;MACjB,UAAU,EAAE;KACb;IAAA;IAAAN,aAAA,GAAAM,CAAA;IACD,OAAO,2BAAAN,aAAA,GAAAoC,CAAA,WAAAiG,OAAO,CAACzC,QAAQ,CAAC;IAAA;IAAA,CAAA5F,aAAA,GAAAoC,CAAA,WAAI,MAAM;EACpC;EAEA;;;EAGQiC,2BAA2BA,CAACL,SAAqC;IAAA;IAAAhE,aAAA,GAAAqC,CAAA;IACvE,MAAMgG,OAAO;IAAA;IAAA,CAAArI,aAAA,GAAAM,CAAA,QAAG,IAAI0B,GAAG,EAAsC;IAAA;IAAAhC,aAAA,GAAAM,CAAA;IAE7D,KAAK,MAAMgI,QAAQ,IAAItE,SAAS,EAAE;MAChC,MAAMuE,SAAS;MAAA;MAAA,CAAAvI,aAAA,GAAAM,CAAA,QAAGgI,QAAQ,CAACvD,UAAU;MAAA;MAAA/E,aAAA,GAAAM,CAAA;MACrC,IAAI,CAAC+H,OAAO,CAACG,GAAG,CAACD,SAAS,CAAC,EAAE;QAAA;QAAAvI,aAAA,GAAAoC,CAAA;QAAApC,aAAA,GAAAM,CAAA;QAC3B+H,OAAO,CAAC7D,GAAG,CAAC+D,SAAS,EAAE,EAAE,CAAC;MAC5B,CAAC;MAAA;MAAA;QAAAvI,aAAA,GAAAoC,CAAA;MAAA;MAAApC,aAAA,GAAAM,CAAA;MACD+H,OAAO,CAACI,GAAG,CAACF,SAAS,CAAE,CAACG,IAAI,CAACJ,QAAQ,CAAC;IACxC;IAAC;IAAAtI,aAAA,GAAAM,CAAA;IAED,OAAO+H,OAAO;EAChB;EAEA;;;EAGQnF,kBAAkBA,CAAA;IAAA;IAAAlD,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACxB,KAAK,MAAMqI,OAAO,IAAI,IAAI,CAAC5G,gBAAgB,CAAC6G,MAAM,EAAE,EAAE;MAAA;MAAA5I,aAAA,GAAAM,CAAA;MACpD,IAAIqI,OAAO,CAAC1E,QAAQ,EAAE;QAAA;QAAAjE,aAAA,GAAAoC,CAAA;QAAApC,aAAA,GAAAM,CAAA;QACpB,KAAK,MAAMuI,OAAO,IAAIF,OAAO,CAAC1E,QAAQ,EAAE;UAAA;UAAAjE,aAAA,GAAAM,CAAA;UACtC,IAAI,CAAC2B,aAAa,CAACuC,GAAG,CAACqE,OAAO,CAAC9D,UAAU,EAAE8D,OAAO,CAAC;QACrD;MACF,CAAC;MAAA;MAAA;QAAA7I,aAAA,GAAAoC,CAAA;MAAA;IACH;IAAC;IAAApC,aAAA,GAAAM,CAAA;IACDqC,OAAO,CAACC,GAAG,CAAC,gBAAgB,IAAI,CAACX,aAAa,CAAC6G,IAAI,KAAK,CAAC;EAC3D;EAEA;;;EAGAC,sBAAsBA,CAAC/F,QAAsB;IAAA;IAAAhD,aAAA,GAAAqC,CAAA;IAC3C,MAAMsG,OAAO;IAAA;IAAA,CAAA3I,aAAA,GAAAM,CAAA,QAAG,IAAI,CAACyB,gBAAgB,CAAC0G,GAAG,CAACzF,QAAQ,CAAC;IAAA;IAAAhD,aAAA,GAAAM,CAAA;IACnD,IAAI,CAACqI,OAAO,EAAE;MAAA;MAAA3I,aAAA,GAAAoC,CAAA;MAAApC,aAAA,GAAAM,CAAA;MACZ,MAAM,IAAIiD,KAAK,CAAC,YAAYP,QAAQ,EAAE,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAhD,aAAA,GAAAoC,CAAA;IAAA;IAAApC,aAAA,GAAAM,CAAA;IACD,OAAOqI,OAAO,CAAC3E,SAAS;EAC1B;EAEA;;;EAGAgF,gCAAgCA,CAACT,SAAiB,EAAEvF,QAAsB;IAAA;IAAAhD,aAAA,GAAAqC,CAAA;IACxE,MAAMsG,OAAO;IAAA;IAAA,CAAA3I,aAAA,GAAAM,CAAA,QAAG,IAAI,CAACyB,gBAAgB,CAAC0G,GAAG,CAACzF,QAAQ,CAAC;IAAA;IAAAhD,aAAA,GAAAM,CAAA;IACnD,IAAI,CAACqI,OAAO,EAAE;MAAA;MAAA3I,aAAA,GAAAoC,CAAA;MAAApC,aAAA,GAAAM,CAAA;MACZ,MAAM,IAAIiD,KAAK,CAAC,YAAYP,QAAQ,EAAE,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAhD,aAAA,GAAAoC,CAAA;IAAA;IAAApC,aAAA,GAAAM,CAAA;IACD,OAAO,2BAAAN,aAAA,GAAAoC,CAAA,WAAAuG,OAAO,CAACvE,sBAAsB,CAACqE,GAAG,CAACF,SAAS,CAAC;IAAA;IAAA,CAAAvI,aAAA,GAAAoC,CAAA,WAAI,EAAE;EAC5D;EAEA;;;EAGA6G,UAAUA,CAACV,SAAiB;IAAA;IAAAvI,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IAC1B,OAAO,IAAI,CAAC2B,aAAa,CAACwG,GAAG,CAACF,SAAS,CAAC;EAC1C;EAEA;;;EAGAW,qBAAqBA,CAAA;IAAA;IAAAlJ,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACnB,OAAO,IAAI,CAACwB,MAAM,CAACS,gBAAgB;EACrC;EAEA;;;EAGA4G,mBAAmBA,CAACnG,QAAsB;IAAA;IAAAhD,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACxC,OAAO,IAAI,CAACyB,gBAAgB,CAACyG,GAAG,CAACxF,QAAQ,CAAC;EAC5C;EAEA;;;EAGAoG,gBAAgBA,CAACpG,QAAsB;IAAA;IAAAhD,aAAA,GAAAqC,CAAA;IACrC,MAAMsG,OAAO;IAAA;IAAA,CAAA3I,aAAA,GAAAM,CAAA,QAAG,IAAI,CAACyB,gBAAgB,CAAC0G,GAAG,CAACzF,QAAQ,CAAC;IAAA;IAAAhD,aAAA,GAAAM,CAAA;IACnD,IAAI,CAACqI,OAAO,EAAE;MAAA;MAAA3I,aAAA,GAAAoC,CAAA;MAAApC,aAAA,GAAAM,CAAA;MACZ,OAAO;QAAE+I,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC,CAAE;IAC9C,CAAC;IAAA;IAAA;MAAAtJ,aAAA,GAAAoC,CAAA;IAAA;IAAApC,aAAA,GAAAM,CAAA;IAED,OAAO;MACL+I,aAAa,EAAEV,OAAO,CAAC3E,SAAS,CAACZ,MAAM;MACvCkG,YAAY;MAAE;MAAA,CAAAtJ,aAAA,GAAAoC,CAAA,WAAAuG,OAAO,CAAC1E,QAAQ,EAAEb,MAAM;MAAA;MAAA,CAAApD,aAAA,GAAAoC,CAAA,WAAI,CAAC;KAC5C;EACH;EAEA;;;EAGA,MAAMmH,cAAcA,CAACvG,QAAsB;IAAA;IAAAhD,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACzCqC,OAAO,CAACC,GAAG,CAAC,UAAUI,QAAQ,OAAO,CAAC;IAAA;IAAAhD,aAAA,GAAAM,CAAA;IACtC,MAAM,IAAI,CAAC2C,gBAAgB,CAACD,QAAQ,CAAC;IAAA;IAAAhD,aAAA,GAAAM,CAAA;IACrC,IAAI,CAAC4C,kBAAkB,EAAE;EAC3B;EAEA;;;EAGQG,wBAAwBA,CAAA;IAAA;IAAArD,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IAC9BqC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAAA;IAAA5C,aAAA,GAAAM,CAAA;IAClCqC,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACd,MAAM,CAACQ,eAAe,EAAE,CAAC;IAAA;IAAAtC,aAAA,GAAAM,CAAA;IACtDqC,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACd,MAAM,CAACS,gBAAgB,CAACxB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAAA;IAAAf,aAAA,GAAAM,CAAA;IAClEqC,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAACX,aAAa,CAAC6G,IAAI,EAAE,CAAC;IAAA;IAAA9I,aAAA,GAAAM,CAAA;IAElD,KAAK,MAAM0C,QAAQ,IAAI,IAAI,CAAClB,MAAM,CAACS,gBAAgB,EAAE;MACnD,MAAMiH,KAAK;MAAA;MAAA,CAAAxJ,aAAA,GAAAM,CAAA,QAAG,IAAI,CAAC8I,gBAAgB,CAACpG,QAAQ,CAAC;MAAA;MAAAhD,aAAA,GAAAM,CAAA;MAC7CqC,OAAO,CAACC,GAAG,CAAC,MAAMI,QAAQ,KAAKwG,KAAK,CAACH,aAAa,QAAQG,KAAK,CAACF,YAAY,KAAK,CAAC;IACpF;EACF;EAEA;;;EAGAG,OAAOA,CAAA;IAAA;IAAAzJ,aAAA,GAAAqC,CAAA;IAAArC,aAAA,GAAAM,CAAA;IACL,OAAO,IAAI,CAAC4B,aAAa;EAC3B", "ignoreList": []}