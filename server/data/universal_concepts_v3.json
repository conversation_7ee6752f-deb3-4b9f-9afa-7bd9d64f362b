[{"concept_id": "concept_warm", "concept_category": "emotions", "concept_subcategory": "positive_emotions", "semantic_vector": {"vector": [0.8, 0.6, 0.7, 0.9, 0.5, 0.4, 0.6, 0.7, 0.8, 0.5, 0.6, 0.7, 0.8, 0.4, 0.5, 0.6, 0.7, 0.8, 0.5, 0.6], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.95}, "abstraction_level": 0.7, "cultural_neutrality": 0.9, "cross_lingual_stability": 0.95, "cognitive_attributes": {"cognitive_load": 0.3, "memorability": 0.85, "emotional_valence": 0.8}, "concept_tags": ["comfort", "positive", "temperature", "emotion"], "related_concepts": ["concept_cozy", "concept_comfortable", "concept_pleasant"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_fresh", "concept_category": "emotions", "concept_subcategory": "artistic_emotions", "semantic_vector": {"vector": [0.7, 0.8, 0.6, 0.8, 0.6, 0.5, 0.7, 0.8, 0.7, 0.6, 0.7, 0.8, 0.7, 0.5, 0.6, 0.7, 0.8, 0.7, 0.6, 0.7], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.92}, "abstraction_level": 0.6, "cultural_neutrality": 0.8, "cross_lingual_stability": 0.88, "cognitive_attributes": {"cognitive_load": 0.4, "memorability": 0.8, "emotional_valence": 0.7}, "concept_tags": ["newness", "clean", "natural", "modern"], "related_concepts": ["concept_new", "concept_clean", "concept_natural"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_elegant", "concept_category": "emotions", "concept_subcategory": "artistic_emotions", "semantic_vector": {"vector": [0.9, 0.7, 0.8, 0.8, 0.7, 0.6, 0.8, 0.9, 0.8, 0.7, 0.8, 0.9, 0.8, 0.6, 0.7, 0.8, 0.9, 0.8, 0.7, 0.8], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.9}, "abstraction_level": 0.8, "cultural_neutrality": 0.7, "cross_lingual_stability": 0.85, "cognitive_attributes": {"cognitive_load": 0.5, "memorability": 0.85, "emotional_valence": 0.8}, "concept_tags": ["sophistication", "refinement", "beauty", "grace"], "related_concepts": ["concept_graceful", "concept_refined", "concept_sophisticated"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_healing", "concept_category": "emotions", "concept_subcategory": "modern_emotions", "semantic_vector": {"vector": [0.8, 0.9, 0.7, 0.9, 0.8, 0.7, 0.8, 0.9, 0.8, 0.7, 0.8, 0.9, 0.8, 0.7, 0.8, 0.9, 0.8, 0.7, 0.8, 0.9], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.88}, "abstraction_level": 0.6, "cultural_neutrality": 0.8, "cross_lingual_stability": 0.82, "cognitive_attributes": {"cognitive_load": 0.4, "memorability": 0.88, "emotional_valence": 0.9}, "concept_tags": ["recovery", "wellness", "therapeutic", "restoration"], "related_concepts": ["concept_therapeutic", "concept_restorative", "concept_wellness"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_poetic", "concept_category": "emotions", "concept_subcategory": "artistic_emotions", "semantic_vector": {"vector": [0.9, 0.8, 0.9, 0.7, 0.8, 0.9, 0.8, 0.7, 0.9, 0.8, 0.9, 0.7, 0.8, 0.9, 0.8, 0.7, 0.9, 0.8, 0.9, 0.7], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.85}, "abstraction_level": 0.9, "cultural_neutrality": 0.6, "cross_lingual_stability": 0.75, "cognitive_attributes": {"cognitive_load": 0.6, "memorability": 0.85, "emotional_valence": 0.7}, "concept_tags": ["literary", "artistic", "expressive", "traditional"], "related_concepts": ["concept_literary", "concept_artistic", "concept_expressive"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_adorable", "concept_category": "emotions", "concept_subcategory": "modern_emotions", "semantic_vector": {"vector": [0.8, 0.9, 0.6, 0.8, 0.9, 0.7, 0.6, 0.8, 0.9, 0.7, 0.6, 0.8, 0.9, 0.7, 0.6, 0.8, 0.9, 0.7, 0.6, 0.8], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.9}, "abstraction_level": 0.5, "cultural_neutrality": 0.8, "cross_lingual_stability": 0.85, "cognitive_attributes": {"cognitive_load": 0.3, "memorability": 0.85, "emotional_valence": 0.9}, "concept_tags": ["cuteness", "endearing", "lovable", "charming"], "related_concepts": ["concept_cute", "concept_charming", "concept_lovable"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_designer", "concept_category": "professions", "concept_subcategory": "creative_professions", "semantic_vector": {"vector": [0.5, 0.8, 0.6, 0.4, 0.7, 0.9, 0.8, 0.6, 0.5, 0.7, 0.8, 0.6, 0.4, 0.9, 0.7, 0.5, 0.6, 0.8, 0.7, 0.4], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.92}, "abstraction_level": 0.4, "cultural_neutrality": 0.9, "cross_lingual_stability": 0.95, "cognitive_attributes": {"cognitive_load": 0.3, "memorability": 0.88, "emotional_valence": 0.6}, "concept_tags": ["creativity", "profession", "visual", "planning"], "related_concepts": ["concept_creator", "concept_planner", "concept_artist"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_engineer", "concept_category": "professions", "concept_subcategory": "technical_professions", "semantic_vector": {"vector": [0.6, 0.7, 0.8, 0.5, 0.8, 0.9, 0.7, 0.6, 0.8, 0.7, 0.6, 0.8, 0.5, 0.9, 0.8, 0.6, 0.7, 0.8, 0.7, 0.5], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.95}, "abstraction_level": 0.4, "cultural_neutrality": 0.95, "cross_lingual_stability": 0.98, "cognitive_attributes": {"cognitive_load": 0.4, "memorability": 0.85, "emotional_valence": 0.5}, "concept_tags": ["technical", "problem-solving", "systematic", "professional"], "related_concepts": ["concept_technician", "concept_builder", "concept_problem_solver"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_artist", "concept_category": "professions", "concept_subcategory": "creative_professions", "semantic_vector": {"vector": [0.8, 0.9, 0.7, 0.6, 0.8, 0.7, 0.9, 0.8, 0.7, 0.8, 0.9, 0.7, 0.6, 0.8, 0.7, 0.9, 0.8, 0.7, 0.8, 0.9], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.9}, "abstraction_level": 0.5, "cultural_neutrality": 0.8, "cross_lingual_stability": 0.88, "cognitive_attributes": {"cognitive_load": 0.4, "memorability": 0.88, "emotional_valence": 0.7}, "concept_tags": ["creativity", "expression", "aesthetic", "cultural"], "related_concepts": ["concept_creator", "concept_expressive", "concept_aesthetic"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_creative", "concept_category": "characteristics", "concept_subcategory": "personality_traits", "semantic_vector": {"vector": [0.7, 0.9, 0.8, 0.6, 0.8, 0.7, 0.9, 0.8, 0.7, 0.8, 0.9, 0.8, 0.6, 0.7, 0.8, 0.9, 0.8, 0.7, 0.8, 0.9], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.88}, "abstraction_level": 0.6, "cultural_neutrality": 0.9, "cross_lingual_stability": 0.92, "cognitive_attributes": {"cognitive_load": 0.4, "memorability": 0.88, "emotional_valence": 0.8}, "concept_tags": ["innovation", "originality", "imagination", "artistic"], "related_concepts": ["concept_innovative", "concept_original", "concept_imaginative"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_brilliant", "concept_category": "characteristics", "concept_subcategory": "intellectual_traits", "semantic_vector": {"vector": [0.9, 0.8, 0.9, 0.7, 0.8, 0.9, 0.8, 0.7, 0.9, 0.8, 0.9, 0.7, 0.8, 0.9, 0.8, 0.7, 0.9, 0.8, 0.9, 0.7], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.9}, "abstraction_level": 0.7, "cultural_neutrality": 0.8, "cross_lingual_stability": 0.85, "cognitive_attributes": {"cognitive_load": 0.5, "memorability": 0.9, "emotional_valence": 0.8}, "concept_tags": ["intelligence", "excellence", "outstanding", "impressive"], "related_concepts": ["concept_intelligent", "concept_excellent", "concept_outstanding"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}, {"concept_id": "concept_gentle", "concept_category": "characteristics", "concept_subcategory": "personality_traits", "semantic_vector": {"vector": [0.8, 0.7, 0.6, 0.9, 0.7, 0.6, 0.8, 0.7, 0.6, 0.9, 0.8, 0.7, 0.6, 0.9, 0.7, 0.6, 0.8, 0.7, 0.6, 0.9], "model": "universal_semantic_v3", "dimension": 20, "confidence": 0.92}, "abstraction_level": 0.6, "cultural_neutrality": 0.9, "cross_lingual_stability": 0.95, "cognitive_attributes": {"cognitive_load": 0.3, "memorability": 0.85, "emotional_valence": 0.9}, "concept_tags": ["kindness", "softness", "caring", "tenderness"], "related_concepts": ["concept_kind", "concept_soft", "concept_caring"], "created_at": "2025-06-24T08:25:00.000Z", "updated_at": "2025-06-24T08:25:00.000Z", "version": "3.0.0"}]