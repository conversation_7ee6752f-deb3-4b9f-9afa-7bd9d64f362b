{"metadata": {"version": "3.0.0", "language": "de-DE", "language_name": "De<PERSON>ch", "created_at": "2025-06-24T00:00:00Z", "updated_at": "2025-06-24T00:00:00Z", "total_morphemes": 8, "categories": ["emotion", "nature", "profession", "abstract"], "description": "德语语素数据集 v3.0 - 支持欧洲语言特性的多维度语素库"}, "morphemes": [{"morpheme_id": "de_freude_001", "text": "<PERSON><PERSON>", "alternative_forms": ["Glück", "<PERSON><PERSON>", "Fröhlichkeit"], "universal_concept_id": "emotion_joy_001", "concept_category": "emotion", "semantic_vector": [0.9, 0.8, 0.1, 0.2, 0.7, 0.85, 0.8, 0.6], "phonetic_features": {"ipa_transcription": "[ˈfʁɔʏdə]", "syllable_count": 2, "stress_position": 1, "has_umlauts": true, "compound_potential": 0.9, "romanization": "<PERSON><PERSON>"}, "morphological_info": {"root": "freud", "prefixes": [], "suffixes": ["e"], "inflection_info": {"gender": "feminine", "number": "singular", "case": "nominative"}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.9}, "word_class": "noun"}, "cultural_context": {"formality": 0.7, "regionality": 0.8, "traditionality": 0.85, "modernity": 0.7, "aesthetic_value": 0.8, "cultural_significance": 0.8, "precision": 0.8, "emotional_depth": 0.85}, "usage_frequency": 0.85, "semantic_rarity": 0.3, "register_level": "neutral", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_liebe_002", "text": "<PERSON><PERSON>", "alternative_forms": ["Zuneigung", "Verliebtheit", "Herzlichkeit"], "universal_concept_id": "emotion_love_002", "concept_category": "emotion", "semantic_vector": [0.95, 0.9, 0.2, 0.1, 0.8, 0.9, 0.85, 0.7], "phonetic_features": {"ipa_transcription": "[ˈliːbə]", "syllable_count": 2, "stress_position": 1, "has_umlauts": false, "compound_potential": 0.95, "romanization": "<PERSON><PERSON>"}, "morphological_info": {"root": "lieb", "prefixes": [], "suffixes": ["e"], "inflection_info": {"gender": "feminine", "number": "singular", "case": "nominative"}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.95}, "word_class": "noun"}, "cultural_context": {"formality": 0.6, "regionality": 0.9, "traditionality": 0.95, "modernity": 0.8, "aesthetic_value": 0.9, "cultural_significance": 0.95, "precision": 0.85, "emotional_depth": 0.95, "romantic_quality": 0.9}, "usage_frequency": 0.9, "semantic_rarity": 0.2, "register_level": "neutral", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_hoffnung_003", "text": "<PERSON><PERSON><PERSON><PERSON>", "alternative_forms": ["Zuversicht", "Erwart<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "universal_concept_id": "emotion_hope_003", "concept_category": "emotion", "semantic_vector": [0.8, 0.75, 0.3, 0.2, 0.9, 0.8, 0.75, 0.8], "phonetic_features": {"ipa_transcription": "[ˈhɔfnʊŋ]", "syllable_count": 2, "stress_position": 1, "has_umlauts": false, "compound_potential": 0.8, "romanization": "<PERSON><PERSON><PERSON><PERSON>"}, "morphological_info": {"root": "hoff", "prefixes": [], "suffixes": ["nung"], "inflection_info": {"gender": "feminine", "number": "singular", "case": "nominative"}, "derivation_info": {"base_word": "hoffen", "derivation_type": "nominalization", "productivity": 0.8}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.8}, "word_class": "noun"}, "cultural_context": {"formality": 0.8, "regionality": 0.7, "traditionality": 0.9, "modernity": 0.8, "aesthetic_value": 0.8, "cultural_significance": 0.9, "precision": 0.9, "philosophical_depth": 0.9, "emotional_depth": 0.85}, "usage_frequency": 0.75, "semantic_rarity": 0.4, "register_level": "formal", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_sonne_004", "text": "<PERSON><PERSON>", "alternative_forms": ["Gestirn", "Stern", "Licht"], "universal_concept_id": "nature_sun_001", "concept_category": "nature", "semantic_vector": [0.7, 0.6, 0.9, 0.8, 0.5, 0.8, 0.8, 0.9], "phonetic_features": {"ipa_transcription": "[ˈzɔnə]", "syllable_count": 2, "stress_position": 1, "has_umlauts": false, "compound_potential": 0.95, "romanization": "<PERSON><PERSON>"}, "morphological_info": {"root": "sonn", "prefixes": [], "suffixes": ["e"], "inflection_info": {"gender": "feminine", "number": "singular", "case": "nominative"}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.95}, "word_class": "noun"}, "cultural_context": {"formality": 0.5, "regionality": 0.8, "traditionality": 0.9, "modernity": 0.8, "aesthetic_value": 0.85, "cultural_significance": 0.9, "precision": 0.9, "natural_connection": 0.95, "mythological_significance": 0.8}, "usage_frequency": 0.9, "semantic_rarity": 0.2, "register_level": "neutral", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_mond_005", "text": "Mond", "alternative_forms": ["Trabant", "<PERSON><PERSON><PERSON>", "Nachtgestirn"], "universal_concept_id": "nature_moon_002", "concept_category": "nature", "semantic_vector": [0.6, 0.7, 0.8, 0.9, 0.4, 0.8, 0.75, 0.8], "phonetic_features": {"ipa_transcription": "[moːnt]", "syllable_count": 1, "stress_position": 1, "has_umlauts": false, "compound_potential": 0.9, "romanization": "Mond"}, "morphological_info": {"root": "mond", "prefixes": [], "suffixes": [], "inflection_info": {"gender": "masculine", "number": "singular", "case": "nominative"}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.9}, "word_class": "noun"}, "cultural_context": {"formality": 0.6, "regionality": 0.8, "traditionality": 0.9, "modernity": 0.7, "aesthetic_value": 0.9, "cultural_significance": 0.85, "precision": 0.9, "mystical_quality": 0.8, "poetic_quality": 0.85, "natural_connection": 0.9}, "usage_frequency": 0.8, "semantic_rarity": 0.3, "register_level": "neutral", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_lehrer_006", "text": "<PERSON><PERSON><PERSON>", "alternative_forms": ["Pädagoge", "Dozent", "<PERSON><PERSON><PERSON><PERSON>"], "universal_concept_id": "profession_teacher_001", "concept_category": "profession", "semantic_vector": [0.7, 0.8, 0.6, 0.5, 0.9, 0.8, 0.75, 0.9], "phonetic_features": {"ipa_transcription": "[ˈleːʁɐ]", "syllable_count": 2, "stress_position": 1, "has_umlauts": false, "compound_potential": 0.9, "romanization": "<PERSON><PERSON><PERSON>"}, "morphological_info": {"root": "lehr", "prefixes": [], "suffixes": ["er"], "inflection_info": {"gender": "masculine", "number": "singular", "case": "nominative"}, "derivation_info": {"base_word": "lehren", "derivation_type": "agentive", "productivity": 0.9}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.9}, "word_class": "noun"}, "cultural_context": {"formality": 0.8, "regionality": 0.7, "traditionality": 0.9, "modernity": 0.8, "aesthetic_value": 0.7, "cultural_significance": 0.9, "precision": 0.9, "professional_respect": 0.9, "educational_authority": 0.95}, "usage_frequency": 0.85, "semantic_rarity": 0.3, "register_level": "formal", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_kuenstler_007", "text": "<PERSON><PERSON><PERSON><PERSON>", "alternative_forms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Maler", "<PERSON><PERSON><PERSON>"], "universal_concept_id": "profession_artist_002", "concept_category": "profession", "semantic_vector": [0.8, 0.9, 0.7, 0.6, 0.8, 0.9, 0.8, 0.7], "phonetic_features": {"ipa_transcription": "[ˈkʏnstlɐ]", "syllable_count": 2, "stress_position": 1, "has_umlauts": true, "compound_potential": 0.85, "romanization": "<PERSON><PERSON><PERSON>"}, "morphological_info": {"root": "kunst", "prefixes": [], "suffixes": ["ler"], "inflection_info": {"gender": "masculine", "number": "singular", "case": "nominative"}, "derivation_info": {"base_word": "<PERSON><PERSON>", "derivation_type": "agentive", "productivity": 0.8}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.85}, "word_class": "noun"}, "cultural_context": {"formality": 0.7, "regionality": 0.6, "traditionality": 0.8, "modernity": 0.9, "aesthetic_value": 0.95, "cultural_significance": 0.9, "precision": 0.8, "creative_expression": 0.95, "intellectual_prestige": 0.8}, "usage_frequency": 0.75, "semantic_rarity": 0.4, "register_level": "neutral", "created_at": 1719187200, "updated_at": 1719187200}, {"morpheme_id": "de_freiheit_008", "text": "Freiheit", "alternative_forms": ["Unabhängigkeit", "Autonomie", "Be<PERSON><PERSON>iung"], "universal_concept_id": "abstract_freedom_001", "concept_category": "abstract", "semantic_vector": [0.9, 0.8, 0.5, 0.4, 0.9, 0.8, 0.8, 0.8], "phonetic_features": {"ipa_transcription": "[ˈfʁaɪhaɪt]", "syllable_count": 2, "stress_position": 1, "has_umlauts": false, "compound_potential": 0.9, "romanization": "Freiheit"}, "morphological_info": {"root": "frei", "prefixes": [], "suffixes": ["heit"], "inflection_info": {"gender": "feminine", "number": "singular", "case": "nominative"}, "derivation_info": {"base_word": "frei", "derivation_type": "nominalization", "productivity": 0.9}, "compound_info": {"can_be_first_element": true, "can_be_second_element": true, "productivity": 0.9}, "word_class": "noun"}, "cultural_context": {"formality": 0.9, "regionality": 0.6, "traditionality": 0.8, "modernity": 0.9, "aesthetic_value": 0.8, "cultural_significance": 0.95, "precision": 0.95, "philosophical_depth": 0.95, "political_significance": 0.9, "historical_weight": 0.9}, "usage_frequency": 0.8, "semantic_rarity": 0.4, "register_level": "formal", "created_at": 1719187200, "updated_at": 1719187200}]}