{"metadata": {"version": "1.0.0", "created_at": "2025-06-22", "description": "namer-v6 MVP阶段核心创意模式", "total_count": 5, "based_on": "469个用户名样例分析结果"}, "patterns": [{"id": "pattern_001", "name": "谐音双关", "description": "基于谐音的双关语创意，最受欢迎的模式(23.5%)", "template": "{adjective}的{profession}", "weight": 0.235, "examples": ["专业的退堂鼓艺术家", "资深的摸鱼工程师", "优雅的划水大师"], "rules": [{"type": "category_required", "constraint": ["characteristics", "professions"], "weight": 1.0}, {"type": "quality_threshold", "constraint": 0.7, "weight": 0.8}], "cultural_preference": ["modern", "neutral"], "min_quality_score": 0.7}, {"id": "pattern_002", "name": "形容词+名词", "description": "简单直接的形容词修饰名词结构", "template": "{adjective}{noun}", "weight": 0.2, "examples": ["温暖诗人", "清新设计师", "优雅艺术家"], "rules": [{"type": "category_required", "constraint": ["emotions", "professions"], "weight": 1.0}, {"type": "morpheme_count", "constraint": 2, "weight": 0.9}], "cultural_preference": ["neutral"], "min_quality_score": 0.6}, {"id": "pattern_003", "name": "文艺复合", "description": "具有文艺气息的复合词结构", "template": "{emotion}{concept}", "weight": 0.18, "examples": ["诗意生活", "温暖时光", "清新世界"], "rules": [{"type": "category_required", "constraint": ["emotions", "concepts"], "weight": 1.0}, {"type": "cultural_match", "constraint": "artistic", "weight": 0.8}], "cultural_preference": ["ancient", "neutral"], "min_quality_score": 0.75}, {"id": "pattern_004", "name": "可爱萌系", "description": "现代网络文化中的可爱风格", "template": "小{adjective}", "weight": 0.15, "examples": ["小可爱", "小温暖", "小清新"], "rules": [{"type": "category_required", "constraint": ["emotions"], "weight": 1.0}, {"type": "cultural_match", "constraint": "modern", "weight": 0.9}], "cultural_preference": ["modern"], "min_quality_score": 0.65}, {"id": "pattern_005", "name": "职业特色", "description": "突出职业特色的创意组合", "template": "{characteristic}的{profession}", "weight": 0.215, "examples": ["聪明的工程师", "优秀的设计师", "治愈的艺术家"], "rules": [{"type": "category_required", "constraint": ["characteristics", "professions"], "weight": 1.0}, {"type": "quality_threshold", "constraint": 0.65, "weight": 0.7}], "cultural_preference": ["modern", "neutral"], "min_quality_score": 0.65}]}