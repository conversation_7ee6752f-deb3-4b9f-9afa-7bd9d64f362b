/**
 * Jest 测试配置
 * 
 * 配置TypeScript支持、测试覆盖率、测试环境等
 */

export default {
  // 测试环境
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',

  // 模块解析
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  
  // TypeScript 配置
  globals: {
    'ts-jest': {
      useESM: true,
      tsconfig: {
        module: 'ESNext',
        target: 'ES2022',
        moduleResolution: 'node',
        allowSyntheticDefaultImports: true,
        esModuleInterop: true,
        strict: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true
      }
    }
  },

  // 测试文件匹配模式
  testMatch: [
    '**/test/**/*.test.ts',
    '**/test/**/*.spec.ts'
  ],

  // 忽略的文件和目录
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/'
  ],

  // 覆盖率配置
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],

  // 覆盖率收集范围
  collectCoverageFrom: [
    'core/**/*.ts',
    'types/**/*.ts',
    'config/**/*.ts',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/test/**',
    '!**/dist/**'
  ],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85
    },
    // 核心模块要求更高覆盖率
    './core/engines/': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './core/multilingual/': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },

  // 测试设置 - 暂时禁用setup文件
  // setupFilesAfterEnv: ['<rootDir>/test/setup.ts'],
  
  // 测试超时
  testTimeout: 30000,

  // 详细输出
  verbose: true,

  // 错误报告
  errorOnDeprecated: true,
  
  // 并行测试
  maxWorkers: '50%',

  // 缓存
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  // 监听模式配置
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/dist/'
  ],

  // 报告器配置
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './coverage/html-report',
        filename: 'test-report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'namer-v6 测试报告'
      }
    ]
  ],

  // 全局变量
  globals: {
    __TEST_ENV__: true
  }
}
