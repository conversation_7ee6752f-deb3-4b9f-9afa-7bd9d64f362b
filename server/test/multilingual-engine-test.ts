/**
 * 多语种生成引擎测试脚本
 * 
 * 测试多语种用户名生成功能，验证v3.0多语种架构的集成效果
 * 
 * <AUTHOR> team
 * @version 3.0.0
 * @created 2025-06-24
 */

import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine.js'
import { LanguageCode } from '../types/multilingual.js'
import type { GenerationContext } from '../types/core.js'

// ============================================================================
// 测试配置
// ============================================================================

const TEST_LANGUAGES = [LanguageCode.ZH_CN] // 暂时只测试中文，避免数据格式冲突
const TEST_COUNT = 3

// 测试上下文
const testContext: GenerationContext = {
  cultural_preference: 'modern' as any,
  style_preference: 'elegant',
  creativity_level: 0.8,
  quality_threshold: 0.7,
  context_keywords: ['test', 'multilingual']
}

// ============================================================================
// 测试函数
// ============================================================================

/**
 * 主测试函数
 */
async function runMultilingualEngineTest(): Promise<void> {
  console.log('🌍 开始多语种生成引擎测试...\n')

  const engine = new CoreGenerationEngine()

  try {
    // 1. 初始化引擎
    console.log('📋 步骤1: 初始化多语种生成引擎')
    await engine.initialize()
    
    if (!engine.isReady()) {
      throw new Error('引擎初始化失败')
    }
    console.log('✅ 引擎初始化成功\n')

    // 2. 检查多语种支持
    console.log('📋 步骤2: 检查多语种支持状态')
    const supportedLanguages = engine.getSupportedLanguages()
    const multilingualStats = engine.getMultilingualStats()
    
    console.log(`支持的语言: ${supportedLanguages.join(', ')}`)
    console.log(`多语种就绪状态: ${multilingualStats.isMultilingualReady}`)
    console.log(`总概念数: ${multilingualStats.totalConcepts}`)
    
    for (const [language, stats] of Object.entries(multilingualStats.languageStats)) {
      console.log(`  ${language}: ${stats.morphemeCount}个语素, ${stats.conceptCount}个概念`)
    }
    console.log()

    // 3. 测试各语言生成
    for (const language of TEST_LANGUAGES) {
      await testLanguageGeneration(engine, language, testContext)
    }

    // 4. 性能测试
    await performanceTest(engine)

    // 5. 质量对比测试
    await qualityComparisonTest(engine)

    console.log('🎉 多语种生成引擎测试完成！')

  } catch (error) {
    console.error('❌ 测试失败:', error)
    throw error
  } finally {
    engine.destroy()
  }
}

/**
 * 测试特定语言的生成
 */
async function testLanguageGeneration(
  engine: CoreGenerationEngine,
  language: LanguageCode,
  context: GenerationContext
): Promise<void> {
  console.log(`📋 步骤3.${language}: 测试${language}用户名生成`)

  try {
    if (!engine.isLanguageSupported(language)) {
      console.log(`⚠️ ${language}语言不支持，跳过测试\n`)
      return
    }

    const startTime = Date.now()
    const results = await engine.generateMultilingual(context, TEST_COUNT, language)
    const generationTime = Date.now() - startTime

    console.log(`✅ ${language}生成完成: ${results.length}个用户名, 耗时${generationTime}ms`)
    
    // 输出生成结果
    results.forEach((result, index) => {
      console.log(`  ${index + 1}. "${result.text}" (${result.pattern})`)
      console.log(`     质量评分: ${(result.quality_score.overall * 100).toFixed(1)}%`)
      console.log(`     创意性: ${(result.quality_score.dimensions.creativity * 100).toFixed(1)}%`)
      console.log(`     记忆性: ${(result.quality_score.dimensions.memorability * 100).toFixed(1)}%`)
      console.log(`     文化适配: ${(result.quality_score.dimensions.cultural_fit * 100).toFixed(1)}%`)
      console.log(`     解释: ${result.explanation}`)
      console.log()
    })

    // 统计分析
    const avgQuality = results.reduce((sum, r) => sum + r.quality_score.overall, 0) / results.length
    const avgCreativity = results.reduce((sum, r) => sum + r.quality_score.dimensions.creativity, 0) / results.length
    const avgMemorability = results.reduce((sum, r) => sum + r.quality_score.dimensions.memorability, 0) / results.length
    const avgCulturalFit = results.reduce((sum, r) => sum + r.quality_score.dimensions.cultural_fit, 0) / results.length

    console.log(`📊 ${language}生成统计:`)
    console.log(`   平均质量: ${(avgQuality * 100).toFixed(1)}%`)
    console.log(`   平均创意性: ${(avgCreativity * 100).toFixed(1)}%`)
    console.log(`   平均记忆性: ${(avgMemorability * 100).toFixed(1)}%`)
    console.log(`   平均文化适配: ${(avgCulturalFit * 100).toFixed(1)}%`)
    console.log()

  } catch (error) {
    console.error(`❌ ${language}生成测试失败:`, error)
    console.log()
  }
}

/**
 * 性能测试
 */
async function performanceTest(engine: CoreGenerationEngine): Promise<void> {
  console.log('📋 步骤4: 性能测试')

  const performanceResults: Array<{
    language: LanguageCode
    count: number
    time: number
    avgTime: number
  }> = []

  for (const language of TEST_LANGUAGES) {
    if (!engine.isLanguageSupported(language)) continue

    const startTime = Date.now()
    const results = await engine.generateMultilingual(testContext, 5, language)
    const totalTime = Date.now() - startTime
    const avgTime = totalTime / results.length

    performanceResults.push({
      language,
      count: results.length,
      time: totalTime,
      avgTime
    })
  }

  console.log('📊 性能测试结果:')
  performanceResults.forEach(result => {
    console.log(`  ${result.language}: ${result.count}个用户名, 总耗时${result.time}ms, 平均${result.avgTime.toFixed(1)}ms/个`)
  })
  console.log()
}

/**
 * 质量对比测试
 */
async function qualityComparisonTest(engine: CoreGenerationEngine): Promise<void> {
  console.log('📋 步骤5: 跨语言质量对比测试')

  const qualityResults: Array<{
    language: LanguageCode
    avgQuality: number
    avgCreativity: number
    avgMemorability: number
    avgCulturalFit: number
  }> = []

  for (const language of TEST_LANGUAGES) {
    if (!engine.isLanguageSupported(language)) continue

    const results = await engine.generateMultilingual(testContext, 5, language)
    
    const avgQuality = results.reduce((sum, r) => sum + r.quality_score.overall, 0) / results.length
    const avgCreativity = results.reduce((sum, r) => sum + r.quality_score.dimensions.creativity, 0) / results.length
    const avgMemorability = results.reduce((sum, r) => sum + r.quality_score.dimensions.memorability, 0) / results.length
    const avgCulturalFit = results.reduce((sum, r) => sum + r.quality_score.dimensions.cultural_fit, 0) / results.length

    qualityResults.push({
      language,
      avgQuality,
      avgCreativity,
      avgMemorability,
      avgCulturalFit
    })
  }

  console.log('📊 跨语言质量对比:')
  console.log('语言\t\t总体质量\t创意性\t\t记忆性\t\t文化适配')
  console.log('─'.repeat(70))
  
  qualityResults.forEach(result => {
    console.log(
      `${result.language}\t\t${(result.avgQuality * 100).toFixed(1)}%\t\t${(result.avgCreativity * 100).toFixed(1)}%\t\t${(result.avgMemorability * 100).toFixed(1)}%\t\t${(result.avgCulturalFit * 100).toFixed(1)}%`
    )
  })
  console.log()

  // 分析最佳表现语言
  const bestQuality = qualityResults.reduce((best, current) => 
    current.avgQuality > best.avgQuality ? current : best
  )
  const bestCreativity = qualityResults.reduce((best, current) => 
    current.avgCreativity > best.avgCreativity ? current : best
  )

  console.log(`🏆 最佳质量语言: ${bestQuality.language} (${(bestQuality.avgQuality * 100).toFixed(1)}%)`)
  console.log(`🎨 最佳创意语言: ${bestCreativity.language} (${(bestCreativity.avgCreativity * 100).toFixed(1)}%)`)
  console.log()
}

// ============================================================================
// 执行测试
// ============================================================================

if (typeof import.meta !== 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  runMultilingualEngineTest()
    .then(() => {
      console.log('✅ 所有测试通过')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error)
      process.exit(1)
    })
}

export { runMultilingualEngineTest }
