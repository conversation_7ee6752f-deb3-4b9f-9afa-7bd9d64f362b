/**
 * 质量评估系统测试脚本
 * 
 * 测试新的8维度质量评估体系和精确算法
 * 
 * @fileoverview 质量评估测试
 * @version 2.0.0
 * @since 2025-06-23
 */

import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import { CulturalContext } from '../types/core'
import type { StylePreference, GenerationContext } from '../types/core'

/**
 * 测试质量评估系统
 */
async function testQualityEvaluation() {
  console.log('\n🧪 测试8维度质量评估系统...')
  
  try {
    const engine = new CoreGenerationEngine()
    await engine.initialize()
    
    // 测试不同类型的生成上下文
    const testContexts = [
      {
        name: '现代专业风格',
        context: {
          cultural_preference: CulturalContext.MODERN,
          style_preference: 'cool' as StylePreference,
          creativity_level: 0.7,
          quality_threshold: 0.8
        }
      },
      {
        name: '传统文艺风格',
        context: {
          cultural_preference: CulturalContext.ANCIENT,
          style_preference: 'artistic' as StylePreference,
          creativity_level: 0.9,
          quality_threshold: 0.7
        }
      },
      {
        name: '中性平衡风格',
        context: {
          cultural_preference: CulturalContext.NEUTRAL,
          style_preference: 'elegant' as StylePreference,
          creativity_level: 0.6,
          quality_threshold: 0.75
        }
      }
    ]
    
    for (const testCase of testContexts) {
      console.log(`\n📊 测试场景: ${testCase.name}`)
      
      // 生成用户名
      const results = await engine.generate(testCase.context, 3)
      
      if (results.length === 0) {
        console.warn(`⚠️ ${testCase.name}: 未生成任何用户名`)
        continue
      }
      
      // 分析质量评估结果
      for (let i = 0; i < results.length; i++) {
        const result = results[i]
        const quality = result.quality_score
        
        console.log(`\n   用户名 ${i + 1}: "${result.text}"`)
        console.log(`   综合评分: ${quality.overall.toFixed(3)} (置信度: ${quality.confidence.toFixed(3)})`)
        
        // 显示8维度详细评分
        console.log('   维度评分:')
        console.log(`     创意性: ${quality.dimensions.creativity.toFixed(3)}`)
        console.log(`     记忆性: ${quality.dimensions.memorability.toFixed(3)}`)
        console.log(`     文化适配: ${quality.dimensions.cultural_fit.toFixed(3)}`)
        console.log(`     独特性: ${quality.dimensions.uniqueness.toFixed(3)}`)
        console.log(`     发音友好: ${quality.dimensions.pronunciation.toFixed(3)}`)
        console.log(`     语义连贯: ${quality.dimensions.semantic_coherence.toFixed(3)}`)
        console.log(`     美学吸引: ${quality.dimensions.aesthetic_appeal.toFixed(3)}`)
        console.log(`     实用性: ${quality.dimensions.practical_usability.toFixed(3)}`)
        
        // 显示问题和建议
        if (quality.issues.length > 0) {
          console.log(`   发现问题: ${quality.issues.join(', ')}`)
        }
        if (quality.suggestions.length > 0) {
          console.log(`   改进建议: ${quality.suggestions.join(', ')}`)
        }
        
        console.log(`   评估耗时: ${quality.evaluation_time}ms`)
        console.log(`   算法版本: ${quality.algorithm_version}`)
      }
      
      // 计算平均质量
      const avgQuality = results.reduce((sum, r) => sum + r.quality_score.overall, 0) / results.length
      console.log(`\n   平均质量: ${avgQuality.toFixed(3)}`)
      
      // 分析维度分布
      const dimensionAvgs = {
        creativity: results.reduce((sum, r) => sum + r.quality_score.dimensions.creativity, 0) / results.length,
        memorability: results.reduce((sum, r) => sum + r.quality_score.dimensions.memorability, 0) / results.length,
        cultural_fit: results.reduce((sum, r) => sum + r.quality_score.dimensions.cultural_fit, 0) / results.length,
        uniqueness: results.reduce((sum, r) => sum + r.quality_score.dimensions.uniqueness, 0) / results.length,
        pronunciation: results.reduce((sum, r) => sum + r.quality_score.dimensions.pronunciation, 0) / results.length,
        semantic_coherence: results.reduce((sum, r) => sum + r.quality_score.dimensions.semantic_coherence, 0) / results.length,
        aesthetic_appeal: results.reduce((sum, r) => sum + r.quality_score.dimensions.aesthetic_appeal, 0) / results.length,
        practical_usability: results.reduce((sum, r) => sum + r.quality_score.dimensions.practical_usability, 0) / results.length
      }
      
      console.log('   维度平均分:')
      Object.entries(dimensionAvgs).forEach(([dim, avg]) => {
        console.log(`     ${dim}: ${avg.toFixed(3)}`)
      })
    }
    
    console.log('\n✅ 质量评估系统测试完成')
    
    // 获取引擎统计
    const stats = engine.getStats()
    console.log('\n📈 引擎统计信息:')
    console.log(`   总生成次数: ${stats.total_generations}`)
    console.log(`   平均生成时间: ${stats.avg_generation_time.toFixed(2)}ms`)
    console.log(`   成功率: ${(stats.success_rate * 100).toFixed(1)}%`)
    
    return true
    
  } catch (error) {
    console.error('❌ 质量评估系统测试失败:', error)
    throw error
  }
}

/**
 * 测试质量评估边界情况
 */
async function testQualityEvaluationEdgeCases() {
  console.log('\n🧪 测试质量评估边界情况...')
  
  try {
    const engine = new CoreGenerationEngine()
    await engine.initialize()
    
    // 测试极端情况
    const edgeCases = [
      {
        name: '高创意要求',
        context: {
          cultural_preference: CulturalContext.MODERN,
          style_preference: 'cool' as StylePreference,
          creativity_level: 1.0,
          quality_threshold: 0.9
        }
      },
      {
        name: '低质量阈值',
        context: {
          cultural_preference: CulturalContext.NEUTRAL,
          style_preference: 'elegant' as StylePreference,
          creativity_level: 0.3,
          quality_threshold: 0.3
        }
      },
      {
        name: '古典文化偏好',
        context: {
          cultural_preference: CulturalContext.ANCIENT,
          style_preference: 'elegant' as StylePreference,
          creativity_level: 0.8,
          quality_threshold: 0.8
        }
      }
    ]
    
    for (const testCase of edgeCases) {
      console.log(`\n📊 边界测试: ${testCase.name}`)
      
      const results = await engine.generate(testCase.context, 2)
      
      if (results.length > 0) {
        const avgQuality = results.reduce((sum, r) => sum + r.quality_score.overall, 0) / results.length
        console.log(`   生成数量: ${results.length}`)
        console.log(`   平均质量: ${avgQuality.toFixed(3)}`)
        console.log(`   示例: ${results[0].text}`)
      } else {
        console.log(`   ⚠️ 未能生成用户名`)
      }
    }
    
    console.log('\n✅ 边界情况测试完成')
    return true
    
  } catch (error) {
    console.error('❌ 边界情况测试失败:', error)
    throw error
  }
}

/**
 * 性能基准测试
 */
async function benchmarkQualityEvaluation() {
  console.log('\n🧪 质量评估性能基准测试...')
  
  try {
    const engine = new CoreGenerationEngine()
    await engine.initialize()
    
    const context = {
      cultural_preference: CulturalContext.MODERN,
      style_preference: 'cool' as StylePreference,
      creativity_level: 0.7,
      quality_threshold: 0.7
    }
    
    // 测试不同批量大小的性能
    const batchSizes = [1, 3, 5, 10]
    
    for (const batchSize of batchSizes) {
      const iterations = 5
      const times: number[] = []
      
      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now()
        const results = await engine.generate(context, batchSize)
        const endTime = Date.now()
        
        if (results.length > 0) {
          times.push(endTime - startTime)
        }
      }
      
      if (times.length > 0) {
        const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length
        const avgTimePerUsername = avgTime / batchSize
        
        console.log(`   批量大小 ${batchSize}: 平均${avgTime.toFixed(2)}ms (${avgTimePerUsername.toFixed(2)}ms/个)`)
      }
    }
    
    console.log('\n✅ 性能基准测试完成')
    return true
    
  } catch (error) {
    console.error('❌ 性能基准测试失败:', error)
    throw error
  }
}

/**
 * 运行所有质量评估测试
 */
async function runAllQualityTests() {
  console.log('🚀 开始质量评估系统测试...')
  
  try {
    await testQualityEvaluation()
    await testQualityEvaluationEdgeCases()
    await benchmarkQualityEvaluation()
    
    console.log('\n✅ 所有质量评估测试通过!')
    
  } catch (error) {
    console.error('\n❌ 质量评估测试失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllQualityTests().catch(error => {
    console.error('测试执行失败:', error)
    process.exit(1)
  })
}

export {
  testQualityEvaluation,
  testQualityEvaluationEdgeCases,
  benchmarkQualityEvaluation,
  runAllQualityTests
}
