/**
 * 东亚语言处理器测试用例
 * 
 * 测试日语、韩语语素处理的核心功能
 * 
 * @fileoverview 东亚语言处理器单元测试
 * @version 3.0.0
 * @since 2025-06-24
 */

import { describe, it, expect, beforeEach } from '@jest/globals'
import { EastAsianLanguageProcessor } from '../../core/multilingual/EastAsianLanguageProcessor'
import { LanguageCode, RegisterLevel } from '../../types/multilingual'
import type { LanguageSpecificMorpheme } from '../../types/multilingual'

describe('EastAsianLanguageProcessor', () => {
  let processor: EastAsianLanguageProcessor

  beforeEach(() => {
    processor = new EastAsianLanguageProcessor()
  })

  describe('语言配置管理', () => {
    it('应该正确识别东亚语言', () => {
      expect(processor.isEastAsianLanguage(LanguageCode.JA_JP)).toBe(true)
      expect(processor.isEastAsianLanguage(LanguageCode.KO_KR)).toBe(true)
      expect(processor.isEastAsianLanguage(LanguageCode.ZH_CN)).toBe(true)
      expect(processor.isEastAsianLanguage(LanguageCode.EN_US)).toBe(false)
    })

    it('应该返回正确的日语配置', () => {
      const config = processor.getLanguageConfig(LanguageCode.JA_JP)
      expect(config).toBeDefined()
      expect(config?.language).toBe(LanguageCode.JA_JP)
      expect(config?.writing_systems).toContain('logographic')
      expect(config?.writing_systems).toContain('syllabic')
      expect(config?.uses_hanzi).toBe(true)
      expect(config?.has_honorifics).toBe(true)
      expect(config?.syllable_counting).toBe('mora')
    })

    it('应该返回正确的韩语配置', () => {
      const config = processor.getLanguageConfig(LanguageCode.KO_KR)
      expect(config).toBeDefined()
      expect(config?.language).toBe(LanguageCode.KO_KR)
      expect(config?.writing_systems).toContain('alphabetic')
      expect(config?.uses_hanzi).toBe(false)
      expect(config?.has_honorifics).toBe(true)
      expect(config?.syllable_counting).toBe('syllable')
    })
  })

  describe('日语语素处理', () => {
    const japaneseMorpheme: LanguageSpecificMorpheme = {
      morpheme_id: 'ja_test_001',
      concept_id: 'concept_test',
      language: LanguageCode.JA_JP,
      text: 'さくら',
      alternative_forms: ['桜', 'サクラ'],
      phonetic_features: {
        ipa_transcription: 'sakɯɾa',
        syllable_count: 3,
        phonetic_harmony: 0.95
      },
      morphological_info: {
        pos_tag: 'NOUN',
        morphological_type: 'root',
        prefixes: [],
        suffixes: []
      },
      syntactic_properties: {
        syntactic_function: ['subject', 'object'],
        collocation_constraints: ['美しい', '咲く'],
        grammatical_features: {
          animacy: 'inanimate',
          countability: 'countable'
        }
      },
      cultural_context: {
        traditionality: 0.95,
        modernity: 0.80,
        formality: 0.70,
        regionality: 0.90,
        religious_sensitivity: 0.1,
        age_appropriateness: ['all'],
        cultural_tags: ['nature', 'beauty'],
        seasonal_association: 'spring',
        aesthetic_value: 0.98
      },
      regional_variants: [],
      register_level: RegisterLevel.NEUTRAL,
      language_quality_scores: {
        naturalness: 0.98,
        fluency: 0.95,
        authenticity: 0.98,
        aesthetic_appeal: 0.98,
        pronunciation_ease: 0.90,
        memorability: 0.95,
        uniqueness: 0.85,
        practicality: 0.88
      },
      cultural_appropriateness: 0.98,
      native_speaker_rating: 0.96,
      usage_frequency: 0.85,
      popularity_trend: 0.90,
      created_at: '2025-06-24T09:30:00.000Z',
      updated_at: '2025-06-24T09:30:00.000Z',
      version: '3.0.0',
      source: 'test',
      validation_status: 'validated'
    }

    it('应该正确处理日语语素的假名信息', () => {
      const processed = processor.processJapaneseMorpheme(japaneseMorpheme)
      
      expect(processed.phonetic_features.mora_count).toBe(3)
      expect(processed.phonetic_features.romanization).toBeDefined()
      expect(processed.phonetic_features.accent_pattern).toBeDefined()
    })

    it('应该正确处理日语语素的汉字信息', () => {
      const processed = processor.processJapaneseMorpheme(japaneseMorpheme)
      
      expect(processed.morphological_info.kanji_info).toBeDefined()
      expect(processed.morphological_info.kanji_info?.kanji).toBe('桜')
      expect(processed.morphological_info.kanji_info?.stroke_count).toBeGreaterThan(0)
      expect(processed.morphological_info.kanji_info?.radical).toBeDefined()
    })

    it('应该正确处理日语敬语系统', () => {
      const honorificMorpheme = {
        ...japaneseMorpheme,
        text: 'せんせい',
        alternative_forms: ['先生', 'センセイ']
      }
      
      const processed = processor.processJapaneseMorpheme(honorificMorpheme)
      
      expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.8)
      expect(processed.cultural_context.traditionality).toBeGreaterThanOrEqual(0.85)
    })
  })

  describe('韩语语素处理', () => {
    const koreanMorpheme: LanguageSpecificMorpheme = {
      morpheme_id: 'ko_test_001',
      concept_id: 'concept_test',
      language: LanguageCode.KO_KR,
      text: '사랑',
      alternative_forms: ['愛', 'SARANG'],
      phonetic_features: {
        ipa_transcription: 'saɾaŋ',
        syllable_count: 2,
        phonetic_harmony: 0.92
      },
      morphological_info: {
        pos_tag: 'NOUN',
        morphological_type: 'root',
        prefixes: [],
        suffixes: []
      },
      syntactic_properties: {
        syntactic_function: ['subject', 'object'],
        collocation_constraints: ['깊은', '진실한'],
        grammatical_features: {
          animacy: 'abstract',
          countability: 'uncountable'
        }
      },
      cultural_context: {
        traditionality: 0.80,
        modernity: 0.95,
        formality: 0.70,
        regionality: 0.60,
        religious_sensitivity: 0.1,
        age_appropriateness: ['all'],
        cultural_tags: ['emotion', 'universal']
      },
      regional_variants: [],
      register_level: RegisterLevel.NEUTRAL,
      language_quality_scores: {
        naturalness: 0.95,
        fluency: 0.92,
        authenticity: 0.95,
        aesthetic_appeal: 0.90,
        pronunciation_ease: 0.90,
        memorability: 0.95,
        uniqueness: 0.75,
        practicality: 0.90
      },
      cultural_appropriateness: 0.95,
      native_speaker_rating: 0.93,
      usage_frequency: 0.90,
      popularity_trend: 0.92,
      created_at: '2025-06-24T09:45:00.000Z',
      updated_at: '2025-06-24T09:45:00.000Z',
      version: '3.0.0',
      source: 'test',
      validation_status: 'validated'
    }

    it('应该正确处理韩语语素的韩文信息', () => {
      const processed = processor.processKoreanMorpheme(koreanMorpheme)
      
      expect(processed.phonetic_features.hangul_structure).toBeDefined()
      expect(processed.phonetic_features.consonant_clusters).toBeDefined()
    })

    it('应该正确处理韩语语素的汉字词汇', () => {
      const processed = processor.processKoreanMorpheme(koreanMorpheme)
      
      // 사랑 对应汉字 愛
      expect(processed.morphological_info.hanja_info).toBeDefined()
      expect(processed.morphological_info.hanja_info?.hanja).toBe('愛')
    })

    it('应该正确处理韩语敬语系统', () => {
      const honorificMorpheme = {
        ...koreanMorpheme,
        text: '선생님',
        alternative_forms: ['先生님']
      }
      
      const processed = processor.processKoreanMorpheme(honorificMorpheme)
      
      expect(processed.cultural_context.formality).toBeGreaterThanOrEqual(0.8)
      expect(processed.cultural_context.modernity).toBeGreaterThanOrEqual(0.8)
    })
  })

  describe('语言特性分析', () => {
    it('应该正确计算音拍数', () => {
      // 私有方法测试需要通过公共接口
      const japaneseMorpheme: LanguageSpecificMorpheme = {
        morpheme_id: 'ja_test_mora',
        concept_id: 'concept_test',
        language: LanguageCode.JA_JP,
        text: 'こんにちは', // 5音拍
        alternative_forms: ['こんにちは'],
        phonetic_features: {
          ipa_transcription: 'konnitʃiwa',
          syllable_count: 5,
          phonetic_harmony: 0.9
        },
        morphological_info: {
          pos_tag: 'INTJ',
          morphological_type: 'phrase',
          prefixes: [],
          suffixes: []
        },
        syntactic_properties: {
          syntactic_function: ['interjection'],
          collocation_constraints: [],
          grammatical_features: {}
        },
        cultural_context: {
          traditionality: 0.8,
          modernity: 0.9,
          formality: 0.7,
          regionality: 0.5,
          religious_sensitivity: 0.1,
          age_appropriateness: ['all'],
          cultural_tags: ['greeting']
        },
        regional_variants: [],
        register_level: RegisterLevel.NEUTRAL,
        language_quality_scores: {
          naturalness: 0.95,
          fluency: 0.95,
          authenticity: 0.95,
          aesthetic_appeal: 0.8,
          pronunciation_ease: 0.9,
          memorability: 0.9,
          uniqueness: 0.7,
          practicality: 0.95
        },
        cultural_appropriateness: 0.95,
        native_speaker_rating: 0.95,
        usage_frequency: 0.95,
        popularity_trend: 0.9,
        created_at: '2025-06-24T09:30:00.000Z',
        updated_at: '2025-06-24T09:30:00.000Z',
        version: '3.0.0',
        source: 'test',
        validation_status: 'validated'
      }

      const processed = processor.processJapaneseMorpheme(japaneseMorpheme)
      expect(processed.phonetic_features.mora_count).toBe(5)
    })
  })
})
