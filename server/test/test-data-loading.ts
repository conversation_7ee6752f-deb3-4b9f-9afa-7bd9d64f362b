/**
 * 数据加载机制测试脚本
 * 
 * 测试新的数据加载器、验证器和语素仓库功能
 * 
 * @fileoverview 数据加载测试
 * @version 2.0.0
 * @since 2025-06-22
 */

import { DataLoader } from '../core/data/DataLoader'
import { DataValidator } from '../core/data/DataValidator'
import { MorphemeRepository } from '../core/repositories/MorphemeRepository'
import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import { CulturalContext, MorphemeCategory } from '../types/core'
import type { GenerationContext, StylePreference } from '../types/core'

/**
 * 测试数据加载器
 */
async function testDataLoader() {
  console.log('\n🧪 测试数据加载器...')
  
  try {
    const dataLoader = new DataLoader({
      enableHotReload: false, // 测试时禁用热重载
      enableValidation: true,
      enableCache: true
    })

    const result = await dataLoader.loadAll()
    
    console.log('✅ 数据加载测试通过:')
    console.log(`   加载语素: ${result.morphemes.length}个`)
    console.log(`   验证状态: ${result.validation.passed ? '通过' : '失败'}`)
    console.log(`   加载时间: ${result.stats.load_time}ms`)
    console.log(`   平均质量: ${result.stats.avg_quality.toFixed(3)}`)
    
    if (!result.validation.passed) {
      console.error('❌ 数据验证失败:', result.validation.errors.slice(0, 5))
    }
    
    return result
  } catch (error) {
    console.error('❌ 数据加载器测试失败:', error)
    throw error
  }
}

/**
 * 测试数据验证器
 */
async function testDataValidator() {
  console.log('\n🧪 测试数据验证器...')
  
  try {
    const dataLoader = new DataLoader()
    const loadResult = await dataLoader.loadAll()
    
    const validator = new DataValidator({
      strict_mode: true,
      skip_warnings: false
    })

    const validationResult = await validator.validate(loadResult.morphemes)
    
    console.log('✅ 数据验证器测试通过:')
    console.log(`   验证语素: ${validationResult.total_count}个`)
    console.log(`   通过数量: ${validationResult.passed_count}个`)
    console.log(`   失败数量: ${validationResult.failed_count}个`)
    console.log(`   错误数量: ${validationResult.errors.length}个`)
    console.log(`   警告数量: ${validationResult.warnings.length}个`)
    console.log(`   验证时间: ${validationResult.validation_time}ms`)
    
    if (validationResult.errors.length > 0) {
      console.warn('⚠️ 发现验证错误:', validationResult.errors.slice(0, 3))
    }
    
    return validationResult
  } catch (error) {
    console.error('❌ 数据验证器测试失败:', error)
    throw error
  }
}

/**
 * 测试语素仓库
 */
async function testMorphemeRepository() {
  console.log('\n🧪 测试语素仓库...')
  
  try {
    const repository = new MorphemeRepository()
    await repository.initialize()
    
    // 测试基础查询
    const emotions = repository.findByCategory(MorphemeCategory.EMOTIONS)
    const professions = repository.findByCategory(MorphemeCategory.PROFESSIONS)
    const modernMorphemes = repository.findByContext(CulturalContext.MODERN)
    
    // 测试采样
    const emotionSamples = repository.sampleByCategory(MorphemeCategory.EMOTIONS, 3)
    const contextSamples = repository.sampleByContext(CulturalContext.MODERN, 2)
    
    // 测试语义相似度
    if (emotions.length > 0) {
      const similar = repository.findSimilar(emotions[0], 0.5, 5)
      console.log(`   语义相似查询: 找到${similar.length}个相似语素`)
    }
    
    // 获取统计信息
    const stats = repository.getStats()
    
    console.log('✅ 语素仓库测试通过:')
    console.log(`   总语素数: ${stats.total}个`)
    console.log(`   情感类: ${emotions.length}个`)
    console.log(`   职业类: ${professions.length}个`)
    console.log(`   现代语境: ${modernMorphemes.length}个`)
    console.log(`   情感采样: ${emotionSamples.length}个`)
    console.log(`   语境采样: ${contextSamples.length}个`)
    console.log(`   平均质量: ${stats.avgQuality.toFixed(3)}`)
    console.log(`   平均频率: ${stats.avgFrequency.toFixed(3)}`)
    
    return repository
  } catch (error) {
    console.error('❌ 语素仓库测试失败:', error)
    throw error
  }
}

/**
 * 测试核心生成引擎
 */
async function testCoreGenerationEngine() {
  console.log('\n🧪 测试核心生成引擎...')
  
  try {
    const engine = new CoreGenerationEngine()
    await engine.initialize()
    
    // 测试生成功能
    const context: GenerationContext = {
      cultural_preference: CulturalContext.MODERN,
      style_preference: 'professional',
      creativity_level: 0.8,
      quality_threshold: 0.7
    }
    
    // 测试单个生成
    const singleResult = await engine.generate(context, 1)
    console.log(`   单个生成: ${singleResult.length > 0 ? singleResult[0].text : '失败'}`)
    
    // 测试批量生成 (1-10个)
    const batchResult = await engine.generate(context, 5)
    console.log(`   批量生成: ${batchResult.length}个用户名`)
    
    // 测试边界条件
    try {
      await engine.generate(context, 11) // 应该失败
      console.error('❌ 边界测试失败: 应该拒绝超过10个的请求')
    } catch (error) {
      console.log('✅ 边界测试通过: 正确拒绝了超过10个的请求')
    }
    
    // 获取引擎统计
    const engineStats = engine.getStats()
    
    console.log('✅ 核心生成引擎测试通过:')
    console.log(`   总生成次数: ${engineStats.morpheme_stats.total}`)
    console.log(`   平均生成时间: ${engineStats.morpheme_stats.avgQuality.toFixed(2)}ms`)
    console.log(`   引擎状态: ${engineStats.engine_status}`)
    
    // 显示生成的用户名
    console.log('   生成的用户名:')
    batchResult.forEach((username, index) => {
      console.log(`     ${index + 1}. ${username.text} (质量: ${username.quality_score.overall.toFixed(3)})`)
    })
    
    return engine
  } catch (error) {
    console.error('❌ 核心生成引擎测试失败:', error)
    throw error
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始数据加载机制测试...')
  
  try {
    // 测试各个组件
    await testDataLoader()
    await testDataValidator()
    await testMorphemeRepository()
    await testCoreGenerationEngine()
    
    console.log('\n✅ 所有测试通过! 数据加载机制工作正常')
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('测试执行失败:', error)
    process.exit(1)
  })
}

export {
  testDataLoader,
  testDataValidator,
  testMorphemeRepository,
  testCoreGenerationEngine,
  runAllTests
}
