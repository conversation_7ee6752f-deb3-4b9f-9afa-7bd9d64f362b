/**
 * TypeScript 类型安全测试
 * 
 * 确保TypeScript编译无错误，验证类型定义的正确性
 */

import { describe, it, expect } from '@jest/globals'
import type {
  Morpheme,
  GenerationContext,
  GeneratedUsername,
  QualityScore,
  MorphemeCategory
} from '../../types/core.js'
import { CulturalContext } from '../../types/core.js'
import {
  LanguageCode,
  RegisterLevel,
  ConceptCategory,
  SEMANTIC_VECTOR_DIMENSIONS
} from '../../types/multilingual.js'
import type {
  UniversalConcept,
  LanguageSpecificMorpheme,
  UniversalSemanticVector,
  MultiDimensionalCulturalContext,
  PhoneticFeatures,
  MorphologicalInfo,
  LanguageQualityScores
} from '../../types/multilingual.js'

describe('TypeScript 类型安全测试', () => {
  describe('核心类型定义', () => {
    it('应该正确定义 Morpheme 类型', () => {
      const morpheme: Morpheme = {
        id: 'test_001',
        text: '测试',
        category: 'emotions' as MorphemeCategory,
        subcategory: 'positive',
        cultural_context: CulturalContext.MODERN,
        usage_frequency: 0.8,
        quality_score: 0.9,
        semantic_vector: [0.1, 0.2, 0.3],
        tags: ['test', 'example'],
        language_properties: {
          syllable_count: 2,
          character_count: 2,
          phonetic_features: ['tone2', 'tone4'],
          morphological_type: 'compound',
          pronunciation: 'cè shì'
        },
        quality_metrics: {
          naturalness: 0.9,
          frequency: 0.8,
          acceptability: 0.9,
          aesthetic_appeal: 0.7
        },
        created_at: Date.now(),
        source: 'test',
        version: '2.0.0'
      }

      expect(morpheme.id).toBe('test_001')
      expect(morpheme.text).toBe('测试')
      expect(morpheme.quality_score).toBe(0.9)
    })

    it('应该正确定义 GenerationContext 类型', () => {
      const context: GenerationContext = {
        cultural_preference: CulturalContext.MODERN,
        style_preference: 'elegant',
        creativity_level: 0.8,
        quality_threshold: 0.7,
        patterns: ['形容词+名词'],
        exclude_patterns: ['小{adjective}']
      }

      expect(context.style_preference).toBe('elegant')
      expect(context.quality_threshold).toBe(0.7)
      expect(context.creativity_level).toBe(0.8)
    })

    it('应该正确定义 GeneratedUsername 类型', () => {
      const username: GeneratedUsername = {
        text: '优雅设计师',
        pattern: '形容词+名词',
        quality_score: {
          overall: 0.85,
          dimensions: {
            creativity: 0.9,
            memorability: 0.8,
            cultural_fit: 0.9,
            uniqueness: 0.7,
            pronunciation: 0.8,
            semantic_coherence: 0.8,
            aesthetic_appeal: 0.8,
            practical_usability: 0.8
          },
          confidence: 0.9,
          evaluation_time: 150,
          algorithm_version: 'v2.0',
          issues: [],
          suggestions: [],
          timestamp: Date.now()
        },
        explanation: '结合了优雅的特质和设计师的职业特色',
        components: [],
        metadata: {
          cultural_fit: 0.8,
          creativity: 0.7,
          memorability: 0.9,
          uniqueness: 0.7,
          generation_time: 150
        }
      }

      expect(username.text).toBe('优雅设计师')
      expect(username.quality_score.overall).toBe(0.85)
      expect(username.metadata.generation_time).toBe(150)
    })
  })

  describe('多语种类型定义', () => {
    it('应该正确定义 LanguageCode 枚举', () => {
      const languages: LanguageCode[] = [
        LanguageCode.ZH_CN,
        LanguageCode.EN_US,
        LanguageCode.JA_JP,
        LanguageCode.KO_KR,
        LanguageCode.ES_ES,
        LanguageCode.FR_FR,
        LanguageCode.DE_DE,
        LanguageCode.AR_SA
      ]

      expect(languages).toHaveLength(8)
      expect(languages).toContain(LanguageCode.ZH_CN)
      expect(languages).toContain(LanguageCode.EN_US)
    })

    it('应该正确定义 UniversalConcept 类型', () => {
      const concept: UniversalConcept = {
        concept_id: 'concept_001',
        semantic_vector: {
          vector: new Array(512).fill(0.1),
          model_version: 'mBERT-v1.0',
          confidence: 0.95,
          updated_at: '2025-06-24T00:00:00Z'
        },
        concept_category: ConceptCategory.EMOTIONS,
        abstraction_level: 0.6,
        cultural_neutrality: 0.8,
        cross_lingual_stability: 0.9,
        cognitive_load: 0.3,
        memorability_score: 0.8,
        emotional_valence: 0.7,
        cognitive_attributes: {
          memorability: 0.8,
          cognitive_load: 0.3,
          emotional_valence: 0.7
        },
        related_concepts: ['joy_001', 'satisfaction_001'],
        hierarchical_children: [],
        created_at: '2025-06-24T00:00:00Z',
        updated_at: '2025-06-24T00:00:00Z',
        version: '3.0.0'
      }

      expect(concept.concept_id).toBe('concept_001')
      expect(concept.semantic_vector.vector).toHaveLength(512)
      expect(concept.concept_category).toBe('emotions')
    })

    it('应该正确定义 LanguageSpecificMorpheme 类型', () => {
      const morpheme: LanguageSpecificMorpheme = {
        morpheme_id: 'zh_morpheme_001',
        concept_id: 'concept_001',
        language: LanguageCode.ZH_CN,
        text: '快乐',
        alternative_forms: ['愉快', '开心'],
        phonetic_features: {
          ipa_transcription: '/kuài.lè/',
          syllable_count: 2,
          tone_pattern: ['4', '4'],
          phonetic_harmony: 0.8
        },
        morphological_info: {
          pos_tag: 'ADJ',
          morphological_type: 'compound',
          root: '快乐',
          prefixes: [],
          suffixes: []
        },
        syntactic_properties: {
          syntactic_function: ['attributive', 'predicative'],
          collocation_constraints: ['快乐的', '很快乐'],
          grammatical_features: {}
        },
        cultural_context: {
          traditionality: 0.6,
          modernity: 0.8,
          formality: 0.5,
          regionality: 0.7,
          religious_sensitivity: 0.3,
          age_appropriateness: ['adult', 'teen', 'child'],
          cultural_tags: ['positive', 'emotion', 'wellbeing']
        },
        regional_variants: [],
        register_level: RegisterLevel.NEUTRAL,
        language_quality_scores: {
          naturalness: 0.9,
          fluency: 0.9,
          authenticity: 0.95,
          aesthetic_appeal: 0.8,
          pronunciation_ease: 0.8,
          memorability: 0.9,
          uniqueness: 0.6,
          practicality: 0.9
        },
        cultural_appropriateness: 0.9,
        native_speaker_rating: 0.95,
        usage_frequency: 0.8,
        popularity_trend: 0.1,
        created_at: '2025-06-24T00:00:00Z',
        updated_at: '2025-06-24T00:00:00Z',
        version: '3.0.0',
        source: 'native_corpus',
        validation_status: 'validated'
      }

      expect(morpheme.morpheme_id).toBe('zh_morpheme_001')
      expect(morpheme.language).toBe(LanguageCode.ZH_CN)
      expect(morpheme.phonetic_features.syllable_count).toBe(2)
      expect(morpheme.language_quality_scores.naturalness).toBe(0.9)
    })

    it('应该正确定义语义向量维度常量', () => {
      // 测试常量类型
      const legacyDim: number = SEMANTIC_VECTOR_DIMENSIONS.LEGACY
      const multilingualDim: number = SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL
      const defaultDim: number = SEMANTIC_VECTOR_DIMENSIONS.DEFAULT

      expect(legacyDim).toBe(20)
      expect(multilingualDim).toBe(512)
      expect(defaultDim).toBe(512)
    })
  })

  describe('类型兼容性测试', () => {
    it('应该支持传统 CulturalContext 枚举', () => {
      const contexts: CulturalContext[] = [CulturalContext.ANCIENT, CulturalContext.MODERN, CulturalContext.NEUTRAL]

      contexts.forEach(context => {
        expect([CulturalContext.ANCIENT, CulturalContext.MODERN, CulturalContext.NEUTRAL]).toContain(context)
      })
    })

    it('应该支持多维度 CulturalContext 接口', () => {
      const multiContext: MultiDimensionalCulturalContext = {
        traditionality: 0.6,
        modernity: 0.8,
        formality: 0.5,
        regionality: 0.7,
        religious_sensitivity: 0.3,
        age_appropriateness: ['adult', 'teen'],
        cultural_tags: ['positive', 'modern']
      }

      expect(multiContext.traditionality).toBe(0.6)
      expect(multiContext.cultural_tags).toContain('positive')
    })

    it('应该支持语义向量的向后兼容', () => {
      const universalVector: UniversalSemanticVector = {
        vector: new Array(512).fill(0.1),
        model_version: 'mBERT-v1.0',
        confidence: 0.95,
        updated_at: '2025-06-24T00:00:00Z',
        legacy_vector: new Array(20).fill(0.1) // 向后兼容
      }

      expect(universalVector.vector).toHaveLength(512)
      expect(universalVector.legacy_vector).toHaveLength(20)
    })
  })

  describe('类型推断测试', () => {
    it('应该正确推断函数返回类型', () => {
      // 模拟函数类型推断
      function createMorpheme(text: string): Morpheme {
        return {
          id: 'test',
          text,
          category: 'emotions' as MorphemeCategory,
          subcategory: 'positive',
          cultural_context: CulturalContext.MODERN,
          usage_frequency: 0.8,
          quality_score: 0.9,
          semantic_vector: [0.1, 0.2],
          tags: ['test'],
          language_properties: {
            syllable_count: 1,
            character_count: 2,
            phonetic_features: ['tone4'],
            morphological_type: 'simple'
          },
          quality_metrics: {
            naturalness: 0.9,
            frequency: 0.8,
            acceptability: 0.9,
            aesthetic_appeal: 0.7
          },
          created_at: Date.now(),
          source: 'test',
          version: '2.0.0'
        }
      }

      const morpheme = createMorpheme('测试')
      expect(morpheme.text).toBe('测试')
    })

    it('应该支持泛型类型约束', () => {
      // 测试泛型约束
      function processLanguageData<T extends { language: LanguageCode }>(data: T[]): T[] {
        return data.filter(item => item.language === 'zh-CN')
      }

      const mockData = [
        { language: LanguageCode.ZH_CN, text: '测试1' },
        { language: LanguageCode.EN_US, text: 'test2' }
      ]

      const filtered = processLanguageData(mockData)
      expect(filtered).toHaveLength(1)
      expect(filtered[0].language).toBe(LanguageCode.ZH_CN)
    })
  })

  describe('编译时类型检查', () => {
    it('应该防止类型错误的赋值', () => {
      // 这些测试确保TypeScript编译器能够捕获类型错误
      
      // 正确的类型赋值
      const validLanguage: LanguageCode = LanguageCode.ZH_CN
      const validRegister: RegisterLevel = RegisterLevel.FORMAL

      expect(validLanguage).toBe(LanguageCode.ZH_CN)
      expect(validRegister).toBe(RegisterLevel.FORMAL)
      
      // TypeScript应该在编译时捕获以下错误：
      // const invalidLanguage: LanguageCode = 'invalid-lang' // 编译错误
      // const invalidRegister: RegisterLevel = 'INVALID' // 编译错误
    })

    it('应该确保必需字段的存在', () => {
      // TypeScript应该确保所有必需字段都存在
      const partialMorpheme: Partial<Morpheme> = {
        id: 'test',
        text: '测试'
        // 缺少其他必需字段
      }

      // 完整的morpheme应该包含所有必需字段
      expect(partialMorpheme.id).toBe('test')
      expect(partialMorpheme.text).toBe('测试')
    })
  })

  describe('质量评估系统准确性测试', () => {
    it('应该正确计算8维度质量评分', () => {
      const qualityScore: QualityScore = {
        overall: 0.85,
        dimensions: {
          creativity: 0.9,
          memorability: 0.8,
          cultural_fit: 0.9,
          uniqueness: 0.7,
          pronunciation: 0.8,
          semantic_coherence: 0.8,
          aesthetic_appeal: 0.8,
          practical_usability: 0.8
        },
        confidence: 0.9,
        evaluation_time: 150,
        algorithm_version: 'v2.0',
        issues: [],
        suggestions: [],
        timestamp: Date.now()
      }

      // 验证所有维度都在有效范围内
      Object.values(qualityScore.dimensions).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(0)
        expect(score).toBeLessThanOrEqual(1)
      })

      // 验证整体评分与各维度的一致性
      const avgScore = Object.values(qualityScore.dimensions).reduce((sum, score) => sum + score, 0) / 8

      // 整体评分应该接近各维度平均值
      expect(Math.abs(qualityScore.overall - avgScore)).toBeLessThan(0.1)
    })

    it('应该验证多语种质量评分的一致性', () => {
      const langQualityScores: LanguageQualityScores = {
        naturalness: 0.9,
        fluency: 0.85,
        authenticity: 0.95,
        aesthetic_appeal: 0.8,
        pronunciation_ease: 0.75,
        memorability: 0.9,
        uniqueness: 0.6,
        practicality: 0.85
      }

      // 验证所有评分都在有效范围内
      Object.values(langQualityScores).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(0)
        expect(score).toBeLessThanOrEqual(1)
      })

      // 验证评分的合理性
      expect(langQualityScores.authenticity).toBeGreaterThan(0.8) // 真实性应该较高
      expect(langQualityScores.naturalness).toBeGreaterThan(0.7) // 自然度应该较高
    })
  })
})
