/**
 * 基础功能测试
 * 
 * 测试核心类型定义和基本功能的正确性
 */

import { describe, it, expect } from '@jest/globals'
import { CulturalContext } from '../types/core.js'
import { LanguageCode, RegisterLevel, SEMANTIC_VECTOR_DIMENSIONS } from '../types/multilingual.js'

describe('基础功能测试', () => {
  describe('类型定义测试', () => {
    it('应该正确定义 CulturalContext 枚举', () => {
      expect(CulturalContext.ANCIENT).toBe('ancient')
      expect(CulturalContext.MODERN).toBe('modern')
      expect(CulturalContext.NEUTRAL).toBe('neutral')
    })

    it('应该正确定义 LanguageCode 枚举', () => {
      expect(LanguageCode.ZH_CN).toBe('zh-CN')
      expect(LanguageCode.EN_US).toBe('en-US')
      expect(LanguageCode.JA_JP).toBe('ja-JP')
      expect(LanguageCode.KO_KR).toBe('ko-KR')
      expect(LanguageCode.ES_ES).toBe('es-ES')
      expect(LanguageCode.FR_FR).toBe('fr-FR')
      expect(LanguageCode.DE_DE).toBe('de-DE')
      expect(LanguageCode.AR_SA).toBe('ar-SA')
    })

    it('应该正确定义 RegisterLevel 枚举', () => {
      expect(RegisterLevel.FORMAL).toBe('FORMAL')
      expect(RegisterLevel.NEUTRAL).toBe('NEUTRAL')
      expect(RegisterLevel.INFORMAL).toBe('INFORMAL')
      expect(RegisterLevel.COLLOQUIAL).toBe('COLLOQUIAL')
    })

    it('应该正确定义语义向量维度常量', () => {
      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBe(20)
      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBe(512)
      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(512)
    })
  })

  describe('类型兼容性测试', () => {
    it('应该支持枚举值的类型检查', () => {
      const culturalContexts = Object.values(CulturalContext)
      expect(culturalContexts).toHaveLength(3)
      expect(culturalContexts).toContain('ancient')
      expect(culturalContexts).toContain('modern')
      expect(culturalContexts).toContain('neutral')
    })

    it('应该支持语言代码的类型检查', () => {
      const languageCodes = Object.values(LanguageCode)
      expect(languageCodes).toHaveLength(8)
      expect(languageCodes).toContain('zh-CN')
      expect(languageCodes).toContain('en-US')
    })

    it('应该支持寄存器级别的类型检查', () => {
      const registerLevels = Object.values(RegisterLevel)
      expect(registerLevels).toHaveLength(4)
      expect(registerLevels).toContain('FORMAL')
      expect(registerLevels).toContain('NEUTRAL')
      expect(registerLevels).toContain('INFORMAL')
      expect(registerLevels).toContain('COLLOQUIAL')
    })
  })

  describe('配置常量测试', () => {
    it('应该正确配置语义向量维度', () => {
      // 测试向量维度配置的一致性
      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)
      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeLessThan(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)
      
      // 测试维度值的合理性
      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeGreaterThan(0)
      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBeGreaterThan(0)
      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBeGreaterThan(0)
    })

    it('应该支持向量维度的数学运算', () => {
      const legacyVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).fill(0.1)
      const multilingualVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).fill(0.1)
      
      expect(legacyVector).toHaveLength(20)
      expect(multilingualVector).toHaveLength(512)
      
      // 测试向量的基本数学属性
      expect(legacyVector.every(v => v === 0.1)).toBe(true)
      expect(multilingualVector.every(v => v === 0.1)).toBe(true)
    })
  })

  describe('数据结构验证', () => {
    it('应该支持多语言标签映射', () => {
      const multilingualLabels = {
        [LanguageCode.ZH_CN]: '测试',
        [LanguageCode.EN_US]: 'test',
        [LanguageCode.JA_JP]: 'テスト'
      }
      
      expect(multilingualLabels[LanguageCode.ZH_CN]).toBe('测试')
      expect(multilingualLabels[LanguageCode.EN_US]).toBe('test')
      expect(multilingualLabels[LanguageCode.JA_JP]).toBe('テスト')
    })

    it('应该支持文化适应性配置', () => {
      const culturalAdaptation = {
        traditionality: 0.6,
        modernity: 0.8,
        formality: 0.5,
        regionality: 0.7,
        religious_sensitivity: 0.3,
        age_appropriateness: ['adult', 'teen'],
        cultural_tags: ['positive', 'modern']
      }
      
      // 验证数值范围
      expect(culturalAdaptation.traditionality).toBeGreaterThanOrEqual(0)
      expect(culturalAdaptation.traditionality).toBeLessThanOrEqual(1)
      expect(culturalAdaptation.modernity).toBeGreaterThanOrEqual(0)
      expect(culturalAdaptation.modernity).toBeLessThanOrEqual(1)
      
      // 验证数组属性
      expect(Array.isArray(culturalAdaptation.age_appropriateness)).toBe(true)
      expect(Array.isArray(culturalAdaptation.cultural_tags)).toBe(true)
    })

    it('应该支持语音特征配置', () => {
      const phoneticFeatures = {
        ipa_transcription: '/test/',
        syllable_count: 1,
        tone_pattern: ['1'],
        phonetic_harmony: 0.8
      }
      
      expect(phoneticFeatures.ipa_transcription).toMatch(/^\/.*\/$/);
      expect(phoneticFeatures.syllable_count).toBeGreaterThan(0)
      expect(Array.isArray(phoneticFeatures.tone_pattern)).toBe(true)
      expect(phoneticFeatures.phonetic_harmony).toBeGreaterThanOrEqual(0)
      expect(phoneticFeatures.phonetic_harmony).toBeLessThanOrEqual(1)
    })
  })

  describe('质量评估系统测试', () => {
    it('应该支持8维度质量评分', () => {
      const qualityDimensions = {
        creativity: 0.9,
        memorability: 0.8,
        cultural_fit: 0.9,
        aesthetic_appeal: 0.8,
        pronunciation_ease: 0.8,
        uniqueness: 0.7,
        practicality: 0.8,
        semantic_richness: 0.8
      }
      
      // 验证所有维度都在有效范围内
      Object.values(qualityDimensions).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(0)
        expect(score).toBeLessThanOrEqual(1)
      })
      
      // 验证维度数量
      expect(Object.keys(qualityDimensions)).toHaveLength(8)
    })

    it('应该计算平均质量评分', () => {
      const dimensions = {
        creativity: 0.9,
        memorability: 0.8,
        cultural_fit: 0.9,
        aesthetic_appeal: 0.8,
        pronunciation_ease: 0.8,
        uniqueness: 0.7,
        practicality: 0.8,
        semantic_richness: 0.8
      }
      
      const average = Object.values(dimensions).reduce((sum, score) => sum + score, 0) / 8
      expect(average).toBeCloseTo(0.8125, 4)
      expect(average).toBeGreaterThan(0.7)
      expect(average).toBeLessThan(0.9)
    })
  })

  describe('错误处理测试', () => {
    it('应该处理无效的枚举值', () => {
      // TypeScript 编译时会捕获这些错误，但我们可以测试运行时行为
      const validCulturalContext = CulturalContext.MODERN
      expect(Object.values(CulturalContext)).toContain(validCulturalContext)
    })

    it('应该处理边界值', () => {
      // 测试质量评分的边界值
      const minScore = 0
      const maxScore = 1
      
      expect(minScore).toBeGreaterThanOrEqual(0)
      expect(minScore).toBeLessThanOrEqual(1)
      expect(maxScore).toBeGreaterThanOrEqual(0)
      expect(maxScore).toBeLessThanOrEqual(1)
    })

    it('应该处理空数组和空对象', () => {
      const emptyArray: string[] = []
      const emptyObject: Record<string, any> = {}
      
      expect(Array.isArray(emptyArray)).toBe(true)
      expect(emptyArray).toHaveLength(0)
      expect(typeof emptyObject).toBe('object')
      expect(Object.keys(emptyObject)).toHaveLength(0)
    })
  })
})
