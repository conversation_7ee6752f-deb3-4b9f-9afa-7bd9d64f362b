/**
 * 基础功能测试
 * 
 * 测试核心类型定义和基本功能的正确性
 */

import { describe, it, expect } from '@jest/globals'
import { CulturalContext } from '../types/core.js'
import { LanguageCode, RegisterLevel, SEMANTIC_VECTOR_DIMENSIONS } from '../types/multilingual.js'
import {
  TEST_QUALITY_THRESHOLDS,
  TEST_VECTOR_DIMENSIONS,
  TEST_ENUM_COUNTS,
  TEST_VALUE_RANGES,
  TEST_MULTILINGUAL_LABELS,
  TEST_CULTURAL_ADAPTATION,
  TEST_PHONETIC_FEATURES,
  TEST_QUALITY_DIMENSIONS,
  TEST_IPA_PATTERN,
  TEST_AVERAGE_QUALITY,
  TEST_PRECISION,
  TEST_BOUNDARY_VALUES
} from '../config/test-constants.js'

describe('基础功能测试', () => {
  describe('类型定义测试', () => {
    it('应该正确定义 CulturalContext 枚举', () => {
      expect(CulturalContext.ANCIENT).toBe('ancient')
      expect(CulturalContext.MODERN).toBe('modern')
      expect(CulturalContext.NEUTRAL).toBe('neutral')
    })

    it('应该正确定义 LanguageCode 枚举', () => {
      expect(LanguageCode.ZH_CN).toBe('zh-CN')
      expect(LanguageCode.EN_US).toBe('en-US')
      expect(LanguageCode.JA_JP).toBe('ja-JP')
      expect(LanguageCode.KO_KR).toBe('ko-KR')
      expect(LanguageCode.ES_ES).toBe('es-ES')
      expect(LanguageCode.FR_FR).toBe('fr-FR')
      expect(LanguageCode.DE_DE).toBe('de-DE')
      expect(LanguageCode.AR_SA).toBe('ar-SA')
    })

    it('应该正确定义 RegisterLevel 枚举', () => {
      expect(RegisterLevel.FORMAL).toBe('formal')
      expect(RegisterLevel.NEUTRAL).toBe('neutral')
      expect(RegisterLevel.INFORMAL).toBe('informal')
      expect(RegisterLevel.COLLOQUIAL).toBe('colloquial')
    })

    it('应该正确定义语义向量维度常量', () => {
      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBe(TEST_VECTOR_DIMENSIONS.LEGACY_EXPECTED)
      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBe(TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED)
      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED)
    })
  })

  describe('类型兼容性测试', () => {
    it('应该支持枚举值的类型检查', () => {
      const culturalContexts = Object.values(CulturalContext)
      expect(culturalContexts).toHaveLength(TEST_ENUM_COUNTS.CULTURAL_CONTEXT)
      expect(culturalContexts).toContain('ancient')
      expect(culturalContexts).toContain('modern')
      expect(culturalContexts).toContain('neutral')
    })

    it('应该支持语言代码的类型检查', () => {
      const languageCodes = Object.values(LanguageCode)
      expect(languageCodes).toHaveLength(TEST_ENUM_COUNTS.LANGUAGE_CODE)
      expect(languageCodes).toContain('zh-CN')
      expect(languageCodes).toContain('en-US')
    })

    it('应该支持寄存器级别的类型检查', () => {
      const registerLevels = Object.values(RegisterLevel)
      expect(registerLevels).toHaveLength(TEST_ENUM_COUNTS.REGISTER_LEVEL)
      expect(registerLevels).toContain('formal')
      expect(registerLevels).toContain('neutral')
      expect(registerLevels).toContain('informal')
      expect(registerLevels).toContain('colloquial')
    })
  })

  describe('配置常量测试', () => {
    it('应该正确配置语义向量维度', () => {
      // 测试向量维度配置的一致性
      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBe(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)
      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeLessThan(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL)
      
      // 测试维度值的合理性
      expect(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).toBeGreaterThan(0)
      expect(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).toBeGreaterThan(0)
      expect(SEMANTIC_VECTOR_DIMENSIONS.DEFAULT).toBeGreaterThan(0)
    })

    it('应该支持向量维度的数学运算', () => {
      const legacyVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.LEGACY).fill(TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)
      const multilingualVector = new Array(SEMANTIC_VECTOR_DIMENSIONS.MULTILINGUAL).fill(TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)

      expect(legacyVector).toHaveLength(TEST_VECTOR_DIMENSIONS.LEGACY_EXPECTED)
      expect(multilingualVector).toHaveLength(TEST_VECTOR_DIMENSIONS.MULTILINGUAL_EXPECTED)

      // 测试向量的基本数学属性
      expect(legacyVector.every(v => v === TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)).toBe(true)
      expect(multilingualVector.every(v => v === TEST_VECTOR_DIMENSIONS.TEST_FILL_VALUE)).toBe(true)
    })
  })

  describe('数据结构验证', () => {
    it('应该支持多语言标签映射', () => {
      const multilingualLabels = {
        [LanguageCode.ZH_CN]: TEST_MULTILINGUAL_LABELS.ZH_CN,
        [LanguageCode.EN_US]: TEST_MULTILINGUAL_LABELS.EN_US,
        [LanguageCode.JA_JP]: TEST_MULTILINGUAL_LABELS.JA_JP
      }

      expect(multilingualLabels[LanguageCode.ZH_CN]).toBe(TEST_MULTILINGUAL_LABELS.ZH_CN)
      expect(multilingualLabels[LanguageCode.EN_US]).toBe(TEST_MULTILINGUAL_LABELS.EN_US)
      expect(multilingualLabels[LanguageCode.JA_JP]).toBe(TEST_MULTILINGUAL_LABELS.JA_JP)
    })

    it('应该支持文化适应性配置', () => {
      const culturalAdaptation = TEST_CULTURAL_ADAPTATION

      // 验证数值范围
      expect(culturalAdaptation.traditionality).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.min)
      expect(culturalAdaptation.traditionality).toBeLessThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.max)
      expect(culturalAdaptation.modernity).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.min)
      expect(culturalAdaptation.modernity).toBeLessThanOrEqual(TEST_VALUE_RANGES.CULTURAL_ADAPTATION.max)

      // 验证数组属性
      expect(Array.isArray(culturalAdaptation.age_appropriateness)).toBe(true)
      expect(Array.isArray(culturalAdaptation.cultural_tags)).toBe(true)
    })

    it('应该支持语音特征配置', () => {
      const phoneticFeatures = TEST_PHONETIC_FEATURES

      expect(phoneticFeatures.ipa_transcription).toMatch(TEST_IPA_PATTERN)
      expect(phoneticFeatures.syllable_count).toBeGreaterThan(0)
      expect(Array.isArray(phoneticFeatures.tone_pattern)).toBe(true)
      expect(phoneticFeatures.phonetic_harmony).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.PHONETIC_HARMONY.min)
      expect(phoneticFeatures.phonetic_harmony).toBeLessThanOrEqual(TEST_VALUE_RANGES.PHONETIC_HARMONY.max)
    })
  })

  describe('质量评估系统测试', () => {
    it('应该支持8维度质量评分', () => {
      const qualityDimensions = TEST_QUALITY_DIMENSIONS

      // 验证所有维度都在有效范围内
      Object.values(qualityDimensions).forEach(score => {
        expect(score).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.min)
        expect(score).toBeLessThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.max)
      })

      // 验证维度数量
      expect(Object.keys(qualityDimensions)).toHaveLength(TEST_ENUM_COUNTS.QUALITY_DIMENSIONS)
    })

    it('应该计算平均质量评分', () => {
      const dimensions = TEST_QUALITY_DIMENSIONS

      const average = Object.values(dimensions).reduce((sum, score) => sum + score, 0) / TEST_ENUM_COUNTS.QUALITY_DIMENSIONS
      expect(average).toBeCloseTo(TEST_AVERAGE_QUALITY, TEST_PRECISION.FLOAT_PRECISION)
      expect(average).toBeGreaterThan(TEST_QUALITY_THRESHOLDS.HIGH_QUALITY)
      expect(average).toBeLessThan(TEST_QUALITY_THRESHOLDS.PERFECT)
    })
  })

  describe('错误处理测试', () => {
    it('应该处理无效的枚举值', () => {
      // TypeScript 编译时会捕获这些错误，但我们可以测试运行时行为
      const validCulturalContext = CulturalContext.MODERN
      expect(Object.values(CulturalContext)).toContain(validCulturalContext)
    })

    it('应该处理边界值', () => {
      // 测试质量评分的边界值
      const minScore = TEST_BOUNDARY_VALUES.MIN_SCORE
      const maxScore = TEST_BOUNDARY_VALUES.MAX_SCORE

      expect(minScore).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.min)
      expect(minScore).toBeLessThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.max)
      expect(maxScore).toBeGreaterThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.min)
      expect(maxScore).toBeLessThanOrEqual(TEST_VALUE_RANGES.QUALITY_SCORE.max)
    })

    it('应该处理空数组和空对象', () => {
      const emptyArray: string[] = []
      const emptyObject: Record<string, any> = {}

      expect(Array.isArray(emptyArray)).toBe(true)
      expect(emptyArray).toHaveLength(TEST_BOUNDARY_VALUES.EMPTY_ARRAY_LENGTH)
      expect(typeof emptyObject).toBe('object')
      expect(Object.keys(emptyObject)).toHaveLength(TEST_BOUNDARY_VALUES.EMPTY_OBJECT_KEYS)
    })
  })
})
