{"source": {"include": ["./types/", "./core/", "./config/", "./README.md"], "includePattern": "\\.(js|ts)$", "exclude": ["node_modules/", "coverage/", "dist/", ".jest-cache/", "*.test.ts", "*.spec.ts"]}, "opts": {"destination": "./docs/api/", "recurse": true, "readme": "./README.md"}, "plugins": ["plugins/markdown"], "templates": {"cleverLinks": false, "monospaceLinks": false}, "tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "markdown": {"parser": "gfm"}}