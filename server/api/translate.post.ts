/**
 * 跨语言翻译API端点 (v3.0多语种支持)
 * POST /api/translate
 */

import { defineEventHandler, readBody, setResponseStatus } from 'h3'
import type {
  TranslationRequest,
  TranslationResponse,
  APIResponse
} from '../types/api'
import { LanguageCode } from '../types/multilingual'
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createInternalErrorResponse 
} from '../utils/api-response'
import { generateRequestId } from '../utils/common'
import { 
  API, 
  VALIDATION, 
  ERROR_MESSAGES 
} from '../config/constants'

/**
 * 简化的翻译映射表 (MVP实现)
 * 实际项目中应使用专业翻译引擎或API
 */
const TRANSLATION_MAPPINGS: Record<string, Partial<Record<LanguageCode, string[]>>> = {
  // 中文示例
  '智慧': {
    [LanguageCode.EN_US]: ['wisdom', 'intelligence', 'smart'],
    [LanguageCode.JA_JP]: ['知恵', '智慧', '賢明'],
    [LanguageCode.KO_KR]: ['지혜', '지능', '현명'],
    [LanguageCode.ES_ES]: ['sabiduría', 'inteligencia', 'sabio'],
    [LanguageCode.FR_FR]: ['sagesse', 'intelligence', 'sage'],
    [LanguageCode.DE_DE]: ['weisheit', 'intelligenz', 'klug'],
    [LanguageCode.AR_SA]: ['حكمة', 'ذكاء', 'حكيم']
  },
  '星辰': {
    [LanguageCode.EN_US]: ['star', 'stellar', 'astral'],
    [LanguageCode.JA_JP]: ['星', '星辰', '恒星'],
    [LanguageCode.KO_KR]: ['별', '성진', '항성'],
    [LanguageCode.ES_ES]: ['estrella', 'estelar', 'astral'],
    [LanguageCode.FR_FR]: ['étoile', 'stellaire', 'astral'],
    [LanguageCode.DE_DE]: ['stern', 'stellar', 'astral'],
    [LanguageCode.AR_SA]: ['نجم', 'نجمي', 'فلكي']
  },
  // 英文示例
  'wisdom': {
    [LanguageCode.ZH_CN]: ['智慧', '睿智', '明智'],
    [LanguageCode.JA_JP]: ['知恵', '智慧', '賢明'],
    [LanguageCode.KO_KR]: ['지혜', '지능', '현명'],
    [LanguageCode.ES_ES]: ['sabiduría', 'inteligencia', 'sabio'],
    [LanguageCode.FR_FR]: ['sagesse', 'intelligence', 'sage'],
    [LanguageCode.DE_DE]: ['weisheit', 'intelligenz', 'klug'],
    [LanguageCode.AR_SA]: ['حكمة', 'ذكاء', 'حكيم']
  },
  'star': {
    [LanguageCode.ZH_CN]: ['星', '星辰', '恒星'],
    [LanguageCode.JA_JP]: ['星', '星辰', '恒星'],
    [LanguageCode.KO_KR]: ['별', '성진', '항성'],
    [LanguageCode.ES_ES]: ['estrella', 'estelar', 'astral'],
    [LanguageCode.FR_FR]: ['étoile', 'stellaire', 'astral'],
    [LanguageCode.DE_DE]: ['stern', 'stellar', 'astral'],
    [LanguageCode.AR_SA]: ['نجم', 'نجمي', 'فلكي']
  }
}

/**
 * 执行用户名翻译
 */
function translateUsername(
  username: string, 
  sourceLanguage: LanguageCode, 
  targetLanguage: LanguageCode,
  options: {
    preserve_meaning?: boolean
    preserve_style?: boolean
    cultural_adaptation?: boolean
  } = {}
): {
  translated_username: string
  confidence: number
  alternatives: string[]
  semantic_similarity: number
  cultural_adaptation_score: number
  explanation?: string
} {
  // 如果源语言和目标语言相同，直接返回
  if (sourceLanguage === targetLanguage) {
    return {
      translated_username: username,
      confidence: 1.0,
      alternatives: [],
      semantic_similarity: 1.0,
      cultural_adaptation_score: 1.0,
      explanation: 'Source and target languages are the same'
    }
  }

  // 查找翻译映射
  const mapping = TRANSLATION_MAPPINGS[username.toLowerCase()]
  if (mapping && mapping[targetLanguage]) {
    const translations = mapping[targetLanguage]
    const primaryTranslation = translations[0]
    const alternatives = translations.slice(1)

    return {
      translated_username: primaryTranslation,
      confidence: 0.85,
      alternatives,
      semantic_similarity: 0.9,
      cultural_adaptation_score: options.cultural_adaptation ? 0.8 : 0.6,
      explanation: `Translated from ${sourceLanguage} to ${targetLanguage} using semantic mapping`
    }
  }

  // 如果没有找到映射，进行基础音译处理
  const transliterated = transliterateBasic(username, targetLanguage)
  
  return {
    translated_username: transliterated,
    confidence: 0.4,
    alternatives: [username], // 保留原文作为备选
    semantic_similarity: 0.3,
    cultural_adaptation_score: 0.2,
    explanation: `Basic transliteration from ${sourceLanguage} to ${targetLanguage} (limited mapping available)`
  }
}

/**
 * 基础音译处理
 */
function transliterateBasic(text: string, targetLanguage: LanguageCode): string {
  // 简化的音译逻辑
  switch (targetLanguage) {
    case LanguageCode.EN_US:
      // 中文转英文音译示例
      return text.replace(/[\u4e00-\u9fff]/g, (char) => {
        // 这里应该使用专业的拼音转换库
        return 'x' // 简化处理
      })
    case LanguageCode.ZH_CN:
      // 英文转中文音译示例
      return text.replace(/[a-zA-Z]/g, (char) => {
        // 这里应该使用专业的音译库
        return '克' // 简化处理
      })
    default:
      return text + '_' + targetLanguage.split('-')[0]
  }
}

/**
 * 验证翻译请求参数
 */
function validateTranslationRequest(body: TranslationRequest): { isValid: boolean; errors?: string[] } {
  const errors: string[] = []

  // 检查username参数
  if (!body.username || typeof body.username !== 'string') {
    errors.push('username is required and must be a string')
  } else if (body.username.trim().length === 0) {
    errors.push('username cannot be empty')
  } else if (body.username.length > 50) {
    errors.push('username cannot exceed 50 characters')
  }

  // 检查source_language参数
  if (!body.source_language) {
    errors.push('source_language is required')
  } else if (!VALIDATION.SUPPORTED_LANGUAGES.includes(body.source_language as any)) {
    errors.push(ERROR_MESSAGES.VALIDATION.INVALID_LANGUAGE(
      VALIDATION.SUPPORTED_LANGUAGES.map(lang => lang.toString())
    ))
  }

  // 检查target_language参数
  if (!body.target_language) {
    errors.push('target_language is required')
  } else if (!VALIDATION.SUPPORTED_LANGUAGES.includes(body.target_language as any)) {
    errors.push(ERROR_MESSAGES.VALIDATION.INVALID_LANGUAGE(
      VALIDATION.SUPPORTED_LANGUAGES.map(lang => lang.toString())
    ))
  }

  // 检查语言组合是否支持
  if (body.source_language && body.target_language) {
    if (body.source_language === body.target_language) {
      // 允许相同语言，但给出警告
      console.warn('Source and target languages are the same')
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  }
}

export default defineEventHandler(async (event): Promise<APIResponse<TranslationResponse>> => {
  const startTime = Date.now()
  const requestId = generateRequestId()

  try {
    // 解析请求体
    const body = await readBody(event) as TranslationRequest

    // 验证请求参数
    const validation = validateTranslationRequest(body)
    if (!validation.isValid) {
      setResponseStatus(event, 400)
      return createErrorResponse(
        {
          code: VALIDATION.ERROR_CODES.INVALID_PARAMETERS,
          message: ERROR_MESSAGES.INTERNAL.VALIDATION_FAILED,
          details: { errors: validation.errors }
        },
        {
          requestId,
          startTime,
          version: API.VERSION
        }
      )
    }

    // 执行翻译
    const translation = translateUsername(
      body.username,
      body.source_language,
      body.target_language,
      {
        preserve_meaning: body.preserve_meaning ?? true,
        preserve_style: body.preserve_style ?? false,
        cultural_adaptation: body.cultural_adaptation ?? true
      }
    )

    const response: TranslationResponse = translation

    // 返回成功响应
    return createSuccessResponse(response, {
      requestId,
      startTime,
      version: API.VERSION
    })

  } catch (error) {
    console.error('Translation API error:', error)
    return createInternalErrorResponse(error, {
      requestId,
      startTime,
      version: API.VERSION
    })
  }
})
