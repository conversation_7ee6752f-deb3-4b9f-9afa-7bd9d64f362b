/**
 * 概念查询API端点 (v3.0多语种支持)
 * POST /api/concepts
 */

import { defineEventHandler, readBody, setResponseStatus } from 'h3'
import type {
  ConceptQueryRequest,
  ConceptQueryResponse,
  APIResponse
} from '../types/api'
import { LanguageCode, ConceptCategory } from '../types/multilingual'
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createInternalErrorResponse 
} from '../utils/api-response'
import { generateRequestId } from '../utils/common'
import { 
  API, 
  VALIDATION, 
  ERROR_MESSAGES 
} from '../config/constants'

/**
 * 概念数据库 (MVP实现)
 * 实际项目中应使用专业的概念知识库
 */
const CONCEPT_DATABASE = {
  // 智慧相关概念
  'wisdom': {
    id: 'concept_001',
    primary_language: LanguageCode.EN_US,
    category: ConceptCategory.CONCEPTS,
    semantic_vector: [0.8, 0.7, 0.9, 0.6, 0.8, 0.7, 0.9, 0.8],
    cross_lingual_mappings: {
      [LanguageCode.ZH_CN]: ['智慧', '睿智', '明智'],
      [LanguageCode.JA_JP]: ['知恵', '智慧', '賢明'],
      [LanguageCode.KO_KR]: ['지혜', '지능', '현명'],
      [LanguageCode.ES_ES]: ['sabiduría', 'inteligencia'],
      [LanguageCode.FR_FR]: ['sagesse', 'intelligence'],
      [LanguageCode.DE_DE]: ['weisheit', 'intelligenz'],
      [LanguageCode.AR_SA]: ['حكمة', 'ذكاء']
    },
    cultural_adaptations: {
      [LanguageCode.ZH_CN]: { traditionality: 0.9, formality: 0.8 },
      [LanguageCode.JA_JP]: { traditionality: 0.8, formality: 0.9 },
      [LanguageCode.EN_US]: { traditionality: 0.5, formality: 0.6 }
    }
  },
  // 星辰相关概念
  'star': {
    id: 'concept_002',
    primary_language: LanguageCode.EN_US,
    category: ConceptCategory.OBJECTS,
    semantic_vector: [0.6, 0.8, 0.7, 0.9, 0.6, 0.8, 0.7, 0.9],
    cross_lingual_mappings: {
      [LanguageCode.ZH_CN]: ['星', '星辰', '恒星'],
      [LanguageCode.JA_JP]: ['星', '星辰', '恒星'],
      [LanguageCode.KO_KR]: ['별', '성진', '항성'],
      [LanguageCode.ES_ES]: ['estrella', 'astro'],
      [LanguageCode.FR_FR]: ['étoile', 'astre'],
      [LanguageCode.DE_DE]: ['stern', 'gestirn'],
      [LanguageCode.AR_SA]: ['نجم', 'كوكب']
    },
    cultural_adaptations: {
      [LanguageCode.ZH_CN]: { traditionality: 0.8, formality: 0.7 },
      [LanguageCode.JA_JP]: { traditionality: 0.7, formality: 0.8 },
      [LanguageCode.EN_US]: { traditionality: 0.4, formality: 0.5 }
    }
  },
  // 中文概念示例
  '智慧': {
    id: 'concept_001', // 同一概念的中文表示
    primary_language: LanguageCode.ZH_CN,
    category: ConceptCategory.CONCEPTS,
    semantic_vector: [0.8, 0.7, 0.9, 0.6, 0.8, 0.7, 0.9, 0.8],
    cross_lingual_mappings: {
      [LanguageCode.EN_US]: ['wisdom', 'intelligence', 'smart'],
      [LanguageCode.JA_JP]: ['知恵', '智慧', '賢明'],
      [LanguageCode.KO_KR]: ['지혜', '지능', '현명']
    },
    cultural_adaptations: {
      [LanguageCode.ZH_CN]: { traditionality: 0.9, formality: 0.8 },
      [LanguageCode.EN_US]: { traditionality: 0.5, formality: 0.6 }
    }
  }
}

/**
 * 查询概念信息
 */
function queryConcepts(
  query: string,
  language?: LanguageCode,
  category?: ConceptCategory,
  limit: number = 10
): {
  concepts: Array<{
    id: string
    primary_language: LanguageCode
    category: ConceptCategory
    semantic_vector: number[]
    cross_lingual_mappings: Partial<Record<LanguageCode, string[]>>
    cultural_adaptations: Partial<Record<LanguageCode, { traditionality: number; formality: number }>>
    relevance_score: number
  }>
  total_found: number
} {
  const results = []
  const queryLower = query.toLowerCase()

  // 搜索概念数据库
  for (const [key, concept] of Object.entries(CONCEPT_DATABASE)) {
    let relevanceScore = 0

    // 直接匹配
    if (key.toLowerCase().includes(queryLower)) {
      relevanceScore += 1.0
    }

    // 跨语言映射匹配
    for (const [lang, mappings] of Object.entries(concept.cross_lingual_mappings)) {
      for (const mapping of mappings) {
        if (mapping.toLowerCase().includes(queryLower)) {
          relevanceScore += 0.8
          break
        }
      }
    }

    // 语言过滤
    if (language && concept.primary_language !== language) {
      // 检查是否有该语言的映射
      const mappings = (concept.cross_lingual_mappings as any)[language]
      if (!mappings || mappings.length === 0) {
        continue
      }
      relevanceScore *= 0.7 // 非主要语言降权
    }

    // 类别过滤
    if (category && concept.category !== category) {
      continue
    }

    if (relevanceScore > 0) {
      results.push({
        ...concept,
        relevance_score: Math.min(relevanceScore, 1.0)
      })
    }
  }

  // 按相关性排序并限制结果数量
  results.sort((a, b) => b.relevance_score - a.relevance_score)
  const limitedResults = results.slice(0, limit)

  return {
    concepts: limitedResults,
    total_found: results.length
  }
}

/**
 * 验证概念查询请求参数
 */
function validateConceptQueryRequest(body: ConceptQueryRequest): { isValid: boolean; errors?: string[] } {
  const errors: string[] = []

  // 检查query参数
  if (!body.query || typeof body.query !== 'string') {
    errors.push('query is required and must be a string')
  } else if (body.query.trim().length === 0) {
    errors.push('query cannot be empty')
  } else if (body.query.length > 100) {
    errors.push('query cannot exceed 100 characters')
  }

  // 检查language参数
  if (body.language !== undefined) {
    if (!VALIDATION.SUPPORTED_LANGUAGES.includes(body.language as any)) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_LANGUAGE(
        VALIDATION.SUPPORTED_LANGUAGES.map(lang => lang.toString())
      ))
    }
  }

  // 检查category参数
  if (body.category !== undefined) {
    const validCategories = Object.values(ConceptCategory)
    if (!validCategories.includes(body.category)) {
      errors.push(`category must be one of: ${validCategories.join(', ')}`)
    }
  }

  // 检查limit参数
  if (body.limit !== undefined) {
    if (typeof body.limit !== 'number' || body.limit < 1 || body.limit > 50) {
      errors.push('limit must be a number between 1 and 50')
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  }
}

export default defineEventHandler(async (event): Promise<APIResponse<ConceptQueryResponse>> => {
  const startTime = Date.now()
  const requestId = generateRequestId()

  try {
    // 解析请求体
    const body = await readBody(event) as ConceptQueryRequest

    // 验证请求参数
    const validation = validateConceptQueryRequest(body)
    if (!validation.isValid) {
      setResponseStatus(event, 400)
      return createErrorResponse(
        {
          code: VALIDATION.ERROR_CODES.INVALID_PARAMETERS,
          message: ERROR_MESSAGES.INTERNAL.VALIDATION_FAILED,
          details: { errors: validation.errors }
        },
        {
          requestId,
          startTime,
          version: API.VERSION
        }
      )
    }

    // 执行概念查询
    const queryResult = queryConcepts(
      body.query,
      body.language,
      body.category,
      body.limit || 10
    )

    // 转换为API响应格式
    const formattedConcepts = queryResult.concepts.map(concept => ({
      concept_id: concept.id,
      category: concept.category,
      abstract_meaning: `Abstract concept representing ${concept.id}`,
      language_implementations: Object.fromEntries(
        Object.entries(concept.cross_lingual_mappings).map(([lang, mappings]) => [
          lang,
          {
            text: mappings[0] || concept.id,
            alternatives: mappings.slice(1),
            cultural_context: (concept.cultural_adaptations as any)[lang] || {
              traditionality: 0.5,
              modernity: 0.5,
              formality: 0.5,
              regionality: 0.5,
              religious_sensitivity: 0.5,
              age_appropriateness: ['general'],
              cultural_tags: []
            }
          }
        ])
      )
    }))

    const response: ConceptQueryResponse = {
      concepts: formattedConcepts,
      total_count: queryResult.total_found
    }

    // 返回成功响应
    return createSuccessResponse(response, {
      requestId,
      startTime,
      version: API.VERSION
    })

  } catch (error) {
    console.error('Concept query API error:', error)
    return createInternalErrorResponse(error, {
      requestId,
      startTime,
      version: API.VERSION
    })
  }
})
