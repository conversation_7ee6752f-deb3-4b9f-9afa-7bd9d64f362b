/**
 * 健康检查API端点
 * GET /api/health
 */

import type { HealthResponse, HealthCheck } from '../types/api'
import { defineEventHandler } from 'h3'
import { getErrorMessage } from '../utils/error'

/**
 * 检查内存健康状态
 */
async function checkMemoryHealth(): Promise<HealthCheck> {
  const memoryUsage = process.memoryUsage()
  const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024
  const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024
  const usagePercent = (heapUsedMB / heapTotalMB) * 100

  let status: 'pass' | 'warn' | 'fail' = 'pass'
  let message = `Memory usage: ${heapUsedMB.toFixed(2)}MB / ${heapTotalMB.toFixed(2)}MB (${usagePercent.toFixed(1)}%)`

  if (usagePercent > 90) {
    status = 'fail'
    message += ' - Critical memory usage'
  } else if (usagePercent > 75) {
    status = 'warn'
    message += ' - High memory usage'
  }

  return {
    status,
    details: {
      heap_used_mb: heapUsedMB,
      heap_total_mb: heapTotalMB,
      usage_percent: usagePercent,
      rss_mb: memoryUsage.rss / 1024 / 1024
    },
    message
  }
}

/**
 * 检查缓存健康状态
 */
async function checkCacheHealth(): Promise<HealthCheck> {
  // MVP阶段简化的缓存检查
  // 在实际实现中，这里会检查缓存服务的状态

  return {
    status: 'pass',
    details: {
      cache_type: 'memory',
      status: 'operational'
    },
    message: 'Cache system operational'
  }
}

/**
 * 检查数据完整性
 */
async function checkDataIntegrity(): Promise<HealthCheck> {
  try {
    // 检查语素数据文件是否存在和可读
    const morphemesPath = './server/data/morphemes.json'
    const patternsPath = './server/data/patterns.json'

    // 简化检查 - 在实际实现中会验证数据格式和完整性
    const checks = {
      morphemes_available: true,
      patterns_available: true,
      data_format_valid: true
    }

    const allChecksPass = Object.values(checks).every(check => check)

    return {
      status: allChecksPass ? 'pass' : 'fail',
      details: checks,
      message: allChecksPass ? 'All data integrity checks passed' : 'Data integrity issues detected'
    }
  } catch (error) {
    return {
      status: 'fail',
      details: { error: getErrorMessage(error) },
      message: 'Data integrity check failed'
    }
  }
}

/**
 * 检查性能健康状态
 */
async function checkPerformanceHealth(): Promise<HealthCheck> {
  const startTime = process.hrtime.bigint()

  // 执行一个简单的性能测试
  await new Promise(resolve => setTimeout(resolve, 1))

  const endTime = process.hrtime.bigint()
  const responseTime = Number(endTime - startTime) / 1000000 // 转换为毫秒

  let status: 'pass' | 'warn' | 'fail' = 'pass'
  let message = `Response time: ${responseTime.toFixed(2)}ms`

  if (responseTime > 100) {
    status = 'fail'
    message += ' - Poor performance'
  } else if (responseTime > 50) {
    status = 'warn'
    message += ' - Degraded performance'
  }

  return {
    status,
    response_time: responseTime,
    details: {
      response_time_ms: responseTime,
      performance_threshold_ms: 50
    },
    message
  }
}

/**
 * 确定整体健康状态
 */
function determineOverallStatus(checks: HealthCheck[]): 'healthy' | 'degraded' | 'unhealthy' {
  const hasFailures = checks.some(check => check.status === 'fail')
  const hasWarnings = checks.some(check => check.status === 'warn')

  if (hasFailures) {
    return 'unhealthy'
  } else if (hasWarnings) {
    return 'degraded'
  } else {
    return 'healthy'
  }
}

export default defineEventHandler(async (event): Promise<HealthResponse> => {
  const startTime = Date.now()

  try {
    // 检查内存使用情况
    const memoryCheck = await checkMemoryHealth()

    // 检查缓存状态
    const cacheCheck = await checkCacheHealth()

    // 检查数据完整性
    const dataCheck = await checkDataIntegrity()

    // 检查性能状态
    const performanceCheck = await checkPerformanceHealth()

    // 确定整体健康状态
    const overallStatus = determineOverallStatus([
      memoryCheck,
      cacheCheck,
      dataCheck,
      performanceCheck
    ])

    const response: HealthResponse = {
      status: overallStatus,
      timestamp: Date.now(),
      version: '1.0.0-mvp',
      uptime: process.uptime(),
      checks: {
        memory: memoryCheck,
        cache: cacheCheck,
        data_integrity: dataCheck,
        performance: performanceCheck
      }
    }

    // 设置适当的HTTP状态码
    if (overallStatus === 'unhealthy') {
      setResponseStatus(event, 503)
    } else if (overallStatus === 'degraded') {
      setResponseStatus(event, 200) // 仍然可用，但有警告
    }

    return response

  } catch (error) {
    console.error('Health check failed:', error)

    setResponseStatus(event, 503)
    return {
      status: 'unhealthy',
      timestamp: Date.now(),
      version: '1.0.0-mvp',
      uptime: process.uptime(),
      checks: {
        memory: { status: 'fail', message: 'Health check failed' },
        cache: { status: 'fail', message: 'Health check failed' },
        data_integrity: { status: 'fail', message: 'Health check failed' },
        performance: { status: 'fail', message: 'Health check failed' }
      }
    }
  }
})