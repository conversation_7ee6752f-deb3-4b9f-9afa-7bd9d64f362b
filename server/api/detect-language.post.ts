/**
 * 语言检测API端点 (v3.0多语种支持)
 * POST /api/detect-language
 */

import type {
  LanguageDetectionRequest,
  LanguageDetectionResponse,
  APIResponse
} from '../types/api'
import { LanguageCode } from '../types/multilingual'
import {
  createSuccessResponse,
  createErrorResponse,
  createInternalErrorResponse
} from '../utils/api-response'
import { generateRequestId } from '../utils/common'
import {
  API,
  VALIDATION,
  ERROR_MESSAGES
} from '../config/constants'

/**
 * 简化的语言检测逻辑 (MVP实现)
 * 基于字符特征进行基础语言识别
 */
function detectLanguage(text: string): { language: LanguageCode; confidence: number; alternatives: { language: LanguageCode; confidence: number }[] } {
  if (!text || text.trim().length === 0) {
    return {
      language: LanguageCode.ZH_CN,
      confidence: 0.1,
      alternatives: []
    }
  }

  const cleanText = text.trim()

  // 中文检测 (汉字)
  const chineseRegex = /[\u4e00-\u9fff]/g
  const chineseMatches = cleanText.match(chineseRegex)
  const chineseRatio = chineseMatches ? chineseMatches.length / cleanText.length : 0

  // 日文检测 (平假名、片假名)
  const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/g
  const japaneseMatches = cleanText.match(japaneseRegex)
  const japaneseRatio = japaneseMatches ? japaneseMatches.length / cleanText.length : 0

  // 韩文检测 (韩文字母)
  const koreanRegex = /[\uac00-\ud7af]/g
  const koreanMatches = cleanText.match(koreanRegex)
  const koreanRatio = koreanMatches ? koreanMatches.length / cleanText.length : 0

  // 阿拉伯文检测
  const arabicRegex = /[\u0600-\u06ff]/g
  const arabicMatches = cleanText.match(arabicRegex)
  const arabicRatio = arabicMatches ? arabicMatches.length / cleanText.length : 0

  // 英文检测 (基础拉丁字母)
  const englishRegex = /[a-zA-Z]/g
  const englishMatches = cleanText.match(englishRegex)
  const englishRatio = englishMatches ? englishMatches.length / cleanText.length : 0

  // 计算各语言得分
  const scores = [
    { language: LanguageCode.ZH_CN, confidence: chineseRatio },
    { language: LanguageCode.JA_JP, confidence: japaneseRatio },
    { language: LanguageCode.KO_KR, confidence: koreanRatio },
    { language: LanguageCode.AR_SA, confidence: arabicRatio },
    { language: LanguageCode.EN_US, confidence: englishRatio },
    // 欧洲语言简化处理 (基于拉丁字母)
    { language: LanguageCode.ES_ES, confidence: englishRatio * 0.8 },
    { language: LanguageCode.FR_FR, confidence: englishRatio * 0.7 },
    { language: LanguageCode.DE_DE, confidence: englishRatio * 0.6 }
  ]

  // 排序并获取最高分
  scores.sort((a, b) => b.confidence - a.confidence)

  const primaryLanguage = scores[0]
  const alternatives = scores.slice(1, 4).filter(s => s.confidence > 0.1)

  return {
    language: primaryLanguage.language,
    confidence: Math.min(primaryLanguage.confidence + 0.2, 1.0), // 增加基础置信度
    alternatives
  }
}

/**
 * 验证语言检测请求参数
 */
function validateLanguageDetectionRequest(body: LanguageDetectionRequest): { isValid: boolean; errors?: string[] } {
  const errors: string[] = []

  // 检查text参数
  if (!body.text || typeof body.text !== 'string') {
    errors.push('text is required and must be a string')
  } else if (body.text.trim().length === 0) {
    errors.push('text cannot be empty')
  } else if (body.text.length > 1000) {
    errors.push('text cannot exceed 1000 characters')
  }

  // 检查confidence_threshold参数
  if (body.confidence_threshold !== undefined) {
    if (typeof body.confidence_threshold !== 'number' ||
        body.confidence_threshold < 0 ||
        body.confidence_threshold > 1) {
      errors.push('confidence_threshold must be a number between 0 and 1')
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  }
}

export default defineEventHandler(async (event): Promise<APIResponse<LanguageDetectionResponse>> => {
  const startTime = Date.now()
  const requestId = generateRequestId()

  try {
    // 解析请求体
    const body = await readBody(event) as LanguageDetectionRequest

    // 验证请求参数
    const validation = validateLanguageDetectionRequest(body)
    if (!validation.isValid) {
      setResponseStatus(event, 400)
      return createErrorResponse(
        {
          code: VALIDATION.ERROR_CODES.INVALID_PARAMETERS,
          message: ERROR_MESSAGES.INTERNAL.VALIDATION_FAILED,
          details: { errors: validation.errors }
        },
        {
          requestId,
          startTime,
          version: API.VERSION
        }
      )
    }

    // 执行语言检测
    const detection = detectLanguage(body.text)

    // 应用置信度阈值过滤
    const confidenceThreshold = body.confidence_threshold || 0.1
    const filteredAlternatives = detection.alternatives.filter(
      alt => alt.confidence >= confidenceThreshold
    )

    const response: LanguageDetectionResponse = {
      detected_language: detection.language,
      confidence: detection.confidence,
      alternatives: filteredAlternatives
    }

    // 返回成功响应
    return createSuccessResponse(response, {
      requestId,
      startTime,
      version: API.VERSION
    })

  } catch (error) {
    console.error('Language detection API error:', error)
    return createInternalErrorResponse(error, {
      requestId,
      startTime,
      version: API.VERSION
    })
  }
})