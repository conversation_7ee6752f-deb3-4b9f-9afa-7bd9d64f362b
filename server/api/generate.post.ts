/**
 * 用户名生成API端点
 * POST /api/generate
 */

import type { GenerateRequest, GenerateResponse, APIResponse } from '../types/api'
import type { GenerationContext } from '../types/core'
import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createInternalErrorResponse 
} from '../utils/api-response'
import { generateRequestId } from '../utils/common'
import { 
  API, 
  GENERATION, 
  VALIDATION, 
  ERROR_MESSAGES 
} from '../config/constants'

// 全局生成引擎实例（在实际应用中可能使用依赖注入）
let generationEngine: CoreGenerationEngine | null = null

async function getGenerationEngine(): Promise<CoreGenerationEngine> {
  if (!generationEngine) {
    generationEngine = new CoreGenerationEngine()
    await generationEngine.initialize()
  }
  return generationEngine
}

/**
 * 验证生成请求参数
 */
function validateGenerateRequest(body: GenerateRequest): { isValid: boolean; errors?: string[] } {
  const errors: string[] = []

  // 检查count参数
  if (body.count !== undefined) {
    if (typeof body.count !== 'number' || 
        body.count < VALIDATION.COUNT.MIN || 
        body.count > VALIDATION.COUNT.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_COUNT(
        VALIDATION.COUNT.MIN, 
        VALIDATION.COUNT.MAX
      ))
    }
  }

  // 检查creativity_level参数
  if (body.creativity_level !== undefined) {
    if (typeof body.creativity_level !== 'number' || 
        body.creativity_level < VALIDATION.CREATIVITY_LEVEL.MIN || 
        body.creativity_level > VALIDATION.CREATIVITY_LEVEL.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_CREATIVITY_LEVEL(
        VALIDATION.CREATIVITY_LEVEL.MIN, 
        VALIDATION.CREATIVITY_LEVEL.MAX
      ))
    }
  }

  // 检查quality_threshold参数
  if (body.quality_threshold !== undefined) {
    if (typeof body.quality_threshold !== 'number' || 
        body.quality_threshold < VALIDATION.QUALITY_THRESHOLD.MIN || 
        body.quality_threshold > VALIDATION.QUALITY_THRESHOLD.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_QUALITY_THRESHOLD(
        VALIDATION.QUALITY_THRESHOLD.MIN, 
        VALIDATION.QUALITY_THRESHOLD.MAX
      ))
    }
  }

  // 检查cultural_preference参数
  if (body.cultural_preference !== undefined) {
    if (!VALIDATION.CULTURAL_PREFERENCES.includes(body.cultural_preference)) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_CULTURAL_PREFERENCE(
        VALIDATION.CULTURAL_PREFERENCES
      ))
    }
  }

  // 检查style_preference参数
  if (body.style_preference !== undefined) {
    if (!VALIDATION.STYLE_PREFERENCES.includes(body.style_preference)) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_STYLE_PREFERENCE(
        VALIDATION.STYLE_PREFERENCES
      ))
    }
  }

  // 检查patterns参数
  if (body.patterns !== undefined && !Array.isArray(body.patterns)) {
    errors.push(ERROR_MESSAGES.VALIDATION.PATTERNS_MUST_BE_ARRAY)
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  }
}

export default defineEventHandler(async (event): Promise<APIResponse<GenerateResponse>> => {
  const startTime = Date.now()
  const requestId = generateRequestId()

  try {
    // 解析请求体
    const body = await readBody(event) as GenerateRequest

    // 验证请求参数
    const validation = validateGenerateRequest(body)
    if (!validation.isValid) {
      setResponseStatus(event, 400)
      return createErrorResponse(
        {
          code: VALIDATION.ERROR_CODES.INVALID_PARAMETERS,
          message: ERROR_MESSAGES.INTERNAL.VALIDATION_FAILED,
          details: { errors: validation.errors }
        },
        {
          requestId,
          startTime,
          version: API.VERSION
        }
      )
    }

    // 构建生成上下文
    const context: GenerationContext = {
      user_preferences: {
        cultural_preference: body.cultural_preference || GENERATION.DEFAULT_CULTURAL_PREFERENCE,
        style_preference: body.style_preference || GENERATION.DEFAULT_STYLE_PREFERENCE,
        creativity_level: body.creativity_level ?? GENERATION.DEFAULT_CREATIVITY_LEVEL,
        quality_threshold: body.quality_threshold ?? GENERATION.DEFAULT_QUALITY_THRESHOLD,
        preferred_length: body.length_preference || GENERATION.DEFAULT_LENGTH_PREFERENCE,
        favorite_patterns: [],
        excluded_categories: []
      },
      cultural_preference: body.cultural_preference || GENERATION.DEFAULT_CULTURAL_PREFERENCE,
      style_preference: body.style_preference || GENERATION.DEFAULT_STYLE_PREFERENCE,
      creativity_level: body.creativity_level ?? GENERATION.DEFAULT_CREATIVITY_LEVEL,
      quality_threshold: body.quality_threshold ?? GENERATION.DEFAULT_QUALITY_THRESHOLD,
      patterns: body.patterns,
      exclude_patterns: body.exclude_patterns,
      context_keywords: body.context?.keywords
    }

    // 获取生成引擎并生成用户名
    const engine = await getGenerationEngine()
    const count = Math.min(
      Math.max(
        body.count ?? GENERATION.DEFAULT_GENERATE_COUNT,
        VALIDATION.COUNT.MIN
      ),
      GENERATION.MAX_GENERATE_COUNT
    )

    const usernames = await engine.generate(context, count)

    const response: GenerateResponse = {
      usernames,
      generation_time: Date.now() - startTime,
      cache_hit: false, // MVP版本暂不实现缓存
      patterns_used: usernames.map(u => u.pattern),
      total_attempts: count // 简化统计
    }

    // 返回成功响应
    return createSuccessResponse(response, {
      requestId,
      startTime,
      version: API.VERSION
    })

  } catch (error) {
    console.error('Generate API error:', error)
    return createInternalErrorResponse(error, {
      requestId,
      startTime,
      version: API.VERSION
    })
  }
})