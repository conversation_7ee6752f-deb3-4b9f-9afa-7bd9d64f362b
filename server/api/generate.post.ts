/**
 * 用户名生成API端点 (v3.0多语种支持)
 * POST /api/generate
 */

import type { GenerateRequest, GenerateResponse, APIResponse } from '../types/api'
import type { GenerationContext } from '../types/core'
import type { LanguageCode } from '../types/multilingual'
import { CoreGenerationEngine } from '../core/engines/CoreGenerationEngine'
import {
  createSuccessResponse,
  createErrorResponse,
  createInternalErrorResponse
} from '../utils/api-response'
import { generateRequestId } from '../utils/common'
import {
  API,
  GENERATION,
  VALIDATION,
  ERROR_MESSAGES
} from '../config/constants'

// 全局生成引擎实例（在实际应用中可能使用依赖注入）
let generationEngine: CoreGenerationEngine | null = null

async function getGenerationEngine(): Promise<CoreGenerationEngine> {
  if (!generationEngine) {
    generationEngine = new CoreGenerationEngine()
    await generationEngine.initialize()
  }
  return generationEngine
}

/**
 * 验证生成请求参数
 */
function validateGenerateRequest(body: GenerateRequest): { isValid: boolean; errors?: string[] } {
  const errors: string[] = []

  // 检查count参数
  if (body.count !== undefined) {
    if (typeof body.count !== 'number' || 
        body.count < VALIDATION.COUNT.MIN || 
        body.count > VALIDATION.COUNT.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_COUNT(
        VALIDATION.COUNT.MIN, 
        VALIDATION.COUNT.MAX
      ))
    }
  }

  // 检查creativity_level参数
  if (body.creativity_level !== undefined) {
    if (typeof body.creativity_level !== 'number' || 
        body.creativity_level < VALIDATION.CREATIVITY_LEVEL.MIN || 
        body.creativity_level > VALIDATION.CREATIVITY_LEVEL.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_CREATIVITY_LEVEL(
        VALIDATION.CREATIVITY_LEVEL.MIN, 
        VALIDATION.CREATIVITY_LEVEL.MAX
      ))
    }
  }

  // 检查quality_threshold参数
  if (body.quality_threshold !== undefined) {
    if (typeof body.quality_threshold !== 'number' || 
        body.quality_threshold < VALIDATION.QUALITY_THRESHOLD.MIN || 
        body.quality_threshold > VALIDATION.QUALITY_THRESHOLD.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_QUALITY_THRESHOLD(
        VALIDATION.QUALITY_THRESHOLD.MIN, 
        VALIDATION.QUALITY_THRESHOLD.MAX
      ))
    }
  }

  // 检查cultural_preference参数
  if (body.cultural_preference !== undefined) {
    if (!VALIDATION.CULTURAL_PREFERENCES.includes(body.cultural_preference)) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_CULTURAL_PREFERENCE(
        VALIDATION.CULTURAL_PREFERENCES
      ))
    }
  }

  // 检查style_preference参数
  if (body.style_preference !== undefined) {
    if (!VALIDATION.STYLE_PREFERENCES.includes(body.style_preference)) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_STYLE_PREFERENCE(
        VALIDATION.STYLE_PREFERENCES
      ))
    }
  }

  // 检查patterns参数
  if (body.patterns !== undefined && !Array.isArray(body.patterns)) {
    errors.push(ERROR_MESSAGES.VALIDATION.PATTERNS_MUST_BE_ARRAY)
  }

  // v3.0多语种参数验证
  // 检查language参数
  if (body.language !== undefined) {
    if (!VALIDATION.SUPPORTED_LANGUAGES.includes(body.language as any)) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_LANGUAGE(
        VALIDATION.SUPPORTED_LANGUAGES.map(lang => lang.toString())
      ))
    }
  }

  // 检查concept_preferences参数
  if (body.concept_preferences !== undefined) {
    if (typeof body.concept_preferences !== 'object' || body.concept_preferences === null) {
      errors.push(ERROR_MESSAGES.VALIDATION.CONCEPT_PREFERENCES_INVALID)
    } else {
      // 检查abstraction_level
      if (body.concept_preferences.abstraction_level !== undefined) {
        if (typeof body.concept_preferences.abstraction_level !== 'number' ||
            body.concept_preferences.abstraction_level < 0 ||
            body.concept_preferences.abstraction_level > 1) {
          errors.push('abstraction_level must be a number between 0 and 1')
        }
      }
    }
  }

  // 检查multilingual_options参数
  if (body.multilingual_options !== undefined) {
    if (typeof body.multilingual_options !== 'object' || body.multilingual_options === null) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_MULTILINGUAL_OPTIONS)
    } else {
      // 检查fallback_languages
      if (body.multilingual_options.fallback_languages !== undefined) {
        if (!Array.isArray(body.multilingual_options.fallback_languages)) {
          errors.push('fallback_languages must be an array')
        } else {
          for (const lang of body.multilingual_options.fallback_languages) {
            if (!VALIDATION.SUPPORTED_LANGUAGES.includes(lang as any)) {
              errors.push(`Invalid fallback language: ${lang}`)
            }
          }
        }
      }
    }
  }

  // 检查context.cultural_sensitivity参数
  if (body.context?.cultural_sensitivity !== undefined) {
    if (typeof body.context.cultural_sensitivity !== 'number' ||
        body.context.cultural_sensitivity < VALIDATION.CULTURAL_SENSITIVITY.MIN ||
        body.context.cultural_sensitivity > VALIDATION.CULTURAL_SENSITIVITY.MAX) {
      errors.push(ERROR_MESSAGES.VALIDATION.INVALID_CULTURAL_SENSITIVITY(
        VALIDATION.CULTURAL_SENSITIVITY.MIN,
        VALIDATION.CULTURAL_SENSITIVITY.MAX
      ))
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  }
}

export default defineEventHandler(async (event): Promise<APIResponse<GenerateResponse>> => {
  const startTime = Date.now()
  const requestId = generateRequestId()

  try {
    // 解析请求体
    const body = await readBody(event) as GenerateRequest

    // 验证请求参数
    const validation = validateGenerateRequest(body)
    if (!validation.isValid) {
      setResponseStatus(event, 400)
      return createErrorResponse(
        {
          code: VALIDATION.ERROR_CODES.INVALID_PARAMETERS,
          message: ERROR_MESSAGES.INTERNAL.VALIDATION_FAILED,
          details: { errors: validation.errors }
        },
        {
          requestId,
          startTime,
          version: API.VERSION
        }
      )
    }

    // 构建生成上下文 (v3.0多语种支持)
    const context: GenerationContext = {
      user_preferences: {
        cultural_preference: body.cultural_preference || GENERATION.DEFAULT_CULTURAL_PREFERENCE,
        style_preference: body.style_preference || GENERATION.DEFAULT_STYLE_PREFERENCE,
        creativity_level: body.creativity_level ?? GENERATION.DEFAULT_CREATIVITY_LEVEL,
        quality_threshold: body.quality_threshold ?? GENERATION.DEFAULT_QUALITY_THRESHOLD,
        preferred_length: body.length_preference || GENERATION.DEFAULT_LENGTH_PREFERENCE,
        favorite_patterns: [],
        excluded_categories: []
      },
      cultural_preference: body.cultural_preference || GENERATION.DEFAULT_CULTURAL_PREFERENCE,
      style_preference: body.style_preference || GENERATION.DEFAULT_STYLE_PREFERENCE,
      creativity_level: body.creativity_level ?? GENERATION.DEFAULT_CREATIVITY_LEVEL,
      quality_threshold: body.quality_threshold ?? GENERATION.DEFAULT_QUALITY_THRESHOLD,
      patterns: body.patterns,
      exclude_patterns: body.exclude_patterns,
      context_keywords: body.context?.keywords,
      // v3.0多语种新增字段
      target_language: body.language || GENERATION.DEFAULT_LANGUAGE,
      concept_preferences: body.concept_preferences,
      multilingual_options: body.multilingual_options,
      cultural_sensitivity: body.context?.cultural_sensitivity
    }

    // 获取生成引擎并生成用户名
    const engine = await getGenerationEngine()
    const count = Math.min(
      Math.max(
        body.count ?? GENERATION.DEFAULT_GENERATE_COUNT,
        VALIDATION.COUNT.MIN
      ),
      GENERATION.MAX_GENERATE_COUNT
    )

    const usernames = await engine.generate(context, count)

    // 构建v3.0多语种响应
    const actualLanguage = body.language || GENERATION.DEFAULT_LANGUAGE
    const qualityDistribution = {
      excellent: usernames.filter(u => u.quality_score.overall >= 0.8).length,
      good: usernames.filter(u => u.quality_score.overall >= 0.6 && u.quality_score.overall < 0.8).length,
      acceptable: usernames.filter(u => u.quality_score.overall < 0.6).length
    }

    const response: GenerateResponse = {
      usernames,
      generation_time: Date.now() - startTime,
      cache_hit: false, // v3.0版本暂不实现缓存
      // v3.0多语种新增字段
      language: actualLanguage,
      semantic_consistency_score: body.multilingual_options?.maintain_semantic_consistency ? 0.85 : undefined,
      // 传统字段 (向后兼容)
      patterns_used: usernames.map(u => u.pattern),
      total_attempts: count,
      // v3.0质量统计增强
      quality_distribution: qualityDistribution
    }

    // 返回成功响应
    return createSuccessResponse(response, {
      requestId,
      startTime,
      version: API.VERSION
    })

  } catch (error) {
    console.error('Generate API error:', error)
    return createInternalErrorResponse(error, {
      requestId,
      startTime,
      version: API.VERSION
    })
  }
})