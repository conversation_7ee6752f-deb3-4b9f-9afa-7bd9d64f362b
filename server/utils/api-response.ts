import type { APIResponse } from '../types/api'

interface ResponseMetaOptions {
  requestId: string
  startTime: number
  version?: string
  includeErrorDetails?: boolean
}

/**
 * 创建成功的 API 响应
 */
export function createSuccessResponse<T = unknown>(
  data: T,
  meta: ResponseMetaOptions
): APIResponse<T> {
  const now = Date.now()
  return {
    success: true,
    data,
    meta: {
      timestamp: now,
      request_id: meta.requestId,
      execution_time: now - meta.startTime,
      version: meta.version || '1.0.0-mvp'
    }
  }
}

/**
 * 创建错误的 API 响应
 */
export function createErrorResponse(
  error: {
    code: string
    message: string
    details?: Record<string, unknown>
  },
  meta: ResponseMetaOptions
): APIResponse<never> {
  const now = Date.now()
  const errorDetails = meta.includeErrorDetails
    ? { details: error.details }
    : {}

  return {
    success: false,
    error: {
      code: error.code,
      message: error.message,
      ...errorDetails
    },
    meta: {
      timestamp: now,
      request_id: meta.requestId,
      execution_time: now - meta.startTime,
      version: meta.version || '1.0.0-mvp'
    }
  }
}

/**
 * 创建内部服务器错误的 API 响应
 */
export function createInternalErrorResponse(
  error: unknown,
  meta: Omit<ResponseMetaOptions, 'includeErrorDetails'>
): APIResponse<never> {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
  
  return createErrorResponse(
    {
      code: 'E5001',
      message: 'Internal server error',
      ...(process.env.NODE_ENV === 'development' && {
        details: { error: errorMessage, stack: error instanceof Error ? error.stack : undefined }
      })
    },
    {
      ...meta,
      includeErrorDetails: process.env.NODE_ENV === 'development'
    }
  )
}
