/**
 * 错误处理工具函数
 */

/**
 * 从错误对象中安全地提取错误信息
 * @param error 错误对象，可以是 Error 实例或任何其他类型
 * @returns 错误信息字符串
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  
  return 'An unknown error occurred'
}

/**
 * 从错误对象中获取堆栈跟踪信息
 * @param error 错误对象
 * @returns 堆栈跟踪字符串（如果可用）
 */
export function getErrorStack(error: unknown): string | undefined {
  if (error instanceof Error) {
    return error.stack
  }
  return undefined
}
