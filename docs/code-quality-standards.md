# 代码质量标准文档

**版本**: 1.0.0  
**创建时间**: 2025-06-24  
**作者**: namer-v6 team

## 概述

本文档定义了namer-v6项目的代码质量标准和最佳实践，确保代码的可维护性、可读性和可靠性。

## TypeScript 最佳实践

### 1. 类型安全

- ✅ **禁止使用 `any` 类型**
  ```typescript
  // ❌ 错误
  function process(data: any): any {
    return data.someProperty
  }
  
  // ✅ 正确
  function process<T extends { someProperty: unknown }>(data: T): T['someProperty'] {
    return data.someProperty
  }
  ```

- ✅ **使用严格的类型检查**
  ```json
  // tsconfig.json
  {
    "compilerOptions": {
      "strict": true,
      "noImplicitAny": true,
      "strictNullChecks": true,
      "strictFunctionTypes": true
    }
  }
  ```

### 2. 接口和类型定义

- ✅ **使用描述性的接口名称**
  ```typescript
  // ✅ 正确
  interface UserPreferences {
    theme: 'light' | 'dark'
    language: LanguageCode
  }
  ```

- ✅ **为复杂类型提供JSDoc注释**
  ```typescript
  /**
   * 用户偏好配置接口
   * 
   * @interface UserPreferences
   * @property {string} theme - 主题设置
   * @property {LanguageCode} language - 语言设置
   */
  interface UserPreferences {
    theme: 'light' | 'dark'
    language: LanguageCode
  }
  ```

## 配置管理标准

### 1. 避免硬编码

- ✅ **将常量提取到配置文件**
  ```typescript
  // ❌ 错误 - 硬编码
  if (score > 0.8) {
    return 'excellent'
  }
  
  // ✅ 正确 - 使用配置
  import { QUALITY_THRESHOLDS } from '../config/constants'
  
  if (score > QUALITY_THRESHOLDS.EXCELLENT) {
    return 'excellent'
  }
  ```

- ✅ **使用 `as const` 确保类型安全**
  ```typescript
  export const SUPPORTED_LANGUAGES = {
    ZH_CN: 'zh-CN',
    EN_US: 'en-US'
  } as const
  ```

### 2. 配置文件结构

```
server/config/
├── constants.ts          # 全局常量
├── test-constants.ts     # 测试专用常量
├── validation-rules.ts   # 验证规则配置
└── environment.ts        # 环境相关配置
```

## JSDoc 文档标准

### 1. 文件级注释

每个TypeScript文件必须包含完整的文件级JSDoc注释：

```typescript
/**
 * 文件功能描述
 *
 * 详细说明文件的用途、主要功能和设计思路
 *
 * @fileoverview 简短的文件概述
 * @version 1.0.0
 * @since 2025-06-24
 * <AUTHOR> team
 */
```

### 2. 函数和方法注释

```typescript
/**
 * 函数功能描述
 *
 * @param {string} param1 - 参数1描述
 * @param {number} param2 - 参数2描述
 * @returns {Promise<boolean>} 返回值描述
 * @throws {Error} 可能抛出的错误
 * @example
 * ```typescript
 * const result = await myFunction('test', 42)
 * console.log(result) // true
 * ```
 */
async function myFunction(param1: string, param2: number): Promise<boolean> {
  // 实现
}
```

### 3. 接口和类型注释

```typescript
/**
 * 接口描述
 *
 * @interface MyInterface
 * @property {string} prop1 - 属性1描述
 * @property {number} prop2 - 属性2描述
 */
interface MyInterface {
  /** 属性1 - 简短描述 */
  prop1: string
  /** 属性2 - 简短描述 */
  prop2: number
}
```

## 测试标准

### 1. 测试覆盖率要求

- **全局覆盖率**: 85%
- **核心模块覆盖率**: 90%
- **语句覆盖率**: 85%
- **分支覆盖率**: 80%
- **函数覆盖率**: 85%
- **行覆盖率**: 85%

### 2. 测试文件结构

```typescript
import { describe, it, expect } from '@jest/globals'
import { TEST_CONSTANTS } from '../config/test-constants'

describe('模块名称', () => {
  describe('功能分组', () => {
    it('应该执行特定行为', () => {
      // 测试实现
      expect(result).toBe(TEST_CONSTANTS.EXPECTED_VALUE)
    })
  })
})
```

### 3. 测试常量管理

- 将测试中的硬编码值提取到 `test-constants.ts`
- 使用描述性的常量名称
- 为测试数据提供类型定义

## 代码风格标准

### 1. 命名约定

- **文件名**: kebab-case (`user-preferences.ts`)
- **类名**: PascalCase (`UserPreferences`)
- **函数名**: camelCase (`getUserPreferences`)
- **常量**: SCREAMING_SNAKE_CASE (`MAX_RETRY_COUNT`)
- **接口**: PascalCase (`UserPreferences`)
- **枚举**: PascalCase (`LanguageCode`)

### 2. 代码组织

```typescript
// 1. 导入语句
import { ... } from '...'

// 2. 类型定义
interface MyInterface { ... }

// 3. 常量定义
const CONSTANTS = { ... }

// 4. 主要实现
export class MyClass { ... }
```

## 质量检查流程

### 1. 开发阶段检查

```bash
# TypeScript编译检查
pnpm run check

# 运行测试
pnpm run test

# 代码风格检查
pnpm run lint

# 完整质量检查
pnpm run quality:check
```

### 2. 提交前检查清单

- [ ] TypeScript编译无错误
- [ ] 所有测试通过
- [ ] 测试覆盖率达标
- [ ] JSDoc文档完整
- [ ] 无硬编码值
- [ ] 代码风格符合标准

### 3. 持续集成检查

在CI/CD流程中自动执行：
1. TypeScript编译检查
2. 单元测试执行
3. 代码覆盖率检查
4. 代码风格检查
5. 文档生成检查

## 工具配置

### 1. 必需的配置文件

- `tsconfig.json` - TypeScript配置
- `jest.config.js` - 测试配置
- `jsdoc.config.js` - 文档生成配置
- `.eslintrc.js` - 代码风格配置（可选）

### 2. 推荐的VS Code扩展

- TypeScript Importer
- Jest Runner
- ESLint
- JSDoc Generator

## 总结

遵循这些代码质量标准将确保：

1. **类型安全**: 避免运行时类型错误
2. **可维护性**: 代码易于理解和修改
3. **可测试性**: 高测试覆盖率和质量
4. **文档完整性**: 完善的API文档
5. **一致性**: 统一的代码风格和结构

定期审查和更新这些标准，确保它们与项目需求和最佳实践保持同步。
