# namer-v6 系统架构文档

## 概述

namer-v6 是一个基于第一性原理设计的智能用户名生成系统，采用模块化架构，支持多语言扩展和智能质量评估。

## 系统架构图

### 核心组件架构

```mermaid
graph TB
    subgraph "数据层 (Data Layer)"
        DF[数据文件<br/>morphemes.json]
        DFB[数据文件<br/>morphemes_base.json]
    end
    
    subgraph "数据管理层 (Data Management Layer)"
        DL[DataLoader<br/>数据加载器]
        DV[DataValidator<br/>数据验证器]
        
        DL --> |加载| DF
        DL --> |加载| DFB
        DL --> |验证| DV
    end
    
    subgraph "存储层 (Storage Layer)"
        MR[MorphemeRepository<br/>语素仓库]
        
        subgraph "多维度索引系统"
            IDX1[类别索引<br/>byCategory]
            IDX2[文化语境索引<br/>byContext]
            IDX3[子分类索引<br/>bySubcategory]
            IDX4[质量索引<br/>byQuality]
            IDX5[语义聚类索引<br/>bySemanticCluster]
            IDX6[标签索引<br/>byTags]
        end
        
        subgraph "采样算法"
            AT[Alias Table<br/>O(1)采样算法]
        end
        
        DL --> |提供数据| MR
        DV --> |验证结果| MR
        MR --> IDX1
        MR --> IDX2
        MR --> IDX3
        MR --> IDX4
        MR --> IDX5
        MR --> IDX6
        MR --> AT
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        CGE[CoreGenerationEngine<br/>核心生成引擎]
        
        subgraph "质量评估系统"
            QE[8维度质量评估<br/>Quality Evaluator]
            QE1[创意性评估]
            QE2[记忆性评估]
            QE3[文化适配评估]
            QE4[独特性评估]
            QE5[发音友好度评估]
            QE6[语义连贯性评估]
            QE7[美学吸引力评估]
            QE8[实用性评估]
            
            QE --> QE1
            QE --> QE2
            QE --> QE3
            QE --> QE4
            QE --> QE5
            QE --> QE6
            QE --> QE7
            QE --> QE8
        end
        
        subgraph "生成策略"
            MS[语素选择策略]
            CS[组合策略]
            FS[筛选策略]
        end
        
        MR --> |查询&采样| CGE
        CGE --> MS
        CGE --> CS
        CGE --> FS
        CGE --> QE
    end
    
    subgraph "接口层 (API Layer)"
        API[REST API<br/>用户接口]
        
        API --> |生成请求| CGE
        CGE --> |生成结果| API
    end
    
    subgraph "客户端 (Client Layer)"
        WEB[Web界面]
        CLI[命令行工具]
        
        WEB --> API
        CLI --> API
    end

    classDef dataLayer fill:#e1f5fe
    classDef managementLayer fill:#f3e5f5
    classDef storageLayer fill:#e8f5e8
    classDef businessLayer fill:#fff3e0
    classDef apiLayer fill:#fce4ec
    classDef clientLayer fill:#f1f8e9

    class DF,DFB dataLayer
    class DL,DV managementLayer
    class MR,IDX1,IDX2,IDX3,IDX4,IDX5,IDX6,AT storageLayer
    class CGE,QE,QE1,QE2,QE3,QE4,QE5,QE6,QE7,QE8,MS,CS,FS businessLayer
    class API apiLayer
    class WEB,CLI clientLayer
```

### 数据流向图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as REST API
    participant CGE as 核心生成引擎
    participant MR as 语素仓库
    participant DL as 数据加载器
    participant DV as 数据验证器
    participant FS as 文件系统

    Note over DL,FS: 系统初始化阶段
    DL->>FS: 扫描数据文件
    FS-->>DL: 返回文件列表
    DL->>FS: 加载语素数据
    FS-->>DL: 返回原始数据
    DL->>DV: 验证数据完整性
    DV-->>DL: 返回验证结果
    DL->>MR: 提供验证后的数据
    MR->>MR: 构建多维度索引
    MR->>MR: 构建Alias Table

    Note over Client,MR: 用户名生成阶段
    Client->>API: 发送生成请求
    API->>CGE: 调用生成方法
    CGE->>MR: 查询语素数据
    MR-->>CGE: 返回采样结果
    CGE->>CGE: 语素组合
    CGE->>CGE: 8维度质量评估
    CGE-->>API: 返回生成结果
    API-->>Client: 返回用户名列表
```

## 核心组件详解

### 1. DataLoader (数据加载器)

**职责**: 负责从文件系统加载语素数据，支持热重载和缓存

**核心功能**:
- 支持JSON和JSONL格式
- 数据格式兼容性处理 (v1.0 → v2.0)
- 热重载机制
- 缓存优化
- 错误恢复

**接口**:
```typescript
interface DataLoader {
  loadAll(): Promise<DataLoadResult>
  reload(): Promise<void>
  destroy(): void
}
```

### 2. DataValidator (数据验证器)

**职责**: 确保数据质量和完整性

**核心功能**:
- 8维度数据验证
- 向后兼容性检查
- 自定义验证规则
- 详细错误报告

**接口**:
```typescript
interface DataValidator {
  validate(morphemes: Morpheme[]): Promise<ValidationResult>
  addCustomRule(rule: ValidationRule): void
}
```

### 3. MorphemeRepository (语素仓库)

**职责**: 高效的语素数据管理和查询

**核心功能**:
- 多维度索引系统 (6个维度)
- O(1) Alias Table采样算法
- 语义相似度计算
- 统计信息管理

**接口**:
```typescript
interface MorphemeRepository {
  findByCategory(category: MorphemeCategory): Morpheme[]
  sampleByCategory(category: MorphemeCategory, count: number): Morpheme[]
  findSimilar(target: Morpheme, threshold: number): SimilarityResult[]
  getStats(): MorphemeRepositoryStats
}
```

### 4. CoreGenerationEngine (核心生成引擎)

**职责**: 协调各组件完成用户名生成

**核心功能**:
- 智能语素选择
- 8维度质量评估
- 批量生成 (1-10个)
- 性能统计

**接口**:
```typescript
interface CoreGenerationEngine {
  generate(context: GenerationContext, count: number): Promise<GeneratedUsername[]>
  getStats(): EngineStats
}
```

## 质量评估体系

### 8维度评估框架

基于认知心理学和语言学理论设计的质量评估体系：

1. **创意性 (Creativity)** - 权重: 15%
   - 语素组合新颖性
   - 语义距离
   - 文化创新性

2. **记忆性 (Memorability)** - 权重: 20%
   - 长度记忆性 (Miller's Rule)
   - 音韵记忆性
   - 语义记忆性

3. **文化适配度 (Cultural Fit)** - 权重: 15%
   - 直接文化匹配
   - 文化和谐性
   - 时代适应性

4. **独特性 (Uniqueness)** - 权重: 12%
   - 语素稀有性
   - 组合独特性
   - 字符独特性

5. **发音友好度 (Pronunciation)** - 权重: 13%
   - 音节流畅性
   - 声调和谐性
   - 国际化友好度

6. **语义连贯性 (Semantic Coherence)** - 权重: 10%
   - 语义相关性
   - 概念层次一致性
   - 语法合理性

7. **美学吸引力 (Aesthetic Appeal)** - 权重: 8%
   - 字形美感
   - 音韵美感
   - 意境美感

8. **实用性 (Practical Usability)** - 权重: 7%
   - 输入便利性
   - 平台兼容性
   - 传播便利性

## 技术特性

### 性能优化

- **O(1)采样算法**: Walker's Alias Method
- **多维度索引**: 支持高效查询
- **缓存机制**: 减少重复加载
- **批量处理**: 1-10个用户名批量生成

### 可扩展性

- **模块化设计**: 松耦合组件架构
- **接口抽象**: 支持不同实现
- **配置驱动**: 灵活的参数配置
- **多语言支持**: 数据结构支持扩展

### 可观测性

- **详细日志**: 完整的操作日志
- **性能指标**: 生成时间、成功率统计
- **错误追踪**: 完善的错误处理
- **数据验证**: 实时数据质量监控

## 部署架构

### 开发环境
```
Client (Browser/CLI) → API Server → Core Engine → Data Files
```

### 生产环境
```
Load Balancer → API Servers → Core Engines → Distributed Cache → Data Storage
```

## 版本信息

- **当前版本**: 2.0.0
- **数据格式版本**: 支持 v1.0, v2.0
- **API版本**: v1
- **最后更新**: 2025-06-23

---

*本文档描述了namer-v6系统的核心架构设计，基于第一性原理和现代软件工程最佳实践。*
