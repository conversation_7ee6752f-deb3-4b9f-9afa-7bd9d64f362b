# 技术实施建议与最佳实践

**文档类型**: 🛠️ 技术实施指南与最佳实践
**制定时间**: 2025-06-22
**适用范围**: namer-v6 Phase 2 开发实施
**技术栈**: Nuxt.js + TypeScript + Node.js

---

## 🏗️ **架构设计最佳实践**

### **1. 依赖注入容器实现**
```typescript
// 推荐实现：轻量级依赖注入容器
interface Container {
  register<T>(token: string, factory: () => T): void
  resolve<T>(token: string): T
  singleton<T>(token: string, factory: () => T): void
}

class DIContainer implements Container {
  private services = new Map<string, any>()
  private singletons = new Map<string, any>()

  register<T>(token: string, factory: () => T): void {
    this.services.set(token, factory)
  }

  singleton<T>(token: string, factory: () => T): void {
    this.singletons.set(token, factory)
  }

  resolve<T>(token: string): T {
    if (this.singletons.has(token)) {
      const factory = this.singletons.get(token)
      if (!this.services.has(`${token}_instance`)) {
        this.services.set(`${token}_instance`, factory())
      }
      return this.services.get(`${token}_instance`)
    }

    const factory = this.services.get(token)
    if (!factory) {
      throw new Error(`Service ${token} not found`)
    }
    return factory()
  }
}

// 使用示例
const container = new DIContainer()
container.singleton('morphemeRepo', () => new MorphemeRepository())
container.singleton('generationEngine', () => new CoreGenerationEngine(
  container.resolve('morphemeRepo')
))
```

### **2. 配置管理系统设计**
```typescript
// 推荐实现：类型安全的配置管理
interface AppConfig {
  server: {
    port: number
    host: string
    cors: boolean
  }
  cache: {
    l1: { size: number; ttl: number }
    l2: { size: number; ttl: number }
    l3: { size: number; ttl: number }
  }
  generation: {
    maxCount: number
    defaultQualityThreshold: number
    maxConcurrentRequests: number
  }
  monitoring: {
    enabled: boolean
    metricsInterval: number
    alertThresholds: {
      responseTime: number
      errorRate: number
      memoryUsage: number
    }
  }
}

class ConfigManager {
  private config: AppConfig

  constructor() {
    this.config = this.loadConfig()
    this.validateConfig()
  }

  private loadConfig(): AppConfig {
    return {
      server: {
        port: Number(process.env.PORT) || 3000,
        host: process.env.HOST || 'localhost',
        cors: process.env.CORS === 'true'
      },
      cache: {
        l1: {
          size: Number(process.env.L1_CACHE_SIZE) || 1024 * 1024,
          ttl: Number(process.env.L1_CACHE_TTL) || 300000
        },
        l2: {
          size: Number(process.env.L2_CACHE_SIZE) || 10 * 1024 * 1024,
          ttl: Number(process.env.L2_CACHE_TTL) || 1800000
        },
        l3: {
          size: Number(process.env.L3_CACHE_SIZE) || 50 * 1024 * 1024,
          ttl: Number(process.env.L3_CACHE_TTL) || 7200000
        }
      },
      generation: {
        maxCount: Number(process.env.MAX_GENERATION_COUNT) || 20,
        defaultQualityThreshold: Number(process.env.DEFAULT_QUALITY_THRESHOLD) || 0.7,
        maxConcurrentRequests: Number(process.env.MAX_CONCURRENT_REQUESTS) || 100
      },
      monitoring: {
        enabled: process.env.MONITORING_ENABLED === 'true',
        metricsInterval: Number(process.env.METRICS_INTERVAL) || 60000,
        alertThresholds: {
          responseTime: Number(process.env.ALERT_RESPONSE_TIME) || 100,
          errorRate: Number(process.env.ALERT_ERROR_RATE) || 0.05,
          memoryUsage: Number(process.env.ALERT_MEMORY_USAGE) || 0.8
        }
      }
    }
  }

  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key]
  }

  private validateConfig(): void {
    // 配置验证逻辑
    if (this.config.server.port < 1 || this.config.server.port > 65535) {
      throw new Error('Invalid port number')
    }
    // 更多验证...
  }
}
```

### **3. 日志系统设计**
```typescript
// 推荐实现：结构化日志系统
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

interface LogEntry {
  timestamp: number
  level: LogLevel
  message: string
  context?: Record<string, any>
  requestId?: string
  userId?: string
  duration?: number
}

class Logger {
  private level: LogLevel
  private outputs: LogOutput[]

  constructor(level: LogLevel = LogLevel.INFO) {
    this.level = level
    this.outputs = [new ConsoleOutput()]
  }

  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context)
  }

  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context)
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context)
  }

  error(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context)
  }

  private log(level: LogLevel, message: string, context?: Record<string, any>): void {
    if (level < this.level) return

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      context
    }

    this.outputs.forEach(output => output.write(entry))
  }
}

interface LogOutput {
  write(entry: LogEntry): void
}

class ConsoleOutput implements LogOutput {
  write(entry: LogEntry): void {
    const levelName = LogLevel[entry.level]
    const timestamp = new Date(entry.timestamp).toISOString()
    console.log(`[${timestamp}] ${levelName}: ${entry.message}`, entry.context || '')
  }
}
```

---

## 🚀 **性能优化实施策略**

### **1. 高效缓存系统实现**
```typescript
// 推荐实现：多级缓存管理器
interface CacheEntry<T> {
  value: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccess: number
}

class MultiLevelCache<T> {
  private l1Cache = new Map<string, CacheEntry<T>>()
  private l2Cache = new Map<string, CacheEntry<T>>()
  private l3Cache = new Map<string, CacheEntry<T>>()

  private readonly l1Config = { maxSize: 1000, ttl: 5 * 60 * 1000 }
  private readonly l2Config = { maxSize: 10000, ttl: 30 * 60 * 1000 }
  private readonly l3Config = { maxSize: 50000, ttl: 2 * 60 * 60 * 1000 }

  async get(key: string): Promise<T | null> {
    // L1 缓存查找
    const l1Entry = this.l1Cache.get(key)
    if (l1Entry && this.isValid(l1Entry)) {
      l1Entry.accessCount++
      l1Entry.lastAccess = Date.now()
      return l1Entry.value
    }

    // L2 缓存查找
    const l2Entry = this.l2Cache.get(key)
    if (l2Entry && this.isValid(l2Entry)) {
      // 提升到L1
      this.setL1(key, l2Entry.value, l2Entry.ttl)
      return l2Entry.value
    }

    // L3 缓存查找
    const l3Entry = this.l3Cache.get(key)
    if (l3Entry && this.isValid(l3Entry)) {
      // 提升到L2
      this.setL2(key, l3Entry.value, l3Entry.ttl)
      return l3Entry.value
    }

    return null
  }

  async set(key: string, value: T, level: 1 | 2 | 3 = 1): Promise<void> {
    switch (level) {
      case 1:
        this.setL1(key, value, this.l1Config.ttl)
        break
      case 2:
        this.setL2(key, value, this.l2Config.ttl)
        break
      case 3:
        this.setL3(key, value, this.l3Config.ttl)
        break
    }
  }

  private setL1(key: string, value: T, ttl: number): void {
    if (this.l1Cache.size >= this.l1Config.maxSize) {
      this.evictLRU(this.l1Cache)
    }

    this.l1Cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
      accessCount: 1,
      lastAccess: Date.now()
    })
  }

  private isValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl
  }

  private evictLRU<T>(cache: Map<string, CacheEntry<T>>): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, entry] of cache.entries()) {
      if (entry.lastAccess < oldestTime) {
        oldestTime = entry.lastAccess
        oldestKey = key
      }
    }

    if (oldestKey) {
      cache.delete(oldestKey)
    }
  }

  getStats() {
    return {
      l1: { size: this.l1Cache.size, maxSize: this.l1Config.maxSize },
      l2: { size: this.l2Cache.size, maxSize: this.l2Config.maxSize },
      l3: { size: this.l3Cache.size, maxSize: this.l3Config.maxSize }
    }
  }
}
```

### **2. Alias Table算法实现**
```typescript
// 推荐实现：O(1)语素采样算法
class AliasTable {
  private prob: number[]
  private alias: number[]

  constructor(weights: number[]) {
    const n = weights.length
    this.prob = new Array(n)
    this.alias = new Array(n)

    // 归一化权重
    const sum = weights.reduce((a, b) => a + b, 0)
    const normalizedWeights = weights.map(w => w * n / sum)

    // 构建Alias Table
    const small: number[] = []
    const large: number[] = []

    for (let i = 0; i < n; i++) {
      if (normalizedWeights[i] < 1.0) {
        small.push(i)
      } else {
        large.push(i)
      }
    }

    while (small.length > 0 && large.length > 0) {
      const l = small.pop()!
      const g = large.pop()!

      this.prob[l] = normalizedWeights[l]
      this.alias[l] = g

      normalizedWeights[g] = normalizedWeights[g] + normalizedWeights[l] - 1.0

      if (normalizedWeights[g] < 1.0) {
        small.push(g)
      } else {
        large.push(g)
      }
    }

    while (large.length > 0) {
      this.prob[large.pop()!] = 1.0
    }

    while (small.length > 0) {
      this.prob[small.pop()!] = 1.0
    }
  }

  sample(): number {
    const n = this.prob.length
    const i = Math.floor(Math.random() * n)
    const r = Math.random()

    return r < this.prob[i] ? i : this.alias[i]
  }
}
```

---

## 🧪 **测试策略与质量保证**

### **1. 测试框架配置**
```typescript
// vitest.config.ts - 推荐配置
import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    setupFiles: ['./tests/setup.ts'],
    testTimeout: 10000
  },
  resolve: {
    alias: {
      '~': resolve(__dirname, './'),
      '@': resolve(__dirname, './server')
    }
  }
})
```

### **2. 测试工具类**
```typescript
// tests/utils/TestHelper.ts
export class TestHelper {
  static createMockMorpheme(overrides: Partial<Morpheme> = {}): Morpheme {
    return {
      id: 'test_001',
      text: '测试',
      category: 'emotions',
      subcategory: 'test_emotions',
      cultural_context: 'neutral',
      usage_frequency: 0.8,
      quality_score: 0.85,
      semantic_vector: new Array(20).fill(0.5),
      tags: ['测试', '示例'],
      created_at: Date.now(),
      source: 'test_data',
      ...overrides
    }
  }

  static createMockGenerationContext(overrides: Partial<GenerationContext> = {}): GenerationContext {
    return {
      cultural_preference: 'neutral',
      style_preference: 'humorous',
      creativity_level: 0.8,
      quality_threshold: 0.7,
      ...overrides
    }
  }

  static async waitFor(condition: () => boolean, timeout: number = 5000): Promise<void> {
    const start = Date.now()
    while (!condition() && Date.now() - start < timeout) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }
    if (!condition()) {
      throw new Error('Condition not met within timeout')
    }
  }

  static measurePerformance<T>(fn: () => T): { result: T; duration: number } {
    const start = process.hrtime.bigint()
    const result = fn()
    const end = process.hrtime.bigint()
    const duration = Number(end - start) / 1000000 // 转换为毫秒
    return { result, duration }
  }
}
```

### **3. 单元测试示例**
```typescript
// tests/unit/MorphemeRepository.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { MorphemeRepository } from '@/core/repositories/MorphemeRepository'
import { TestHelper } from '../utils/TestHelper'

describe('MorphemeRepository', () => {
  let repository: MorphemeRepository

  beforeEach(async () => {
    repository = new MorphemeRepository()
    await repository.initialize()
  })

  describe('initialization', () => {
    it('should load morphemes successfully', () => {
      expect(repository.getCount()).toBeGreaterThan(0)
    })

    it('should build indices correctly', () => {
      const stats = repository.getStats()
      expect(stats.byCategory).toBeDefined()
      expect(Object.keys(stats.byCategory).length).toBeGreaterThan(0)
    })
  })

  describe('findByCategory', () => {
    it('should return morphemes for valid category', () => {
      const emotions = repository.findByCategory('emotions')
      expect(emotions.length).toBeGreaterThan(0)
      emotions.forEach(morpheme => {
        expect(morpheme.category).toBe('emotions')
      })
    })

    it('should return empty array for invalid category', () => {
      const result = repository.findByCategory('invalid' as any)
      expect(result).toEqual([])
    })
  })

  describe('sample', () => {
    it('should respect sample criteria', () => {
      const criteria = {
        category: 'emotions' as const,
        min_quality_score: 0.8,
        limit: 3
      }

      const samples = repository.sample(criteria)
      expect(samples.length).toBeLessThanOrEqual(3)
      samples.forEach(morpheme => {
        expect(morpheme.category).toBe('emotions')
        expect(morpheme.quality_score).toBeGreaterThanOrEqual(0.8)
      })
    })
  })

  describe('performance', () => {
    it('should find by category in reasonable time', () => {
      const { duration } = TestHelper.measurePerformance(() => {
        repository.findByCategory('emotions')
      })
      expect(duration).toBeLessThan(1) // 1ms
    })

    it('should sample efficiently', () => {
      const { duration } = TestHelper.measurePerformance(() => {
        repository.sample({ limit: 10 })
      })
      expect(duration).toBeLessThan(5) // 5ms
    })
  })
})
```

### **4. 集成测试示例**
```typescript
// tests/integration/GenerationAPI.test.ts
import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { $fetch } from 'ofetch'
import type { GenerateRequest, GenerateResponse } from '@/types/api'

describe('Generation API Integration', () => {
  const baseURL = 'http://localhost:3000'

  beforeAll(async () => {
    // 等待服务器启动
    await TestHelper.waitFor(async () => {
      try {
        await $fetch(`${baseURL}/api/health`)
        return true
      } catch {
        return false
      }
    })
  })

  describe('POST /api/generate', () => {
    it('should generate usernames with default parameters', async () => {
      const response = await $fetch<GenerateResponse>(`${baseURL}/api/generate`, {
        method: 'POST',
        body: {}
      })

      expect(response.usernames).toBeDefined()
      expect(response.usernames.length).toBeGreaterThan(0)
      expect(response.generation_time).toBeGreaterThan(0)
    })

    it('should respect count parameter', async () => {
      const request: GenerateRequest = { count: 3 }
      const response = await $fetch<GenerateResponse>(`${baseURL}/api/generate`, {
        method: 'POST',
        body: request
      })

      expect(response.usernames.length).toBe(3)
    })

    it('should validate parameters', async () => {
      const request = { count: 25 } // 超过最大限制

      await expect(
        $fetch(`${baseURL}/api/generate`, {
          method: 'POST',
          body: request
        })
      ).rejects.toThrow()
    })

    it('should meet performance requirements', async () => {
      const { duration } = TestHelper.measurePerformance(async () => {
        await $fetch(`${baseURL}/api/generate`, {
          method: 'POST',
          body: { count: 5 }
        })
      })

      expect(duration).toBeLessThan(200) // 200ms
    })
  })
})
```
```
```