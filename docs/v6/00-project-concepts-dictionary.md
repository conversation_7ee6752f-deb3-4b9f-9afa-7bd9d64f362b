# namer-v6 项目概念词典

**文档类型**: 📚 核心概念定义与技术规范  
**创建时间**: 2025-06-22  
**版本**: v1.0  
**适用范围**: namer-v6项目全生命周期  

---

## 📖 **概念词典总览**

本文档定义了namer-v6项目中的所有核心概念，为开发团队提供统一的术语理解和技术实现指导。每个概念都包含定义、技术实现、使用示例和相关配置。

---

## 🧬 **语素库 (Morpheme Repository)**

### **定义**
语素库是namer-v6系统的核心数据基础，存储和管理用于用户名生成的最小语义单位。每个语素都包含丰富的语义、文化和质量属性，支持智能检索和组合。

### **核心属性结构**
```typescript
interface Morpheme {
  // 基础标识
  id: string                    // 唯一标识符，格式：{category}_{序号}
  text: string                  // 语素文本内容
  
  // 分类信息
  category: MorphemeCategory    // 主分类：emotions/professions/characteristics等
  subcategory: string          // 子分类：positive_emotions/creative_professions等
  
  // 语义属性
  semantic_vector: number[]     // 20维语义向量，用于相似度计算
  tags: string[]               // 语义标签，支持多维度检索
  
  // 文化属性
  cultural_context: CulturalContext  // 文化语境：ancient/modern/neutral
  usage_frequency: number       // 使用频率 [0-1]，影响采样权重
  
  // 质量属性
  quality_score: number         // 质量评分 [0-1]，综合评估指标
  
  // 元数据
  created_at: number           // 创建时间戳
  source: string               // 数据来源标识
}
```

### **技术实现机制**
```typescript
class MorphemeRepository {
  // 多维度索引系统
  private indices: {
    byCategory: Map<MorphemeCategory, Morpheme[]>     // 按类别索引
    byContext: Map<CulturalContext, Morpheme[]>       // 按文化语境索引
    byQuality: Map<number, Morpheme[]>                // 按质量分数索引
    bySemanticCluster: Map<string, Morpheme[]>        // 按语义聚类索引
  }
  
  // O(1)采样算法支持
  private aliasTables: Map<string, AliasTable>        // 加权采样表
  
  // 语义相似度计算
  calculateSimilarity(m1: Morpheme, m2: Morpheme): number {
    return cosineSimilarity(m1.semantic_vector, m2.semantic_vector)
  }
  
  // 智能采样接口
  sample(criteria: SampleCriteria): Morpheme[] {
    // 基于条件筛选 + 权重采样
  }
}
```

### **使用示例**
```typescript
// 基础查询
const emotions = repository.findByCategory('emotions')
const modernMorphemes = repository.findByContext('modern')

// 条件采样
const samples = repository.sample({
  category: 'emotions',
  cultural_preference: 'modern',
  min_quality_score: 0.8,
  limit: 5
})

// 语义相似度查询
const similar = repository.findSimilar(targetMorpheme, 0.7, 10)
```

### **相关配置**
```json
{
  "morpheme_config": {
    "semantic_vector_dimension": 20,
    "quality_threshold": 0.7,
    "similarity_threshold": 0.6,
    "max_samples_per_request": 100,
    "cache_strategy": "LRU",
    "index_rebuild_interval": 3600000
  }
}
```

---

## 🎨 **创意模式 (Creative Patterns)**

### **定义**
创意模式定义了用户名生成的具体规则和逻辑，每种模式代表一种独特的创意思路和组合方式。模式系统支持权重分配、动态调整和效果评估。

### **模式类型体系**
```typescript
interface CreativePattern {
  // 基础信息
  id: string                    // 模式唯一标识
  name: string                  // 模式名称
  description: string           // 模式描述
  
  // 权重配置
  weight: number               // 全局权重 [0-1]
  cultural_weights: {          // 文化语境权重
    ancient: number
    modern: number
    neutral: number
  }
  
  // 生成规则
  rules: PatternRule[]         // 组合规则集合
  constraints: Constraint[]    // 约束条件
  
  // 效果评估
  effectiveness_score: number  // 效果评分 [0-1]
  user_preference_score: number // 用户偏好评分 [0-1]
  
  // 示例数据
  examples: GeneratedExample[] // 生成示例
  anti_examples: string[]      // 反例（避免生成的类型）
}

interface PatternRule {
  step: number                 // 执行步骤
  action: 'select' | 'combine' | 'transform' | 'validate'
  parameters: RuleParameters   // 规则参数
  fallback?: PatternRule       // 失败回退规则
}
```

### **核心模式定义**
```yaml
1. 形容词+名词模式 (Adjective + Noun):
   权重: 0.20
   规则: [情感类语素] + [职业/特征类语素]
   示例: "温暖设计师", "优雅艺术家"
   适用: 专业感 + 个性化表达

2. 谐音双关模式 (Homophonic Pun):
   权重: 0.23
   规则: 基于拼音相似度的创意组合
   示例: "程序猿", "设计狮"
   适用: 幽默感 + 职业特色

3. 文艺复合模式 (Literary Compound):
   权重: 0.18
   规则: [古典元素] + [现代概念]
   示例: "墨染代码", "诗意算法"
   适用: 文艺气质 + 技术背景

4. 可爱萌系模式 (Cute Style):
   权重: 0.15
   规则: [可爱形容词] + [萌化后缀]
   示例: "小小程序员", "萌萌设计师"
   适用: 亲和力 + 年轻化表达

5. 古风雅致模式 (Classical Elegance):
   权重: 0.12
   规则: [古典词汇] + [雅致组合]
   示例: "墨客", "雅士"
   适用: 传统文化 + 高雅品味
```

### **技术实现**
```typescript
class PatternEngine {
  private patterns: Map<string, CreativePattern>
  private weightCalculator: WeightCalculator
  
  // 模式选择算法
  selectPattern(context: GenerationContext): CreativePattern {
    const weights = this.calculateDynamicWeights(context)
    return this.weightedRandomSelect(weights)
  }
  
  // 动态权重计算
  private calculateDynamicWeights(context: GenerationContext): Map<string, number> {
    return this.patterns.forEach((pattern, id) => {
      let weight = pattern.weight
      
      // 文化语境调整
      weight *= pattern.cultural_weights[context.cultural_preference]
      
      // 用户偏好调整
      weight *= this.getUserPreferenceMultiplier(pattern, context)
      
      // 效果反馈调整
      weight *= pattern.effectiveness_score
      
      return weight
    })
  }
  
  // 模式执行
  executePattern(pattern: CreativePattern, morphemes: Morpheme[]): GenerationResult {
    for (const rule of pattern.rules) {
      try {
        const result = this.executeRule(rule, morphemes)
        if (result.success) return result
      } catch (error) {
        if (rule.fallback) {
          return this.executeRule(rule.fallback, morphemes)
        }
      }
    }
    throw new Error(`Pattern execution failed: ${pattern.id}`)
  }
}
```

### **使用示例**
```typescript
// 模式选择
const context = {
  cultural_preference: 'modern',
  style_preference: 'humorous',
  creativity_level: 0.8
}
const selectedPattern = patternEngine.selectPattern(context)

// 模式执行
const morphemes = morphemeRepository.sample({
  categories: selectedPattern.required_categories,
  limit: 10
})
const result = patternEngine.executePattern(selectedPattern, morphemes)
```

---

## ⚖️ **兼容性规则 (Compatibility Rules)**

### **定义**
兼容性规则确保生成的用户名在语义、文化和美学层面的协调性。规则系统支持多层次验证，从基础的语法检查到高级的语义一致性验证。

### **规则类型体系**
```typescript
interface CompatibilityRule {
  id: string                   // 规则唯一标识
  name: string                 // 规则名称
  type: RuleType              // 规则类型
  priority: number            // 优先级 [1-10]
  enabled: boolean            // 是否启用

  // 匹配条件
  conditions: RuleCondition[] // 触发条件

  // 验证逻辑
  validator: RuleValidator    // 验证函数

  // 处理策略
  action: 'reject' | 'modify' | 'warn' | 'score_penalty'
  penalty_score: number      // 惩罚分数

  // 元数据
  description: string        // 规则描述
  examples: {               // 示例
    valid: string[]         // 符合规则的示例
    invalid: string[]       // 违反规则的示例
  }
}

enum RuleType {
  SEMANTIC = 'semantic',           // 语义兼容性
  CULTURAL = 'cultural',           // 文化适配性
  PHONETIC = 'phonetic',           // 语音和谐性
  LENGTH = 'length',               // 长度限制
  CHARACTER = 'character',         // 字符规范
  AESTHETIC = 'aesthetic'          // 美学协调性
}
```

### **核心规则定义**
```yaml
语义兼容性规则:
  1. 情感一致性规则:
     条件: 包含多个情感类语素
     验证: 情感极性不冲突
     示例: ✓"温暖治愈师" ✗"愤怒温暖者"

  2. 职业逻辑性规则:
     条件: 包含职业类语素
     验证: 修饰词与职业匹配
     示例: ✓"创意设计师" ✗"暴力艺术家"

文化适配性规则:
  1. 时代一致性规则:
     条件: 混合古典与现代元素
     验证: 文化语境协调
     示例: ✓"古风程序员" ✗"赛博墨客"

  2. 语域匹配规则:
     条件: 正式与非正式语素混合
     验证: 语域层次一致
     示例: ✓"专业设计师" ✗"萌萌学者"

语音和谐性规则:
  1. 音韵协调规则:
     条件: 多音节组合
     验证: 声调和韵母搭配
     示例: ✓"诗意设计" ✗"急躁暴躁"

  2. 重复避免规则:
     条件: 相同或相似音素
     验证: 避免不必要重复
     示例: ✓"温暖治愈" ✗"温温暖暖"
```

### **技术实现**
```typescript
class CompatibilityEngine {
  private rules: Map<string, CompatibilityRule>
  private semanticAnalyzer: SemanticAnalyzer

  // 兼容性验证
  validateCompatibility(components: MorphemeComponent[]): CompatibilityResult {
    const results: RuleResult[] = []
    let totalScore = 1.0

    for (const rule of this.getApplicableRules(components)) {
      const result = this.executeRule(rule, components)
      results.push(result)

      if (result.action === 'reject') {
        return { compatible: false, score: 0, violations: [result] }
      }

      if (result.action === 'score_penalty') {
        totalScore *= (1 - rule.penalty_score)
      }
    }

    return {
      compatible: totalScore > 0.6,
      score: totalScore,
      results,
      suggestions: this.generateSuggestions(results)
    }
  }

  // 语义一致性检查
  private checkSemanticConsistency(components: MorphemeComponent[]): boolean {
    const semanticVectors = components.map(c => c.morpheme.semantic_vector)
    const avgSimilarity = this.calculateAverageSimilarity(semanticVectors)
    return avgSimilarity > 0.3 // 语义相似度阈值
  }

  // 文化适配检查
  private checkCulturalCompatibility(components: MorphemeComponent[]): boolean {
    const contexts = components.map(c => c.morpheme.cultural_context)
    const uniqueContexts = new Set(contexts)

    // 允许neutral与任何语境组合，但ancient和modern不能直接组合
    if (uniqueContexts.has('ancient') && uniqueContexts.has('modern')) {
      return uniqueContexts.has('neutral') // 需要neutral作为桥梁
    }
    return true
  }
}
```

### **使用示例**
```typescript
// 兼容性验证
const components = [
  { morpheme: warmMorpheme, position: 0 },
  { morpheme: designerMorpheme, position: 1 }
]

const result = compatibilityEngine.validateCompatibility(components)
if (!result.compatible) {
  console.log('兼容性问题:', result.violations)
  console.log('改进建议:', result.suggestions)
}
```

### **相关配置**
```json
{
  "compatibility_config": {
    "semantic_similarity_threshold": 0.3,
    "cultural_mixing_allowed": true,
    "phonetic_harmony_weight": 0.2,
    "length_limits": {
      "min_characters": 2,
      "max_characters": 12,
      "preferred_range": [4, 8]
    },
    "rule_priorities": {
      "semantic": 9,
      "cultural": 7,
      "phonetic": 5,
      "aesthetic": 6
    }
  }
}
```

---

## 📊 **质量评估体系 (Quality Assessment)**

### **定义**
质量评估体系通过多维度指标对生成的用户名进行综合评分，确保输出结果符合用户期望和使用场景要求。评估体系支持实时计算、批量分析和趋势预测。

### **评估维度定义**
```typescript
interface QualityScore {
  // 综合评分
  overall: number              // 总体质量评分 [0-1]

  // 分维度评分
  dimensions: {
    creativity: number         // 创意性 [0-1]
    memorability: number       // 记忆性 [0-1]
    cultural_fit: number       // 文化适配度 [0-1]
    uniqueness: number         // 独特性 [0-1]
    pronunciation: number      // 发音友好度 [0-1]
    semantic_coherence: number // 语义连贯性 [0-1]
    aesthetic_appeal: number   // 美学吸引力 [0-1]
    practical_usability: number // 实用性 [0-1]
  }

  // 元数据
  confidence: number          // 评估置信度 [0-1]
  evaluation_time: number     // 评估耗时 (ms)
  algorithm_version: string   // 算法版本
}
```

### **评估算法实现**
```typescript
class QualityAssessment {
  private weights: DimensionWeights
  private benchmarks: QualityBenchmarks

  // 综合质量评估
  evaluate(username: string, components: MorphemeComponent[], context: GenerationContext): QualityScore {
    const dimensions = {
      creativity: this.evaluateCreativity(components, context),
      memorability: this.evaluateMemorability(username),
      cultural_fit: this.evaluateCulturalFit(components, context),
      uniqueness: this.evaluateUniqueness(username, components),
      pronunciation: this.evaluatePronunciation(username),
      semantic_coherence: this.evaluateSemanticCoherence(components),
      aesthetic_appeal: this.evaluateAestheticAppeal(username, components),
      practical_usability: this.evaluatePracticalUsability(username)
    }

    // 加权计算总分
    const overall = this.calculateWeightedScore(dimensions, this.weights)

    return {
      overall,
      dimensions,
      confidence: this.calculateConfidence(dimensions),
      evaluation_time: Date.now() - startTime,
      algorithm_version: '2.1.0'
    }
  }

  // 创意性评估
  private evaluateCreativity(components: MorphemeComponent[], context: GenerationContext): number {
    let score = 0.5 // 基础分

    // 语素组合新颖度
    const combinationNovelty = this.calculateCombinationNovelty(components)
    score += combinationNovelty * 0.4

    // 模式创新度
    const patternInnovation = this.calculatePatternInnovation(context.pattern)
    score += patternInnovation * 0.3

    // 语义跳跃度
    const semanticLeap = this.calculateSemanticLeap(components)
    score += semanticLeap * 0.3

    return Math.min(score, 1.0)
  }

  // 记忆性评估
  private evaluateMemorability(username: string): number {
    let score = 0.5

    // 长度适中性 (4-8字符最佳)
    const lengthScore = this.calculateLengthScore(username.length)
    score += lengthScore * 0.3

    // 音韵特征
    const phoneticScore = this.calculatePhoneticMemorability(username)
    score += phoneticScore * 0.4

    // 视觉特征
    const visualScore = this.calculateVisualMemorability(username)
    score += visualScore * 0.3

    return Math.min(score, 1.0)
  }

  // 文化适配度评估
  private evaluateCulturalFit(components: MorphemeComponent[], context: GenerationContext): number {
    const targetCulture = context.cultural_preference
    let score = 0.5

    // 文化语境一致性
    const contextConsistency = this.calculateContextConsistency(components, targetCulture)
    score += contextConsistency * 0.5

    // 文化价值观匹配
    const valueAlignment = this.calculateValueAlignment(components, targetCulture)
    score += valueAlignment * 0.3

    // 时代适应性
    const timeRelevance = this.calculateTimeRelevance(components, targetCulture)
    score += timeRelevance * 0.2

    return Math.min(score, 1.0)
  }
}
```

### **使用示例**
```typescript
// 质量评估
const username = "温暖设计师"
const components = [warmMorpheme, designerMorpheme]
const context = { cultural_preference: 'modern', style_preference: 'professional' }

const qualityScore = qualityAssessment.evaluate(username, components, context)

console.log(`总体评分: ${qualityScore.overall}`)
console.log(`创意性: ${qualityScore.dimensions.creativity}`)
console.log(`记忆性: ${qualityScore.dimensions.memorability}`)

// 批量评估
const usernames = ["温暖设计师", "诗意程序员", "可爱艺术家"]
const batchResults = qualityAssessment.evaluateBatch(usernames, context)
```

### **相关配置**
```json
{
  "quality_config": {
    "dimension_weights": {
      "creativity": 0.20,
      "memorability": 0.18,
      "cultural_fit": 0.15,
      "uniqueness": 0.12,
      "pronunciation": 0.10,
      "semantic_coherence": 0.10,
      "aesthetic_appeal": 0.08,
      "practical_usability": 0.07
    },
    "thresholds": {
      "minimum_acceptable": 0.6,
      "good_quality": 0.75,
      "excellent_quality": 0.9
    },
    "benchmarks": {
      "creativity_baseline": 0.5,
      "memorability_baseline": 0.6,
      "cultural_fit_baseline": 0.7
    }
  }
}
```

---

## 🌏 **文化适配 (Cultural Adaptation)**

### **定义**
文化适配是namer-v6系统的核心特色，通过深度理解不同文化语境的语言特点、价值观念和审美偏好，为用户提供符合其文化背景的个性化用户名生成服务。

### **文化语境分类**
```typescript
enum CulturalContext {
  ANCIENT = 'ancient',     // 古典文化：传统、典雅、深厚
  MODERN = 'modern',       // 现代文化：时尚、创新、国际化
  NEUTRAL = 'neutral'      // 中性文化：通用、平衡、包容
}

interface CulturalProfile {
  context: CulturalContext
  characteristics: {
    language_style: string[]      // 语言风格特征
    value_orientation: string[]   // 价值取向
    aesthetic_preference: string[] // 审美偏好
    social_context: string[]      // 社会语境
  }

  // 语素偏好
  morpheme_preferences: {
    preferred_categories: MorphemeCategory[]
    avoided_categories: MorphemeCategory[]
    weight_adjustments: Map<string, number>
  }

  // 模式适配
  pattern_compatibility: Map<string, number> // 模式兼容度

  // 质量标准
  quality_criteria: {
    creativity_weight: number
    tradition_weight: number
    innovation_weight: number
    harmony_weight: number
  }
}
```

### **文化适配策略**
```yaml
古典文化适配 (Ancient):
  语言特征:
    - 偏好文言词汇和典故
    - 重视音韵和谐
    - 追求意境深远
    - 体现文化底蕴

  适配策略:
    - 提升古典语素权重
    - 优先选择雅致模式
    - 强化语音和谐性检查
    - 增加文化内涵评估

  示例生成:
    ✓ "墨客" "雅士" "诗韵"
    ✗ "酷炫" "潮流" "萌萌"

现代文化适配 (Modern):
  语言特征:
    - 偏好时尚新颖词汇
    - 接受外来语融合
    - 追求个性表达
    - 体现时代特色

  适配策略:
    - 提升现代语素权重
    - 优先选择创新模式
    - 允许跨文化组合
    - 增加创新性评估

  示例生成:
    ✓ "创客" "极客" "设计师"
    ✗ "古韵" "雅致" "墨香"

中性文化适配 (Neutral):
  语言特征:
    - 平衡传统与现代
    - 适用范围广泛
    - 避免极端表达
    - 追求和谐统一

  适配策略:
    - 均衡各类语素权重
    - 支持多种模式组合
    - 强化兼容性检查
    - 优化整体协调性

  示例生成:
    ✓ "温暖设计师" "优雅艺术家"
    ✗ "古风赛博客" "萌萌老学究"
```

### **技术实现**
```typescript
class CulturalAdapter {
  private profiles: Map<CulturalContext, CulturalProfile>
  private contextAnalyzer: ContextAnalyzer

  // 文化适配主流程
  adapt(context: GenerationContext): AdaptationResult {
    const profile = this.profiles.get(context.cultural_preference)
    if (!profile) {
      throw new Error(`Unsupported cultural context: ${context.cultural_preference}`)
    }

    return {
      morpheme_weights: this.calculateMorphemeWeights(profile),
      pattern_weights: this.calculatePatternWeights(profile),
      quality_adjustments: this.calculateQualityAdjustments(profile),
      compatibility_rules: this.getCompatibilityRules(profile)
    }
  }

  // 语素权重计算
  private calculateMorphemeWeights(profile: CulturalProfile): Map<string, number> {
    const weights = new Map<string, number>()

    // 基础权重
    for (const category of profile.morpheme_preferences.preferred_categories) {
      weights.set(category, 1.2) // 提升偏好类别权重
    }

    for (const category of profile.morpheme_preferences.avoided_categories) {
      weights.set(category, 0.8) // 降低避免类别权重
    }

    // 应用调整系数
    for (const [morphemeId, adjustment] of profile.morpheme_preferences.weight_adjustments) {
      weights.set(morphemeId, weights.get(morphemeId) || 1.0 * adjustment)
    }

    return weights
  }

  // 模式权重计算
  private calculatePatternWeights(profile: CulturalProfile): Map<string, number> {
    const weights = new Map<string, number>()

    for (const [patternId, compatibility] of profile.pattern_compatibility) {
      weights.set(patternId, compatibility)
    }

    return weights
  }

  // 文化一致性验证
  validateCulturalConsistency(components: MorphemeComponent[], context: CulturalContext): boolean {
    const profile = this.profiles.get(context)
    if (!profile) return true

    // 检查语素文化语境一致性
    const contexts = components.map(c => c.morpheme.cultural_context)
    const inconsistentCount = contexts.filter(c =>
      c !== context && c !== 'neutral'
    ).length

    // 允许最多20%的不一致
    return inconsistentCount / contexts.length <= 0.2
  }

  // 文化价值观匹配度计算
  calculateValueAlignment(components: MorphemeComponent[], context: CulturalContext): number {
    const profile = this.profiles.get(context)
    if (!profile) return 0.5

    let alignmentScore = 0
    let totalWeight = 0

    for (const component of components) {
      const morpheme = component.morpheme
      let componentScore = 0.5 // 基础分

      // 检查价值取向匹配
      for (const value of profile.characteristics.value_orientation) {
        if (morpheme.tags.includes(value)) {
          componentScore += 0.2
        }
      }

      // 检查审美偏好匹配
      for (const aesthetic of profile.characteristics.aesthetic_preference) {
        if (morpheme.tags.includes(aesthetic)) {
          componentScore += 0.15
        }
      }

      const weight = morpheme.usage_frequency * morpheme.quality_score
      alignmentScore += componentScore * weight
      totalWeight += weight
    }

    return totalWeight > 0 ? alignmentScore / totalWeight : 0.5
  }
}
```

### **使用示例**
```typescript
// 文化适配
const context = {
  cultural_preference: 'ancient',
  style_preference: 'elegant',
  creativity_level: 0.7
}

const adaptation = culturalAdapter.adapt(context)

// 应用适配结果
const morphemes = morphemeRepository.sample({
  weights: adaptation.morpheme_weights,
  limit: 10
})

const patterns = patternEngine.selectPatterns({
  weights: adaptation.pattern_weights,
  count: 3
})

// 验证文化一致性
const components = [ancientMorpheme, modernMorpheme]
const isConsistent = culturalAdapter.validateCulturalConsistency(
  components,
  'ancient'
)
```

### **相关配置**
```json
{
  "cultural_config": {
    "ancient": {
      "morpheme_weight_boost": 1.3,
      "pattern_preferences": {
        "classical_elegance": 1.5,
        "literary_compound": 1.2,
        "homophonic_pun": 0.8
      },
      "quality_adjustments": {
        "tradition_weight": 0.4,
        "innovation_weight": 0.1,
        "harmony_weight": 0.3
      }
    },
    "modern": {
      "morpheme_weight_boost": 1.2,
      "pattern_preferences": {
        "creative_combination": 1.4,
        "professional_style": 1.3,
        "cute_style": 1.1
      },
      "quality_adjustments": {
        "creativity_weight": 0.4,
        "innovation_weight": 0.3,
        "uniqueness_weight": 0.2
      }
    },
    "neutral": {
      "morpheme_weight_boost": 1.0,
      "pattern_preferences": {
        "adjective_noun": 1.2,
        "balanced_compound": 1.1
      },
      "quality_adjustments": {
        "balance_weight": 0.4,
        "harmony_weight": 0.3,
        "usability_weight": 0.3
      }
    }
  }
}
```

---

## 📝 **概念关系图**

```mermaid
graph TB
    subgraph "数据层"
        MR[语素库<br/>Morpheme Repository]
        CR[兼容性规则<br/>Compatibility Rules]
    end

    subgraph "算法层"
        CP[创意模式<br/>Creative Patterns]
        QA[质量评估<br/>Quality Assessment]
        CA[文化适配<br/>Cultural Adaptation]
    end

    subgraph "应用层"
        GE[生成引擎<br/>Generation Engine]
        API[API接口<br/>API Interface]
    end

    MR --> CP
    MR --> CA
    CR --> GE
    CP --> GE
    QA --> GE
    CA --> CP
    CA --> QA
    GE --> API

    classDef dataLayer fill:#e1f5fe
    classDef algorithmLayer fill:#f3e5f5
    classDef applicationLayer fill:#e8f5e8

    class MR,CR dataLayer
    class CP,QA,CA algorithmLayer
    class GE,API applicationLayer
```

---

**文档维护说明**: 本概念词典是namer-v6项目的核心参考文档，所有概念定义和技术实现都应与此文档保持一致。如有概念更新或新增，请及时更新此文档并通知相关开发人员。

**最后更新**: 2025-06-22
**下次审查**: 2025-07-22
**维护责任人**: 项目架构师
