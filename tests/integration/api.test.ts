/**
 * API集成测试
 * 测试核心API端点的功能
 */

import { describe, it, expect, beforeAll } from 'vitest'

const API_BASE = 'http://localhost:3000/api'

describe('API Integration Tests', () => {
  beforeAll(async () => {
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 1000))
  })

  describe('Health Check API', () => {
    it('should return health status', async () => {
      const response = await fetch(`${API_BASE}/health`)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('status')
      expect(data).toHaveProperty('timestamp')
      expect(data).toHaveProperty('version')
      expect(data).toHaveProperty('uptime')
      expect(data).toHaveProperty('checks')

      expect(data.checks).toHaveProperty('memory')
      expect(data.checks).toHaveProperty('cache')
      expect(data.checks).toHaveProperty('data_integrity')
      expect(data.checks).toHaveProperty('performance')
    })
  })

  describe('Username Generation API', () => {
    it('should generate usernames with default parameters', async () => {
      const response = await fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      })

      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('usernames')
      expect(data.data).toHaveProperty('generation_time')
      expect(data.data).toHaveProperty('cache_hit')
      expect(data.data).toHaveProperty('patterns_used')

      expect(Array.isArray(data.data.usernames)).toBe(true)
      expect(data.data.usernames.length).toBeGreaterThan(0)
      expect(data.data.usernames.length).toBeLessThanOrEqual(5) // 默认最大5个
    })

    it('should generate specified number of usernames', async () => {
      const count = 3
      const response = await fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ count })
      })

      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.usernames.length).toBe(count)
    })

    it('should validate username structure', async () => {
      const response = await fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ count: 1 })
      })

      const data = await response.json()
      const username = data.data.usernames[0]

      expect(username).toHaveProperty('text')
      expect(username).toHaveProperty('pattern')
      expect(username).toHaveProperty('quality_score')
      expect(username).toHaveProperty('explanation')
      expect(username).toHaveProperty('components')
      expect(username).toHaveProperty('metadata')

      expect(typeof username.text).toBe('string')
      expect(username.text.length).toBeGreaterThan(0)

      expect(username.quality_score).toHaveProperty('overall')
      expect(username.quality_score).toHaveProperty('dimensions')
      expect(username.quality_score.overall).toBeGreaterThanOrEqual(0)
      expect(username.quality_score.overall).toBeLessThanOrEqual(1)

      expect(Array.isArray(username.components)).toBe(true)
      expect(username.components.length).toBeGreaterThan(0)
    })

    it('should respect quality threshold', async () => {
      const response = await fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          count: 5,
          quality_threshold: 0.8
        })
      })

      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)

      // 所有生成的用户名都应该满足质量阈值
      data.data.usernames.forEach(username => {
        expect(username.quality_score.overall).toBeGreaterThanOrEqual(0.8)
      })
    })

    it('should handle invalid parameters', async () => {
      const response = await fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          count: 25, // 超过最大限制
          creativity_level: 1.5 // 超出范围
        })
      })

      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.success).toBe(false)
      expect(data.error).toHaveProperty('code')
      expect(data.error).toHaveProperty('message')
    })

    it('should support different cultural preferences', async () => {
      const preferences = ['ancient', 'modern', 'neutral']

      for (const preference of preferences) {
        const response = await fetch(`${API_BASE}/generate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            count: 2,
            cultural_preference: preference
          })
        })

        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.data.usernames.length).toBe(2)
      }
    })

    it('should have reasonable response time', async () => {
      const startTime = Date.now()

      const response = await fetch(`${API_BASE}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ count: 5 })
      })

      const endTime = Date.now()
      const responseTime = endTime - startTime

      expect(response.status).toBe(200)
      expect(responseTime).toBeLessThan(1000) // 应该在1秒内完成

      const data = await response.json()
      expect(data.data.generation_time).toBeLessThan(500) // 服务器端生成时间应该在500ms内
    })
  })
})