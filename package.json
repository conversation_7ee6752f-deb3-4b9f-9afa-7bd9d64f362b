{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "check": "npx tsc --noEmit"}, "dependencies": {"@nuxt/content": "3.6.1", "@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.14.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.8", "@nuxt/test-utils": "3.19.1", "@nuxt/ui": "3.1.3", "@unhead/vue": "^2.0.3", "eslint": "^9.0.0", "nuxt": "^3.17.5", "typescript": "^5.6.3", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "devDependencies": {"@jest/globals": "^30.0.2", "@types/jest": "^30.0.0", "@types/uuid": "^10.0.0", "jest": "^30.0.2", "jest-html-reporters": "^3.1.7", "ts-jest": "^29.4.0", "uuid": "^10.0.0", "vitest": "^3.2.4"}}